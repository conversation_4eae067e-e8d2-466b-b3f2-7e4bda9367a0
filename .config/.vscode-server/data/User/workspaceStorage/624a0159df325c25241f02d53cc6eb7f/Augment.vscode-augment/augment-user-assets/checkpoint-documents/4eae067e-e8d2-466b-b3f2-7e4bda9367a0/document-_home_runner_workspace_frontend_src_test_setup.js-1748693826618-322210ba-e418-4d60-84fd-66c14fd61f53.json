{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/setup.js"}, "modifiedCode": "import { vi } from 'vitest'\nimport '@testing-library/jest-dom'\n\n// Mock global objects\nglobal.ResizeObserver = vi.fn().mockImplementation(() => ({\n  observe: vi.fn(),\n  unobserve: vi.fn(),\n  disconnect: vi.fn(),\n}))\n\nglobal.IntersectionObserver = vi.fn().mockImplementation(() => ({\n  observe: vi.fn(),\n  unobserve: vi.fn(),\n  disconnect: vi.fn(),\n}))\n\n// Mock window.matchMedia\nObject.defineProperty(window, 'matchMedia', {\n  writable: true,\n  value: vi.fn().mockImplementation(query => ({\n    matches: false,\n    media: query,\n    onchange: null,\n    addListener: vi.fn(), // deprecated\n    removeListener: vi.fn(), // deprecated\n    addEventListener: vi.fn(),\n    removeEventListener: vi.fn(),\n    dispatchEvent: vi.fn(),\n  })),\n})\n\n// Mock fetch globally\nglobal.fetch = vi.fn()\n\n// Mock localStorage\nconst localStorageMock = {\n  getItem: vi.fn(),\n  setItem: vi.fn(),\n  removeItem: vi.fn(),\n  clear: vi.fn(),\n}\nglobal.localStorage = localStorageMock\n\n// Mock sessionStorage\nconst sessionStorageMock = {\n  getItem: vi.fn(),\n  setItem: vi.fn(),\n  removeItem: vi.fn(),\n  clear: vi.fn(),\n}\nglobal.sessionStorage = sessionStorageMock\n\n// Mock URL.createObjectURL\nglobal.URL.createObjectURL = vi.fn(() => 'mocked-url')\nglobal.URL.revokeObjectURL = vi.fn()\n\n// Mock console methods for cleaner test output\nglobal.console = {\n  ...console,\n  // Uncomment to suppress console logs during tests\n  // log: vi.fn(),\n  // warn: vi.fn(),\n  // error: vi.fn(),\n}\n\n// Setup default fetch responses\nbeforeEach(() => {\n  fetch.mockClear()\n  localStorage.clear()\n  sessionStorage.clear()\n  \n  // Default successful API response\n  fetch.mockResolvedValue({\n    ok: true,\n    status: 200,\n    json: async () => ({\n      success: true,\n      data: {},\n      message: 'Success'\n    }),\n    text: async () => 'Success',\n    headers: new Headers(),\n  })\n})\n"}
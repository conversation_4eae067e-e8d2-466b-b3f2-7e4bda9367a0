import{r as u,f as C,A as E,c as s,j as t,I as A,a as m,i as y,b as D,g as c,v as V,x as N,H as F,F as w,k,n as H,m as v,t as l,o}from"./vendor.js";import{c as S}from"./app.js";const I={class:"personnel-directory"},U={class:"flex justify-between items-center mb-6"},Q={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-4 mb-6"},q={class:"flex flex-col lg:flex-row gap-4"},G={class:"flex-1"},J={class:"relative"},K={class:"lg:w-48"},O=["value"],R={class:"flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1"},W={key:0,class:"flex items-center gap-2 mt-4 pt-4 border-t border-gray-200 dark:border-gray-700"},X={key:0,class:"inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"},Y={key:1,class:"inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"},Z={key:0,class:"flex justify-center items-center h-64"},tt={key:1,class:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6"},et={class:"flex"},rt={class:"text-sm text-red-700 dark:text-red-300 mt-1"},at={key:2},st={key:0,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"},ot={class:"p-6"},lt={class:"flex items-center space-x-4"},it={class:"flex-shrink-0"},dt={class:"w-16 h-16 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center"},nt=["src","alt"],ut={key:1,class:"w-8 h-8 text-gray-600 dark:text-gray-300",fill:"currentColor",viewBox:"0 0 20 20"},ct={class:"flex-1 min-w-0"},xt={class:"text-lg font-medium text-gray-900 dark:text-white truncate"},gt={class:"text-sm text-gray-500 dark:text-gray-400 truncate"},vt={key:0,class:"text-sm text-gray-500 dark:text-gray-400 truncate"},ht={class:"mt-4 space-y-2"},pt={class:"flex items-center text-sm text-gray-600 dark:text-gray-400"},ft=["href"],mt={key:0,class:"flex items-center text-sm text-gray-600 dark:text-gray-400"},yt=["href"],wt={class:"mt-4 flex space-x-2"},kt=["href"],bt={key:1,class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},_t={class:"overflow-x-auto"},zt={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},Mt={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},Ct={class:"px-6 py-4 whitespace-nowrap"},Vt={class:"flex items-center"},Ht={class:"flex-shrink-0 w-10 h-10"},Bt={class:"w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center"},Lt=["src","alt"],Tt={key:1,class:"w-5 h-5 text-gray-600 dark:text-gray-300",fill:"currentColor",viewBox:"0 0 20 20"},$t={class:"ml-4"},Pt={class:"text-sm font-medium text-gray-900 dark:text-white"},jt={class:"text-sm text-gray-500 dark:text-gray-400"},Et={class:"px-6 py-4 whitespace-nowrap"},At={class:"text-sm text-gray-900 dark:text-white"},Dt={class:"px-6 py-4 whitespace-nowrap"},Nt={class:"text-sm text-gray-900 dark:text-white"},Ft={class:"px-6 py-4 whitespace-nowrap"},St={class:"flex space-x-2"},It=["href"],Ut=["href"],Qt={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},qt={key:3,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-12 text-center"},Gt={class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},Rt={__name:"PersonnelDirectory",setup(Jt){S();const b=u([]),h=u([]),p=u(!1),g=u(null),d=u(""),n=u(""),x=u("grid"),f=C(()=>{let a=b.value;if(d.value.trim()){const e=d.value.toLowerCase();a=a.filter(i=>i.full_name.toLowerCase().includes(e)||i.email.toLowerCase().includes(e)||i.position&&i.position.toLowerCase().includes(e)||i.department&&i.department.name.toLowerCase().includes(e))}return n.value&&(a=a.filter(e=>e.department_id==n.value)),a}),_=C(()=>d.value.trim()!==""||n.value!==""),B=a=>{const e=h.value.find(i=>i.id==a);return e?e.name:""};let z=null;const L=()=>{clearTimeout(z),z=setTimeout(()=>{},300)},T=async()=>{p.value=!0,g.value=null;try{const a=await fetch("/api/personnel/users",{credentials:"include"});if(!a.ok)throw new Error(`HTTP ${a.status}: ${a.statusText}`);const e=await a.json();if(e.success)b.value=e.data.users||[];else throw new Error(e.message||"Errore nel caricamento dei dipendenti")}catch(a){console.error("Error fetching employees:",a),g.value=a.message}finally{p.value=!1}},$=async()=>{try{const a=await fetch("/api/personnel/departments",{credentials:"include"});if(!a.ok)throw new Error(`HTTP ${a.status}: ${a.statusText}`);const e=await a.json();e.success&&(h.value=e.data.departments||[])}catch(a){console.error("Error fetching departments:",a)}},P=()=>{},j=()=>{d.value="",n.value=""};return E(async()=>{await Promise.all([T(),$()])}),(a,e)=>{const i=D("router-link");return o(),s("div",I,[t("div",U,[e[5]||(e[5]=A('<div class="flex items-center"><svg class="w-8 h-8 text-blue-600 dark:text-blue-400 mr-3" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z" clip-rule="evenodd"></path></svg><div><h1 class="text-2xl font-bold text-gray-900 dark:text-white">Directory Aziendale</h1><p class="text-gray-600 dark:text-gray-400 mt-1">Trova e contatta i colleghi</p></div></div>',1)),m(i,{to:"/app/personnel",class:"text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"},{default:y(()=>e[4]||(e[4]=[t("svg",{class:"w-6 h-6",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{"fill-rule":"evenodd",d:"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z","clip-rule":"evenodd"})],-1)])),_:1,__:[4]})]),t("div",Q,[t("div",q,[t("div",G,[t("div",J,[e[6]||(e[6]=t("svg",{class:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{"fill-rule":"evenodd",d:"M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z","clip-rule":"evenodd"})],-1)),V(t("input",{"onUpdate:modelValue":e[0]||(e[0]=r=>d.value=r),onInput:L,type:"text",placeholder:"Cerca per nome, email, posizione...",class:"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,544),[[N,d.value]])])]),t("div",K,[V(t("select",{"onUpdate:modelValue":e[1]||(e[1]=r=>n.value=r),onChange:P,class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},[e[7]||(e[7]=t("option",{value:""},"Tutti i dipartimenti",-1)),(o(!0),s(w,null,k(h.value,r=>(o(),s("option",{key:r.id,value:r.id},l(r.name),9,O))),128))],544),[[F,n.value]])]),t("div",R,[t("button",{onClick:e[2]||(e[2]=r=>x.value="grid"),class:H(["px-3 py-1 rounded-md text-sm font-medium transition-colors duration-200",x.value==="grid"?"bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm":"text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"])},e[8]||(e[8]=[t("svg",{class:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{d:"M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"})],-1)]),2),t("button",{onClick:e[3]||(e[3]=r=>x.value="list"),class:H(["px-3 py-1 rounded-md text-sm font-medium transition-colors duration-200",x.value==="list"?"bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm":"text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"])},e[9]||(e[9]=[t("svg",{class:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{"fill-rule":"evenodd",d:"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z","clip-rule":"evenodd"})],-1)]),2)])]),_.value?(o(),s("div",W,[e[12]||(e[12]=t("span",{class:"text-sm text-gray-600 dark:text-gray-400"},"Filtri attivi:",-1)),d.value?(o(),s("span",X,[e[10]||(e[10]=t("svg",{class:"w-3 h-3 mr-1",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{"fill-rule":"evenodd",d:"M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z","clip-rule":"evenodd"})],-1)),v(' "'+l(d.value)+'" ',1)])):c("",!0),n.value?(o(),s("span",Y,[e[11]||(e[11]=t("svg",{class:"w-3 h-3 mr-1",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z","clip-rule":"evenodd"})],-1)),v(" "+l(B(n.value)),1)])):c("",!0),t("button",{onClick:j,class:"text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 underline"}," Pulisci filtri ")])):c("",!0)]),p.value?(o(),s("div",Z,e[13]||(e[13]=[t("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):g.value?(o(),s("div",tt,[t("div",et,[e[15]||(e[15]=t("svg",{class:"w-5 h-5 text-red-400 mr-2 mt-0.5",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z","clip-rule":"evenodd"})],-1)),t("div",null,[e[14]||(e[14]=t("h3",{class:"text-sm font-medium text-red-800 dark:text-red-200"},"Errore nel caricamento della directory",-1)),t("p",rt,l(g.value),1)])])])):f.value.length>0?(o(),s("div",at,[x.value==="grid"?(o(),s("div",st,[(o(!0),s(w,null,k(f.value,r=>(o(),s("div",{key:r.id,class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden hover:shadow-lg transition-shadow duration-200"},[t("div",ot,[t("div",lt,[t("div",it,[t("div",dt,[r.profile_image?(o(),s("img",{key:0,src:r.profile_image,alt:r.full_name,class:"w-16 h-16 rounded-full object-cover"},null,8,nt)):(o(),s("svg",ut,e[16]||(e[16]=[t("path",{"fill-rule":"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z","clip-rule":"evenodd"},null,-1)])))])]),t("div",ct,[t("h3",xt,l(r.full_name),1),t("p",gt,l(r.position||"Posizione non specificata"),1),r.department?(o(),s("p",vt,l(r.department.name),1)):c("",!0)])]),t("div",ht,[t("div",pt,[e[17]||(e[17]=t("svg",{class:"w-4 h-4 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{d:"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"}),t("path",{d:"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"})],-1)),t("a",{href:`mailto:${r.email}`,class:"hover:text-blue-600 dark:hover:text-blue-400 truncate"},l(r.email),9,ft)]),r.phone?(o(),s("div",mt,[e[18]||(e[18]=t("svg",{class:"w-4 h-4 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{d:"M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"})],-1)),t("a",{href:`tel:${r.phone}`,class:"hover:text-blue-600 dark:hover:text-blue-400"},l(r.phone),9,yt)])):c("",!0)]),t("div",wt,[m(i,{to:`/app/personnel/${r.id}`,class:"flex-1 bg-blue-600 hover:bg-blue-700 text-white text-center px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200"},{default:y(()=>e[19]||(e[19]=[v(" Visualizza Profilo ")])),_:2,__:[19]},1032,["to"]),t("a",{href:`mailto:${r.email}`,class:"bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200"},e[20]||(e[20]=[t("svg",{class:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{d:"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"}),t("path",{d:"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"})],-1)]),8,kt)])])]))),128))])):(o(),s("div",bt,[t("div",_t,[t("table",zt,[e[25]||(e[25]=t("thead",{class:"bg-gray-50 dark:bg-gray-700"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Dipendente "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Posizione "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Dipartimento "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Contatti "),t("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Azioni ")])],-1)),t("tbody",Mt,[(o(!0),s(w,null,k(f.value,r=>{var M;return o(),s("tr",{key:r.id,class:"hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"},[t("td",Ct,[t("div",Vt,[t("div",Ht,[t("div",Bt,[r.profile_image?(o(),s("img",{key:0,src:r.profile_image,alt:r.full_name,class:"w-10 h-10 rounded-full object-cover"},null,8,Lt)):(o(),s("svg",Tt,e[21]||(e[21]=[t("path",{"fill-rule":"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z","clip-rule":"evenodd"},null,-1)])))])]),t("div",$t,[t("div",Pt,l(r.full_name),1),t("div",jt,l(r.email),1)])])]),t("td",Et,[t("div",At,l(r.position||"Posizione non specificata"),1)]),t("td",Dt,[t("div",Nt,l(((M=r.department)==null?void 0:M.name)||"Nessun dipartimento"),1)]),t("td",Ft,[t("div",St,[t("a",{href:`mailto:${r.email}`,class:"text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300"},e[22]||(e[22]=[t("svg",{class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{d:"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"}),t("path",{d:"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"})],-1)]),8,It),r.phone?(o(),s("a",{key:0,href:`tel:${r.phone}`,class:"text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300"},e[23]||(e[23]=[t("svg",{class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{d:"M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"})],-1)]),8,Ut)):c("",!0)])]),t("td",Qt,[m(i,{to:`/app/personnel/${r.id}`,class:"text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300"},{default:y(()=>e[24]||(e[24]=[v(" Visualizza Profilo ")])),_:2,__:[24]},1032,["to"])])])}),128))])])])]))])):(o(),s("div",qt,[e[26]||(e[26]=t("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})],-1)),e[27]||(e[27]=t("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun dipendente trovato",-1)),t("p",Gt,l(_.value?"Prova a modificare i filtri di ricerca.":"Non ci sono dipendenti nella directory."),1)]))])}}};export{Rt as default};

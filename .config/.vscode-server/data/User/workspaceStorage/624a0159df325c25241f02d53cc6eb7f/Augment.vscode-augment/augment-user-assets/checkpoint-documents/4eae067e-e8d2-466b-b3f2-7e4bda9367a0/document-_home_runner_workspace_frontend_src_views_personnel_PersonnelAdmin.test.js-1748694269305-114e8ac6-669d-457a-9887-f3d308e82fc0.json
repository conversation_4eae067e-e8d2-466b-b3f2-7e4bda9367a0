{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/personnel/PersonnelAdmin.test.js"}, "originalCode": "import { describe, it, expect, beforeEach, vi } from 'vitest'\nimport { screen, fireEvent } from '@testing-library/vue'\nimport PersonnelAdmin from './PersonnelAdmin.vue'\nimport { mountComponent, mockFetch, mockApiResponse, createMockUser, createMockDepartment, createMockSkill } from '@/test/utils'\n\ndescribe('PersonnelAdmin', () => {\n  beforeEach(() => {\n    // Mock API responses\n    mockFetch({\n      '/api/personnel/users': mockApiResponse({\n        users: [createMockUser(), createMockUser({ id: 2, username: 'user2' })],\n        total: 2,\n        page: 1,\n        per_page: 20\n      }),\n      '/api/personnel/departments': mockApiResponse({\n        departments: [createMockDepartment()]\n      }),\n      '/api/personnel/skills': mockApiResponse({\n        skills: [createMockSkill()],\n        categories: ['Programming']\n      }),\n      '/api/personnel/orgchart': mockApiResponse({\n        stats: {\n          total_employees: 10,\n          total_departments: 3,\n          total_managers: 2,\n          avg_profile_completion: 85.5\n        }\n      })\n    })\n  })\n\n  it('renders all tabs correctly', async () => {\n    const wrapper = mountComponent(PersonnelAdmin)\n    \n    // Check if all tabs are present\n    expect(wrapper.find('[data-testid=\"tab-users\"]').exists()).toBe(true)\n    expect(wrapper.find('[data-testid=\"tab-departments\"]').exists()).toBe(true)\n    expect(wrapper.find('[data-testid=\"tab-skills\"]').exists()).toBe(true)\n    expect(wrapper.find('[data-testid=\"tab-analytics\"]').exists()).toBe(true)\n    expect(wrapper.find('[data-testid=\"tab-bulk\"]').exists()).toBe(true)\n  })\n\n  it('loads users data on mount', async () => {\n    const wrapper = mountComponent(PersonnelAdmin)\n    \n    // Wait for component to load\n    await wrapper.vm.$nextTick()\n    \n    // Check if fetch was called for users\n    expect(fetch).toHaveBeenCalledWith('/api/personnel/users', expect.any(Object))\n  })\n\n  it('switches between tabs correctly', async () => {\n    const wrapper = mountComponent(PersonnelAdmin)\n    \n    // Initially should show users tab\n    expect(wrapper.vm.activeTab).toBe('users')\n    \n    // Click on departments tab\n    await wrapper.find('[data-testid=\"tab-departments\"]').trigger('click')\n    expect(wrapper.vm.activeTab).toBe('departments')\n    \n    // Click on skills tab\n    await wrapper.find('[data-testid=\"tab-skills\"]').trigger('click')\n    expect(wrapper.vm.activeTab).toBe('skills')\n  })\n\n  it('updates URL hash when switching tabs', async () => {\n    const wrapper = mountComponent(PersonnelAdmin)\n    \n    // Mock window.location.hash\n    delete window.location\n    window.location = { hash: '' }\n    \n    // Switch to departments tab\n    await wrapper.find('[data-testid=\"tab-departments\"]').trigger('click')\n    \n    // Should update hash (this would be handled by the component)\n    expect(wrapper.vm.activeTab).toBe('departments')\n  })\n\n  it('handles API errors gracefully', async () => {\n    // Mock API error\n    fetch.mockRejectedValueOnce(new Error('API Error'))\n    \n    const wrapper = mountComponent(PersonnelAdmin)\n    \n    // Wait for component to handle error\n    await wrapper.vm.$nextTick()\n    \n    // Component should still render without crashing\n    expect(wrapper.exists()).toBe(true)\n  })\n\n  it('shows loading state while fetching data', async () => {\n    // Mock delayed response\n    fetch.mockImplementationOnce(() => \n      new Promise(resolve => \n        setTimeout(() => resolve(mockApiResponse({ users: [] })), 100)\n      )\n    )\n    \n    const wrapper = mountComponent(PersonnelAdmin)\n    \n    // Should show loading state initially\n    expect(wrapper.vm.loading).toBe(true)\n  })\n\n  it('displays correct tab icons', async () => {\n    const wrapper = mountComponent(PersonnelAdmin)\n    \n    // Check if tab icons are present\n    const usersTab = wrapper.find('[data-testid=\"tab-users\"]')\n    const departmentsTab = wrapper.find('[data-testid=\"tab-departments\"]')\n    const skillsTab = wrapper.find('[data-testid=\"tab-skills\"]')\n    \n    expect(usersTab.find('svg').exists()).toBe(true)\n    expect(departmentsTab.find('svg').exists()).toBe(true)\n    expect(skillsTab.find('svg').exists()).toBe(true)\n  })\n\n  it('handles tab content rendering correctly', async () => {\n    const wrapper = mountComponent(PersonnelAdmin)\n    \n    // Users tab should be active by default\n    expect(wrapper.findComponent({ name: 'UsersManagement' }).exists()).toBe(true)\n    \n    // Switch to departments tab\n    await wrapper.find('[data-testid=\"tab-departments\"]').trigger('click')\n    expect(wrapper.findComponent({ name: 'DepartmentsManagement' }).exists()).toBe(true)\n    \n    // Switch to skills tab\n    await wrapper.find('[data-testid=\"tab-skills\"]').trigger('click')\n    expect(wrapper.findComponent({ name: 'SkillsManagement' }).exists()).toBe(true)\n  })\n\n  it('emits events when child components trigger actions', async () => {\n    const wrapper = mountComponent(PersonnelAdmin)\n    \n    // Simulate user creation event from child component\n    const usersComponent = wrapper.findComponent({ name: 'UsersManagement' })\n    if (usersComponent.exists()) {\n      await usersComponent.vm.$emit('user-created', createMockUser())\n      \n      // Should trigger data reload\n      expect(fetch).toHaveBeenCalledWith('/api/personnel/users', expect.any(Object))\n    }\n  })\n\n  it('maintains responsive design classes', async () => {\n    const wrapper = mountComponent(PersonnelAdmin)\n    \n    // Check for responsive classes\n    const container = wrapper.find('.container')\n    expect(container.classes()).toContain('mx-auto')\n    expect(container.classes()).toContain('px-4')\n  })\n})\n"}
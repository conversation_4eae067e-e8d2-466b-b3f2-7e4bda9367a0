import{S as se,i as ce,s as ie,a as ae,b as We,I as Xe,J as Je,K as Qe,L as Ye,h as b,d as ke,M as Ze,g as et,n as z,j as Se,P as Q,Q as S,D as M,E as ue,c as y,e as L,f as D,V as Y,W as Z,X as ee,u as p,q as j,t as d,r as O,a9 as dt,T as ze,y as A,z as R,B as k,G as T,H as te,aq as Pe,a4 as Le,a6 as ft,w as he,x as we,A as ve,a2 as ge,ae as Ce,a0 as tt,_ as an,am as nt,a7 as Oe,aa as Ln,a3 as xt,ay as Ve,ag as ut,ac as ln,ad as He}from"./SpinnerAugment-BRymMBwV.js";import{B as Cn,A as un,a as mt,b as $t,C as An,g as Rn}from"./main-panel-Cc1uNcRa.js";import{d as _t,T as kn}from"./Content-CZt_q_72.js";import{R as ot}from"./open-in-new-window-_CQmfLgB.js";import{R as Sn}from"./types-BSMhNRWH.js";import{G as gt}from"./folder-Dee44ws-.js";import{B as Ue}from"./ButtonAugment-B4rD0Iq1.js";import{C as rt,P as In}from"./pen-to-square-DsQhBKje.js";import{T as De}from"./TextTooltipAugment-VmEmcMVL.js";import{f as qe}from"./index-DNgdG9gK.js";import{M as ht,R as mn}from"./magnifying-glass-Dmvedn_X.js";import{C as $n,G as wt,T as Fn}from"./github-DCBOV_oD.js";import{e as Ke,u as pn,o as dn}from"./BaseButton-rKFNr-KO.js";import{D as le,C as Nn,T as En}from"./index-C57dba63.js";import{T as fn}from"./terminal-BinWa3Yp.js";import{A as Dn}from"./arrow-up-right-from-square-CGpkfGTp.js";import{I as vt}from"./IconButtonAugment-5yqT_m78.js";import{T as gn}from"./Keybindings-BL1lLIIt.js";import{a as Lt}from"./types-LfaCSdmF.js";import{E as Pn}from"./exclamation-triangle-B1zowysU.js";import{T as Un}from"./StatusIndicator-bsJDs3Ra.js";import"./layer-group-DxRJbFig.js";import"./design-system-init-BYyh-Ygh.js";import"./test_service_pb-B6vKXZrG.js";import"./chat-types-NgqNgjwU.js";import"./diff-utils-UT8EbVpu.js";import"./toggleHighContrast-CwIv4U26.js";import"./preload-helper-Dv6uf1Os.js";import"./index-C59i2ECO.js";import"./isObjectLike-CaDwJzWd.js";import"./globals-D0QH3NT1.js";import"./await_block-B6zp5aG7.js";import"./CardAugment-BpvKVhgc.js";import"./ellipsis-bUrc34Ic.js";import"./keypress-DD1aQVr0.js";import"./file-paths-BcSg4gks.js";import"./Filespan-V8rQ2geT.js";import"./folder-opened-qXv2xhk3.js";import"./lodash-xZzKttBF.js";import"./MaterialIcon-D3827jJW.js";import"./types-DvVg976p.js";import"./autofix-state-d-ymFdyn.js";import"./VSCodeCodicon-C_4gSY6s.js";import"./augment-logo-Cb6FLr8P.js";import"./chat-flags-model-D7CElkH1.js";import"./TextAreaAugment-DJ6NCdxI.js";function Ct(r){const e=r.match(/github\.com\/([^/]+)\/([^/]+?)(?:\.git|\/|$)/);if(e)return{owner:e[1],name:e[2]}}function At(r){return r.replace(/^origin\//,"")}function Bn(r){let e,n,t=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},r[0]],o={};for(let s=0;s<t.length;s+=1)o=ae(o,t[s]);return{c(){e=We("svg"),n=new Xe(!0),this.h()},l(s){e=Je(s,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var c=Qe(e);n=Ye(c,!0),c.forEach(b),this.h()},h(){n.a=null,ke(e,o)},m(s,c){Ze(s,e,c),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M472 224c13.3 0 24-10.7 24-24V56c0-13.3-10.7-24-24-24s-24 10.7-24 24v80.1l-20-23.5C387 63.4 325.1 32 256 32 132.3 32 32 132.3 32 256s100.3 224 224 224c50.4 0 97-16.7 134.4-44.8 10.6-8 12.7-23 4.8-33.6s-23-12.7-33.6-4.8C332.2 418.9 295.7 432 256 432c-97.2 0-176-78.8-176-176S158.8 80 256 80c54.3 0 102.9 24.6 135.2 63.4l.1.2 27.6 32.4H328c-13.3 0-24 10.7-24 24s10.7 24 24 24z"/>',e)},p(s,[c]){ke(e,o=et(t,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&c&&s[0]]))},i:z,o:z,d(s){s&&b(e)}}}function Mn(r,e,n){return r.$$set=t=>{n(0,e=ae(ae({},e),Se(t)))},[e=Se(e)]}class zn extends se{constructor(e){super(),ce(this,e,Mn,Bn,ie,{})}}const Tn=r=>({}),Rt=r=>({}),Gn=r=>({}),kt=r=>({}),jn=r=>({}),St=r=>({}),On=r=>({}),It=r=>({});function Vn(r){let e;return{c(){e=T(r[0])},m(n,t){L(n,e,t)},p(n,t){1&t&&te(e,n[0])},d(n){n&&b(e)}}}function Ft(r){let e,n;const t=r[3].subtitle,o=Q(t,r,r[4],kt),s=o||function(c){let i,a;return i=new ze({props:{size:2,$$slots:{default:[Hn]},$$scope:{ctx:c}}}),{c(){A(i.$$.fragment)},m(l,m){R(i,l,m),a=!0},p(l,m){const u={};18&m&&(u.$$scope={dirty:m,ctx:l}),i.$set(u)},i(l){a||(p(i.$$.fragment,l),a=!0)},o(l){d(i.$$.fragment,l),a=!1},d(l){k(i,l)}}}(r);return{c(){e=S("div"),s&&s.c(),y(e,"class","c-card-button__subtitle svelte-z367s9")},m(c,i){L(c,e,i),s&&s.m(e,null),n=!0},p(c,i){o?o.p&&(!n||16&i)&&Y(o,t,c,c[4],n?ee(t,c[4],i,Gn):Z(c[4]),kt):s&&s.p&&(!n||2&i)&&s.p(c,n?i:-1)},i(c){n||(p(s,c),n=!0)},o(c){d(s,c),n=!1},d(c){c&&b(e),s&&s.d(c)}}}function Hn(r){let e;return{c(){e=T(r[1])},m(n,t){L(n,e,t)},p(n,t){2&t&&te(e,n[1])},d(n){n&&b(e)}}}function Nt(r){let e,n;const t=r[3].iconRight,o=Q(t,r,r[4],Rt);return{c(){e=S("div"),o&&o.c(),y(e,"class","c-card-button__icon-right svelte-z367s9")},m(s,c){L(s,e,c),o&&o.m(e,null),n=!0},p(s,c){o&&o.p&&(!n||16&c)&&Y(o,t,s,s[4],n?ee(t,s[4],c,Tn):Z(s[4]),Rt)},i(s){n||(p(o,s),n=!0)},o(s){d(o,s),n=!1},d(s){s&&b(e),o&&o.d(s)}}}function qn(r){let e,n,t,o,s,c,i,a;const l=r[3].iconLeft,m=Q(l,r,r[4],It),u=r[3].title,w=Q(u,r,r[4],St),h=w||function($){let v,_;return v=new ze({props:{size:2,$$slots:{default:[Vn]},$$scope:{ctx:$}}}),{c(){A(v.$$.fragment)},m(I,P){R(v,I,P),_=!0},p(I,P){const x={};17&P&&(x.$$scope={dirty:P,ctx:I}),v.$set(x)},i(I){_||(p(v.$$.fragment,I),_=!0)},o(I){d(v.$$.fragment,I),_=!1},d(I){k(v,I)}}}(r);let g=r[1]&&Ft(r),f=r[2].iconRight&&Nt(r);return{c(){e=S("div"),m&&m.c(),n=M(),t=S("div"),o=S("div"),h&&h.c(),s=M(),g&&g.c(),c=M(),f&&f.c(),i=ue(),y(e,"class","c-card-button__icon-left svelte-z367s9"),y(o,"class","c-card-button__title svelte-z367s9"),y(t,"class","c-card-button__content svelte-z367s9")},m($,v){L($,e,v),m&&m.m(e,null),L($,n,v),L($,t,v),D(t,o),h&&h.m(o,null),D(t,s),g&&g.m(t,null),L($,c,v),f&&f.m($,v),L($,i,v),a=!0},p($,[v]){m&&m.p&&(!a||16&v)&&Y(m,l,$,$[4],a?ee(l,$[4],v,On):Z($[4]),It),w?w.p&&(!a||16&v)&&Y(w,u,$,$[4],a?ee(u,$[4],v,jn):Z($[4]),St):h&&h.p&&(!a||1&v)&&h.p($,a?v:-1),$[1]?g?(g.p($,v),2&v&&p(g,1)):(g=Ft($),g.c(),p(g,1),g.m(t,null)):g&&(j(),d(g,1,1,()=>{g=null}),O()),$[2].iconRight?f?(f.p($,v),4&v&&p(f,1)):(f=Nt($),f.c(),p(f,1),f.m(i.parentNode,i)):f&&(j(),d(f,1,1,()=>{f=null}),O())},i($){a||(p(m,$),p(h,$),p(g),p(f),a=!0)},o($){d(m,$),d(h,$),d(g),d(f),a=!1},d($){$&&(b(e),b(n),b(t),b(c),b(i)),m&&m.d($),h&&h.d($),g&&g.d(),f&&f.d($)}}}function Kn(r,e,n){let{$$slots:t={},$$scope:o}=e;const s=dt(t);let{title:c="Select an option"}=e,{subtitle:i=""}=e;return r.$$set=a=>{"title"in a&&n(0,c=a.title),"subtitle"in a&&n(1,i=a.subtitle),"$$scope"in a&&n(4,o=a.$$scope)},[c,i,s,t,o]}class hn extends se{constructor(e){super(),ce(this,e,Kn,qn,ie,{title:0,subtitle:1})}}const Wn=r=>({}),Et=r=>({slot:"iconLeft"}),Xn=r=>({}),Dt=r=>({slot:"iconRight"});function Pt(r,e,n){const t=r.slice();return t[19]=e[n],t}const Jn=r=>({}),Ut=r=>({}),Qn=r=>({}),Bt=r=>({}),Yn=r=>({}),Mt=r=>({slot:"iconLeft"}),Zn=r=>({}),zt=r=>({slot:"title"}),eo=r=>({}),Tt=r=>({slot:"iconRight"});function to(r){let e,n,t,o,s;return n=new hn({props:{title:r[3],subtitle:r[4],$$slots:{iconRight:[ro],iconLeft:[oo]},$$scope:{ctx:r}}}),{c(){e=S("button"),A(n.$$.fragment),y(e,"class","c-card-button__display svelte-1km5ln2"),y(e,"type","button"),e.disabled=r[10]},m(c,i){L(c,e,i),R(n,e,null),t=!0,o||(s=[Le(e,"click",r[16]),Le(e,"keydown",r[17])],o=!0)},p(c,i){const a={};8&i&&(a.title=c[3]),16&i&&(a.subtitle=c[4]),262144&i&&(a.$$scope={dirty:i,ctx:c}),n.$set(a),(!t||1024&i)&&(e.disabled=c[10])},i(c){t||(p(n.$$.fragment,c),t=!0)},o(c){d(n.$$.fragment,c),t=!1},d(c){c&&b(e),k(n),o=!1,ft(s)}}}function no(r){let e,n,t;function o(c){r[15](c)}let s={onOpenChange:r[9],$$slots:{default:[fo]},$$scope:{ctx:r}};return r[1]!==void 0&&(s.requestClose=r[1]),e=new le.Root({props:s}),he.push(()=>we(e,"requestClose",o)),{c(){A(e.$$.fragment)},m(c,i){R(e,c,i),t=!0},p(c,i){const a={};512&i&&(a.onOpenChange=c[9]),263641&i&&(a.$$scope={dirty:i,ctx:c}),!n&&2&i&&(n=!0,a.requestClose=c[1],ve(()=>n=!1)),e.$set(a)},i(c){t||(p(e.$$.fragment,c),t=!0)},o(c){d(e.$$.fragment,c),t=!1},d(c){k(e,c)}}}function oo(r){let e;const n=r[13].iconLeft,t=Q(n,r,r[18],Et);return{c(){t&&t.c()},m(o,s){t&&t.m(o,s),e=!0},p(o,s){t&&t.p&&(!e||262144&s)&&Y(t,n,o,o[18],e?ee(n,o[18],s,Wn):Z(o[18]),Et)},i(o){e||(p(t,o),e=!0)},o(o){d(t,o),e=!1},d(o){t&&t.d(o)}}}function ro(r){let e;const n=r[13].iconRight,t=Q(n,r,r[18],Dt);return{c(){t&&t.c()},m(o,s){t&&t.m(o,s),e=!0},p(o,s){t&&t.p&&(!e||262144&s)&&Y(t,n,o,o[18],e?ee(n,o[18],s,Xn):Z(o[18]),Dt)},i(o){e||(p(t,o),e=!0)},o(o){d(t,o),e=!1},d(o){t&&t.d(o)}}}function so(r){let e;const n=r[13].iconLeft,t=Q(n,r,r[18],Mt);return{c(){t&&t.c()},m(o,s){t&&t.m(o,s),e=!0},p(o,s){t&&t.p&&(!e||262144&s)&&Y(t,n,o,o[18],e?ee(n,o[18],s,Yn):Z(o[18]),Mt)},i(o){e||(p(t,o),e=!0)},o(o){d(t,o),e=!1},d(o){t&&t.d(o)}}}function co(r){let e;const n=r[13].title,t=Q(n,r,r[18],zt),o=t||function(s){let c;return{c(){c=T(s[3])},m(i,a){L(i,c,a)},p(i,a){8&a&&te(c,i[3])},d(i){i&&b(c)}}}(r);return{c(){o&&o.c()},m(s,c){o&&o.m(s,c),e=!0},p(s,c){t?t.p&&(!e||262144&c)&&Y(t,n,s,s[18],e?ee(n,s[18],c,Zn):Z(s[18]),zt):o&&o.p&&(!e||8&c)&&o.p(s,e?c:-1)},i(s){e||(p(o,s),e=!0)},o(s){d(o,s),e=!1},d(s){o&&o.d(s)}}}function io(r){let e;const n=r[13].iconRight,t=Q(n,r,r[18],Tt),o=t||function(s){let c,i;return c=new $n({}),{c(){A(c.$$.fragment)},m(a,l){R(c,a,l),i=!0},i(a){i||(p(c.$$.fragment,a),i=!0)},o(a){d(c.$$.fragment,a),i=!1},d(a){k(c,a)}}}();return{c(){o&&o.c()},m(s,c){o&&o.m(s,c),e=!0},p(s,c){t&&t.p&&(!e||262144&c)&&Y(t,n,s,s[18],e?ee(n,s[18],c,eo):Z(s[18]),Tt)},i(s){e||(p(o,s),e=!0)},o(s){d(o,s),e=!1},d(s){o&&o.d(s)}}}function ao(r){let e,n,t,o;return n=new hn({props:{subtitle:r[4],$$slots:{iconRight:[io],title:[co],iconLeft:[so]},$$scope:{ctx:r}}}),{c(){e=S("div"),A(n.$$.fragment),y(e,"class","c-card-button__display svelte-1km5ln2"),y(e,"role","button"),y(e,"tabindex",t=r[10]?-1:0),ge(e,"disabled",r[10])},m(s,c){L(s,e,c),R(n,e,null),o=!0},p(s,c){const i={};16&c&&(i.subtitle=s[4]),262152&c&&(i.$$scope={dirty:c,ctx:s}),n.$set(i),(!o||1024&c&&t!==(t=s[10]?-1:0))&&y(e,"tabindex",t),(!o||1024&c)&&ge(e,"disabled",s[10])},i(s){o||(p(n.$$.fragment,s),o=!0)},o(s){d(n.$$.fragment,s),o=!1},d(s){s&&b(e),k(n)}}}function lo(r){let e,n;return e=new le.Label({props:{$$slots:{default:[mo]},$$scope:{ctx:r}}}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p(t,o){const s={};262400&o&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function uo(r){let e,n,t=[],o=new Map,s=Ke(r[6]);const c=i=>i[7](i[19]);for(let i=0;i<s.length;i+=1){let a=Pt(r,s,i),l=c(a);o.set(l,t[i]=Gt(l,a))}return{c(){for(let i=0;i<t.length;i+=1)t[i].c();e=ue()},m(i,a){for(let l=0;l<t.length;l+=1)t[l]&&t[l].m(i,a);L(i,e,a),n=!0},p(i,a){2241&a&&(s=Ke(i[6]),j(),t=pn(t,a,c,1,i,s,o,e.parentNode,dn,Gt,e,Pt),O())},i(i){if(!n){for(let a=0;a<s.length;a+=1)p(t[a]);n=!0}},o(i){for(let a=0;a<t.length;a+=1)d(t[a]);n=!1},d(i){i&&b(e);for(let a=0;a<t.length;a+=1)t[a].d(i)}}}function mo(r){let e;return{c(){e=T(r[8])},m(n,t){L(n,e,t)},p(n,t){256&t&&te(e,n[8])},d(n){n&&b(e)}}}function $o(r){let e,n,t=r[7](r[19])+"";return{c(){e=T(t),n=M()},m(o,s){L(o,e,s),L(o,n,s)},p(o,s){192&s&&t!==(t=o[7](o[19])+"")&&te(e,t)},d(o){o&&(b(e),b(n))}}}function Gt(r,e){let n,t,o;function s(){return e[14](e[19])}return t=new le.Item({props:{onSelect:s,highlight:e[0]===e[19],$$slots:{default:[$o]},$$scope:{ctx:e}}}),{key:r,first:null,c(){n=ue(),A(t.$$.fragment),this.first=n},m(c,i){L(c,n,i),R(t,c,i),o=!0},p(c,i){e=c;const a={};64&i&&(a.onSelect=s),65&i&&(a.highlight=e[0]===e[19]),262336&i&&(a.$$scope={dirty:i,ctx:e}),t.$set(a)},i(c){o||(p(t.$$.fragment,c),o=!0)},o(c){d(t.$$.fragment,c),o=!1},d(c){c&&b(n),k(t,c)}}}function po(r){let e,n,t,o,s;const c=r[13]["dropdown-top"],i=Q(c,r,r[18],Bt),a=r[13]["dropdown-content"],l=Q(a,r,r[18],Ut),m=l||function(u){let w,h,g,f;const $=[uo,lo],v=[];function _(I,P){return I[6].length>0?0:1}return w=_(u),h=v[w]=$[w](u),{c(){h.c(),g=ue()},m(I,P){v[w].m(I,P),L(I,g,P),f=!0},p(I,P){let x=w;w=_(I),w===x?v[w].p(I,P):(j(),d(v[x],1,1,()=>{v[x]=null}),O(),h=v[w],h?h.p(I,P):(h=v[w]=$[w](I),h.c()),p(h,1),h.m(g.parentNode,g))},i(I){f||(p(h),f=!0)},o(I){d(h),f=!1},d(I){I&&b(g),v[w].d(I)}}}(r);return{c(){e=S("div"),n=S("div"),i&&i.c(),t=M(),o=S("div"),m&&m.c(),y(n,"class","c-card-button__dropdown-top svelte-1km5ln2"),y(o,"class","c-card-button__dropdown-content svelte-1km5ln2"),y(e,"class","c-card__dropdown-contents svelte-1km5ln2")},m(u,w){L(u,e,w),D(e,n),i&&i.m(n,null),D(e,t),D(e,o),m&&m.m(o,null),s=!0},p(u,w){i&&i.p&&(!s||262144&w)&&Y(i,c,u,u[18],s?ee(c,u[18],w,Qn):Z(u[18]),Bt),l?l.p&&(!s||262144&w)&&Y(l,a,u,u[18],s?ee(a,u[18],w,Jn):Z(u[18]),Ut):m&&m.p&&(!s||449&w)&&m.p(u,s?w:-1)},i(u){s||(p(i,u),p(m,u),s=!0)},o(u){d(i,u),d(m,u),s=!1},d(u){u&&b(e),i&&i.d(u),m&&m.d(u)}}}function fo(r){let e,n,t,o;return e=new le.Trigger({props:{$$slots:{default:[ao]},$$scope:{ctx:r}}}),t=new le.Content({props:{align:"start",side:"bottom",$$slots:{default:[po]},$$scope:{ctx:r}}}),{c(){A(e.$$.fragment),n=M(),A(t.$$.fragment)},m(s,c){R(e,s,c),L(s,n,c),R(t,s,c),o=!0},p(s,c){const i={};263192&c&&(i.$$scope={dirty:c,ctx:s}),e.$set(i);const a={};262593&c&&(a.$$scope={dirty:c,ctx:s}),t.$set(a)},i(s){o||(p(e.$$.fragment,s),p(t.$$.fragment,s),o=!0)},o(s){d(e.$$.fragment,s),d(t.$$.fragment,s),o=!1},d(s){s&&b(n),k(e,s),k(t,s)}}}function go(r){let e,n,t,o;const s=[no,to],c=[];function i(a,l){return a[2]==="dropdown"?0:1}return n=i(r),t=c[n]=s[n](r),{c(){e=S("div"),t.c(),y(e,"class","c-card-button svelte-1km5ln2")},m(a,l){L(a,e,l),c[n].m(e,null),o=!0},p(a,[l]){let m=n;n=i(a),n===m?c[n].p(a,l):(j(),d(c[m],1,1,()=>{c[m]=null}),O(),t=c[n],t?t.p(a,l):(t=c[n]=s[n](a),t.c()),p(t,1),t.m(e,null))},i(a){o||(p(t),o=!0)},o(a){d(t),o=!1},d(a){a&&b(e),c[n].d()}}}function ho(r,e,n){let{$$slots:t={},$$scope:o}=e,{type:s="button"}=e,{title:c="Select an option"}=e,{subtitle:i=""}=e,{onClick:a=()=>{}}=e,{items:l=[]}=e,{selectedItem:m}=e,{formatItemLabel:u=_=>(_==null?void 0:_.toString())||""}=e,{noItemsLabel:w="No items found"}=e,{onDropdownOpenChange:h=()=>{}}=e,{requestClose:g=()=>{}}=e,{disabled:f=!1}=e;function $(_){n(0,m=_),v("select",_)}const v=Pe();return r.$$set=_=>{"type"in _&&n(2,s=_.type),"title"in _&&n(3,c=_.title),"subtitle"in _&&n(4,i=_.subtitle),"onClick"in _&&n(5,a=_.onClick),"items"in _&&n(6,l=_.items),"selectedItem"in _&&n(0,m=_.selectedItem),"formatItemLabel"in _&&n(7,u=_.formatItemLabel),"noItemsLabel"in _&&n(8,w=_.noItemsLabel),"onDropdownOpenChange"in _&&n(9,h=_.onDropdownOpenChange),"requestClose"in _&&n(1,g=_.requestClose),"disabled"in _&&n(10,f=_.disabled),"$$scope"in _&&n(18,o=_.$$scope)},[m,g,s,c,i,a,l,u,w,h,f,$,v,t,_=>$(_),function(_){g=_,n(1,g)},()=>{a(),v("click")},_=>{_.key!=="Enter"&&_.key!==" "||(a(),v("click"))},o]}class wn extends se{constructor(e){super(),ce(this,e,ho,go,ie,{type:2,title:3,subtitle:4,onClick:5,items:6,selectedItem:0,formatItemLabel:7,noItemsLabel:8,onDropdownOpenChange:9,requestClose:1,disabled:10,selectItem:11})}get selectItem(){return this.$$.ctx[11]}}function wo(r){let e,n;return e=new wn({props:{type:"dropdown",title:"Connected to your GitHub account",$$slots:{"dropdown-content":[Ao],iconLeft:[bo]},$$scope:{ctx:r}}}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p(t,o){const s={};4100&o&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function vo(r){let e,n;return e=new wn({props:{type:"button",title:r[1]?"Cancel":"Connect to GitHub",onClick:r[4],$$slots:{iconRight:[Io],iconLeft:[Ro]},$$scope:{ctx:r}}}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p(t,o){const s={};2&o&&(s.title=t[1]?"Cancel":"Connect to GitHub"),4098&o&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function bo(r){let e,n;return e=new wt({props:{slot:"iconLeft"}}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p:z,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function yo(r){let e,n;return e=new ze({props:{size:1,weight:"medium",$$slots:{default:[_o]},$$scope:{ctx:r}}}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function xo(r){let e,n,t,o;return e=new nt({props:{slot:"iconLeft",useCurrentColor:!0,size:1}}),t=new ze({props:{size:1,weight:"medium",$$slots:{default:[Lo]},$$scope:{ctx:r}}}),{c(){A(e.$$.fragment),n=M(),A(t.$$.fragment)},m(s,c){R(e,s,c),L(s,n,c),R(t,s,c),o=!0},i(s){o||(p(e.$$.fragment,s),p(t.$$.fragment,s),o=!0)},o(s){d(e.$$.fragment,s),d(t.$$.fragment,s),o=!1},d(s){s&&b(n),k(e,s),k(t,s)}}}function _o(r){let e;return{c(){e=T("Revoke Access")},m(n,t){L(n,e,t)},d(n){n&&b(e)}}}function Lo(r){let e;return{c(){e=T("Revoking...")},m(n,t){L(n,e,t)},d(n){n&&b(e)}}}function Co(r){let e,n,t,o;const s=[xo,yo],c=[];function i(a,l){return a[2]?0:1}return e=i(r),n=c[e]=s[e](r),{c(){n.c(),t=ue()},m(a,l){c[e].m(a,l),L(a,t,l),o=!0},p(a,l){let m=e;e=i(a),e!==m&&(j(),d(c[m],1,1,()=>{c[m]=null}),O(),n=c[e],n||(n=c[e]=s[e](a),n.c()),p(n,1),n.m(t.parentNode,t))},i(a){o||(p(n),o=!0)},o(a){d(n),o=!1},d(a){a&&b(t),c[e].d(a)}}}function Ao(r){let e,n,t;return n=new le.Item({props:{color:"error",onSelect:r[6],$$slots:{default:[Co]},$$scope:{ctx:r}}}),{c(){e=S("div"),A(n.$$.fragment),y(e,"slot","dropdown-content")},m(o,s){L(o,e,s),R(n,e,null),t=!0},p(o,s){const c={};4100&s&&(c.$$scope={dirty:s,ctx:o}),n.$set(c)},i(o){t||(p(n.$$.fragment,o),t=!0)},o(o){d(n.$$.fragment,o),t=!1},d(o){o&&b(e),k(n)}}}function Ro(r){let e,n;return e=new wt({props:{slot:"iconLeft"}}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p:z,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function ko(r){let e,n;return e=new Nn({}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function So(r){let e,n;return e=new nt({props:{size:1,useCurrentColor:!0}}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function Io(r){let e,n,t,o;const s=[So,ko],c=[];function i(a,l){return a[1]?0:1}return n=i(r),t=c[n]=s[n](r),{c(){e=S("div"),t.c(),y(e,"slot","iconRight")},m(a,l){L(a,e,l),c[n].m(e,null),o=!0},p(a,l){let m=n;n=i(a),n!==m&&(j(),d(c[m],1,1,()=>{c[m]=null}),O(),t=c[n],t||(t=c[n]=s[n](a),t.c()),p(t,1),t.m(e,null))},i(a){o||(p(t),o=!0)},o(a){d(t),o=!1},d(a){a&&b(e),c[n].d()}}}function Fo(r){let e,n,t,o,s;const c=[vo,wo],i=[];function a(l,m){return l[0]?1:0}return t=a(r),o=i[t]=c[t](r),{c(){e=S("div"),n=S("div"),o.c(),y(n,"class","github-auth-button"),y(e,"class","github-auth-card svelte-zdlnsr")},m(l,m){L(l,e,m),D(e,n),i[t].m(n,null),s=!0},p(l,[m]){let u=t;t=a(l),t===u?i[t].p(l,m):(j(),d(i[u],1,1,()=>{i[u]=null}),O(),o=i[t],o?o.p(l,m):(o=i[t]=c[t](l),o.c()),p(o,1),o.m(n,null))},i(l){s||(p(o),s=!0)},o(l){d(o),s=!1},d(l){l&&b(e),i[t].d()}}}function No(r,e,n){const t=Pe(),o=Ce(gt.key);let s=!1,c=!1,i=!1,a=null,l=null;async function m(){if(!i){n(2,i=!0);try{const u=await o.revokeGithubAccess();u.success?(n(0,s=!1),t("authStateChange",{isAuthenticated:!1})):console.error("Failed to revoke GitHub access:",u.message)}catch(u){console.error("Error revoking GitHub access:",u)}finally{n(2,i=!1)}}}return tt(async()=>{await async function(){try{const u=await o.isGithubAuthenticated();u!==s?(n(0,s=u),t("authStateChange",{isAuthenticated:s})):n(0,s=u)}catch(u){console.error("Failed to check GitHub authentication status:",u),n(0,s=!1),t("authStateChange",{isAuthenticated:!1})}}()}),an(()=>{a&&(clearTimeout(a),a=null),l&&(clearInterval(l),l=null)}),[s,c,i,()=>{},async function(){if(c)return n(1,c=!1),void(a&&(clearTimeout(a),a=null));n(1,c=!0);try{await o.authenticateGithub(),l=setInterval(async()=>{await o.isGithubAuthenticated()&&(n(0,s=!0),n(1,c=!1),t("authStateChange",{isAuthenticated:!0}),l&&clearInterval(l),a&&(clearTimeout(a),a=null))},5e3),a=setTimeout(()=>{l&&clearInterval(l),n(1,c=!1),a=null},6e4)}catch(u){console.error("Failed to authenticate with GitHub:",u),n(1,c=!1)}},m,()=>{m()}]}class Eo extends se{constructor(e){super(),ce(this,e,No,Fo,ie,{})}}const Do=r=>({}),jt=r=>({});function Ot(r,e,n){const t=r.slice();return t[27]=e[n],t[29]=n,t}const Po=r=>({item:64&r}),Vt=r=>({item:r[27]}),Uo=r=>({}),Ht=r=>({}),Bo=r=>({}),qt=r=>({}),Mo=r=>({}),Kt=r=>({}),zo=r=>({}),Wt=r=>({});function To(r){let e,n,t,o,s,c,i,a,l,m,u,w,h,g;const f=[Oo,jo],$=[];function v(x,U){return x[4]?0:1}o=v(r),s=$[o]=f[o](r);const _=[Ho,Vo],I=[];function P(x,U){return x[17].title?0:1}return a=P(r),l=I[a]=_[a](r),w=new $n({}),{c(){e=S("div"),n=S("div"),t=S("div"),s.c(),c=M(),i=S("span"),l.c(),m=M(),u=S("div"),A(w.$$.fragment),y(t,"class","c-searchable-dropdown__icon svelte-jowwyu"),y(i,"class","c-searchable-dropdown__button-text svelte-jowwyu"),y(n,"class","c-searchable-dropdown__icon-text svelte-jowwyu"),y(u,"class","c-searchable-dropdown__chevron svelte-jowwyu"),y(e,"class","c-searchable-dropdown__button svelte-jowwyu"),y(e,"role","button"),y(e,"tabindex",h=r[5]?-1:0),ge(e,"c-searchable-dropdown__button--disabled",r[5])},m(x,U){L(x,e,U),D(e,n),D(n,t),$[o].m(t,null),D(n,c),D(n,i),I[a].m(i,null),D(e,m),D(e,u),R(w,u,null),g=!0},p(x,U){let E=o;o=v(x),o===E?$[o].p(x,U):(j(),d($[E],1,1,()=>{$[E]=null}),O(),s=$[o],s?s.p(x,U):(s=$[o]=f[o](x),s.c()),p(s,1),s.m(t,null));let q=a;a=P(x),a===q?I[a].p(x,U):(j(),d(I[q],1,1,()=>{I[q]=null}),O(),l=I[a],l?l.p(x,U):(l=I[a]=_[a](x),l.c()),p(l,1),l.m(i,null)),(!g||32&U&&h!==(h=x[5]?-1:0))&&y(e,"tabindex",h),(!g||32&U)&&ge(e,"c-searchable-dropdown__button--disabled",x[5])},i(x){g||(p(s),p(l),p(w.$$.fragment,x),g=!0)},o(x){d(s),d(l),d(w.$$.fragment,x),g=!1},d(x){x&&b(e),$[o].d(),I[a].d(),k(w)}}}function Go(r){let e,n,t,o,s,c,i;const a=r[18].searchIcon,l=Q(a,r,r[25],Wt),m=l||function(u){let w;const h=u[18].icon,g=Q(h,u,u[25],Kt);return{c(){g&&g.c()},m(f,$){g&&g.m(f,$),w=!0},p(f,$){g&&g.p&&(!w||33554432&$)&&Y(g,h,f,f[25],w?ee(h,f[25],$,Mo):Z(f[25]),Kt)},i(f){w||(p(g,f),w=!0)},o(f){d(g,f),w=!1},d(f){g&&g.d(f)}}}(r);return{c(){e=S("div"),n=S("div"),m&&m.c(),t=M(),o=S("input"),y(n,"class","c-searchable-dropdown__icon svelte-jowwyu"),y(o,"type","text"),y(o,"class","c-searchable-dropdown__trigger-input svelte-jowwyu"),y(o,"placeholder",r[3]),y(e,"class","c-searchable-dropdown__input-container svelte-jowwyu")},m(u,w){L(u,e,w),D(e,n),m&&m.m(n,null),D(e,t),D(e,o),xt(o,r[0]),s=!0,c||(i=[Le(o,"input",r[21]),Le(o,"input",r[22]),Le(o,"click",Ve(r[19])),Le(o,"mousedown",Ve(r[20]))],c=!0)},p(u,w){l?l.p&&(!s||33554432&w)&&Y(l,a,u,u[25],s?ee(a,u[25],w,zo):Z(u[25]),Wt):m&&m.p&&(!s||33554432&w)&&m.p(u,s?w:-1),(!s||8&w)&&y(o,"placeholder",u[3]),1&w&&o.value!==u[0]&&xt(o,u[0])},i(u){s||(p(m,u),s=!0)},o(u){d(m,u),s=!1},d(u){u&&b(e),m&&m.d(u),c=!1,ft(i)}}}function jo(r){let e;const n=r[18].icon,t=Q(n,r,r[25],qt);return{c(){t&&t.c()},m(o,s){t&&t.m(o,s),e=!0},p(o,s){t&&t.p&&(!e||33554432&s)&&Y(t,n,o,o[25],e?ee(n,o[25],s,Bo):Z(o[25]),qt)},i(o){e||(p(t,o),e=!0)},o(o){d(t,o),e=!1},d(o){t&&t.d(o)}}}function Oo(r){let e,n;return e=new nt({props:{size:1,useCurrentColor:!0}}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p:z,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function Vo(r){let e,n=(r[4]?r[11]:r[2])+"";return{c(){e=T(n)},m(t,o){L(t,e,o)},p(t,o){2068&o&&n!==(n=(t[4]?t[11]:t[2])+"")&&te(e,n)},i:z,o:z,d(t){t&&b(e)}}}function Ho(r){let e;const n=r[18].title,t=Q(n,r,r[25],Ht);return{c(){t&&t.c()},m(o,s){t&&t.m(o,s),e=!0},p(o,s){t&&t.p&&(!e||33554432&s)&&Y(t,n,o,o[25],e?ee(n,o[25],s,Uo):Z(o[25]),Ht)},i(o){e||(p(t,o),e=!0)},o(o){d(t,o),e=!1},d(o){t&&t.d(o)}}}function qo(r){let e,n,t,o;const s=[Go,To],c=[];function i(a,l){return a[12]?0:1}return e=i(r),n=c[e]=s[e](r),{c(){n.c(),t=ue()},m(a,l){c[e].m(a,l),L(a,t,l),o=!0},p(a,l){let m=e;e=i(a),e===m?c[e].p(a,l):(j(),d(c[m],1,1,()=>{c[m]=null}),O(),n=c[e],n?n.p(a,l):(n=c[e]=s[e](a),n.c()),p(n,1),n.m(t.parentNode,t))},i(a){o||(p(n),o=!0)},o(a){d(n),o=!1},d(a){a&&b(t),c[e].d(a)}}}function Xt(r){let e,n;return e=new le.Content({props:{side:"bottom",align:"start",$$slots:{default:[Zo]},$$scope:{ctx:r}}}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p(t,o){const s={};33689298&o&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function Ko(r){let e,n;return e=new le.Item({props:{disabled:!0,$$slots:{default:[Jo]},$$scope:{ctx:r}}}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p(t,o){const s={};33555456&o&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function Wo(r){let e,n,t=[],o=new Map,s=Ke(r[6]);const c=i=>i[27]===null?`null-item-${i[29]}`:i[8](i[27]);for(let i=0;i<s.length;i+=1){let a=Ot(r,s,i),l=c(a);o.set(l,t[i]=Jt(l,a))}return{c(){for(let i=0;i<t.length;i+=1)t[i].c();e=ue()},m(i,a){for(let l=0;l<t.length;l+=1)t[l]&&t[l].m(i,a);L(i,e,a),n=!0},p(i,a){33620930&a&&(s=Ke(i[6]),j(),t=pn(t,a,c,1,i,s,o,e.parentNode,dn,Jt,e,Ot),O())},i(i){if(!n){for(let a=0;a<s.length;a+=1)p(t[a]);n=!0}},o(i){for(let a=0;a<t.length;a+=1)d(t[a]);n=!1},d(i){i&&b(e);for(let a=0;a<t.length;a+=1)t[a].d(i)}}}function Xo(r){let e,n;return e=new le.Item({props:{disabled:!0,$$slots:{default:[Yo]},$$scope:{ctx:r}}}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p(t,o){const s={};33556480&o&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function Jo(r){let e;return{c(){e=T(r[10])},m(n,t){L(n,e,t)},p(n,t){1024&t&&te(e,n[10])},d(n){n&&b(e)}}}function Qo(r){let e,n;const t=r[18].item,o=Q(t,r,r[25],Vt),s=o||function(c){let i,a=c[7](c[27])+"";return{c(){i=T(a)},m(l,m){L(l,i,m)},p(l,m){192&m&&a!==(a=l[7](l[27])+"")&&te(i,a)},d(l){l&&b(i)}}}(r);return{c(){s&&s.c(),e=M()},m(c,i){s&&s.m(c,i),L(c,e,i),n=!0},p(c,i){o?o.p&&(!n||33554496&i)&&Y(o,t,c,c[25],n?ee(t,c[25],i,Po):Z(c[25]),Vt):s&&s.p&&(!n||192&i)&&s.p(c,n?i:-1)},i(c){n||(p(s,c),n=!0)},o(c){d(s,c),n=!1},d(c){c&&b(e),s&&s.d(c)}}}function Jt(r,e){let n,t,o;function s(){return e[23](e[27])}return t=new le.Item({props:{onSelect:s,highlight:e[9]?e[9](e[27],e[1]):!!e[1]&&e[7](e[1])===e[7](e[27]),$$slots:{default:[Qo]},$$scope:{ctx:e}}}),{key:r,first:null,c(){n=ue(),A(t.$$.fragment),this.first=n},m(c,i){L(c,n,i),R(t,c,i),o=!0},p(c,i){e=c;const a={};64&i&&(a.onSelect=s),706&i&&(a.highlight=e[9]?e[9](e[27],e[1]):!!e[1]&&e[7](e[1])===e[7](e[27])),33554624&i&&(a.$$scope={dirty:i,ctx:e}),t.$set(a)},i(c){o||(p(t.$$.fragment,c),o=!0)},o(c){d(t.$$.fragment,c),o=!1},d(c){c&&b(n),k(t,c)}}}function Yo(r){let e,n,t,o,s,c;return n=new nt({props:{size:1,useCurrentColor:!0}}),{c(){e=S("div"),A(n.$$.fragment),t=M(),o=S("span"),s=T(r[11]),y(e,"class","c-searchable-dropdown__loading svelte-jowwyu")},m(i,a){L(i,e,a),R(n,e,null),D(e,t),D(e,o),D(o,s),c=!0},p(i,a){(!c||2048&a)&&te(s,i[11])},i(i){c||(p(n.$$.fragment,i),c=!0)},o(i){d(n.$$.fragment,i),c=!1},d(i){i&&b(e),k(n)}}}function Qt(r){let e;const n=r[18].footer,t=Q(n,r,r[25],jt);return{c(){t&&t.c()},m(o,s){t&&t.m(o,s),e=!0},p(o,s){t&&t.p&&(!e||33554432&s)&&Y(t,n,o,o[25],e?ee(n,o[25],s,Do):Z(o[25]),jt)},i(o){e||(p(t,o),e=!0)},o(o){d(t,o),e=!1},d(o){t&&t.d(o)}}}function Zo(r){let e,n,t,o,s,c;const i=[Xo,Wo,Ko],a=[];function l(u,w){return u[4]?0:u[6].length>0?1:2}e=l(r),n=a[e]=i[e](r);let m=r[17].footer&&Qt(r);return{c(){n.c(),t=M(),m&&m.c(),o=M(),s=S("div"),Ln(s,"margin-bottom","var(--ds-spacing-2)")},m(u,w){a[e].m(u,w),L(u,t,w),m&&m.m(u,w),L(u,o,w),L(u,s,w),c=!0},p(u,w){let h=e;e=l(u),e===h?a[e].p(u,w):(j(),d(a[h],1,1,()=>{a[h]=null}),O(),n=a[e],n?n.p(u,w):(n=a[e]=i[e](u),n.c()),p(n,1),n.m(t.parentNode,t)),u[17].footer?m?(m.p(u,w),131072&w&&p(m,1)):(m=Qt(u),m.c(),p(m,1),m.m(o.parentNode,o)):m&&(j(),d(m,1,1,()=>{m=null}),O())},i(u){c||(p(n),p(m),c=!0)},o(u){d(n),d(m),c=!1},d(u){u&&(b(t),b(o),b(s)),a[e].d(u),m&&m.d(u)}}}function er(r){let e,n,t,o;e=new le.Trigger({props:{$$slots:{default:[qo]},$$scope:{ctx:r}}});let s=!r[5]&&Xt(r);return{c(){A(e.$$.fragment),n=M(),s&&s.c(),t=ue()},m(c,i){R(e,c,i),L(c,n,i),s&&s.m(c,i),L(c,t,i),o=!0},p(c,i){const a={};33691709&i&&(a.$$scope={dirty:i,ctx:c}),e.$set(a),c[5]?s&&(j(),d(s,1,1,()=>{s=null}),O()):s?(s.p(c,i),32&i&&p(s,1)):(s=Xt(c),s.c(),p(s,1),s.m(t.parentNode,t))},i(c){o||(p(e.$$.fragment,c),p(s),o=!0)},o(c){d(e.$$.fragment,c),d(s),o=!1},d(c){c&&(b(n),b(t)),k(e,c),s&&s.d(c)}}}function tr(r){let e,n,t,o;function s(i){r[24](i)}let c={onOpenChange:r[14],$$slots:{default:[er]},$$scope:{ctx:r}};return r[13]!==void 0&&(c.requestClose=r[13]),n=new le.Root({props:c}),he.push(()=>we(n,"requestClose",s)),{c(){e=S("div"),A(n.$$.fragment),y(e,"class","c-searchable-dropdown svelte-jowwyu")},m(i,a){L(i,e,a),R(n,e,null),o=!0},p(i,[a]){const l={};33693439&a&&(l.$$scope={dirty:a,ctx:i}),!t&&8192&a&&(t=!0,l.requestClose=i[13],ve(()=>t=!1)),n.$set(l)},i(i){o||(p(n.$$.fragment,i),o=!0)},o(i){d(n.$$.fragment,i),o=!1},d(i){i&&b(e),k(n)}}}function nr(r,e,n){let{$$slots:t={},$$scope:o}=e;const s=dt(t);let{title:c=""}=e,{placeholder:i="Search..."}=e,{isLoading:a=!1}=e,{disabled:l=!1}=e,{searchValue:m=""}=e,{items:u=[]}=e,{selectedItem:w=null}=e,{itemLabelFn:h=E=>(E==null?void 0:E.toString())||""}=e,{itemKeyFn:g=E=>(E==null?void 0:E.toString())||""}=e,{isItemSelected:f}=e,{noItemsLabel:$="No items found"}=e,{loadingLabel:v="Loading..."}=e,_=!1,I=()=>{};const P=Pe();function x(E){n(0,m=E),P("search",E)}function U(E){n(1,w=E),P("select",E),I()}return r.$$set=E=>{"title"in E&&n(2,c=E.title),"placeholder"in E&&n(3,i=E.placeholder),"isLoading"in E&&n(4,a=E.isLoading),"disabled"in E&&n(5,l=E.disabled),"searchValue"in E&&n(0,m=E.searchValue),"items"in E&&n(6,u=E.items),"selectedItem"in E&&n(1,w=E.selectedItem),"itemLabelFn"in E&&n(7,h=E.itemLabelFn),"itemKeyFn"in E&&n(8,g=E.itemKeyFn),"isItemSelected"in E&&n(9,f=E.isItemSelected),"noItemsLabel"in E&&n(10,$=E.noItemsLabel),"loadingLabel"in E&&n(11,v=E.loadingLabel),"$$scope"in E&&n(25,o=E.$$scope)},[m,w,c,i,a,l,u,h,g,f,$,v,_,I,function(E){if(!l){if(n(12,_=E),E&&w){const q=h(w);n(0,m=q),P("search",""),setTimeout(()=>{const V=document.querySelector(".c-searchable-dropdown__trigger-input");V&&V.select()},0)}else E&&(n(0,m=""),P("search",""));P("openChange",E)}},x,U,s,t,function(E){Oe.call(this,r,E)},function(E){Oe.call(this,r,E)},function(){m=this.value,n(0,m)},E=>x(E.currentTarget.value),E=>U(E),function(E){I=E,n(13,I)},o]}class pt extends se{constructor(e){super(),ce(this,e,nr,tr,ie,{title:2,placeholder:3,isLoading:4,disabled:5,searchValue:0,items:6,selectedItem:1,itemLabelFn:7,itemKeyFn:8,isItemSelected:9,noItemsLabel:10,loadingLabel:11})}}function or(r){let e,n,t;return n=new rt({props:{color:"warning",variant:"soft",size:2,$$slots:{default:[lr]},$$scope:{ctx:r}}}),{c(){e=S("div"),A(n.$$.fragment),y(e,"class","c-commit-ref-selector__error svelte-14w5nl7")},m(o,s){L(o,e,s),R(n,e,null),t=!0},p(o,s){const c={};8195&s[0]|8&s[2]&&(c.$$scope={dirty:s,ctx:o}),n.$set(c)},i(o){t||(p(n.$$.fragment,o),t=!0)},o(o){d(n.$$.fragment,o),t=!1},d(o){o&&b(e),k(n)}}}function rr(r){var g,f;let e,n,t,o,s,c,i,a,l;function m($){r[30]($)}let u={title:((g=r[2])==null?void 0:g.name)||"Choose repository...",placeholder:"Search repositories...",itemKeyFn:hr,isLoading:r[5],disabled:!r[6].length,items:r[7],selectedItem:r[2],itemLabelFn:wr,noItemsLabel:"No repositories found",loadingLabel:"Loading repositories...",$$slots:{searchIcon:[mr],icon:[ur]},$$scope:{ctx:r}};function w($){r[35]($)}r[11]!==void 0&&(u.searchValue=r[11]),t=new pt({props:u}),he.push(()=>we(t,"searchValue",m)),t.$on("openChange",r[31]),t.$on("search",r[32]),t.$on("select",r[33]);let h={title:((f=r[3])==null?void 0:f.name)||"Choose branch...",itemKeyFn:vr,placeholder:"Search branches...",isLoading:r[16],disabled:r[14],items:r[8],selectedItem:r[3],itemLabelFn:br,noItemsLabel:"No branches found",loadingLabel:"Loading branches...",$$slots:{footer:[fr],searchIcon:[pr],icon:[$r]},$$scope:{ctx:r}};return r[12]!==void 0&&(h.searchValue=r[12]),i=new pt({props:h}),he.push(()=>we(i,"searchValue",w)),i.$on("openChange",r[36]),i.$on("search",r[37]),i.$on("select",r[38]),{c(){e=S("div"),n=S("div"),A(t.$$.fragment),s=M(),c=S("div"),A(i.$$.fragment),y(n,"class","c-commit-ref-selector__selector svelte-14w5nl7"),y(c,"class","c-commit-ref-selector__selector svelte-14w5nl7"),y(e,"class","c-commit-ref-selector__selectors-container svelte-14w5nl7")},m($,v){L($,e,v),D(e,n),R(t,n,null),D(e,s),D(e,c),R(i,c,null),l=!0},p($,v){var P,x;const _={};4&v[0]&&(_.title=((P=$[2])==null?void 0:P.name)||"Choose repository..."),32&v[0]&&(_.isLoading=$[5]),64&v[0]&&(_.disabled=!$[6].length),128&v[0]&&(_.items=$[7]),4&v[0]&&(_.selectedItem=$[2]),8&v[2]&&(_.$$scope={dirty:v,ctx:$}),!o&&2048&v[0]&&(o=!0,_.searchValue=$[11],ve(()=>o=!1)),t.$set(_);const I={};8&v[0]&&(I.title=((x=$[3])==null?void 0:x.name)||"Choose branch..."),65536&v[0]&&(I.isLoading=$[16]),16384&v[0]&&(I.disabled=$[14]),256&v[0]&&(I.items=$[8]),8&v[0]&&(I.selectedItem=$[3]),1552&v[0]|8&v[2]&&(I.$$scope={dirty:v,ctx:$}),!a&&4096&v[0]&&(a=!0,I.searchValue=$[12],ve(()=>a=!1)),i.$set(I)},i($){l||(p(t.$$.fragment,$),p(i.$$.fragment,$),l=!0)},o($){d(t.$$.fragment,$),d(i.$$.fragment,$),l=!1},d($){$&&b(e),k(t),k(i)}}}function sr(r){let e,n;return e=new Ue({props:{variant:"ghost",color:"warning",size:1,loading:r[1],class:"c-commit-ref-selector__fetch-button",$$slots:{iconLeft:[ar],default:[ir]},$$scope:{ctx:r}}}),e.$on("click",r[21]),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p(t,o){const s={};2&o[0]&&(s.loading=t[1]),8&o[2]&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function cr(r){let e,n;return e=new Eo({}),e.$on("authStateChange",r[39]),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p:z,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function ir(r){let e;return{c(){e=T("Reload available repos and branches")},m(n,t){L(n,e,t)},d(n){n&&b(e)}}}function ar(r){let e,n,t;return n=new mn({}),{c(){e=S("span"),A(n.$$.fragment),y(e,"slot","iconLeft"),y(e,"class","svelte-14w5nl7")},m(o,s){L(o,e,s),R(n,e,null),t=!0},p:z,i(o){t||(p(n.$$.fragment,o),t=!0)},o(o){d(n.$$.fragment,o),t=!1},d(o){o&&b(e),k(n)}}}function lr(r){let e,n,t,o,s,c,i;const a=[cr,sr],l=[];function m(u,w){return u[13]?1:0}return s=m(r),c=l[s]=a[s](r),{c(){e=S("div"),n=S("div"),t=T(r[0]),o=M(),c.c(),y(n,"class","c-commit-ref-selector__error-message svelte-14w5nl7"),y(e,"class","c-commit-ref-selector__error-content svelte-14w5nl7")},m(u,w){L(u,e,w),D(e,n),D(n,t),D(e,o),l[s].m(e,null),i=!0},p(u,w){(!i||1&w[0])&&te(t,u[0]);let h=s;s=m(u),s===h?l[s].p(u,w):(j(),d(l[h],1,1,()=>{l[h]=null}),O(),c=l[s],c?c.p(u,w):(c=l[s]=a[s](u),c.c()),p(c,1),c.m(e,null))},i(u){i||(p(c),i=!0)},o(u){d(c),i=!1},d(u){u&&b(e),l[s].d()}}}function ur(r){let e,n;return e=new wt({props:{slot:"icon"}}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p:z,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function mr(r){let e,n;return e=new ht({props:{slot:"searchIcon"}}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p:z,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function $r(r){let e,n;return e=new Cn({props:{slot:"icon"}}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p:z,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function pr(r){let e,n;return e=new ht({props:{slot:"searchIcon"}}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p:z,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function Yt(r){let e,n,t,o,s,c,i,a,l;return t=new zn({}),c=new ze({props:{size:2,color:"neutral",class:"c-commit-ref-selector__item-name",$$slots:{default:[dr]},$$scope:{ctx:r}}}),{c(){e=S("button"),n=S("div"),A(t.$$.fragment),o=M(),s=S("div"),A(c.$$.fragment),y(n,"class","c-commit-ref-selector__item-icon svelte-14w5nl7"),y(s,"class","c-commit-ref-selector__item-content svelte-14w5nl7"),y(e,"type","button"),y(e,"class","c-commit-ref-selector__item c-commit-ref-selector__item--loading svelte-14w5nl7")},m(m,u){L(m,e,u),D(e,n),R(t,n,null),D(e,o),D(e,s),R(c,s,null),i=!0,a||(l=Le(e,"click",r[34]),a=!0)},p(m,u){const w={};8&u[2]&&(w.$$scope={dirty:u,ctx:m}),c.$set(w)},i(m){i||(p(t.$$.fragment,m),p(c.$$.fragment,m),i=!0)},o(m){d(t.$$.fragment,m),d(c.$$.fragment,m),i=!1},d(m){m&&b(e),k(t),k(c),a=!1,l()}}}function dr(r){let e;return{c(){e=T("Load more branches")},m(n,t){L(n,e,t)},d(n){n&&b(e)}}}function fr(r){let e,n,t=r[10]&&!r[4]&&Yt(r);return{c(){t&&t.c(),e=ue()},m(o,s){t&&t.m(o,s),L(o,e,s),n=!0},p(o,s){o[10]&&!o[4]?t?(t.p(o,s),1040&s[0]&&p(t,1)):(t=Yt(o),t.c(),p(t,1),t.m(e.parentNode,e)):t&&(j(),d(t,1,1,()=>{t=null}),O())},i(o){n||(p(t),n=!0)},o(o){d(t),n=!1},d(o){o&&b(e),t&&t.d(o)}}}function gr(r){let e,n,t,o,s;const c=[rr,or],i=[];function a(l,m){return l[15]?l[15]?1:-1:0}return~(t=a(r))&&(o=i[t]=c[t](r)),{c(){e=S("div"),n=S("div"),o&&o.c(),y(n,"class","c-commit-ref-selector__content svelte-14w5nl7"),y(e,"class","c-commit-ref-selector svelte-14w5nl7")},m(l,m){L(l,e,m),D(e,n),~t&&i[t].m(n,null),s=!0},p(l,m){let u=t;t=a(l),t===u?~t&&i[t].p(l,m):(o&&(j(),d(i[u],1,1,()=>{i[u]=null}),O()),~t?(o=i[t],o?o.p(l,m):(o=i[t]=c[t](l),o.c()),p(o,1),o.m(n,null)):o=null)},i(l){s||(p(o),s=!0)},o(l){d(o),s=!1},d(l){l&&b(e),~t&&i[t].d()}}}const hr=r=>`${r.owner}-${r.name}`,wr=r=>`${r.owner}/${r.name}`,vr=r=>`${r.name}-${r.commit.sha}`,br=r=>r.name.replace("origin/","");function yr(r,e,n){let t,o,s,c;const i=Ce(gt.key),a=Pe(),l=Ce(ot.key);let m,u,{errorMessage:w=""}=e,{isLoading:h=!1}=e,{lastUsedBranchName:g=null}=e,{lastUsedRepoUrl:f=null}=e,$=[],v=$,_=[],I=_,P=!1,x=!1,U=0,E=!1,q=!1,V=!1,X="",oe="";function re(F){n(11,X=F),q=!0,vn(F)}function me(F){n(12,oe=F),n(29,V=!0),bn(F)}const $e={noRemoteBranches:"No remote branches found. Remote agents require remote branches to work properly. Please push your current branch to remote with 'git push -u origin <branch>'.",failedToFetchBranches:"Failed to fetch branches. Please try again.",failedToParseRemoteUrl:"Failed to parse remote URL in your local git repo. Please check your remote URL and try again.",failedToFetchFromRemote:"Failed to fetch from remote. Please try again."};async function be(){n(5,x=!0),n(4,P=!0);const{repos:F,error:G,isDevDeploy:W}=await i.listUserRepos();if(W)return await async function(){console.warn("Fetching branches from local git environment.");const{remoteUrl:fe,error:Re}=await i.getRemoteUrl();n(1,h=!0);const Ee=Ct(fe);if(!Ee||Re)return C(Re??$e.failedToParseRemoteUrl),void n(1,h=!1);n(2,m={name:Ee.name,owner:Ee.owner,html_url:fe}),n(6,$=[m]),n(7,v=$);const at=function(Fe){const je=Fe.find(_e=>_e.isCurrentBranch),lt=Fe.find(_e=>_e.isDefault),_n=!!je&&(je==null?void 0:je.name)===(lt==null?void 0:lt.name.replace("origin/",""));return Fe.filter(_e=>(!_n||!_e.isDefault)&&(!_e.isCurrentBranch||!_e.isRemote)&&!!_e.isRemote&&_e.isRemote)}((await i.listBranches()).branches),Ge=at.find(Fe=>Fe.isDefault);n(3,u={name:Ge!=null&&Ge.name?At(Ge.name):at[0].name,commit:{sha:"",url:""},protected:!1}),n(28,_=at.map(Fe=>({name:At(Fe.name),commit:{sha:"",url:""},protected:!1}))),n(8,I=_),N(),t||de(),n(1,h=!1)}(),void n(5,x=!1);if(G)return C(`An error occured while fetching your repos. If this continues, please contact support. Error: ${G}`),n(1,h=!1),void n(5,x=!1);if(n(6,$=F),n(7,v=$),!m&&f){const fe=$.find(Re=>Re.html_url===f);fe&&n(2,m=fe)}const{remoteUrl:Me,error:yn}=await i.getRemoteUrl(),xn=Ct(Me);if(yn)return n(1,h=!1),void n(5,x=!1);const{owner:st,name:ct}=xn||{},it=$.find(fe=>fe.name===ct&&fe.owner===st);if(it&&!m)n(2,m=it);else if(!it&&ct&&st){const fe={name:ct,owner:st,html_url:Me};try{const{repo:Re,error:Ee}=await i.getGithubRepo(fe);Ee?(console.warn("Failed to fetch GitHub repo details:",Ee),n(2,m=$[0])):(n(2,m=Re),n(6,$=[m,...$]))}catch(Re){console.error("Error fetching GitHub repo:",Re),n(2,m=$[0])}}else if(!m)return n(1,h=!1),void n(5,x=!1);n(5,x=!1)}async function H(F){if(!m)return;n(4,P=!0);const G=m;do{if(G!==m){n(4,P=!1),n(28,_=[]);break}const W=await i.listRepoBranches(m,F);if(W.error)return C(`Failed to fetch branches for the repo ${m.owner}/${m.name}. Please make sure you have access to this repo on GitHub. If this continues, please contact support. Error: ${W.error}`),void n(1,h=!1);if(n(28,_=[..._,...W.branches]),n(10,E=W.hasNextPage),Ne(),!E)break;F=W.nextPage,n(9,U++,U),ne()}while(U%20!=0&&E);n(8,I=_),n(4,P=!1)}function Ne(){if(m&&!u){if(g){const F=_.find(G=>G.name===g);if(F)return n(3,u=F),void de()}if(m.default_branch){const F=m.default_branch,G=I.find(W=>W.name===F);if(G)return n(3,u=G),void de()}P||n(3,u=_[0]),de()}}function Ie(){m&&async function(){m&&(n(9,U=0),await H(U+1))}().then(()=>{ne(),n(1,h=!1),N(),t||de()}).catch(F=>{console.error("Error fetching all branches:",F),C(`Failed to fetch branches: ${F instanceof Error?F.message:String(F)}`)})}tt(async()=>{await ye()});let J=!0;const pe=async()=>{try{n(13,J=await i.isGithubAuthenticated()),J||C("Please authenticate with GitHub to use this feature.")}catch(F){console.error("Failed to check GitHub authentication status:",F),C("Please authenticate with GitHub to use this feature."),n(13,J=!1)}};async function ye(){n(1,h=!0);try{await async function(){n(1,h=!0),B();try{if(await pe(),!J)return void n(1,h=!1);await be(),t||Ie(),N(),t||de()}catch(F){console.error("Error fetching git data:",F),C($e.failedToFetchBranches)}finally{n(1,h=!1)}}()}catch(F){console.error("Error fetching and syncing branches:",F),C("Failed to fetch repos and branches. Please try again. If this continues, please contact support.")}finally{n(1,h=!1)}}async function N(){if(!t&&!o)try{if(!o&&m&&!u&&_.length===0)return void C($e.noRemoteBranches);B()}catch(F){console.error("Error checking git repository:",F),C($e.failedToFetchFromRemote)}}function C(F){console.error("Error:",F),n(15,t=!0),n(0,w=F)}function B(){n(15,t=!1),n(0,w="")}async function ne(F=""){n(15,t=!1);try{if(V&&oe.trim()!=="")n(8,(G=F||oe,I=_.filter(W=>W.name.includes(G.toLowerCase()))));else{let W;n(8,I=_.filter(Me=>Me.name!==(m==null?void 0:m.default_branch)||(W=Me,!1))),W?I.unshift(W):m!=null&&m.default_branch&&I.unshift({name:m.default_branch,commit:{sha:"",url:""},protected:!1})}N()}catch(W){console.error("Error fetching branches:",W),n(8,I=[]),C($e.failedToFetchBranches)}var G}async function K(F){n(3,u=F),n(29,V=!1),Be((u==null?void 0:u.name)??""),ne();const G=l.creationMetrics;l.setCreationMetrics({changedRepo:(G==null?void 0:G.changedRepo)??!1,changedBranch:!0}),de()}async function xe(F){n(4,P=!0),n(2,m=F),n(3,u=void 0),n(28,_=[]),n(8,I=[]),q=!1,Te(""),n(7,v=$),Ie();const G=l.creationMetrics;l.setCreationMetrics({changedRepo:!0,changedBranch:(G==null?void 0:G.changedBranch)??!1})}function Ae(F,G){F||(G==="repo"?q=!1:(G==="branch"||(q=!1),n(29,V=!1)))}function de(){if(!(m!=null&&m.html_url)||!u)return;const F={github_commit_ref:{repository_url:m.html_url,git_ref:u.name}};a("commitRefChange",{commitRef:F,selectedBranch:u})}const Be=F=>{n(12,oe=F)},Te=F=>{n(11,X=F)},vn=_t(async function(F=""){n(15,t=!1);try{q?n(7,(G=F||X,v=$.filter(W=>W.name.includes(G.toLowerCase())||W.owner.includes(G.toLowerCase())))):n(7,v=$)}catch(W){console.error("Error fetching repos:",W),n(7,v=[]),C($e.failedToFetchFromRemote)}var G},300,{leading:!1,trailing:!0}),bn=_t(ne,300,{leading:!1,trailing:!0});function bt(F){F&&c||Ae(F,"branch")}function yt(F){F&&!$.length||Ae(F,"repo")}return r.$$set=F=>{"errorMessage"in F&&n(0,w=F.errorMessage),"isLoading"in F&&n(1,h=F.isLoading),"lastUsedBranchName"in F&&n(26,g=F.lastUsedBranchName),"lastUsedRepoUrl"in F&&n(27,f=F.lastUsedRepoUrl)},r.$$.update=()=>{1&r.$$.dirty[0]&&n(15,t=w!==""),50&r.$$.dirty[0]&&(o=h||P||x),28&r.$$.dirty[0]&&n(16,s=m&&P&&!u),536870920&r.$$.dirty[0]&&Be(V?"":(u==null?void 0:u.name)??""),805306372&r.$$.dirty[0]&&n(14,c=!m||!V&&!_.length)},[w,h,m,u,P,x,$,v,I,U,E,X,oe,J,c,t,s,re,me,H,pe,ye,K,xe,bt,yt,g,f,_,V,function(F){X=F,n(11,X)},F=>yt(F.detail),F=>re(F.detail),F=>xe(F.detail),()=>{H(U+1)},function(F){oe=F,n(12,oe)},F=>bt(F.detail),F=>me(F.detail),F=>K(F.detail),async()=>{await pe(),J&&await ye()}]}class xr extends se{constructor(e){super(),ce(this,e,yr,gr,ie,{errorMessage:0,isLoading:1,lastUsedBranchName:26,lastUsedRepoUrl:27},null,[-1,-1,-1])}}function _r(r){let e,n,t=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},r[0]],o={};for(let s=0;s<t.length;s+=1)o=ae(o,t[s]);return{c(){e=We("svg"),n=new Xe(!0),this.h()},l(s){e=Je(s,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var c=Qe(e);n=Ye(c,!0),c.forEach(b),this.h()},h(){n.a=null,ke(e,o)},m(s,c){Ze(s,e,c),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="m36.4 360.9-23 78.1L1 481.2c-2.5 8.5-.2 17.6 6 23.8s15.3 8.5 23.7 6.1L73 498.6l78.1-23c12.4-3.6 23.7-9.9 33.4-18.4 1.4-1.2 2.7-2.5 4-3.8l304.2-304.1c21.9-21.9 24.6-55.6 8.2-80.5-2.3-3.5-5.1-6.9-8.2-10l-39.4-39.5c-25-25-65.5-25-90.5 0L58.6 323.5c-10.4 10.4-18 23.3-22.2 37.4m46 13.5c1.7-5.6 4.5-10.8 8.4-15.2.6-.6 1.1-1.2 1.7-1.8L321 129l62 62-228.4 228.5c-4.7 4.7-10.6 8.2-17 10.1l-23.4 6.9-54.8 16.1 16.1-54.8z"/>',e)},p(s,[c]){ke(e,o=et(t,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&c&&s[0]]))},i:z,o:z,d(s){s&&b(e)}}}function Lr(r,e,n){return r.$$set=t=>{n(0,e=ae(ae({},e),Se(t)))},[e=Se(e)]}class Cr extends se{constructor(e){super(),ce(this,e,Lr,_r,ie,{})}}function Ar(r){let e,n,t=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 576 512"},r[0]],o={};for(let s=0;s<t.length;s+=1)o=ae(o,t[s]);return{c(){e=We("svg"),n=new Xe(!0),this.h()},l(s){e=Je(s,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var c=Qe(e);n=Ye(c,!0),c.forEach(b),this.h()},h(){n.a=null,ke(e,o)},m(s,c){Ze(s,e,c),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M234.7 42.7 197 56.8c-3 1.1-5 4-5 7.2s2 6.1 5 7.2l37.7 14.1 14.1 37.7c1.1 3 4 5 7.2 5s6.1-2 7.2-5l14.1-37.7L315 71.2c3-1.1 5-4 5-7.2s-2-6.1-5-7.2l-37.7-14.1L263.2 5c-1.1-3-4-5-7.2-5s-6.1 2-7.2 5zM461.4 48 496 82.6 386.2 192.3l-34.6-34.6zM80 429.4l237.7-237.7 34.6 34.6L114.6 464zM427.4 14.1 46.1 395.4c-18.7 18.7-18.7 49.1 0 67.9l34.6 34.6c18.7 18.7 49.1 18.7 67.9 0l381.3-381.4c18.7-18.7 18.7-49.1 0-67.9l-34.6-34.5c-18.7-18.7-49.1-18.7-67.9 0M7.5 117.2C3 118.9 0 123.2 0 128s3 9.1 7.5 10.8L64 160l21.2 56.5c1.7 4.5 6 7.5 10.8 7.5s9.1-3 10.8-7.5L128 160l56.5-21.2c4.5-1.7 7.5-6 7.5-10.8s-3-9.1-7.5-10.8L128 96l-21.2-56.5c-1.7-4.5-6-7.5-10.8-7.5s-9.1 3-10.8 7.5L64 96zm352 256c-4.5 1.7-7.5 6-7.5 10.8s3 9.1 7.5 10.8L416 416l21.2 56.5c1.7 4.5 6 7.5 10.8 7.5s9.1-3 10.8-7.5L480 416l56.5-21.2c4.5-1.7 7.5-6 7.5-10.8s-3-9.1-7.5-10.8L480 352l-21.2-56.5c-1.7-4.5-6-7.5-10.8-7.5s-9.1 3-10.8 7.5L416 352z"/>',e)},p(s,[c]){ke(e,o=et(t,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 576 512"},1&c&&s[0]]))},i:z,o:z,d(s){s&&b(e)}}}function Rr(r,e,n){return r.$$set=t=>{n(0,e=ae(ae({},e),Se(t)))},[e=Se(e)]}class kr extends se{constructor(e){super(),ce(this,e,Rr,Ar,ie,{})}}const Sr=r=>({}),Zt=r=>({});function en(r){let e,n;const t=r[12].icon,o=Q(t,r,r[11],Zt);return{c(){e=S("div"),o&&o.c(),y(e,"class","c-setup-script-selector__icon svelte-udt6j8")},m(s,c){L(s,e,c),o&&o.m(e,null),n=!0},p(s,c){o&&o.p&&(!n||2048&c)&&Y(o,t,s,s[11],n?ee(t,s[11],c,Sr):Z(s[11]),Zt)},i(s){n||(p(o,s),n=!0)},o(s){d(o,s),n=!1},d(s){s&&b(e),o&&o.d(s)}}}function Ir(r){let e,n,t,o,s;return{c(){e=S("span"),n=T(r[0]),t=M(),o=S("span"),s=T(r[1]),y(e,"class","c-setup-script-selector__script-name svelte-udt6j8"),y(o,"class","c-setup-script-selector__script-path svelte-udt6j8")},m(c,i){L(c,e,i),D(e,n),L(c,t,i),L(c,o,i),D(o,s)},p(c,i){1&i&&te(n,c[0]),2&i&&te(s,c[1])},i:z,o:z,d(c){c&&(b(e),b(t),b(o))}}}function Fr(r){let e,n,t,o,s,c,i,a,l;function m(g){r[15](g)}function u(g){r[16](g)}let w={size:1,variant:"surface"};r[6]!==void 0&&(w.value=r[6]),r[5]!==void 0&&(w.textInput=r[5]),t=new En({props:w}),he.push(()=>we(t,"value",m)),he.push(()=>we(t,"textInput",u)),t.$on("keydown",r[8]),t.$on("blur",r[9]);let h=r[7]&&function(g){let f;return{c(){f=S("span"),f.textContent=`${g[7]}`,y(f,"class","c-setup-script-selector__extension svelte-udt6j8")},m($,v){L($,f,v)},p:z,d($){$&&b(f)}}}(r);return{c(){e=S("div"),n=S("div"),A(t.$$.fragment),c=M(),h&&h.c(),y(n,"class","c-setup-script-selector__rename-input-container svelte-udt6j8"),y(n,"role","presentation"),y(e,"class","c-setup-script-selector__rename-input svelte-udt6j8"),y(e,"role","presentation")},m(g,f){L(g,e,f),D(e,n),R(t,n,null),D(n,c),h&&h.m(n,null),i=!0,a||(l=[Le(e,"click",Ve(r[13])),Le(e,"mousedown",Ve(r[14]))],a=!0)},p(g,f){const $={};!o&&64&f&&(o=!0,$.value=g[6],ve(()=>o=!1)),!s&&32&f&&(s=!0,$.textInput=g[5],ve(()=>s=!1)),t.$set($),g[7]&&h.p(g,f)},i(g){i||(p(t.$$.fragment,g),i=!0)},o(g){d(t.$$.fragment,g),i=!1},d(g){g&&b(e),k(t),h&&h.d(),a=!1,ft(l)}}}function Nr(r){let e,n,t,o,s,c,i,a,l=r[10].icon&&en(r);const m=[Fr,Ir],u=[];function w(f,$){return f[3]?0:1}o=w(r),s=u[o]=m[o](r);const h=r[12].default,g=Q(h,r,r[11],null);return{c(){e=S("div"),l&&l.c(),n=M(),t=S("div"),s.c(),c=M(),i=S("div"),g&&g.c(),y(t,"class","c-setup-script-selector__script-info svelte-udt6j8"),y(i,"class","c-setup-script-selector__script-actions svelte-udt6j8"),y(e,"class","c-setup-script-selector__script-item-content svelte-udt6j8"),y(e,"role","presentation"),ge(e,"c-setup-script-selector__script-item-content--renaming",r[3]),ge(e,"c-setup-script-selector__script-item-content--is-path",r[2]),ge(e,"c-setup-script-selector__script-item-content--selected",r[4])},m(f,$){L(f,e,$),l&&l.m(e,null),D(e,n),D(e,t),u[o].m(t,null),D(e,c),D(e,i),g&&g.m(i,null),a=!0},p(f,[$]){f[10].icon?l?(l.p(f,$),1024&$&&p(l,1)):(l=en(f),l.c(),p(l,1),l.m(e,n)):l&&(j(),d(l,1,1,()=>{l=null}),O());let v=o;o=w(f),o===v?u[o].p(f,$):(j(),d(u[v],1,1,()=>{u[v]=null}),O(),s=u[o],s?s.p(f,$):(s=u[o]=m[o](f),s.c()),p(s,1),s.m(t,null)),g&&g.p&&(!a||2048&$)&&Y(g,h,f,f[11],a?ee(h,f[11],$,null):Z(f[11]),null),(!a||8&$)&&ge(e,"c-setup-script-selector__script-item-content--renaming",f[3]),(!a||4&$)&&ge(e,"c-setup-script-selector__script-item-content--is-path",f[2]),(!a||16&$)&&ge(e,"c-setup-script-selector__script-item-content--selected",f[4])},i(f){a||(p(l),p(s),p(g,f),a=!0)},o(f){d(l),d(s),d(g,f),a=!1},d(f){f&&b(e),l&&l.d(),u[o].d(),g&&g.d(f)}}}function Er(r,e,n){let{$$slots:t={},$$scope:o}=e;const s=dt(t);let{name:c}=e,{path:i}=e,{isPath:a=!1}=e,{isRenaming:l=!1}=e,{isSelected:m=!1}=e;const u=Pe(),{baseName:w,extension:h}=function($){const v=$.lastIndexOf(".");return v===-1?{baseName:$,extension:""}:{baseName:$.substring(0,v),extension:$.substring(v)}}(c);let g,f=w;return r.$$set=$=>{"name"in $&&n(0,c=$.name),"path"in $&&n(1,i=$.path),"isPath"in $&&n(2,a=$.isPath),"isRenaming"in $&&n(3,l=$.isRenaming),"isSelected"in $&&n(4,m=$.isSelected),"$$scope"in $&&n(11,o=$.$$scope)},r.$$.update=()=>{40&r.$$.dirty&&l&&g&&setTimeout(()=>{g==null||g.focus(),g==null||g.select()},0)},[c,i,a,l,m,g,f,h,function($){if($.key!=="ArrowLeft"&&$.key!=="ArrowRight"&&$.key!=="ArrowUp"&&$.key!=="ArrowDown")if($.key==="Enter")if($.preventDefault(),f.trim()&&f!==w){const v=f.trim()+h;u("rename",{oldName:c,newName:v})}else u("cancelRename");else $.key==="Escape"&&($.preventDefault(),$.stopPropagation(),u("cancelRename"));else $.stopPropagation()},function(){u("cancelRename")},s,o,t,function($){Oe.call(this,r,$)},function($){Oe.call(this,r,$)},function($){f=$,n(6,f)},function($){g=$,n(5,g)}]}class Dr extends se{constructor(e){super(),ce(this,e,Er,Nr,ie,{name:0,path:1,isPath:2,isRenaming:3,isSelected:4})}}function tn(r){let e,n,t,o,s,c,i,a,l,m;function u(h){r[34](h)}let w={placeholder:"Search scripts...",isLoading:r[1],disabled:!1,items:r[7],selectedItem:r[2],itemLabelFn:ms,itemKeyFn:$s,isItemSelected:ps,noItemsLabel:"No scripts found",loadingLabel:"Loading scripts...",$$slots:{item:[Yr,({item:h})=>({45:h}),({item:h})=>[0,h?16384:0]],searchIcon:[jr],icon:[Gr],title:[Tr]},$$scope:{ctx:r}};return r[3]!==void 0&&(w.searchValue=r[3]),t=new pt({props:w}),he.push(()=>we(t,"searchValue",u)),t.$on("openChange",r[35]),t.$on("search",r[36]),t.$on("select",r[37]),i=new De({props:{content:r[10],$$slots:{default:[ns]},$$scope:{ctx:r}}}),l=new De({props:{content:"Open a new file for you to write a setup script that you can edit directly.",$$slots:{default:[cs]},$$scope:{ctx:r}}}),{c(){e=S("div"),n=S("div"),A(t.$$.fragment),s=M(),c=S("div"),A(i.$$.fragment),a=M(),A(l.$$.fragment),y(c,"class","c-setup-script-selector__action-buttons svelte-3cd2r2"),y(n,"class","c-setup-script-selector__script-line svelte-3cd2r2"),y(e,"class","c-setup-script-selector__script-line-container svelte-3cd2r2")},m(h,g){L(h,e,g),D(e,n),R(t,n,null),D(n,s),D(n,c),R(i,c,null),D(c,a),R(l,c,null),m=!0},p(h,g){const f={};2&g[0]&&(f.isLoading=h[1]),128&g[0]&&(f.items=h[7]),4&g[0]&&(f.selectedItem=h[2]),884&g[0]|49152&g[1]&&(f.$$scope={dirty:g,ctx:h}),!o&&8&g[0]&&(o=!0,f.searchValue=h[3],ve(()=>o=!1)),t.$set(f);const $={};1024&g[0]&&($.content=h[10]),2048&g[0]|32768&g[1]&&($.$$scope={dirty:g,ctx:h}),i.$set($);const v={};32768&g[1]&&(v.$$scope={dirty:g,ctx:h}),l.$set(v)},i(h){m||(p(t.$$.fragment,h),p(i.$$.fragment,h),p(l.$$.fragment,h),m=!0)},o(h){d(t.$$.fragment,h),d(i.$$.fragment,h),d(l.$$.fragment,h),m=!1},d(h){h&&b(e),k(t),k(i),k(l)}}}function Pr(r){let e,n;return e=new gn({props:{$$slots:{text:[Br]},$$scope:{ctx:r}}}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p(t,o){const s={};16&o[0]|32768&o[1]&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function Ur(r){let e,n;return e=new gn({props:{$$slots:{grayText:[zr],text:[Mr]},$$scope:{ctx:r}}}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p(t,o){const s={};768&o[0]|32768&o[1]&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function Br(r){let e,n;return{c(){e=S("span"),n=T(r[4]),y(e,"slot","text")},m(t,o){L(t,e,o),D(e,n)},p(t,o){16&o[0]&&te(n,t[4])},d(t){t&&b(e)}}}function Mr(r){let e,n;return{c(){e=S("span"),n=T(r[9]),y(e,"slot","text")},m(t,o){L(t,e,o),D(e,n)},p(t,o){512&o[0]&&te(n,t[9])},d(t){t&&b(e)}}}function zr(r){let e,n;return{c(){e=S("span"),n=T(r[8]),y(e,"slot","grayText")},m(t,o){L(t,e,o),D(e,n)},p(t,o){256&o[0]&&te(n,t[8])},d(t){t&&b(e)}}}function Tr(r){let e,n,t,o;const s=[Ur,Pr],c=[];function i(a,l){return a[5]?0:1}return n=i(r),t=c[n]=s[n](r),{c(){e=S("div"),t.c(),y(e,"slot","title")},m(a,l){L(a,e,l),c[n].m(e,null),o=!0},p(a,l){let m=n;n=i(a),n===m?c[n].p(a,l):(j(),d(c[m],1,1,()=>{c[m]=null}),O(),t=c[n],t?t.p(a,l):(t=c[n]=s[n](a),t.c()),p(t,1),t.m(e,null))},i(a){o||(p(t),o=!0)},o(a){d(t),o=!1},d(a){a&&b(e),c[n].d()}}}function Gr(r){let e,n;return e=new fn({props:{slot:"icon"}}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p:z,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function jr(r){let e,n;return e=new ht({props:{slot:"searchIcon"}}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p:z,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function Or(r){var t;let e,n;return e=new Dr({props:{name:r[45].name,path:r[45].path,isPath:!0,isRenaming:((t=r[6])==null?void 0:t.path)===r[45].path,isSelected:!(!r[2]||r[2].path!==r[45].path),$$slots:{default:[Qr]},$$scope:{ctx:r}}}),e.$on("rename",function(...o){return r[33](r[45],...o)}),e.$on("cancelRename",r[21]),{c(){A(e.$$.fragment)},m(o,s){R(e,o,s),n=!0},p(o,s){var i;r=o;const c={};16384&s[1]&&(c.name=r[45].name),16384&s[1]&&(c.path=r[45].path),64&s[0]|16384&s[1]&&(c.isRenaming=((i=r[6])==null?void 0:i.path)===r[45].path),4&s[0]|16384&s[1]&&(c.isSelected=!(!r[2]||r[2].path!==r[45].path)),49152&s[1]&&(c.$$scope={dirty:s,ctx:r}),e.$set(c)},i(o){n||(p(e.$$.fragment,o),n=!0)},o(o){d(e.$$.fragment,o),n=!1},d(o){k(e,o)}}}function Vr(r){let e,n,t,o;return n=new fn({}),{c(){e=S("div"),A(n.$$.fragment),t=T(`
                  Use basic environment`),y(e,"class","c-setup-script-selector__basic-option svelte-3cd2r2")},m(s,c){L(s,e,c),R(n,e,null),D(e,t),o=!0},p:z,i(s){o||(p(n.$$.fragment,s),o=!0)},o(s){d(n.$$.fragment,s),o=!1},d(s){s&&b(e),k(n)}}}function Hr(r){let e,n;return e=new Dn({}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function qr(r){let e,n;return e=new vt({props:{size:1,variant:"ghost-block",class:"c-setup-script-selector__action-button",$$slots:{default:[Hr]},$$scope:{ctx:r}}}),e.$on("click",function(...t){return r[30](r[45],...t)}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p(t,o){r=t;const s={};32768&o[1]&&(s.$$scope={dirty:o,ctx:r}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function Kr(r){let e,n;return e=new Cr({}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function Wr(r){let e,n;return e=new vt({props:{size:1,variant:"ghost-block",class:"c-setup-script-selector__action-button",$$slots:{default:[Kr]},$$scope:{ctx:r}}}),e.$on("click",function(...t){return r[31](r[45],...t)}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p(t,o){r=t;const s={};32768&o[1]&&(s.$$scope={dirty:o,ctx:r}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function Xr(r){let e,n;return e=new Fn({}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function Jr(r){let e,n;return e=new vt({props:{size:1,variant:"ghost-block",class:"c-setup-script-selector__action-button",$$slots:{default:[Xr]},$$scope:{ctx:r}}}),e.$on("click",function(...t){return r[32](r[45],...t)}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p(t,o){r=t;const s={};32768&o[1]&&(s.$$scope={dirty:o,ctx:r}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function Qr(r){let e,n,t,o,s,c;return e=new De({props:{content:"Open script in editor",$$slots:{default:[qr]},$$scope:{ctx:r}}}),t=new De({props:{content:"Rename script",$$slots:{default:[Wr]},$$scope:{ctx:r}}}),s=new De({props:{content:"Delete script",$$slots:{default:[Jr]},$$scope:{ctx:r}}}),{c(){A(e.$$.fragment),n=M(),A(t.$$.fragment),o=M(),A(s.$$.fragment)},m(i,a){R(e,i,a),L(i,n,a),R(t,i,a),L(i,o,a),R(s,i,a),c=!0},p(i,a){const l={};49152&a[1]&&(l.$$scope={dirty:a,ctx:i}),e.$set(l);const m={};49152&a[1]&&(m.$$scope={dirty:a,ctx:i}),t.$set(m);const u={};49152&a[1]&&(u.$$scope={dirty:a,ctx:i}),s.$set(u)},i(i){c||(p(e.$$.fragment,i),p(t.$$.fragment,i),p(s.$$.fragment,i),c=!0)},o(i){d(e.$$.fragment,i),d(t.$$.fragment,i),d(s.$$.fragment,i),c=!1},d(i){i&&(b(n),b(o)),k(e,i),k(t,i),k(s,i)}}}function Yr(r){let e,n,t,o;const s=[Vr,Or],c=[];function i(a,l){return a[45]===null?0:1}return e=i(r),n=c[e]=s[e](r),{c(){n.c(),t=ue()},m(a,l){c[e].m(a,l),L(a,t,l),o=!0},p(a,l){let m=e;e=i(a),e===m?c[e].p(a,l):(j(),d(c[m],1,1,()=>{c[m]=null}),O(),n=c[e],n?n.p(a,l):(n=c[e]=s[e](a),n.c()),p(n,1),n.m(t.parentNode,t))},i(a){o||(p(n),o=!0)},o(a){d(n),o=!1},d(a){a&&b(t),c[e].d(a)}}}function Zr(r){let e,n;return{c(){e=T("Auto-generate"),n=S("span"),n.textContent="a script",y(n,"class","c-setup-script-selector__long-text svelte-3cd2r2")},m(t,o){L(t,e,o),L(t,n,o)},p:z,d(t){t&&(b(e),b(n))}}}function es(r){let e,n;return e=new kr({props:{slot:"iconLeft"}}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p:z,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function ts(r){let e,n;return e=new un({props:{slot:"iconRight"}}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p:z,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function ns(r){let e,n;return e=new Ue({props:{variant:"soft",color:"neutral",size:1,disabled:r[11],$$slots:{iconRight:[ts],iconLeft:[es],default:[Zr]},$$scope:{ctx:r}}}),e.$on("click",r[14]),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p(t,o){const s={};2048&o[0]&&(s.disabled=t[11]),32768&o[1]&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function os(r){let e,n,t;return{c(){e=T("Write "),n=S("span"),n.textContent="a script",t=T("by hand"),y(n,"class","c-setup-script-selector__long-text svelte-3cd2r2")},m(o,s){L(o,e,s),L(o,n,s),L(o,t,s)},p:z,d(o){o&&(b(e),b(n),b(t))}}}function rs(r){let e,n;return e=new In({props:{slot:"iconLeft"}}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p:z,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function ss(r){let e,n;return e=new un({props:{slot:"iconRight"}}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p:z,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function cs(r){let e,n;return e=new Ue({props:{variant:"soft",color:"neutral",size:1,highlight:!1,$$slots:{iconRight:[ss],iconLeft:[rs],default:[os]},$$scope:{ctx:r}}}),e.$on("click",r[15]),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p(t,o){const s={};32768&o[1]&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function nn(r){let e,n,t;return n=new rt({props:{color:"warning",variant:"soft",size:2,$$slots:{default:[ls]},$$scope:{ctx:r}}}),{c(){e=S("div"),A(n.$$.fragment),y(e,"class","c-setup-script-selector__error svelte-3cd2r2")},m(o,s){L(o,e,s),R(n,e,null),t=!0},p(o,s){const c={};3&s[0]|32768&s[1]&&(c.$$scope={dirty:s,ctx:o}),n.$set(c)},i(o){t||(p(n.$$.fragment,o),t=!0)},o(o){d(n.$$.fragment,o),t=!1},d(o){o&&b(e),k(n)}}}function is(r){let e;return{c(){e=T("Refresh")},m(n,t){L(n,e,t)},d(n){n&&b(e)}}}function as(r){let e,n,t;return n=new mn({}),{c(){e=S("span"),A(n.$$.fragment),y(e,"slot","iconLeft")},m(o,s){L(o,e,s),R(n,e,null),t=!0},p:z,i(o){t||(p(n.$$.fragment,o),t=!0)},o(o){d(n.$$.fragment,o),t=!1},d(o){o&&b(e),k(n)}}}function ls(r){let e,n,t,o,s,c;return s=new Ue({props:{variant:"ghost",color:"warning",size:1,loading:r[1],$$slots:{iconLeft:[as],default:[is]},$$scope:{ctx:r}}}),s.$on("click",r[17]),{c(){e=S("div"),n=S("div"),t=T(r[0]),o=M(),A(s.$$.fragment),y(n,"class","c-setup-script-selector__error-message svelte-3cd2r2"),y(e,"class","c-setup-script-selector__error-content svelte-3cd2r2")},m(i,a){L(i,e,a),D(e,n),D(n,t),D(e,o),R(s,e,null),c=!0},p(i,a){(!c||1&a[0])&&te(t,i[0]);const l={};2&a[0]&&(l.loading=i[1]),32768&a[1]&&(l.$$scope={dirty:a,ctx:i}),s.$set(l)},i(i){c||(p(s.$$.fragment,i),c=!0)},o(i){d(s.$$.fragment,i),c=!1},d(i){i&&b(e),k(s)}}}function us(r){let e,n,t,o,s=(!r[12]||r[0]===r[16].noScriptsFound)&&tn(r),c=r[12]&&r[0]!==r[16].noScriptsFound&&nn(r);return{c(){e=S("div"),n=S("div"),s&&s.c(),t=M(),c&&c.c(),y(n,"class","c-setup-script-selector__content svelte-3cd2r2"),y(e,"class","c-setup-script-selector svelte-3cd2r2")},m(i,a){L(i,e,a),D(e,n),s&&s.m(n,null),D(n,t),c&&c.m(n,null),o=!0},p(i,a){i[12]&&i[0]!==i[16].noScriptsFound?s&&(j(),d(s,1,1,()=>{s=null}),O()):s?(s.p(i,a),4097&a[0]&&p(s,1)):(s=tn(i),s.c(),p(s,1),s.m(n,t)),i[12]&&i[0]!==i[16].noScriptsFound?c?(c.p(i,a),4097&a[0]&&p(c,1)):(c=nn(i),c.c(),p(c,1),c.m(n,null)):c&&(j(),d(c,1,1,()=>{c=null}),O())},i(i){o||(p(s),p(c),o=!0)},o(i){d(s),d(c),o=!1},d(i){i&&b(e),s&&s.d(),c&&c.d()}}}const ms=r=>(r==null?void 0:r.name)||"",$s=r=>`${r==null?void 0:r.path}-${r==null?void 0:r.location}-${r==null?void 0:r.name}`,ps=(r,e)=>r===null&&e===null||!(!r||!e)&&r.path===e.path;function ds(r,e,n){var ye;let t,o,s,c,i,a,l,m,{errorMessage:u=""}=e,{isLoading:w=!1}=e,{lastUsedScriptPath:h=null}=e,{disableNewAgentCreation:g=!1}=e;const f=Ce(ot.key),$=Pe(),v=Ce("chatModel").extensionClient,_=N=>{v.openFile({repoRoot:"",pathName:N.path,allowOutOfWorkspace:!0,openLocalUri:N.location==="home"})};let I=[],P=((ye=f.newAgentDraft)==null?void 0:ye.setupScript)??null,x="",U=null,E=I,q=!0;const V={noScriptsFound:"No setup scripts found. You can create one in ~/.augment/env/, <git root>/.augment/env/, or <workspace root>/.augment/env/.",failedToFetchScripts:"Failed to fetch setup scripts. Please try again."};async function X(){n(0,u="");try{const N=P==null?void 0:P.path;if(n(28,I=await f.listSetupScripts()),q)if(h&&I.length>0){const C=I.find(B=>B.path===h);C&&(n(2,P=C),pe())}else h===null&&(n(2,P=null),pe());else if(N){const C=I.find(B=>B.path===N);C&&n(2,P=C)}q=!1,I.length===0?n(0,u=V.noScriptsFound):n(0,u="")}catch(N){console.error("Error fetching setup scripts:",N),n(0,u=V.failedToFetchScripts)}}async function oe(N,C){C&&C.stopPropagation();try{const B=await f.deleteSetupScript(N.name,N.location);B.success?((P==null?void 0:P.path)===N.path&&J(null),await X()):(console.error("Failed to delete script:",B.error),be(`Failed to delete script: ${B.error||"Unknown error"}`))}catch(B){console.error("Error deleting script:",B),be(`Error deleting script: ${B instanceof Error?B.message:String(B)}`)}}async function re(N,C){C&&C.stopPropagation(),n(6,U=N)}async function me(N,C){const{oldName:B,newName:ne}=C.detail;try{const K=await f.renameSetupScript(B,ne,N.location);if(K.success){await X();const xe=I.find(Ae=>Ae.path===K.path);xe&&J(xe)}else console.error("Failed to rename script:",K.error),be(`Failed to rename script: ${K.error||"Unknown error"}`)}catch(K){console.error("Error renaming script:",K),be(`Error renaming script: ${K instanceof Error?K.message:String(K)}`)}finally{$e()}}function $e(){n(6,U=null)}function be(N){n(0,u=N)}function H(N){n(3,x=N)}function Ne(N){J(N)}function Ie(N){N&&(X(),n(3,x=""))}async function J(N){n(2,P=N),pe(),f.saveLastRemoteAgentSetup(null,null,(P==null?void 0:P.path)||null)}function pe(){$("setupScriptChange",{script:P})}return tt(async()=>{var N;await X(),h===null?J(null):(N=f.newAgentDraft)!=null&&N.setupScript&&!P&&J(f.newAgentDraft.setupScript)}),r.$$set=N=>{"errorMessage"in N&&n(0,u=N.errorMessage),"isLoading"in N&&n(1,w=N.isLoading),"lastUsedScriptPath"in N&&n(26,h=N.lastUsedScriptPath),"disableNewAgentCreation"in N&&n(27,g=N.disableNewAgentCreation)},r.$$.update=()=>{var N,C;if(1&r.$$.dirty[0]&&n(12,t=u!==""),134217728&r.$$.dirty[0]&&n(11,o=g||((N=f.newAgentDraft)==null?void 0:N.isDisabled)||!f.newAgentDraft),134217728&r.$$.dirty[0]&&n(10,s=f.newAgentDraft?(C=f.newAgentDraft)!=null&&C.isDisabled?"Please resolve the issues with your workspace selection":g?"Agent limit reached or other restrictions apply":"An AI agent will automatically generate a setup script for your project.":"Please select a repository and branch first"),268435464&r.$$.dirty[0])if(x.trim()!==""){const B="Use basic environment".toLowerCase().includes(x.toLowerCase()),ne=I.filter(K=>K.name.toLowerCase().includes(x.toLowerCase())||K.path.toLowerCase().includes(x.toLowerCase()));n(7,E=B?[null,...ne]:ne)}else n(7,E=[null,...I]);6&r.$$.dirty[0]&&n(29,c=()=>w?"...":P?P.isGenerateOption?P.name:P.location==="home"?"~/.augment/env/"+P.name:P.path:"Use basic environment"),536870912&r.$$.dirty[0]&&n(4,i=c()),4&r.$$.dirty[0]&&n(5,a=!!(P!=null&&P.path)),48&r.$$.dirty[0]&&n(9,l=a?i.split("/").pop():i),48&r.$$.dirty[0]&&n(8,m=a?i.slice(0,i.lastIndexOf("/")):"")},[u,w,P,x,i,a,U,E,m,l,s,o,t,_,async()=>{try{const N=f.newAgentDraft;N&&f.setNewAgentDraft({...N,isSetupScriptAgent:!0});const C=await f.createRemoteAgentFromDraft("SETUP_MODE");return C&&f.setCurrentAgent(C),C}catch(N){console.error("Failed to select setup script generation:",N)}},async()=>{try{const N="setup.sh",C=`#!/bin/bash

# Setup Script for Remote Agent Environment
#
# This script installs dependencies and configures the environment for your project.
# It runs with sudo privileges when needed.
#
# Examples:
# sudo apt-get update && sudo apt-get install -y package-name
# pip install package-name
# npm install -g package-name
# export ENV_VAR=value

# Add your commands below:

`,B=await f.saveSetupScript(N,C,"home");if(B.success&&B.path){await X();const ne=I.find(K=>K.path===B.path);ne&&(J(ne),_(ne))}else console.error("Failed to create manual setup script:",B.error),n(0,u=`Failed to create manual setup script: ${B.error||"Unknown error"}`)}catch(N){console.error("Error creating manual setup script:",N),n(0,u=`Error creating manual setup script: ${N instanceof Error?N.message:String(N)}`)}},V,X,oe,re,me,$e,H,Ne,Ie,J,h,g,I,c,(N,C)=>{C.stopPropagation(),_(N),J(N)},(N,C)=>{C.stopPropagation(),re(N)},(N,C)=>{C.stopPropagation(),oe(N)},(N,C)=>me(N,C),function(N){x=N,n(3,x)},N=>Ie(N.detail),N=>H(N.detail),N=>Ne(N.detail)]}class fs extends se{constructor(e){super(),ce(this,e,ds,us,ie,{errorMessage:0,isLoading:1,lastUsedScriptPath:26,disableNewAgentCreation:27},null,[-1,-1])}}function gs(r){let e,n,t=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 320 512"},r[0]],o={};for(let s=0;s<t.length;s+=1)o=ae(o,t[s]);return{c(){e=We("svg"),n=new Xe(!0),this.h()},l(s){e=Je(s,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var c=Qe(e);n=Ye(c,!0),c.forEach(b),this.h()},h(){n.a=null,ke(e,o)},m(s,c){Ze(s,e,c),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M48 112v288h48V112zm-48 0c0-26.5 21.5-48 48-48h48c26.5 0 48 21.5 48 48v288c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48zm224 0v288h48V112zm-48 0c0-26.5 21.5-48 48-48h48c26.5 0 48 21.5 48 48v288c0 26.5-21.5 48-48 48h-48c-26.5 0-48-21.5-48-48z"/>',e)},p(s,[c]){ke(e,o=et(t,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 320 512"},1&c&&s[0]]))},i:z,o:z,d(s){s&&b(e)}}}function hs(r,e,n){return r.$$set=t=>{n(0,e=ae(ae({},e),Se(t)))},[e=Se(e)]}class ws extends se{constructor(e){super(),ce(this,e,hs,gs,ie,{})}}function on(r){let e,n;return e=new rt({props:{color:"info",variant:"soft",size:2,$$slots:{icon:[Ls],default:[_s]},$$scope:{ctx:r}}}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p(t,o){const s={};16414&o&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function vs(r){let e;return{c(){e=T(r[4])},m(n,t){L(n,e,t)},p(n,t){16&t&&te(e,n[4])},d(n){n&&b(e)}}}function bs(r){let e,n;return e=new Un({}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function ys(r){let e,n;return e=new ws({}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function xs(r){let e,n,t,o;const s=[ys,bs],c=[];function i(a,l){return a[1]?0:1}return n=i(r),t=c[n]=s[n](r),{c(){e=S("div"),t.c(),y(e,"slot","iconLeft")},m(a,l){L(a,e,l),c[n].m(e,null),o=!0},p(a,l){let m=n;n=i(a),n!==m&&(j(),d(c[m],1,1,()=>{c[m]=null}),O(),t=c[n],t||(t=c[n]=s[n](a),t.c()),p(t,1),t.m(e,null))},i(a){o||(p(t),o=!0)},o(a){d(t),o=!1},d(a){a&&b(e),c[n].d()}}}function _s(r){let e,n,t,o,s,c,i=(r[2]?mt:$t).replace("%MAX_AGENTS%",(r[2]?r[3].maxRemoteAgents:r[3].maxActiveRemoteAgents).toString())+"";return s=new Ue({props:{variant:"soft",color:"neutral",size:1,$$slots:{iconLeft:[xs],default:[vs]},$$scope:{ctx:r}}}),s.$on("click",r[11]),{c(){e=S("div"),n=S("p"),t=T(i),o=M(),A(s.$$.fragment),y(n,"class","svelte-f3wuoa"),y(e,"class","agent-limit-message svelte-f3wuoa")},m(a,l){L(a,e,l),D(e,n),D(n,t),D(e,o),R(s,e,null),c=!0},p(a,l){(!c||12&l)&&i!==(i=(a[2]?mt:$t).replace("%MAX_AGENTS%",(a[2]?a[3].maxRemoteAgents:a[3].maxActiveRemoteAgents).toString())+"")&&te(t,i);const m={};16402&l&&(m.$$scope={dirty:l,ctx:a}),s.$set(m)},i(a){c||(p(s.$$.fragment,a),c=!0)},o(a){d(s.$$.fragment,a),c=!1},d(a){a&&b(e),k(s)}}}function Ls(r){let e,n;return e=new Pn({props:{slot:"icon"}}),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p:z,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function Cs(r){let e,n,t=!!r[0]&&on(r);return{c(){t&&t.c(),e=ue()},m(o,s){t&&t.m(o,s),L(o,e,s),n=!0},p(o,[s]){o[0]?t?(t.p(o,s),1&s&&p(t,1)):(t=on(o),t.c(),p(t,1),t.m(e.parentNode,e)):t&&(j(),d(t,1,1,()=>{t=null}),O())},i(o){n||(p(t),n=!0)},o(o){d(t),n=!1},d(o){o&&b(e),t&&t.d(o)}}}function rn(r){if(!r)return;const e=r.is_setup_script_agent?"Setup script generation":r.session_summary||"";return{id:r.remote_agent_id,title:e.length>30?e.substring(0,27)+"...":e}}function sn(r,e){return r.replace("%MAX_AGENTS%",e.toString())}function As(r,e,n){let t,o,s,{agentLimitErrorMessage:c}=e;const i=Ce(ot.key);ut(r,i,$=>n(3,s=$));let a,l,m,u=!1,w=[];function h(){return s.agentOverviews.sort(($,v)=>new Date($.started_at).getTime()-new Date(v.started_at).getTime())}async function g(){if(!u&&(a!=null&&a.id))try{u=!0,await i.deleteAgent(a.id)}catch($){console.error("Failed to delete oldest agent:",$)}finally{u=!1}}async function f(){if(!u&&(l!=null&&l.id))try{u=!0,await i.pauseRemoteAgentWorkspace(l.id)}catch($){console.error("Failed to pause oldest active agent:",$)}finally{u=!1}}return r.$$set=$=>{"agentLimitErrorMessage"in $&&n(0,c=$.agentLimitErrorMessage)},r.$$.update=()=>{if(8&r.$$.dirty&&n(2,t=!!s.maxRemoteAgents&&s.agentOverviews.length>=s.maxRemoteAgents),8&r.$$.dirty&&n(1,o=!!s.maxActiveRemoteAgents&&s.agentOverviews.filter($=>$.workspace_status===Lt.workspaceRunning).length>=s.maxActiveRemoteAgents),1806&r.$$.dirty)if(t)n(10,w=h()),n(8,a=rn(w[0])),n(0,c=sn(mt,s.maxRemoteAgents)),n(4,m="Delete Oldest Agent"+(a?`: ${a.title}`:""));else if(o){n(10,w=h());const $=w.filter(v=>v.workspace_status===Lt.workspaceRunning);n(9,l=rn($[0])),n(0,c=sn($t,s.maxActiveRemoteAgents)),n(4,m="Pause Oldest Agent"+(l?`: ${l.title}`:""))}else n(8,a=void 0),n(0,c=void 0)},[c,o,t,s,m,i,g,f,a,l,w,()=>{o?f():g()}]}class Rs extends se{constructor(e){super(),ce(this,e,As,Cs,ie,{agentLimitErrorMessage:0})}}function cn(r){let e,n,t,o;return n=new rt({props:{color:"error",variant:"soft",size:2,$$slots:{default:[ks]},$$scope:{ctx:r}}}),{c(){e=S("div"),A(n.$$.fragment),y(e,"class","error-message svelte-1klrgvd")},m(s,c){L(s,e,c),R(n,e,null),o=!0},p(s,c){const i={};16777280&c&&(i.$$scope={dirty:c,ctx:s}),n.$set(i)},i(s){o||(p(n.$$.fragment,s),s&&ln(()=>{o&&(t||(t=He(e,qe,{y:10},!0)),t.run(1))}),o=!0)},o(s){d(n.$$.fragment,s),s&&(t||(t=He(e,qe,{y:10},!1)),t.run(0)),o=!1},d(s){s&&b(e),k(n),s&&t&&t.end()}}}function ks(r){let e,n=r[6].remoteAgentCreationError+"";return{c(){e=T(n)},m(t,o){L(t,e,o)},p(t,o){64&o&&n!==(n=t[6].remoteAgentCreationError+"")&&te(e,n)},d(t){t&&b(e)}}}function Ss(r){let e;return{c(){e=T("Create agent")},m(n,t){L(n,e,t)},d(n){n&&b(e)}}}function Is(r){let e,n;return e=new Ue({props:{variant:"solid",color:"accent",size:2,disabled:r[10],$$slots:{default:[Ss]},$$scope:{ctx:r}}}),e.$on("click",r[15]),{c(){A(e.$$.fragment)},m(t,o){R(e,t,o),n=!0},p(t,o){const s={};1024&o&&(s.disabled=t[10]),16777216&o&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function Fs(r){var pe,ye,N;let e,n,t,o,s,c,i,a,l,m,u,w,h,g,f,$,v,_,I,P,x,U,E,q,V,X,oe,re,me;function $e(C){r[17](C)}let be={};r[2]!==void 0&&(be.agentLimitErrorMessage=r[2]),i=new Rs({props:be}),he.push(()=>we(i,"agentLimitErrorMessage",$e));let H=r[6].remoteAgentCreationError&&cn(r);function Ne(C){r[18](C)}function Ie(C){r[19](C)}let J={lastUsedRepoUrl:r[7],lastUsedBranchName:r[8]};return r[0]!==void 0&&(J.errorMessage=r[0]),r[1]!==void 0&&(J.isLoading=r[1]),f=new xr({props:J}),he.push(()=>we(f,"errorMessage",Ne)),he.push(()=>we(f,"isLoading",Ie)),f.$on("commitRefChange",r[13]),U=new fs({props:{lastUsedScriptPath:r[9],disableNewAgentCreation:!!r[2]||!((pe=r[3])!=null&&pe.name)||!((N=(ye=r[4])==null?void 0:ye.github_commit_ref)!=null&&N.repository_url)}}),U.$on("setupScriptChange",r[14]),V=new An({props:{editable:!0,hasSendButton:!1}}),re=new De({props:{class:"full-width-button",content:r[5],triggerOn:[kn.Hover],$$slots:{default:[Is]},$$scope:{ctx:r}}}),{c(){e=S("div"),n=S("div"),t=S("div"),t.innerHTML=`<p>Kick off a remote agent to work <strong class="svelte-1klrgvd">in parallel</strong>, in an
        <strong class="svelte-1klrgvd">isolated environment</strong>
        that will keep running, <strong class="svelte-1klrgvd">even when you shut off your laptop</strong>.</p>`,o=M(),s=S("div"),c=S("div"),A(i.$$.fragment),m=M(),H&&H.c(),u=M(),w=S("div"),w.textContent="Start from any GitHub repo and branch:",h=M(),g=S("div"),A(f.$$.fragment),_=M(),I=S("div"),I.textContent=`Select a setup script to prepare the remote environment, so the agent can make better
        changes by running scripts, tests, and building your code:`,P=M(),x=S("div"),A(U.$$.fragment),E=M(),q=S("div"),A(V.$$.fragment),X=M(),oe=S("div"),A(re.$$.fragment),y(t,"class","main-description svelte-1klrgvd"),y(c,"class","error-message svelte-1klrgvd"),y(w,"class","description svelte-1klrgvd"),y(g,"class","commit-ref-selector svelte-1klrgvd"),y(I,"class","description svelte-1klrgvd"),y(x,"class","setup-script svelte-1klrgvd"),y(q,"class","chat svelte-1klrgvd"),y(oe,"class","create-button svelte-1klrgvd"),y(s,"class","form-fields"),y(n,"class","content svelte-1klrgvd"),y(e,"class","remote-agent-setup svelte-1klrgvd")},m(C,B){L(C,e,B),D(e,n),D(n,t),D(n,o),D(n,s),D(s,c),R(i,c,null),D(s,m),H&&H.m(s,null),D(s,u),D(s,w),D(s,h),D(s,g),R(f,g,null),D(s,_),D(s,I),D(s,P),D(s,x),R(U,x,null),D(s,E),D(s,q),R(V,q,null),D(s,X),D(s,oe),R(re,oe,null),me=!0},p(C,[B]){var de,Be,Te;const ne={};!a&&4&B&&(a=!0,ne.agentLimitErrorMessage=C[2],ve(()=>a=!1)),i.$set(ne),C[6].remoteAgentCreationError?H?(H.p(C,B),64&B&&p(H,1)):(H=cn(C),H.c(),p(H,1),H.m(s,u)):H&&(j(),d(H,1,1,()=>{H=null}),O());const K={};128&B&&(K.lastUsedRepoUrl=C[7]),256&B&&(K.lastUsedBranchName=C[8]),!$&&1&B&&($=!0,K.errorMessage=C[0],ve(()=>$=!1)),!v&&2&B&&(v=!0,K.isLoading=C[1],ve(()=>v=!1)),f.$set(K);const xe={};512&B&&(xe.lastUsedScriptPath=C[9]),28&B&&(xe.disableNewAgentCreation=!!C[2]||!((de=C[3])!=null&&de.name)||!((Te=(Be=C[4])==null?void 0:Be.github_commit_ref)!=null&&Te.repository_url)),U.$set(xe);const Ae={};32&B&&(Ae.content=C[5]),16778240&B&&(Ae.$$scope={dirty:B,ctx:C}),re.$set(Ae)},i(C){me||(p(i.$$.fragment,C),C&&ln(()=>{me&&(l||(l=He(c,qe,{y:10},!0)),l.run(1))}),p(H),p(f.$$.fragment,C),p(U.$$.fragment,C),p(V.$$.fragment,C),p(re.$$.fragment,C),me=!0)},o(C){d(i.$$.fragment,C),C&&(l||(l=He(c,qe,{y:10},!1)),l.run(0)),d(H),d(f.$$.fragment,C),d(U.$$.fragment,C),d(V.$$.fragment,C),d(re.$$.fragment,C),me=!1},d(C){C&&b(e),k(i),C&&l&&l.end(),H&&H.d(),k(f),k(U),k(V),k(re)}}}function Ns(r,e,n){let t,o,s,c,i,a,l,m;const u=Ce(ot.key);ut(r,u,x=>n(6,m=x));const w=Ce("chatModel");ut(r,w,x=>n(21,l=x));const h=Ce(gt.key);let g,f="",$=!1,v=null,_=null,I=null;tt(async()=>{try{const x=await u.getLastRemoteAgentSetup();n(7,v=x.lastRemoteAgentGitRepoUrl),n(8,_=x.lastRemoteAgentGitBranch),n(9,I=x.lastRemoteAgentSetupScript),await u.reportRemoteAgentEvent({eventName:Sn.setupPageOpened,remoteAgentId:"",eventData:{setupPageOpened:{}}})}catch(x){console.error("Failed to load last remote agent setup:",x)}}),an(()=>{u.setNewAgentDraft(null),u.setCreationMetrics(void 0)});const P=Rn(u,l.currentConversationModel,h);return r.$$.update=()=>{var x,U,E;64&r.$$.dirty&&n(4,t=((x=m.newAgentDraft)==null?void 0:x.commitRef)??null),64&r.$$.dirty&&n(3,o=((U=m.newAgentDraft)==null?void 0:U.selectedBranch)??null),64&r.$$.dirty&&(s=((E=m.newAgentDraft)==null?void 0:E.setupScript)??null),31&r.$$.dirty&&n(5,i=(()=>{var X;const q=(X=t==null?void 0:t.github_commit_ref)==null?void 0:X.repository_url,V=o==null?void 0:o.name;return f||g||($?"Loading repos and branches...":"")||!q&&"Please select a repository"||!V&&"Please select a branch"||(!(!$&&q&&V)&&q&&V?"Loading branch data...":"")||""})()),32&r.$$.dirty&&n(16,a=!!i),65536&r.$$.dirty&&n(10,c=a),65536&r.$$.dirty&&u.newAgentDraft&&!u.isCreatingAgent&&u.newAgentDraft.isDisabled!==a&&u.setNewAgentDraft({...u.newAgentDraft,isDisabled:a})},[f,$,g,o,t,i,m,v,_,I,c,u,w,async function(x){u.setRemoteAgentCreationError(null);const U=u.newAgentDraft;U?u.setNewAgentDraft({...U,commitRef:x.detail.commitRef,selectedBranch:x.detail.selectedBranch}):u.setNewAgentDraft({commitRef:x.detail.commitRef,selectedBranch:x.detail.selectedBranch,setupScript:null,isDisabled:a,enableNotification:!0})},function(x){u.setRemoteAgentCreationError(null);const U=u.newAgentDraft;U?u.setNewAgentDraft({...U,setupScript:x.detail.script}):u.setNewAgentDraft({commitRef:null,selectedBranch:null,setupScript:x.detail.script,isDisabled:a,enableNotification:!0})},async function(){try{P(),u.saveLastRemoteAgentSetup((t==null?void 0:t.github_commit_ref.repository_url)||null,(o==null?void 0:o.name)||null,(s==null?void 0:s.path)||null)}catch(x){console.error("Failed to create agent:",x)}},a,function(x){g=x,n(2,g)},function(x){f=x,n(0,f)},function(x){$=x,n(1,$)}]}class Cc extends se{constructor(e){super(),ce(this,e,Ns,Fs,ie,{})}}export{Cc as default};

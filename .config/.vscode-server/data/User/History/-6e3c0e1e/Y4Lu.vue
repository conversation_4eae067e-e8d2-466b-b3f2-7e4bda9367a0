<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
          <svg class="w-8 h-8 mr-3 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"></path>
          </svg>
          Amministrazione Personnel
        </h1>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Gestione completa del personale e dati contrattuali
        </p>
      </div>

      <!-- Quick Actions -->
      <div class="mt-4 sm:mt-0 flex items-center space-x-3">
        <button @click="showCreateUserModal = true"
                class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
          </svg>
          Nuovo Dipendente
        </button>

        <button @click="exportData"
                :disabled="loading"
                class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
          Esporta Dati
        </button>
      </div>
    </div>

    <!-- Alerts Section -->
    <div v-if="analytics && (analytics.alerts.expiring_contracts.length > 0 || analytics.alerts.ending_probation.length > 0)"
         class="space-y-4">
      <!-- Expiring Contracts Alert -->
      <div v-if="analytics.alerts.expiring_contracts.length > 0"
           class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
        <div class="flex">
          <svg class="w-5 h-5 text-red-400 mr-3 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
          </svg>
          <div class="flex-1">
            <h3 class="text-sm font-medium text-red-800 dark:text-red-200">Contratti in Scadenza</h3>
            <div class="mt-2 text-sm text-red-700 dark:text-red-300">
              <p class="mb-2">{{ analytics.alerts.expiring_contracts.length }} contratti scadranno nei prossimi 90 giorni:</p>
              <ul class="list-disc list-inside space-y-1">
                <li v-for="contract in analytics.alerts.expiring_contracts.slice(0, 3)" :key="contract.user_id">
                  <strong>{{ contract.full_name }}</strong> - {{ formatDate(contract.contract_end_date) }}
                  ({{ contract.days_remaining }} giorni)
                </li>
              </ul>
              <p v-if="analytics.alerts.expiring_contracts.length > 3" class="mt-2 text-xs">
                +{{ analytics.alerts.expiring_contracts.length - 3 }} altri contratti
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Ending Probation Alert -->
      <div v-if="analytics.alerts.ending_probation.length > 0"
           class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
        <div class="flex">
          <svg class="w-5 h-5 text-yellow-400 mr-3 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
          </svg>
          <div class="flex-1">
            <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">Periodi di Prova in Scadenza</h3>
            <div class="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
              <p class="mb-2">{{ analytics.alerts.ending_probation.length }} periodi di prova termineranno nei prossimi 30 giorni:</p>
              <ul class="list-disc list-inside space-y-1">
                <li v-for="probation in analytics.alerts.ending_probation.slice(0, 3)" :key="probation.user_id">
                  <strong>{{ probation.full_name }}</strong> - {{ formatDate(probation.probation_end_date) }}
                  ({{ probation.days_remaining }} giorni)
                </li>
              </ul>
              <p v-if="analytics.alerts.ending_probation.length > 3" class="mt-2 text-xs">
                +{{ analytics.alerts.ending_probation.length - 3 }} altri periodi di prova
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Tab Navigation -->
    <div class="border-b border-gray-200 dark:border-gray-700">
      <nav class="-mb-px flex space-x-8">
        <button v-for="tab in tabs"
                :key="tab.id"
                @click="activeTab = tab.id"
                :class="[
                  'py-2 px-1 border-b-2 font-medium text-sm flex items-center',
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
                ]">
          <svg v-if="tab.id === 'users'" class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"></path>
          </svg>
          <svg v-else-if="tab.id === 'departments'" class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z" clip-rule="evenodd"></path>
          </svg>
          <svg v-else-if="tab.id === 'skills'" class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
          </svg>
          <svg v-else-if="tab.id === 'analytics'" class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"></path>
          </svg>
          <svg v-else class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z" clip-rule="evenodd"></path>
          </svg>
          {{ tab.name }}
        </button>
      </nav>
    </div>

    <!-- Tab Content -->
    <div class="mt-6">
      <!-- Users Management Tab -->
      <div v-if="activeTab === 'users'">
        <UsersManagement
          @user-created="loadAnalytics"
          @user-updated="loadAnalytics"
          @user-deleted="loadAnalytics"
        />
      </div>

      <!-- Departments Management Tab -->
      <div v-else-if="activeTab === 'departments'">
        <DepartmentsManagement
          @department-created="loadAnalytics"
          @department-updated="loadAnalytics"
          @department-deleted="loadAnalytics"
        />
      </div>

      <!-- Skills Management Tab -->
      <div v-else-if="activeTab === 'skills'">
        <SkillsManagement
          @skill-created="loadAnalytics"
          @skill-updated="loadAnalytics"
          @skill-deleted="loadAnalytics"
        />
      </div>

      <!-- Analytics Tab -->
      <div v-else-if="activeTab === 'analytics'">
        <AnalyticsDashboard
          :analytics="analytics"
          :loading="loading"
          @refresh="loadAnalytics"
        />
      </div>

      <!-- Bulk Operations Tab -->
      <div v-else-if="activeTab === 'bulk'">
        <BulkOperations
          @operation-completed="loadAnalytics"
        />
      </div>
    </div>

    <!-- Create User Modal -->
    <CreateUserModal
      v-if="showCreateUserModal"
      @close="showCreateUserModal = false"
      @user-created="onUserCreated"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import UsersManagement from './components/admin/UsersManagement.vue'
import DepartmentsManagement from './components/admin/DepartmentsManagement.vue'
import SkillsManagement from './components/admin/SkillsManagement.vue'
import AnalyticsDashboard from './components/admin/AnalyticsDashboard.vue'
import BulkOperations from './components/admin/BulkOperations.vue'
import CreateUserModal from './components/admin/CreateUserModal.vue'

// Reactive state
const loading = ref(false)
const analytics = ref(null)
const activeTab = ref('users')
const showCreateUserModal = ref(false)

// Tab configuration
const tabs = ref([
  { id: 'users', name: 'Gestione Utenti' },
  { id: 'departments', name: 'Dipartimenti' },
  { id: 'skills', name: 'Competenze' },
  { id: 'analytics', name: 'Analytics' },
  { id: 'bulk', name: 'Operazioni Bulk' }
])

// Methods
const loadAnalytics = async () => {
  loading.value = true

  try {
    const response = await fetch('/api/personnel/admin/analytics', {
      credentials: 'include'
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()

    if (data.success) {
      analytics.value = data.data
    } else {
      throw new Error(data.message || 'Errore nel caricamento analytics')
    }
  } catch (err) {
    console.error('Error loading analytics:', err)
    // Could show toast notification here
  } finally {
    loading.value = false
  }
}

const exportData = async () => {
  try {
    const response = await fetch('/api/personnel/export', {
      credentials: 'include'
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    // Create download link
    const blob = await response.blob()
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `personnel-export-${new Date().toISOString().split('T')[0]}.csv`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

  } catch (err) {
    console.error('Error exporting data:', err)
    // Could show toast notification here
  }
}

const onUserCreated = (user) => {
  showCreateUserModal.value = false
  loadAnalytics()
  // Could show success toast here
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('it-IT', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

// Lifecycle
onMounted(() => {
  loadAnalytics()
})
</script>

<style scoped>
/* Custom styles for admin panel */
.tab-content {
  min-height: 400px;
}

/* Responsive tab navigation */
@media (max-width: 768px) {
  .nav {
    flex-wrap: wrap;
  }

  .nav button {
    margin-bottom: 0.5rem;
  }
}
</style>

<template>
  <div class="space-y-6">
    <!-- Overview Cards -->
    <div v-if="analytics" class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <!-- Total Users -->
      <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                  Dipendenti Totali
                </dt>
                <dd class="text-lg font-medium text-gray-900 dark:text-white">
                  {{ analytics.overview.total_users }}
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <!-- Total Departments -->
      <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                  Dipartimenti
                </dt>
                <dd class="text-lg font-medium text-gray-900 dark:text-white">
                  {{ analytics.overview.total_departments }}
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <!-- Recent Hires -->
      <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-6 w-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                  Assunzioni Recenti (90gg)
                </dt>
                <dd class="text-lg font-medium text-gray-900 dark:text-white">
                  {{ analytics.overview.recent_hires }}
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Charts Row -->
    <div v-if="analytics" class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Users by Role Chart -->
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Distribuzione per Ruolo</h3>
        <div class="space-y-3">
          <div v-for="role in analytics.users_by_role" :key="role.role" class="flex items-center justify-between">
            <div class="flex items-center">
              <div :class="[
                'w-3 h-3 rounded-full mr-3',
                role.role === 'admin' ? 'bg-red-500' :
                role.role === 'manager' ? 'bg-blue-500' :
                'bg-gray-500'
              ]"></div>
              <span class="text-sm text-gray-700 dark:text-gray-300">{{ getRoleLabel(role.role) }}</span>
            </div>
            <div class="flex items-center">
              <span class="text-sm font-medium text-gray-900 dark:text-white mr-2">{{ role.count }}</span>
              <div class="w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div :class="[
                  'h-2 rounded-full',
                  role.role === 'admin' ? 'bg-red-500' :
                  role.role === 'manager' ? 'bg-blue-500' :
                  'bg-gray-500'
                ]" :style="{ width: `${(role.count / analytics.overview.total_users) * 100}%` }"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Users by Department Chart -->
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Distribuzione per Dipartimento</h3>
        <div class="space-y-3 max-h-64 overflow-y-auto">
          <div v-for="dept in analytics.users_by_department" :key="dept.department" class="flex items-center justify-between">
            <div class="flex items-center">
              <div class="w-3 h-3 rounded-full bg-indigo-500 mr-3"></div>
              <span class="text-sm text-gray-700 dark:text-gray-300 truncate">{{ dept.department }}</span>
            </div>
            <div class="flex items-center">
              <span class="text-sm font-medium text-gray-900 dark:text-white mr-2">{{ dept.count }}</span>
              <div class="w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div class="h-2 rounded-full bg-indigo-500" :style="{ width: `${(dept.count / analytics.overview.total_users) * 100}%` }"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Employment Types and Salary Analysis -->
    <div v-if="analytics" class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Employment Types -->
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Tipi di Contratto</h3>
        <div class="space-y-3">
          <div v-for="type in analytics.employment_types" :key="type.type" class="flex items-center justify-between">
            <div class="flex items-center">
              <div class="w-3 h-3 rounded-full bg-green-500 mr-3"></div>
              <span class="text-sm text-gray-700 dark:text-gray-300">{{ getEmploymentTypeLabel(type.type) }}</span>
            </div>
            <div class="flex items-center">
              <span class="text-sm font-medium text-gray-900 dark:text-white mr-2">{{ type.count }}</span>
              <div class="w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div class="h-2 rounded-full bg-green-500" :style="{ width: `${(type.count / analytics.overview.total_users) * 100}%` }"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Average Salary by Department -->
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Stipendio Medio per Dipartimento</h3>
        <div v-if="analytics.avg_salary_by_department.length > 0" class="space-y-3 max-h-64 overflow-y-auto">
          <div v-for="dept in analytics.avg_salary_by_department" :key="dept.department" class="flex items-center justify-between">
            <span class="text-sm text-gray-700 dark:text-gray-300 truncate">{{ dept.department }}</span>
            <span class="text-sm font-medium text-gray-900 dark:text-white">
              {{ formatCurrency(dept.avg_salary) }}
            </span>
          </div>
        </div>
        <div v-else class="text-center py-8">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
          <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">Nessun dato stipendio disponibile</p>
        </div>
      </div>
    </div>

    <!-- Refresh Button -->
    <div class="flex justify-center">
      <button @click="$emit('refresh')"
              :disabled="loading"
              class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50">
        <svg v-if="loading" class="animate-spin -ml-1 mr-3 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <svg v-else class="-ml-1 mr-3 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
        </svg>
        Aggiorna Dati
      </button>
    </div>

    <!-- Loading State -->
    <div v-if="loading && !analytics" class="flex justify-center items-center h-64">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
    </div>

    <!-- Empty State -->
    <div v-if="!loading && !analytics" class="bg-white dark:bg-gray-800 shadow rounded-lg p-12 text-center">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">Nessun dato disponibile</h3>
      <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
        Clicca su "Aggiorna Dati" per caricare le analytics.
      </p>
    </div>
  </div>
</template>

<script setup>
// Props
const props = defineProps({
  analytics: {
    type: Object,
    default: null
  },
  loading: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['refresh'])

// Methods
const getRoleLabel = (role) => {
  const labels = {
    'admin': 'Admin',
    'manager': 'Manager',
    'employee': 'Dipendente'
  }
  return labels[role] || role
}

const getEmploymentTypeLabel = (type) => {
  const labels = {
    'full_time': 'Tempo pieno',
    'part_time': 'Part-time',
    'contractor': 'Consulente',
    'intern': 'Stagista'
  }
  return labels[type] || type
}

const formatCurrency = (amount) => {
  if (!amount) return '€0'
  return new Intl.NumberFormat('it-IT', {
    style: 'currency',
    currency: 'EUR'
  }).format(amount)
}
</script>

<style scoped>
/* Custom styles for analytics dashboard */
.chart-container {
  position: relative;
  height: 300px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .chart-container {
    height: 200px;
  }
}
</style>

# Implementation Planning and Priorities
- User prefers complete implementation plans with clear priorities before starting development work
- User prefers to create detailed roadmaps/scalettas before starting development work on migration tasks.
- User prefers rapid, decent quality implementation over perfectionism when working on tasks
- User expects comprehensive solutions that address all mentioned issues in a request, not just partial fixes.
- Never create mockup or simplified code - always implement full, production-ready solutions
- When proposing new architectures or migrations, ensure all existing features, utilities, and test structures are properly migrated
- Task 7 HR Management implementation plan: Phase 1 (Profile Management, Skills CRUD), Phase 2 (Department Management, Organization Chart), Phase 3 (Resource Allocation), Phase 4 (Security & Compliance)
- Complete department management (including fixing missing orgchart navigation link) before implementing resource allocation
- User requires production-ready code with no mock data
- All components must be production-ready (no mock/placeholder code), dashboard needs migration from legacy, and development should follow the Vue professional migration plan in specs/vue_professional_migration.md.
- User prefers isofunctional Vue.js migrations that maintain exact functional parity with legacy systems, preserving all features, workflows, data structures, and user experience rather than reimplementing from scratch.
- User prefers to follow documented migration plans (like specs/personnel_migration_plan.md) for isofunctional migrations that maintain exact functional parity.
- Sprint 3 of the Personnel Vue.js migration plan to be completed: PersonnelOrgChart (company org chart), SkillsMatrix (skills matrix), and PersonnelAdmin (personnel administration) - Sprints 1 and 2 completed successfully.
- User prefers to implement Personnel components in this order: PersonnelOrgChart.vue (interactive org chart), SkillsMatrix.vue (team skills matrix), PersonnelAdmin.vue (advanced personnel administration).

# UI/UX Design and Branding
- User prefers consolidated UI interfaces with tab-based pages rather than separate dedicated pages
- User prefers icons in all tab headers for consistency and expects URL hash fragments to maintain tab state on page reload
- When creating items via modal forms, the page should stay on the current tab instead of redirecting
- User prefers centralized CSS instead of inline <style> tags and CSRF tokens should always be in hidden input fields
- User strongly prefers to maintain SPA functionality with non-reloading page navigation and static sidebar during page changes
- Future task will implement dynamic branding management (logo, fonts, colors), so Tailwind CSS should use CSS variables for customization
- Dark mode toggle should be implemented in layout components and managed at component level, not just in views
- User prefers to create example logo files (including monogram versions) when logo assets are missing from the static/img directory during build process.
- User prefers tab navigation that doesn't reload entire views for better UX.
- User prefers no emojis anywhere in the UI and wants them replaced with proper icons instead.
- User prefers department list pages to have pagination, proper grid sizing, action buttons for each item, and prominent creation buttons rather than wide unpaginated tables.
- User prefers department lists without 'Tipo' column and expects action buttons (View, Edit, Delete) to be clearly visible in department management interfaces.
- User prefers single-column vertical layouts over two-column layouts for profile pages to optimize space utilization and maximize content area for tabs, especially for tables and lists.
- User prefers HR information sections to be positioned in the same row as other profile sections rather than being separated or given special positioning.
- User prefers admin panels to have simplified tab versions with basic CRUD and statistics, plus prominent links to dedicated full-featured pages for complex operations, maintaining design consistency throughout.

# Vue.js Development and Organization
- User prefers proper Vue.js Single File Components (.vue) with build system over inline template .js approach
- User prefers organizing Vue.js components within view directories (e.g., views/projects/components/) rather than in separate global components directories
- When reorganizing Vue.js components, ensure all components are moved consistently across all modules
- User wants to continue Vue.js SPA migration by converting additional views beyond the dashboard that's currently implemented
- The base.html template was intentionally removed during Vue.js refactoring and spa.html should be used universally for all pages instead
- User prefers frontend to work exclusively with Vue.js and identified that app.py routing may have issues preventing proper Vue.js SPA functionality
- User prefers blueprints to contain only API endpoints when using Vue.js SPA architecture
- The backend/static/dist folder contains JS references that are used in the single HTML template (vue_app.html) for serving the Vue.js SPA.
- User prefers to follow existing component structure and directory organization when creating new components.
- Dashboard links should be consistent without /app prefix, and legacy admin page should be replicated in Vue.js migration.
- User expects the new Vue.js project detail view to have feature parity with the legacy project detail view functionality.
- Legacy code/files are not being used in the current Vue.js migration and should not be considered for fixes or imports.
- User reported sidebar collapse issue fixed, Gantt today line hidden, expenses API error fixed with can_view_project method, but now there's a 404 error for /api/personnel/ endpoint in ProjectView.js
- PersonnelProfile.vue should have a tab system (Projects, Tasks, Skills, Timesheet, CV), progress bar for profile completion, and timesheet is a critical daily-use feature for employees to track work hours on projects.

# Task and Project Management
- Different project types (service, license, consulting, product, R&D) should have different KPI expectations and calculations
- Projects should have billable/non-billable flag, and personnel costs should be valued with validity periods to handle changes
- Project economic management should include client rate display, real billing tracking, and comprehensive financial features
- For personnel cost calculations, use daily rates instead of hourly rates as the base unit
- User prefers KPI thresholds to be configurable per project, with AI-suggested or default values, rather than hardcoded
- For timesheet views, user prefers days as columns and hours in cells, plus an aggregated view with persons as rows and months as columns
- KPI components should include configuration functionality that connects to admin/kpi_templates from legacy system for managing KPI templates and thresholds.
- User prefers hours to be rounded/formatted in KPI displays rather than showing decimal precision.
- User identified that complete CRUD operations for Projects need to be implemented, including edit functionality that is currently missing.

# Database and Technical Management
- When seeding database, preserve existing users instead of clearing them completely; database clearing should be optional
- For database schema changes, use db_update file instead of creating migrations
- The /backend/db_update.py file can be used to execute database queries and operations.
- The Flask application should be started with 'python main.py' instead of 'python app.py'
- Always keep Swagger JSON documentation updated for every API endpoint when creating or modifying APIs
- User prefers simpler routing patterns with single route instead of multiple separate routes for similar functionality
- User wants tests to be written, verified and updated when implementing new features or making code changes
- User wants unused/dead files to be cleaned up rather than left in the codebase
- User requires notification and transparency when making code changes, especially when disabling or modifying critical functionality like permissions.

# HR Management
- Personnel module has 4 incomplete placeholder views (Organigramma, Competenze, Dipartimenti, Amministrazione) that need full implementation, with Competenze being particularly complex based on legacy system, and PersonnelProfile will require many future modifications.
- User wants to integrate existing contractual data fields from the UserProfile model (hire_date, probation_end_date, contract_end_date, notice_period_days, employment_type, salary, etc.) into the PersonnelAdmin.vue implementation plan.
- User prefers to keep 'Directory' as the sidebar navigation label for the personnel module instead of 'Team'.
- User prefers timesheet views to use monthly grid layout similar to projects view.
- Profile completion percentages should be rounded rather than showing decimals.
- User prefers orgchart components to have multiple visualization modes (Tree, List, Chart), comprehensive filters (hierarchical level, department size, budget), and expects all interactive controls to be fully functional with real-time filtering and responsive design.
- Personnel module should include contractual management views for HR use, requiring analysis of legacy implementation, database models, and APIs before finalizing the development plan.
import{r as f,f as R,w as de,A as F,c as a,o,j as e,g as k,v as _,x as V,H as A,F as U,k as S,t as m,m as z,n as P,a as O,i as X,b as Y,s as q,h as Z,y as ue,z as W,I as K,C as ce}from"./vendor.js";import{_ as J}from"./app.js";const me={class:"space-y-6"},ge={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0"},pe={class:"flex-1 max-w-lg"},ve={class:"relative"},xe={class:"flex items-center space-x-3"},ye=["value"],be=["disabled"],fe={key:0,class:"animate-spin w-4 h-4",fill:"none",viewBox:"0 0 24 24"},ke={key:1,class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},he={key:0,class:"flex justify-center items-center h-64"},we={key:1,class:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4"},$e={class:"flex"},_e={class:"text-sm text-red-700 dark:text-red-300 mt-1"},Ce={key:2,class:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6"},Me={class:"flex items-center justify-between"},ze={class:"flex items-center"},je={class:"text-sm font-medium text-blue-800 dark:text-blue-200"},Ee={key:0,class:"font-normal"},Be={key:0,class:"text-xs text-blue-600 dark:text-blue-400 mt-1"},Te={key:0,class:"flex items-center space-x-2"},He={key:3,class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},Ve={class:"overflow-x-auto"},De={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},Le={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},Ue={class:"px-6 py-4 whitespace-nowrap"},Se={class:"flex items-center"},Pe={class:"flex-shrink-0 w-10 h-10"},Ae={class:"w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center"},Ie=["src","alt"],Ne={key:1,class:"w-5 h-5 text-gray-600 dark:text-gray-300",fill:"currentColor",viewBox:"0 0 20 20"},Re={class:"ml-4"},Oe={class:"text-sm font-medium text-gray-900 dark:text-white"},Fe={class:"text-sm text-gray-500 dark:text-gray-400"},Ge={key:0,class:"text-xs text-gray-400 dark:text-gray-500"},qe={class:"px-6 py-4 whitespace-nowrap"},Je={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},Qe={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"},Ke={class:"space-y-1"},We={key:0},Xe={key:1},Ye={class:"px-6 py-4 whitespace-nowrap"},Ze={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},et={class:"flex items-center justify-end space-x-2"},tt=["onClick"],rt=["onClick"],st=["onClick"],ot={key:0,class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},at={key:1,class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},lt={key:0,class:"bg-white dark:bg-gray-800 px-4 py-3 border-t border-gray-200 dark:border-gray-700 sm:px-6"},nt={class:"flex items-center justify-between"},it={class:"flex-1 flex justify-between sm:hidden"},dt=["disabled"],ut=["disabled"],ct={class:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between"},mt={class:"text-sm text-gray-700 dark:text-gray-300"},gt={class:"font-medium"},pt={class:"font-medium"},vt={class:"font-medium"},xt={key:0,class:"text-gray-500 dark:text-gray-400"},yt={key:0,class:"text-xs text-gray-500 dark:text-gray-400 mt-1"},bt={class:"flex items-center space-x-2"},ft={class:"flex items-center space-x-1"},kt=["disabled"],ht=["disabled"],wt={class:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px"},$t=["onClick","disabled"],_t={class:"flex items-center space-x-1"},Ct=["disabled"],Mt=["disabled"],zt={key:4,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-12 text-center"},jt={class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},Et={__name:"UsersManagement",emits:["user-created","user-updated","user-deleted"],setup(C,{emit:L}){const b=L,x=f([]),h=f([]),y=f(!1),i=f(null),u=f(""),M=f(""),$=f(""),l=f(1),t=f(20),r=R(()=>{let g=x.value;if(u.value.trim()){const n=u.value.toLowerCase();g=g.filter(E=>E.full_name.toLowerCase().includes(n)||E.email.toLowerCase().includes(n)||E.position&&E.position.toLowerCase().includes(n)||E.department&&E.department.name.toLowerCase().includes(n))}return M.value&&(g=g.filter(n=>n.department_id==M.value)),$.value&&(g=g.filter(n=>n.role===$.value)),g}),w=R(()=>{const g=(l.value-1)*t.value,n=g+t.value;return r.value.slice(g,n)}),B=R(()=>Math.ceil(r.value.length/t.value)),I=R(()=>{const g=[],n=B.value,E=l.value;if(n<=7)for(let p=1;p<=n;p++)g.push(p);else if(E<=4){for(let p=1;p<=5;p++)g.push(p);g.push("..."),g.push(n)}else if(E>=n-3){g.push(1),g.push("...");for(let p=n-4;p<=n;p++)g.push(p)}else{g.push(1),g.push("...");for(let p=E-1;p<=E+1;p++)g.push(p);g.push("..."),g.push(n)}return g.filter(p=>p!=="..."||g.indexOf(p)===g.lastIndexOf(p))}),D=R(()=>u.value.trim()!==""||M.value!==""||$.value!==""),T=async()=>{y.value=!0,i.value=null;try{const g=await fetch("/api/personnel/users",{credentials:"include"});if(!g.ok)throw new Error(`HTTP ${g.status}: ${g.statusText}`);const n=await g.json();if(n.success)x.value=n.data.users||[];else throw new Error(n.message||"Errore nel caricamento utenti")}catch(g){console.error("Error loading users:",g),i.value=g.message}finally{y.value=!1}},c=async()=>{try{const g=await fetch("/api/personnel/departments",{credentials:"include"});if(!g.ok)throw new Error(`HTTP ${g.status}: ${g.statusText}`);const n=await g.json();n.success&&(h.value=n.data.departments||[])}catch(g){console.error("Error loading departments:",g)}},v=g=>{console.log("Edit user:",g)},s=async g=>{if(confirm(`Sei sicuro di voler resettare la password di ${g.full_name}?`))try{const n=await fetch(`/api/personnel/admin/users/${g.id}/reset-password`,{method:"POST",credentials:"include"});if(!n.ok)throw new Error(`HTTP ${n.status}: ${n.statusText}`);const E=await n.json();if(E.success)alert(`Password resettata per ${g.full_name}. Nuova password temporanea: ${E.data.temporary_password}`);else throw new Error(E.message||"Errore nel reset password")}catch(n){console.error("Error resetting password:",n),alert("Errore nel reset della password: "+n.message)}},d=async g=>{const n=g.is_active?"disattivare":"riattivare";if(confirm(`Sei sicuro di voler ${n} ${g.full_name}?`))try{if(g.is_active){const E=await fetch(`/api/personnel/admin/users/${g.id}`,{method:"DELETE",credentials:"include"});if(!E.ok)throw new Error(`HTTP ${E.status}: ${E.statusText}`);(await E.json()).success&&(g.is_active=!1,b("user-deleted",g))}else{const E=await fetch(`/api/personnel/users/${g.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({is_active:!0})});if(!E.ok)throw new Error(`HTTP ${E.status}: ${E.statusText}`);(await E.json()).success&&(g.is_active=!0,b("user-updated",g))}}catch(E){console.error("Error toggling user status:",E),alert("Errore nell'operazione: "+E.message)}},j=()=>{l.value>1&&l.value--},H=()=>{l.value<B.value&&l.value++},N=()=>{l.value=1},Q=()=>{l.value=B.value},oe=g=>{g!=="..."&&g>=1&&g<=B.value&&(l.value=g)},ae=()=>{u.value="",M.value="",$.value=""},le=g=>({admin:"Admin",manager:"Manager",employee:"Dipendente"})[g]||g,ne=g=>({full_time:"Tempo pieno",part_time:"Part-time",contractor:"Consulente",intern:"Stagista"})[g]||g,ee=g=>g?new Date(g).toLocaleDateString("it-IT",{year:"numeric",month:"short",day:"numeric"}):"",ie=g=>{if(!g)return!1;const n=new Date,p=new Date(g)-n,G=Math.ceil(p/(1e3*60*60*24));return G<=90&&G>=0};return de([u,M,$,t],()=>{l.value=1}),F(async()=>{await Promise.all([T(),c()])}),(g,n)=>{const E=Y("router-link");return o(),a("div",me,[e("div",ge,[e("div",pe,[e("div",ve,[n[4]||(n[4]=e("svg",{class:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1)),_(e("input",{"onUpdate:modelValue":n[0]||(n[0]=p=>u.value=p),type:"text",placeholder:"Cerca dipendenti...",class:"pl-10 pr-4 py-2 w-full border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[V,u.value]])])]),e("div",xe,[_(e("select",{"onUpdate:modelValue":n[1]||(n[1]=p=>M.value=p),class:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},[n[5]||(n[5]=e("option",{value:""},"Tutti i dipartimenti",-1)),(o(!0),a(U,null,S(h.value,p=>(o(),a("option",{key:p.id,value:p.id},m(p.name),9,ye))),128))],512),[[A,M.value]]),_(e("select",{"onUpdate:modelValue":n[2]||(n[2]=p=>$.value=p),class:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},n[6]||(n[6]=[e("option",{value:""},"Tutti i ruoli",-1),e("option",{value:"admin"},"Admin",-1),e("option",{value:"manager"},"Manager",-1),e("option",{value:"employee"},"Dipendente",-1)]),512),[[A,$.value]]),_(e("select",{"onUpdate:modelValue":n[3]||(n[3]=p=>t.value=p),class:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},n[7]||(n[7]=[e("option",{value:10},"10 per pagina",-1),e("option",{value:20},"20 per pagina",-1),e("option",{value:50},"50 per pagina",-1),e("option",{value:100},"100 per pagina",-1)]),512),[[A,t.value]]),e("button",{onClick:T,disabled:y.value,class:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"},[y.value?(o(),a("svg",fe,n[8]||(n[8]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):(o(),a("svg",ke,n[9]||(n[9]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"},null,-1)])))],8,be)])]),y.value&&!x.value.length?(o(),a("div",he,n[10]||(n[10]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):i.value?(o(),a("div",we,[e("div",$e,[n[12]||(n[12]=e("svg",{class:"w-5 h-5 text-red-400 mr-2 mt-0.5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z","clip-rule":"evenodd"})],-1)),e("div",null,[n[11]||(n[11]=e("h3",{class:"text-sm font-medium text-red-800 dark:text-red-200"},"Errore nel caricamento",-1)),e("p",_e,m(i.value),1)])])])):k("",!0),r.value.length>0?(o(),a("div",Ce,[e("div",Me,[e("div",ze,[n[13]||(n[13]=e("svg",{class:"w-5 h-5 text-blue-600 dark:text-blue-400 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})],-1)),e("div",null,[e("p",je,[z(m(r.value.length)+" dipendenti trovati ",1),D.value?(o(),a("span",Ee," (filtrati da "+m(x.value.length)+" totali) ",1)):k("",!0)]),B.value>1?(o(),a("p",Be," Visualizzazione pagina "+m(l.value)+" di "+m(B.value)+" • "+m(t.value)+" per pagina ",1)):k("",!0)])]),D.value?(o(),a("div",Te,[e("button",{onClick:ae,class:"inline-flex items-center px-3 py-1 border border-blue-300 dark:border-blue-600 text-xs font-medium rounded text-blue-700 dark:text-blue-300 bg-white dark:bg-blue-900/50 hover:bg-blue-50 dark:hover:bg-blue-900/70"},n[14]||(n[14]=[e("svg",{class:"w-3 h-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1),z(" Rimuovi Filtri ")]))])):k("",!0)])])):k("",!0),w.value.length>0?(o(),a("div",He,[e("div",Ve,[e("table",De,[n[21]||(n[21]=e("thead",{class:"bg-gray-50 dark:bg-gray-700"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Dipendente "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Ruolo "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Dipartimento "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Contratto "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Stato "),e("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Azioni ")])],-1)),e("tbody",Le,[(o(!0),a(U,null,S(w.value,p=>{var G,te,re;return o(),a("tr",{key:p.id,class:"hover:bg-gray-50 dark:hover:bg-gray-700"},[e("td",Ue,[e("div",Se,[e("div",Pe,[e("div",Ae,[p.profile_image?(o(),a("img",{key:0,src:p.profile_image,alt:p.full_name,class:"w-10 h-10 rounded-full object-cover"},null,8,Ie)):(o(),a("svg",Ne,n[15]||(n[15]=[e("path",{"fill-rule":"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z","clip-rule":"evenodd"},null,-1)])))])]),e("div",Re,[e("div",Oe,m(p.full_name),1),e("div",Fe,m(p.email),1),p.position?(o(),a("div",Ge,m(p.position),1)):k("",!0)])])]),e("td",qe,[e("span",{class:P(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",p.role==="admin"?"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200":p.role==="manager"?"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200":"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"])},m(le(p.role)),3)]),e("td",Je,m(((G=p.department)==null?void 0:G.name)||"Nessun dipartimento"),1),e("td",Qe,[e("div",Ke,[p.hire_date?(o(),a("div",We," Assunto: "+m(ee(p.hire_date)),1)):k("",!0),(te=p.profile)!=null&&te.employment_type?(o(),a("div",Xe,m(ne(p.profile.employment_type)),1)):k("",!0),(re=p.profile)!=null&&re.contract_end_date?(o(),a("div",{key:2,class:P(ie(p.profile.contract_end_date)?"text-red-600 dark:text-red-400 font-medium":"")}," Scade: "+m(ee(p.profile.contract_end_date)),3)):k("",!0)])]),e("td",Ye,[e("span",{class:P(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",p.is_active?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200":"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"])},m(p.is_active?"Attivo":"Disattivato"),3)]),e("td",Ze,[e("div",et,[O(E,{to:`/app/personnel/${p.id}`,class:"text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300"},{default:X(()=>n[16]||(n[16]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})],-1)])),_:2,__:[16]},1032,["to"]),e("button",{onClick:se=>v(p),class:"text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300"},n[17]||(n[17]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1)]),8,tt),e("button",{onClick:se=>s(p),class:"text-yellow-600 dark:text-yellow-400 hover:text-yellow-900 dark:hover:text-yellow-300"},n[18]||(n[18]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"})],-1)]),8,rt),e("button",{onClick:se=>d(p),class:P(p.is_active?"text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300":"text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300")},[p.is_active?(o(),a("svg",ot,n[19]||(n[19]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"},null,-1)]))):(o(),a("svg",at,n[20]||(n[20]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"},null,-1)])))],10,st)])])])}),128))])])]),B.value>1?(o(),a("div",lt,[e("div",nt,[e("div",it,[e("button",{onClick:j,disabled:l.value===1,class:"relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"}," Precedente ",8,dt),e("button",{onClick:H,disabled:l.value===B.value,class:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"}," Successiva ",8,ut)]),e("div",ct,[e("div",null,[e("p",mt,[n[22]||(n[22]=z(" Mostrando ")),e("span",gt,m((l.value-1)*t.value+1),1),n[23]||(n[23]=z(" a ")),e("span",pt,m(Math.min(l.value*t.value,r.value.length)),1),n[24]||(n[24]=z(" di ")),e("span",vt,m(r.value.length),1),n[25]||(n[25]=z(" risultati ")),D.value?(o(),a("span",xt," (filtrati da "+m(x.value.length)+" totali) ",1)):k("",!0)]),B.value>1?(o(),a("p",yt," Pagina "+m(l.value)+" di "+m(B.value),1)):k("",!0)]),e("div",bt,[e("div",ft,[e("button",{onClick:N,disabled:l.value===1,class:"relative inline-flex items-center px-2 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 rounded-md",title:"Prima pagina"},n[26]||(n[26]=[e("svg",{class:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M15.707 15.707a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 010 1.414zm-6 0a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 011.414 1.414L5.414 10l4.293 4.293a1 1 0 010 1.414z","clip-rule":"evenodd"})],-1)]),8,kt),e("button",{onClick:j,disabled:l.value===1,class:"relative inline-flex items-center px-2 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 rounded-md",title:"Pagina precedente"},n[27]||(n[27]=[e("svg",{class:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z","clip-rule":"evenodd"})],-1)]),8,ht)]),e("nav",wt,[(o(!0),a(U,null,S(I.value,p=>(o(),a("button",{key:p,onClick:G=>oe(p),disabled:p==="...",class:P(["relative inline-flex items-center px-3 py-2 border text-sm font-medium",p===l.value?"z-10 bg-blue-50 dark:bg-blue-900 border-blue-500 text-blue-600 dark:text-blue-200":p==="..."?"bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-400 cursor-default":"bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700"])},m(p),11,$t))),128))]),e("div",_t,[e("button",{onClick:H,disabled:l.value===B.value,class:"relative inline-flex items-center px-2 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 rounded-md",title:"Pagina successiva"},n[28]||(n[28]=[e("svg",{class:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})],-1)]),8,Ct),e("button",{onClick:Q,disabled:l.value===B.value,class:"relative inline-flex items-center px-2 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 rounded-md",title:"Ultima pagina"},n[29]||(n[29]=[e("svg",{class:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10.293 15.707a1 1 0 010-1.414L14.586 10l-4.293-4.293a1 1 0 111.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0zm-6 0a1 1 0 010-1.414L8.586 10 4.293 5.707a1 1 0 011.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z","clip-rule":"evenodd"})],-1)]),8,Mt)])])])])])):k("",!0)])):(o(),a("div",zt,[n[30]||(n[30]=e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})],-1)),n[31]||(n[31]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun dipendente trovato",-1)),e("p",jt,m(D.value?"Prova a modificare i filtri di ricerca.":"Inizia creando il primo dipendente."),1)]))])}}},Bt=J(Et,[["__scopeId","data-v-26a59f08"]]),Tt={class:"flex items-center justify-between pb-4 border-b border-gray-200 dark:border-gray-700"},Ht={class:"text-lg font-medium text-gray-900 dark:text-white"},Vt={class:"mt-6"},Dt={class:"grid grid-cols-1 gap-4"},Lt={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Ut=["value"],St=["value"],Pt={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},At={key:0,class:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4"},It={class:"flex"},Nt={class:"text-sm text-red-700 dark:text-red-300 mt-1"},Rt={class:"flex items-center justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700"},Ot=["disabled"],Ft={key:0,class:"animate-spin -ml-1 mr-3 h-4 w-4 text-white inline",fill:"none",viewBox:"0 0 24 24"},Gt={__name:"DepartmentModal",props:{department:{type:Object,default:null},managers:{type:Array,default:()=>[]},departments:{type:Array,default:()=>[]}},emits:["close","saved"],setup(C,{emit:L}){const b=C,x=L,h=f(!1),y=f(null),i=f({name:"",description:"",manager_id:"",parent_id:"",budget:null,code:""}),u=R(()=>b.department?b.departments.filter($=>$.id!==b.department.id&&$.parent_id!==b.department.id):b.departments),M=async()=>{h.value=!0,y.value=null;try{const $={...i.value};Object.keys($).forEach(B=>{$[B]===""&&($[B]=null)});const l=b.department?`/api/personnel/departments/${b.department.id}`:"/api/personnel/departments",t=b.department?"PUT":"POST",r=await fetch(l,{method:t,headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify($)});if(!r.ok)throw new Error(`HTTP ${r.status}: ${r.statusText}`);const w=await r.json();if(w.success)x("saved",w.data.department);else throw new Error(w.message||"Errore nel salvataggio dipartimento")}catch($){console.error("Error saving department:",$),y.value=$.message}finally{h.value=!1}};return F(()=>{b.department&&(i.value={name:b.department.name||"",description:b.department.description||"",manager_id:b.department.manager_id||"",parent_id:b.department.parent_id||"",budget:b.department.budget||null,code:b.department.code||""})}),($,l)=>(o(),a("div",{class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:l[9]||(l[9]=t=>$.$emit("close"))},[e("div",{class:"relative top-20 mx-auto p-5 border w-11/12 max-w-2xl shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:l[8]||(l[8]=q(()=>{},["stop"]))},[e("div",Tt,[e("h3",Ht,m(C.department?"Modifica Dipartimento":"Nuovo Dipartimento"),1),e("button",{onClick:l[0]||(l[0]=t=>$.$emit("close")),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},l[10]||(l[10]=[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),e("div",Vt,[e("form",{onSubmit:q(M,["prevent"]),class:"space-y-6"},[e("div",null,[l[13]||(l[13]=e("h4",{class:"text-md font-medium text-gray-900 dark:text-white mb-4"},"Informazioni Base",-1)),e("div",Dt,[e("div",null,[l[11]||(l[11]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Nome Dipartimento * ",-1)),_(e("input",{"onUpdate:modelValue":l[1]||(l[1]=t=>i.value.name=t),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[V,i.value.name]])]),e("div",null,[l[12]||(l[12]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Descrizione ",-1)),_(e("textarea",{"onUpdate:modelValue":l[2]||(l[2]=t=>i.value.description=t),rows:"3",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[V,i.value.description]])])])]),e("div",null,[l[18]||(l[18]=e("h4",{class:"text-md font-medium text-gray-900 dark:text-white mb-4"},"Gestione",-1)),e("div",Lt,[e("div",null,[l[15]||(l[15]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Manager ",-1)),_(e("select",{"onUpdate:modelValue":l[3]||(l[3]=t=>i.value.manager_id=t),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},[l[14]||(l[14]=e("option",{value:""},"Seleziona manager",-1)),(o(!0),a(U,null,S(C.managers,t=>(o(),a("option",{key:t.id,value:t.id},m(t.full_name),9,Ut))),128))],512),[[A,i.value.manager_id]])]),e("div",null,[l[17]||(l[17]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Dipartimento Padre ",-1)),_(e("select",{"onUpdate:modelValue":l[4]||(l[4]=t=>i.value.parent_id=t),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},[l[16]||(l[16]=e("option",{value:""},"Nessun padre (dipartimento principale)",-1)),(o(!0),a(U,null,S(u.value,t=>(o(),a("option",{key:t.id,value:t.id},m(t.name),9,St))),128))],512),[[A,i.value.parent_id]])])])]),e("div",null,[l[21]||(l[21]=e("h4",{class:"text-md font-medium text-gray-900 dark:text-white mb-4"},"Budget",-1)),e("div",Pt,[e("div",null,[l[19]||(l[19]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Budget Annuale (€) ",-1)),_(e("input",{"onUpdate:modelValue":l[5]||(l[5]=t=>i.value.budget=t),type:"number",step:"100",min:"0",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[V,i.value.budget,void 0,{number:!0}]])]),e("div",null,[l[20]||(l[20]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Codice Dipartimento ",-1)),_(e("input",{"onUpdate:modelValue":l[6]||(l[6]=t=>i.value.code=t),type:"text",placeholder:"es. IT, HR, SALES",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[V,i.value.code]])])])]),y.value?(o(),a("div",At,[e("div",It,[l[23]||(l[23]=e("svg",{class:"w-5 h-5 text-red-400 mr-2 mt-0.5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z","clip-rule":"evenodd"})],-1)),e("div",null,[l[22]||(l[22]=e("h3",{class:"text-sm font-medium text-red-800 dark:text-red-200"},"Errore nel salvataggio",-1)),e("p",Nt,m(y.value),1)])])])):k("",!0),e("div",Rt,[e("button",{type:"button",onClick:l[7]||(l[7]=t=>$.$emit("close")),class:"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"}," Annulla "),e("button",{type:"submit",disabled:h.value,class:"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"},[h.value?(o(),a("svg",Ft,l[24]||(l[24]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):k("",!0),z(" "+m(h.value?"Salvataggio...":C.department?"Aggiorna":"Crea"),1)],8,Ot)])],32)])])]))}},qt=J(Gt,[["__scopeId","data-v-2d5db05c"]]),Jt={class:"space-y-6"},Qt={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},Kt={class:"mt-4 sm:mt-0 flex items-center space-x-3"},Wt={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},Xt={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},Yt={class:"p-5"},Zt={class:"flex items-center"},er={class:"ml-5 w-0 flex-1"},tr={class:"text-lg font-medium text-gray-900 dark:text-white"},rr={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},sr={class:"p-5"},or={class:"flex items-center"},ar={class:"ml-5 w-0 flex-1"},lr={class:"text-lg font-medium text-gray-900 dark:text-white"},nr={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},ir={class:"p-5"},dr={class:"flex items-center"},ur={class:"ml-5 w-0 flex-1"},cr={class:"text-lg font-medium text-gray-900 dark:text-white"},mr={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},gr={class:"p-5"},pr={class:"flex items-center"},vr={class:"ml-5 w-0 flex-1"},xr={class:"text-lg font-medium text-gray-900 dark:text-white"},yr={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0"},br={class:"flex-1 max-w-lg"},fr={class:"relative"},kr={class:"flex items-center space-x-3"},hr=["disabled"],wr={key:0,class:"animate-spin w-4 h-4",fill:"none",viewBox:"0 0 24 24"},$r={key:1,class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},_r={key:0,class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},Cr={class:"overflow-x-auto"},Mr={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},zr={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},jr={class:"px-6 py-4 whitespace-nowrap"},Er={class:"text-sm font-medium text-gray-900 dark:text-white"},Br={key:0,class:"text-sm text-gray-500 dark:text-gray-400"},Tr={key:1,class:"text-xs text-gray-400 dark:text-gray-500"},Hr={class:"px-6 py-4 whitespace-nowrap"},Vr={key:0,class:"flex items-center"},Dr={class:"ml-3"},Lr={class:"text-sm font-medium text-gray-900 dark:text-white"},Ur={class:"text-sm text-gray-500 dark:text-gray-400"},Sr={key:1,class:"text-sm text-gray-500 dark:text-gray-400"},Pr={class:"px-6 py-4 whitespace-nowrap"},Ar={class:"flex items-center"},Ir={class:"text-sm font-medium text-gray-900 dark:text-white"},Nr={class:"px-6 py-4 whitespace-nowrap"},Rr={class:"text-sm text-gray-900 dark:text-white"},Or={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},Fr={class:"flex items-center justify-end space-x-2"},Gr=["onClick"],qr=["onClick","disabled"],Jr={key:1,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-12 text-center"},Qr={class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},Kr={class:"mt-6"},Wr={key:2,class:"flex justify-center items-center h-64"},Xr={__name:"DepartmentsManagement",emits:["department-created","department-updated","department-deleted"],setup(C,{emit:L}){const b=L,x=f([]),h=f([]),y=f({}),i=f(!1),u=f(""),M=f(!1),$=f(!1),l=f(null),t=R(()=>{if(!u.value.trim())return x.value;const s=u.value.toLowerCase();return x.value.filter(d=>d.name.toLowerCase().includes(s)||d.description&&d.description.toLowerCase().includes(s)||d.manager&&d.manager.full_name.toLowerCase().includes(s))}),r=async()=>{i.value=!0;try{const s=await fetch("/api/personnel/departments",{credentials:"include"});if(!s.ok)throw new Error(`HTTP ${s.status}: ${s.statusText}`);const d=await s.json();if(d.success)x.value=d.data.departments||[];else throw new Error(d.message||"Errore nel caricamento dipartimenti")}catch(s){console.error("Error loading departments:",s)}finally{i.value=!1}},w=async()=>{try{const s=await fetch("/api/personnel/orgchart",{credentials:"include"});if(!s.ok)throw new Error(`HTTP ${s.status}: ${s.statusText}`);const d=await s.json();if(d.success){y.value=d.data.stats||{};const j=x.value.reduce((H,N)=>H+(N.budget||0),0);y.value.total_budget=j}}catch(s){console.error("Error loading stats:",s)}},B=async()=>{try{const s=await fetch("/api/personnel/users?role=manager,admin",{credentials:"include"});if(!s.ok)throw new Error(`HTTP ${s.status}: ${s.statusText}`);const d=await s.json();d.success&&(h.value=d.data.users||[])}catch(s){console.error("Error loading managers:",s)}},I=s=>{l.value={...s},$.value=!0},D=async s=>{if(s.user_count>0){alert("Impossibile eliminare un dipartimento con dipendenti assegnati");return}if(confirm(`Sei sicuro di voler eliminare il dipartimento "${s.name}"?`))try{const d=await fetch(`/api/personnel/departments/${s.id}`,{method:"DELETE",credentials:"include"});if(!d.ok)throw new Error(`HTTP ${d.status}: ${d.statusText}`);const j=await d.json();if(j.success)await r(),await w(),b("department-deleted",s);else throw new Error(j.message||"Errore nell'eliminazione")}catch(d){console.error("Error deleting department:",d),alert("Errore nell'eliminazione: "+d.message)}},T=()=>{M.value=!1,$.value=!1,l.value=null},c=async s=>{T(),await r(),await w(),l.value?b("department-updated",s):b("department-created",s)},v=s=>s?new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(s):"€0";return F(async()=>{await Promise.all([r(),w(),B()])}),(s,d)=>{const j=Y("router-link");return o(),a("div",Jt,[e("div",Qt,[d[5]||(d[5]=e("div",null,[e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Gestione Dipartimenti"),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"}," Gestisci la struttura organizzativa aziendale ")],-1)),e("div",Kt,[O(j,{to:"/app/personnel/departments",class:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"},{default:X(()=>d[3]||(d[3]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"})],-1),z(" Vista Avanzata ")])),_:1,__:[3]}),e("button",{onClick:d[0]||(d[0]=H=>M.value=!0),class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"},d[4]||(d[4]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),z(" Nuovo Dipartimento ")]))])]),e("div",Wt,[e("div",Xt,[e("div",Yt,[e("div",Zt,[d[7]||(d[7]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-6 w-6 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})])],-1)),e("div",er,[e("dl",null,[d[6]||(d[6]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Dipartimenti Totali ",-1)),e("dd",tr,m(y.value.total_departments||0),1)])])])])]),e("div",rr,[e("div",sr,[e("div",or,[d[9]||(d[9]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-6 w-6 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})])],-1)),e("div",ar,[e("dl",null,[d[8]||(d[8]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Dipendenti Totali ",-1)),e("dd",lr,m(y.value.total_employees||0),1)])])])])]),e("div",nr,[e("div",ir,[e("div",dr,[d[11]||(d[11]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-6 w-6 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"})])],-1)),e("div",ur,[e("dl",null,[d[10]||(d[10]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Manager Assegnati ",-1)),e("dd",cr,m(y.value.total_managers||0),1)])])])])]),e("div",mr,[e("div",gr,[e("div",pr,[d[13]||(d[13]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-6 w-6 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",vr,[e("dl",null,[d[12]||(d[12]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Budget Totale ",-1)),e("dd",xr,m(v(y.value.total_budget||0)),1)])])])])])]),e("div",yr,[e("div",br,[e("div",fr,[d[14]||(d[14]=e("svg",{class:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1)),_(e("input",{"onUpdate:modelValue":d[1]||(d[1]=H=>u.value=H),type:"text",placeholder:"Cerca dipartimenti...",class:"pl-10 pr-4 py-2 w-full border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[V,u.value]])])]),e("div",kr,[e("button",{onClick:r,disabled:i.value,class:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"},[i.value?(o(),a("svg",wr,d[15]||(d[15]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):(o(),a("svg",$r,d[16]||(d[16]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"},null,-1)])))],8,hr)])]),t.value.length>0?(o(),a("div",_r,[e("div",Cr,[e("table",Mr,[d[21]||(d[21]=e("thead",{class:"bg-gray-50 dark:bg-gray-700"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Dipartimento "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Manager "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Dipendenti "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Budget "),e("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Azioni ")])],-1)),e("tbody",zr,[(o(!0),a(U,null,S(t.value,H=>(o(),a("tr",{key:H.id,class:"hover:bg-gray-50 dark:hover:bg-gray-700"},[e("td",jr,[e("div",null,[e("div",Er,m(H.name),1),H.description?(o(),a("div",Br,m(H.description),1)):k("",!0),H.parent?(o(),a("div",Tr," Sotto: "+m(H.parent.name),1)):k("",!0)])]),e("td",Hr,[H.manager?(o(),a("div",Vr,[d[17]||(d[17]=e("div",{class:"flex-shrink-0 w-8 h-8"},[e("div",{class:"w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-gray-600 dark:text-gray-300",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z","clip-rule":"evenodd"})])])],-1)),e("div",Dr,[e("div",Lr,m(H.manager.full_name),1),e("div",Ur,m(H.manager.email),1)])])):(o(),a("span",Sr,"Nessun manager"))]),e("td",Pr,[e("div",Ar,[e("span",Ir,m(H.user_count||0),1),d[18]||(d[18]=e("span",{class:"ml-1 text-sm text-gray-500 dark:text-gray-400"}," dipendenti ",-1))])]),e("td",Nr,[e("span",Rr,m(v(H.budget||0)),1)]),e("td",Or,[e("div",Fr,[e("button",{onClick:N=>I(H),class:"text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300"},d[19]||(d[19]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1)]),8,Gr),e("button",{onClick:N=>D(H),disabled:H.user_count>0,class:P(H.user_count>0?"text-gray-400 cursor-not-allowed":"text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300")},d[20]||(d[20]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)]),10,qr)])])]))),128))])])])])):i.value?k("",!0):(o(),a("div",Jr,[d[23]||(d[23]=e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})],-1)),d[24]||(d[24]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun dipartimento trovato",-1)),e("p",Qr,m(u.value?"Prova a modificare i criteri di ricerca.":"Inizia creando il primo dipartimento."),1),e("div",Kr,[e("button",{onClick:d[2]||(d[2]=H=>M.value=!0),class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"},d[22]||(d[22]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),z(" Crea Primo Dipartimento ")]))])])),i.value?(o(),a("div",Wr,d[25]||(d[25]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):k("",!0),M.value||$.value?(o(),Z(qt,{key:3,department:l.value,managers:h.value,departments:x.value,onClose:T,onSaved:c},null,8,["department","managers","departments"])):k("",!0)])}}},Yr={class:"flex items-center justify-between pb-4 border-b border-gray-200 dark:border-gray-700"},Zr={class:"text-lg font-medium text-gray-900 dark:text-white"},es={class:"mt-6"},ts={class:"flex space-x-2"},rs=["value"],ss={key:0,class:"mt-2"},os={key:0,class:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4"},as={class:"flex"},ls={class:"text-sm text-red-700 dark:text-red-300 mt-1"},ns={class:"flex items-center justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700"},is=["disabled"],ds={key:0,class:"animate-spin -ml-1 mr-3 h-4 w-4 text-white inline",fill:"none",viewBox:"0 0 24 24"},us={__name:"SkillModal",props:{skill:{type:Object,default:null},categories:{type:Array,default:()=>[]}},emits:["close","saved"],setup(C,{emit:L}){const b=C,x=L,h=f(!1),y=f(null),i=f(!1),u=f(""),M=f({name:"",category:"",description:""}),$=()=>{u.value.trim()&&(M.value.category=u.value.trim(),u.value="",i.value=!1)},l=async()=>{h.value=!0,y.value=null;try{const t={...M.value};Object.keys(t).forEach(D=>{t[D]===""&&(t[D]=null)});const r=b.skill?`/api/personnel/skills/${b.skill.id}`:"/api/personnel/skills",w=b.skill?"PUT":"POST",B=await fetch(r,{method:w,headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(t)});if(!B.ok)throw new Error(`HTTP ${B.status}: ${B.statusText}`);const I=await B.json();if(I.success)x("saved",I.data.skill);else throw new Error(I.message||"Errore nel salvataggio competenza")}catch(t){console.error("Error saving skill:",t),y.value=t.message}finally{h.value=!1}};return F(()=>{b.skill&&(M.value={name:b.skill.name||"",category:b.skill.category||"",description:b.skill.description||""})}),(t,r)=>(o(),a("div",{class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:r[8]||(r[8]=w=>t.$emit("close"))},[e("div",{class:"relative top-20 mx-auto p-5 border w-11/12 max-w-lg shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:r[7]||(r[7]=q(()=>{},["stop"]))},[e("div",Yr,[e("h3",Zr,m(C.skill?"Modifica Competenza":"Nuova Competenza"),1),e("button",{onClick:r[0]||(r[0]=w=>t.$emit("close")),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},r[9]||(r[9]=[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),e("div",es,[e("form",{onSubmit:q(l,["prevent"]),class:"space-y-4"},[e("div",null,[r[10]||(r[10]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Nome Competenza * ",-1)),_(e("input",{"onUpdate:modelValue":r[1]||(r[1]=w=>M.value.name=w),type:"text",required:"",placeholder:"es. JavaScript, Project Management, Design Thinking",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[V,M.value.name]])]),e("div",null,[r[12]||(r[12]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Categoria ",-1)),e("div",ts,[_(e("select",{"onUpdate:modelValue":r[2]||(r[2]=w=>M.value.category=w),class:"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},[r[11]||(r[11]=e("option",{value:""},"Seleziona categoria esistente",-1)),(o(!0),a(U,null,S(C.categories,w=>(o(),a("option",{key:w,value:w},m(w),9,rs))),128))],512),[[A,M.value.category]]),e("button",{type:"button",onClick:r[3]||(r[3]=w=>i.value=!i.value),class:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"},m(i.value?"Annulla":"Nuova"),1)]),i.value?(o(),a("div",ss,[_(e("input",{"onUpdate:modelValue":r[4]||(r[4]=w=>u.value=w),type:"text",placeholder:"Nome nuova categoria",onBlur:$,onKeyup:ue($,["enter"]),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,544),[[V,u.value]])])):k("",!0)]),e("div",null,[r[13]||(r[13]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Descrizione ",-1)),_(e("textarea",{"onUpdate:modelValue":r[5]||(r[5]=w=>M.value.description=w),rows:"3",placeholder:"Descrizione della competenza e come viene utilizzata...",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[V,M.value.description]])]),y.value?(o(),a("div",os,[e("div",as,[r[15]||(r[15]=e("svg",{class:"w-5 h-5 text-red-400 mr-2 mt-0.5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z","clip-rule":"evenodd"})],-1)),e("div",null,[r[14]||(r[14]=e("h3",{class:"text-sm font-medium text-red-800 dark:text-red-200"},"Errore nel salvataggio",-1)),e("p",ls,m(y.value),1)])])])):k("",!0),e("div",ns,[e("button",{type:"button",onClick:r[6]||(r[6]=w=>t.$emit("close")),class:"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"}," Annulla "),e("button",{type:"submit",disabled:h.value,class:"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"},[h.value?(o(),a("svg",ds,r[16]||(r[16]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):k("",!0),z(" "+m(h.value?"Salvataggio...":C.skill?"Aggiorna":"Crea"),1)],8,is)])],32)])])]))}},cs=J(us,[["__scopeId","data-v-06d169f0"]]),ms={class:"space-y-6"},gs={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},ps={class:"mt-4 sm:mt-0 flex items-center space-x-3"},vs={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},xs={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},ys={class:"p-5"},bs={class:"flex items-center"},fs={class:"ml-5 w-0 flex-1"},ks={class:"text-lg font-medium text-gray-900 dark:text-white"},hs={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},ws={class:"p-5"},$s={class:"flex items-center"},_s={class:"ml-5 w-0 flex-1"},Cs={class:"text-lg font-medium text-gray-900 dark:text-white"},Ms={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},zs={class:"p-5"},js={class:"flex items-center"},Es={class:"ml-5 w-0 flex-1"},Bs={class:"text-lg font-medium text-gray-900 dark:text-white"},Ts={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},Hs={class:"p-5"},Vs={class:"flex items-center"},Ds={class:"ml-5 w-0 flex-1"},Ls={class:"text-lg font-medium text-gray-900 dark:text-white"},Us={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0"},Ss={class:"flex-1 max-w-lg"},Ps={class:"relative"},As={class:"flex items-center space-x-3"},Is=["value"],Ns=["disabled"],Rs={key:0,class:"animate-spin w-4 h-4",fill:"none",viewBox:"0 0 24 24"},Os={key:1,class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Fs={key:0,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"},Gs={class:"flex items-start justify-between"},qs={class:"flex-1"},Js={class:"text-lg font-medium text-gray-900 dark:text-white"},Qs={key:0,class:"text-sm text-blue-600 dark:text-blue-400 mt-1"},Ks={key:1,class:"text-sm text-gray-500 dark:text-gray-400 mt-2"},Ws={class:"mt-4 flex items-center justify-between"},Xs={class:"flex items-center text-sm text-gray-500 dark:text-gray-400"},Ys={class:"flex items-center space-x-2"},Zs=["onClick"],eo=["onClick","disabled"],to={key:1,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-12 text-center"},ro={class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},so={class:"mt-6"},oo={key:2,class:"flex justify-center items-center h-64"},ao={__name:"SkillsManagement",emits:["skill-created","skill-updated","skill-deleted"],setup(C,{emit:L}){const b=L,x=f([]),h=f([]),y=f(!1),i=f(""),u=f(""),M=f(!1),$=f(!1),l=f(null),t=R(()=>{let v=x.value;if(i.value.trim()){const s=i.value.toLowerCase();v=v.filter(d=>d.name.toLowerCase().includes(s)||d.description&&d.description.toLowerCase().includes(s)||d.category&&d.category.toLowerCase().includes(s))}return u.value&&(v=v.filter(s=>s.category===u.value)),v}),r=R(()=>x.value.reduce((v,s)=>v+(s.user_count||0),0)),w=R(()=>x.value.filter(s=>s.user_count>0).length===0?0:3.2),B=async()=>{y.value=!0;try{const v=await fetch("/api/personnel/skills",{credentials:"include"});if(!v.ok)throw new Error(`HTTP ${v.status}: ${v.statusText}`);const s=await v.json();if(s.success)x.value=s.data.skills||[],h.value=s.data.categories||[];else throw new Error(s.message||"Errore nel caricamento competenze")}catch(v){console.error("Error loading skills:",v)}finally{y.value=!1}},I=v=>{l.value={...v},$.value=!0},D=async v=>{if(v.user_count>0){alert("Impossibile eliminare una competenza assegnata a dipendenti");return}if(confirm(`Sei sicuro di voler eliminare la competenza "${v.name}"?`))try{const s=await fetch(`/api/personnel/skills/${v.id}`,{method:"DELETE",credentials:"include"});if(!s.ok)throw new Error(`HTTP ${s.status}: ${s.statusText}`);const d=await s.json();if(d.success)await B(),b("skill-deleted",v);else throw new Error(d.message||"Errore nell'eliminazione")}catch(s){console.error("Error deleting skill:",s),alert("Errore nell'eliminazione: "+s.message)}},T=()=>{M.value=!1,$.value=!1,l.value=null},c=async v=>{T(),await B(),l.value?b("skill-updated",v):b("skill-created",v)};return F(()=>{B()}),(v,s)=>{const d=Y("router-link");return o(),a("div",ms,[e("div",gs,[s[6]||(s[6]=e("div",null,[e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Gestione Competenze"),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"}," Gestisci il catalogo delle competenze aziendali ")],-1)),e("div",ps,[O(d,{to:"/app/personnel/skills",class:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"},{default:X(()=>s[4]||(s[4]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"})],-1),z(" Matrice Completa ")])),_:1,__:[4]}),e("button",{onClick:s[0]||(s[0]=j=>M.value=!0),class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"},s[5]||(s[5]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),z(" Nuova Competenza ")]))])]),e("div",vs,[e("div",xs,[e("div",ys,[e("div",bs,[s[8]||(s[8]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-6 w-6 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})])],-1)),e("div",fs,[e("dl",null,[s[7]||(s[7]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Competenze Totali ",-1)),e("dd",ks,m(x.value.length),1)])])])])]),e("div",hs,[e("div",ws,[e("div",$s,[s[10]||(s[10]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-6 w-6 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"})])],-1)),e("div",_s,[e("dl",null,[s[9]||(s[9]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Categorie ",-1)),e("dd",Cs,m(h.value.length),1)])])])])]),e("div",Ms,[e("div",zs,[e("div",js,[s[12]||(s[12]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-6 w-6 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})])],-1)),e("div",Es,[e("dl",null,[s[11]||(s[11]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Competenze Assegnate ",-1)),e("dd",Bs,m(r.value),1)])])])])]),e("div",Ts,[e("div",Hs,[e("div",Vs,[s[14]||(s[14]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-6 w-6 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])],-1)),e("div",Ds,[e("dl",null,[s[13]||(s[13]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Livello Medio ",-1)),e("dd",Ls,m(w.value.toFixed(1)),1)])])])])])]),e("div",Us,[e("div",Ss,[e("div",Ps,[s[15]||(s[15]=e("svg",{class:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1)),_(e("input",{"onUpdate:modelValue":s[1]||(s[1]=j=>i.value=j),type:"text",placeholder:"Cerca competenze...",class:"pl-10 pr-4 py-2 w-full border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[V,i.value]])])]),e("div",As,[_(e("select",{"onUpdate:modelValue":s[2]||(s[2]=j=>u.value=j),class:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},[s[16]||(s[16]=e("option",{value:""},"Tutte le categorie",-1)),(o(!0),a(U,null,S(h.value,j=>(o(),a("option",{key:j,value:j},m(j),9,Is))),128))],512),[[A,u.value]]),e("button",{onClick:B,disabled:y.value,class:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"},[y.value?(o(),a("svg",Rs,s[17]||(s[17]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):(o(),a("svg",Os,s[18]||(s[18]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"},null,-1)])))],8,Ns)])]),t.value.length>0?(o(),a("div",Fs,[(o(!0),a(U,null,S(t.value,j=>(o(),a("div",{key:j.id,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6 hover:shadow-lg transition-shadow"},[e("div",Gs,[e("div",qs,[e("h4",Js,m(j.name),1),j.category?(o(),a("p",Qs,m(j.category),1)):k("",!0),j.description?(o(),a("p",Ks,m(j.description),1)):k("",!0),e("div",Ws,[e("div",Xs,[s[19]||(s[19]=e("svg",{class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})],-1)),z(" "+m(j.user_count)+" dipendenti ",1)]),e("div",Ys,[e("button",{onClick:H=>I(j),class:"text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300"},s[20]||(s[20]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1)]),8,Zs),e("button",{onClick:H=>D(j),disabled:j.user_count>0,class:P(j.user_count>0?"text-gray-400 cursor-not-allowed":"text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300")},s[21]||(s[21]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)]),10,eo)])])])])]))),128))])):y.value?k("",!0):(o(),a("div",to,[s[23]||(s[23]=e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})],-1)),s[24]||(s[24]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessuna competenza trovata",-1)),e("p",ro,m(i.value||u.value?"Prova a modificare i filtri di ricerca.":"Inizia creando la prima competenza."),1),e("div",so,[e("button",{onClick:s[3]||(s[3]=j=>M.value=!0),class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"},s[22]||(s[22]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),z(" Crea Prima Competenza ")]))])])),y.value?(o(),a("div",oo,s[25]||(s[25]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):k("",!0),M.value||$.value?(o(),Z(cs,{key:3,skill:l.value,categories:h.value,onClose:T,onSaved:c},null,8,["skill","categories"])):k("",!0)])}}},lo={class:"space-y-6"},no={key:0,class:"grid grid-cols-1 md:grid-cols-3 gap-6"},io={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},uo={class:"p-5"},co={class:"flex items-center"},mo={class:"ml-5 w-0 flex-1"},go={class:"text-lg font-medium text-gray-900 dark:text-white"},po={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},vo={class:"p-5"},xo={class:"flex items-center"},yo={class:"ml-5 w-0 flex-1"},bo={class:"text-lg font-medium text-gray-900 dark:text-white"},fo={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},ko={class:"p-5"},ho={class:"flex items-center"},wo={class:"ml-5 w-0 flex-1"},$o={class:"text-lg font-medium text-gray-900 dark:text-white"},_o={key:1,class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},Co={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Mo={class:"space-y-3"},zo={class:"flex items-center"},jo={class:"text-sm text-gray-700 dark:text-gray-300"},Eo={class:"flex items-center"},Bo={class:"text-sm font-medium text-gray-900 dark:text-white mr-2"},To={class:"w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2"},Ho={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Vo={class:"space-y-3 max-h-64 overflow-y-auto"},Do={class:"flex items-center"},Lo={class:"text-sm text-gray-700 dark:text-gray-300 truncate"},Uo={class:"flex items-center"},So={class:"text-sm font-medium text-gray-900 dark:text-white mr-2"},Po={class:"w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2"},Ao={key:2,class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},Io={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},No={class:"space-y-3"},Ro={class:"flex items-center"},Oo={class:"text-sm text-gray-700 dark:text-gray-300"},Fo={class:"flex items-center"},Go={class:"text-sm font-medium text-gray-900 dark:text-white mr-2"},qo={class:"w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2"},Jo={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Qo={key:0,class:"space-y-3 max-h-64 overflow-y-auto"},Ko={class:"text-sm text-gray-700 dark:text-gray-300 truncate"},Wo={class:"text-sm font-medium text-gray-900 dark:text-white"},Xo={key:1,class:"text-center py-8"},Yo={class:"flex justify-center"},Zo=["disabled"],ea={key:0,class:"animate-spin -ml-1 mr-3 h-4 w-4 text-white",fill:"none",viewBox:"0 0 24 24"},ta={key:1,class:"-ml-1 mr-3 h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},ra={key:3,class:"flex justify-center items-center h-64"},sa={key:4,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-12 text-center"},oa={__name:"AnalyticsDashboard",props:{analytics:{type:Object,default:null},loading:{type:Boolean,default:!1}},emits:["refresh"],setup(C,{emit:L}){const b=y=>({admin:"Admin",manager:"Manager",employee:"Dipendente"})[y]||y,x=y=>({full_time:"Tempo pieno",part_time:"Part-time",contractor:"Consulente",intern:"Stagista"})[y]||y,h=y=>y?new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(y):"€0";return(y,i)=>(o(),a("div",lo,[C.analytics?(o(),a("div",no,[e("div",io,[e("div",uo,[e("div",co,[i[2]||(i[2]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-6 w-6 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})])],-1)),e("div",mo,[e("dl",null,[i[1]||(i[1]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Dipendenti Totali ",-1)),e("dd",go,m(C.analytics.overview.total_users),1)])])])])]),e("div",po,[e("div",vo,[e("div",xo,[i[4]||(i[4]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-6 w-6 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})])],-1)),e("div",yo,[e("dl",null,[i[3]||(i[3]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Dipartimenti ",-1)),e("dd",bo,m(C.analytics.overview.total_departments),1)])])])])]),e("div",fo,[e("div",ko,[e("div",ho,[i[6]||(i[6]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-6 w-6 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"})])],-1)),e("div",wo,[e("dl",null,[i[5]||(i[5]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Assunzioni Recenti (90gg) ",-1)),e("dd",$o,m(C.analytics.overview.recent_hires),1)])])])])])])):k("",!0),C.analytics?(o(),a("div",_o,[e("div",Co,[i[7]||(i[7]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Distribuzione per Ruolo",-1)),e("div",Mo,[(o(!0),a(U,null,S(C.analytics.users_by_role,u=>(o(),a("div",{key:u.role,class:"flex items-center justify-between"},[e("div",zo,[e("div",{class:P(["w-3 h-3 rounded-full mr-3",u.role==="admin"?"bg-red-500":u.role==="manager"?"bg-blue-500":"bg-gray-500"])},null,2),e("span",jo,m(b(u.role)),1)]),e("div",Eo,[e("span",Bo,m(u.count),1),e("div",To,[e("div",{class:P(["h-2 rounded-full",u.role==="admin"?"bg-red-500":u.role==="manager"?"bg-blue-500":"bg-gray-500"]),style:W({width:`${u.count/C.analytics.overview.total_users*100}%`})},null,6)])])]))),128))])]),e("div",Ho,[i[9]||(i[9]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Distribuzione per Dipartimento",-1)),e("div",Vo,[(o(!0),a(U,null,S(C.analytics.users_by_department,u=>(o(),a("div",{key:u.department,class:"flex items-center justify-between"},[e("div",Do,[i[8]||(i[8]=e("div",{class:"w-3 h-3 rounded-full bg-indigo-500 mr-3"},null,-1)),e("span",Lo,m(u.department),1)]),e("div",Uo,[e("span",So,m(u.count),1),e("div",Po,[e("div",{class:"h-2 rounded-full bg-indigo-500",style:W({width:`${u.count/C.analytics.overview.total_users*100}%`})},null,4)])])]))),128))])])])):k("",!0),C.analytics?(o(),a("div",Ao,[e("div",Io,[i[11]||(i[11]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Tipi di Contratto",-1)),e("div",No,[(o(!0),a(U,null,S(C.analytics.employment_types,u=>(o(),a("div",{key:u.type,class:"flex items-center justify-between"},[e("div",Ro,[i[10]||(i[10]=e("div",{class:"w-3 h-3 rounded-full bg-green-500 mr-3"},null,-1)),e("span",Oo,m(x(u.type)),1)]),e("div",Fo,[e("span",Go,m(u.count),1),e("div",qo,[e("div",{class:"h-2 rounded-full bg-green-500",style:W({width:`${u.count/C.analytics.overview.total_users*100}%`})},null,4)])])]))),128))])]),e("div",Jo,[i[13]||(i[13]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Stipendio Medio per Dipartimento",-1)),C.analytics.avg_salary_by_department.length>0?(o(),a("div",Qo,[(o(!0),a(U,null,S(C.analytics.avg_salary_by_department,u=>(o(),a("div",{key:u.department,class:"flex items-center justify-between"},[e("span",Ko,m(u.department),1),e("span",Wo,m(h(u.avg_salary)),1)]))),128))])):(o(),a("div",Xo,i[12]||(i[12]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),e("p",{class:"mt-2 text-sm text-gray-500 dark:text-gray-400"},"Nessun dato stipendio disponibile",-1)])))])])):k("",!0),e("div",Yo,[e("button",{onClick:i[0]||(i[0]=u=>y.$emit("refresh")),disabled:C.loading,class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"},[C.loading?(o(),a("svg",ea,i[14]||(i[14]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):(o(),a("svg",ta,i[15]||(i[15]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"},null,-1)]))),i[16]||(i[16]=z(" Aggiorna Dati "))],8,Zo)]),C.loading&&!C.analytics?(o(),a("div",ra,i[17]||(i[17]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):k("",!0),!C.loading&&!C.analytics?(o(),a("div",sa,i[18]||(i[18]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun dato disponibile",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},' Clicca su "Aggiorna Dati" per caricare le analytics. ',-1)]))):k("",!0)]))}},aa=J(oa,[["__scopeId","data-v-ed21a1db"]]),la={class:"space-y-6"},na={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},ia={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},da={class:"space-y-3"},ua=["disabled"],ca={key:0,class:"animate-spin w-4 h-4 mr-2",fill:"none",viewBox:"0 0 24 24"},ma={key:1,class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},ga={class:"relative"},pa=["disabled"],va={key:0,class:"animate-spin w-4 h-4 mr-2",fill:"none",viewBox:"0 0 24 24"},xa={key:1,class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},ya={key:0,class:"mt-3 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg"},ba={class:"flex items-center justify-between mb-2"},fa={class:"text-sm text-blue-600 dark:text-blue-400"},ka={class:"w-full bg-blue-200 dark:bg-blue-800 rounded-full h-2"},ha={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},wa={class:"space-y-3"},$a=["disabled"],_a={key:0,class:"animate-spin w-4 h-4 mr-2",fill:"none",viewBox:"0 0 24 24"},Ca={key:1,class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Ma=["disabled"],za={key:0,class:"animate-spin w-4 h-4 mr-2",fill:"none",viewBox:"0 0 24 24"},ja={key:1,class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Ea={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Ba={class:"space-y-3"},Ta={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Ha={class:"space-y-3"},Va=["disabled"],Da={key:0,class:"animate-spin w-4 h-4 mr-2",fill:"none",viewBox:"0 0 24 24"},La={key:1,class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Ua={class:"flex"},Sa={key:0,class:"w-5 h-5 text-green-400 mr-2 mt-0.5",fill:"currentColor",viewBox:"0 0 20 20"},Pa={key:1,class:"w-5 h-5 text-red-400 mr-2 mt-0.5",fill:"currentColor",viewBox:"0 0 20 20"},Aa={key:0,class:"mt-2"},Ia={__name:"BulkOperations",emits:["operation-completed"],setup(C,{emit:L}){const b=L,x=f({template:!1,import:!1,export:!1,verify:!1}),h=f({show:!1,processed:0,total:0}),y=f(null),i=f(!1),u=f(!1),M=f(!1),$=f(!1),l=f(!1),t=f(!1),r=async()=>{x.value.template=!0;try{const c=["email","first_name","last_name","phone","department_id","hire_date","employment_type","salary","role","is_active"].join(",")+`
<EMAIL>,Mario,Rossi,+39 ************,1,2024-01-15,full_time,45000,employee,true
<EMAIL>,Giulia,Bianchi,+39 ************,2,2024-02-01,part_time,30000,manager,true`,v=new Blob([c],{type:"text/csv;charset=utf-8;"}),s=document.createElement("a"),d=URL.createObjectURL(v);s.setAttribute("href",d),s.setAttribute("download","template_dipendenti.csv"),s.style.visibility="hidden",document.body.appendChild(s),s.click(),document.body.removeChild(s),D("success","Template scaricato","Il template CSV è stato scaricato con successo.")}catch(T){console.error("Error downloading template:",T),D("error","Errore download","Impossibile scaricare il template.")}finally{x.value.template=!1}},w=async T=>{const c=T.target.files[0];if(c){x.value.import=!0,h.value.show=!0,h.value.processed=0;try{const v=new FormData;v.append("file",c);const s=await fetch("/api/personnel/import",{method:"POST",credentials:"include",body:v});if(!s.ok)throw new Error(`HTTP ${s.status}: ${s.statusText}`);const d=await s.json();if(d.success)D("success","Import completato",`Importati ${d.data.imported} dipendenti su ${d.data.total} righe processate.`,d.data.errors||[]),b("operation-completed","import");else throw new Error(d.message||"Errore durante l'import")}catch(v){console.error("Error importing file:",v),D("error","Errore import",v.message)}finally{x.value.import=!1,h.value.show=!1,T.target.value=""}}},B=async T=>{x.value.export=!0;try{const v=await fetch(T==="full"?"/api/personnel/export":"/api/personnel/export/contacts",{credentials:"include"});if(!v.ok)throw new Error(`HTTP ${v.status}: ${v.statusText}`);const s=v.headers.get("content-disposition");let d=`export_${T}_${new Date().toISOString().split("T")[0]}.csv`;if(s){const Q=s.match(/filename="(.+)"/);Q&&(d=Q[1])}const j=await v.blob(),H=window.URL.createObjectURL(j),N=document.createElement("a");N.href=H,N.download=d,document.body.appendChild(N),N.click(),document.body.removeChild(N),window.URL.revokeObjectURL(H),D("success","Export completato",`I dati sono stati esportati in ${d}`)}catch(c){console.error("Error exporting data:",c),D("error","Errore export",c.message)}finally{x.value.export=!1}},I=async()=>{x.value.verify=!0;try{const T=await fetch("/api/personnel/verify",{credentials:"include"});if(!T.ok)throw new Error(`HTTP ${T.status}: ${T.statusText}`);const c=await T.json();if(c.success){const v=c.data.issues||[];v.length===0?D("success","Verifica completata","Nessun problema di integrità rilevato."):D("error","Problemi rilevati",`Trovati ${v.length} problemi di integrità dati.`,v)}else throw new Error(c.message||"Errore durante la verifica")}catch(T){console.error("Error verifying data:",T),D("error","Errore verifica",T.message)}finally{x.value.verify=!1}},D=(T,c,v,s=[])=>{y.value={type:T,title:c,message:v,details:s},T==="success"&&setTimeout(()=>{y.value=null},5e3)};return(T,c)=>(o(),a("div",la,[c[34]||(c[34]=e("div",null,[e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Operazioni di Massa"),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"}," Import/Export dati e operazioni bulk ")],-1)),e("div",na,[e("div",ia,[c[15]||(c[15]=K('<div class="flex items-center mb-4"><svg class="w-6 h-6 text-green-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path></svg><h4 class="text-lg font-medium text-gray-900 dark:text-white">Import Dipendenti</h4></div><p class="text-sm text-gray-500 dark:text-gray-400 mb-4"> Importa dipendenti da file CSV con dati contrattuali completi </p>',2)),e("div",da,[e("button",{onClick:r,disabled:x.value.template,class:"w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"},[x.value.template?(o(),a("svg",ca,c[10]||(c[10]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):(o(),a("svg",ma,c[11]||(c[11]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"},null,-1)]))),z(" "+m(x.value.template?"Generando...":"Scarica Template CSV"),1)],8,ua),e("div",ga,[e("input",{ref:"fileInput",type:"file",accept:".csv",onChange:w,class:"hidden"},null,544),e("button",{onClick:c[0]||(c[0]=v=>T.$refs.fileInput.click()),disabled:x.value.import,class:"w-full inline-flex justify-center items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 disabled:opacity-50"},[x.value.import?(o(),a("svg",va,c[12]||(c[12]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):(o(),a("svg",xa,c[13]||(c[13]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"},null,-1)]))),z(" "+m(x.value.import?"Importando...":"Carica File CSV"),1)],8,pa)]),h.value.show?(o(),a("div",ya,[e("div",ba,[c[14]||(c[14]=e("span",{class:"text-sm font-medium text-blue-800 dark:text-blue-200"},"Import in corso...",-1)),e("span",fa,m(h.value.processed)+"/"+m(h.value.total),1)]),e("div",ka,[e("div",{class:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:W({width:`${h.value.processed/h.value.total*100}%`})},null,4)])])):k("",!0)])]),e("div",ha,[c[21]||(c[21]=K('<div class="flex items-center mb-4"><svg class="w-6 h-6 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path></svg><h4 class="text-lg font-medium text-gray-900 dark:text-white">Export Dati</h4></div><p class="text-sm text-gray-500 dark:text-gray-400 mb-4"> Esporta dati del personale in vari formati </p>',2)),e("div",wa,[e("button",{onClick:c[1]||(c[1]=v=>B("full")),disabled:x.value.export,class:"w-full inline-flex justify-center items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"},[x.value.export?(o(),a("svg",_a,c[16]||(c[16]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):(o(),a("svg",Ca,c[17]||(c[17]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"},null,-1)]))),z(" "+m(x.value.export?"Esportando...":"Export Completo CSV"),1)],8,$a),e("button",{onClick:c[2]||(c[2]=v=>B("contacts")),disabled:x.value.export,class:"w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"},[x.value.export?(o(),a("svg",za,c[18]||(c[18]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):(o(),a("svg",ja,c[19]||(c[19]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"},null,-1)]))),z(" "+m(x.value.export?"Esportando...":"Export Solo Contatti"),1)],8,Ma),e("button",{onClick:c[3]||(c[3]=v=>i.value=!0),class:"w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"},c[20]||(c[20]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"})],-1),z(" Export Personalizzato ")]))])]),e("div",Ea,[c[25]||(c[25]=K('<div class="flex items-center mb-4"><svg class="w-6 h-6 text-purple-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path></svg><h4 class="text-lg font-medium text-gray-900 dark:text-white">Aggiornamenti di Massa</h4></div><p class="text-sm text-gray-500 dark:text-gray-400 mb-4"> Applica modifiche a più dipendenti contemporaneamente </p>',2)),e("div",Ba,[e("button",{onClick:c[4]||(c[4]=v=>u.value=!0),class:"w-full inline-flex justify-center items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700"},c[22]||(c[22]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})],-1),z(" Assegnazione Dipartimenti ")])),e("button",{onClick:c[5]||(c[5]=v=>M.value=!0),class:"w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"},c[23]||(c[23]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})],-1),z(" Assegnazione Competenze ")])),e("button",{onClick:c[6]||(c[6]=v=>$.value=!0),class:"w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"},c[24]||(c[24]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"})],-1),z(" Modifica Ruoli ")]))])]),e("div",Ta,[c[30]||(c[30]=K('<div class="flex items-center mb-4"><svg class="w-6 h-6 text-red-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path></svg><h4 class="text-lg font-medium text-gray-900 dark:text-white">Pulizia Dati</h4></div><p class="text-sm text-gray-500 dark:text-gray-400 mb-4"> Strumenti per la manutenzione e pulizia dei dati </p>',2)),e("div",Ha,[e("button",{onClick:I,disabled:x.value.verify,class:"w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"},[x.value.verify?(o(),a("svg",Da,c[26]||(c[26]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):(o(),a("svg",La,c[27]||(c[27]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"},null,-1)]))),z(" "+m(x.value.verify?"Verificando...":"Verifica Integrità Dati"),1)],8,Va),e("button",{onClick:c[7]||(c[7]=v=>l.value=!0),class:"w-full inline-flex justify-center items-center px-4 py-2 border border-red-300 dark:border-red-600 shadow-sm text-sm font-medium rounded-md text-red-700 dark:text-red-300 bg-white dark:bg-gray-800 hover:bg-red-50 dark:hover:bg-red-900/20"},c[28]||(c[28]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1),z(" Rimuovi Utenti Inattivi ")])),e("button",{onClick:c[8]||(c[8]=v=>t.value=!0),class:"w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"},c[29]||(c[29]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})],-1),z(" Pulizia Avanzata ")]))])])]),y.value?(o(),a("div",{key:0,class:P(["mt-6 p-4 rounded-lg",y.value.type==="success"?"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800":"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800"])},[e("div",Ua,[y.value.type==="success"?(o(),a("svg",Sa,c[31]||(c[31]=[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"},null,-1)]))):(o(),a("svg",Pa,c[32]||(c[32]=[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z","clip-rule":"evenodd"},null,-1)]))),e("div",null,[e("h3",{class:P(["text-sm font-medium",y.value.type==="success"?"text-green-800 dark:text-green-200":"text-red-800 dark:text-red-200"])},m(y.value.title),3),e("p",{class:P(["text-sm mt-1",y.value.type==="success"?"text-green-700 dark:text-green-300":"text-red-700 dark:text-red-300"])},m(y.value.message),3),y.value.details&&y.value.details.length>0?(o(),a("div",Aa,[e("ul",{class:P(["text-xs space-y-1",y.value.type==="success"?"text-green-600 dark:text-green-400":"text-red-600 dark:text-red-400"])},[(o(!0),a(U,null,S(y.value.details,v=>(o(),a("li",{key:v},"• "+m(v),1))),128))],2)])):k("",!0)]),e("button",{onClick:c[9]||(c[9]=v=>y.value=null),class:"ml-auto"},[(o(),a("svg",{class:P(["w-4 h-4",y.value.type==="success"?"text-green-400":"text-red-400"]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},c[33]||(c[33]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"},null,-1)]),2))])])],2)):k("",!0)]))}},Na={class:"flex items-center justify-between pb-4 border-b border-gray-200 dark:border-gray-700"},Ra={class:"mt-6"},Oa={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Fa={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Ga=["value"],qa={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Ja={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Qa={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},Ka={class:"flex items-center space-x-4"},Wa={class:"flex items-center"},Xa={key:0,class:"mt-3"},Ya={key:0,class:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4"},Za={class:"flex"},el={class:"text-sm text-red-700 dark:text-red-300 mt-1"},tl={class:"flex items-center justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700"},rl=["disabled"],sl={key:0,class:"animate-spin -ml-1 mr-3 h-4 w-4 text-white inline",fill:"none",viewBox:"0 0 24 24"},ol={__name:"CreateUserModal",emits:["close","user-created"],setup(C,{emit:L}){const b=L,x=f(!1),h=f(null),y=f([]),i=f(!0),u=f({first_name:"",last_name:"",username:"",email:"",phone:"",position:"",role:"employee",department_id:"",hire_date:"",employment_type:"full_time",work_location:"",weekly_hours:40,probation_end_date:"",contract_end_date:"",salary:null,salary_currency:"EUR",emergency_contact_name:"",emergency_contact_phone:"",emergency_contact_relationship:"",password:""}),M=async()=>{try{const l=await fetch("/api/personnel/departments",{credentials:"include"});if(!l.ok)throw new Error(`HTTP ${l.status}: ${l.statusText}`);const t=await l.json();t.success&&(y.value=t.data.departments||[])}catch(l){console.error("Error loading departments:",l)}},$=async()=>{x.value=!0,h.value=null;try{const l={...u.value};i.value&&delete l.password,Object.keys(l).forEach(w=>{l[w]===""&&(l[w]=null)});const t=await fetch("/api/personnel/admin/users",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(l)});if(!t.ok)throw new Error(`HTTP ${t.status}: ${t.statusText}`);const r=await t.json();if(r.success)b("user-created",r.data.user),i.value&&r.data.temporary_password&&alert(`Utente creato con successo!
Password temporanea: ${r.data.temporary_password}`);else throw new Error(r.message||"Errore nella creazione utente")}catch(l){console.error("Error creating user:",l),h.value=l.message}finally{x.value=!1}};return F(()=>{M();const l=new Date().toISOString().split("T")[0];u.value.hire_date=l}),(l,t)=>(o(),a("div",{class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:t[24]||(t[24]=r=>l.$emit("close"))},[e("div",{class:"relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:t[23]||(t[23]=q(()=>{},["stop"]))},[e("div",Na,[t[26]||(t[26]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Crea Nuovo Dipendente ",-1)),e("button",{onClick:t[0]||(t[0]=r=>l.$emit("close")),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},t[25]||(t[25]=[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),e("div",Ra,[e("form",{onSubmit:q($,["prevent"]),class:"space-y-6"},[e("div",null,[t[33]||(t[33]=e("h4",{class:"text-md font-medium text-gray-900 dark:text-white mb-4"},"Informazioni Base",-1)),e("div",Oa,[e("div",null,[t[27]||(t[27]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Nome * ",-1)),_(e("input",{"onUpdate:modelValue":t[1]||(t[1]=r=>u.value.first_name=r),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[V,u.value.first_name]])]),e("div",null,[t[28]||(t[28]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Cognome * ",-1)),_(e("input",{"onUpdate:modelValue":t[2]||(t[2]=r=>u.value.last_name=r),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[V,u.value.last_name]])]),e("div",null,[t[29]||(t[29]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Username * ",-1)),_(e("input",{"onUpdate:modelValue":t[3]||(t[3]=r=>u.value.username=r),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[V,u.value.username]])]),e("div",null,[t[30]||(t[30]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Email * ",-1)),_(e("input",{"onUpdate:modelValue":t[4]||(t[4]=r=>u.value.email=r),type:"email",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[V,u.value.email]])]),e("div",null,[t[31]||(t[31]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Telefono ",-1)),_(e("input",{"onUpdate:modelValue":t[5]||(t[5]=r=>u.value.phone=r),type:"tel",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[V,u.value.phone]])]),e("div",null,[t[32]||(t[32]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Posizione ",-1)),_(e("input",{"onUpdate:modelValue":t[6]||(t[6]=r=>u.value.position=r),type:"text",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[V,u.value.position]])])])]),e("div",null,[t[38]||(t[38]=e("h4",{class:"text-md font-medium text-gray-900 dark:text-white mb-4"},"Ruolo e Dipartimento",-1)),e("div",Fa,[e("div",null,[t[35]||(t[35]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Ruolo ",-1)),_(e("select",{"onUpdate:modelValue":t[7]||(t[7]=r=>u.value.role=r),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},t[34]||(t[34]=[e("option",{value:"employee"},"Dipendente",-1),e("option",{value:"manager"},"Manager",-1),e("option",{value:"admin"},"Admin",-1)]),512),[[A,u.value.role]])]),e("div",null,[t[37]||(t[37]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Dipartimento ",-1)),_(e("select",{"onUpdate:modelValue":t[8]||(t[8]=r=>u.value.department_id=r),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},[t[36]||(t[36]=e("option",{value:""},"Seleziona dipartimento",-1)),(o(!0),a(U,null,S(y.value,r=>(o(),a("option",{key:r.id,value:r.id},m(r.name),9,Ga))),128))],512),[[A,u.value.department_id]])])])]),e("div",null,[t[47]||(t[47]=e("h4",{class:"text-md font-medium text-gray-900 dark:text-white mb-4"},"Informazioni Contrattuali",-1)),e("div",qa,[e("div",null,[t[39]||(t[39]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Data Assunzione ",-1)),_(e("input",{"onUpdate:modelValue":t[9]||(t[9]=r=>u.value.hire_date=r),type:"date",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[V,u.value.hire_date]])]),e("div",null,[t[41]||(t[41]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Tipo Contratto ",-1)),_(e("select",{"onUpdate:modelValue":t[10]||(t[10]=r=>u.value.employment_type=r),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},t[40]||(t[40]=[e("option",{value:"full_time"},"Tempo pieno",-1),e("option",{value:"part_time"},"Part-time",-1),e("option",{value:"contractor"},"Consulente",-1),e("option",{value:"intern"},"Stagista",-1)]),512),[[A,u.value.employment_type]])]),e("div",null,[t[43]||(t[43]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Modalità Lavoro ",-1)),_(e("select",{"onUpdate:modelValue":t[11]||(t[11]=r=>u.value.work_location=r),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},t[42]||(t[42]=[e("option",{value:""},"Seleziona modalità",-1),e("option",{value:"office"},"Ufficio",-1),e("option",{value:"remote"},"Remoto",-1),e("option",{value:"hybrid"},"Ibrido",-1)]),512),[[A,u.value.work_location]])]),e("div",null,[t[44]||(t[44]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Ore Settimanali ",-1)),_(e("input",{"onUpdate:modelValue":t[12]||(t[12]=r=>u.value.weekly_hours=r),type:"number",step:"0.5",min:"0",max:"60",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[V,u.value.weekly_hours,void 0,{number:!0}]])]),e("div",null,[t[45]||(t[45]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Fine Periodo Prova ",-1)),_(e("input",{"onUpdate:modelValue":t[13]||(t[13]=r=>u.value.probation_end_date=r),type:"date",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[V,u.value.probation_end_date]])]),e("div",null,[t[46]||(t[46]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Scadenza Contratto ",-1)),_(e("input",{"onUpdate:modelValue":t[14]||(t[14]=r=>u.value.contract_end_date=r),type:"date",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[V,u.value.contract_end_date]])])])]),e("div",null,[t[51]||(t[51]=e("h4",{class:"text-md font-medium text-gray-900 dark:text-white mb-4"},"Informazioni Economiche",-1)),e("div",Ja,[e("div",null,[t[48]||(t[48]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Stipendio Annuo ",-1)),_(e("input",{"onUpdate:modelValue":t[15]||(t[15]=r=>u.value.salary=r),type:"number",step:"100",min:"0",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[V,u.value.salary,void 0,{number:!0}]])]),e("div",null,[t[50]||(t[50]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Valuta ",-1)),_(e("select",{"onUpdate:modelValue":t[16]||(t[16]=r=>u.value.salary_currency=r),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},t[49]||(t[49]=[e("option",{value:"EUR"},"EUR",-1),e("option",{value:"USD"},"USD",-1),e("option",{value:"GBP"},"GBP",-1)]),512),[[A,u.value.salary_currency]])])])]),e("div",null,[t[55]||(t[55]=e("h4",{class:"text-md font-medium text-gray-900 dark:text-white mb-4"},"Contatto di Emergenza",-1)),e("div",Qa,[e("div",null,[t[52]||(t[52]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Nome Contatto ",-1)),_(e("input",{"onUpdate:modelValue":t[17]||(t[17]=r=>u.value.emergency_contact_name=r),type:"text",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[V,u.value.emergency_contact_name]])]),e("div",null,[t[53]||(t[53]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Telefono Emergenza ",-1)),_(e("input",{"onUpdate:modelValue":t[18]||(t[18]=r=>u.value.emergency_contact_phone=r),type:"tel",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[V,u.value.emergency_contact_phone]])]),e("div",null,[t[54]||(t[54]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Relazione ",-1)),_(e("input",{"onUpdate:modelValue":t[19]||(t[19]=r=>u.value.emergency_contact_relationship=r),type:"text",placeholder:"es. Coniuge, Genitore...",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[V,u.value.emergency_contact_relationship]])])])]),e("div",null,[t[58]||(t[58]=e("h4",{class:"text-md font-medium text-gray-900 dark:text-white mb-4"},"Password",-1)),e("div",Ka,[e("label",Wa,[_(e("input",{"onUpdate:modelValue":t[20]||(t[20]=r=>i.value=r),type:"checkbox",class:"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"},null,512),[[ce,i.value]]),t[56]||(t[56]=e("span",{class:"ml-2 text-sm text-gray-700 dark:text-gray-300"}," Genera password temporanea automaticamente ",-1))])]),i.value?k("",!0):(o(),a("div",Xa,[t[57]||(t[57]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Password Personalizzata ",-1)),_(e("input",{"onUpdate:modelValue":t[21]||(t[21]=r=>u.value.password=r),type:"password",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[V,u.value.password]])]))]),h.value?(o(),a("div",Ya,[e("div",Za,[t[60]||(t[60]=e("svg",{class:"w-5 h-5 text-red-400 mr-2 mt-0.5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z","clip-rule":"evenodd"})],-1)),e("div",null,[t[59]||(t[59]=e("h3",{class:"text-sm font-medium text-red-800 dark:text-red-200"},"Errore nella creazione",-1)),e("p",el,m(h.value),1)])])])):k("",!0),e("div",tl,[e("button",{type:"button",onClick:t[22]||(t[22]=r=>l.$emit("close")),class:"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"}," Annulla "),e("button",{type:"submit",disabled:x.value,class:"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"},[x.value?(o(),a("svg",sl,t[61]||(t[61]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):k("",!0),z(" "+m(x.value?"Creazione...":"Crea Dipendente"),1)],8,rl)])],32)])])]))}},al=J(ol,[["__scopeId","data-v-86e643b3"]]),ll={class:"space-y-6"},nl={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},il={class:"mt-4 sm:mt-0 flex items-center space-x-3"},dl=["disabled"],ul={key:0,class:"space-y-4"},cl={key:0,class:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4"},ml={class:"flex"},gl={class:"flex-1"},pl={class:"mt-2 text-sm text-red-700 dark:text-red-300"},vl={class:"mb-2"},xl={class:"list-disc list-inside space-y-1"},yl={key:0,class:"mt-2 text-xs"},bl={key:1,class:"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4"},fl={class:"flex"},kl={class:"flex-1"},hl={class:"mt-2 text-sm text-yellow-700 dark:text-yellow-300"},wl={class:"mb-2"},$l={class:"list-disc list-inside space-y-1"},_l={key:0,class:"mt-2 text-xs"},Cl={class:"border-b border-gray-200 dark:border-gray-700"},Ml={class:"-mb-px flex space-x-8"},zl=["onClick"],jl={key:0,class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},El={key:1,class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},Bl={key:2,class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},Tl={key:3,class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},Hl={key:4,class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},Vl={class:"mt-6"},Dl={key:0},Ll={key:1},Ul={key:2},Sl={key:3},Pl={key:4},Al={__name:"PersonnelAdmin",setup(C){const L=f(!1),b=f(null),x=f("users"),h=f(!1),y=f([{id:"users",name:"Gestione Utenti"},{id:"departments",name:"Dipartimenti"},{id:"skills",name:"Competenze"},{id:"analytics",name:"Analytics"},{id:"bulk",name:"Operazioni Bulk"}]),i=async()=>{L.value=!0;try{const l=await fetch("/api/personnel/admin/analytics",{credentials:"include"});if(!l.ok)throw new Error(`HTTP ${l.status}: ${l.statusText}`);const t=await l.json();if(t.success)b.value=t.data;else throw new Error(t.message||"Errore nel caricamento analytics")}catch(l){console.error("Error loading analytics:",l)}finally{L.value=!1}},u=async()=>{try{const l=await fetch("/api/personnel/export",{credentials:"include"});if(!l.ok)throw new Error(`HTTP ${l.status}: ${l.statusText}`);const t=await l.blob(),r=window.URL.createObjectURL(t),w=document.createElement("a");w.href=r,w.download=`personnel-export-${new Date().toISOString().split("T")[0]}.csv`,document.body.appendChild(w),w.click(),document.body.removeChild(w),window.URL.revokeObjectURL(r)}catch(l){console.error("Error exporting data:",l)}},M=l=>{h.value=!1,i()},$=l=>l?new Date(l).toLocaleDateString("it-IT",{year:"numeric",month:"short",day:"numeric"}):"";return F(()=>{i()}),(l,t)=>(o(),a("div",ll,[e("div",nl,[t[4]||(t[4]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white flex items-center"},[e("svg",{class:"w-8 h-8 mr-3 text-purple-600",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z","clip-rule":"evenodd"})]),z(" Amministrazione Personnel ")]),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"}," Gestione completa del personale e dati contrattuali ")],-1)),e("div",il,[e("button",{onClick:t[0]||(t[0]=r=>h.value=!0),class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},t[2]||(t[2]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),z(" Nuovo Dipendente ")])),e("button",{onClick:u,disabled:L.value,class:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"},t[3]||(t[3]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),z(" Esporta Dati ")]),8,dl)])]),b.value&&(b.value.alerts.expiring_contracts.length>0||b.value.alerts.ending_probation.length>0)?(o(),a("div",ul,[b.value.alerts.expiring_contracts.length>0?(o(),a("div",cl,[e("div",ml,[t[6]||(t[6]=e("svg",{class:"w-5 h-5 text-red-400 mr-3 mt-0.5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z","clip-rule":"evenodd"})],-1)),e("div",gl,[t[5]||(t[5]=e("h3",{class:"text-sm font-medium text-red-800 dark:text-red-200"},"Contratti in Scadenza",-1)),e("div",pl,[e("p",vl,m(b.value.alerts.expiring_contracts.length)+" contratti scadranno nei prossimi 90 giorni:",1),e("ul",xl,[(o(!0),a(U,null,S(b.value.alerts.expiring_contracts.slice(0,3),r=>(o(),a("li",{key:r.user_id},[e("strong",null,m(r.full_name),1),z(" - "+m($(r.contract_end_date))+" ("+m(r.days_remaining)+" giorni) ",1)]))),128))]),b.value.alerts.expiring_contracts.length>3?(o(),a("p",yl," +"+m(b.value.alerts.expiring_contracts.length-3)+" altri contratti ",1)):k("",!0)])])])])):k("",!0),b.value.alerts.ending_probation.length>0?(o(),a("div",bl,[e("div",fl,[t[8]||(t[8]=e("svg",{class:"w-5 h-5 text-yellow-400 mr-3 mt-0.5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z","clip-rule":"evenodd"})],-1)),e("div",kl,[t[7]||(t[7]=e("h3",{class:"text-sm font-medium text-yellow-800 dark:text-yellow-200"},"Periodi di Prova in Scadenza",-1)),e("div",hl,[e("p",wl,m(b.value.alerts.ending_probation.length)+" periodi di prova termineranno nei prossimi 30 giorni:",1),e("ul",$l,[(o(!0),a(U,null,S(b.value.alerts.ending_probation.slice(0,3),r=>(o(),a("li",{key:r.user_id},[e("strong",null,m(r.full_name),1),z(" - "+m($(r.probation_end_date))+" ("+m(r.days_remaining)+" giorni) ",1)]))),128))]),b.value.alerts.ending_probation.length>3?(o(),a("p",_l," +"+m(b.value.alerts.ending_probation.length-3)+" altri periodi di prova ",1)):k("",!0)])])])])):k("",!0)])):k("",!0),e("div",Cl,[e("nav",Ml,[(o(!0),a(U,null,S(y.value,r=>(o(),a("button",{key:r.id,onClick:w=>x.value=r.id,class:P(["py-2 px-1 border-b-2 font-medium text-sm flex items-center",x.value===r.id?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600"])},[r.id==="users"?(o(),a("svg",jl,t[9]||(t[9]=[e("path",{d:"M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"},null,-1)]))):r.id==="departments"?(o(),a("svg",El,t[10]||(t[10]=[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z","clip-rule":"evenodd"},null,-1)]))):r.id==="skills"?(o(),a("svg",Bl,t[11]||(t[11]=[e("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"},null,-1)]))):r.id==="analytics"?(o(),a("svg",Tl,t[12]||(t[12]=[e("path",{d:"M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"},null,-1)]))):(o(),a("svg",Hl,t[13]||(t[13]=[e("path",{"fill-rule":"evenodd",d:"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z","clip-rule":"evenodd"},null,-1)]))),z(" "+m(r.name),1)],10,zl))),128))])]),e("div",Vl,[x.value==="users"?(o(),a("div",Dl,[O(Bt,{onUserCreated:i,onUserUpdated:i,onUserDeleted:i})])):x.value==="departments"?(o(),a("div",Ll,[O(Xr,{onDepartmentCreated:i,onDepartmentUpdated:i,onDepartmentDeleted:i})])):x.value==="skills"?(o(),a("div",Ul,[O(ao,{onSkillCreated:i,onSkillUpdated:i,onSkillDeleted:i})])):x.value==="analytics"?(o(),a("div",Sl,[O(aa,{analytics:b.value,loading:L.value,onRefresh:i},null,8,["analytics","loading"])])):x.value==="bulk"?(o(),a("div",Pl,[O(Ia,{onOperationCompleted:i})])):k("",!0)]),h.value?(o(),Z(al,{key:1,onClose:t[1]||(t[1]=r=>h.value=!1),onUserCreated:M})):k("",!0)]))}},Rl=J(Al,[["__scopeId","data-v-2701ad16"]]);export{Rl as default};

{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/personnel/PersonnelProfile.vue"}, "originalCode": "<template>\n  <div class=\"personnel-profile\">\n    <!-- Loading State -->\n    <div v-if=\"loading\" class=\"flex justify-center items-center h-64\">\n      <div class=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n    </div>\n\n    <!-- Error State -->\n    <div v-else-if=\"error\" class=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6\">\n      <div class=\"flex\">\n        <svg class=\"w-5 h-5 text-red-400 mr-2 mt-0.5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n          <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clip-rule=\"evenodd\"></path>\n        </svg>\n        <div>\n          <h3 class=\"text-sm font-medium text-red-800 dark:text-red-200\">Errore nel caricamento del profilo</h3>\n          <p class=\"text-sm text-red-700 dark:text-red-300 mt-1\">{{ error }}</p>\n        </div>\n      </div>\n    </div>\n\n    <!-- Profile Content -->\n    <div v-else-if=\"user\" class=\"space-y-6\">\n      <!-- Header Section -->\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden\">\n        <div class=\"bg-gradient-to-r from-blue-500 to-purple-600 px-6 py-8\">\n          <div class=\"flex items-center space-x-6\">\n            <!-- Avatar -->\n            <div class=\"flex-shrink-0\">\n              <div class=\"w-24 h-24 bg-white rounded-full flex items-center justify-center shadow-lg\">\n                <img v-if=\"user.profile_image\"\n                     :src=\"user.profile_image\"\n                     :alt=\"user.full_name\"\n                     class=\"w-24 h-24 rounded-full object-cover\">\n                <div v-else class=\"w-24 h-24 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center\">\n                  <svg class=\"w-12 h-12 text-gray-400 dark:text-gray-300\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fill-rule=\"evenodd\" d=\"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\" clip-rule=\"evenodd\"></path>\n                  </svg>\n                </div>\n              </div>\n            </div>\n\n            <!-- User Info -->\n            <div class=\"flex-1 text-white\">\n              <h1 class=\"text-3xl font-bold\">{{ user.full_name }}</h1>\n              <p class=\"text-blue-100 text-lg\">{{ user.position || 'Posizione non specificata' }}</p>\n              <div class=\"flex items-center space-x-4 mt-2\">\n                <span v-if=\"user.department\" class=\"inline-flex items-center px-3 py-1 rounded-full text-sm bg-white/20 text-white\">\n                  <svg class=\"w-4 h-4 mr-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fill-rule=\"evenodd\" d=\"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z\" clip-rule=\"evenodd\"></path>\n                  </svg>\n                  {{ user.department.name }}\n                </span>\n                <span class=\"inline-flex items-center px-3 py-1 rounded-full text-sm bg-white/20 text-white\">\n                  <svg class=\"w-4 h-4 mr-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fill-rule=\"evenodd\" d=\"M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z\" clip-rule=\"evenodd\"></path>\n                  </svg>\n                  {{ user.role }}\n                </span>\n              </div>\n            </div>\n\n            <!-- Actions -->\n            <div class=\"flex-shrink-0\">\n              <button v-if=\"canEdit\"\n                      @click=\"editMode = !editMode\"\n                      class=\"bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-colors duration-200\">\n                <svg class=\"w-5 h-5 inline mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path d=\"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z\"></path>\n                </svg>\n                {{ editMode ? 'Annulla' : 'Modifica' }}\n              </button>\n            </div>\n          </div>\n        </div>\n\n        <!-- Profile Completion Bar -->\n        <div v-if=\"user.profile && user.profile.profile_completion !== undefined\" class=\"px-6 py-4 bg-gray-50 dark:bg-gray-700\">\n          <div class=\"flex items-center justify-between mb-2\">\n            <span class=\"text-sm font-medium text-gray-700 dark:text-gray-300\">Completamento Profilo</span>\n            <span class=\"text-sm text-gray-500 dark:text-gray-400\">{{ user.profile.profile_completion }}%</span>\n          </div>\n          <div class=\"w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2\">\n            <div class=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\n                 :style=\"{ width: user.profile.profile_completion + '%' }\"></div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Personal Information Cards - Horizontal Layout -->\n      <div class=\"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 mb-6\">\n          <!-- Contact Information -->\n          <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n            <div class=\"flex items-center justify-between mb-4\">\n              <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">Informazioni di Contatto</h3>\n              <button v-if=\"canEdit && !editMode\"\n                      @click=\"editMode = true\"\n                      class=\"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300\">\n                <svg class=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path d=\"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z\"></path>\n                </svg>\n              </button>\n            </div>\n\n            <!-- View Mode -->\n            <div v-if=\"!editMode\" class=\"space-y-3\">\n              <div class=\"flex items-center\">\n                <svg class=\"w-5 h-5 text-gray-400 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path d=\"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z\"></path>\n                  <path d=\"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z\"></path>\n                </svg>\n                <span class=\"text-gray-900 dark:text-white\">{{ user.email }}</span>\n              </div>\n              <div v-if=\"user.phone\" class=\"flex items-center\">\n                <svg class=\"w-5 h-5 text-gray-400 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path d=\"M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z\"></path>\n                </svg>\n                <span class=\"text-gray-900 dark:text-white\">{{ user.phone }}</span>\n              </div>\n              <div v-if=\"user.hire_date\" class=\"flex items-center\">\n                <svg class=\"w-5 h-5 text-gray-400 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fill-rule=\"evenodd\" d=\"M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z\" clip-rule=\"evenodd\"></path>\n                </svg>\n                <span class=\"text-gray-900 dark:text-white\">Assunto il {{ formatDate(user.hire_date) }}</span>\n              </div>\n            </div>\n\n            <!-- Edit Mode -->\n            <div v-else class=\"space-y-4\">\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Telefono</label>\n                <input v-model=\"editForm.phone\"\n                       type=\"tel\"\n                       class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\">\n              </div>\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Bio</label>\n                <textarea v-model=\"editForm.bio\"\n                          rows=\"3\"\n                          class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"></textarea>\n              </div>\n              <div class=\"flex space-x-2\">\n                <button @click=\"saveProfile\"\n                        :disabled=\"saving\"\n                        class=\"flex-1 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-4 py-2 rounded-md transition-colors duration-200\">\n                  {{ saving ? 'Salvataggio...' : 'Salva' }}\n                </button>\n                <button @click=\"cancelEdit\"\n                        class=\"flex-1 bg-gray-300 hover:bg-gray-400 dark:bg-gray-600 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-200 px-4 py-2 rounded-md transition-colors duration-200\">\n                  Annulla\n                </button>\n              </div>\n            </div>\n          </div>\n\n        <!-- Skills Overview -->\n        <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">Competenze Principali</h3>\n          <div v-if=\"user.skills && user.skills.length > 0\" class=\"space-y-3\">\n            <div v-for=\"skill in user.skills.slice(0, 4)\" :key=\"skill.id\" class=\"flex items-center justify-between\">\n              <span class=\"text-sm font-medium text-gray-900 dark:text-white\">{{ skill.name }}</span>\n              <div class=\"flex items-center space-x-2\">\n                <div class=\"flex space-x-1\">\n                  <div v-for=\"i in 5\" :key=\"i\"\n                       class=\"w-2 h-2 rounded-full\"\n                       :class=\"i <= skill.proficiency_level ? 'bg-blue-500' : 'bg-gray-300 dark:bg-gray-600'\"></div>\n                </div>\n                <span v-if=\"skill.certified\" class=\"text-xs text-green-600 dark:text-green-400\">✓</span>\n              </div>\n            </div>\n            <div v-if=\"user.skills.length > 4\" class=\"text-sm text-gray-500 dark:text-gray-400 text-center pt-2\">\n              +{{ user.skills.length - 4 }} altre competenze\n            </div>\n          </div>\n          <div v-else class=\"text-gray-500 dark:text-gray-400 text-sm\">\n            Nessuna competenza registrata\n          </div>\n        </div>\n\n          <!-- HR Information -->\n          <div v-if=\"user.profile\" class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n            <div class=\"flex items-center justify-between mb-4\">\n              <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">Informazioni HR</h3>\n              <button v-if=\"canEdit && !editMode\"\n                      @click=\"editMode = true\"\n                      class=\"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300\">\n                <svg class=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path d=\"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z\"></path>\n                </svg>\n              </button>\n            </div>\n\n            <!-- View Mode -->\n            <div v-if=\"!editMode\" class=\"space-y-3\">\n              <div v-if=\"user.profile.employee_id\" class=\"flex justify-between\">\n                <span class=\"text-sm text-gray-500 dark:text-gray-400\">ID Dipendente:</span>\n                <span class=\"text-sm text-gray-900 dark:text-white\">{{ user.profile.employee_id }}</span>\n              </div>\n              <div v-if=\"user.profile.job_title\" class=\"flex justify-between\">\n                <span class=\"text-sm text-gray-500 dark:text-gray-400\">Titolo:</span>\n                <span class=\"text-sm text-gray-900 dark:text-white\">{{ user.profile.job_title }}</span>\n              </div>\n              <div v-if=\"user.profile.employment_type\" class=\"flex justify-between\">\n                <span class=\"text-sm text-gray-500 dark:text-gray-400\">Tipo Contratto:</span>\n                <span class=\"text-sm text-gray-900 dark:text-white\">{{ getEmploymentTypeLabel(user.profile.employment_type) }}</span>\n              </div>\n              <div v-if=\"user.profile.work_location\" class=\"flex justify-between\">\n                <span class=\"text-sm text-gray-500 dark:text-gray-400\">Modalità Lavoro:</span>\n                <span class=\"text-sm text-gray-900 dark:text-white\">{{ getWorkLocationLabel(user.profile.work_location) }}</span>\n              </div>\n              <div v-if=\"user.profile.weekly_hours\" class=\"flex justify-between\">\n                <span class=\"text-sm text-gray-500 dark:text-gray-400\">Ore Settimanali:</span>\n                <span class=\"text-sm text-gray-900 dark:text-white\">{{ user.profile.weekly_hours }}h</span>\n              </div>\n            </div>\n\n            <!-- Edit Mode HR -->\n            <div v-else class=\"space-y-4\">\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">ID Dipendente</label>\n                <input v-model=\"editForm.employee_id\"\n                       type=\"text\"\n                       class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\">\n              </div>\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Titolo Lavoro</label>\n                <input v-model=\"editForm.job_title\"\n                       type=\"text\"\n                       class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\">\n              </div>\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Tipo Contratto</label>\n                <select v-model=\"editForm.employment_type\"\n                        class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\">\n                  <option value=\"\">Seleziona tipo</option>\n                  <option value=\"full_time\">Tempo Pieno</option>\n                  <option value=\"part_time\">Part Time</option>\n                  <option value=\"contractor\">Consulente</option>\n                  <option value=\"intern\">Stagista</option>\n                </select>\n              </div>\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Modalità Lavoro</label>\n                <select v-model=\"editForm.work_location\"\n                        class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\">\n                  <option value=\"\">Seleziona modalità</option>\n                  <option value=\"office\">Ufficio</option>\n                  <option value=\"remote\">Remoto</option>\n                  <option value=\"hybrid\">Ibrido</option>\n                </select>\n              </div>\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Ore Settimanali</label>\n                <input v-model=\"editForm.weekly_hours\"\n                       type=\"number\"\n                       min=\"1\"\n                       max=\"60\"\n                       step=\"0.5\"\n                       class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\">\n              </div>\n\n              <!-- Emergency Contact Section -->\n              <div class=\"border-t border-gray-200 dark:border-gray-600 pt-4 mt-4\">\n                <h4 class=\"text-sm font-medium text-gray-900 dark:text-white mb-3\">Contatto di Emergenza</h4>\n                <div class=\"space-y-3\">\n                  <div>\n                    <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Nome</label>\n                    <input v-model=\"editForm.emergency_contact_name\"\n                           type=\"text\"\n                           class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\">\n                  </div>\n                  <div>\n                    <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Telefono</label>\n                    <input v-model=\"editForm.emergency_contact_phone\"\n                           type=\"tel\"\n                           class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\">\n                  </div>\n                  <div>\n                    <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Relazione</label>\n                    <input v-model=\"editForm.emergency_contact_relationship\"\n                           type=\"text\"\n                           placeholder=\"es. Coniuge, Genitore, Fratello\"\n                           class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\">\n                  </div>\n                </div>\n              </div>\n\n              <!-- Address Section -->\n              <div class=\"border-t border-gray-200 dark:border-gray-600 pt-4 mt-4\">\n                <h4 class=\"text-sm font-medium text-gray-900 dark:text-white mb-3\">Indirizzo</h4>\n                <div>\n                  <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Indirizzo Completo</label>\n                  <textarea v-model=\"editForm.address\"\n                            rows=\"2\"\n                            placeholder=\"Via, Città, CAP, Provincia\"\n                            class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"></textarea>\n                </div>\n              </div>\n            </div>\n          </div>\n\n      </div>\n\n      <!-- Bio Section - Full Width -->\n      <div v-if=\"user.bio || editMode\" class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6\">\n        <h3 class=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">Bio</h3>\n        <p v-if=\"!editMode\" class=\"text-gray-700 dark:text-gray-300 text-sm leading-relaxed\">{{ user.bio || 'Nessuna bio disponibile' }}</p>\n      </div>\n\n      <!-- Tabs Content - Full Width -->\n      <div class=\"w-full\">\n          <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden\">\n            <!-- Tab Navigation -->\n            <div class=\"border-b border-gray-200 dark:border-gray-700\">\n              <nav class=\"-mb-px flex space-x-8 px-6\" aria-label=\"Tabs\">\n                <button v-for=\"tab in tabs\" :key=\"tab.id\"\n                        @click=\"switchTab(tab.id)\"\n                        :class=\"[\n                          activeTab === tab.id\n                            ? 'border-blue-500 text-blue-600 dark:text-blue-400'\n                            : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300',\n                          'whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center'\n                        ]\">\n                  <!-- Tab Icons -->\n                  <svg v-if=\"tab.id === 'projects'\" class=\"w-5 h-5 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fill-rule=\"evenodd\" d=\"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z\" clip-rule=\"evenodd\"></path>\n                  </svg>\n                  <svg v-else-if=\"tab.id === 'tasks'\" class=\"w-5 h-5 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fill-rule=\"evenodd\" d=\"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z\" clip-rule=\"evenodd\"></path>\n                  </svg>\n                  <svg v-else-if=\"tab.id === 'skills'\" class=\"w-5 h-5 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"></path>\n                  </svg>\n                  <svg v-else-if=\"tab.id === 'timesheet'\" class=\"w-5 h-5 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\" clip-rule=\"evenodd\"></path>\n                  </svg>\n                  <svg v-else-if=\"tab.id === 'cv'\" class=\"w-5 h-5 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fill-rule=\"evenodd\" d=\"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z\" clip-rule=\"evenodd\"></path>\n                  </svg>\n                  {{ tab.name }}\n                  <span v-if=\"tab.count !== undefined\"\n                        class=\"ml-2 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 py-0.5 px-2 rounded-full text-xs\">\n                    {{ tab.count }}\n                  </span>\n                </button>\n              </nav>\n            </div>\n\n            <!-- Tab Content -->\n            <div class=\"p-6\">\n              <!-- Projects Tab -->\n              <div v-if=\"activeTab === 'projects'\" class=\"space-y-4\">\n                <div v-if=\"userProjects.length > 0\">\n                  <div v-for=\"project in userProjects\" :key=\"project.id\"\n                       class=\"border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\">\n                    <div class=\"flex items-center justify-between\">\n                      <div>\n                        <h4 class=\"font-medium text-gray-900 dark:text-white\">{{ project.name }}</h4>\n                        <p class=\"text-sm text-gray-500 dark:text-gray-400\">{{ project.role || 'Team Member' }}</p>\n                      </div>\n                      <span :class=\"[\n                        'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',\n                        project.status === 'active' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :\n                        project.status === 'completed' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :\n                        'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'\n                      ]\">\n                        {{ project.status }}\n                      </span>\n                    </div>\n                  </div>\n                </div>\n                <div v-else class=\"text-center py-8\">\n                  <svg class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\" />\n                  </svg>\n                  <h3 class=\"mt-2 text-sm font-medium text-gray-900 dark:text-white\">Nessun progetto</h3>\n                  <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">L'utente non è assegnato a nessun progetto.</p>\n                </div>\n              </div>\n\n              <!-- Tasks Tab -->\n              <div v-if=\"activeTab === 'tasks'\" class=\"space-y-4\">\n                <div v-if=\"userTasks.length > 0\">\n                  <div v-for=\"task in userTasks\" :key=\"task.id\"\n                       class=\"border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\">\n                    <div class=\"flex items-start justify-between\">\n                      <div class=\"flex-1\">\n                        <h4 class=\"font-medium text-gray-900 dark:text-white\">{{ task.name }}</h4>\n                        <p class=\"text-sm text-gray-500 dark:text-gray-400 mt-1\">{{ task.project_name }}</p>\n                        <div class=\"flex items-center mt-2 space-x-4\">\n                          <span :class=\"[\n                            'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',\n                            task.priority === 'urgent' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :\n                            task.priority === 'high' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200' :\n                            task.priority === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :\n                            'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'\n                          ]\">\n                            {{ task.priority }}\n                          </span>\n                          <span v-if=\"task.due_date\" class=\"text-xs text-gray-500 dark:text-gray-400\">\n                            Scadenza: {{ formatDate(task.due_date) }}\n                          </span>\n                        </div>\n                      </div>\n                      <span :class=\"[\n                        'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ml-4',\n                        task.status === 'done' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :\n                        task.status === 'in-progress' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :\n                        task.status === 'review' ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200' :\n                        'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'\n                      ]\">\n                        {{ task.status }}\n                      </span>\n                    </div>\n                  </div>\n                </div>\n                <div v-else class=\"text-center py-8\">\n                  <svg class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4\" />\n                  </svg>\n                  <h3 class=\"mt-2 text-sm font-medium text-gray-900 dark:text-white\">Nessun task</h3>\n                  <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">L'utente non ha task assegnati.</p>\n                </div>\n              </div>\n\n              <!-- Skills Tab -->\n              <div v-if=\"activeTab === 'skills'\" class=\"space-y-6\">\n                <div v-if=\"user.skills && user.skills.length > 0\">\n                  <!-- Skills Header with Stats -->\n                  <div class=\"flex items-center justify-between mb-4\">\n                    <div class=\"flex items-center space-x-4\">\n                      <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                        Competenze ({{ user.skills.length }})\n                      </h3>\n                      <span class=\"text-sm text-gray-500 dark:text-gray-400\">\n                        Pagina {{ skillsCurrentPage }} di {{ totalSkillsPages }}\n                      </span>\n                    </div>\n                    <div class=\"text-sm text-gray-500 dark:text-gray-400\">\n                      {{ user.skills.filter(s => s.certified).length }} certificate\n                    </div>\n                  </div>\n\n                  <!-- Skills Grid -->\n                  <div class=\"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4\">\n                    <div v-for=\"skill in paginatedSkills\" :key=\"skill.id\"\n                         class=\"border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow\">\n                      <div class=\"flex items-center justify-between mb-2\">\n                        <h4 class=\"font-medium text-gray-900 dark:text-white\">{{ skill.name }}</h4>\n                        <span v-if=\"skill.certified\" class=\"text-green-600 dark:text-green-400 text-sm font-medium\">✓ Certificato</span>\n                      </div>\n                      <p v-if=\"skill.category\" class=\"text-sm text-gray-500 dark:text-gray-400 mb-3\">{{ skill.category }}</p>\n                      <div class=\"flex items-center justify-between\">\n                        <div class=\"flex items-center space-x-2\">\n                          <span class=\"text-sm text-gray-600 dark:text-gray-400\">Livello:</span>\n                          <div class=\"flex space-x-1\">\n                            <div v-for=\"i in 5\" :key=\"i\"\n                                 class=\"w-3 h-3 rounded-full\"\n                                 :class=\"i <= skill.proficiency_level ? 'bg-blue-500' : 'bg-gray-300 dark:bg-gray-600'\"></div>\n                          </div>\n                          <span class=\"text-xs text-gray-500 dark:text-gray-400\">({{ skill.proficiency_level }}/5)</span>\n                        </div>\n                        <span v-if=\"skill.years_experience\" class=\"text-sm text-gray-500 dark:text-gray-400\">\n                          {{ skill.years_experience }}{{ skill.years_experience === 1 ? ' anno' : ' anni' }}\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- Pagination -->\n                  <div v-if=\"totalSkillsPages > 1\" class=\"flex items-center justify-between border-t border-gray-200 dark:border-gray-700 pt-4\">\n                    <div class=\"flex items-center space-x-2\">\n                      <button @click=\"skillsCurrentPage = Math.max(1, skillsCurrentPage - 1)\"\n                              :disabled=\"skillsCurrentPage === 1\"\n                              class=\"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700\">\n                        Precedente\n                      </button>\n                      <span class=\"text-sm text-gray-700 dark:text-gray-300\">\n                        Pagina {{ skillsCurrentPage }} di {{ totalSkillsPages }}\n                      </span>\n                      <button @click=\"skillsCurrentPage = Math.min(totalSkillsPages, skillsCurrentPage + 1)\"\n                              :disabled=\"skillsCurrentPage === totalSkillsPages\"\n                              class=\"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700\">\n                        Successiva\n                      </button>\n                    </div>\n                    <div class=\"text-sm text-gray-500 dark:text-gray-400\">\n                      Mostrando {{ ((skillsCurrentPage - 1) * skillsPerPage) + 1 }}-{{ Math.min(skillsCurrentPage * skillsPerPage, user.skills.length) }} di {{ user.skills.length }} competenze\n                    </div>\n                  </div>\n                </div>\n                <div v-else class=\"text-center py-8\">\n                  <svg class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\" />\n                  </svg>\n                  <h3 class=\"mt-2 text-sm font-medium text-gray-900 dark:text-white\">Nessuna competenza</h3>\n                  <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">Non sono state registrate competenze per questo utente.</p>\n                </div>\n              </div>\n\n              <!-- Timesheet Tab -->\n              <div v-if=\"activeTab === 'timesheet'\" class=\"space-y-4\">\n                <div v-if=\"userTimesheets.length > 0\">\n                  <div class=\"overflow-x-auto\">\n                    <table class=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\n                      <thead class=\"bg-gray-50 dark:bg-gray-700\">\n                        <tr>\n                          <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">Data</th>\n                          <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">Progetto</th>\n                          <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">Task</th>\n                          <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">Ore</th>\n                          <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">Descrizione</th>\n                        </tr>\n                      </thead>\n                      <tbody class=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\n                        <tr v-for=\"timesheet in userTimesheets\" :key=\"timesheet.id\">\n                          <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                            {{ formatDate(timesheet.date) }}\n                          </td>\n                          <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                            {{ timesheet.project_name }}\n                          </td>\n                          <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\">\n                            {{ timesheet.task_name || '-' }}\n                          </td>\n                          <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                            {{ timesheet.hours }}h\n                          </td>\n                          <td class=\"px-6 py-4 text-sm text-gray-500 dark:text-gray-400\">\n                            {{ timesheet.description || '-' }}\n                          </td>\n                        </tr>\n                      </tbody>\n                    </table>\n                  </div>\n                </div>\n                <div v-else class=\"text-center py-8\">\n                  <svg class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                  </svg>\n                  <h3 class=\"mt-2 text-sm font-medium text-gray-900 dark:text-white\">Nessun timesheet</h3>\n                  <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">Non sono stati registrati timesheet per questo utente.</p>\n                </div>\n              </div>\n\n              <!-- CV Tab -->\n              <div v-if=\"activeTab === 'cv'\" class=\"space-y-6\">\n                <!-- Current CV Section -->\n                <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n                  <div class=\"flex items-center justify-between mb-4\">\n                    <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">CV Attuale</h3>\n                    <button v-if=\"canEdit\"\n                            class=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm transition-colors duration-200\">\n                      <svg class=\"w-4 h-4 inline mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fill-rule=\"evenodd\" d=\"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z\" clip-rule=\"evenodd\"></path>\n                      </svg>\n                      Carica CV\n                    </button>\n                  </div>\n\n                  <div v-if=\"user.profile?.current_cv_path\" class=\"flex items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg\">\n                    <svg class=\"w-8 h-8 text-red-600 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fill-rule=\"evenodd\" d=\"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z\" clip-rule=\"evenodd\"></path>\n                    </svg>\n                    <div class=\"flex-1\">\n                      <p class=\"text-sm font-medium text-gray-900 dark:text-white\">CV_{{ user.full_name }}.pdf</p>\n                      <p class=\"text-xs text-gray-500 dark:text-gray-400\">\n                        Caricato il {{ formatDate(user.profile.cv_last_updated) }}\n                      </p>\n                    </div>\n                    <div class=\"flex space-x-2\">\n                      <button class=\"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300\">\n                        <svg class=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                          <path d=\"M10 12a2 2 0 100-4 2 2 0 000 4z\"></path>\n                          <path fill-rule=\"evenodd\" d=\"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z\" clip-rule=\"evenodd\"></path>\n                        </svg>\n                      </button>\n                      <button class=\"text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300\">\n                        <svg class=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                          <path fill-rule=\"evenodd\" d=\"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z\" clip-rule=\"evenodd\"></path>\n                        </svg>\n                      </button>\n                    </div>\n                  </div>\n\n                  <div v-else class=\"text-center py-8\">\n                    <svg class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                    </svg>\n                    <h3 class=\"mt-2 text-sm font-medium text-gray-900 dark:text-white\">Nessun CV caricato</h3>\n                    <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">Carica il tuo CV per completare il profilo.</p>\n                  </div>\n                </div>\n\n                <!-- Documents Section -->\n                <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n                  <div class=\"flex items-center justify-between mb-4\">\n                    <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">Documenti</h3>\n                    <button v-if=\"canEdit\"\n                            class=\"bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm transition-colors duration-200\">\n                      <svg class=\"w-4 h-4 inline mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fill-rule=\"evenodd\" d=\"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z\" clip-rule=\"evenodd\"></path>\n                      </svg>\n                      Carica Documento\n                    </button>\n                  </div>\n\n                  <div class=\"text-center py-8\">\n                    <svg class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z\" />\n                    </svg>\n                    <h3 class=\"mt-2 text-sm font-medium text-gray-900 dark:text-white\">Nessun documento</h3>\n                    <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">I documenti caricati appariranno qui.</p>\n                  </div>\n                </div>\n\n                <!-- Profile Completion Tips -->\n                <div class=\"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4\">\n                  <div class=\"flex\">\n                    <svg class=\"w-5 h-5 text-blue-400 mt-0.5 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fill-rule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\" clip-rule=\"evenodd\"></path>\n                    </svg>\n                    <div>\n                      <h3 class=\"text-sm font-medium text-blue-800 dark:text-blue-200\">Suggerimenti per completare il profilo</h3>\n                      <div class=\"mt-2 text-sm text-blue-700 dark:text-blue-300\">\n                        <ul class=\"list-disc list-inside space-y-1\">\n                          <li>Carica un CV aggiornato in formato PDF</li>\n                          <li>Aggiungi certificazioni e documenti di formazione</li>\n                          <li>Mantieni i documenti sempre aggiornati</li>\n                        </ul>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted, watch } from 'vue'\nimport { useRoute, useRouter } from 'vue-router'\nimport { usePersonnelStore } from '@/stores/personnel'\nimport { usePermissions } from '@/composables/usePermissions'\n\n// Router\nconst route = useRoute()\nconst router = useRouter()\n\n// Stores\nconst personnelStore = usePersonnelStore()\nconst { hasPermission } = usePermissions()\n\n// Reactive state\nconst user = ref(null)\nconst userProjects = ref([])\nconst userTasks = ref([])\nconst userTimesheets = ref([])\nconst loading = ref(false)\nconst error = ref(null)\nconst editMode = ref(false)\nconst saving = ref(false)\nconst activeTab = ref('projects')\n\n// Skills pagination\nconst skillsCurrentPage = ref(1)\nconst skillsPerPage = ref(6)\n\n// Edit form\nconst editForm = ref({\n  phone: '',\n  bio: '',\n  employee_id: '',\n  job_title: '',\n  employment_type: '',\n  work_location: '',\n  weekly_hours: 40,\n  address: '',\n  emergency_contact_name: '',\n  emergency_contact_phone: '',\n  emergency_contact_relationship: ''\n})\n\n// Computed properties\nconst canEdit = computed(() => {\n  if (!user.value) return false\n  try {\n    // Fix: hasPermission is a computed that returns a function\n    return hasPermission.value && typeof hasPermission.value === 'function' ? hasPermission.value('edit_personnel_data') : false\n  } catch (e) {\n    console.warn('Permission check failed:', e)\n    return false\n  }\n})\n\n// Skills pagination computed\nconst paginatedSkills = computed(() => {\n  if (!user.value?.skills) return []\n  const start = (skillsCurrentPage.value - 1) * skillsPerPage.value\n  const end = start + skillsPerPage.value\n  return user.value.skills.slice(start, end)\n})\n\nconst totalSkillsPages = computed(() => {\n  if (!user.value?.skills) return 0\n  return Math.ceil(user.value.skills.length / skillsPerPage.value)\n})\n\n// Tab configuration\nconst tabs = computed(() => [\n  {\n    id: 'projects',\n    name: 'Progetti',\n    count: userProjects.value.length\n  },\n  {\n    id: 'tasks',\n    name: 'Task',\n    count: userTasks.value.length\n  },\n  {\n    id: 'skills',\n    name: 'Competenze',\n    count: user.value?.skills?.length || 0\n  },\n  {\n    id: 'timesheet',\n    name: 'Timesheet',\n    count: userTimesheets.value.length\n  },\n  {\n    id: 'cv',\n    name: 'CV'\n  }\n])\n\n// Utility functions\nconst formatDate = (dateString) => {\n  if (!dateString) return ''\n  const date = new Date(dateString)\n  return date.toLocaleDateString('it-IT', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  })\n}\n\nconst formatDateTime = (dateString) => {\n  if (!dateString) return ''\n  return new Date(dateString).toLocaleString('it-IT')\n}\n\nconst getPriorityColor = (priority) => {\n  const colors = {\n    'low': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',\n    'medium': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',\n    'high': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'\n  }\n  return colors[priority] || colors['medium']\n}\n\nconst getStatusColor = (status) => {\n  const colors = {\n    'active': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',\n    'completed': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',\n    'on_hold': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',\n    'cancelled': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'\n  }\n  return colors[status] || colors['active']\n}\n\nconst getEmploymentTypeLabel = (type) => {\n  const labels = {\n    'full_time': 'Tempo Pieno',\n    'part_time': 'Part Time',\n    'contractor': 'Consulente',\n    'intern': 'Stagista'\n  }\n  return labels[type] || type\n}\n\nconst getWorkLocationLabel = (location) => {\n  const labels = {\n    'office': 'Ufficio',\n    'remote': 'Remoto',\n    'hybrid': 'Ibrido'\n  }\n  return labels[location] || location\n}\n\n// Tab navigation\nconst switchTab = (tabId) => {\n  activeTab.value = tabId\n  // Reset skills pagination when switching to skills tab\n  if (tabId === 'skills') {\n    skillsCurrentPage.value = 1\n  }\n}\n\n// Edit methods\nconst initEditForm = () => {\n  if (!user.value) return\n\n  editForm.value = {\n    phone: user.value.phone || '',\n    bio: user.value.bio || '',\n    employee_id: user.value.profile?.employee_id || '',\n    job_title: user.value.profile?.job_title || '',\n    employment_type: user.value.profile?.employment_type || '',\n    work_location: user.value.profile?.work_location || '',\n    weekly_hours: user.value.profile?.weekly_hours || 40,\n    address: user.value.profile?.address || '',\n    emergency_contact_name: user.value.profile?.emergency_contact_name || '',\n    emergency_contact_phone: user.value.profile?.emergency_contact_phone || '',\n    emergency_contact_relationship: user.value.profile?.emergency_contact_relationship || ''\n  }\n}\n\nconst cancelEdit = () => {\n  editMode.value = false\n  initEditForm()\n}\n\nconst saveProfile = async () => {\n  if (!user.value) return\n\n  saving.value = true\n  try {\n    const response = await fetch(`/api/personnel/users/${user.value.id}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': document.querySelector('meta[name=\"csrf-token\"]')?.getAttribute('content') || ''\n      },\n      body: JSON.stringify(editForm.value)\n    })\n\n    const data = await response.json()\n\n    if (data.success) {\n      // Update user data\n      user.value = data.data.user\n      editMode.value = false\n\n      // Show success message\n      console.log('Profilo aggiornato con successo')\n    } else {\n      throw new Error(data.message || 'Errore durante il salvataggio')\n    }\n  } catch (err) {\n    console.error('Errore durante il salvataggio:', err)\n    error.value = err.message\n  } finally {\n    saving.value = false\n  }\n}\n\n// API functions\nconst fetchUserProfile = async (userId) => {\n  loading.value = true\n  error.value = null\n\n  try {\n    const response = await fetch(`/api/personnel/users/${userId}`, {\n      credentials: 'include'\n    })\n\n    if (!response.ok) {\n      throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n\n    if (data.success) {\n      user.value = data.data.user\n      // Initialize edit form with user data\n      initEditForm()\n    } else {\n      throw new Error(data.message || 'Errore nel caricamento del profilo')\n    }\n  } catch (err) {\n    console.error('Error fetching user profile:', err)\n    error.value = err.message\n  } finally {\n    loading.value = false\n  }\n}\n\nconst fetchUserTasks = async (userId) => {\n  try {\n    const response = await fetch(`/api/tasks?assignee_id=${userId}&limit=20`, {\n      credentials: 'include'\n    })\n\n    if (!response.ok) {\n      throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n\n    if (data.success) {\n      userTasks.value = data.data.tasks || []\n    }\n  } catch (err) {\n    console.error('Error fetching user tasks:', err)\n    // Non bloccare l'interfaccia per errori sui task\n  }\n}\n\nconst fetchUserTimesheets = async (userId) => {\n  try {\n    // Fetch timesheets for the last 30 days\n    const endDate = new Date()\n    const startDate = new Date()\n    startDate.setDate(startDate.getDate() - 30)\n\n    const response = await fetch(`/api/timesheets?user_id=${userId}&start_date=${startDate.toISOString().split('T')[0]}&end_date=${endDate.toISOString().split('T')[0]}&limit=50`, {\n      credentials: 'include'\n    })\n\n    if (!response.ok) {\n      throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n\n    if (data.success) {\n      userTimesheets.value = data.data || []\n    }\n  } catch (err) {\n    console.error('Error fetching user timesheets:', err)\n    // Non bloccare l'interfaccia per errori sui timesheet\n  }\n}\n\n// Lifecycle\nonMounted(async () => {\n  const userId = route.params.id\n\n  if (!userId) {\n    error.value = 'ID utente non specificato'\n    return\n  }\n\n  // Fetch user profile\n  await fetchUserProfile(userId)\n\n  // Fetch related data\n  if (user.value) {\n    userProjects.value = user.value.projects || []\n    await fetchUserTasks(userId)\n    await fetchUserTimesheets(userId)\n  }\n})\n\n// Watch for route changes\nwatch(() => route.params.id, async (newId) => {\n  if (newId) {\n    await fetchUserProfile(newId)\n    if (user.value) {\n      userProjects.value = user.value.projects || []\n      await fetchUserTasks(newId)\n      await fetchUserTimesheets(newId)\n    }\n  }\n})\n</script>\n", "modifiedCode": "<template>\n  <div class=\"personnel-profile\">\n    <!-- Loading State -->\n    <div v-if=\"loading\" class=\"flex justify-center items-center h-64\">\n      <div class=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n    </div>\n\n    <!-- Error State -->\n    <div v-else-if=\"error\" class=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6\">\n      <div class=\"flex\">\n        <svg class=\"w-5 h-5 text-red-400 mr-2 mt-0.5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n          <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clip-rule=\"evenodd\"></path>\n        </svg>\n        <div>\n          <h3 class=\"text-sm font-medium text-red-800 dark:text-red-200\">Errore nel caricamento del profilo</h3>\n          <p class=\"text-sm text-red-700 dark:text-red-300 mt-1\">{{ error }}</p>\n        </div>\n      </div>\n    </div>\n\n    <!-- Profile Content -->\n    <div v-else-if=\"user\" class=\"space-y-6\">\n      <!-- Header Section -->\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden\">\n        <div class=\"bg-gradient-to-r from-blue-500 to-purple-600 px-6 py-8\">\n          <div class=\"flex items-center space-x-6\">\n            <!-- Avatar -->\n            <div class=\"flex-shrink-0\">\n              <div class=\"w-24 h-24 bg-white rounded-full flex items-center justify-center shadow-lg\">\n                <img v-if=\"user.profile_image\"\n                     :src=\"user.profile_image\"\n                     :alt=\"user.full_name\"\n                     class=\"w-24 h-24 rounded-full object-cover\">\n                <div v-else class=\"w-24 h-24 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center\">\n                  <svg class=\"w-12 h-12 text-gray-400 dark:text-gray-300\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fill-rule=\"evenodd\" d=\"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\" clip-rule=\"evenodd\"></path>\n                  </svg>\n                </div>\n              </div>\n            </div>\n\n            <!-- User Info -->\n            <div class=\"flex-1 text-white\">\n              <h1 class=\"text-3xl font-bold\">{{ user.full_name }}</h1>\n              <p class=\"text-blue-100 text-lg\">{{ user.position || 'Posizione non specificata' }}</p>\n              <div class=\"flex items-center space-x-4 mt-2\">\n                <span v-if=\"user.department\" class=\"inline-flex items-center px-3 py-1 rounded-full text-sm bg-white/20 text-white\">\n                  <svg class=\"w-4 h-4 mr-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fill-rule=\"evenodd\" d=\"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z\" clip-rule=\"evenodd\"></path>\n                  </svg>\n                  {{ user.department.name }}\n                </span>\n                <span class=\"inline-flex items-center px-3 py-1 rounded-full text-sm bg-white/20 text-white\">\n                  <svg class=\"w-4 h-4 mr-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fill-rule=\"evenodd\" d=\"M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z\" clip-rule=\"evenodd\"></path>\n                  </svg>\n                  {{ user.role }}\n                </span>\n              </div>\n            </div>\n\n            <!-- Actions -->\n            <div class=\"flex-shrink-0\">\n              <button v-if=\"canEdit\"\n                      @click=\"editMode = !editMode\"\n                      class=\"bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-colors duration-200\">\n                <svg class=\"w-5 h-5 inline mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path d=\"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z\"></path>\n                </svg>\n                {{ editMode ? 'Annulla' : 'Modifica' }}\n              </button>\n            </div>\n          </div>\n        </div>\n\n        <!-- Profile Completion Bar -->\n        <div v-if=\"user.profile && user.profile.profile_completion !== undefined\" class=\"px-6 py-4 bg-gray-50 dark:bg-gray-700\">\n          <div class=\"flex items-center justify-between mb-2\">\n            <span class=\"text-sm font-medium text-gray-700 dark:text-gray-300\">Completamento Profilo</span>\n            <span class=\"text-sm text-gray-500 dark:text-gray-400\">{{ user.profile.profile_completion }}%</span>\n          </div>\n          <div class=\"w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2\">\n            <div class=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\n                 :style=\"{ width: user.profile.profile_completion + '%' }\"></div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Personal Information Cards - Horizontal Layout -->\n      <div class=\"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 mb-6\">\n          <!-- Contact Information -->\n          <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n            <div class=\"flex items-center justify-between mb-4\">\n              <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">Informazioni di Contatto</h3>\n              <button v-if=\"canEdit && !editMode\"\n                      @click=\"editMode = true\"\n                      class=\"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300\">\n                <svg class=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path d=\"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z\"></path>\n                </svg>\n              </button>\n            </div>\n\n            <!-- View Mode -->\n            <div v-if=\"!editMode\" class=\"space-y-3\">\n              <div class=\"flex items-center\">\n                <svg class=\"w-5 h-5 text-gray-400 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path d=\"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z\"></path>\n                  <path d=\"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z\"></path>\n                </svg>\n                <span class=\"text-gray-900 dark:text-white\">{{ user.email }}</span>\n              </div>\n              <div v-if=\"user.phone\" class=\"flex items-center\">\n                <svg class=\"w-5 h-5 text-gray-400 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path d=\"M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z\"></path>\n                </svg>\n                <span class=\"text-gray-900 dark:text-white\">{{ user.phone }}</span>\n              </div>\n              <div v-if=\"user.hire_date\" class=\"flex items-center\">\n                <svg class=\"w-5 h-5 text-gray-400 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fill-rule=\"evenodd\" d=\"M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z\" clip-rule=\"evenodd\"></path>\n                </svg>\n                <span class=\"text-gray-900 dark:text-white\">Assunto il {{ formatDate(user.hire_date) }}</span>\n              </div>\n            </div>\n\n            <!-- Edit Mode -->\n            <div v-else class=\"space-y-4\">\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Telefono</label>\n                <input v-model=\"editForm.phone\"\n                       type=\"tel\"\n                       class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\">\n              </div>\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Bio</label>\n                <textarea v-model=\"editForm.bio\"\n                          rows=\"3\"\n                          class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"></textarea>\n              </div>\n              <div class=\"flex space-x-2\">\n                <button @click=\"saveProfile\"\n                        :disabled=\"saving\"\n                        class=\"flex-1 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-4 py-2 rounded-md transition-colors duration-200\">\n                  {{ saving ? 'Salvataggio...' : 'Salva' }}\n                </button>\n                <button @click=\"cancelEdit\"\n                        class=\"flex-1 bg-gray-300 hover:bg-gray-400 dark:bg-gray-600 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-200 px-4 py-2 rounded-md transition-colors duration-200\">\n                  Annulla\n                </button>\n              </div>\n            </div>\n          </div>\n\n        <!-- Skills Overview -->\n        <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">Competenze Principali</h3>\n          <div v-if=\"user.skills && user.skills.length > 0\" class=\"space-y-3\">\n            <div v-for=\"skill in user.skills.slice(0, 4)\" :key=\"skill.id\" class=\"flex items-center justify-between\">\n              <span class=\"text-sm font-medium text-gray-900 dark:text-white\">{{ skill.name }}</span>\n              <div class=\"flex items-center space-x-2\">\n                <div class=\"flex space-x-1\">\n                  <div v-for=\"i in 5\" :key=\"i\"\n                       class=\"w-2 h-2 rounded-full\"\n                       :class=\"i <= skill.proficiency_level ? 'bg-blue-500' : 'bg-gray-300 dark:bg-gray-600'\"></div>\n                </div>\n                <span v-if=\"skill.certified\" class=\"text-xs text-green-600 dark:text-green-400\">✓</span>\n              </div>\n            </div>\n            <div v-if=\"user.skills.length > 4\" class=\"text-sm text-gray-500 dark:text-gray-400 text-center pt-2\">\n              +{{ user.skills.length - 4 }} altre competenze\n            </div>\n          </div>\n          <div v-else class=\"text-gray-500 dark:text-gray-400 text-sm\">\n            Nessuna competenza registrata\n          </div>\n        </div>\n\n          <!-- HR Information -->\n          <div v-if=\"user.profile\" class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n            <div class=\"flex items-center justify-between mb-4\">\n              <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">Informazioni HR</h3>\n              <button v-if=\"canEdit && !editMode\"\n                      @click=\"editMode = true\"\n                      class=\"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300\">\n                <svg class=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path d=\"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z\"></path>\n                </svg>\n              </button>\n            </div>\n\n            <!-- View Mode -->\n            <div v-if=\"!editMode\" class=\"space-y-3\">\n              <div v-if=\"user.profile.employee_id\" class=\"flex justify-between\">\n                <span class=\"text-sm text-gray-500 dark:text-gray-400\">ID Dipendente:</span>\n                <span class=\"text-sm text-gray-900 dark:text-white\">{{ user.profile.employee_id }}</span>\n              </div>\n              <div v-if=\"user.profile.job_title\" class=\"flex justify-between\">\n                <span class=\"text-sm text-gray-500 dark:text-gray-400\">Titolo:</span>\n                <span class=\"text-sm text-gray-900 dark:text-white\">{{ user.profile.job_title }}</span>\n              </div>\n              <div v-if=\"user.profile.employment_type\" class=\"flex justify-between\">\n                <span class=\"text-sm text-gray-500 dark:text-gray-400\">Tipo Contratto:</span>\n                <span class=\"text-sm text-gray-900 dark:text-white\">{{ getEmploymentTypeLabel(user.profile.employment_type) }}</span>\n              </div>\n              <div v-if=\"user.profile.work_location\" class=\"flex justify-between\">\n                <span class=\"text-sm text-gray-500 dark:text-gray-400\">Modalità Lavoro:</span>\n                <span class=\"text-sm text-gray-900 dark:text-white\">{{ getWorkLocationLabel(user.profile.work_location) }}</span>\n              </div>\n              <div v-if=\"user.profile.weekly_hours\" class=\"flex justify-between\">\n                <span class=\"text-sm text-gray-500 dark:text-gray-400\">Ore Settimanali:</span>\n                <span class=\"text-sm text-gray-900 dark:text-white\">{{ user.profile.weekly_hours }}h</span>\n              </div>\n            </div>\n\n            <!-- Edit Mode HR -->\n            <div v-else class=\"space-y-4\">\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">ID Dipendente</label>\n                <input v-model=\"editForm.employee_id\"\n                       type=\"text\"\n                       class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\">\n              </div>\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Titolo Lavoro</label>\n                <input v-model=\"editForm.job_title\"\n                       type=\"text\"\n                       class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\">\n              </div>\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Tipo Contratto</label>\n                <select v-model=\"editForm.employment_type\"\n                        class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\">\n                  <option value=\"\">Seleziona tipo</option>\n                  <option value=\"full_time\">Tempo Pieno</option>\n                  <option value=\"part_time\">Part Time</option>\n                  <option value=\"contractor\">Consulente</option>\n                  <option value=\"intern\">Stagista</option>\n                </select>\n              </div>\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Modalità Lavoro</label>\n                <select v-model=\"editForm.work_location\"\n                        class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\">\n                  <option value=\"\">Seleziona modalità</option>\n                  <option value=\"office\">Ufficio</option>\n                  <option value=\"remote\">Remoto</option>\n                  <option value=\"hybrid\">Ibrido</option>\n                </select>\n              </div>\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Ore Settimanali</label>\n                <input v-model=\"editForm.weekly_hours\"\n                       type=\"number\"\n                       min=\"1\"\n                       max=\"60\"\n                       step=\"0.5\"\n                       class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\">\n              </div>\n\n              <!-- Emergency Contact Section -->\n              <div class=\"border-t border-gray-200 dark:border-gray-600 pt-4 mt-4\">\n                <h4 class=\"text-sm font-medium text-gray-900 dark:text-white mb-3\">Contatto di Emergenza</h4>\n                <div class=\"space-y-3\">\n                  <div>\n                    <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Nome</label>\n                    <input v-model=\"editForm.emergency_contact_name\"\n                           type=\"text\"\n                           class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\">\n                  </div>\n                  <div>\n                    <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Telefono</label>\n                    <input v-model=\"editForm.emergency_contact_phone\"\n                           type=\"tel\"\n                           class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\">\n                  </div>\n                  <div>\n                    <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Relazione</label>\n                    <input v-model=\"editForm.emergency_contact_relationship\"\n                           type=\"text\"\n                           placeholder=\"es. Coniuge, Genitore, Fratello\"\n                           class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\">\n                  </div>\n                </div>\n              </div>\n\n              <!-- Address Section -->\n              <div class=\"border-t border-gray-200 dark:border-gray-600 pt-4 mt-4\">\n                <h4 class=\"text-sm font-medium text-gray-900 dark:text-white mb-3\">Indirizzo</h4>\n                <div>\n                  <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Indirizzo Completo</label>\n                  <textarea v-model=\"editForm.address\"\n                            rows=\"2\"\n                            placeholder=\"Via, Città, CAP, Provincia\"\n                            class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"></textarea>\n                </div>\n              </div>\n            </div>\n          </div>\n\n      </div>\n\n      <!-- Bio Section - Full Width -->\n      <div v-if=\"user.bio || editMode\" class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6\">\n        <h3 class=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">Bio</h3>\n        <p v-if=\"!editMode\" class=\"text-gray-700 dark:text-gray-300 text-sm leading-relaxed\">{{ user.bio || 'Nessuna bio disponibile' }}</p>\n      </div>\n\n      <!-- Tabs Content - Full Width -->\n      <div class=\"w-full\">\n          <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden\">\n            <!-- Tab Navigation -->\n            <div class=\"border-b border-gray-200 dark:border-gray-700\">\n              <nav class=\"-mb-px flex space-x-8 px-6\" aria-label=\"Tabs\">\n                <button v-for=\"tab in tabs\" :key=\"tab.id\"\n                        @click=\"switchTab(tab.id)\"\n                        :class=\"[\n                          activeTab === tab.id\n                            ? 'border-blue-500 text-blue-600 dark:text-blue-400'\n                            : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300',\n                          'whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center'\n                        ]\">\n                  <!-- Tab Icons -->\n                  <svg v-if=\"tab.id === 'projects'\" class=\"w-5 h-5 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fill-rule=\"evenodd\" d=\"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z\" clip-rule=\"evenodd\"></path>\n                  </svg>\n                  <svg v-else-if=\"tab.id === 'tasks'\" class=\"w-5 h-5 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fill-rule=\"evenodd\" d=\"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z\" clip-rule=\"evenodd\"></path>\n                  </svg>\n                  <svg v-else-if=\"tab.id === 'skills'\" class=\"w-5 h-5 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"></path>\n                  </svg>\n                  <svg v-else-if=\"tab.id === 'timesheet'\" class=\"w-5 h-5 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\" clip-rule=\"evenodd\"></path>\n                  </svg>\n                  <svg v-else-if=\"tab.id === 'cv'\" class=\"w-5 h-5 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fill-rule=\"evenodd\" d=\"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z\" clip-rule=\"evenodd\"></path>\n                  </svg>\n                  {{ tab.name }}\n                  <span v-if=\"tab.count !== undefined\"\n                        class=\"ml-2 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 py-0.5 px-2 rounded-full text-xs\">\n                    {{ tab.count }}\n                  </span>\n                </button>\n              </nav>\n            </div>\n\n            <!-- Tab Content -->\n            <div class=\"p-6\">\n              <!-- Projects Tab -->\n              <div v-if=\"activeTab === 'projects'\" class=\"space-y-4\">\n                <div v-if=\"userProjects.length > 0\">\n                  <div v-for=\"project in userProjects\" :key=\"project.id\"\n                       class=\"border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\">\n                    <div class=\"flex items-center justify-between\">\n                      <div>\n                        <h4 class=\"font-medium text-gray-900 dark:text-white\">{{ project.name }}</h4>\n                        <p class=\"text-sm text-gray-500 dark:text-gray-400\">{{ project.role || 'Team Member' }}</p>\n                      </div>\n                      <span :class=\"[\n                        'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',\n                        project.status === 'active' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :\n                        project.status === 'completed' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :\n                        'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'\n                      ]\">\n                        {{ project.status }}\n                      </span>\n                    </div>\n                  </div>\n                </div>\n                <div v-else class=\"text-center py-8\">\n                  <svg class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\" />\n                  </svg>\n                  <h3 class=\"mt-2 text-sm font-medium text-gray-900 dark:text-white\">Nessun progetto</h3>\n                  <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">L'utente non è assegnato a nessun progetto.</p>\n                </div>\n              </div>\n\n              <!-- Tasks Tab -->\n              <div v-if=\"activeTab === 'tasks'\" class=\"space-y-4\">\n                <div v-if=\"userTasks.length > 0\">\n                  <div v-for=\"task in userTasks\" :key=\"task.id\"\n                       class=\"border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\">\n                    <div class=\"flex items-start justify-between\">\n                      <div class=\"flex-1\">\n                        <h4 class=\"font-medium text-gray-900 dark:text-white\">{{ task.name }}</h4>\n                        <p class=\"text-sm text-gray-500 dark:text-gray-400 mt-1\">{{ task.project_name }}</p>\n                        <div class=\"flex items-center mt-2 space-x-4\">\n                          <span :class=\"[\n                            'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',\n                            task.priority === 'urgent' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :\n                            task.priority === 'high' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200' :\n                            task.priority === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :\n                            'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'\n                          ]\">\n                            {{ task.priority }}\n                          </span>\n                          <span v-if=\"task.due_date\" class=\"text-xs text-gray-500 dark:text-gray-400\">\n                            Scadenza: {{ formatDate(task.due_date) }}\n                          </span>\n                        </div>\n                      </div>\n                      <span :class=\"[\n                        'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ml-4',\n                        task.status === 'done' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :\n                        task.status === 'in-progress' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :\n                        task.status === 'review' ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200' :\n                        'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'\n                      ]\">\n                        {{ task.status }}\n                      </span>\n                    </div>\n                  </div>\n                </div>\n                <div v-else class=\"text-center py-8\">\n                  <svg class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4\" />\n                  </svg>\n                  <h3 class=\"mt-2 text-sm font-medium text-gray-900 dark:text-white\">Nessun task</h3>\n                  <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">L'utente non ha task assegnati.</p>\n                </div>\n              </div>\n\n              <!-- Skills Tab -->\n              <div v-if=\"activeTab === 'skills'\" class=\"space-y-6\">\n                <div v-if=\"user.skills && user.skills.length > 0\">\n                  <!-- Skills Header with Stats -->\n                  <div class=\"flex items-center justify-between mb-4\">\n                    <div class=\"flex items-center space-x-4\">\n                      <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                        Competenze ({{ user.skills.length }})\n                      </h3>\n                      <span class=\"text-sm text-gray-500 dark:text-gray-400\">\n                        Pagina {{ skillsCurrentPage }} di {{ totalSkillsPages }}\n                      </span>\n                    </div>\n                    <div class=\"text-sm text-gray-500 dark:text-gray-400\">\n                      {{ user.skills.filter(s => s.certified).length }} certificate\n                    </div>\n                  </div>\n\n                  <!-- Skills Grid -->\n                  <div class=\"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4\">\n                    <div v-for=\"skill in paginatedSkills\" :key=\"skill.id\"\n                         class=\"border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow\">\n                      <div class=\"flex items-center justify-between mb-2\">\n                        <h4 class=\"font-medium text-gray-900 dark:text-white\">{{ skill.name }}</h4>\n                        <span v-if=\"skill.certified\" class=\"text-green-600 dark:text-green-400 text-sm font-medium\">✓ Certificato</span>\n                      </div>\n                      <p v-if=\"skill.category\" class=\"text-sm text-gray-500 dark:text-gray-400 mb-3\">{{ skill.category }}</p>\n                      <div class=\"flex items-center justify-between\">\n                        <div class=\"flex items-center space-x-2\">\n                          <span class=\"text-sm text-gray-600 dark:text-gray-400\">Livello:</span>\n                          <div class=\"flex space-x-1\">\n                            <div v-for=\"i in 5\" :key=\"i\"\n                                 class=\"w-3 h-3 rounded-full\"\n                                 :class=\"i <= skill.proficiency_level ? 'bg-blue-500' : 'bg-gray-300 dark:bg-gray-600'\"></div>\n                          </div>\n                          <span class=\"text-xs text-gray-500 dark:text-gray-400\">({{ skill.proficiency_level }}/5)</span>\n                        </div>\n                        <span v-if=\"skill.years_experience\" class=\"text-sm text-gray-500 dark:text-gray-400\">\n                          {{ skill.years_experience }}{{ skill.years_experience === 1 ? ' anno' : ' anni' }}\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- Pagination -->\n                  <div v-if=\"totalSkillsPages > 1\" class=\"flex items-center justify-between border-t border-gray-200 dark:border-gray-700 pt-4\">\n                    <div class=\"flex items-center space-x-2\">\n                      <button @click=\"skillsCurrentPage = Math.max(1, skillsCurrentPage - 1)\"\n                              :disabled=\"skillsCurrentPage === 1\"\n                              class=\"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700\">\n                        Precedente\n                      </button>\n                      <span class=\"text-sm text-gray-700 dark:text-gray-300\">\n                        Pagina {{ skillsCurrentPage }} di {{ totalSkillsPages }}\n                      </span>\n                      <button @click=\"skillsCurrentPage = Math.min(totalSkillsPages, skillsCurrentPage + 1)\"\n                              :disabled=\"skillsCurrentPage === totalSkillsPages\"\n                              class=\"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700\">\n                        Successiva\n                      </button>\n                    </div>\n                    <div class=\"text-sm text-gray-500 dark:text-gray-400\">\n                      Mostrando {{ ((skillsCurrentPage - 1) * skillsPerPage) + 1 }}-{{ Math.min(skillsCurrentPage * skillsPerPage, user.skills.length) }} di {{ user.skills.length }} competenze\n                    </div>\n                  </div>\n                </div>\n                <div v-else class=\"text-center py-8\">\n                  <svg class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\" />\n                  </svg>\n                  <h3 class=\"mt-2 text-sm font-medium text-gray-900 dark:text-white\">Nessuna competenza</h3>\n                  <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">Non sono state registrate competenze per questo utente.</p>\n                </div>\n              </div>\n\n              <!-- Timesheet Tab -->\n              <div v-if=\"activeTab === 'timesheet'\" class=\"space-y-6\">\n                <div v-if=\"userTimesheets.length > 0\">\n                  <!-- Timesheet Header with Summary -->\n                  <div class=\"flex items-center justify-between mb-4\">\n                    <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                      Timesheet Ultimi 30 Giorni\n                    </h3>\n                    <div class=\"flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400\">\n                      <span>{{ userTimesheets.length }} registrazioni</span>\n                      <span>{{ userTimesheets.reduce((sum, t) => sum + parseFloat(t.hours || 0), 0).toFixed(1) }}h totali</span>\n                    </div>\n                  </div>\n\n                  <!-- Responsive Timesheet Table -->\n                  <div class=\"overflow-x-auto shadow ring-1 ring-black ring-opacity-5 rounded-lg\">\n                    <table class=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\n                      <thead class=\"bg-gray-50 dark:bg-gray-700\">\n                        <tr>\n                          <th class=\"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">Data</th>\n                          <th class=\"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">Progetto</th>\n                          <th class=\"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">Task</th>\n                          <th class=\"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">Ore</th>\n                          <th class=\"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">Descrizione</th>\n                        </tr>\n                      </thead>\n                      <tbody class=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\n                        <tr v-for=\"timesheet in userTimesheets\" :key=\"timesheet.id\" class=\"hover:bg-gray-50 dark:hover:bg-gray-700\">\n                          <td class=\"px-4 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                            {{ formatDate(timesheet.date) }}\n                          </td>\n                          <td class=\"px-4 py-4 text-sm text-gray-900 dark:text-white\">\n                            <div class=\"max-w-xs truncate\" :title=\"timesheet.project_name\">\n                              {{ timesheet.project_name }}\n                            </div>\n                          </td>\n                          <td class=\"px-4 py-4 text-sm text-gray-500 dark:text-gray-400\">\n                            <div class=\"max-w-xs truncate\" :title=\"timesheet.task_name\">\n                              {{ timesheet.task_name || '-' }}\n                            </div>\n                          </td>\n                          <td class=\"px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white\">\n                            {{ timesheet.hours }}h\n                          </td>\n                          <td class=\"px-4 py-4 text-sm text-gray-500 dark:text-gray-400\">\n                            <div class=\"max-w-md truncate\" :title=\"timesheet.description\">\n                              {{ timesheet.description || '-' }}\n                            </div>\n                          </td>\n                        </tr>\n                      </tbody>\n                    </table>\n                  </div>\n                </div>\n                <div v-else class=\"text-center py-12\">\n                  <svg class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                  </svg>\n                  <h3 class=\"mt-2 text-sm font-medium text-gray-900 dark:text-white\">Nessun timesheet</h3>\n                  <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">Non sono stati registrati timesheet per questo utente negli ultimi 30 giorni.</p>\n                </div>\n              </div>\n\n              <!-- CV Tab -->\n              <div v-if=\"activeTab === 'cv'\" class=\"space-y-6\">\n                <!-- Current CV Section -->\n                <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n                  <div class=\"flex items-center justify-between mb-4\">\n                    <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">CV Attuale</h3>\n                    <button v-if=\"canEdit\"\n                            class=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm transition-colors duration-200\">\n                      <svg class=\"w-4 h-4 inline mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fill-rule=\"evenodd\" d=\"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z\" clip-rule=\"evenodd\"></path>\n                      </svg>\n                      Carica CV\n                    </button>\n                  </div>\n\n                  <div v-if=\"user.profile?.current_cv_path\" class=\"flex items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg\">\n                    <svg class=\"w-8 h-8 text-red-600 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fill-rule=\"evenodd\" d=\"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z\" clip-rule=\"evenodd\"></path>\n                    </svg>\n                    <div class=\"flex-1\">\n                      <p class=\"text-sm font-medium text-gray-900 dark:text-white\">CV_{{ user.full_name }}.pdf</p>\n                      <p class=\"text-xs text-gray-500 dark:text-gray-400\">\n                        Caricato il {{ formatDate(user.profile.cv_last_updated) }}\n                      </p>\n                    </div>\n                    <div class=\"flex space-x-2\">\n                      <button class=\"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300\">\n                        <svg class=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                          <path d=\"M10 12a2 2 0 100-4 2 2 0 000 4z\"></path>\n                          <path fill-rule=\"evenodd\" d=\"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z\" clip-rule=\"evenodd\"></path>\n                        </svg>\n                      </button>\n                      <button class=\"text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300\">\n                        <svg class=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                          <path fill-rule=\"evenodd\" d=\"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z\" clip-rule=\"evenodd\"></path>\n                        </svg>\n                      </button>\n                    </div>\n                  </div>\n\n                  <div v-else class=\"text-center py-8\">\n                    <svg class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                    </svg>\n                    <h3 class=\"mt-2 text-sm font-medium text-gray-900 dark:text-white\">Nessun CV caricato</h3>\n                    <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">Carica il tuo CV per completare il profilo.</p>\n                  </div>\n                </div>\n\n                <!-- Documents Section -->\n                <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n                  <div class=\"flex items-center justify-between mb-4\">\n                    <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">Documenti</h3>\n                    <button v-if=\"canEdit\"\n                            class=\"bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm transition-colors duration-200\">\n                      <svg class=\"w-4 h-4 inline mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fill-rule=\"evenodd\" d=\"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z\" clip-rule=\"evenodd\"></path>\n                      </svg>\n                      Carica Documento\n                    </button>\n                  </div>\n\n                  <div class=\"text-center py-8\">\n                    <svg class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z\" />\n                    </svg>\n                    <h3 class=\"mt-2 text-sm font-medium text-gray-900 dark:text-white\">Nessun documento</h3>\n                    <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">I documenti caricati appariranno qui.</p>\n                  </div>\n                </div>\n\n                <!-- Profile Completion Tips -->\n                <div class=\"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4\">\n                  <div class=\"flex\">\n                    <svg class=\"w-5 h-5 text-blue-400 mt-0.5 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fill-rule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\" clip-rule=\"evenodd\"></path>\n                    </svg>\n                    <div>\n                      <h3 class=\"text-sm font-medium text-blue-800 dark:text-blue-200\">Suggerimenti per completare il profilo</h3>\n                      <div class=\"mt-2 text-sm text-blue-700 dark:text-blue-300\">\n                        <ul class=\"list-disc list-inside space-y-1\">\n                          <li>Carica un CV aggiornato in formato PDF</li>\n                          <li>Aggiungi certificazioni e documenti di formazione</li>\n                          <li>Mantieni i documenti sempre aggiornati</li>\n                        </ul>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted, watch } from 'vue'\nimport { useRoute, useRouter } from 'vue-router'\nimport { usePersonnelStore } from '@/stores/personnel'\nimport { usePermissions } from '@/composables/usePermissions'\n\n// Router\nconst route = useRoute()\nconst router = useRouter()\n\n// Stores\nconst personnelStore = usePersonnelStore()\nconst { hasPermission } = usePermissions()\n\n// Reactive state\nconst user = ref(null)\nconst userProjects = ref([])\nconst userTasks = ref([])\nconst userTimesheets = ref([])\nconst loading = ref(false)\nconst error = ref(null)\nconst editMode = ref(false)\nconst saving = ref(false)\nconst activeTab = ref('projects')\n\n// Skills pagination\nconst skillsCurrentPage = ref(1)\nconst skillsPerPage = ref(6)\n\n// Edit form\nconst editForm = ref({\n  phone: '',\n  bio: '',\n  employee_id: '',\n  job_title: '',\n  employment_type: '',\n  work_location: '',\n  weekly_hours: 40,\n  address: '',\n  emergency_contact_name: '',\n  emergency_contact_phone: '',\n  emergency_contact_relationship: ''\n})\n\n// Computed properties\nconst canEdit = computed(() => {\n  if (!user.value) return false\n  try {\n    // Fix: hasPermission is a computed that returns a function\n    return hasPermission.value && typeof hasPermission.value === 'function' ? hasPermission.value('edit_personnel_data') : false\n  } catch (e) {\n    console.warn('Permission check failed:', e)\n    return false\n  }\n})\n\n// Skills pagination computed\nconst paginatedSkills = computed(() => {\n  if (!user.value?.skills) return []\n  const start = (skillsCurrentPage.value - 1) * skillsPerPage.value\n  const end = start + skillsPerPage.value\n  return user.value.skills.slice(start, end)\n})\n\nconst totalSkillsPages = computed(() => {\n  if (!user.value?.skills) return 0\n  return Math.ceil(user.value.skills.length / skillsPerPage.value)\n})\n\n// Tab configuration\nconst tabs = computed(() => [\n  {\n    id: 'projects',\n    name: 'Progetti',\n    count: userProjects.value.length\n  },\n  {\n    id: 'tasks',\n    name: 'Task',\n    count: userTasks.value.length\n  },\n  {\n    id: 'skills',\n    name: 'Competenze',\n    count: user.value?.skills?.length || 0\n  },\n  {\n    id: 'timesheet',\n    name: 'Timesheet',\n    count: userTimesheets.value.length\n  },\n  {\n    id: 'cv',\n    name: 'CV'\n  }\n])\n\n// Utility functions\nconst formatDate = (dateString) => {\n  if (!dateString) return ''\n  const date = new Date(dateString)\n  return date.toLocaleDateString('it-IT', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  })\n}\n\nconst formatDateTime = (dateString) => {\n  if (!dateString) return ''\n  return new Date(dateString).toLocaleString('it-IT')\n}\n\nconst getPriorityColor = (priority) => {\n  const colors = {\n    'low': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',\n    'medium': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',\n    'high': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'\n  }\n  return colors[priority] || colors['medium']\n}\n\nconst getStatusColor = (status) => {\n  const colors = {\n    'active': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',\n    'completed': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',\n    'on_hold': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',\n    'cancelled': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'\n  }\n  return colors[status] || colors['active']\n}\n\nconst getEmploymentTypeLabel = (type) => {\n  const labels = {\n    'full_time': 'Tempo Pieno',\n    'part_time': 'Part Time',\n    'contractor': 'Consulente',\n    'intern': 'Stagista'\n  }\n  return labels[type] || type\n}\n\nconst getWorkLocationLabel = (location) => {\n  const labels = {\n    'office': 'Ufficio',\n    'remote': 'Remoto',\n    'hybrid': 'Ibrido'\n  }\n  return labels[location] || location\n}\n\n// Tab navigation\nconst switchTab = (tabId) => {\n  activeTab.value = tabId\n  // Reset skills pagination when switching to skills tab\n  if (tabId === 'skills') {\n    skillsCurrentPage.value = 1\n  }\n}\n\n// Edit methods\nconst initEditForm = () => {\n  if (!user.value) return\n\n  editForm.value = {\n    phone: user.value.phone || '',\n    bio: user.value.bio || '',\n    employee_id: user.value.profile?.employee_id || '',\n    job_title: user.value.profile?.job_title || '',\n    employment_type: user.value.profile?.employment_type || '',\n    work_location: user.value.profile?.work_location || '',\n    weekly_hours: user.value.profile?.weekly_hours || 40,\n    address: user.value.profile?.address || '',\n    emergency_contact_name: user.value.profile?.emergency_contact_name || '',\n    emergency_contact_phone: user.value.profile?.emergency_contact_phone || '',\n    emergency_contact_relationship: user.value.profile?.emergency_contact_relationship || ''\n  }\n}\n\nconst cancelEdit = () => {\n  editMode.value = false\n  initEditForm()\n}\n\nconst saveProfile = async () => {\n  if (!user.value) return\n\n  saving.value = true\n  try {\n    const response = await fetch(`/api/personnel/users/${user.value.id}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': document.querySelector('meta[name=\"csrf-token\"]')?.getAttribute('content') || ''\n      },\n      body: JSON.stringify(editForm.value)\n    })\n\n    const data = await response.json()\n\n    if (data.success) {\n      // Update user data\n      user.value = data.data.user\n      editMode.value = false\n\n      // Show success message\n      console.log('Profilo aggiornato con successo')\n    } else {\n      throw new Error(data.message || 'Errore durante il salvataggio')\n    }\n  } catch (err) {\n    console.error('Errore durante il salvataggio:', err)\n    error.value = err.message\n  } finally {\n    saving.value = false\n  }\n}\n\n// API functions\nconst fetchUserProfile = async (userId) => {\n  loading.value = true\n  error.value = null\n\n  try {\n    const response = await fetch(`/api/personnel/users/${userId}`, {\n      credentials: 'include'\n    })\n\n    if (!response.ok) {\n      throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n\n    if (data.success) {\n      user.value = data.data.user\n      // Initialize edit form with user data\n      initEditForm()\n    } else {\n      throw new Error(data.message || 'Errore nel caricamento del profilo')\n    }\n  } catch (err) {\n    console.error('Error fetching user profile:', err)\n    error.value = err.message\n  } finally {\n    loading.value = false\n  }\n}\n\nconst fetchUserTasks = async (userId) => {\n  try {\n    const response = await fetch(`/api/tasks?assignee_id=${userId}&limit=20`, {\n      credentials: 'include'\n    })\n\n    if (!response.ok) {\n      throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n\n    if (data.success) {\n      userTasks.value = data.data.tasks || []\n    }\n  } catch (err) {\n    console.error('Error fetching user tasks:', err)\n    // Non bloccare l'interfaccia per errori sui task\n  }\n}\n\nconst fetchUserTimesheets = async (userId) => {\n  try {\n    // Fetch timesheets for the last 30 days\n    const endDate = new Date()\n    const startDate = new Date()\n    startDate.setDate(startDate.getDate() - 30)\n\n    const response = await fetch(`/api/timesheets?user_id=${userId}&start_date=${startDate.toISOString().split('T')[0]}&end_date=${endDate.toISOString().split('T')[0]}&limit=50`, {\n      credentials: 'include'\n    })\n\n    if (!response.ok) {\n      throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n\n    if (data.success) {\n      userTimesheets.value = data.data || []\n    }\n  } catch (err) {\n    console.error('Error fetching user timesheets:', err)\n    // Non bloccare l'interfaccia per errori sui timesheet\n  }\n}\n\n// Lifecycle\nonMounted(async () => {\n  const userId = route.params.id\n\n  if (!userId) {\n    error.value = 'ID utente non specificato'\n    return\n  }\n\n  // Fetch user profile\n  await fetchUserProfile(userId)\n\n  // Fetch related data\n  if (user.value) {\n    userProjects.value = user.value.projects || []\n    await fetchUserTasks(userId)\n    await fetchUserTimesheets(userId)\n  }\n})\n\n// Watch for route changes\nwatch(() => route.params.id, async (newId) => {\n  if (newId) {\n    await fetchUserProfile(newId)\n    if (user.value) {\n      userProjects.value = user.value.projects || []\n      await fetchUserTasks(newId)\n      await fetchUserTimesheets(newId)\n    }\n  }\n})\n</script>\n"}
import{r as c,f,A as I,c as a,j as e,I as G,g as x,m as E,v as H,x as U,H as R,t as i,F as Q,k as q,l as J,b as K,o as l,a as O,i as W}from"./vendor.js";import{u as X}from"./personnel.js";import{a as Y}from"./app.js";const Z={class:"department-list"},ee={class:"flex justify-between items-center mb-6"},te={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-4 mb-6"},re={class:"flex flex-col sm:flex-row gap-4"},se={class:"flex-1"},ae={class:"relative"},le={class:"sm:w-48"},oe={class:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6"},ie={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-4"},ne={class:"flex items-center"},de={class:"ml-4"},ue={class:"text-2xl font-semibold text-gray-900 dark:text-white"},ce={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-4"},ge={class:"flex items-center"},xe={class:"ml-4"},ve={class:"text-2xl font-semibold text-gray-900 dark:text-white"},me={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-4"},pe={class:"flex items-center"},he={class:"ml-4"},fe={class:"text-2xl font-semibold text-gray-900 dark:text-white"},ye={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-4"},ke={class:"flex items-center"},we={class:"ml-4"},be={class:"text-2xl font-semibold text-gray-900 dark:text-white"},_e={key:0,class:"flex justify-center items-center h-64"},ze={key:1,class:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6"},Ce={class:"flex"},Me={class:"text-sm text-red-700 dark:text-red-300 mt-1"},De={key:2,class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},Be={class:"overflow-x-auto"},Le={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},Ee={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},He={class:"px-6 py-4 whitespace-nowrap"},Te={class:"flex items-center"},Ve={class:"ml-4"},je={class:"text-sm font-medium text-gray-900 dark:text-white"},$e={key:0,class:"text-sm text-gray-500 dark:text-gray-400"},Ne={class:"px-6 py-4 whitespace-nowrap"},Se={key:0,class:"flex items-center"},Ae={class:"ml-3"},Pe={class:"text-sm font-medium text-gray-900 dark:text-white"},Fe={class:"text-sm text-gray-500 dark:text-gray-400"},Ie={key:1,class:"text-sm text-gray-500 dark:text-gray-400"},Ge={class:"px-6 py-4 whitespace-nowrap"},Ue={class:"flex items-center"},Re={class:"text-sm font-medium text-gray-900 dark:text-white"},Qe={class:"px-6 py-4 whitespace-nowrap"},qe={key:0,class:"text-sm text-gray-900 dark:text-white"},Je={key:1,class:"text-sm text-gray-500 dark:text-gray-400"},Ke={class:"px-6 py-4 whitespace-nowrap"},Oe={key:0,class:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"},We={key:1,class:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"},Xe={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},Ye={class:"flex items-center justify-end space-x-2"},Ze=["onClick"],et=["onClick"],tt={key:3,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-12 text-center"},rt={class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},st={key:0,class:"mt-6"},nt={__name:"DepartmentList",setup(at){J(),X();const{hasPermission:y}=Y(),d=c([]),k=c(!1),v=c(null),g=c(""),u=c(""),w=c(!1),T=c(null),m=f(()=>{try{return y&&typeof y=="function"?y("manage_users"):!1}catch(r){return console.warn("Permission check failed:",r),!1}}),M=f(()=>{let r=d.value;if(g.value.trim()){const t=g.value.toLowerCase();r=r.filter(o=>o.name.toLowerCase().includes(t)||o.description&&o.description.toLowerCase().includes(t)||o.manager&&o.manager.full_name.toLowerCase().includes(t))}return u.value&&(u.value==="root"?r=r.filter(t=>!t.parent_id):u.value==="sub"&&(r=r.filter(t=>t.parent_id))),r}),b=f(()=>g.value.trim()!==""||u.value!==""),p=f(()=>{const r=d.value.length,t=d.value.filter(n=>n.manager_id).length,o=d.value.reduce((n,h)=>n+(h.user_count||0),0);let s=0;try{const n=(h,F=null,_=0)=>{const B=h.filter(C=>C.parent_id===F);if(B.length===0)return _;let z=_;for(const C of B){const L=n(h,C.id,_+1);L>z&&(z=L)}return z};s=d.value.length>0?n(d.value)+1:0}catch(n){console.warn("Error calculating hierarchy levels:",n),s=1}return{totalDepartments:r,managersAssigned:t,totalEmployees:o,hierarchyLevels:s}}),V=r=>new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(r);let D=null;const j=()=>{clearTimeout(D),D=setTimeout(()=>{},300)},$=async()=>{k.value=!0,v.value=null;try{const r=await fetch("/api/personnel/departments",{credentials:"include"});if(!r.ok)throw new Error(`HTTP ${r.status}: ${r.statusText}`);const t=await r.json();if(t.success)d.value=t.data.departments||[];else throw new Error(t.message||"Errore nel caricamento dei dipartimenti")}catch(r){console.error("Error fetching departments:",r),v.value=r.message}finally{k.value=!1}},N=async r=>{if(confirm(`Sei sicuro di voler eliminare il dipartimento "${r.name}"?`))try{const t=await fetch(`/api/personnel/departments/${r.id}`,{method:"DELETE",credentials:"include"});if(!t.ok)throw new Error(`HTTP ${t.status}: ${t.statusText}`);const o=await t.json();if(o.success)d.value=d.value.filter(s=>s.id!==r.id),alert(o.message||"Dipartimento eliminato con successo");else throw new Error(o.message||"Errore nell'eliminazione del dipartimento")}catch(t){console.error("Error deleting department:",t),alert("Errore nell'eliminazione del dipartimento: "+t.message)}},S=r=>{T.value=r,w.value=!0},A=()=>{},P=()=>{g.value="",u.value=""};return I(()=>{$()}),(r,t)=>{const o=K("router-link");return l(),a("div",Z,[e("div",ee,[t[5]||(t[5]=G('<div><div class="flex items-center"><svg class="w-8 h-8 text-blue-600 dark:text-blue-400 mr-3" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z" clip-rule="evenodd"></path></svg><h1 class="text-2xl font-bold text-gray-900 dark:text-white">Gestione Dipartimenti</h1></div><p class="text-gray-600 dark:text-gray-400 mt-1">Gestisci la struttura organizzativa aziendale</p></div>',1)),m.value?(l(),a("button",{key:0,onClick:t[0]||(t[0]=s=>w.value=!0),class:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors duration-200"},t[4]||(t[4]=[e("svg",{class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z","clip-rule":"evenodd"})],-1),E(" Nuovo Dipartimento ")]))):x("",!0)]),e("div",te,[e("div",re,[e("div",se,[e("div",ae,[t[6]||(t[6]=e("svg",{class:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z","clip-rule":"evenodd"})],-1)),H(e("input",{"onUpdate:modelValue":t[1]||(t[1]=s=>g.value=s),onInput:j,type:"text",placeholder:"Cerca dipartimenti...",class:"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,544),[[U,g.value]])])]),e("div",le,[H(e("select",{"onUpdate:modelValue":t[2]||(t[2]=s=>u.value=s),onChange:A,class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},t[7]||(t[7]=[e("option",{value:""},"Tutti i livelli",-1),e("option",{value:"root"},"Solo dipartimenti principali",-1),e("option",{value:"sub"},"Solo sotto-dipartimenti",-1)]),544),[[R,u.value]])]),b.value?(l(),a("button",{key:0,onClick:P,class:"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"}," Pulisci Filtri ")):x("",!0)])]),e("div",oe,[e("div",ie,[e("div",ne,[t[9]||(t[9]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-blue-600 dark:text-blue-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z","clip-rule":"evenodd"})])])],-1)),e("div",de,[t[8]||(t[8]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400"},"Totale Dipartimenti",-1)),e("p",ue,i(p.value.totalDepartments),1)])])]),e("div",ce,[e("div",ge,[t[11]||(t[11]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-green-600 dark:text-green-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z","clip-rule":"evenodd"})])])],-1)),e("div",xe,[t[10]||(t[10]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400"},"Manager Assegnati",-1)),e("p",ve,i(p.value.managersAssigned),1)])])]),e("div",me,[e("div",pe,[t[13]||(t[13]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-purple-600 dark:text-purple-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z","clip-rule":"evenodd"})])])],-1)),e("div",he,[t[12]||(t[12]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400"},"Totale Dipendenti",-1)),e("p",fe,i(p.value.totalEmployees),1)])])]),e("div",ye,[e("div",ke,[t[15]||(t[15]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-yellow-100 dark:bg-yellow-900 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-yellow-600 dark:text-yellow-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z","clip-rule":"evenodd"})])])],-1)),e("div",we,[t[14]||(t[14]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400"},"Livelli Gerarchia",-1)),e("p",be,i(p.value.hierarchyLevels),1)])])])]),k.value?(l(),a("div",_e,t[16]||(t[16]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):v.value?(l(),a("div",ze,[e("div",Ce,[t[18]||(t[18]=e("svg",{class:"w-5 h-5 text-red-400 mr-2 mt-0.5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z","clip-rule":"evenodd"})],-1)),e("div",null,[t[17]||(t[17]=e("h3",{class:"text-sm font-medium text-red-800 dark:text-red-200"},"Errore nel caricamento dei dipartimenti",-1)),e("p",Me,i(v.value),1)])])])):M.value.length>0?(l(),a("div",De,[e("div",Be,[e("table",Le,[t[25]||(t[25]=e("thead",{class:"bg-gray-50 dark:bg-gray-700"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Dipartimento "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Manager "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Dipendenti "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Budget "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Gerarchia "),e("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Azioni ")])],-1)),e("tbody",Ee,[(l(!0),a(Q,null,q(M.value,s=>(l(),a("tr",{key:s.id,class:"hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"},[e("td",He,[e("div",Te,[t[19]||(t[19]=e("div",{class:"flex-shrink-0 w-8 h-8"},[e("div",{class:"w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-blue-600 dark:text-blue-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z","clip-rule":"evenodd"})])])],-1)),e("div",Ve,[e("div",je,i(s.name),1),s.description?(l(),a("div",$e,i(s.description),1)):x("",!0)])])]),e("td",Ne,[s.manager?(l(),a("div",Se,[t[20]||(t[20]=e("div",{class:"flex-shrink-0 w-8 h-8"},[e("div",{class:"w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-gray-600 dark:text-gray-300",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z","clip-rule":"evenodd"})])])],-1)),e("div",Ae,[e("div",Pe,i(s.manager.full_name),1),e("div",Fe,i(s.manager.email),1)])])):(l(),a("span",Ie,"Nessun manager"))]),e("td",Ge,[e("div",Ue,[e("span",Re,i(s.user_count),1),t[21]||(t[21]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400 ml-1"},"dipendenti",-1))])]),e("td",Qe,[s.budget?(l(),a("span",qe,i(V(s.budget)),1)):(l(),a("span",Je,"Non specificato"))]),e("td",Ke,[s.parent_id?(l(),a("span",Oe," Sotto-dipartimento ")):(l(),a("span",We," Dipartimento principale "))]),e("td",Xe,[e("div",Ye,[O(o,{to:`/app/personnel/departments/${s.id}`,class:"text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 transition-colors duration-200"},{default:W(()=>t[22]||(t[22]=[e("svg",{class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M10 12a2 2 0 100-4 2 2 0 000 4z"}),e("path",{"fill-rule":"evenodd",d:"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z","clip-rule":"evenodd"})],-1)])),_:2,__:[22]},1032,["to"]),m.value?(l(),a("button",{key:0,onClick:n=>S(s),class:"text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 transition-colors duration-200"},t[23]||(t[23]=[e("svg",{class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"})],-1)]),8,Ze)):x("",!0),m.value&&s.user_count===0?(l(),a("button",{key:1,onClick:n=>N(s),class:"text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 transition-colors duration-200"},t[24]||(t[24]=[e("svg",{class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M9 2a1 1 0 000 2h2a1 1 0 100-2H9z","clip-rule":"evenodd"}),e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414L8.586 12l-2.293 2.293a1 1 0 101.414 1.414L10 13.414l2.293 2.293a1 1 0 001.414-1.414L11.414 12l2.293-2.293z","clip-rule":"evenodd"})],-1)]),8,et)):x("",!0)])])]))),128))])])])])):(l(),a("div",tt,[t[27]||(t[27]=e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})],-1)),t[28]||(t[28]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun dipartimento trovato",-1)),e("p",rt,i(b.value?"Prova a modificare i filtri di ricerca.":"Inizia creando il primo dipartimento."),1),m.value&&!b.value?(l(),a("div",st,[e("button",{onClick:t[3]||(t[3]=s=>w.value=!0),class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},t[26]||(t[26]=[e("svg",{class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z","clip-rule":"evenodd"})],-1),E(" Crea Primo Dipartimento ")]))])):x("",!0)]))])}}};export{nt as default};

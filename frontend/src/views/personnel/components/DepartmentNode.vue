<template>
  <div class="department-node">
    <!-- Department Header -->
    <div class="department-header bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm p-4 mb-4 min-w-80">
      <div class="flex items-center justify-between">
        <div class="flex-1">
          <div class="flex items-center">
            <button v-if="department.subdepartments.length > 0"
                    @click="toggleExpansion"
                    class="mr-2 p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
              <svg :class="[
                'w-4 h-4 text-gray-500 transition-transform',
                isExpanded ? 'transform rotate-90' : ''
              ]" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
              </svg>
            </button>
            
            <div class="flex items-center">
              <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mr-3">
                <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z" clip-rule="evenodd"></path>
                </svg>
              </div>
              
              <div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ department.name }}</h3>
                <p v-if="department.description" class="text-sm text-gray-500 dark:text-gray-400">{{ department.description }}</p>
                <div class="flex items-center mt-1 space-x-4 text-xs text-gray-500 dark:text-gray-400">
                  <span>{{ department.employee_count }} dipendenti</span>
                  <span v-if="department.budget > 0">Budget: €{{ formatCurrency(department.budget) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Manager Info -->
        <div v-if="department.manager" class="ml-4 flex items-center">
          <div class="text-right mr-3">
            <p class="text-sm font-medium text-gray-900 dark:text-white">{{ department.manager.full_name }}</p>
            <p class="text-xs text-gray-500 dark:text-gray-400">Manager</p>
          </div>
          <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center">
            <svg class="w-4 h-4 text-purple-600 dark:text-purple-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
            </svg>
          </div>
        </div>
      </div>
      
      <!-- Employees -->
      <div v-if="department.employees.length > 0" class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
          <div v-for="employee in visibleEmployees" 
               :key="employee.id"
               @click="$emit('employee-click', employee)"
               class="flex items-center p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors">
            <div class="w-8 h-8 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center mr-3">
              <svg class="w-4 h-4 text-gray-500 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-gray-900 dark:text-white truncate">{{ employee.full_name }}</p>
              <p class="text-xs text-gray-500 dark:text-gray-400 truncate">{{ employee.position || 'Dipendente' }}</p>
            </div>
            <div v-if="employee.is_manager" class="ml-2">
              <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                Manager
              </span>
            </div>
          </div>
        </div>
        
        <!-- Show More/Less Button -->
        <div v-if="department.employees.length > maxVisibleEmployees" class="mt-3 text-center">
          <button @click="showAllEmployees = !showAllEmployees"
                  class="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300">
            {{ showAllEmployees ? 'Mostra meno' : `Mostra altri ${department.employees.length - maxVisibleEmployees}` }}
          </button>
        </div>
      </div>
    </div>
    
    <!-- Subdepartments -->
    <div v-if="isExpanded && department.subdepartments.length > 0" class="ml-8 space-y-4">
      <div class="relative">
        <!-- Connection Lines -->
        <div class="absolute -left-4 top-0 bottom-0 w-px bg-gray-300 dark:bg-gray-600"></div>
        <div class="absolute -left-4 top-6 w-4 h-px bg-gray-300 dark:bg-gray-600"></div>
        
        <DepartmentNode 
          v-for="subdept in department.subdepartments" 
          :key="subdept.id"
          :department="subdept"
          :expanded="expanded"
          :search-query="searchQuery"
          @toggle-node="$emit('toggle-node', $event)"
          @employee-click="$emit('employee-click', $event)"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

// Props
const props = defineProps({
  department: {
    type: Object,
    required: true
  },
  expanded: {
    type: Set,
    required: true
  },
  searchQuery: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['toggle-node', 'employee-click'])

// Reactive state
const showAllEmployees = ref(false)
const maxVisibleEmployees = 6

// Computed properties
const isExpanded = computed(() => props.expanded.has(props.department.id))

const visibleEmployees = computed(() => {
  if (showAllEmployees.value || props.department.employees.length <= maxVisibleEmployees) {
    return props.department.employees
  }
  return props.department.employees.slice(0, maxVisibleEmployees)
})

// Methods
const toggleExpansion = () => {
  emit('toggle-node', props.department.id)
}

const formatCurrency = (amount) => {
  return new Intl.NumberFormat('it-IT').format(amount)
}
</script>

<style scoped>
.department-node {
  position: relative;
}

.orgchart-tree {
  font-family: inherit;
}
</style>

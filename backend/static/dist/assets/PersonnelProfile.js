import{r as m,f as B,A as Y,u as Z,w as ee,c as s,g as n,j as e,t as o,m as y,z as te,v,x as p,F as h,k as f,H as O,I as P,l as ae,o as r,n as C}from"./vendor.js";import{u as se}from"./personnel.js";import{a as re}from"./app.js";const le={class:"personnel-profile"},oe={key:0,class:"flex justify-center items-center h-64"},ie={key:1,class:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6"},ne={class:"flex"},de={class:"text-sm text-red-700 dark:text-red-300 mt-1"},ue={key:2,class:"space-y-6"},ge={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},ce={class:"bg-gradient-to-r from-blue-500 to-purple-600 px-6 py-8"},xe={class:"flex items-center space-x-6"},me={class:"flex-shrink-0"},ve={class:"w-24 h-24 bg-white rounded-full flex items-center justify-center shadow-lg"},ye=["src","alt"],pe={key:1,class:"w-24 h-24 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center"},he={class:"flex-1 text-white"},fe={class:"text-3xl font-bold"},be={class:"text-blue-100 text-lg"},ke={class:"flex items-center space-x-4 mt-2"},we={key:0,class:"inline-flex items-center px-3 py-1 rounded-full text-sm bg-white/20 text-white"},_e={class:"inline-flex items-center px-3 py-1 rounded-full text-sm bg-white/20 text-white"},ze={class:"flex-shrink-0"},Ce={key:0,class:"px-6 py-4 bg-gray-50 dark:bg-gray-700"},Ve={class:"flex items-center justify-between mb-2"},Me={class:"text-sm text-gray-500 dark:text-gray-400"},je={class:"w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2"},He={class:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 mb-6"},Te={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Be={class:"flex items-center justify-between mb-4"},Pe={key:0,class:"space-y-3"},Le={class:"flex items-center"},Se={class:"text-gray-900 dark:text-white"},De={key:0,class:"flex items-center"},Ae={class:"text-gray-900 dark:text-white"},Ee={key:1,class:"flex items-center"},Ue={class:"text-gray-900 dark:text-white"},$e={key:1,class:"space-y-4"},Ne={class:"flex space-x-2"},Ie=["disabled"],Fe={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Re={key:0,class:"space-y-3"},Oe={class:"text-sm font-medium text-gray-900 dark:text-white"},qe={class:"flex items-center space-x-2"},Ge={class:"flex space-x-1"},Je={key:0,class:"text-xs text-green-600 dark:text-green-400"},We={key:0,class:"text-sm text-gray-500 dark:text-gray-400 text-center pt-2"},Xe={key:1,class:"text-gray-500 dark:text-gray-400 text-sm"},Ke={key:0,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Qe={class:"flex items-center justify-between mb-4"},Ye={key:0,class:"space-y-3"},Ze={key:0,class:"flex justify-between"},et={class:"text-sm text-gray-900 dark:text-white"},tt={key:1,class:"flex justify-between"},at={class:"text-sm text-gray-900 dark:text-white"},st={key:2,class:"flex justify-between"},rt={class:"text-sm text-gray-900 dark:text-white"},lt={key:3,class:"flex justify-between"},ot={class:"text-sm text-gray-900 dark:text-white"},it={key:4,class:"flex justify-between"},nt={class:"text-sm text-gray-900 dark:text-white"},dt={key:1,class:"space-y-4"},ut={class:"border-t border-gray-200 dark:border-gray-600 pt-4 mt-4"},gt={class:"space-y-3"},ct={class:"border-t border-gray-200 dark:border-gray-600 pt-4 mt-4"},xt={key:0,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6"},mt={key:0,class:"text-gray-700 dark:text-gray-300 text-sm leading-relaxed"},vt={class:"w-full"},yt={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},pt={class:"border-b border-gray-200 dark:border-gray-700"},ht={class:"-mb-px flex space-x-8 px-6","aria-label":"Tabs"},ft=["onClick"],bt={key:0,class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},kt={key:1,class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},wt={key:2,class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},_t={key:3,class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},zt={key:4,class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},Ct={key:5,class:"ml-2 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 py-0.5 px-2 rounded-full text-xs"},Vt={class:"p-6"},Mt={key:0,class:"space-y-6"},jt={key:0},Ht={class:"flex items-center justify-between mb-4"},Tt={class:"text-lg font-medium text-gray-900 dark:text-white"},Bt={class:"text-sm text-gray-500 dark:text-gray-400"},Pt={class:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4"},Lt={class:"flex items-start justify-between mb-3"},St={class:"flex-1"},Dt={class:"font-medium text-gray-900 dark:text-white mb-1"},At={class:"text-sm text-gray-500 dark:text-gray-400"},Et={class:"space-y-2 text-sm"},Ut={key:0,class:"flex items-center text-gray-600 dark:text-gray-400"},$t={key:1,class:"flex items-center text-gray-600 dark:text-gray-400"},Nt={key:1,class:"text-center py-12"},It={key:1,class:"space-y-6"},Ft={key:0},Rt={class:"flex items-center justify-between mb-4"},Ot={class:"text-lg font-medium text-gray-900 dark:text-white"},qt={class:"flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400"},Gt={class:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4"},Jt={class:"flex items-start justify-between mb-3"},Wt={class:"flex-1"},Xt={class:"font-medium text-gray-900 dark:text-white mb-1"},Kt={key:0,class:"text-sm text-gray-500 dark:text-gray-400"},Qt={class:"flex flex-col space-y-1 ml-2"},Yt={key:0,class:"flex items-center text-sm text-gray-500 dark:text-gray-400"},Zt={key:1,class:"text-center py-12"},ea={key:2,class:"space-y-6"},ta={key:0},aa={class:"flex items-center justify-between mb-4"},sa={class:"flex items-center space-x-4"},ra={class:"text-lg font-medium text-gray-900 dark:text-white"},la={class:"text-sm text-gray-500 dark:text-gray-400"},oa={class:"text-sm text-gray-500 dark:text-gray-400"},ia={class:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4"},na={class:"flex items-center justify-between mb-2"},da={class:"font-medium text-gray-900 dark:text-white"},ua={key:0,class:"text-green-600 dark:text-green-400 text-sm font-medium"},ga={key:0,class:"text-sm text-gray-500 dark:text-gray-400 mb-3"},ca={class:"flex items-center justify-between"},xa={class:"flex items-center space-x-2"},ma={class:"flex space-x-1"},va={class:"text-xs text-gray-500 dark:text-gray-400"},ya={key:0,class:"text-sm text-gray-500 dark:text-gray-400"},pa={key:0,class:"flex items-center justify-between border-t border-gray-200 dark:border-gray-700 pt-4"},ha={class:"flex items-center space-x-2"},fa=["disabled"],ba={class:"text-sm text-gray-700 dark:text-gray-300"},ka=["disabled"],wa={class:"text-sm text-gray-500 dark:text-gray-400"},_a={key:1,class:"text-center py-8"},za={key:3,class:"space-y-6"},Ca={key:0},Va={class:"flex items-center justify-between mb-4"},Ma={class:"flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400"},ja={class:"overflow-x-auto shadow ring-1 ring-black ring-opacity-5 rounded-lg"},Ha={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},Ta={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},Ba={class:"px-4 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},Pa={class:"px-4 py-4 text-sm text-gray-900 dark:text-white"},La=["title"],Sa={class:"px-4 py-4 text-sm text-gray-500 dark:text-gray-400"},Da=["title"],Aa={class:"px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white"},Ea={class:"px-4 py-4 text-sm text-gray-500 dark:text-gray-400"},Ua=["title"],$a={key:1,class:"text-center py-12"},Na={key:4,class:"space-y-6"},Ia={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Fa={class:"flex items-center justify-between mb-4"},Ra={key:0,class:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm transition-colors duration-200"},Oa={key:0,class:"flex items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"},qa={class:"flex-1"},Ga={class:"text-sm font-medium text-gray-900 dark:text-white"},Ja={class:"text-xs text-gray-500 dark:text-gray-400"},Wa={key:1,class:"text-center py-8"},Xa={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Ka={class:"flex items-center justify-between mb-4"},Qa={key:0,class:"bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm transition-colors duration-200"},as={__name:"PersonnelProfile",setup(Ya){const D=Z();ae(),se();const{hasPermission:L}=re(),l=m(null),b=m([]),k=m([]),_=m([]),S=m(!1),z=m(null),c=m(!1),T=m(!1),w=m("projects"),x=m(1),V=m(6),d=m({phone:"",bio:"",employee_id:"",job_title:"",employment_type:"",work_location:"",weekly_hours:40,address:"",emergency_contact_name:"",emergency_contact_phone:"",emergency_contact_relationship:""}),M=B(()=>{if(!l.value)return!1;try{return L.value&&typeof L.value=="function"?L.value("edit_personnel_data"):!1}catch(i){return console.warn("Permission check failed:",i),!1}}),q=B(()=>{var u;if(!((u=l.value)!=null&&u.skills))return[];const i=(x.value-1)*V.value,t=i+V.value;return l.value.skills.slice(i,t)}),j=B(()=>{var i;return(i=l.value)!=null&&i.skills?Math.ceil(l.value.skills.length/V.value):0}),G=B(()=>{var i,t;return[{id:"projects",name:"Progetti",count:b.value.length},{id:"tasks",name:"Task",count:k.value.length},{id:"skills",name:"Competenze",count:((t=(i=l.value)==null?void 0:i.skills)==null?void 0:t.length)||0},{id:"timesheet",name:"Timesheet",count:_.value.length},{id:"cv",name:"CV"}]}),H=i=>i?new Date(i).toLocaleDateString("it-IT",{year:"numeric",month:"long",day:"numeric"}):"",J=i=>({full_time:"Tempo Pieno",part_time:"Part Time",contractor:"Consulente",intern:"Stagista"})[i]||i,W=i=>({office:"Ufficio",remote:"Remoto",hybrid:"Ibrido"})[i]||i,X=i=>{w.value=i,i==="skills"&&(x.value=1)},A=()=>{var i,t,u,a,g,N,I,F,R;l.value&&(d.value={phone:l.value.phone||"",bio:l.value.bio||"",employee_id:((i=l.value.profile)==null?void 0:i.employee_id)||"",job_title:((t=l.value.profile)==null?void 0:t.job_title)||"",employment_type:((u=l.value.profile)==null?void 0:u.employment_type)||"",work_location:((a=l.value.profile)==null?void 0:a.work_location)||"",weekly_hours:((g=l.value.profile)==null?void 0:g.weekly_hours)||40,address:((N=l.value.profile)==null?void 0:N.address)||"",emergency_contact_name:((I=l.value.profile)==null?void 0:I.emergency_contact_name)||"",emergency_contact_phone:((F=l.value.profile)==null?void 0:F.emergency_contact_phone)||"",emergency_contact_relationship:((R=l.value.profile)==null?void 0:R.emergency_contact_relationship)||""})},K=()=>{c.value=!1,A()},Q=async()=>{var i;if(l.value){T.value=!0;try{const u=await(await fetch(`/api/personnel/users/${l.value.id}`,{method:"PUT",headers:{"Content-Type":"application/json","X-CSRFToken":((i=document.querySelector('meta[name="csrf-token"]'))==null?void 0:i.getAttribute("content"))||""},body:JSON.stringify(d.value)})).json();if(u.success)l.value=u.data.user,c.value=!1,console.log("Profilo aggiornato con successo");else throw new Error(u.message||"Errore durante il salvataggio")}catch(t){console.error("Errore durante il salvataggio:",t),z.value=t.message}finally{T.value=!1}}},E=async i=>{S.value=!0,z.value=null;try{const t=await fetch(`/api/personnel/users/${i}`,{credentials:"include"});if(!t.ok)throw new Error(`HTTP ${t.status}: ${t.statusText}`);const u=await t.json();if(u.success)l.value=u.data.user,A();else throw new Error(u.message||"Errore nel caricamento del profilo")}catch(t){console.error("Error fetching user profile:",t),z.value=t.message}finally{S.value=!1}},U=async i=>{try{const t=await fetch(`/api/tasks?assignee_id=${i}&limit=20`,{credentials:"include"});if(!t.ok)throw new Error(`HTTP ${t.status}: ${t.statusText}`);const u=await t.json();u.success&&(k.value=u.data.tasks||[])}catch(t){console.error("Error fetching user tasks:",t)}},$=async i=>{try{const t=new Date,u=new Date;u.setDate(u.getDate()-30);const a=await fetch(`/api/timesheets?user_id=${i}&start_date=${u.toISOString().split("T")[0]}&end_date=${t.toISOString().split("T")[0]}&limit=50`,{credentials:"include"});if(!a.ok)throw new Error(`HTTP ${a.status}: ${a.statusText}`);const g=await a.json();g.success&&(_.value=g.data||[])}catch(t){console.error("Error fetching user timesheets:",t)}};return Y(async()=>{const i=D.params.id;if(!i){z.value="ID utente non specificato";return}await E(i),l.value&&(b.value=l.value.projects||[],await U(i),await $(i))}),ee(()=>D.params.id,async i=>{i&&(await E(i),l.value&&(b.value=l.value.projects||[],await U(i),await $(i)))}),(i,t)=>{var u;return r(),s("div",le,[S.value?(r(),s("div",oe,t[16]||(t[16]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):z.value?(r(),s("div",ie,[e("div",ne,[t[18]||(t[18]=e("svg",{class:"w-5 h-5 text-red-400 mr-2 mt-0.5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z","clip-rule":"evenodd"})],-1)),e("div",null,[t[17]||(t[17]=e("h3",{class:"text-sm font-medium text-red-800 dark:text-red-200"},"Errore nel caricamento del profilo",-1)),e("p",de,o(z.value),1)])])])):l.value?(r(),s("div",ue,[e("div",ge,[e("div",ce,[e("div",xe,[e("div",me,[e("div",ve,[l.value.profile_image?(r(),s("img",{key:0,src:l.value.profile_image,alt:l.value.full_name,class:"w-24 h-24 rounded-full object-cover"},null,8,ye)):(r(),s("div",pe,t[19]||(t[19]=[e("svg",{class:"w-12 h-12 text-gray-400 dark:text-gray-300",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z","clip-rule":"evenodd"})],-1)])))])]),e("div",he,[e("h1",fe,o(l.value.full_name),1),e("p",be,o(l.value.position||"Posizione non specificata"),1),e("div",ke,[l.value.department?(r(),s("span",we,[t[20]||(t[20]=e("svg",{class:"w-4 h-4 mr-1",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z","clip-rule":"evenodd"})],-1)),y(" "+o(l.value.department.name),1)])):n("",!0),e("span",_e,[t[21]||(t[21]=e("svg",{class:"w-4 h-4 mr-1",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z","clip-rule":"evenodd"})],-1)),y(" "+o(l.value.role),1)])])]),e("div",ze,[M.value?(r(),s("button",{key:0,onClick:t[0]||(t[0]=a=>c.value=!c.value),class:"bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-colors duration-200"},[t[22]||(t[22]=e("svg",{class:"w-5 h-5 inline mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"})],-1)),y(" "+o(c.value?"Annulla":"Modifica"),1)])):n("",!0)])])]),l.value.profile&&l.value.profile.profile_completion!==void 0?(r(),s("div",Ce,[e("div",Ve,[t[23]||(t[23]=e("span",{class:"text-sm font-medium text-gray-700 dark:text-gray-300"},"Completamento Profilo",-1)),e("span",Me,o(l.value.profile.profile_completion)+"%",1)]),e("div",je,[e("div",{class:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:te({width:l.value.profile.profile_completion+"%"})},null,4)])])):n("",!0)]),e("div",He,[e("div",Te,[e("div",Be,[t[25]||(t[25]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Informazioni di Contatto",-1)),M.value&&!c.value?(r(),s("button",{key:0,onClick:t[1]||(t[1]=a=>c.value=!0),class:"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"},t[24]||(t[24]=[e("svg",{class:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"})],-1)]))):n("",!0)]),c.value?(r(),s("div",$e,[e("div",null,[t[29]||(t[29]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Telefono",-1)),v(e("input",{"onUpdate:modelValue":t[2]||(t[2]=a=>d.value.phone=a),type:"tel",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[p,d.value.phone]])]),e("div",null,[t[30]||(t[30]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Bio",-1)),v(e("textarea",{"onUpdate:modelValue":t[3]||(t[3]=a=>d.value.bio=a),rows:"3",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[p,d.value.bio]])]),e("div",Ne,[e("button",{onClick:Q,disabled:T.value,class:"flex-1 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-4 py-2 rounded-md transition-colors duration-200"},o(T.value?"Salvataggio...":"Salva"),9,Ie),e("button",{onClick:K,class:"flex-1 bg-gray-300 hover:bg-gray-400 dark:bg-gray-600 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-200 px-4 py-2 rounded-md transition-colors duration-200"}," Annulla ")])])):(r(),s("div",Pe,[e("div",Le,[t[26]||(t[26]=e("svg",{class:"w-5 h-5 text-gray-400 mr-3",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"}),e("path",{d:"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"})],-1)),e("span",Se,o(l.value.email),1)]),l.value.phone?(r(),s("div",De,[t[27]||(t[27]=e("svg",{class:"w-5 h-5 text-gray-400 mr-3",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"})],-1)),e("span",Ae,o(l.value.phone),1)])):n("",!0),l.value.hire_date?(r(),s("div",Ee,[t[28]||(t[28]=e("svg",{class:"w-5 h-5 text-gray-400 mr-3",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z","clip-rule":"evenodd"})],-1)),e("span",Ue,"Assunto il "+o(H(l.value.hire_date)),1)])):n("",!0)]))]),e("div",Fe,[t[31]||(t[31]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Competenze Principali",-1)),l.value.skills&&l.value.skills.length>0?(r(),s("div",Re,[(r(!0),s(h,null,f(l.value.skills.slice(0,4),a=>(r(),s("div",{key:a.id,class:"flex items-center justify-between"},[e("span",Oe,o(a.name),1),e("div",qe,[e("div",Ge,[(r(),s(h,null,f(5,g=>e("div",{key:g,class:C(["w-2 h-2 rounded-full",g<=a.proficiency_level?"bg-blue-500":"bg-gray-300 dark:bg-gray-600"])},null,2)),64))]),a.certified?(r(),s("span",Je,"✓")):n("",!0)])]))),128)),l.value.skills.length>4?(r(),s("div",We," +"+o(l.value.skills.length-4)+" altre competenze ",1)):n("",!0)])):(r(),s("div",Xe," Nessuna competenza registrata "))]),l.value.profile?(r(),s("div",Ke,[e("div",Qe,[t[33]||(t[33]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Informazioni HR",-1)),M.value&&!c.value?(r(),s("button",{key:0,onClick:t[4]||(t[4]=a=>c.value=!0),class:"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"},t[32]||(t[32]=[e("svg",{class:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"})],-1)]))):n("",!0)]),c.value?(r(),s("div",dt,[e("div",null,[t[39]||(t[39]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"ID Dipendente",-1)),v(e("input",{"onUpdate:modelValue":t[5]||(t[5]=a=>d.value.employee_id=a),type:"text",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[p,d.value.employee_id]])]),e("div",null,[t[40]||(t[40]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Titolo Lavoro",-1)),v(e("input",{"onUpdate:modelValue":t[6]||(t[6]=a=>d.value.job_title=a),type:"text",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[p,d.value.job_title]])]),e("div",null,[t[42]||(t[42]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Tipo Contratto",-1)),v(e("select",{"onUpdate:modelValue":t[7]||(t[7]=a=>d.value.employment_type=a),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},t[41]||(t[41]=[P('<option value="">Seleziona tipo</option><option value="full_time">Tempo Pieno</option><option value="part_time">Part Time</option><option value="contractor">Consulente</option><option value="intern">Stagista</option>',5)]),512),[[O,d.value.employment_type]])]),e("div",null,[t[44]||(t[44]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Modalità Lavoro",-1)),v(e("select",{"onUpdate:modelValue":t[8]||(t[8]=a=>d.value.work_location=a),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},t[43]||(t[43]=[e("option",{value:""},"Seleziona modalità",-1),e("option",{value:"office"},"Ufficio",-1),e("option",{value:"remote"},"Remoto",-1),e("option",{value:"hybrid"},"Ibrido",-1)]),512),[[O,d.value.work_location]])]),e("div",null,[t[45]||(t[45]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ore Settimanali",-1)),v(e("input",{"onUpdate:modelValue":t[9]||(t[9]=a=>d.value.weekly_hours=a),type:"number",min:"1",max:"60",step:"0.5",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[p,d.value.weekly_hours]])]),e("div",ut,[t[49]||(t[49]=e("h4",{class:"text-sm font-medium text-gray-900 dark:text-white mb-3"},"Contatto di Emergenza",-1)),e("div",gt,[e("div",null,[t[46]||(t[46]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Nome",-1)),v(e("input",{"onUpdate:modelValue":t[10]||(t[10]=a=>d.value.emergency_contact_name=a),type:"text",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[p,d.value.emergency_contact_name]])]),e("div",null,[t[47]||(t[47]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Telefono",-1)),v(e("input",{"onUpdate:modelValue":t[11]||(t[11]=a=>d.value.emergency_contact_phone=a),type:"tel",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[p,d.value.emergency_contact_phone]])]),e("div",null,[t[48]||(t[48]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Relazione",-1)),v(e("input",{"onUpdate:modelValue":t[12]||(t[12]=a=>d.value.emergency_contact_relationship=a),type:"text",placeholder:"es. Coniuge, Genitore, Fratello",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[p,d.value.emergency_contact_relationship]])])])]),e("div",ct,[t[51]||(t[51]=e("h4",{class:"text-sm font-medium text-gray-900 dark:text-white mb-3"},"Indirizzo",-1)),e("div",null,[t[50]||(t[50]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Indirizzo Completo",-1)),v(e("textarea",{"onUpdate:modelValue":t[13]||(t[13]=a=>d.value.address=a),rows:"2",placeholder:"Via, Città, CAP, Provincia",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[p,d.value.address]])])])])):(r(),s("div",Ye,[l.value.profile.employee_id?(r(),s("div",Ze,[t[34]||(t[34]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"ID Dipendente:",-1)),e("span",et,o(l.value.profile.employee_id),1)])):n("",!0),l.value.profile.job_title?(r(),s("div",tt,[t[35]||(t[35]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Titolo:",-1)),e("span",at,o(l.value.profile.job_title),1)])):n("",!0),l.value.profile.employment_type?(r(),s("div",st,[t[36]||(t[36]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Tipo Contratto:",-1)),e("span",rt,o(J(l.value.profile.employment_type)),1)])):n("",!0),l.value.profile.work_location?(r(),s("div",lt,[t[37]||(t[37]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Modalità Lavoro:",-1)),e("span",ot,o(W(l.value.profile.work_location)),1)])):n("",!0),l.value.profile.weekly_hours?(r(),s("div",it,[t[38]||(t[38]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Ore Settimanali:",-1)),e("span",nt,o(l.value.profile.weekly_hours)+"h",1)])):n("",!0)]))])):n("",!0)]),l.value.bio||c.value?(r(),s("div",xt,[t[52]||(t[52]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Bio",-1)),c.value?n("",!0):(r(),s("p",mt,o(l.value.bio||"Nessuna bio disponibile"),1))])):n("",!0),e("div",vt,[e("div",yt,[e("div",pt,[e("nav",ht,[(r(!0),s(h,null,f(G.value,a=>(r(),s("button",{key:a.id,onClick:g=>X(a.id),class:C([w.value===a.id?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300","whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center"])},[a.id==="projects"?(r(),s("svg",bt,t[53]||(t[53]=[e("path",{"fill-rule":"evenodd",d:"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z","clip-rule":"evenodd"},null,-1)]))):a.id==="tasks"?(r(),s("svg",kt,t[54]||(t[54]=[e("path",{"fill-rule":"evenodd",d:"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z","clip-rule":"evenodd"},null,-1)]))):a.id==="skills"?(r(),s("svg",wt,t[55]||(t[55]=[e("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"},null,-1)]))):a.id==="timesheet"?(r(),s("svg",_t,t[56]||(t[56]=[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z","clip-rule":"evenodd"},null,-1)]))):a.id==="cv"?(r(),s("svg",zt,t[57]||(t[57]=[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z","clip-rule":"evenodd"},null,-1)]))):n("",!0),y(" "+o(a.name)+" ",1),a.count!==void 0?(r(),s("span",Ct,o(a.count),1)):n("",!0)],10,ft))),128))])]),e("div",Vt,[w.value==="projects"?(r(),s("div",Mt,[b.value.length>0?(r(),s("div",jt,[e("div",Ht,[e("h3",Tt," Progetti Assegnati ("+o(b.value.length)+") ",1),e("div",Bt,o(b.value.filter(a=>a.status==="active").length)+" attivi ",1)]),e("div",Pt,[(r(!0),s(h,null,f(b.value,a=>(r(),s("div",{key:a.id,class:"border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-all duration-200 cursor-pointer"},[e("div",Lt,[e("div",St,[e("h4",Dt,o(a.name),1),e("p",At,o(a.role||"Team Member"),1)]),e("span",{class:C(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ml-2",a.status==="active"?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200":a.status==="completed"?"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200":a.status==="on_hold"?"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200":"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"])},o(a.status),3)]),e("div",Et,[a.client?(r(),s("div",Ut,[t[58]||(t[58]=e("svg",{class:"w-4 h-4 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z","clip-rule":"evenodd"})],-1)),y(" "+o(a.client),1)])):n("",!0),a.deadline?(r(),s("div",$t,[t[59]||(t[59]=e("svg",{class:"w-4 h-4 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z","clip-rule":"evenodd"})],-1)),y(" "+o(H(a.deadline)),1)])):n("",!0)])]))),128))])])):(r(),s("div",Nt,t[60]||(t[60]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun progetto",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"L'utente non è assegnato a nessun progetto.",-1)])))])):n("",!0),w.value==="tasks"?(r(),s("div",It,[k.value.length>0?(r(),s("div",Ft,[e("div",Rt,[e("h3",Ot," Task Assegnati ("+o(k.value.length)+") ",1),e("div",qt,[e("span",null,o(k.value.filter(a=>a.status==="in-progress").length)+" in corso",1),e("span",null,o(k.value.filter(a=>a.status==="done").length)+" completati",1)])]),e("div",Gt,[(r(!0),s(h,null,f(k.value,a=>(r(),s("div",{key:a.id,class:"border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-all duration-200"},[e("div",Jt,[e("div",Wt,[e("h4",Xt,o(a.name),1),a.project_name?(r(),s("p",Kt,o(a.project_name),1)):n("",!0)]),e("div",Qt,[e("span",{class:C(["inline-flex items-center px-2 py-0.5 rounded text-xs font-medium",a.priority==="urgent"?"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200":a.priority==="high"?"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200":a.priority==="medium"?"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200":"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"])},o(a.priority),3),e("span",{class:C(["inline-flex items-center px-2 py-0.5 rounded text-xs font-medium",a.status==="done"?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200":a.status==="in-progress"?"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200":a.status==="review"?"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200":"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"])},o(a.status),3)])]),a.due_date?(r(),s("div",Yt,[t[61]||(t[61]=e("svg",{class:"w-4 h-4 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z","clip-rule":"evenodd"})],-1)),y(" Scadenza: "+o(H(a.due_date)),1)])):n("",!0)]))),128))])])):(r(),s("div",Zt,t[62]||(t[62]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun task",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"Non sono stati assegnati task a questo utente.",-1)])))])):n("",!0),w.value==="skills"?(r(),s("div",ea,[l.value.skills&&l.value.skills.length>0?(r(),s("div",ta,[e("div",aa,[e("div",sa,[e("h3",ra," Competenze ("+o(l.value.skills.length)+") ",1),e("span",la," Pagina "+o(x.value)+" di "+o(j.value),1)]),e("div",oa,o(l.value.skills.filter(a=>a.certified).length)+" certificate ",1)]),e("div",ia,[(r(!0),s(h,null,f(q.value,a=>(r(),s("div",{key:a.id,class:"border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow"},[e("div",na,[e("h4",da,o(a.name),1),a.certified?(r(),s("span",ua,"✓ Certificato")):n("",!0)]),a.category?(r(),s("p",ga,o(a.category),1)):n("",!0),e("div",ca,[e("div",xa,[t[63]||(t[63]=e("span",{class:"text-sm text-gray-600 dark:text-gray-400"},"Livello:",-1)),e("div",ma,[(r(),s(h,null,f(5,g=>e("div",{key:g,class:C(["w-3 h-3 rounded-full",g<=a.proficiency_level?"bg-blue-500":"bg-gray-300 dark:bg-gray-600"])},null,2)),64))]),e("span",va,"("+o(a.proficiency_level)+"/5)",1)]),a.years_experience?(r(),s("span",ya,o(a.years_experience)+o(a.years_experience===1?" anno":" anni"),1)):n("",!0)])]))),128))]),j.value>1?(r(),s("div",pa,[e("div",ha,[e("button",{onClick:t[14]||(t[14]=a=>x.value=Math.max(1,x.value-1)),disabled:x.value===1,class:"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700"}," Precedente ",8,fa),e("span",ba," Pagina "+o(x.value)+" di "+o(j.value),1),e("button",{onClick:t[15]||(t[15]=a=>x.value=Math.min(j.value,x.value+1)),disabled:x.value===j.value,class:"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700"}," Successiva ",8,ka)]),e("div",wa," Mostrando "+o((x.value-1)*V.value+1)+"-"+o(Math.min(x.value*V.value,l.value.skills.length))+" di "+o(l.value.skills.length)+" competenze ",1)])):n("",!0)])):(r(),s("div",_a,t[64]||(t[64]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessuna competenza",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"Non sono state registrate competenze per questo utente.",-1)])))])):n("",!0),w.value==="timesheet"?(r(),s("div",za,[_.value.length>0?(r(),s("div",Ca,[e("div",Va,[t[65]||(t[65]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Timesheet Ultimi 30 Giorni ",-1)),e("div",Ma,[e("span",null,o(_.value.length)+" registrazioni",1),e("span",null,o(_.value.reduce((a,g)=>a+parseFloat(g.hours||0),0).toFixed(1))+"h totali",1)])]),e("div",ja,[e("table",Ha,[t[66]||(t[66]=e("thead",{class:"bg-gray-50 dark:bg-gray-700"},[e("tr",null,[e("th",{class:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"},"Data"),e("th",{class:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"},"Progetto"),e("th",{class:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"},"Task"),e("th",{class:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"},"Ore"),e("th",{class:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"},"Descrizione")])],-1)),e("tbody",Ta,[(r(!0),s(h,null,f(_.value,a=>(r(),s("tr",{key:a.id,class:"hover:bg-gray-50 dark:hover:bg-gray-700"},[e("td",Ba,o(H(a.date)),1),e("td",Pa,[e("div",{class:"max-w-xs truncate",title:a.project_name},o(a.project_name),9,La)]),e("td",Sa,[e("div",{class:"max-w-xs truncate",title:a.task_name},o(a.task_name||"-"),9,Da)]),e("td",Aa,o(a.hours)+"h ",1),e("td",Ea,[e("div",{class:"max-w-md truncate",title:a.description},o(a.description||"-"),9,Ua)])]))),128))])])])])):(r(),s("div",$a,t[67]||(t[67]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun timesheet",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"Non sono stati registrati timesheet per questo utente negli ultimi 30 giorni.",-1)])))])):n("",!0),w.value==="cv"?(r(),s("div",Na,[e("div",Ia,[e("div",Fa,[t[69]||(t[69]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"CV Attuale",-1)),M.value?(r(),s("button",Ra,t[68]||(t[68]=[e("svg",{class:"w-4 h-4 inline mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z","clip-rule":"evenodd"})],-1),y(" Carica CV ")]))):n("",!0)]),(u=l.value.profile)!=null&&u.current_cv_path?(r(),s("div",Oa,[t[70]||(t[70]=e("svg",{class:"w-8 h-8 text-red-600 mr-3",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z","clip-rule":"evenodd"})],-1)),e("div",qa,[e("p",Ga,"CV_"+o(l.value.full_name)+".pdf",1),e("p",Ja," Caricato il "+o(H(l.value.profile.cv_last_updated)),1)]),t[71]||(t[71]=P('<div class="flex space-x-2"><button class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"><svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path><path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path></svg></button><button class="text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300"><svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg></button></div>',1))])):(r(),s("div",Wa,t[72]||(t[72]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun CV caricato",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"Carica il tuo CV per completare il profilo.",-1)])))]),e("div",Xa,[e("div",Ka,[t[74]||(t[74]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Documenti",-1)),M.value?(r(),s("button",Qa,t[73]||(t[73]=[e("svg",{class:"w-4 h-4 inline mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z","clip-rule":"evenodd"})],-1),y(" Carica Documento ")]))):n("",!0)]),t[75]||(t[75]=P('<div class="text-center py-8"><svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path></svg><h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">Nessun documento</h3><p class="mt-1 text-sm text-gray-500 dark:text-gray-400">I documenti caricati appariranno qui.</p></div>',1))]),t[76]||(t[76]=P('<div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4"><div class="flex"><svg class="w-5 h-5 text-blue-400 mt-0.5 mr-3" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path></svg><div><h3 class="text-sm font-medium text-blue-800 dark:text-blue-200">Suggerimenti per completare il profilo</h3><div class="mt-2 text-sm text-blue-700 dark:text-blue-300"><ul class="list-disc list-inside space-y-1"><li>Carica un CV aggiornato in formato PDF</li><li>Aggiungi certificazioni e documenti di formazione</li><li>Mantieni i documenti sempre aggiornati</li></ul></div></div></div></div>',1))])):n("",!0)])])])])):n("",!0)])}}};export{as as default};

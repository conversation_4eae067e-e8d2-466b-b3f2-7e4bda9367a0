{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/personnel/DepartmentCreate.vue"}, "originalCode": "<template>\n  <div>\n    <h1 class=\"text-2xl font-bold text-gray-900 dark:text-white mb-6\">➕ Nuovo Dipartimento</h1>\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n      <p class=\"text-gray-600 dark:text-gray-400\">Creazione dipartimento in fase di migrazione...</p>\n      <div class=\"mt-4\">\n        <router-link\n          to=\"/app/personnel/departments\"\n          class=\"text-primary-600 dark:text-primary-400 hover:underline\"\n        >\n          ← Torna ai Dipartimenti\n        </router-link>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\n// Placeholder component - will be implemented in Sprint 2\n</script>\n", "modifiedCode": "<template>\n  <div class=\"min-h-screen bg-gray-50 dark:bg-gray-900 py-6\">\n    <div class=\"max-w-3xl mx-auto px-4 sm:px-6 lg:px-8\">\n      <!-- Header -->\n      <div class=\"mb-8\">\n        <div class=\"flex items-center space-x-4\">\n          <router-link to=\"/app/personnel/departments\"\n                       class=\"text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200\">\n            <svg class=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fill-rule=\"evenodd\" d=\"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z\" clip-rule=\"evenodd\"></path>\n            </svg>\n          </router-link>\n          <div>\n            <h1 class=\"text-2xl font-bold text-gray-900 dark:text-white\">Crea Nuovo Dipartimento</h1>\n            <p class=\"text-gray-600 dark:text-gray-400 mt-1\">Aggiungi un nuovo dipartimento all'organizzazione</p>\n          </div>\n        </div>\n      </div>\n\n      <!-- Form -->\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg\">\n        <form @submit.prevent=\"createDepartment\" class=\"p-6 space-y-6\">\n          <!-- Nome Dipartimento -->\n          <div>\n            <label for=\"name\" class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Nome Dipartimento *\n            </label>\n            <input\n              id=\"name\"\n              v-model=\"form.name\"\n              type=\"text\"\n              required\n              class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              placeholder=\"Es. Sviluppo Software\"\n            >\n            <p v-if=\"errors.name\" class=\"mt-1 text-sm text-red-600 dark:text-red-400\">{{ errors.name }}</p>\n          </div>\n\n          <!-- Descrizione -->\n          <div>\n            <label for=\"description\" class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Descrizione\n            </label>\n            <textarea\n              id=\"description\"\n              v-model=\"form.description\"\n              rows=\"3\"\n              class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              placeholder=\"Descrizione del dipartimento e delle sue responsabilità\"\n            ></textarea>\n          </div>\n\n          <!-- Dipartimento Padre -->\n          <div>\n            <label for=\"parent_id\" class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Dipartimento Padre\n            </label>\n            <select\n              id=\"parent_id\"\n              v-model=\"form.parent_id\"\n              class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            >\n              <option value=\"\">Nessun dipartimento padre (livello radice)</option>\n              <option v-for=\"dept in departments\" :key=\"dept.id\" :value=\"dept.id\">\n                {{ dept.name }}\n              </option>\n            </select>\n          </div>\n\n          <!-- Manager -->\n          <div>\n            <label for=\"manager_id\" class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Manager\n            </label>\n            <select\n              id=\"manager_id\"\n              v-model=\"form.manager_id\"\n              class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            >\n              <option value=\"\">Nessun manager assegnato</option>\n              <option v-for=\"user in users\" :key=\"user.id\" :value=\"user.id\">\n                {{ user.first_name }} {{ user.last_name }} ({{ user.email }})\n              </option>\n            </select>\n          </div>\n"}
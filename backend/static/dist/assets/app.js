const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/ProjectCreate.js","assets/vendor.js","assets/ProjectView.js","assets/ProjectView.css","assets/ProjectEdit.js","assets/PersonnelDirectory.js","assets/PersonnelOrgChart.js","assets/SkillsMatrix.js","assets/DepartmentList.js","assets/DepartmentCreate.js","assets/DepartmentView.js","assets/DepartmentEdit.js","assets/PersonnelAdmin.js","assets/PersonnelProfile.js","assets/Admin.js","assets/KPITemplates.js","assets/Profile.js","assets/Settings.js"])))=>i.map(i=>d[i]);
import{r as M,w as oe,c as r,a as z,b as W,o as s,d as Le,e as ue,f as _,g as b,n as D,h as F,i as I,t as d,u as me,j as e,F as O,k as K,l as se,m as V,p as T,q as Pe,s as ge,v as Q,x as te,y as ie,z as ve,A as X,T as De,B as He,C as Be,D as Se,E as pe,G as qe,H as ne,I as Ee,J as Re,K as Ue,L as Ne,M as Fe}from"./vendor.js";(function(){const n=document.createElement("link").relList;if(n&&n.supports&&n.supports("modulepreload"))return;for(const a of document.querySelectorAll('link[rel="modulepreload"]'))t(a);new MutationObserver(a=>{for(const u of a)if(u.type==="childList")for(const m of u.addedNodes)m.tagName==="LINK"&&m.rel==="modulepreload"&&t(m)}).observe(document,{childList:!0,subtree:!0});function l(a){const u={};return a.integrity&&(u.integrity=a.integrity),a.referrerPolicy&&(u.referrerPolicy=a.referrerPolicy),a.crossOrigin==="use-credentials"?u.credentials="include":a.crossOrigin==="anonymous"?u.credentials="omit":u.credentials="same-origin",u}function t(a){if(a.ep)return;a.ep=!0;const u=l(a);fetch(a.href,u)}})();const Y=M(!1);let we=!1;const Ae=o=>{o?(document.documentElement.classList.add("dark"),localStorage.setItem("darkMode","true")):(document.documentElement.classList.remove("dark"),localStorage.setItem("darkMode","false"))},Oe=()=>{we||(oe(Y,o=>{Ae(o)}),we=!0)};function he(){return Oe(),{isDarkMode:Y,toggleDarkMode:()=>{Y.value=!Y.value},setDarkMode:t=>{Y.value=t},initializeDarkMode:()=>{const t=localStorage.getItem("darkMode"),a=document.documentElement.classList.contains("dark");if(t==="true")Y.value=!0;else if(t==="false")Y.value=!1;else{const x=window.matchMedia("(prefers-color-scheme: dark)").matches;Y.value=a||x}Ae(Y.value);const u=window.matchMedia("(prefers-color-scheme: dark)"),m=x=>{const h=localStorage.getItem("darkMode");(!h||h==="null")&&(Y.value=x.matches)};u.addEventListener("change",m)}}}const Ke={id:"app"},Ge={__name:"App",setup(o){const{initializeDarkMode:n}=he();return n(),(l,t)=>{const a=W("router-view");return s(),r("div",Ke,[z(a)])}}},We="modulepreload",Qe=function(o){return"/"+o},$e={},G=function(n,l,t){let a=Promise.resolve();if(l&&l.length>0){document.getElementsByTagName("link");const m=document.querySelector("meta[property=csp-nonce]"),x=(m==null?void 0:m.nonce)||(m==null?void 0:m.getAttribute("nonce"));a=Promise.allSettled(l.map(h=>{if(h=Qe(h),h in $e)return;$e[h]=!0;const p=h.endsWith(".css"),i=p?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${h}"]${i}`))return;const g=document.createElement("link");if(g.rel=p?"stylesheet":We,p||(g.as="script"),g.crossOrigin="",g.href=h,x&&g.setAttribute("nonce",x),document.head.appendChild(g),p)return new Promise((y,w)=>{g.addEventListener("load",y),g.addEventListener("error",()=>w(new Error(`Unable to preload CSS for ${h}`)))})}))}function u(m){const x=new Event("vite:preloadError",{cancelable:!0});if(x.payload=m,window.dispatchEvent(x),!x.defaultPrevented)throw m}return a.then(m=>{for(const x of m||[])x.status==="rejected"&&u(x.reason);return n().catch(u)})},J=Le.create({baseURL:"",timeout:1e4,withCredentials:!0,headers:{"Content-Type":"application/json"}});J.interceptors.request.use(o=>{var l,t;const n=(l=document.querySelector('meta[name="csrf-token"]'))==null?void 0:l.getAttribute("content");return n&&["post","put","patch","delete"].includes((t=o.method)==null?void 0:t.toLowerCase())&&(o.headers["X-CSRFToken"]=n),o},o=>Promise.reject(o));J.interceptors.response.use(o=>o,o=>{var n;return((n=o.response)==null?void 0:n.status)===401&&(localStorage.removeItem("user"),console.warn("Sessione scaduta, autenticazione richiesta")),Promise.reject(o)});const re=ue("auth",()=>{const o=localStorage.getItem("user"),n=M(o?JSON.parse(o):null),l=M(!1),t=M(null),a=M(!1),u=_(()=>!!n.value&&a.value),m={admin:["admin","manage_users","assign_roles","view_all_projects","create_project","edit_project","delete_project","assign_to_project","manage_project_tasks","manage_project_resources","approve_timesheets","view_personnel_data","edit_personnel_data","view_contracts","manage_contracts","view_crm","manage_clients","manage_proposals","view_reports","view_dashboard","submit_timesheet","view_own_timesheets","view_funding","manage_funding","view_products","manage_products","view_performance","manage_performance","view_communications","manage_communications","view_startup","manage_startup"],manager:["view_dashboard","view_all_projects","edit_project","assign_to_project","manage_project_tasks","manage_project_resources","approve_timesheets","view_personnel_data","view_crm","view_reports","submit_timesheet","view_own_timesheets","manage_clients","manage_proposals","view_funding","manage_funding","view_products","manage_products","view_performance","manage_performance","view_communications","manage_communications","view_startup","manage_startup"],employee:["view_dashboard","view_own_timesheets","submit_timesheet"],sales:["view_dashboard","view_crm","manage_clients","manage_proposals","submit_timesheet","view_own_timesheets","view_reports","view_funding","view_products","manage_products"],human_resources:["view_dashboard","manage_users","view_personnel_data","edit_personnel_data","view_contracts","manage_contracts","submit_timesheet","view_own_timesheets","view_reports","view_funding","manage_funding","view_performance","manage_performance","view_communications","manage_communications","view_startup","manage_startup"]},x=S=>!n.value||!n.value.role?!1:n.value.role==="admin"?!0:(m[n.value.role]||[]).includes(S),h=()=>{var S,L;console.log("Current user:",n.value),console.log("User role:",(S=n.value)==null?void 0:S.role),console.log("Has admin permission:",x("admin")),console.log("Available permissions for role:",m[(L=n.value)==null?void 0:L.role])};async function p(S){var L,E;l.value=!0,t.value=null;try{const C=await J.post("/api/auth/login",S);return C.data.success?(n.value=C.data.data.user,localStorage.setItem("user",JSON.stringify(n.value)),a.value=!0,{success:!0}):(t.value=C.data.message||"Errore durante il login",{success:!1,error:t.value})}catch(C){return t.value=((E=(L=C.response)==null?void 0:L.data)==null?void 0:E.message)||"Errore di connessione",{success:!1,error:t.value}}finally{l.value=!1}}async function i(S){var L,E;l.value=!0,t.value=null;try{const C=await J.post("/api/auth/register",S);return C.data.success?{success:!0,message:C.data.message}:(t.value=C.data.message||"Errore durante la registrazione",{success:!1,error:t.value})}catch(C){return t.value=((E=(L=C.response)==null?void 0:L.data)==null?void 0:E.message)||"Errore di connessione",{success:!1,error:t.value}}finally{l.value=!1}}async function g(){try{await J.post("/api/auth/logout")}catch(S){console.warn("Errore durante il logout:",S)}finally{n.value=null,a.value=!1,localStorage.removeItem("user")}}async function y(){if(a.value)return u.value;try{const S=await J.get("/api/auth/me");return S.data.success?(n.value=S.data.data.user,localStorage.setItem("user",JSON.stringify(n.value)),a.value=!0,!0):(await g(),!1)}catch{return await g(),!1}}async function w(){return n.value?await y():(a.value=!0,!1)}return{user:n,loading:l,error:t,sessionChecked:a,isAuthenticated:u,hasPermission:x,debugPermissions:h,login:p,register:i,logout:g,checkAuth:y,initializeAuth:w}}),ae=ue("tenant",()=>{const o=M(null),n=M(!1),l=M(null),t=_(()=>{var i;return((i=o.value)==null?void 0:i.company)||{}}),a=_(()=>{var i;return((i=o.value)==null?void 0:i.contact)||{}}),u=_(()=>{var i;return((i=o.value)==null?void 0:i.pages)||{}}),m=_(()=>{var i;return((i=o.value)==null?void 0:i.navigation)||{}}),x=_(()=>{var i;return((i=o.value)==null?void 0:i.footer)||{}});async function h(){try{if(n.value=!0,window.TENANT_CONFIG){o.value=window.TENANT_CONFIG;return}const i=await fetch("/api/config/tenant");o.value=await i.json()}catch(i){l.value="Errore nel caricamento della configurazione",console.error("Errore caricamento tenant config:",i)}finally{n.value=!1}}function p(i,g={}){if(!i||typeof i!="string")return i;let y=i;const w={"company.name":t.value.name||"DatVinci","company.tagline":t.value.tagline||"","company.description":t.value.description||"","company.mission":t.value.mission||"","company.vision":t.value.vision||"","company.founded":t.value.founded||"","company.team_size":t.value.team_size||"","contact.email":a.value.email||"","contact.phone":a.value.phone||"","contact.address":a.value.address||"",current_year:new Date().getFullYear().toString(),...g};for(const[S,L]of Object.entries(w)){const E=new RegExp(`\\{${S}\\}`,"g");y=y.replace(E,L||"")}return y}return{config:o,loading:n,error:l,company:t,contact:a,pages:u,navigation:m,footer:x,loadConfig:h,interpolateText:p}});function Ve(){const o=re(),n=_(()=>w=>o.hasPermission(w)),l=_(()=>{var w;return((w=o.user)==null?void 0:w.role)||null}),t=_(()=>l.value==="admin"),a=_(()=>l.value==="manager"),u=_(()=>l.value==="employee"),m=_(()=>l.value==="sales"),x=_(()=>l.value==="human_resources"),h=_(()=>n.value("create_project")||n.value("edit_project")||n.value("delete_project")),p=_(()=>n.value("manage_users")||n.value("assign_roles")),i=_(()=>n.value("view_all_projects")),g=_(()=>n.value("view_personnel_data")||n.value("edit_personnel_data")),y=_(()=>n.value("approve_timesheets"));return{hasPermission:n,userRole:l,isAdmin:t,isManager:a,isEmployee:u,isSales:m,isHR:x,canManageProjects:h,canManageUsers:p,canViewAllProjects:i,canManagePersonnel:g,canApproveTimesheets:y}}const Je={key:0,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"},Ye={key:1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"},Xe={key:2,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"},Ze={key:3,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"},et={key:4,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"},tt={key:5,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"},st={key:6,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"},rt={key:7,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"},at={key:8,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"},ot={key:9,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"},nt={key:10,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"},it={key:11,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"},lt={key:12,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"},dt={key:13,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"},ct={key:14,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"},ut={key:15,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"},mt={key:16,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"},pt={key:17,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"},gt={key:18,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"},vt={key:19,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"},de={__name:"SidebarIcon",props:{icon:{type:String,required:!0},className:{type:String,default:"h-5 w-5"}},setup(o){return(n,l)=>(s(),r("svg",{class:D(o.className),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[o.icon==="dashboard"?(s(),r("path",Je)):o.icon==="projects"?(s(),r("path",Ye)):o.icon==="users"?(s(),r("path",Xe)):o.icon==="clients"?(s(),r("path",Ze)):o.icon==="products"?(s(),r("path",et)):o.icon==="reports"?(s(),r("path",tt)):o.icon==="settings"?(s(),r("path",st)):b("",!0),o.icon==="settings"?(s(),r("path",rt)):o.icon==="user-management"?(s(),r("path",at)):o.icon==="communications"?(s(),r("path",ot)):o.icon==="funding"?(s(),r("path",nt)):o.icon==="reporting"?(s(),r("path",it)):o.icon==="team"?(s(),r("path",lt)):o.icon==="directory"?(s(),r("path",dt)):o.icon==="orgchart"?(s(),r("path",ct)):o.icon==="skills"?(s(),r("path",ut)):o.icon==="departments"?(s(),r("path",mt)):o.icon==="admin"?(s(),r("path",pt)):o.icon==="user-profile"?(s(),r("path",gt)):(s(),r("path",vt))],2))}},ht={key:0,class:"truncate"},ft={key:0,class:"truncate"},ee={__name:"SidebarNavItem",props:{item:{type:Object,required:!0},isCollapsed:{type:Boolean,default:!1}},emits:["click"],setup(o){const n=_(()=>["text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-primary-600 dark:hover:text-primary-400"]);return(l,t)=>{const a=W("router-link");return s(),r("div",null,[o.item.path!=="#"?(s(),F(a,{key:0,to:o.item.path,class:D(["group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150",[n.value,{"justify-center":o.isCollapsed}]]),"active-class":"text-primary-600 bg-primary-50 border-r-2 border-primary-600",onClick:t[0]||(t[0]=u=>l.$emit("click"))},{default:I(()=>[z(de,{icon:o.item.icon,class:D(["flex-shrink-0 h-6 w-6",{"mr-0":o.isCollapsed,"mr-3":!o.isCollapsed}])},null,8,["icon","class"]),o.isCollapsed?b("",!0):(s(),r("span",ht,d(o.item.name),1))]),_:1},8,["to","class"])):(s(),r("div",{key:1,class:D(["group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150 cursor-not-allowed opacity-75",["text-gray-400 hover:text-gray-500",{"justify-center":o.isCollapsed}]])},[z(de,{icon:o.item.icon,class:D(["flex-shrink-0 h-6 w-6",{"mr-0":o.isCollapsed,"mr-3":!o.isCollapsed}])},null,8,["icon","class"]),o.isCollapsed?b("",!0):(s(),r("span",ft,d(o.item.name),1))],2))])}}},xt={key:0,class:"flex-1 text-left truncate"},yt={key:0,class:"ml-6 space-y-1 mt-1"},_t={class:"truncate"},Ce={__name:"SidebarNavItemCollapsible",props:{item:{type:Object,required:!0},isCollapsed:{type:Boolean,default:!1}},emits:["click"],setup(o){const n=o,l=me(),t=re(),a=M(!1),u=_(()=>["text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-primary-600 dark:hover:text-primary-400",{"text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900":m.value}]),m=_(()=>n.item.children?n.item.children.some(i=>i.path!=="#"&&l.path.startsWith(i.path)):!1),x=_(()=>n.item.children?n.item.children.filter(i=>{var g;return i.admin?((g=t.user)==null?void 0:g.role)==="admin":!0}):[]);m.value&&(a.value=!0);function h(){n.isCollapsed||(a.value=!a.value)}function p(i){if(i.path==="#")return!1}return(i,g)=>{const y=W("router-link");return s(),r("div",null,[e("button",{onClick:h,class:D(["group w-full flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150",[u.value,{"justify-center":o.isCollapsed}]])},[z(de,{icon:o.item.icon,class:D(["flex-shrink-0 h-6 w-6",{"mr-0":o.isCollapsed,"mr-3":!o.isCollapsed}])},null,8,["icon","class"]),o.isCollapsed?b("",!0):(s(),r("span",xt,d(o.item.name),1)),o.isCollapsed?b("",!0):(s(),r("svg",{key:1,class:D([{"rotate-90":a.value},"ml-2 h-4 w-4 transition-transform duration-150"]),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},g[0]||(g[0]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"},null,-1)]),2))],2),a.value&&!o.isCollapsed?(s(),r("div",yt,[(s(!0),r(O,null,K(x.value,w=>(s(),F(y,{key:w.name,to:w.path,class:D(["group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150",w.path==="#"?"text-gray-400 dark:text-gray-500 hover:text-gray-500 dark:hover:text-gray-400 cursor-not-allowed opacity-75":"text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-primary-600 dark:hover:text-primary-400"]),"active-class":"text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900",onClick:S=>p(w)},{default:I(()=>[w.icon?(s(),F(de,{key:0,icon:w.icon,class:"flex-shrink-0 h-4 w-4 mr-2"},null,8,["icon"])):b("",!0),e("span",_t,d(w.name),1)]),_:2},1032,["to","class","onClick"]))),128))])):b("",!0)])}}},bt={class:"mt-5 flex-grow flex flex-col overflow-hidden"},kt={class:"flex-1 px-2 space-y-1"},je={__name:"SidebarNavigation",props:{isCollapsed:{type:Boolean,default:!1}},emits:["item-click"],setup(o){const{hasPermission:n}=Ve(),l=_(()=>n.value("view_dashboard")),t=_(()=>n.value("view_personnel_data")),a=_(()=>n.value("view_all_projects")),u=_(()=>n.value("view_crm")),m=_(()=>n.value("view_products")),x=_(()=>n.value("view_performance")),h=_(()=>n.value("view_communications")),p=_(()=>n.value("view_funding")),i=_(()=>n.value("view_reports")),g=_(()=>n.value("admin_access"));return(y,w)=>(s(),r("div",bt,[e("nav",kt,[l.value?(s(),F(ee,{key:0,item:{name:"Dashboard",path:"/app/dashboard",icon:"dashboard"},"is-collapsed":o.isCollapsed,onClick:w[0]||(w[0]=S=>y.$emit("item-click"))},null,8,["is-collapsed"])):b("",!0),t.value?(s(),F(Ce,{key:1,item:{name:"Personale",icon:"users",children:[{name:"Team",path:"/app/personnel",icon:"team"},{name:"Directory",path:"/app/personnel/directory",icon:"directory"},{name:"Organigramma",path:"/app/personnel/orgchart",icon:"orgchart"},{name:"Competenze",path:"/app/personnel/skills",icon:"skills"},{name:"Dipartimenti",path:"/app/personnel/departments",icon:"departments",admin:!0},{name:"Amministrazione",path:"/app/personnel/admin",icon:"admin",admin:!0}]},"is-collapsed":o.isCollapsed,onClick:w[1]||(w[1]=S=>y.$emit("item-click"))},null,8,["is-collapsed"])):b("",!0),a.value?(s(),F(ee,{key:2,item:{name:"Progetti",path:"/app/projects",icon:"projects"},"is-collapsed":o.isCollapsed,onClick:w[2]||(w[2]=S=>y.$emit("item-click"))},null,8,["is-collapsed"])):b("",!0),u.value?(s(),F(ee,{key:3,item:{name:"CRM",path:"#",icon:"clients"},"is-collapsed":o.isCollapsed,onClick:w[3]||(w[3]=S=>y.$emit("item-click"))},null,8,["is-collapsed"])):b("",!0),m.value?(s(),F(ee,{key:4,item:{name:"Prodotti",path:"#",icon:"products"},"is-collapsed":o.isCollapsed,onClick:w[4]||(w[4]=S=>y.$emit("item-click"))},null,8,["is-collapsed"])):b("",!0),x.value?(s(),F(ee,{key:5,item:{name:"Performance",path:"#",icon:"reports"},"is-collapsed":o.isCollapsed,onClick:w[5]||(w[5]=S=>y.$emit("item-click"))},null,8,["is-collapsed"])):b("",!0),h.value?(s(),F(ee,{key:6,item:{name:"Comunicazione",path:"#",icon:"communications"},"is-collapsed":o.isCollapsed,onClick:w[6]||(w[6]=S=>y.$emit("item-click"))},null,8,["is-collapsed"])):b("",!0),p.value?(s(),F(ee,{key:7,item:{name:"Finanziamenti",path:"#",icon:"funding"},"is-collapsed":o.isCollapsed,onClick:w[7]||(w[7]=S=>y.$emit("item-click"))},null,8,["is-collapsed"])):b("",!0),i.value?(s(),F(ee,{key:8,item:{name:"Rendicontazione",path:"#",icon:"reporting"},"is-collapsed":o.isCollapsed,onClick:w[8]||(w[8]=S=>y.$emit("item-click"))},null,8,["is-collapsed"])):b("",!0),g.value?(s(),F(Ce,{key:9,item:{name:"Amministrazione",icon:"settings",children:[{name:"Gestione Utenti",path:"/app/admin/users",icon:"user-management"},{name:"Template KPI",path:"/app/admin/kpi-templates",icon:"reports"}]},"is-collapsed":o.isCollapsed,onClick:w[9]||(w[9]=S=>y.$emit("item-click"))},null,8,["is-collapsed"])):b("",!0)])]))}},wt={class:"flex-shrink-0 border-t border-gray-200 p-4"},$t={class:"flex-shrink-0"},Ct={class:"h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center"},jt={class:"text-sm font-medium text-primary-700"},Mt={key:0,class:"ml-3 flex-1 min-w-0"},zt={class:"text-sm font-medium text-gray-900 truncate"},Pt={class:"text-xs text-gray-500 truncate"},St={class:"py-1"},Et={key:0,class:"mt-3 text-xs text-gray-400 text-center"},Me={__name:"SidebarFooter",props:{isCollapsed:{type:Boolean,default:!1}},setup(o){const n=se(),l=re(),t=M(!1),a=_(()=>l.user&&(l.user.name||l.user.username)||"Utente"),u=_(()=>l.user?a.value.charAt(0).toUpperCase():"U"),m=_(()=>l.user?{admin:"Amministratore",manager:"Manager",employee:"Dipendente",client:"Cliente"}[l.user.role]||l.user.role:""),x=_(()=>"1.0.0");async function h(){t.value=!1,await l.logout(),n.push("/auth/login")}return(p,i)=>{const g=W("router-link");return s(),r("div",wt,[e("div",{class:D(["flex items-center",{"justify-center":o.isCollapsed}])},[e("div",$t,[e("div",Ct,[e("span",jt,d(u.value),1)])]),o.isCollapsed?b("",!0):(s(),r("div",Mt,[e("p",zt,d(a.value),1),e("p",Pt,d(m.value),1)])),e("div",{class:D(["relative",{"ml-3":!o.isCollapsed}])},[e("button",{onClick:i[0]||(i[0]=y=>t.value=!t.value),class:"p-1 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"},i[4]||(i[4]=[e("svg",{class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zM13 12a1 1 0 11-2 0 1 1 0 012 0zM20 12a1 1 0 11-2 0 1 1 0 012 0z"})],-1)])),t.value?(s(),r("div",{key:0,onClick:i[3]||(i[3]=y=>t.value=!1),class:"origin-bottom-left fixed bottom-16 left-4 w-48 rounded-md shadow-xl bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 border border-gray-200 dark:border-gray-600",style:{"z-index":"99999"}},[e("div",St,[z(g,{to:"/app/profile",class:"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",onClick:i[1]||(i[1]=y=>t.value=!1)},{default:I(()=>i[5]||(i[5]=[V(" Il tuo profilo ")])),_:1,__:[5]}),z(g,{to:"/app/settings",class:"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",onClick:i[2]||(i[2]=y=>t.value=!1)},{default:I(()=>i[6]||(i[6]=[V(" Impostazioni ")])),_:1,__:[6]}),i[7]||(i[7]=e("hr",{class:"my-1 border-gray-200 dark:border-gray-600"},null,-1)),e("button",{onClick:h,class:"block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"}," Esci ")])])):b("",!0)],2)],2),x.value&&!o.isCollapsed?(s(),r("div",Et," v"+d(x.value),1)):b("",!0)])}}},At={class:"flex"},Vt={class:"hidden lg:flex lg:flex-shrink-0 lg:fixed lg:inset-y-0 z-10"},Tt={class:"flex flex-col flex-grow pt-5 pb-4 overflow-y-auto bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 shadow-sm"},It={class:"flex items-center flex-shrink-0 px-4"},Lt={class:"w-10 h-10 bg-primary-600 rounded flex items-center justify-center mr-3"},Dt={class:"text-white font-bold text-lg"},Ht={class:"text-xl font-semibold text-gray-900 dark:text-white"},Bt={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center"},qt={class:"text-white font-bold text-sm"},Rt={class:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},Ut=["d"],Nt={class:"flex flex-col h-full pt-5 pb-4 overflow-y-auto bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 shadow-sm"},Ft={class:"flex items-center justify-between px-4 mb-4"},Ot={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center mr-3"},Kt={class:"text-white font-bold text-sm"},Gt={class:"text-xl font-semibold text-gray-900 dark:text-white"},Wt={__name:"AppSidebar",props:{isMobileOpen:{type:Boolean,default:!1}},emits:["close","toggle-collapsed"],setup(o,{emit:n}){const l=n,t=ae(),a=M(!1),u=_(()=>t.config||{}),m=_(()=>{var i;return((i=u.value.company)==null?void 0:i.name)||"DatPortal"}),x=_(()=>m.value.split(" ").map(g=>g[0]).join("").toUpperCase().slice(0,2));function h(){a.value=!a.value,l("toggle-collapsed",a.value)}function p(){a.value&&(a.value=!1)}return(i,g)=>{const y=W("router-link");return s(),r("div",At,[e("div",Vt,[e("div",{class:D(["flex flex-col transition-all duration-300",[a.value?"w-20":"w-64"]])},[e("div",Tt,[e("div",It,[e("div",{class:D(["flex items-center",{"justify-center":a.value}])},[z(y,{to:"/app/dashboard",class:D(["flex items-center",{hidden:a.value}])},{default:I(()=>[e("div",Lt,[e("span",Dt,d(x.value),1)]),e("h3",Ht,d(m.value),1)]),_:1},8,["class"]),z(y,{to:"/app/dashboard",class:D(["flex items-center justify-center",{hidden:!a.value}])},{default:I(()=>[e("div",Bt,[e("span",qt,d(x.value),1)])]),_:1},8,["class"])],2),e("button",{onClick:h,class:"ml-auto text-gray-600 dark:text-gray-400 focus:outline-none hover:bg-gray-100 dark:hover:bg-gray-700 p-1 rounded"},[(s(),r("svg",Rt,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:a.value?"M13 5l7 7-7 7M5 5l7 7-7 7":"M11 19l-7-7 7-7m8 14l-7-7 7-7"},null,8,Ut)]))])]),z(je,{"is-collapsed":a.value,onItemClick:p},null,8,["is-collapsed"]),z(Me,{"is-collapsed":a.value},null,8,["is-collapsed"])])],2)]),e("div",{class:D(["fixed inset-y-0 left-0 z-30 w-64 bg-primary-700 transform transition-transform duration-300 ease-in-out lg:hidden",o.isMobileOpen?"translate-x-0":"-translate-x-full"])},[e("div",Nt,[e("div",Ft,[z(y,{to:"/app/dashboard",class:"flex items-center"},{default:I(()=>[e("div",Ot,[e("span",Kt,d(x.value),1)]),e("h3",Gt,d(m.value),1)]),_:1}),e("button",{onClick:g[0]||(g[0]=w=>i.$emit("close")),class:"p-2 rounded-md text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700"},g[2]||(g[2]=[e("svg",{class:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),z(je,{"is-collapsed":!1,onItemClick:g[1]||(g[1]=w=>i.$emit("close"))}),z(Me,{"is-collapsed":!1})])],2)])}}},Qt={class:"flex","aria-label":"Breadcrumb"},Jt={class:"flex items-center space-x-2 text-sm text-gray-500"},Yt={key:0,class:"mr-2"},Xt={class:"flex items-center"},Zt={key:0,class:"h-3 w-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},es=["d"],ts={key:2,class:"font-medium text-gray-900"},ss={__name:"HeaderBreadcrumbs",props:{breadcrumbs:{type:Array,required:!0}},setup(o){return(n,l)=>{const t=W("router-link");return s(),r("nav",Qt,[e("ol",Jt,[(s(!0),r(O,null,K(o.breadcrumbs,(a,u)=>(s(),r("li",{key:u,class:"flex items-center"},[u>0?(s(),r("div",Yt,l[0]||(l[0]=[e("svg",{class:"h-3 w-3 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})],-1)]))):b("",!0),a.to&&u<o.breadcrumbs.length-1?(s(),F(t,{key:1,to:a.to,class:"hover:text-gray-700 transition-colors duration-150"},{default:I(()=>[e("span",Xt,[a.icon?(s(),r("svg",Zt,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:a.icon},null,8,es)])):b("",!0),V(" "+d(a.label),1)])]),_:2},1032,["to"])):(s(),r("span",ts,d(a.label),1))]))),128))])])}}},rs={class:"flex items-center space-x-2"},as={key:0,class:"h-3 w-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},os={key:1,class:"h-3 w-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},ns={__name:"HeaderQuickActions",emits:["quick-create-project"],setup(o){const n=me(),{isDarkMode:l,toggleDarkMode:t}=he(),a=_(()=>{var u;return((u=n.name)==null?void 0:u.includes("projects"))||n.path.includes("/projects")});return(u,m)=>(s(),r("div",rs,[a.value?(s(),r("button",{key:0,onClick:m[0]||(m[0]=x=>u.$emit("quick-create-project")),class:"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},m[2]||(m[2]=[e("svg",{class:"h-3 w-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),V(" Nuovo Progetto ")]))):b("",!0),e("button",{onClick:m[1]||(m[1]=(...x)=>T(t)&&T(t)(...x)),class:"inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700",title:"Cambia tema"},[T(l)?(s(),r("svg",os,m[4]||(m[4]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"},null,-1)]))):(s(),r("svg",as,m[3]||(m[3]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"},null,-1)])))])]))}},is={class:"relative"},ls={class:"relative"},ds={key:0,class:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center"},cs={class:"py-1"},us={key:0,class:"px-4 py-8 text-center text-gray-500 text-sm"},ms={key:1,class:"max-h-64 overflow-y-auto"},ps=["onClick"],gs={class:"flex items-start"},vs={class:"flex-shrink-0"},hs={class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},fs=["d"],xs={class:"ml-3 flex-1"},ys={class:"text-sm font-medium text-gray-900"},_s={class:"text-xs text-gray-500 mt-1"},bs={class:"text-xs text-gray-400 mt-1"},ks={key:0,class:"flex-shrink-0"},ws={key:2,class:"px-4 py-2 border-t border-gray-100"},$s={__name:"HeaderNotifications",setup(o){const n=M(!1),l=M([{id:1,type:"task",title:"Nuovo task assegnato",message:'Ti è stato assegnato un nuovo task nel progetto "Website Redesign"',created_at:new Date().toISOString(),read:!1},{id:2,type:"project",title:"Progetto completato",message:'Il progetto "Mobile App" è stato completato con successo',created_at:new Date(Date.now()-36e5).toISOString(),read:!0}]),t=_(()=>l.value.filter(p=>!p.read).length);function a(p){const i={task:"h-6 w-6 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center",project:"h-6 w-6 rounded-full bg-green-100 text-green-600 flex items-center justify-center",user:"h-6 w-6 rounded-full bg-purple-100 text-purple-600 flex items-center justify-center",system:"h-6 w-6 rounded-full bg-gray-100 text-gray-600 flex items-center justify-center"};return i[p]||i.system}function u(p){const i={task:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2",project:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10",user:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z",system:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"};return i[p]||i.system}function m(p){const i=new Date(p),y=new Date-i;return y<6e4?"Adesso":y<36e5?`${Math.floor(y/6e4)}m fa`:y<864e5?`${Math.floor(y/36e5)}h fa`:i.toLocaleDateString("it-IT")}function x(p){p.read||(p.read=!0),n.value=!1}function h(){l.value.forEach(p=>p.read=!0)}return(p,i)=>(s(),r("div",is,[e("button",{onClick:i[0]||(i[0]=g=>n.value=!n.value),class:"p-2 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},[i[3]||(i[3]=e("span",{class:"sr-only"},"Visualizza notifiche",-1)),e("div",ls,[i[2]||(i[2]=e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 17h5l-5 5v-5zM10 21a2 2 0 01-2-2V7a7 7 0 1114 0v12a2 2 0 01-2 2H10z"})],-1)),t.value>0?(s(),r("span",ds,d(t.value>9?"9+":t.value),1)):b("",!0)])]),n.value?(s(),r("div",{key:0,onClick:i[1]||(i[1]=g=>n.value=!1),class:"origin-top-right absolute right-0 mt-2 w-80 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50"},[e("div",cs,[i[5]||(i[5]=e("div",{class:"px-4 py-2 border-b border-gray-100"},[e("h3",{class:"text-sm font-medium text-gray-900"},"Notifiche")],-1)),l.value.length===0?(s(),r("div",us," Nessuna notifica ")):(s(),r("div",ms,[(s(!0),r(O,null,K(l.value,g=>(s(),r("div",{key:g.id,class:"px-4 py-3 hover:bg-gray-50 cursor-pointer border-b border-gray-50 last:border-b-0",onClick:y=>x(g)},[e("div",gs,[e("div",vs,[e("div",{class:D(a(g.type))},[(s(),r("svg",hs,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:u(g.type)},null,8,fs)]))],2)]),e("div",xs,[e("p",ys,d(g.title),1),e("p",_s,d(g.message),1),e("p",bs,d(m(g.created_at)),1)]),g.read?b("",!0):(s(),r("div",ks,i[4]||(i[4]=[e("div",{class:"h-2 w-2 bg-primary-500 rounded-full"},null,-1)])))])],8,ps))),128))])),l.value.length>0?(s(),r("div",ws,[e("button",{onClick:h,class:"text-xs text-primary-600 hover:text-primary-800"}," Segna tutte come lette ")])):b("",!0)])])):b("",!0)]))}},Cs={class:"relative"},js={class:"flex items-start justify-center min-h-screen pt-16 px-4 pb-20 text-center sm:block sm:p-0"},Ms={class:"inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6"},zs={class:"flex items-center"},Ps={class:"flex-1"},Ss={key:0,class:"mt-4 max-h-64 overflow-y-auto"},Es={class:"space-y-1"},As=["onClick"],Vs={class:"flex-shrink-0"},Ts={class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Is=["d"],Ls={class:"ml-3 flex-1 min-w-0"},Ds={class:"text-sm font-medium text-gray-900 truncate"},Hs={class:"text-xs text-gray-500 truncate"},Bs={class:"ml-2 text-xs text-gray-400"},qs={key:1,class:"mt-4 text-center py-4"},Rs={key:2,class:"mt-4 text-center py-4"},Us={__name:"HeaderSearch",setup(o){const n=se(),l=M(!1),t=M(""),a=M([]),u=M(-1),m=M(!1),x=M(null),h=[{id:1,type:"project",title:"Website Redesign",description:"Progetto di redesign del sito web aziendale",path:"/app/projects/1"},{id:2,type:"person",title:"Mario Rossi",description:"Senior Developer",path:"/app/personnel/directory/1"},{id:3,type:"document",title:"Specifiche Tecniche",description:"Documento delle specifiche del progetto mobile",path:"/app/documents/1"},{id:4,type:"task",title:"Implementazione API",description:"Task per l'implementazione delle API REST",path:"/app/projects/1/tasks/5"}];oe(l,async E=>{var C;E?(await Pe(),(C=x.value)==null||C.focus()):(t.value="",a.value=[],u.value=-1)});function p(){if(!t.value.trim()){a.value=[];return}m.value=!0,setTimeout(()=>{a.value=h.filter(E=>E.title.toLowerCase().includes(t.value.toLowerCase())||E.description.toLowerCase().includes(t.value.toLowerCase())),u.value=-1,m.value=!1},200)}function i(E){if(a.value.length===0)return;const C=u.value+E;C>=0&&C<a.value.length&&(u.value=C)}function g(){u.value>=0&&a.value[u.value]&&y(a.value[u.value])}function y(E){l.value=!1,n.push(E.path)}function w(E){const C={project:"h-6 w-6 rounded bg-blue-100 text-blue-600 flex items-center justify-center",person:"h-6 w-6 rounded bg-green-100 text-green-600 flex items-center justify-center",document:"h-6 w-6 rounded bg-yellow-100 text-yellow-600 flex items-center justify-center",task:"h-6 w-6 rounded bg-purple-100 text-purple-600 flex items-center justify-center"};return C[E]||C.document}function S(E){const C={project:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10",person:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z",document:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z",task:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"};return C[E]||C.document}function L(E){return{project:"Progetto",person:"Persona",document:"Documento",task:"Task"}[E]||"Elemento"}return(E,C)=>(s(),r("div",Cs,[e("button",{onClick:C[0]||(C[0]=U=>l.value=!l.value),class:"p-2 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},C[7]||(C[7]=[e("span",{class:"sr-only"},"Cerca",-1),e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1)])),l.value?(s(),r("div",{key:0,class:"fixed inset-0 z-50 overflow-y-auto",onClick:C[6]||(C[6]=ge(U=>l.value=!1,["self"]))},[e("div",js,[C[11]||(C[11]=e("div",{class:"fixed inset-0 bg-black bg-opacity-25 transition-opacity"},null,-1)),e("div",Ms,[e("div",null,[e("div",zs,[e("div",Ps,[Q(e("input",{ref_key:"searchInput",ref:x,"onUpdate:modelValue":C[1]||(C[1]=U=>t.value=U),onInput:p,onKeydown:[C[2]||(C[2]=ie(U=>l.value=!1,["escape"])),ie(g,["enter"]),C[3]||(C[3]=ie(U=>i(-1),["up"])),C[4]||(C[4]=ie(U=>i(1),["down"]))],type:"text",placeholder:"Cerca progetti, persone, documenti...",class:"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"},null,544),[[te,t.value]])]),e("button",{onClick:C[5]||(C[5]=U=>l.value=!1),class:"ml-3 p-2 text-gray-400 hover:text-gray-600"},C[8]||(C[8]=[e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),a.value.length>0?(s(),r("div",Ss,[e("div",Es,[(s(!0),r(O,null,K(a.value,(U,P)=>(s(),r("div",{key:U.id,onClick:k=>y(U),class:D(["flex items-center px-3 py-2 rounded-md cursor-pointer",P===u.value?"bg-primary-50":"hover:bg-gray-50"])},[e("div",Vs,[e("div",{class:D(w(U.type))},[(s(),r("svg",Ts,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:S(U.type)},null,8,Is)]))],2)]),e("div",Ls,[e("p",Ds,d(U.title),1),e("p",Hs,d(U.description),1)]),e("div",Bs,d(L(U.type)),1)],10,As))),128))])])):t.value&&!m.value?(s(),r("div",qs,C[9]||(C[9]=[e("p",{class:"text-sm text-gray-500"},"Nessun risultato trovato",-1)]))):t.value?b("",!0):(s(),r("div",Rs,C[10]||(C[10]=[e("p",{class:"text-xs text-gray-400"},"Inizia a digitare per cercare...",-1)])))])])])])):b("",!0)]))}},Ns={class:"relative"},Fs={class:"h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center"},Os={class:"text-sm font-medium text-primary-700"},Ks={class:"py-1"},Gs={class:"px-4 py-2 border-b border-gray-100 dark:border-gray-700"},Ws={class:"text-sm font-medium text-gray-900 dark:text-white"},Qs={class:"text-xs text-gray-500 dark:text-gray-400"},Js={__name:"HeaderUserMenu",setup(o){const n=se(),l=re(),t=M(!1),{isDarkMode:a,toggleDarkMode:u}=he(),m=_(()=>l.user&&(l.user.name||l.user.username)||"Utente"),x=_(()=>{var i;return((i=l.user)==null?void 0:i.email)||""}),h=_(()=>l.user?m.value.charAt(0).toUpperCase():"U");async function p(){t.value=!1,await l.logout(),n.push("/auth/login")}return(i,g)=>{const y=W("router-link");return s(),r("div",Ns,[e("button",{onClick:g[0]||(g[0]=w=>t.value=!t.value),class:"flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},[g[5]||(g[5]=e("span",{class:"sr-only"},"Apri menu utente",-1)),e("div",Fs,[e("span",Os,d(h.value),1)])]),t.value?(s(),r("div",{key:0,onClick:g[4]||(g[4]=w=>t.value=!1),class:"origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 z-50"},[e("div",Ks,[e("div",Gs,[e("p",Ws,d(m.value),1),e("p",Qs,d(x.value),1)]),z(y,{to:"/app/profile",class:"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",onClick:g[1]||(g[1]=w=>t.value=!1)},{default:I(()=>g[6]||(g[6]=[V(" Il tuo profilo ")])),_:1,__:[6]}),z(y,{to:"/app/settings",class:"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",onClick:g[2]||(g[2]=w=>t.value=!1)},{default:I(()=>g[7]||(g[7]=[V(" Impostazioni ")])),_:1,__:[7]}),g[8]||(g[8]=e("div",{class:"border-t border-gray-100 dark:border-gray-700 my-1"},null,-1)),e("button",{onClick:g[3]||(g[3]=(...w)=>T(u)&&T(u)(...w)),class:"flex items-center justify-between w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"},[e("span",null,d(T(a)?"Modalità chiara":"Modalità scura"),1),e("i",{class:D([T(a)?"fas fa-sun":"fas fa-moon","text-xs"])},null,2)]),e("button",{onClick:p,class:"block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"}," Esci ")])])):b("",!0)])}}},Ys={class:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700"},Xs={class:"flex justify-between items-center px-4 py-4 sm:px-6 lg:px-8"},Zs={class:"flex items-center space-x-4"},er={class:"flex flex-col"},tr={class:"text-lg font-semibold text-gray-900 dark:text-white"},sr={class:"flex items-center space-x-4"},rr={class:"hidden md:flex items-center space-x-2"},ar={__name:"AppHeader",props:{pageTitle:{type:String,required:!0},breadcrumbs:{type:Array,default:()=>[]}},emits:["toggle-mobile-sidebar","quick-create-project"],setup(o){return(n,l)=>(s(),r("header",Ys,[e("div",Xs,[e("div",Zs,[e("button",{onClick:l[0]||(l[0]=t=>n.$emit("toggle-mobile-sidebar")),class:"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:text-gray-500 dark:hover:text-gray-300 dark:hover:bg-gray-700"},l[2]||(l[2]=[e("svg",{class:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h16"})],-1)])),e("div",er,[e("h2",tr,d(o.pageTitle),1),o.breadcrumbs.length>0?(s(),F(ss,{key:0,breadcrumbs:o.breadcrumbs},null,8,["breadcrumbs"])):b("",!0)])]),e("div",sr,[e("div",rr,[z(ns,{onQuickCreateProject:l[1]||(l[1]=t=>n.$emit("quick-create-project"))})]),z($s),z(Us),z(Js)])])]))}},or={__name:"LoadingSpinner",props:{size:{type:String,default:"md",validator:o=>["sm","md","lg","xl"].includes(o)},message:{type:String,default:""},centered:{type:Boolean,default:!0}},setup(o){const n=o,l=_(()=>{const m={sm:"20px",md:"32px",lg:"48px",xl:"64px"};return`width: ${m[n.size]}; height: ${m[n.size]};`}),t=_(()=>["flex",n.centered?"items-center justify-center":"","space-y-2"]),a=_(()=>["flex items-center justify-center"]),u=_(()=>["text-sm text-gray-600 text-center"]);return(m,x)=>(s(),r("div",{class:D(t.value)},[e("div",{class:D(a.value)},[e("div",{class:"animate-spin rounded-full border-2 border-gray-300 border-t-primary-600",style:ve(l.value)},null,4)],2),o.message?(s(),r("p",{key:0,class:D(u.value)},d(o.message),3)):b("",!0)],2))}},Te=(o,n)=>{const l=o.__vccOpts||o;for(const[t,a]of n)l[t]=a;return l},nr={class:"fixed bottom-0 right-0 z-50 p-6 space-y-4"},ir={class:"p-4"},lr={class:"flex items-start"},dr={class:"flex-shrink-0"},cr={class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},ur=["d"],mr={class:"ml-3 w-0 flex-1 pt-0.5"},pr={class:"text-sm font-medium text-gray-900"},gr={class:"mt-1 text-sm text-gray-500"},vr={class:"ml-4 flex-shrink-0 flex"},hr=["onClick"],fr={__name:"NotificationManager",setup(o){const n=M([]);function l(x){const h=Date.now(),p={id:h,type:x.type||"info",title:x.title,message:x.message,duration:x.duration||5e3};n.value.push(p),p.duration>0&&setTimeout(()=>{t(h)},p.duration)}function t(x){const h=n.value.findIndex(p=>p.id===x);h>-1&&n.value.splice(h,1)}function a(x){const h={success:"border-l-4 border-green-400",error:"border-l-4 border-red-400",warning:"border-l-4 border-yellow-400",info:"border-l-4 border-blue-400"};return h[x]||h.info}function u(x){const h={success:"h-8 w-8 rounded-full bg-green-100 text-green-600 flex items-center justify-center",error:"h-8 w-8 rounded-full bg-red-100 text-red-600 flex items-center justify-center",warning:"h-8 w-8 rounded-full bg-yellow-100 text-yellow-600 flex items-center justify-center",info:"h-8 w-8 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center"};return h[x]||h.info}function m(x){const h={success:"M5 13l4 4L19 7",error:"M6 18L18 6M6 6l12 12",warning:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-2.694-.833-3.464 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z",info:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"};return h[x]||h.info}return window.showNotification=l,X(()=>{}),(x,h)=>(s(),r("div",nr,[z(De,{name:"notification",tag:"div",class:"space-y-4"},{default:I(()=>[(s(!0),r(O,null,K(n.value,p=>(s(),r("div",{key:p.id,class:D([a(p.type),"max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden"])},[e("div",ir,[e("div",lr,[e("div",dr,[e("div",{class:D(u(p.type))},[(s(),r("svg",cr,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:m(p.type)},null,8,ur)]))],2)]),e("div",mr,[e("p",pr,d(p.title),1),e("p",gr,d(p.message),1)]),e("div",vr,[e("button",{onClick:i=>t(p.id),class:"bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},h[0]||(h[0]=[e("span",{class:"sr-only"},"Chiudi",-1),e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),8,hr)])])])],2))),128))]),_:1})]))}},xr=Te(fr,[["__scopeId","data-v-220f0827"]]),yr={class:"h-screen flex bg-gray-50 dark:bg-gray-900"},_r={class:"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900"},br={class:"py-6"},kr={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},wr={key:0,class:"mb-6"},$r={key:1,class:"flex items-center justify-center h-64"},Cr={__name:"AppLayout",setup(o){const n=me(),l=se(),t=ae(),a=M(!1),u=M(!1),m=M(!1);_(()=>t.config||{});const x=_(()=>t.config!==null),h=_(()=>{var E;return(E=n.meta)!=null&&E.title?n.meta.title:{dashboard:"Dashboard",projects:"Progetti","projects-list":"Elenco Progetti","projects-view":"Dettaglio Progetto","projects-create":"Nuovo Progetto",personnel:"Personale","personnel-directory":"Rubrica Aziendale","personnel-orgchart":"Organigramma","personnel-skills":"Competenze"}[n.name]||"DatPortal"}),p=_(()=>{var L;return(L=n.meta)!=null&&L.breadcrumbs?n.meta.breadcrumbs.map(E=>({label:E.label,to:E.to,icon:E.icon})):[]}),i=_(()=>{var L;return((L=n.meta)==null?void 0:L.hasActions)||!1});function g(){a.value=!a.value}function y(){a.value=!1}function w(L){u.value=L}function S(){l.push("/app/projects/create")}return oe(n,()=>{m.value=!0,setTimeout(()=>{m.value=!1},300)}),oe(n,()=>{y()}),X(()=>{x.value||t.loadConfig()}),(L,E)=>{const C=W("router-view");return s(),r("div",yr,[a.value?(s(),r("div",{key:0,onClick:y,class:"fixed inset-0 z-20 bg-black bg-opacity-50 lg:hidden"})):b("",!0),z(Wt,{"is-mobile-open":a.value,onClose:y,onToggleCollapsed:w},null,8,["is-mobile-open"]),e("div",{class:D(["flex flex-col flex-1 overflow-hidden transition-all duration-300",[u.value?"lg:ml-20":"lg:ml-64"]])},[z(ar,{"page-title":h.value,breadcrumbs:p.value,onToggleMobileSidebar:g,onQuickCreateProject:S},null,8,["page-title","breadcrumbs"]),e("main",_r,[e("div",br,[e("div",kr,[i.value?(s(),r("div",wr,[He(L.$slots,"page-actions")])):b("",!0),m.value?(s(),r("div",$r,[z(or)])):(s(),F(C,{key:2}))])])])],2),z(xr)])}}},jr={class:"min-h-screen bg-gray-50"},Mr={class:"bg-white shadow-sm border-b"},zr={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},Pr={class:"flex justify-between h-16"},Sr={class:"flex items-center"},Er={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center"},Ar={class:"text-white font-bold text-sm"},Vr={class:"text-xl font-semibold text-gray-900"},Tr={class:"hidden md:flex items-center space-x-8"},Ir={class:"flex items-center space-x-4 ml-8 pl-8 border-l border-gray-200"},Lr={class:"md:hidden flex items-center"},Dr={key:0,class:"md:hidden"},Hr={class:"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t"},Br={class:"bg-gray-800 text-white"},qr={class:"max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8"},Rr={class:"grid grid-cols-1 md:grid-cols-4 gap-8"},Ur={class:"col-span-1 md:col-span-2"},Nr={class:"flex items-center space-x-3 mb-4"},Fr={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center"},Or={class:"text-white font-bold text-sm"},Kr={class:"text-xl font-semibold"},Gr={class:"text-gray-300 max-w-md"},Wr={class:"space-y-2"},Qr={class:"space-y-2 text-gray-300"},Jr={key:0},Yr={key:1},Xr={key:2},Zr={class:"mt-8 pt-8 border-t border-gray-700 text-center text-gray-400"},ze={__name:"PublicLayout",setup(o){const n=ae(),l=M(!1),t=_(()=>n.config||{}),a=_(()=>{var h;return((h=t.value.company)==null?void 0:h.name)||"DatVinci"}),u=_(()=>a.value.split(" ").map(p=>p[0]).join("").toUpperCase().slice(0,2)),m=_(()=>n.config!==null),x=new Date().getFullYear();return X(()=>{m.value||n.loadConfig()}),(h,p)=>{var y,w,S,L,E,C;const i=W("router-link"),g=W("router-view");return s(),r("div",jr,[e("nav",Mr,[e("div",zr,[e("div",Pr,[e("div",Sr,[z(i,{to:"/",class:"flex items-center space-x-3"},{default:I(()=>[e("div",Er,[e("span",Ar,d(u.value),1)]),e("span",Vr,d(a.value),1)]),_:1})]),e("div",Tr,[z(i,{to:"/",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:I(()=>p[1]||(p[1]=[V(" Home ")])),_:1,__:[1]}),z(i,{to:"/about",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:I(()=>p[2]||(p[2]=[V(" Chi Siamo ")])),_:1,__:[2]}),z(i,{to:"/services",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:I(()=>p[3]||(p[3]=[V(" Servizi ")])),_:1,__:[3]}),z(i,{to:"/contact",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:I(()=>p[4]||(p[4]=[V(" Contatti ")])),_:1,__:[4]}),e("div",Ir,[z(i,{to:"/auth/login",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:I(()=>p[5]||(p[5]=[V(" Accedi ")])),_:1,__:[5]}),z(i,{to:"/auth/register",class:"bg-primary-600 text-white hover:bg-primary-700 px-4 py-2 rounded-md text-sm font-medium"},{default:I(()=>p[6]||(p[6]=[V(" Registrati ")])),_:1,__:[6]})])]),e("div",Lr,[e("button",{onClick:p[0]||(p[0]=U=>l.value=!l.value),class:"text-gray-400 hover:text-gray-500"},p[7]||(p[7]=[e("svg",{class:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h16"})],-1)]))])])]),l.value?(s(),r("div",Dr,[e("div",Hr,[z(i,{to:"/",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:I(()=>p[8]||(p[8]=[V(" Home ")])),_:1,__:[8]}),z(i,{to:"/about",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:I(()=>p[9]||(p[9]=[V(" Chi Siamo ")])),_:1,__:[9]}),z(i,{to:"/services",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:I(()=>p[10]||(p[10]=[V(" Servizi ")])),_:1,__:[10]}),z(i,{to:"/contact",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:I(()=>p[11]||(p[11]=[V(" Contatti ")])),_:1,__:[11]}),p[14]||(p[14]=e("hr",{class:"my-2"},null,-1)),z(i,{to:"/auth/login",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:I(()=>p[12]||(p[12]=[V(" Accedi ")])),_:1,__:[12]}),z(i,{to:"/auth/register",class:"block px-3 py-2 text-base font-medium bg-primary-600 text-white rounded-md"},{default:I(()=>p[13]||(p[13]=[V(" Registrati ")])),_:1,__:[13]})])])):b("",!0)]),e("main",null,[z(g)]),e("footer",Br,[e("div",qr,[e("div",Rr,[e("div",Ur,[e("div",Nr,[e("div",Fr,[e("span",Or,d(u.value),1)]),e("span",Kr,d(a.value),1)]),e("p",Gr,d(T(n).interpolateText((y=t.value.footer)==null?void 0:y.description)||((w=t.value.company)==null?void 0:w.description)||"Innovazione e tecnologia per il futuro digitale della tua azienda."),1)]),e("div",null,[p[19]||(p[19]=e("h3",{class:"text-sm font-semibold uppercase tracking-wider mb-4"},"Link Rapidi",-1)),e("ul",Wr,[e("li",null,[z(i,{to:"/",class:"text-gray-300 hover:text-white"},{default:I(()=>p[15]||(p[15]=[V("Home")])),_:1,__:[15]})]),e("li",null,[z(i,{to:"/about",class:"text-gray-300 hover:text-white"},{default:I(()=>p[16]||(p[16]=[V("Chi Siamo")])),_:1,__:[16]})]),e("li",null,[z(i,{to:"/services",class:"text-gray-300 hover:text-white"},{default:I(()=>p[17]||(p[17]=[V("Servizi")])),_:1,__:[17]})]),e("li",null,[z(i,{to:"/contact",class:"text-gray-300 hover:text-white"},{default:I(()=>p[18]||(p[18]=[V("Contatti")])),_:1,__:[18]})])])]),e("div",null,[p[20]||(p[20]=e("h3",{class:"text-sm font-semibold uppercase tracking-wider mb-4"},"Contatti",-1)),e("ul",Qr,[(S=t.value.contact)!=null&&S.email?(s(),r("li",Jr,d(t.value.contact.email),1)):b("",!0),(L=t.value.contact)!=null&&L.phone?(s(),r("li",Yr,d(t.value.contact.phone),1)):b("",!0),(E=t.value.contact)!=null&&E.address?(s(),r("li",Xr,d(t.value.contact.address),1)):b("",!0)])])]),e("div",Zr,[e("p",null,d(T(n).interpolateText((C=t.value.footer)==null?void 0:C.copyright)||`© ${T(x)} ${a.value}. Tutti i diritti riservati.`),1)])])])])}}},ea={class:"bg-white"},ta={class:"relative overflow-hidden"},sa={class:"max-w-7xl mx-auto"},ra={class:"relative z-10 pb-8 bg-white sm:pb-16 md:pb-20 lg:max-w-2xl lg:w-full lg:pb-28 xl:pb-32"},aa={class:"mt-10 mx-auto max-w-7xl px-4 sm:mt-12 sm:px-6 md:mt-16 lg:mt-20 lg:px-8 xl:mt-28"},oa={class:"sm:text-center lg:text-left"},na={class:"text-4xl tracking-tight font-extrabold text-gray-900 sm:text-5xl md:text-6xl"},ia={class:"block xl:inline"},la={class:"mt-3 text-base text-gray-500 sm:mt-5 sm:text-lg sm:max-w-xl sm:mx-auto md:mt-5 md:text-xl lg:mx-0"},da={class:"mt-5 sm:mt-8 sm:flex sm:justify-center lg:justify-start"},ca={class:"rounded-md shadow"},ua={class:"mt-3 sm:mt-0 sm:ml-3"},ma={class:"py-12 bg-white"},pa={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},ga={class:"lg:text-center"},va={class:"text-base text-primary-600 font-semibold tracking-wide uppercase"},ha={class:"mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl"},fa={key:0,class:"mt-10"},xa={class:"space-y-10 md:space-y-0 md:grid md:grid-cols-2 md:gap-x-8 md:gap-y-10"},ya={class:"absolute flex items-center justify-center h-12 w-12 rounded-md bg-primary-500 text-white"},_a={class:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},ba={key:0,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"},ka={key:1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"},wa={key:2,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"},$a={key:3,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"},Ca={class:"ml-16 text-lg leading-6 font-medium text-gray-900"},ja={class:"mt-2 ml-16 text-base text-gray-500"},Ma={__name:"Home",setup(o){const n=ae(),l=_(()=>n.config||{}),t=_(()=>{var u;return((u=l.value.pages)==null?void 0:u.home)||{}}),a=_(()=>l.value.company||{});return X(()=>{n.config||n.loadConfig()}),(u,m)=>{var h,p,i,g;const x=W("router-link");return s(),r("div",ea,[e("div",ta,[e("div",sa,[e("div",ra,[e("main",aa,[e("div",oa,[e("h1",na,[e("span",ia,d(((h=t.value.hero)==null?void 0:h.title)||"Innovazione per il futuro"),1)]),e("p",la,d(((p=t.value.hero)==null?void 0:p.subtitle)||T(n).interpolateText(a.value.description)||"Supportiamo le aziende nel loro percorso di crescita attraverso soluzioni tecnologiche all'avanguardia"),1),e("div",da,[e("div",ca,[z(x,{to:"/services",class:"w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 md:py-4 md:text-lg md:px-10"},{default:I(()=>{var y;return[V(d(((y=t.value.hero)==null?void 0:y.cta_primary)||"Scopri i nostri servizi"),1)]}),_:1})]),e("div",ua,[z(x,{to:"/contact",class:"w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 md:py-4 md:text-lg md:px-10"},{default:I(()=>{var y;return[V(d(((y=t.value.hero)==null?void 0:y.cta_secondary)||"Contattaci"),1)]}),_:1})])])])])])]),m[0]||(m[0]=e("div",{class:"lg:absolute lg:inset-y-0 lg:right-0 lg:w-1/2"},[e("div",{class:"h-56 w-full bg-gradient-to-r from-primary-400 to-primary-600 sm:h-72 md:h-96 lg:w-full lg:h-full flex items-center justify-center"},[e("svg",{class:"h-24 w-24 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})])])],-1))]),e("div",ma,[e("div",pa,[e("div",ga,[e("h2",va,d(((i=t.value.services_section)==null?void 0:i.title)||"I nostri servizi"),1),e("p",ha,d(((g=t.value.services_section)==null?void 0:g.subtitle)||"Soluzioni innovative per ogni esigenza aziendale"),1)]),a.value.platform_features?(s(),r("div",fa,[e("div",xa,[(s(!0),r(O,null,K(a.value.platform_features,y=>(s(),r("div",{key:y.title,class:"relative"},[e("div",ya,[(s(),r("svg",_a,[y.icon==="briefcase"?(s(),r("path",ba)):y.icon==="users"?(s(),r("path",ka)):y.icon==="chart"?(s(),r("path",wa)):(s(),r("path",$a))]))]),e("p",Ca,d(y.title),1),e("p",ja,d(y.description),1)]))),128))])])):b("",!0)])])])}}},za={class:"py-16 bg-white"},Pa={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},Sa={class:"text-center"},Ea={class:"text-4xl font-extrabold text-gray-900 sm:text-5xl"},Aa={class:"mt-4 text-xl text-gray-600"},Va={key:0,class:"mt-16"},Ta={class:"max-w-3xl mx-auto"},Ia={class:"text-3xl font-bold text-gray-900 text-center mb-8"},La={class:"text-lg text-gray-700 leading-relaxed"},Da={class:"mt-16 grid grid-cols-1 md:grid-cols-2 gap-12"},Ha={key:0,class:"bg-gray-50 p-8 rounded-lg"},Ba={class:"text-2xl font-bold text-gray-900 mb-4"},qa={class:"text-gray-700"},Ra={key:1,class:"bg-gray-50 p-8 rounded-lg"},Ua={class:"text-2xl font-bold text-gray-900 mb-4"},Na={class:"text-gray-700"},Fa={key:1,class:"mt-16"},Oa={class:"text-center mb-12"},Ka={class:"text-3xl font-bold text-gray-900"},Ga={class:"mt-4 text-xl text-gray-600"},Wa={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"},Qa={class:"text-lg font-semibold text-gray-900"},Ja={key:2,class:"mt-16"},Ya={class:"text-center"},Xa={class:"text-3xl font-bold text-gray-900"},Za={class:"mt-4 text-xl text-gray-600"},eo={class:"mt-8 inline-flex items-center px-6 py-3 bg-primary-50 rounded-lg"},to={class:"text-primary-900 font-medium"},so={__name:"About",setup(o){const n=ae(),l=_(()=>n.config||{}),t=_(()=>{var u;return((u=l.value.pages)==null?void 0:u.about)||{}}),a=_(()=>l.value.company||{});return X(()=>{n.config||n.loadConfig()}),(u,m)=>{var x,h;return s(),r("div",za,[e("div",Pa,[e("div",Sa,[e("h1",Ea,d(((x=t.value.hero)==null?void 0:x.title)||"Chi Siamo"),1),e("p",Aa,d(((h=t.value.hero)==null?void 0:h.subtitle)||"La nostra storia e i nostri valori"),1)]),t.value.story_section?(s(),r("div",Va,[e("div",Ta,[e("h2",Ia,d(t.value.story_section.title),1),e("p",La,d(T(n).interpolateText(t.value.story_section.content)),1)])])):b("",!0),e("div",Da,[t.value.mission_section?(s(),r("div",Ha,[e("h3",Ba,d(t.value.mission_section.title),1),e("p",qa,d(T(n).interpolateText(t.value.mission_section.content)),1)])):b("",!0),t.value.vision_section?(s(),r("div",Ra,[e("h3",Ua,d(t.value.vision_section.title),1),e("p",Na,d(T(n).interpolateText(t.value.vision_section.content)),1)])):b("",!0)]),t.value.expertise_section&&a.value.expertise?(s(),r("div",Fa,[e("div",Oa,[e("h2",Ka,d(t.value.expertise_section.title),1),e("p",Ga,d(t.value.expertise_section.subtitle),1)]),e("div",Wa,[(s(!0),r(O,null,K(a.value.expertise,p=>(s(),r("div",{key:p,class:"bg-white p-6 rounded-lg shadow-sm border border-gray-200 text-center"},[m[0]||(m[0]=e("div",{class:"w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4"},[e("svg",{class:"w-6 h-6 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])],-1)),e("h3",Qa,d(p),1)]))),128))])])):b("",!0),t.value.team_section?(s(),r("div",Ja,[e("div",Ya,[e("h2",Xa,d(t.value.team_section.title),1),e("p",Za,d(t.value.team_section.subtitle),1),e("div",eo,[m[1]||(m[1]=e("svg",{class:"w-5 h-5 text-primary-600 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})],-1)),e("span",to,d(a.value.team_size),1)])])])):b("",!0)])])}}},ro={class:"py-16 bg-white"},ao={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},oo={class:"text-center"},no={class:"text-4xl font-extrabold text-gray-900 sm:text-5xl"},io={class:"mt-4 text-xl text-gray-600"},lo={key:0,class:"mt-8 text-center"},co={class:"text-lg text-gray-700 max-w-3xl mx-auto"},uo={class:"mt-16 grid grid-cols-1 lg:grid-cols-2 gap-16"},mo={key:0},po={class:"text-2xl font-bold text-gray-900 mb-8"},go={class:"block text-sm font-medium text-gray-700 mb-2"},vo={class:"block text-sm font-medium text-gray-700 mb-2"},ho={class:"block text-sm font-medium text-gray-700 mb-2"},fo=["disabled"],xo={key:1},yo={class:"text-2xl font-bold text-gray-900 mb-8"},_o={class:"space-y-6"},bo={key:0,class:"flex items-start"},ko={class:"font-medium text-gray-900"},wo={class:"text-gray-600"},$o={key:1,class:"flex items-start"},Co={class:"font-medium text-gray-900"},jo={class:"text-gray-600"},Mo={key:2,class:"flex items-start"},zo={class:"font-medium text-gray-900"},Po={class:"text-gray-600"},So={key:3,class:"flex items-start"},Eo={class:"font-medium text-gray-900"},Ao={class:"text-gray-600"},Vo={__name:"Contact",setup(o){const n=ae(),l=_(()=>n.config||{}),t=_(()=>{var p;return((p=l.value.pages)==null?void 0:p.contact)||{}}),a=_(()=>l.value.contact||{}),u=M({name:"",email:"",message:""}),m=M(!1),x=M({text:"",type:""}),h=async()=>{var p,i;if(!u.value.name||!u.value.email||!u.value.message){x.value={text:((p=t.value.form)==null?void 0:p.error_message)||"Tutti i campi sono obbligatori",type:"error"};return}m.value=!0,x.value={text:"",type:""};try{await new Promise(g=>setTimeout(g,1e3)),x.value={text:((i=t.value.form)==null?void 0:i.success_message)||"Messaggio inviato con successo!",type:"success"},u.value={name:"",email:"",message:""}}catch{x.value={text:"Errore durante l'invio. Riprova più tardi.",type:"error"}}finally{m.value=!1}};return X(()=>{n.config||n.loadConfig()}),(p,i)=>{var g,y;return s(),r("div",ro,[e("div",ao,[e("div",oo,[e("h1",no,d(((g=t.value.hero)==null?void 0:g.title)||"Contattaci"),1),e("p",io,d(((y=t.value.hero)==null?void 0:y.subtitle)||"Siamo qui per aiutarti"),1)]),t.value.intro?(s(),r("div",lo,[e("p",co,d(t.value.intro.content),1)])):b("",!0),e("div",uo,[t.value.form?(s(),r("div",mo,[e("h2",po,d(t.value.form.title),1),e("form",{onSubmit:ge(h,["prevent"]),class:"space-y-6"},[e("div",null,[e("label",go,d(t.value.form.name_label),1),Q(e("input",{"onUpdate:modelValue":i[0]||(i[0]=w=>u.value.name=w),type:"text",required:"",class:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"},null,512),[[te,u.value.name]])]),e("div",null,[e("label",vo,d(t.value.form.email_label),1),Q(e("input",{"onUpdate:modelValue":i[1]||(i[1]=w=>u.value.email=w),type:"email",required:"",class:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"},null,512),[[te,u.value.email]])]),e("div",null,[e("label",ho,d(t.value.form.message_label),1),Q(e("textarea",{"onUpdate:modelValue":i[2]||(i[2]=w=>u.value.message=w),rows:"6",required:"",class:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"},null,512),[[te,u.value.message]])]),e("button",{type:"submit",disabled:m.value,class:"w-full bg-primary-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-primary-700 disabled:opacity-50"},d(m.value?"Invio in corso...":t.value.form.submit_button),9,fo),x.value.text?(s(),r("div",{key:0,class:D([x.value.type==="success"?"text-green-600":"text-red-600","text-sm mt-2"])},d(x.value.text),3)):b("",!0)],32)])):b("",!0),t.value.info?(s(),r("div",xo,[e("h2",yo,d(t.value.info.title),1),e("div",_o,[a.value.address?(s(),r("div",bo,[i[3]||(i[3]=e("svg",{class:"w-6 h-6 text-primary-600 mt-1 mr-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})],-1)),e("div",null,[e("h3",ko,d(t.value.info.address_label),1),e("p",wo,d(a.value.address),1)])])):b("",!0),a.value.phone?(s(),r("div",$o,[i[4]||(i[4]=e("svg",{class:"w-6 h-6 text-primary-600 mt-1 mr-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"})],-1)),e("div",null,[e("h3",Co,d(t.value.info.phone_label),1),e("p",jo,d(a.value.phone),1)])])):b("",!0),a.value.email?(s(),r("div",Mo,[i[5]||(i[5]=e("svg",{class:"w-6 h-6 text-primary-600 mt-1 mr-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})],-1)),e("div",null,[e("h3",zo,d(t.value.info.email_label),1),e("p",Po,d(a.value.email),1)])])):b("",!0),a.value.hours?(s(),r("div",So,[i[6]||(i[6]=e("svg",{class:"w-6 h-6 text-primary-600 mt-1 mr-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})],-1)),e("div",null,[e("h3",Eo,d(t.value.info.hours_label),1),e("p",Ao,d(a.value.hours),1)])])):b("",!0)])])):b("",!0)])])])}}},To={class:"py-16 bg-white"},Io={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},Lo={class:"text-center"},Do={class:"text-4xl font-extrabold text-gray-900 sm:text-5xl"},Ho={class:"mt-4 text-xl text-gray-600"},Bo={key:0,class:"mt-8 text-center"},qo={class:"text-lg text-gray-700 max-w-3xl mx-auto"},Ro={key:1,class:"mt-16"},Uo={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"},No={class:"text-xl font-bold text-gray-900 text-center mb-4"},Fo={class:"text-gray-600 text-center"},Oo={key:2,class:"mt-20"},Ko={class:"bg-primary-50 rounded-2xl p-12 text-center"},Go={class:"text-3xl font-bold text-gray-900 mb-4"},Wo={class:"text-xl text-gray-600 mb-8"},Qo={__name:"Services",setup(o){const n=ae(),l=_(()=>n.config||{}),t=_(()=>{var m;return((m=l.value.pages)==null?void 0:m.services)||{}}),a=_(()=>l.value.company||{}),u=m=>({"Sviluppo Software":"Soluzioni software personalizzate per ottimizzare i processi aziendali e migliorare l'efficienza operativa.","Intelligenza Artificiale":"Implementazione di sistemi AI avanzati per automatizzare processi e analizzare dati complessi.","Consulenza IT":"Consulenza strategica per la trasformazione digitale e l'ottimizzazione dell'infrastruttura tecnologica.","Gestione Progetti Innovativi":"Coordinamento e gestione di progetti tecnologici complessi con metodologie agili.","Supporto su Bandi e Finanziamenti":"Assistenza nella ricerca e gestione di bandi pubblici e finanziamenti per l'innovazione."})[m]||"Servizio professionale di alta qualità per supportare la crescita della tua azienda.";return X(()=>{n.config||n.loadConfig()}),(m,x)=>{var p,i;const h=W("router-link");return s(),r("div",To,[e("div",Io,[e("div",Lo,[e("h1",Do,d(((p=t.value.hero)==null?void 0:p.title)||"I nostri servizi"),1),e("p",Ho,d(((i=t.value.hero)==null?void 0:i.subtitle)||"Soluzioni complete per la tua azienda"),1)]),t.value.intro?(s(),r("div",Bo,[e("p",qo,d(t.value.intro.content),1)])):b("",!0),a.value.expertise?(s(),r("div",Ro,[e("div",Uo,[(s(!0),r(O,null,K(a.value.expertise,g=>(s(),r("div",{key:g,class:"bg-white p-8 rounded-lg shadow-lg border border-gray-200 hover:shadow-xl transition-shadow"},[x[0]||(x[0]=e("div",{class:"w-16 h-16 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-6"},[e("svg",{class:"w-8 h-8 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])],-1)),e("h3",No,d(g),1),e("p",Fo,d(u(g)),1)]))),128))])])):b("",!0),t.value.cta?(s(),r("div",Oo,[e("div",Ko,[e("h2",Go,d(t.value.cta.title),1),e("p",Wo,d(t.value.cta.subtitle),1),z(h,{to:"/contact",class:"inline-flex items-center px-8 py-4 bg-primary-600 text-white font-medium rounded-lg hover:bg-primary-700 transition-colors"},{default:I(()=>[V(d(t.value.cta.button)+" ",1),x[1]||(x[1]=e("svg",{class:"ml-2 w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 8l4 4m0 0l-4 4m4-4H3"})],-1))]),_:1,__:[1]})])])):b("",!0)])])}}},Jo={class:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8"},Yo={class:"max-w-md w-full space-y-8"},Xo={class:"mt-2 text-center text-sm text-gray-600"},Zo={key:0,class:"rounded-md bg-red-50 p-4"},en={class:"text-sm text-red-700"},tn={class:"rounded-md shadow-sm -space-y-px"},sn={class:"flex items-center justify-between"},rn={class:"flex items-center"},an=["disabled"],on={key:0,class:"absolute left-0 inset-y-0 flex items-center pl-3"},nn={__name:"Login",setup(o){const n=se(),l=re(),t=M({username:"",password:"",remember:!1}),a=_(()=>l.loading),u=_(()=>l.error);async function m(){(await l.login({username:t.value.username,password:t.value.password,remember:t.value.remember})).success&&n.push("/app/dashboard")}return(x,h)=>{const p=W("router-link");return s(),r("div",Jo,[e("div",Yo,[e("div",null,[h[5]||(h[5]=e("div",{class:"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary-100"},[e("svg",{class:"h-6 w-6 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})])],-1)),h[6]||(h[6]=e("h2",{class:"mt-6 text-center text-3xl font-extrabold text-gray-900"}," Accedi al tuo account ",-1)),e("p",Xo,[h[4]||(h[4]=V(" Oppure ")),z(p,{to:"/auth/register",class:"font-medium text-primary-600 hover:text-primary-500"},{default:I(()=>h[3]||(h[3]=[V(" registrati per un nuovo account ")])),_:1,__:[3]})])]),e("form",{onSubmit:ge(m,["prevent"]),class:"mt-8 space-y-6"},[u.value?(s(),r("div",Zo,[e("div",en,d(u.value),1)])):b("",!0),e("div",tn,[e("div",null,[h[7]||(h[7]=e("label",{for:"username",class:"sr-only"},"Username",-1)),Q(e("input",{id:"username","onUpdate:modelValue":h[0]||(h[0]=i=>t.value.username=i),name:"username",type:"text",autocomplete:"username",required:"",class:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm",placeholder:"Username"},null,512),[[te,t.value.username]])]),e("div",null,[h[8]||(h[8]=e("label",{for:"password",class:"sr-only"},"Password",-1)),Q(e("input",{id:"password","onUpdate:modelValue":h[1]||(h[1]=i=>t.value.password=i),name:"password",type:"password",autocomplete:"current-password",required:"",class:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm",placeholder:"Password"},null,512),[[te,t.value.password]])])]),e("div",sn,[e("div",rn,[Q(e("input",{id:"remember-me","onUpdate:modelValue":h[2]||(h[2]=i=>t.value.remember=i),name:"remember-me",type:"checkbox",class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"},null,512),[[Be,t.value.remember]]),h[9]||(h[9]=e("label",{for:"remember-me",class:"ml-2 block text-sm text-gray-900"}," Ricordami ",-1))]),h[10]||(h[10]=e("div",{class:"text-sm"},[e("a",{href:"#",class:"font-medium text-primary-600 hover:text-primary-500"}," Password dimenticata? ")],-1))]),e("div",null,[e("button",{type:"submit",disabled:a.value,class:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"},[a.value?(s(),r("span",on,h[11]||(h[11]=[e("svg",{class:"h-5 w-5 text-primary-500 animate-spin",fill:"none",viewBox:"0 0 24 24"},[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1)]))):b("",!0),V(" "+d(a.value?"Accesso in corso...":"Accedi"),1)],8,an)])],32)])])}}},ln={},dn={class:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8"};function cn(o,n){return s(),r("div",dn,n[0]||(n[0]=[e("div",{class:"max-w-md w-full space-y-8"},[e("div",null,[e("h2",{class:"mt-6 text-center text-3xl font-extrabold text-gray-900"}," Registra un nuovo account ")]),e("div",{class:"text-center text-gray-600"}," Registrazione in arrivo... ")],-1)]))}const un=Te(ln,[["render",cn]]),mn={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},pn={class:"p-5"},gn={class:"flex items-center"},vn={class:"ml-5 w-0 flex-1"},hn={class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"},fn={class:"text-lg font-medium text-gray-900 dark:text-white"},xn={key:0,class:"text-xs text-gray-500 dark:text-gray-400"},yn={key:0,class:"bg-gray-50 dark:bg-gray-700 px-5 py-3"},_n={class:"text-sm"},le={__name:"StatsCard",props:{title:{type:String,required:!0},value:{type:[Number,String],required:!0},subtitle:{type:String,default:null},icon:{type:String,required:!0},color:{type:String,default:"primary"},link:{type:String,default:null}},setup(o){const n=t=>t==="project"?{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
      </svg>`}:t==="users"?{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
      </svg>`}:t==="clock"?{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>`}:t==="team"?{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
      </svg>`}:{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>`},l=t=>{const a={primary:"bg-primary-500",secondary:"bg-secondary-500",red:"bg-red-500",yellow:"bg-yellow-500",blue:"bg-blue-500",green:"bg-green-500"};return a[t]||a.primary};return(t,a)=>{const u=W("router-link");return s(),r("div",mn,[e("div",pn,[e("div",gn,[e("div",{class:D(["flex-shrink-0 rounded-md p-3",l(o.color)])},[(s(),F(Se(n(o.icon)),{class:"h-6 w-6 text-white"}))],2),e("div",vn,[e("dl",null,[e("dt",hn,d(o.title),1),e("dd",null,[e("div",fn,d(o.value),1),o.subtitle?(s(),r("div",xn,d(o.subtitle),1)):b("",!0)])])])])]),o.link?(s(),r("div",yn,[e("div",_n,[z(u,{to:o.link,class:"font-medium text-primary-600 dark:text-primary-400 hover:text-primary-500 dark:hover:text-primary-300"},{default:I(()=>a[0]||(a[0]=[V(" Vedi tutti ")])),_:1,__:[0]},8,["to"])])])):b("",!0)])}}},bn={class:"py-6"},kn={class:"flex flex-col md:flex-row md:items-center md:justify-between mb-8"},wn={class:"mt-4 md:mt-0 flex space-x-3"},$n={class:"relative"},Cn=["disabled"],jn={class:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"},Mn={class:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8"},zn={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Pn={class:"relative h-64"},Sn={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},En={class:"relative h-64"},An={class:"grid grid-cols-1 lg:grid-cols-3 gap-6"},Vn={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},Tn={class:"p-6"},In={key:0,class:"text-center py-8 text-gray-500"},Ln={key:1,class:"space-y-4"},Dn={class:"flex justify-between items-start"},Hn={class:"flex-1"},Bn={class:"text-sm font-medium text-gray-900 dark:text-white"},qn={class:"text-xs text-gray-500 dark:text-gray-400"},Rn={class:"mt-2 flex justify-between items-center"},Un={class:"text-xs text-gray-500 dark:text-gray-400"},Nn={class:"bg-gray-50 dark:bg-gray-700 px-6 py-3"},Fn={class:"text-sm"},On={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},Kn={class:"p-6"},Gn={key:0,class:"text-center py-8 text-gray-500"},Wn={key:1,class:"space-y-4"},Qn={class:"flex-shrink-0"},Jn={class:"flex-1 min-w-0"},Yn={class:"text-sm font-medium text-gray-900 dark:text-white"},Xn={class:"text-xs text-gray-500 dark:text-gray-400"},Zn={class:"text-xs text-gray-400 dark:text-gray-500"},ei={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},ti={class:"p-6"},si={key:0,class:"text-center py-8 text-gray-500"},ri={key:1,class:"space-y-4"},ai={class:"flex justify-between items-start"},oi={class:"flex-1"},ni={class:"text-sm font-medium text-gray-900 dark:text-white"},ii={class:"text-xs text-gray-500 dark:text-gray-400"},li={class:"text-right"},di={class:"text-sm font-bold text-gray-900 dark:text-white"},ci={class:"text-xs text-gray-500"},ui={class:"mt-2"},mi={class:"w-full bg-gray-200 rounded-full h-2"},pi={class:"text-xs text-gray-500 mt-1"},gi={__name:"Dashboard",setup(o){pe.register(...qe),se(),re();const n=M(!1),l=M("7"),t=M({}),a=M([]),u=M([]),m=M([]),x=M(null),h=M(null);let p=null,i=null;const g=async()=>{try{const v=await fetch("/api/dashboard/stats");if(!v.ok)throw new Error("Failed to fetch stats");const c=await v.json();t.value=c.data}catch(v){console.error("Error fetching dashboard stats:",v),t.value={}}},y=async()=>{try{const v=await fetch(`/api/dashboard/upcoming-tasks?days=${l.value}&limit=5`);if(!v.ok)throw new Error("Failed to fetch upcoming tasks");const c=await v.json();a.value=c.data.tasks}catch(v){console.error("Error fetching upcoming tasks:",v),a.value=[]}},w=async()=>{try{const v=await fetch("/api/dashboard/recent-activities?limit=5");if(!v.ok)throw new Error("Failed to fetch recent activities");const c=await v.json();u.value=c.data.activities}catch(v){console.error("Error fetching recent activities:",v),u.value=[]}},S=async()=>{try{const v=await fetch("/api/dashboard/kpis?limit=3");if(!v.ok)throw new Error("Failed to fetch KPIs");const c=await v.json();m.value=c.data.kpis}catch(v){console.error("Error fetching KPIs:",v),m.value=[]}},L=async()=>{try{const v=await fetch("/api/dashboard/charts/project-status");if(!v.ok)throw new Error("Failed to fetch project chart data");const c=await v.json();C(c.data.chart)}catch(v){console.error("Error fetching project chart:",v)}},E=async()=>{try{const v=await fetch("/api/dashboard/charts/task-status");if(!v.ok)throw new Error("Failed to fetch task chart data");const c=await v.json();U(c.data.chart)}catch(v){console.error("Error fetching task chart:",v)}},C=v=>{if(!x.value)return;const c=x.value.getContext("2d");p&&p.destroy(),p=new pe(c,{type:"doughnut",data:{labels:v.labels,datasets:[{data:v.data,backgroundColor:["#3B82F6","#10B981","#F59E0B","#EF4444","#8B5CF6"],borderWidth:2,borderColor:"#ffffff"}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom",labels:{padding:20,usePointStyle:!0}}}}})},U=v=>{if(!h.value)return;const c=h.value.getContext("2d");i&&i.destroy(),i=new pe(c,{type:"bar",data:{labels:v.labels,datasets:[{label:"Tasks",data:v.data,backgroundColor:["#60A5FA","#34D399","#FBBF24","#F87171"],borderColor:["#3B82F6","#10B981","#F59E0B","#EF4444"],borderWidth:1}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1}},scales:{y:{beginAtZero:!0,ticks:{stepSize:1}}}}})},P=async()=>{n.value=!0;try{await Promise.all([g(),y(),w(),S(),L(),E()])}finally{n.value=!1}},k=v=>new Date(v).toLocaleDateString("it-IT"),$=v=>{const c=new Date(v),j=Math.floor((new Date-c)/(1e3*60));return j<60?`${j} minuti fa`:j<1440?`${Math.floor(j/60)} ore fa`:`${Math.floor(j/1440)} giorni fa`},f=v=>{const c={high:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",medium:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",low:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"};return c[v]||c.medium},A=v=>{const c={todo:"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300","in-progress":"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",review:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",done:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"};return c[v]||c.todo},B=v=>{const c={task:{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
      </svg>`},timesheet:{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>`},event:{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
      </svg>`}};return c[v]||c.task},H=v=>{const c={task:"bg-blue-100 text-blue-600",timesheet:"bg-green-100 text-green-600",event:"bg-purple-100 text-purple-600"};return c[v]||c.task},q=v=>v>=90?"bg-green-500":v>=70?"bg-yellow-500":"bg-red-500";return X(async()=>{await P(),await Pe(),x.value&&h.value&&(await L(),await E())}),(v,c)=>{var j,Z,fe,xe,ye,_e,be,ke;const N=W("router-link");return s(),r("div",bn,[e("div",kn,[c[4]||(c[4]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Dashboard"),e("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," Benvenuto! Ecco una panoramica delle attività della tua azienda. ")],-1)),e("div",wn,[e("div",$n,[Q(e("select",{"onUpdate:modelValue":c[0]||(c[0]=R=>l.value=R),onChange:P,class:"bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md py-2 pl-3 pr-10 text-sm font-medium text-gray-700 dark:text-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500"},c[1]||(c[1]=[e("option",{value:"7"},"Ultimi 7 giorni",-1),e("option",{value:"30"},"Ultimo mese",-1),e("option",{value:"90"},"Ultimi 3 mesi",-1)]),544),[[ne,l.value]])]),e("button",{onClick:P,disabled:n.value,type:"button",class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"},[(s(),r("svg",{xmlns:"http://www.w3.org/2000/svg",class:D(["h-4 w-4 mr-2",{"animate-spin":n.value}]),viewBox:"0 0 20 20",fill:"currentColor"},c[2]||(c[2]=[e("path",{"fill-rule":"evenodd",d:"M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z","clip-rule":"evenodd"},null,-1)]),2)),c[3]||(c[3]=V(" Aggiorna "))],8,Cn)])]),e("div",jn,[z(le,{title:"Progetti Attivi",value:((j=t.value.projects)==null?void 0:j.active)||0,subtitle:`di ${((Z=t.value.projects)==null?void 0:Z.total)||0} totali`,icon:"project",color:"primary",link:"/app/projects?status=active"},null,8,["value","subtitle"]),z(le,{title:"Clienti",value:((fe=t.value.team)==null?void 0:fe.clients)||0,icon:"users",color:"secondary",link:"/app/crm/clients"},null,8,["value"]),z(le,{title:"Task Pendenti",value:((xe=t.value.tasks)==null?void 0:xe.pending)||0,subtitle:`${((ye=t.value.tasks)==null?void 0:ye.overdue)||0} in ritardo`,icon:"clock",color:((_e=t.value.tasks)==null?void 0:_e.overdue)>0?"red":"yellow",link:"/app/tasks?status=pending"},null,8,["value","subtitle","color"]),z(le,{title:"Team Members",value:((be=t.value.team)==null?void 0:be.users)||0,subtitle:`${((ke=t.value.team)==null?void 0:ke.departments)||0} dipartimenti`,icon:"team",color:"blue",link:"/app/personnel"},null,8,["value","subtitle"])]),e("div",Mn,[e("div",zn,[c[5]||(c[5]=e("div",{class:"flex items-center justify-between mb-4"},[e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Stato Progetti")],-1)),e("div",Pn,[e("canvas",{ref_key:"projectChart",ref:x},null,512)])]),e("div",Sn,[c[6]||(c[6]=e("div",{class:"flex items-center justify-between mb-4"},[e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Stato Attività")],-1)),e("div",En,[e("canvas",{ref_key:"taskChart",ref:h},null,512)])])]),e("div",An,[e("div",Vn,[e("div",Tn,[c[7]||(c[7]=e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Attività in Scadenza",-1)),a.value.length===0?(s(),r("div",In," Nessuna attività in scadenza ")):(s(),r("div",Ln,[(s(!0),r(O,null,K(a.value,R=>(s(),r("div",{key:R.id,class:"border-b border-gray-200 dark:border-gray-700 pb-4 last:border-b-0 last:pb-0"},[e("div",Dn,[e("div",Hn,[e("h3",Bn,d(R.name),1),e("p",qn,d(R.project_name),1)]),e("span",{class:D(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",f(R.priority)])},d(R.priority),3)]),e("div",Rn,[e("span",Un," Scadenza: "+d(k(R.due_date)),1),e("span",{class:D(["inline-flex items-center px-2 py-1 rounded-full text-xs font-medium",A(R.status)])},d(R.status),3)])]))),128))]))]),e("div",Nn,[e("div",Fn,[z(N,{to:"/app/tasks",class:"font-medium text-primary-600 dark:text-primary-400 hover:text-primary-500"},{default:I(()=>c[8]||(c[8]=[V(" Vedi tutte le attività ")])),_:1,__:[8]})])])]),e("div",On,[e("div",Kn,[c[9]||(c[9]=e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Attività Recenti",-1)),u.value.length===0?(s(),r("div",Gn," Nessuna attività recente ")):(s(),r("div",Wn,[(s(!0),r(O,null,K(u.value,R=>(s(),r("div",{key:`${R.type}-${R.id}`,class:"flex items-start space-x-3"},[e("div",Qn,[e("div",{class:D(["w-8 h-8 rounded-full flex items-center justify-center",H(R.type)])},[(s(),F(Se(B(R.type)),{class:"w-4 h-4"}))],2)]),e("div",Jn,[e("p",Yn,d(R.title),1),e("p",Xn,d(R.description),1),e("p",Zn,d($(R.timestamp)),1)])]))),128))]))])]),e("div",ei,[e("div",ti,[c[10]||(c[10]=e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"KPIs Principali",-1)),m.value.length===0?(s(),r("div",si," Nessun KPI configurato ")):(s(),r("div",ri,[(s(!0),r(O,null,K(m.value,R=>(s(),r("div",{key:R.id,class:"border-b border-gray-200 dark:border-gray-700 pb-4 last:border-b-0 last:pb-0"},[e("div",ai,[e("div",oi,[e("h3",ni,d(R.name),1),e("p",ii,d(R.description),1)]),e("div",li,[e("p",di,d(R.current_value)+d(R.unit),1),e("p",ci," Target: "+d(R.target_value)+d(R.unit),1)])]),e("div",ui,[e("div",mi,[e("div",{class:D(["h-2 rounded-full",q(R.performance_percentage)]),style:ve({width:Math.min(R.performance_percentage,100)+"%"})},null,6)]),e("p",pi,d(Math.round(R.performance_percentage))+"% del target",1)])]))),128))]))])])])])}}},vi=ue("projects",()=>{const o=M([]),n=M(null),l=M(!1),t=M(null),a=M(new Map),u=M({page:1,perPage:20,total:0,totalPages:0}),m=M({search:"",status:"",client:"",type:""}),x=_(()=>{let f=o.value;if(m.value.search){const A=m.value.search.toLowerCase();f=f.filter(B=>{var H,q,v;return B.name.toLowerCase().includes(A)||((H=B.description)==null?void 0:H.toLowerCase().includes(A))||((v=(q=B.client)==null?void 0:q.name)==null?void 0:v.toLowerCase().includes(A))})}return m.value.status&&(f=f.filter(A=>A.status===m.value.status)),m.value.client&&(f=f.filter(A=>A.client_id===m.value.client)),m.value.type&&(f=f.filter(A=>A.project_type===m.value.type)),f}),h=_(()=>{const f={};return o.value.forEach(A=>{f[A.status]||(f[A.status]=[]),f[A.status].push(A)}),f}),p=async(f={})=>{var A,B;l.value=!0,t.value=null;try{const H=new URLSearchParams({page:f.page||u.value.page,per_page:f.perPage||u.value.perPage,search:f.search||m.value.search,status:f.status||m.value.status,client:f.client||m.value.client,type:f.type||m.value.type}),q=await J.get(`/api/projects?${H}`);q.data.success&&(o.value=q.data.data.projects,u.value=q.data.data.pagination)}catch(H){t.value=((B=(A=H.response)==null?void 0:A.data)==null?void 0:B.message)||"Errore nel caricamento progetti",console.error("Error fetching projects:",H)}finally{l.value=!1}},i=async(f,A=!1)=>{var B,H;if(!A&&a.value.has(f)){const q=a.value.get(f);return n.value=q,q}l.value=!0,t.value=null;try{const q=await J.get(`/api/projects/${f}`);if(q.data.success){const v=q.data.data.project;return n.value=v,a.value.set(f,v),v}}catch(q){throw t.value=((H=(B=q.response)==null?void 0:B.data)==null?void 0:H.message)||"Errore nel caricamento progetto",console.error("Error fetching project:",q),q}finally{l.value=!1}};return{projects:o,currentProject:n,loading:l,error:t,pagination:u,filters:m,filteredProjects:x,projectsByStatus:h,fetchProjects:p,fetchProject:i,createProject:async f=>{var A,B;l.value=!0,t.value=null;try{const H=await J.post("/api/projects",f);if(H.data.success){const q=H.data.data.project;return o.value.unshift(q),q}}catch(H){throw t.value=((B=(A=H.response)==null?void 0:A.data)==null?void 0:B.message)||"Errore nella creazione progetto",console.error("Error creating project:",H),H}finally{l.value=!1}},updateProject:async(f,A)=>{var B,H,q;l.value=!0,t.value=null;try{const v=await J.put(`/api/projects/${f}`,A);if(v.data.success){const c=v.data.data.project,N=o.value.findIndex(j=>j.id===f);return N!==-1&&(o.value[N]=c),((B=n.value)==null?void 0:B.id)===f&&(n.value=c),a.value.set(f,c),c}}catch(v){throw t.value=((q=(H=v.response)==null?void 0:H.data)==null?void 0:q.message)||"Errore nell'aggiornamento progetto",console.error("Error updating project:",v),v}finally{l.value=!1}},deleteProject:async f=>{var A,B,H;l.value=!0,t.value=null;try{(await J.delete(`/api/projects/${f}`)).data.success&&(o.value=o.value.filter(v=>v.id!==f),((A=n.value)==null?void 0:A.id)===f&&(n.value=null),a.value.delete(f))}catch(q){throw t.value=((H=(B=q.response)==null?void 0:B.data)==null?void 0:H.message)||"Errore nell'eliminazione progetto",console.error("Error deleting project:",q),q}finally{l.value=!1}},setFilters:f=>{m.value={...m.value,...f}},clearFilters:()=>{m.value={search:"",status:"",client:"",type:""}},setCurrentProject:f=>{n.value=f},clearCurrentProject:()=>{n.value=null},clearCache:()=>{a.value.clear()},refreshProject:async f=>await i(f,!0),getCachedProject:f=>a.value.get(f),$reset:()=>{o.value=[],n.value=null,l.value=!1,t.value=null,a.value.clear(),u.value={page:1,perPage:20,total:0,totalPages:0},m.value={search:"",status:"",client:"",type:""}}}}),hi={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6"},fi={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},xi=["value"],yi={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},_i={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},bi={class:"text-lg font-medium text-gray-900 dark:text-white"},ki={key:0,class:"p-6 text-center"},wi={key:1,class:"p-6 text-center"},$i={key:2,class:"divide-y divide-gray-200 dark:divide-gray-700"},Ci=["onClick"],ji={class:"flex items-center justify-between"},Mi={class:"flex-1"},zi={class:"flex items-center"},Pi={class:"text-lg font-medium text-gray-900 dark:text-white"},Si={class:"mt-1 text-sm text-gray-600 dark:text-gray-400"},Ei={class:"mt-2 flex items-center text-sm text-gray-500 dark:text-gray-400"},Ai={key:0},Vi={key:1,class:"mx-2"},Ti={key:2},Ii={key:3,class:"mx-2"},Li={key:4},Di={class:"ml-4 flex items-center space-x-2"},Hi={class:"text-right"},Bi={class:"text-sm font-medium text-gray-900 dark:text-white"},qi={class:"w-20 bg-gray-200 dark:bg-gray-600 rounded-full h-2 mt-1"},Ri={__name:"Projects",setup(o){const n=se(),l=vi(),t=M(!0),a=M(""),u=M({status:"",client:""}),m=_(()=>l.projects),x=M([]),h=_(()=>{let k=m.value;if(u.value.status&&(k=k.filter($=>$.status===u.value.status)),u.value.client&&(k=k.filter($=>$.client_id==u.value.client)),a.value){const $=a.value.toLowerCase();k=k.filter(f=>f.name.toLowerCase().includes($)||f.description&&f.description.toLowerCase().includes($)||f.client&&f.client.name&&f.client.name.toLowerCase().includes($))}return k}),p=async()=>{t.value=!0;try{await l.fetchProjects(),x.value=[]}catch(k){console.error("Error loading projects:",k)}finally{t.value=!1}},i=()=>{},g=()=>{},y=()=>{u.value={status:"",client:""},a.value=""},w=()=>{n.push("/app/projects/create")},S=k=>{n.push(`/app/projects/${k}`)},L=k=>({planning:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400",active:"bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",completed:"bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400","on-hold":"bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400"})[k]||"bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400",E=k=>({planning:"Pianificazione",active:"Attivo",completed:"Completato","on-hold":"In Pausa"})[k]||k,C=k=>new Date(k).toLocaleDateString("it-IT"),U=k=>new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(k),P=k=>({planning:10,active:50,completed:100,"on-hold":25})[k.status]||0;return X(()=>{p()}),(k,$)=>(s(),r("div",null,[e("div",{class:"mb-6"},[e("div",{class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},[$[4]||($[4]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Progetti"),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"}," Gestisci e monitora tutti i progetti aziendali ")],-1)),e("div",{class:"mt-4 sm:mt-0"},[e("button",{onClick:w,class:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},$[3]||($[3]=[e("svg",{class:"h-4 w-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4v16m8-8H4"})],-1),V(" Nuovo Progetto ")]))])])]),e("div",hi,[e("div",fi,[e("div",null,[$[6]||($[6]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Stato",-1)),Q(e("select",{"onUpdate:modelValue":$[0]||($[0]=f=>u.value.status=f),onChange:g,class:"w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"},$[5]||($[5]=[Ee('<option value="">Tutti gli stati</option><option value="planning">Pianificazione</option><option value="active">Attivo</option><option value="completed">Completato</option><option value="on-hold">In Pausa</option>',5)]),544),[[ne,u.value.status]])]),e("div",null,[$[8]||($[8]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Cliente",-1)),Q(e("select",{"onUpdate:modelValue":$[1]||($[1]=f=>u.value.client=f),onChange:g,class:"w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"},[$[7]||($[7]=e("option",{value:""},"Tutti i clienti",-1)),(s(!0),r(O,null,K(x.value,f=>(s(),r("option",{key:f.id,value:f.id},d(f.name),9,xi))),128))],544),[[ne,u.value.client]])]),e("div",null,[$[9]||($[9]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Ricerca",-1)),Q(e("input",{"onUpdate:modelValue":$[2]||($[2]=f=>a.value=f),onInput:i,type:"text",placeholder:"Cerca progetti...",class:"w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"},null,544),[[te,a.value]])]),e("div",{class:"flex items-end"},[e("button",{onClick:y,class:"w-full px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"}," Reset Filtri ")])])]),e("div",yi,[e("div",_i,[e("h3",bi," Progetti ("+d(h.value.length)+") ",1)]),t.value?(s(),r("div",ki,$[10]||($[10]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mx-auto"},null,-1),e("p",{class:"mt-2 text-gray-600 dark:text-gray-400"},"Caricamento progetti...",-1)]))):h.value.length===0?(s(),r("div",wi,$[11]||($[11]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun progetto",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"Inizia creando il tuo primo progetto.",-1)]))):(s(),r("div",$i,[(s(!0),r(O,null,K(h.value,f=>(s(),r("div",{key:f.id,class:"p-6 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer",onClick:A=>S(f.id)},[e("div",ji,[e("div",Mi,[e("div",zi,[e("h4",Pi,d(f.name),1),e("span",{class:D([L(f.status),"ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},d(E(f.status)),3)]),e("p",Si,d(f.description),1),e("div",Ei,[f.client?(s(),r("span",Ai,"Cliente: "+d(f.client.name),1)):b("",!0),f.client?(s(),r("span",Vi,"•")):b("",!0),f.end_date?(s(),r("span",Ti,"Scadenza: "+d(C(f.end_date)),1)):b("",!0),f.end_date&&f.budget?(s(),r("span",Ii,"•")):b("",!0),f.budget?(s(),r("span",Li,"Budget: "+d(U(f.budget)),1)):b("",!0)])]),e("div",Di,[e("div",Hi,[e("div",Bi,d(P(f))+"% ",1),e("div",qi,[e("div",{class:"bg-primary-600 h-2 rounded-full",style:ve({width:P(f)+"%"})},null,4)])]),$[12]||($[12]=e("svg",{class:"h-5 w-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1))])])],8,Ci))),128))]))])]))}},Ui=ue("personnel",()=>{const o=M([]),n=M([]),l=M([]),t=M(null),a=M(!1),u=M(null),m=M({search:"",department:null,skill:null,role:null,location:null,sort:"name"}),x=M({page:1,per_page:20,total:0,pages:0}),h=_(()=>{let P=o.value;if(m.value.search){const k=m.value.search.toLowerCase();P=P.filter($=>{var f,A,B;return((f=$.full_name)==null?void 0:f.toLowerCase().includes(k))||((A=$.email)==null?void 0:A.toLowerCase().includes(k))||((B=$.position)==null?void 0:B.toLowerCase().includes(k))})}return m.value.department&&(P=P.filter(k=>k.department_id===m.value.department)),m.value.skill&&(P=P.filter(k=>{var $;return($=k.skills)==null?void 0:$.some(f=>f.id===m.value.skill)})),m.value.role&&(P=P.filter(k=>k.role===m.value.role)),P}),p=_(()=>{const P=(k=null)=>n.value.filter($=>$.parent_id===k).map($=>({...$,children:P($.id)}));return P()}),i=async(P={})=>{a.value=!0,u.value=null;try{const k=new URLSearchParams({page:P.page||x.value.page,per_page:P.per_page||x.value.per_page,...P}),$=await fetch(`/api/personnel/users?${k}`,{credentials:"include"});if(!$.ok)throw new Error(`HTTP ${$.status}: ${$.statusText}`);const f=await $.json();if(f.success){o.value=f.data.users||[];const A=f.data.pagination||{};x.value={page:A.page||1,per_page:A.per_page||20,total:A.total||0,pages:A.pages||0}}else throw new Error(f.message||"Errore nel caricamento utenti")}catch(k){u.value=k.message,console.error("Errore fetchUsers:",k)}finally{a.value=!1}},g=async P=>{a.value=!0,u.value=null;try{const k=await fetch(`/api/personnel/users/${P}`,{credentials:"include"});if(!k.ok)throw new Error(`HTTP ${k.status}: ${k.statusText}`);const $=await k.json();if($.success)return t.value=$.data.user,$.data.user;throw new Error($.message||"Errore nel caricamento utente")}catch(k){throw u.value=k.message,console.error("Errore fetchUser:",k),k}finally{a.value=!1}},y=async(P,k)=>{var $;a.value=!0,u.value=null;try{const f=await fetch(`/api/personnel/users/${P}`,{method:"PUT",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(k)});if(!f.ok)throw new Error(`HTTP ${f.status}: ${f.statusText}`);const A=await f.json();if(A.success){const B=o.value.findIndex(H=>H.id===P);return B!==-1&&(o.value[B]={...o.value[B],...A.data.user}),(($=t.value)==null?void 0:$.id)===P&&(t.value={...t.value,...A.data.user}),A.data.user}else throw new Error(A.message||"Errore nell'aggiornamento utente")}catch(f){throw u.value=f.message,console.error("Errore updateUser:",f),f}finally{a.value=!1}},w=async()=>{a.value=!0,u.value=null;try{const P=await fetch("/api/personnel/departments",{credentials:"include"});if(!P.ok)throw new Error(`HTTP ${P.status}: ${P.statusText}`);const k=await P.json();if(k.success)n.value=k.data.departments||[];else throw new Error(k.message||"Errore nel caricamento dipartimenti")}catch(P){u.value=P.message,console.error("Errore fetchDepartments:",P)}finally{a.value=!1}},S=async()=>{a.value=!0,u.value=null;try{const P=await fetch("/api/personnel/skills",{credentials:"include"});if(!P.ok)throw new Error(`HTTP ${P.status}: ${P.statusText}`);const k=await P.json();if(k.success)l.value=k.data.skills||[];else throw new Error(k.message||"Errore nel caricamento competenze")}catch(P){u.value=P.message,console.error("Errore fetchSkills:",P)}finally{a.value=!1}},L=(P,k)=>{m.value[P]=k},E=()=>{m.value={search:"",department:null,skill:null,role:null,location:null,sort:"name"}};return{users:o,departments:n,skills:l,currentUser:t,loading:a,error:u,filters:m,pagination:x,filteredUsers:h,departmentTree:p,fetchUsers:i,fetchUser:g,updateUser:y,fetchDepartments:w,fetchSkills:S,setFilter:L,clearFilters:E,setPagination:(P,k=null)=>{x.value.page=P,k&&(x.value.per_page=k)},$reset:()=>{o.value=[],n.value=[],l.value=[],t.value=null,a.value=!1,u.value=null,E(),x.value={page:1,per_page:20,total:0,pages:0}}}}),Ni={class:"mb-6"},Fi={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},Oi={class:"mt-4 sm:mt-0 flex flex-wrap gap-3"},Ki={class:"bg-white dark:bg-gray-800 shadow rounded-lg mb-6"},Gi={class:"p-6"},Wi={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},Qi=["value"],Ji=["value"],Yi={key:0,class:"mt-4 flex justify-end"},Xi={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},Zi={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},el={class:"flex items-center justify-between"},tl={class:"text-lg font-medium text-gray-900 dark:text-white"},sl={key:0,class:"text-sm text-gray-500 dark:text-gray-400"},rl={key:0,class:"inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 mr-2"},al={key:1,class:"inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 mr-2"},ol={key:2,class:"inline-flex items-center px-2 py-1 rounded-full text-xs bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200"},nl={key:0,class:"p-6"},il={key:1,class:"p-6"},ll={class:"bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md p-4"},dl={class:"flex"},cl={class:"ml-3"},ul={class:"mt-2 text-sm text-red-700 dark:text-red-300"},ml={key:2,class:"overflow-hidden"},pl={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6"},gl={class:"flex items-center mb-4"},vl={class:"flex-shrink-0"},hl=["src","alt"],fl={key:1,class:"h-12 w-12 rounded-full bg-primary-500 dark:bg-primary-600 flex items-center justify-center text-white text-lg font-medium"},xl={class:"ml-4 flex-1"},yl={class:"text-lg font-medium text-gray-900 dark:text-white"},_l={class:"text-sm text-gray-500 dark:text-gray-400"},bl={class:"space-y-2 mb-4"},kl={class:"flex items-center text-sm"},wl={class:"text-gray-900 dark:text-white"},$l={class:"flex items-center text-sm"},Cl={key:0,class:"flex items-center text-sm"},jl={class:"text-gray-900 dark:text-white"},Ml={key:0,class:"mb-4"},zl={class:"flex flex-wrap gap-1"},Pl={key:0,class:"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-600 dark:bg-gray-600 dark:text-gray-300"},Sl={class:"flex space-x-2"},El=["href"],Al=["href"],Vl={key:3,class:"text-center py-12"},Tl={class:"text-gray-500 dark:text-gray-400"},Il={key:4,class:"px-6 py-4 border-t border-gray-200 dark:border-gray-700"},Ll={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4"},Dl={class:"text-sm text-gray-700 dark:text-gray-300"},Hl={class:"font-medium"},Bl={class:"font-medium"},ql={class:"font-medium"},Rl={class:"flex items-center space-x-1"},Ul={key:1,class:"px-3 py-2 border border-gray-200 dark:border-gray-700 rounded-md text-sm font-medium text-gray-400 dark:text-gray-600 bg-gray-50 dark:bg-gray-900 cursor-not-allowed"},Nl=["onClick"],Fl={key:1,class:"px-2 py-2 text-sm text-gray-500 dark:text-gray-400"},Ol={key:3,class:"px-3 py-2 border border-gray-200 dark:border-gray-700 rounded-md text-sm font-medium text-gray-400 dark:text-gray-600 bg-gray-50 dark:bg-gray-900 cursor-not-allowed"},Kl={__name:"Personnel",setup(o){const n=me();se();const l=Ui(),{hasPermission:t}=Ve(),a=M(""),u=M(null),{users:m,departments:x,skills:h,loading:p,error:i,filters:g,pagination:y}=l,w=_(()=>t.value("admin_access")),S=_(()=>a.value||g.department||g.skill||g.role),L=_(()=>g.department?x.find(v=>v.id===g.department):null),E=_(()=>g.skill?h.find(v=>v.id===g.skill):null),C=v=>{var j,Z;const c=((j=v.first_name)==null?void 0:j[0])||"",N=((Z=v.last_name)==null?void 0:Z[0])||"";return(c+N).toUpperCase()},U=v=>({admin:"Admin",manager:"Manager",employee:"Dipendente",human_resources:"HR",project_manager:"PM"})[v]||v,P=v=>({admin:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",manager:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",human_resources:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",project_manager:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"})[v]||"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200",k=()=>{const v=[],c=y.page,N=y.pages;N>0&&v.push(1);for(let j=Math.max(2,c-1);j<=Math.min(N-1,c+1);j++)v.includes(j)||v.push(j);return N>1&&!v.includes(N)&&(v[v.length-1]<N-1&&v.push("..."),v.push(N)),v},$=()=>{clearTimeout(u.value),u.value=setTimeout(()=>{f()},500)},f=async()=>{l.setFilter("search",a.value),l.setPagination(1),await H()},A=async()=>{a.value="",l.clearFilters(),l.setPagination(1),await H()},B=async v=>{l.setPagination(v),await H()},H=async()=>{const v={page:y.page,per_page:y.per_page};a.value&&(v.search=a.value),g.department&&(v.department_id=g.department),g.skill&&(v.skills=g.skill),g.role&&(v.role=g.role),await l.fetchUsers(v)},q=async()=>{await Promise.all([l.fetchDepartments(),l.fetchSkills()]);const v=n.query;v.search&&(a.value=v.search),v.department&&l.setFilter("department",parseInt(v.department)),v.skill&&l.setFilter("skill",parseInt(v.skill)),v.page&&l.setPagination(parseInt(v.page)),await H()};return oe(()=>n.query,v=>{v.search!==a.value&&(a.value=v.search||"")},{deep:!0}),X(()=>{q()}),(v,c)=>{const N=W("router-link");return s(),r("div",null,[e("div",Ni,[e("div",Fi,[c[9]||(c[9]=Ee('<div><div class="flex items-center"><svg class="w-8 h-8 text-blue-600 dark:text-blue-400 mr-3" fill="currentColor" viewBox="0 0 20 20"><path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"></path></svg><h1 class="text-2xl font-bold text-gray-900 dark:text-white">Personale</h1></div><p class="mt-1 text-sm text-gray-500 dark:text-gray-400"> Gestisci il team e le competenze aziendali </p></div>',1)),e("div",Oi,[z(N,{to:"/app/personnel/directory",class:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"},{default:I(()=>c[5]||(c[5]=[e("svg",{class:"w-4 h-4 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z","clip-rule":"evenodd"})],-1),V(" Directory ")])),_:1,__:[5]}),z(N,{to:"/app/personnel/orgchart",class:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"},{default:I(()=>c[6]||(c[6]=[e("svg",{class:"w-4 h-4 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 0l-2 2a1 1 0 101.414 1.414L8 10.414l1.293 1.293a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})],-1),V(" Organigramma ")])),_:1,__:[6]}),z(N,{to:"/app/personnel/skills",class:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"},{default:I(()=>c[7]||(c[7]=[e("svg",{class:"w-4 h-4 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})],-1),V(" Competenze ")])),_:1,__:[7]}),w.value?(s(),F(N,{key:0,to:"/app/personnel/admin",class:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700"},{default:I(()=>c[8]||(c[8]=[e("svg",{class:"w-4 h-4 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z","clip-rule":"evenodd"})],-1),V(" Amministrazione ")])),_:1,__:[8]})):b("",!0)])])]),e("div",Ki,[c[16]||(c[16]=e("div",{class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},[e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Filtri")],-1)),e("div",Gi,[e("div",Wi,[e("div",null,[c[11]||(c[11]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Dipartimento ",-1)),Q(e("select",{"onUpdate:modelValue":c[0]||(c[0]=j=>T(g).department=j),onChange:f,class:"w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},[c[10]||(c[10]=e("option",{value:""},"Tutti i dipartimenti",-1)),(s(!0),r(O,null,K(T(x),j=>(s(),r("option",{key:j.id,value:j.id},d(j.name),9,Qi))),128))],544),[[ne,T(g).department]])]),e("div",null,[c[13]||(c[13]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Competenza ",-1)),Q(e("select",{"onUpdate:modelValue":c[1]||(c[1]=j=>T(g).skill=j),onChange:f,class:"w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},[c[12]||(c[12]=e("option",{value:""},"Tutte le competenze",-1)),(s(!0),r(O,null,K(T(h),j=>(s(),r("option",{key:j.id,value:j.id},d(j.name)+" ("+d(j.category)+") ",9,Ji))),128))],544),[[ne,T(g).skill]])]),e("div",null,[c[14]||(c[14]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Ricerca ",-1)),Q(e("input",{"onUpdate:modelValue":c[2]||(c[2]=j=>a.value=j),onInput:$,type:"text",placeholder:"Nome, email, posizione...",class:"w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},null,544),[[te,a.value]])])]),S.value?(s(),r("div",Yi,[e("button",{onClick:A,class:"inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"},c[15]||(c[15]=[e("svg",{class:"w-4 h-4 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M9 2a1 1 0 000 2h2a1 1 0 100-2H9z","clip-rule":"evenodd"}),e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414L8.586 12l-2.293 2.293a1 1 0 101.414 1.414L10 13.414l2.293 2.293a1 1 0 001.414-1.414L11.414 12l2.293-2.293z","clip-rule":"evenodd"})],-1),V(" Pulisci filtri ")]))])):b("",!0)])]),e("div",Xi,[e("div",Zi,[e("div",el,[e("h3",tl," Team ("+d(T(y).total)+" dipendenti"+d(S.value?" - filtrati":"")+") ",1),S.value?(s(),r("div",sl,[a.value?(s(),r("span",rl,[c[17]||(c[17]=e("svg",{class:"w-3 h-3 mr-1",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z","clip-rule":"evenodd"})],-1)),V(' "'+d(a.value)+'" ',1)])):b("",!0),L.value?(s(),r("span",al,[c[18]||(c[18]=e("svg",{class:"w-3 h-3 mr-1",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z","clip-rule":"evenodd"})],-1)),V(" "+d(L.value.name),1)])):b("",!0),E.value?(s(),r("span",ol,[c[19]||(c[19]=e("svg",{class:"w-3 h-3 mr-1",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})],-1)),V(" "+d(E.value.name),1)])):b("",!0)])):b("",!0)])]),T(p)?(s(),r("div",nl,c[20]||(c[20]=[e("div",{class:"flex items-center justify-center"},[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"}),e("span",{class:"ml-2 text-gray-600 dark:text-gray-400"},"Caricamento...")],-1)]))):T(i)?(s(),r("div",il,[e("div",ll,[e("div",dl,[c[22]||(c[22]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z","clip-rule":"evenodd"})])],-1)),e("div",cl,[c[21]||(c[21]=e("h3",{class:"text-sm font-medium text-red-800 dark:text-red-200"}," Errore nel caricamento ",-1)),e("div",ul,d(T(i)),1)])])])])):T(m).length>0?(s(),r("div",ml,[e("div",pl,[(s(!0),r(O,null,K(T(m),j=>(s(),r("div",{key:j.id,class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-6 hover:shadow-md transition-shadow duration-200"},[e("div",gl,[e("div",vl,[j.profile_image?(s(),r("img",{key:0,src:j.profile_image,alt:j.full_name,class:"h-12 w-12 rounded-full"},null,8,hl)):(s(),r("div",fl,d(C(j)),1))]),e("div",xl,[e("h4",yl,d(j.full_name),1),e("p",_l,d(j.position||"Posizione non specificata"),1)])]),e("div",bl,[e("div",kl,[c[23]||(c[23]=e("span",{class:"text-gray-500 dark:text-gray-400 w-20"},"Email:",-1)),e("span",wl,d(j.email),1)]),e("div",$l,[c[24]||(c[24]=e("span",{class:"text-gray-500 dark:text-gray-400 w-20"},"Ruolo:",-1)),e("span",{class:D(P(j.role))},d(U(j.role)),3)]),j.department?(s(),r("div",Cl,[c[25]||(c[25]=e("span",{class:"text-gray-500 dark:text-gray-400 w-20"},"Dipart.:",-1)),e("span",jl,d(j.department.name),1)])):b("",!0)]),j.skills&&j.skills.length>0?(s(),r("div",Ml,[c[26]||(c[26]=e("p",{class:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Competenze:",-1)),e("div",zl,[(s(!0),r(O,null,K(j.skills.slice(0,3),Z=>(s(),r("span",{key:Z.id,class:"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200"},d(Z.name),1))),128)),j.skills.length>3?(s(),r("span",Pl," +"+d(j.skills.length-3),1)):b("",!0)])])):b("",!0),e("div",Sl,[z(N,{to:`/app/personnel/${j.id}`,class:"flex-1 text-center px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md text-sm font-medium transition-colors duration-200"},{default:I(()=>c[27]||(c[27]=[e("svg",{class:"w-4 h-4 inline mr-1",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z","clip-rule":"evenodd"})],-1),V(" Profilo ")])),_:2,__:[27]},1032,["to"]),j.phone?(s(),r("a",{key:0,href:`tel:${j.phone}`,class:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"},c[28]||(c[28]=[e("svg",{class:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"})],-1)]),8,El)):b("",!0),e("a",{href:`mailto:${j.email}`,class:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"},c[29]||(c[29]=[e("svg",{class:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"}),e("path",{d:"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"})],-1)]),8,Al)])]))),128))])])):(s(),r("div",Vl,[c[30]||(c[30]=e("svg",{class:"mx-auto h-12 w-12 text-gray-400 mb-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})],-1)),c[31]||(c[31]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-2"},"Nessun dipendente trovato",-1)),e("p",Tl,d(S.value?"Prova a modificare i filtri di ricerca":"Non ci sono dipendenti da visualizzare"),1)])),T(y).pages>1?(s(),r("div",Il,[e("div",Ll,[e("div",Dl,[c[32]||(c[32]=V(" Mostrando ")),e("span",Hl,d((T(y).page-1)*T(y).per_page+1),1),c[33]||(c[33]=V(" - ")),e("span",Bl,d(Math.min(T(y).page*T(y).per_page,T(y).total)),1),c[34]||(c[34]=V(" di ")),e("span",ql,d(T(y).total),1),c[35]||(c[35]=V(" dipendenti "))]),e("nav",Rl,[T(y).page>1?(s(),r("button",{key:0,onClick:c[3]||(c[3]=j=>B(T(y).page-1)),class:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"}," ← Precedente ")):(s(),r("span",Ul," ← Precedente ")),(s(!0),r(O,null,K(k(),j=>(s(),r(O,{key:j},[j!=="..."?(s(),r("button",{key:0,onClick:Z=>B(j),class:D(["px-3 py-2 border rounded-md text-sm font-medium transition-colors",j===T(y).page?"border-primary-500 dark:border-primary-400 text-white bg-primary-600 dark:bg-primary-500":"border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"])},d(j),11,Nl)):(s(),r("span",Fl,"…"))],64))),128)),T(y).page<T(y).pages?(s(),r("button",{key:2,onClick:c[4]||(c[4]=j=>B(T(y).page+1)),class:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"}," Successiva → ")):(s(),r("span",Ol," Successiva → "))])])])):b("",!0)])])}}},Gl=[{path:"/",component:ze,children:[{path:"",name:"home",component:Ma},{path:"about",name:"about",component:so},{path:"contact",name:"contact",component:Vo},{path:"services",name:"services",component:Qo}]},{path:"/auth",component:ze,children:[{path:"login",name:"login",component:nn},{path:"register",name:"register",component:un}]},{path:"/app",component:Cr,meta:{requiresAuth:!0},children:[{path:"",redirect:"/app/dashboard"},{path:"dashboard",name:"dashboard",component:gi,meta:{requiresAuth:!0,requiredPermission:"view_dashboard"}},{path:"projects",name:"projects",component:Ri,meta:{requiresAuth:!0,requiredPermission:"view_all_projects"}},{path:"projects/create",name:"projects-create",component:()=>G(()=>import("./ProjectCreate.js"),__vite__mapDeps([0,1])),meta:{requiresAuth:!0,requiredPermission:"edit_project"}},{path:"projects/:id",name:"project-view",component:()=>G(()=>import("./ProjectView.js"),__vite__mapDeps([2,1,3])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects"}},{path:"projects/:id/edit",name:"project-edit",component:()=>G(()=>import("./ProjectEdit.js"),__vite__mapDeps([4,1])),meta:{requiresAuth:!0,requiredPermission:"edit_project"}},{path:"personnel",name:"personnel",component:Kl,meta:{requiresAuth:!0,requiredPermission:"view_personnel_data"}},{path:"personnel/directory",name:"personnel-directory",component:()=>G(()=>import("./PersonnelDirectory.js"),__vite__mapDeps([5,1])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data"}},{path:"personnel/orgchart",name:"personnel-orgchart",component:()=>G(()=>import("./PersonnelOrgChart.js"),__vite__mapDeps([6,1])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data"}},{path:"personnel/skills",name:"personnel-skills",component:()=>G(()=>import("./SkillsMatrix.js"),__vite__mapDeps([7,1])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data"}},{path:"personnel/departments",name:"personnel-departments",component:()=>G(()=>import("./DepartmentList.js"),__vite__mapDeps([8,1])),meta:{requiresAuth:!0,requiredPermission:"manage_users"}},{path:"personnel/departments/create",name:"department-create",component:()=>G(()=>import("./DepartmentCreate.js"),__vite__mapDeps([9,1])),meta:{requiresAuth:!0,requiredPermission:"manage_users"}},{path:"personnel/departments/:id",name:"department-view",component:()=>G(()=>import("./DepartmentView.js"),__vite__mapDeps([10,1])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data"}},{path:"personnel/departments/:id/edit",name:"department-edit",component:()=>G(()=>import("./DepartmentEdit.js"),__vite__mapDeps([11,1])),meta:{requiresAuth:!0,requiredPermission:"manage_users"}},{path:"personnel/admin",name:"personnel-admin",component:()=>G(()=>import("./PersonnelAdmin.js"),__vite__mapDeps([12,1])),meta:{requiresAuth:!0,requiredPermission:"admin_access"}},{path:"personnel/:id",name:"personnel-profile",component:()=>G(()=>import("./PersonnelProfile.js"),__vite__mapDeps([13,1])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data"}},{path:"admin",redirect:"/app/admin/users"},{path:"admin/users",name:"admin-users",component:()=>G(()=>import("./Admin.js"),__vite__mapDeps([14,1])),meta:{requiresAuth:!0,requiredPermission:"admin_access"}},{path:"admin/kpi-templates",name:"admin-kpi-templates",component:()=>G(()=>import("./KPITemplates.js"),__vite__mapDeps([15,1])),meta:{requiresAuth:!0,requiredPermission:"admin_access"}},{path:"profile",name:"profile",component:()=>G(()=>import("./Profile.js"),__vite__mapDeps([16,1])),meta:{requiresAuth:!0}},{path:"settings",name:"settings",component:()=>G(()=>import("./Settings.js"),__vite__mapDeps([17,1])),meta:{requiresAuth:!0}}]}],Ie=Re({history:Ue(),routes:Gl});Ie.beforeEach(async(o,n,l)=>{const t=re();if(o.meta.requiresAuth){if(t.sessionChecked||await t.initializeAuth(),!t.isAuthenticated){l("/auth/login");return}if(o.meta.requiredPermission&&!t.hasPermission(o.meta.requiredPermission)){console.warn(`Accesso negato a ${o.path}: permesso '${o.meta.requiredPermission}' richiesto`),l("/app/dashboard");return}}l()});const ce=Ne(Ge),Wl=Fe();ce.use(Wl);ce.use(Ie);const Ql=re();Ql.initializeAuth().then(()=>{console.log("Auth initialized successfully"),ce.mount("#app")}).catch(o=>{console.error("Auth initialization failed:",o),ce.mount("#app")});export{Te as _,Ve as a,vi as b,Ui as c,J as d,he as e,re as u};

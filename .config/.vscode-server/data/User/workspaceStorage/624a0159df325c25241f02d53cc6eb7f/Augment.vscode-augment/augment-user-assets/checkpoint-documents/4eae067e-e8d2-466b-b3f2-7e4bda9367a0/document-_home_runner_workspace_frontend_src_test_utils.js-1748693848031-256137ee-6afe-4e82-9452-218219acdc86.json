{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/utils.js"}, "modifiedCode": "import { mount } from '@vue/test-utils'\nimport { createRouter, createWebHistory } from 'vue-router'\nimport { createPinia } from 'pinia'\nimport { vi } from 'vitest'\n\n/**\n * Create a test router with mock routes\n */\nexport function createTestRouter(routes = []) {\n  const defaultRoutes = [\n    { path: '/', name: 'home', component: { template: '<div>Home</div>' } },\n    { path: '/login', name: 'login', component: { template: '<div>Login</div>' } },\n    { path: '/dashboard', name: 'dashboard', component: { template: '<div>Dashboard</div>' } },\n    { path: '/personnel', name: 'personnel', component: { template: '<div>Personnel</div>' } },\n    { path: '/projects', name: 'projects', component: { template: '<div>Projects</div>' } },\n    ...routes\n  ]\n\n  return createRouter({\n    history: createWebHistory(),\n    routes: defaultRoutes\n  })\n}\n\n/**\n * Create a test pinia store\n */\nexport function createTestPinia() {\n  return createPinia()\n}\n\n/**\n * Mount a Vue component with common test setup\n */\nexport function mountComponent(component, options = {}) {\n  const router = options.router || createTestRouter()\n  const pinia = options.pinia || createTestPinia()\n\n  const defaultOptions = {\n    global: {\n      plugins: [router, pinia],\n      stubs: {\n        RouterLink: true,\n        RouterView: true,\n        ...options.stubs\n      },\n      mocks: {\n        $t: (key) => key, // Mock i18n\n        ...options.mocks\n      }\n    },\n    ...options\n  }\n\n  // Remove router and pinia from options to avoid duplication\n  delete defaultOptions.router\n  delete defaultOptions.pinia\n\n  return mount(component, defaultOptions)\n}\n\n/**\n * Mock API responses for testing\n */\nexport function mockApiResponse(data, success = true, status = 200) {\n  return {\n    ok: success,\n    status,\n    json: async () => ({\n      success,\n      data,\n      message: success ? 'Success' : 'Error'\n    }),\n    text: async () => success ? 'Success' : 'Error',\n    headers: new Headers(),\n  }\n}\n\n/**\n * Mock fetch with specific responses\n */\nexport function mockFetch(responses = {}) {\n  fetch.mockImplementation((url, options) => {\n    const method = options?.method || 'GET'\n    const key = `${method} ${url}`\n    \n    if (responses[key]) {\n      return Promise.resolve(responses[key])\n    }\n    \n    if (responses[url]) {\n      return Promise.resolve(responses[url])\n    }\n    \n    // Default response\n    return Promise.resolve(mockApiResponse({}))\n  })\n}\n\n/**\n * Create mock user data\n */\nexport function createMockUser(overrides = {}) {\n  return {\n    id: 1,\n    username: 'testuser',\n    email: '<EMAIL>',\n    first_name: 'Test',\n    last_name: 'User',\n    role: 'employee',\n    is_active: true,\n    department_id: 1,\n    position: 'Developer',\n    ...overrides\n  }\n}\n\n/**\n * Create mock project data\n */\nexport function createMockProject(overrides = {}) {\n  return {\n    id: 1,\n    name: 'Test Project',\n    description: 'A test project',\n    status: 'active',\n    start_date: '2024-01-01',\n    end_date: '2024-12-31',\n    budget: 10000,\n    expenses: 2000,\n    client_id: 1,\n    ...overrides\n  }\n}\n\n/**\n * Create mock department data\n */\nexport function createMockDepartment(overrides = {}) {\n  return {\n    id: 1,\n    name: 'Engineering',\n    description: 'Software development team',\n    manager_id: 1,\n    user_count: 5,\n    budget: 50000,\n    ...overrides\n  }\n}\n\n/**\n * Create mock skill data\n */\nexport function createMockSkill(overrides = {}) {\n  return {\n    id: 1,\n    name: 'JavaScript',\n    category: 'Programming',\n    description: 'JavaScript programming language',\n    user_count: 3,\n    ...overrides\n  }\n}\n\n/**\n * Wait for Vue's nextTick and any pending promises\n */\nexport async function flushPromises() {\n  return new Promise(resolve => setTimeout(resolve, 0))\n}\n\n/**\n * Simulate user interaction with better timing\n */\nexport async function userEvent(wrapper, action) {\n  await action()\n  await wrapper.vm.$nextTick()\n  await flushPromises()\n}\n\n/**\n * Mock console methods for testing\n */\nexport function mockConsole() {\n  const originalConsole = { ...console }\n  \n  beforeEach(() => {\n    vi.spyOn(console, 'log').mockImplementation(() => {})\n    vi.spyOn(console, 'warn').mockImplementation(() => {})\n    vi.spyOn(console, 'error').mockImplementation(() => {})\n  })\n  \n  afterEach(() => {\n    console.log.mockRestore()\n    console.warn.mockRestore()\n    console.error.mockRestore()\n  })\n  \n  return originalConsole\n}\n\n/**\n * Create a mock store with common state\n */\nexport function createMockStore(initialState = {}) {\n  return {\n    auth: {\n      user: createMockUser(),\n      isAuthenticated: true,\n      permissions: ['view_personnel', 'manage_users'],\n      ...initialState.auth\n    },\n    personnel: {\n      users: [createMockUser()],\n      departments: [createMockDepartment()],\n      skills: [createMockSkill()],\n      ...initialState.personnel\n    },\n    projects: {\n      projects: [createMockProject()],\n      ...initialState.projects\n    },\n    ...initialState\n  }\n}\n"}
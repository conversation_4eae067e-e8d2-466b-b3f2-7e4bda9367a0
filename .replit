modules = ["nodejs-20", "postgresql-16", "python-3.11"]

[nix]
channel = "stable-24_05"
packages = ["openssl", "postgresql"]

[deployment]
deploymentTarget = "autoscale"
run = ["gunicorn", "--bind", "0.0.0.0:5000", "main:app"]

[workflows]
runButton = "Project"

[[workflows.workflow]]
name = "Project"
mode = "parallel"
author = "agent"

[[workflows.workflow.tasks]]
task = "workflow.run"
args = "Start application"

[[workflows.workflow]]
name = "Start application"
author = "agent"
mode = "parallel"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "gunicorn --bind 0.0.0.0:5000 --reuse-port --reload main:app"
waitForPort = 5000

[[ports]]
localPort = 5000
externalPort = 80

[[ports]]
localPort = 5001
externalPort = 3000

[[ports]]
localPort = 5002
externalPort = 3003

[[ports]]
localPort = 5746
externalPort = 3001

[[ports]]
localPort = 5747
externalPort = 3002

[[ports]]
localPort = 24678
externalPort = 4200

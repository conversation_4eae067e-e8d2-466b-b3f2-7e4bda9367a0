{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/vitest.config.js"}, "modifiedCode": "import { defineConfig } from 'vitest/config'\nimport vue from '@vitejs/plugin-vue'\nimport { fileURLToPath, URL } from 'node:url'\n\nexport default defineConfig({\n  plugins: [vue()],\n  test: {\n    environment: 'jsdom',\n    globals: true,\n    setupFiles: ['./src/test/setup.js'],\n    coverage: {\n      provider: 'v8',\n      reporter: ['text', 'json', 'html'],\n      exclude: [\n        'node_modules/',\n        'src/test/',\n        '**/*.d.ts',\n        'dist/',\n        'coverage/',\n        '**/*.config.js',\n        '**/*.config.ts'\n      ],\n      thresholds: {\n        global: {\n          branches: 70,\n          functions: 70,\n          lines: 70,\n          statements: 70\n        }\n      }\n    },\n    include: [\n      'src/**/*.{test,spec}.{js,ts,vue}',\n      'src/test/**/*.{test,spec}.{js,ts,vue}'\n    ],\n    exclude: [\n      'node_modules/',\n      'dist/',\n      'coverage/',\n      'src/test/setup.js'\n    ]\n  },\n  resolve: {\n    alias: {\n      '@': fileURLToPath(new URL('./src', import.meta.url)),\n      '@components': fileURLToPath(new URL('./src/components', import.meta.url)),\n      '@views': fileURLToPath(new URL('./src/views', import.meta.url)),\n      '@stores': fileURLToPath(new URL('./src/stores', import.meta.url)),\n      '@utils': fileURLToPath(new URL('./src/utils', import.meta.url))\n    }\n  }\n})\n"}
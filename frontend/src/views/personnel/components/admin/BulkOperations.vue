<template>
  <div class="space-y-6">
    <!-- Header -->
    <div>
      <h3 class="text-lg font-medium text-gray-900 dark:text-white">Operazioni di Massa</h3>
      <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
        Import/Export dati e operazioni bulk
      </p>
    </div>

    <!-- Operations Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- Import Users -->
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div class="flex items-center mb-4">
          <svg class="w-6 h-6 text-green-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
          </svg>
          <h4 class="text-lg font-medium text-gray-900 dark:text-white">Import Dipendenti</h4>
        </div>
        <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">
          Importa dipendenti da file CSV con dati contrattuali completi
        </p>
        <div class="space-y-3">
          <button @click="downloadTemplate"
                  :disabled="loading.template"
                  class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50">
            <svg v-if="loading.template" class="animate-spin w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <svg v-else class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            {{ loading.template ? 'Generando...' : 'Scarica Template CSV' }}
          </button>

          <div class="relative">
            <input ref="fileInput"
                   type="file"
                   accept=".csv"
                   @change="handleFileUpload"
                   class="hidden">
            <button @click="$refs.fileInput.click()"
                    :disabled="loading.import"
                    class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 disabled:opacity-50">
              <svg v-if="loading.import" class="animate-spin w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <svg v-else class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
              </svg>
              {{ loading.import ? 'Importando...' : 'Carica File CSV' }}
            </button>
          </div>

          <!-- Import Progress -->
          <div v-if="importProgress.show" class="mt-3 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm font-medium text-blue-800 dark:text-blue-200">Import in corso...</span>
              <span class="text-sm text-blue-600 dark:text-blue-400">{{ importProgress.processed }}/{{ importProgress.total }}</span>
            </div>
            <div class="w-full bg-blue-200 dark:bg-blue-800 rounded-full h-2">
              <div class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                   :style="{ width: `${(importProgress.processed / importProgress.total) * 100}%` }"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Export Data -->
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div class="flex items-center mb-4">
          <svg class="w-6 h-6 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
          </svg>
          <h4 class="text-lg font-medium text-gray-900 dark:text-white">Export Dati</h4>
        </div>
        <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">
          Esporta dati del personale in vari formati
        </p>
        <div class="space-y-3">
          <button @click="exportData('full')"
                  :disabled="loading.export"
                  class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50">
            <svg v-if="loading.export" class="animate-spin w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <svg v-else class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            {{ loading.export ? 'Esportando...' : 'Export Completo CSV' }}
          </button>

          <button @click="exportData('contacts')"
                  :disabled="loading.export"
                  class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50">
            <svg v-if="loading.export" class="animate-spin w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <svg v-else class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            {{ loading.export ? 'Esportando...' : 'Export Solo Contatti' }}
          </button>

          <button @click="showExportModal = true"
                  class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
            </svg>
            Export Personalizzato
          </button>
        </div>
      </div>

      <!-- Bulk Updates -->
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div class="flex items-center mb-4">
          <svg class="w-6 h-6 text-purple-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
          </svg>
          <h4 class="text-lg font-medium text-gray-900 dark:text-white">Aggiornamenti di Massa</h4>
        </div>
        <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">
          Applica modifiche a più dipendenti contemporaneamente
        </p>
        <div class="space-y-3">
          <button @click="showBulkDepartmentModal = true"
                  class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
            </svg>
            Assegnazione Dipartimenti
          </button>

          <button @click="showBulkSkillsModal = true"
                  class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
            </svg>
            Assegnazione Competenze
          </button>

          <button @click="showBulkRoleModal = true"
                  class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
            </svg>
            Modifica Ruoli
          </button>
        </div>
      </div>

      <!-- Data Cleanup -->
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div class="flex items-center mb-4">
          <svg class="w-6 h-6 text-red-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
          </svg>
          <h4 class="text-lg font-medium text-gray-900 dark:text-white">Pulizia Dati</h4>
        </div>
        <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">
          Strumenti per la manutenzione e pulizia dei dati
        </p>
        <div class="space-y-3">
          <button @click="verifyDataIntegrity"
                  :disabled="loading.verify"
                  class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50">
            <svg v-if="loading.verify" class="animate-spin w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <svg v-else class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            {{ loading.verify ? 'Verificando...' : 'Verifica Integrità Dati' }}
          </button>

          <button @click="showCleanupModal = true"
                  class="w-full inline-flex justify-center items-center px-4 py-2 border border-red-300 dark:border-red-600 shadow-sm text-sm font-medium rounded-md text-red-700 dark:text-red-300 bg-white dark:bg-gray-800 hover:bg-red-50 dark:hover:bg-red-900/20">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
            Rimuovi Utenti Inattivi
          </button>

          <button @click="showDataCleanupModal = true"
                  class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            Pulizia Avanzata
          </button>
        </div>
      </div>
    </div>

    <!-- Status Messages -->
    <div v-if="statusMessage" class="mt-6 p-4 rounded-lg" :class="statusMessage.type === 'success' ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800' : 'bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800'">
      <div class="flex">
        <svg v-if="statusMessage.type === 'success'" class="w-5 h-5 text-green-400 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
        </svg>
        <svg v-else class="w-5 h-5 text-red-400 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
        </svg>
        <div>
          <h3 class="text-sm font-medium" :class="statusMessage.type === 'success' ? 'text-green-800 dark:text-green-200' : 'text-red-800 dark:text-red-200'">
            {{ statusMessage.title }}
          </h3>
          <p class="text-sm mt-1" :class="statusMessage.type === 'success' ? 'text-green-700 dark:text-green-300' : 'text-red-700 dark:text-red-300'">
            {{ statusMessage.message }}
          </p>
          <div v-if="statusMessage.details && statusMessage.details.length > 0" class="mt-2">
            <ul class="text-xs space-y-1" :class="statusMessage.type === 'success' ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'">
              <li v-for="detail in statusMessage.details" :key="detail">• {{ detail }}</li>
            </ul>
          </div>
        </div>
        <button @click="statusMessage = null" class="ml-auto">
          <svg class="w-4 h-4" :class="statusMessage.type === 'success' ? 'text-green-400' : 'text-red-400'" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// Emits
const emit = defineEmits(['operation-completed'])

// Reactive state
const loading = ref({
  template: false,
  import: false,
  export: false,
  verify: false
})

const importProgress = ref({
  show: false,
  processed: 0,
  total: 0
})

const statusMessage = ref(null)

// Modal states
const showExportModal = ref(false)
const showBulkDepartmentModal = ref(false)
const showBulkSkillsModal = ref(false)
const showBulkRoleModal = ref(false)
const showCleanupModal = ref(false)
const showDataCleanupModal = ref(false)

// Methods
const downloadTemplate = async () => {
  loading.value.template = true

  try {
    // Generate CSV template with all required fields
    const headers = [
      'email', 'first_name', 'last_name', 'phone', 'department_id',
      'hire_date', 'employment_type', 'salary', 'role', 'is_active'
    ]

    const csvContent = headers.join(',') + '\n' +
      '<EMAIL>,Mario,Rossi,+39 ************,1,2024-01-15,full_time,45000,employee,true\n' +
      '<EMAIL>,Giulia,Bianchi,+39 ************,2,2024-02-01,part_time,30000,manager,true'

    // Create and download file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', 'template_dipendenti.csv')
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    showStatusMessage('success', 'Template scaricato', 'Il template CSV è stato scaricato con successo.')
  } catch (err) {
    console.error('Error downloading template:', err)
    showStatusMessage('error', 'Errore download', 'Impossibile scaricare il template.')
  } finally {
    loading.value.template = false
  }
}

const handleFileUpload = async (event) => {
  const file = event.target.files[0]
  if (!file) return

  loading.value.import = true
  importProgress.value.show = true
  importProgress.value.processed = 0

  try {
    const formData = new FormData()
    formData.append('file', file)

    const response = await fetch('/api/personnel/import', {
      method: 'POST',
      credentials: 'include',
      body: formData
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()

    if (data.success) {
      showStatusMessage('success', 'Import completato',
        `Importati ${data.data.imported} dipendenti su ${data.data.total} righe processate.`,
        data.data.errors || [])
      emit('operation-completed', 'import')
    } else {
      throw new Error(data.message || 'Errore durante l\'import')
    }
  } catch (err) {
    console.error('Error importing file:', err)
    showStatusMessage('error', 'Errore import', err.message)
  } finally {
    loading.value.import = false
    importProgress.value.show = false
    // Reset file input
    event.target.value = ''
  }
}

const exportData = async (type) => {
  loading.value.export = true

  try {
    const endpoint = type === 'full' ? '/api/personnel/export' : '/api/personnel/export/contacts'

    const response = await fetch(endpoint, {
      credentials: 'include'
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    // Get filename from response headers or use default
    const contentDisposition = response.headers.get('content-disposition')
    let filename = `export_${type}_${new Date().toISOString().split('T')[0]}.csv`

    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename="(.+)"/)
      if (filenameMatch) {
        filename = filenameMatch[1]
      }
    }

    const blob = await response.blob()
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    showStatusMessage('success', 'Export completato', `I dati sono stati esportati in ${filename}`)
  } catch (err) {
    console.error('Error exporting data:', err)
    showStatusMessage('error', 'Errore export', err.message)
  } finally {
    loading.value.export = false
  }
}

const verifyDataIntegrity = async () => {
  loading.value.verify = true

  try {
    const response = await fetch('/api/personnel/verify', {
      credentials: 'include'
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()

    if (data.success) {
      const issues = data.data.issues || []
      if (issues.length === 0) {
        showStatusMessage('success', 'Verifica completata', 'Nessun problema di integrità rilevato.')
      } else {
        showStatusMessage('error', 'Problemi rilevati',
          `Trovati ${issues.length} problemi di integrità dati.`, issues)
      }
    } else {
      throw new Error(data.message || 'Errore durante la verifica')
    }
  } catch (err) {
    console.error('Error verifying data:', err)
    showStatusMessage('error', 'Errore verifica', err.message)
  } finally {
    loading.value.verify = false
  }
}

const showStatusMessage = (type, title, message, details = []) => {
  statusMessage.value = { type, title, message, details }

  // Auto-hide success messages after 5 seconds
  if (type === 'success') {
    setTimeout(() => {
      statusMessage.value = null
    }, 5000)
  }
}

const handleCustomExport = (config) => {
  showExportModal.value = false
  // Handle custom export configuration
  console.log('Custom export config:', config)
}

const handleBulkOperationCompleted = (operation) => {
  showStatusMessage('success', 'Operazione completata',
    `L'operazione di massa "${operation}" è stata completata con successo.`)
  emit('operation-completed', operation)
}

const handleCleanupCompleted = (operation) => {
  showStatusMessage('success', 'Pulizia completata',
    `L'operazione di pulizia "${operation}" è stata completata con successo.`)
  emit('operation-completed', operation)
}
</script>

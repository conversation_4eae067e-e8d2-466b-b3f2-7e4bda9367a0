"""
Integration tests for Personnel Admin workflow.
Tests complete user journeys and cross-module interactions.
"""

import pytest
import json
from datetime import datetime, date
from io import BytesIO

from models import User, Department, Skill, UserSkill, UserProfile
from extensions import db


class TestPersonnelAdminWorkflow:
    """Test complete personnel admin workflows."""

    def test_complete_user_management_workflow(self, client, auth, sample_departments):
        """Test complete user management workflow from creation to deletion."""
        auth.login(username='admin', password='password')

        # 1. Create a new user
        user_data = {
            'username': 'workflow_test',
            'email': '<EMAIL>',
            'first_name': 'Workflow',
            'last_name': 'Test',
            'role': 'employee',
            'department_id': 1,
            'position': 'Test Engineer'
        }

        response = client.post('/api/personnel/users',
                             data=json.dumps(user_data),
                             content_type='application/json')
        assert response.status_code == 200

        data = json.loads(response.data)
        user_id = data['data']['user']['id']

        # 2. Update user profile
        profile_data = {
            'hire_date': '2024-01-15',
            'employment_type': 'full_time',
            'salary': 45000,
            'bio': 'Test user for workflow testing'
        }

        response = client.put(f'/api/personnel/users/{user_id}/profile',
                            data=json.dumps(profile_data),
                            content_type='application/json')
        assert response.status_code == 200

        # 3. Assign skills to user
        skill_data = {
            'skill_id': 1,
            'level': 4
        }

        response = client.post(f'/api/personnel/users/{user_id}/skills',
                             data=json.dumps(skill_data),
                             content_type='application/json')
        assert response.status_code == 200

        # 4. Verify user appears in lists with all data
        response = client.get('/api/personnel/users')
        assert response.status_code == 200

        data = json.loads(response.data)
        created_user = next(u for u in data['data']['users'] if u['id'] == user_id)
        assert created_user['email'] == '<EMAIL>'
        assert created_user['department_id'] == 1
        assert len(created_user['skills']) > 0

        # 5. Export data and verify user is included
        response = client.get('/api/personnel/export')
        assert response.status_code == 200

        csv_content = response.data.decode('utf-8')
        assert '<EMAIL>' in csv_content

        # 6. Deactivate user
        response = client.put(f'/api/personnel/users/{user_id}',
                            data=json.dumps({'is_active': False}),
                            content_type='application/json')
        assert response.status_code == 200

        # 7. Verify user is inactive in lists
        response = client.get('/api/personnel/users?is_active=false')
        assert response.status_code == 200

        data = json.loads(response.data)
        inactive_user = next(u for u in data['data']['users'] if u['id'] == user_id)
        assert inactive_user['is_active'] is False

    def test_department_management_workflow(self, client, auth, sample_users):
        """Test complete department management workflow."""
        auth.login(username='admin', password='password')

        # 1. Create a new department
        dept_data = {
            'name': 'Test Department',
            'description': 'Department for workflow testing',
            'manager_id': 1,
            'budget': 100000
        }

        response = client.post('/api/personnel/departments',
                             data=json.dumps(dept_data),
                             content_type='application/json')
        assert response.status_code == 200

        data = json.loads(response.data)
        dept_id = data['data']['department']['id']

        # 2. Assign users to department
        user_update_data = {'department_id': dept_id}

        response = client.put('/api/personnel/users/2',
                            data=json.dumps(user_update_data),
                            content_type='application/json')
        assert response.status_code == 200

        # 3. Verify department shows up in orgchart
        response = client.get('/api/personnel/orgchart')
        assert response.status_code == 200

        data = json.loads(response.data)
        dept_found = any(d['id'] == dept_id for d in data['data']['departments'])
        assert dept_found

        # 4. Update department budget
        update_data = {'budget': 150000}

        response = client.put(f'/api/personnel/departments/{dept_id}',
                            data=json.dumps(update_data),
                            content_type='application/json')
        assert response.status_code == 200

        # 5. Verify updated budget in department list
        response = client.get('/api/personnel/departments')
        assert response.status_code == 200

        data = json.loads(response.data)
        updated_dept = next(d for d in data['data']['departments'] if d['id'] == dept_id)
        assert updated_dept['budget'] == 150000

    def test_skills_management_workflow(self, client, auth, sample_users):
        """Test complete skills management workflow."""
        auth.login(username='admin', password='password')

        # 1. Create a new skill
        skill_data = {
            'name': 'Workflow Testing',
            'category': 'Testing',
            'description': 'Skill for testing workflows'
        }

        response = client.post('/api/personnel/skills',
                             data=json.dumps(skill_data),
                             content_type='application/json')
        assert response.status_code == 200

        data = json.loads(response.data)
        skill_id = data['data']['skill']['id']

        # 2. Assign skill to multiple users
        for user_id in [1, 2]:
            skill_assignment = {
                'skill_id': skill_id,
                'level': 3
            }

            response = client.post(f'/api/personnel/users/{user_id}/skills',
                                 data=json.dumps(skill_assignment),
                                 content_type='application/json')
            assert response.status_code == 200

        # 3. Verify skill appears in skills matrix
        response = client.get('/api/personnel/skills')
        assert response.status_code == 200

        data = json.loads(response.data)
        created_skill = next(s for s in data['data']['skills'] if s['id'] == skill_id)
        assert created_skill['user_count'] >= 2

        # 4. Update skill information
        update_data = {
            'description': 'Updated description for workflow testing'
        }

        response = client.put(f'/api/personnel/skills/{skill_id}',
                            data=json.dumps(update_data),
                            content_type='application/json')
        assert response.status_code == 200

        # 5. Try to delete skill (should fail because it's assigned)
        response = client.delete(f'/api/personnel/skills/{skill_id}')
        assert response.status_code == 400

        # 6. Remove skill assignments and then delete
        for user_id in [1, 2]:
            response = client.delete(f'/api/personnel/users/{user_id}/skills/{skill_id}')
            # May succeed or fail depending on existing data

        # Now delete should work
        response = client.delete(f'/api/personnel/skills/{skill_id}')
        # May succeed now

    def test_bulk_operations_workflow(self, client, auth, sample_departments):
        """Test complete bulk operations workflow."""
        auth.login(username='admin', password='password')

        # 1. Import users via CSV
        csv_content = """email,first_name,last_name,phone,department_id,role,is_active
<EMAIL>,Bulk,User1,+39 ************,1,employee,true
<EMAIL>,Bulk,User2,+39 ************,1,employee,true
<EMAIL>,Bulk,User3,+39 ************,1,manager,true"""

        csv_file = BytesIO(csv_content.encode('utf-8'))
        csv_file.name = 'bulk_test.csv'

        response = client.post('/api/personnel/import',
                             data={'file': (csv_file, 'bulk_test.csv')},
                             content_type='multipart/form-data')
        assert response.status_code == 200

        data = json.loads(response.data)
        assert data['data']['imported'] >= 0

        # 2. Verify imported users appear in system
        response = client.get('/api/personnel/users?search=bulk')
        assert response.status_code == 200

        data = json.loads(response.data)
        bulk_users = [u for u in data['data']['users'] if 'bulk' in u['email']]
        assert len(bulk_users) >= 0  # May be 0 if import failed due to existing data

        # 3. Export all data
        response = client.get('/api/personnel/export')
        assert response.status_code == 200

        csv_content = response.data.decode('utf-8')
        assert 'email' in csv_content
        assert 'first_name' in csv_content

        # 4. Export contacts only
        response = client.get('/api/personnel/export/contacts')
        assert response.status_code == 200

        csv_content = response.data.decode('utf-8')
        assert 'Nome' in csv_content
        assert 'Email' in csv_content

        # 5. Verify data integrity
        response = client.get('/api/personnel/verify')
        assert response.status_code == 200

        data = json.loads(response.data)
        assert 'issues' in data['data']
        assert isinstance(data['data']['issues'], list)

    def test_cross_module_integration(self, client, auth, sample_users, sample_departments, sample_skills):
        """Test integration between personnel and other modules."""
        auth.login(username='admin', password='password')

        # 1. Verify personnel data affects dashboard stats
        response = client.get('/api/dashboard/stats')
        if response.status_code == 200:
            data = json.loads(response.data)
            # Should include personnel-related statistics

        # 2. Verify personnel data is available for project assignments
        # This would test integration with projects module
        response = client.get('/api/personnel/users?role=manager,admin')
        assert response.status_code == 200

        data = json.loads(response.data)
        managers = [u for u in data['data']['users'] if u['role'] in ['manager', 'admin']]
        assert len(managers) > 0

        # 3. Test orgchart data consistency
        response = client.get('/api/personnel/orgchart')
        assert response.status_code == 200

        data = json.loads(response.data)
        assert 'departments' in data['data']
        assert 'stats' in data['data']

        # Verify stats are consistent with actual data
        stats = data['data']['stats']
        assert stats['total_employees'] >= 0
        assert stats['total_departments'] >= 0

    def test_error_handling_and_recovery(self, client, auth):
        """Test error handling and recovery scenarios."""
        auth.login(username='admin', password='password')

        # 1. Test invalid data handling
        invalid_user_data = {
            'email': 'invalid-email',  # Invalid email format
            'first_name': '',  # Empty required field
            'role': 'invalid_role'  # Invalid role
        }

        response = client.post('/api/personnel/users',
                             data=json.dumps(invalid_user_data),
                             content_type='application/json')
        assert response.status_code == 400

        # 2. Test non-existent resource access
        response = client.get('/api/personnel/users/99999')
        assert response.status_code == 404

        response = client.get('/api/personnel/departments/99999')
        assert response.status_code == 404

        # 3. Test invalid file upload
        invalid_file = BytesIO(b'not a csv file')
        invalid_file.name = 'test.txt'

        response = client.post('/api/personnel/import',
                             data={'file': (invalid_file, 'test.txt')},
                             content_type='multipart/form-data')
        assert response.status_code == 400

        # 4. Test constraint violations
        # Try to delete department with users (if any exist)
        response = client.get('/api/personnel/departments')
        if response.status_code == 200:
            data = json.loads(response.data)
            dept_with_users = next((d for d in data['data']['departments'] if d['user_count'] > 0), None)
            
            if dept_with_users:
                response = client.delete(f'/api/personnel/departments/{dept_with_users["id"]}')
                assert response.status_code == 400  # Should fail due to users assigned

        # System should remain stable after all these error scenarios
        response = client.get('/api/personnel/users')
        assert response.status_code == 200

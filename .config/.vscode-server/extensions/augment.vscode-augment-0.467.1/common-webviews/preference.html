<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Preference</title>
    <script nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
/**
 * Monaco bootstrap script
 *
 * This script is included directly in HTML files to load Monaco editor.
 * It's kept as a simple JS file to avoid any build/transpilation requirements.
 */

// Define the Monaco CDN version
const MONACO_VERSION = "0.52.2";
const MONACO_CDN_BASE = `https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/${MONACO_VERSION}/min`;

// Initialize augmentDeps if it doesn't exist
window.augmentDeps = window.augmentDeps || {};

// Create a promise that will resolve when Monaco is ready
let monacoResolve;
window.augmentDeps.monaco = new Promise((resolve) => {
  monacoResolve = resolve;
});

// If Monaco is already loaded, don't load it again
if (window.monaco) {
  console.log("Monaco already loaded, skipping bootstrap");
  initializeMonacoDeps();
} else {
  // Load the Monaco loader script
  const loaderScript = document.createElement("script");
  loaderScript.src = `${MONACO_CDN_BASE}/vs/loader.min.js`;
  loaderScript.onload = initializeMonaco;
  document.head.appendChild(loaderScript);
}

// Initialize Monaco after the loader script has loaded
function initializeMonaco() {
  // require is provided by loader.min.js
  require.config({
    paths: { vs: `${MONACO_CDN_BASE}/vs` },
  });

  require(["vs/editor/editor.main"], () => {
    initializeMonacoDeps();
  });
}

// Initialize Monaco dependencies after Monaco has loaded
function initializeMonacoDeps() {
  // Resolve the monaco promise
  if (monacoResolve) {
    monacoResolve(window.monaco);
  }
}

</script>
    <meta property="csp-nonce" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <script type="module" crossorigin src="./assets/preference-RZDI7OFW.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ=="></script>
    <link rel="modulepreload" crossorigin href="./assets/SpinnerAugment-BRymMBwV.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/BaseButton-rKFNr-KO.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/github-DCBOV_oD.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/pen-to-square-DsQhBKje.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/augment-logo-Cb6FLr8P.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/IconButtonAugment-5yqT_m78.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/globals-D0QH3NT1.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/Content-CZt_q_72.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/TextTooltipAugment-VmEmcMVL.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/types-LfaCSdmF.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/chat-types-NgqNgjwU.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/test_service_pb-B6vKXZrG.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/file-paths-BcSg4gks.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/types-DvVg976p.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/open-in-new-window-_CQmfLgB.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/folder-opened-qXv2xhk3.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/types-BSMhNRWH.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/CardAugment-BpvKVhgc.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/index-C57dba63.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/TextAreaAugment-DJ6NCdxI.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/preload-helper-Dv6uf1Os.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/toggleHighContrast-CwIv4U26.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/index-C59i2ECO.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/diff-utils-UT8EbVpu.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/folder-Dee44ws-.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/keypress-DD1aQVr0.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/await_block-B6zp5aG7.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/ButtonAugment-B4rD0Iq1.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/expand-DqlmSj23.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/mcp-logo-CaRmgfKF.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/ellipsis-bUrc34Ic.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/LanguageIcon-CLfcbvMk.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/next-edit-types-904A5ehg.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/IconFilePath-DzNJjZOv.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/magnifying-glass-Dmvedn_X.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/MaterialIcon-D3827jJW.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/Filespan-V8rQ2geT.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/chevron-down-B8lwMNrs.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/lodash-xZzKttBF.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/terminal-BinWa3Yp.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="modulepreload" crossorigin href="./assets/AugmentMessage-D6u8uU-A.js" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="stylesheet" crossorigin href="./assets/SpinnerAugment-DnPofOlT.css" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="stylesheet" crossorigin href="./assets/BaseButton-DvMdfQ3F.css" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="stylesheet" crossorigin href="./assets/github-DDCjb6F1.css" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="stylesheet" crossorigin href="./assets/pen-to-square-Dvw-pMXw.css" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="stylesheet" crossorigin href="./assets/TextTooltipAugment-BIMZ5dVo.css" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="stylesheet" crossorigin href="./assets/Content-D0WttAzY.css" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="stylesheet" crossorigin href="./assets/folder-D1GL7JhZ.css" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="stylesheet" crossorigin href="./assets/diff-utils-CiAPKcVt.css" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="stylesheet" crossorigin href="./assets/index-McRKs1sU.css" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="stylesheet" crossorigin href="./assets/ButtonAugment-CNK8zC8i.css" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="stylesheet" crossorigin href="./assets/index-eY12-hdZ.css" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="stylesheet" crossorigin href="./assets/IconButtonAugment-BTu-iglL.css" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="stylesheet" crossorigin href="./assets/IconFilePath-CiKel2Kp.css" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="stylesheet" crossorigin href="./assets/MaterialIcon-BO_oU5T3.css" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="stylesheet" crossorigin href="./assets/Filespan-tclW2Ian.css" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="stylesheet" crossorigin href="./assets/CardAugment-BAo8Ti0V.css" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="stylesheet" crossorigin href="./assets/LanguageIcon-D78BqCXT.css" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="stylesheet" crossorigin href="./assets/toggleHighContrast-D4zjdeIP.css" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="stylesheet" crossorigin href="./assets/AugmentMessage-LUamDQhY.css" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="stylesheet" crossorigin href="./assets/TextAreaAugment-J75lFxU7.css" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
    <link rel="stylesheet" crossorigin href="./assets/preference-Dn6mpF6J.css" nonce="nonce-zOw49Xt7waMsIvNFXcZ3cQ==">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>

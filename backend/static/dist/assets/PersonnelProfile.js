import{r as c,f as j,A as P,u as L,w as D,c as a,g as n,j as e,t as l,m as f,z as $,F as u,k as g,I as E,l as N,o as r,n as h}from"./vendor.js";import{u as S}from"./personnel.js";import{a as A}from"./app.js";const I={class:"personnel-profile"},O={key:0,class:"flex justify-center items-center h-64"},U={key:1,class:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6"},q={class:"flex"},F={class:"text-sm text-red-700 dark:text-red-300 mt-1"},R={key:2,class:"space-y-6"},G={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},J={class:"bg-gradient-to-r from-blue-500 to-purple-600 px-6 py-8"},K={class:"flex items-center space-x-6"},Q={class:"flex-shrink-0"},W={class:"w-24 h-24 bg-white rounded-full flex items-center justify-center shadow-lg"},X=["src","alt"],Y={key:1,class:"w-24 h-24 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center"},Z={class:"flex-1 text-white"},ee={class:"text-3xl font-bold"},te={class:"text-blue-100 text-lg"},se={class:"flex items-center space-x-4 mt-2"},ae={key:0,class:"inline-flex items-center px-3 py-1 rounded-full text-sm bg-white/20 text-white"},re={class:"inline-flex items-center px-3 py-1 rounded-full text-sm bg-white/20 text-white"},oe={class:"flex-shrink-0"},le={key:0,class:"px-6 py-4 bg-gray-50 dark:bg-gray-700"},ie={class:"flex items-center justify-between mb-2"},ne={class:"text-sm text-gray-500 dark:text-gray-400"},de={class:"w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2"},ce={class:"grid grid-cols-1 lg:grid-cols-3 gap-6"},ue={class:"lg:col-span-1 space-y-6"},ge={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},xe={class:"space-y-3"},he={class:"flex items-center"},ye={class:"text-gray-900 dark:text-white"},me={key:0,class:"flex items-center"},pe={class:"text-gray-900 dark:text-white"},ve={key:1,class:"flex items-center"},fe={class:"text-gray-900 dark:text-white"},ke={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},_e={key:0,class:"space-y-3"},we={class:"text-sm font-medium text-gray-900 dark:text-white"},be={class:"flex items-center space-x-2"},ze={class:"flex space-x-1"},Ce={key:0,class:"text-xs text-green-600 dark:text-green-400"},Me={key:0,class:"text-sm text-gray-500 dark:text-gray-400 text-center pt-2"},Ve={key:1,class:"text-gray-500 dark:text-gray-400 text-sm"},He={key:0,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},je={class:"text-gray-700 dark:text-gray-300 text-sm leading-relaxed"},Te={class:"lg:col-span-2"},Be={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},Pe={class:"border-b border-gray-200 dark:border-gray-700"},Le={class:"-mb-px flex space-x-8 px-6","aria-label":"Tabs"},De=["onClick"],$e={key:0,class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},Ee={key:1,class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},Ne={key:2,class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},Se={key:3,class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},Ae={key:4,class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},Ie={key:5,class:"ml-2 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 py-0.5 px-2 rounded-full text-xs"},Oe={class:"p-6"},Ue={key:0,class:"space-y-4"},qe={key:0},Fe={class:"flex items-center justify-between"},Re={class:"font-medium text-gray-900 dark:text-white"},Ge={class:"text-sm text-gray-500 dark:text-gray-400"},Je={key:1,class:"text-center py-8"},Ke={key:1,class:"space-y-4"},Qe={key:0},We={class:"flex items-start justify-between"},Xe={class:"flex-1"},Ye={class:"font-medium text-gray-900 dark:text-white"},Ze={class:"text-sm text-gray-500 dark:text-gray-400 mt-1"},et={class:"flex items-center mt-2 space-x-4"},tt={key:0,class:"text-xs text-gray-500 dark:text-gray-400"},st={key:1,class:"text-center py-8"},at={key:2,class:"space-y-4"},rt={key:0},ot={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},lt={class:"flex items-center justify-between mb-2"},it={class:"font-medium text-gray-900 dark:text-white"},nt={key:0,class:"text-green-600 dark:text-green-400 text-sm"},dt={key:0,class:"text-sm text-gray-500 dark:text-gray-400 mb-2"},ct={class:"flex items-center justify-between"},ut={class:"flex items-center space-x-2"},gt={class:"flex space-x-1"},xt={key:0,class:"text-sm text-gray-500 dark:text-gray-400"},ht={key:1,class:"text-center py-8"},yt={key:3,class:"space-y-4"},mt={key:0},pt={class:"overflow-x-auto"},vt={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},ft={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},kt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},_t={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},wt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"},bt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},zt={class:"px-6 py-4 text-sm text-gray-500 dark:text-gray-400"},Ct={key:1,class:"text-center py-8"},Mt={key:4,class:"space-y-4"},Bt={__name:"PersonnelProfile",setup(Vt){const z=L();N(),S();const{hasPermission:k}=A(),o=c(null),y=c([]),p=c([]),v=c([]),_=c(!1),m=c(null),w=c(!1),x=c("projects"),T=j(()=>{if(!o.value)return!1;try{return k&&typeof k=="function"?k("edit_personnel_data"):!1}catch(i){return console.warn("Permission check failed:",i),!1}}),B=j(()=>{var i,t;return[{id:"projects",name:"Progetti",count:y.value.length},{id:"tasks",name:"Task",count:p.value.length},{id:"skills",name:"Competenze",count:((t=(i=o.value)==null?void 0:i.skills)==null?void 0:t.length)||0},{id:"timesheet",name:"Timesheet",count:v.value.length},{id:"cv",name:"CV"}]}),b=i=>i?new Date(i).toLocaleDateString("it-IT",{year:"numeric",month:"long",day:"numeric"}):"",C=async i=>{_.value=!0,m.value=null;try{const t=await fetch(`/api/personnel/users/${i}`,{credentials:"include"});if(!t.ok)throw new Error(`HTTP ${t.status}: ${t.statusText}`);const s=await t.json();if(s.success)o.value=s.data.user;else throw new Error(s.message||"Errore nel caricamento del profilo")}catch(t){console.error("Error fetching user profile:",t),m.value=t.message}finally{_.value=!1}},M=async i=>{try{const t=await fetch(`/api/tasks?assignee_id=${i}&limit=20`,{credentials:"include"});if(!t.ok)throw new Error(`HTTP ${t.status}: ${t.statusText}`);const s=await t.json();s.success&&(p.value=s.data.tasks||[])}catch(t){console.error("Error fetching user tasks:",t)}},V=async i=>{try{const t=new Date,s=new Date;s.setDate(s.getDate()-30);const d=await fetch(`/api/timesheets?user_id=${i}&start_date=${s.toISOString().split("T")[0]}&end_date=${t.toISOString().split("T")[0]}&limit=50`,{credentials:"include"});if(!d.ok)throw new Error(`HTTP ${d.status}: ${d.statusText}`);const H=await d.json();H.success&&(v.value=H.data||[])}catch(t){console.error("Error fetching user timesheets:",t)}};return P(async()=>{const i=z.params.id;if(!i){m.value="ID utente non specificato";return}await C(i),o.value&&(y.value=o.value.projects||[],await M(i),await V(i))}),D(()=>z.params.id,async i=>{i&&(await C(i),o.value&&(y.value=o.value.projects||[],await M(i),await V(i)))}),(i,t)=>(r(),a("div",I,[_.value?(r(),a("div",O,t[1]||(t[1]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):m.value?(r(),a("div",U,[e("div",q,[t[3]||(t[3]=e("svg",{class:"w-5 h-5 text-red-400 mr-2 mt-0.5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z","clip-rule":"evenodd"})],-1)),e("div",null,[t[2]||(t[2]=e("h3",{class:"text-sm font-medium text-red-800 dark:text-red-200"},"Errore nel caricamento del profilo",-1)),e("p",F,l(m.value),1)])])])):o.value?(r(),a("div",R,[e("div",G,[e("div",J,[e("div",K,[e("div",Q,[e("div",W,[o.value.profile_image?(r(),a("img",{key:0,src:o.value.profile_image,alt:o.value.full_name,class:"w-24 h-24 rounded-full object-cover"},null,8,X)):(r(),a("div",Y,t[4]||(t[4]=[e("svg",{class:"w-12 h-12 text-gray-400 dark:text-gray-300",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z","clip-rule":"evenodd"})],-1)])))])]),e("div",Z,[e("h1",ee,l(o.value.full_name),1),e("p",te,l(o.value.position||"Posizione non specificata"),1),e("div",se,[o.value.department?(r(),a("span",ae,[t[5]||(t[5]=e("svg",{class:"w-4 h-4 mr-1",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z","clip-rule":"evenodd"})],-1)),f(" "+l(o.value.department.name),1)])):n("",!0),e("span",re,[t[6]||(t[6]=e("svg",{class:"w-4 h-4 mr-1",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z","clip-rule":"evenodd"})],-1)),f(" "+l(o.value.role),1)])])]),e("div",oe,[T.value?(r(),a("button",{key:0,onClick:t[0]||(t[0]=s=>w.value=!w.value),class:"bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-colors duration-200"},[t[7]||(t[7]=e("svg",{class:"w-5 h-5 inline mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"})],-1)),f(" "+l(w.value?"Annulla":"Modifica"),1)])):n("",!0)])])]),o.value.profile&&o.value.profile.profile_completion!==void 0?(r(),a("div",le,[e("div",ie,[t[8]||(t[8]=e("span",{class:"text-sm font-medium text-gray-700 dark:text-gray-300"},"Completamento Profilo",-1)),e("span",ne,l(o.value.profile.profile_completion)+"%",1)]),e("div",de,[e("div",{class:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:$({width:o.value.profile.profile_completion+"%"})},null,4)])])):n("",!0)]),e("div",ce,[e("div",ue,[e("div",ge,[t[12]||(t[12]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Informazioni di Contatto",-1)),e("div",xe,[e("div",he,[t[9]||(t[9]=e("svg",{class:"w-5 h-5 text-gray-400 mr-3",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"}),e("path",{d:"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"})],-1)),e("span",ye,l(o.value.email),1)]),o.value.phone?(r(),a("div",me,[t[10]||(t[10]=e("svg",{class:"w-5 h-5 text-gray-400 mr-3",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"})],-1)),e("span",pe,l(o.value.phone),1)])):n("",!0),o.value.hire_date?(r(),a("div",ve,[t[11]||(t[11]=e("svg",{class:"w-5 h-5 text-gray-400 mr-3",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z","clip-rule":"evenodd"})],-1)),e("span",fe,"Assunto il "+l(b(o.value.hire_date)),1)])):n("",!0)])]),e("div",ke,[t[13]||(t[13]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Competenze Principali",-1)),o.value.skills&&o.value.skills.length>0?(r(),a("div",_e,[(r(!0),a(u,null,g(o.value.skills.slice(0,5),s=>(r(),a("div",{key:s.id,class:"flex items-center justify-between"},[e("span",we,l(s.name),1),e("div",be,[e("div",ze,[(r(),a(u,null,g(5,d=>e("div",{key:d,class:h(["w-2 h-2 rounded-full",d<=s.proficiency_level?"bg-blue-500":"bg-gray-300 dark:bg-gray-600"])},null,2)),64))]),s.certified?(r(),a("span",Ce,"✓")):n("",!0)])]))),128)),o.value.skills.length>5?(r(),a("div",Me," +"+l(o.value.skills.length-5)+" altre competenze ",1)):n("",!0)])):(r(),a("div",Ve," Nessuna competenza registrata "))]),o.value.bio?(r(),a("div",He,[t[14]||(t[14]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Bio",-1)),e("p",je,l(o.value.bio),1)])):n("",!0)]),e("div",Te,[e("div",Be,[e("div",Pe,[e("nav",Le,[(r(!0),a(u,null,g(B.value,s=>(r(),a("button",{key:s.id,onClick:d=>x.value=s.id,class:h([x.value===s.id?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300","whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center"])},[s.id==="projects"?(r(),a("svg",$e,t[15]||(t[15]=[e("path",{"fill-rule":"evenodd",d:"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z","clip-rule":"evenodd"},null,-1)]))):s.id==="tasks"?(r(),a("svg",Ee,t[16]||(t[16]=[e("path",{"fill-rule":"evenodd",d:"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z","clip-rule":"evenodd"},null,-1)]))):s.id==="skills"?(r(),a("svg",Ne,t[17]||(t[17]=[e("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"},null,-1)]))):s.id==="timesheet"?(r(),a("svg",Se,t[18]||(t[18]=[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z","clip-rule":"evenodd"},null,-1)]))):s.id==="cv"?(r(),a("svg",Ae,t[19]||(t[19]=[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z","clip-rule":"evenodd"},null,-1)]))):n("",!0),f(" "+l(s.name)+" ",1),s.count!==void 0?(r(),a("span",Ie,l(s.count),1)):n("",!0)],10,De))),128))])]),e("div",Oe,[x.value==="projects"?(r(),a("div",Ue,[y.value.length>0?(r(),a("div",qe,[(r(!0),a(u,null,g(y.value,s=>(r(),a("div",{key:s.id,class:"border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"},[e("div",Fe,[e("div",null,[e("h4",Re,l(s.name),1),e("p",Ge,l(s.role||"Team Member"),1)]),e("span",{class:h(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",s.status==="active"?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200":s.status==="completed"?"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200":"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"])},l(s.status),3)])]))),128))])):(r(),a("div",Je,t[20]||(t[20]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun progetto",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"L'utente non è assegnato a nessun progetto.",-1)])))])):n("",!0),x.value==="tasks"?(r(),a("div",Ke,[p.value.length>0?(r(),a("div",Qe,[(r(!0),a(u,null,g(p.value,s=>(r(),a("div",{key:s.id,class:"border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"},[e("div",We,[e("div",Xe,[e("h4",Ye,l(s.name),1),e("p",Ze,l(s.project_name),1),e("div",et,[e("span",{class:h(["inline-flex items-center px-2 py-1 rounded-full text-xs font-medium",s.priority==="urgent"?"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200":s.priority==="high"?"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200":s.priority==="medium"?"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200":"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"])},l(s.priority),3),s.due_date?(r(),a("span",tt," Scadenza: "+l(b(s.due_date)),1)):n("",!0)])]),e("span",{class:h(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ml-4",s.status==="done"?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200":s.status==="in-progress"?"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200":s.status==="review"?"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200":"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"])},l(s.status),3)])]))),128))])):(r(),a("div",st,t[21]||(t[21]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun task",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"L'utente non ha task assegnati.",-1)])))])):n("",!0),x.value==="skills"?(r(),a("div",at,[o.value.skills&&o.value.skills.length>0?(r(),a("div",rt,[e("div",ot,[(r(!0),a(u,null,g(o.value.skills,s=>(r(),a("div",{key:s.id,class:"border border-gray-200 dark:border-gray-700 rounded-lg p-4"},[e("div",lt,[e("h4",it,l(s.name),1),s.certified?(r(),a("span",nt,"✓ Certificato")):n("",!0)]),s.category?(r(),a("p",dt,l(s.category),1)):n("",!0),e("div",ct,[e("div",ut,[t[22]||(t[22]=e("span",{class:"text-sm text-gray-600 dark:text-gray-400"},"Livello:",-1)),e("div",gt,[(r(),a(u,null,g(5,d=>e("div",{key:d,class:h(["w-3 h-3 rounded-full",d<=s.proficiency_level?"bg-blue-500":"bg-gray-300 dark:bg-gray-600"])},null,2)),64))])]),s.years_experience?(r(),a("span",xt,l(s.years_experience)+" anni ",1)):n("",!0)])]))),128))])])):(r(),a("div",ht,t[23]||(t[23]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessuna competenza",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"Non sono state registrate competenze per questo utente.",-1)])))])):n("",!0),x.value==="timesheet"?(r(),a("div",yt,[v.value.length>0?(r(),a("div",mt,[e("div",pt,[e("table",vt,[t[24]||(t[24]=e("thead",{class:"bg-gray-50 dark:bg-gray-700"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"},"Data"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"},"Progetto"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"},"Task"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"},"Ore"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"},"Descrizione")])],-1)),e("tbody",ft,[(r(!0),a(u,null,g(v.value,s=>(r(),a("tr",{key:s.id},[e("td",kt,l(b(s.date)),1),e("td",_t,l(s.project_name),1),e("td",wt,l(s.task_name||"-"),1),e("td",bt,l(s.hours)+"h ",1),e("td",zt,l(s.description||"-"),1)]))),128))])])])])):(r(),a("div",Ct,t[25]||(t[25]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun timesheet",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"Non sono stati registrati timesheet per questo utente.",-1)])))])):n("",!0),x.value==="cv"?(r(),a("div",Mt,t[26]||(t[26]=[E('<div class="text-center py-8"><svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg><h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">CV non disponibile</h3><p class="mt-1 text-sm text-gray-500 dark:text-gray-400">La funzionalità CV sarà implementata in una versione futura.</p></div>',1)]))):n("",!0)])])])])])):n("",!0)]))}};export{Bt as default};

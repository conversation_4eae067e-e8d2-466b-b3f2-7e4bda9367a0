{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/personnel/components/admin/SkillModal.vue"}, "modifiedCode": "<template>\n  <!-- <PERSON><PERSON> Backdrop -->\n  <div class=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\" @click=\"$emit('close')\">\n    <!-- Modal Container -->\n    <div class=\"relative top-20 mx-auto p-5 border w-11/12 max-w-lg shadow-lg rounded-md bg-white dark:bg-gray-800\" @click.stop>\n      <!-- <PERSON><PERSON> Header -->\n      <div class=\"flex items-center justify-between pb-4 border-b border-gray-200 dark:border-gray-700\">\n        <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n          {{ skill ? 'Modifica Competenza' : 'Nuova Competenza' }}\n        </h3>\n        <button @click=\"$emit('close')\" class=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\">\n          <svg class=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\"></path>\n          </svg>\n        </button>\n      </div>\n\n      <!-- Modal Body -->\n      <div class=\"mt-6\">\n        <form @submit.prevent=\"saveSkill\" class=\"space-y-4\">\n          <!-- Skill Name -->\n          <div>\n            <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n              Nome Competenza *\n            </label>\n            <input v-model=\"form.name\"\n                   type=\"text\"\n                   required\n                   placeholder=\"es. JavaScript, Project Management, Design Thinking\"\n                   class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\">\n          </div>\n\n          <!-- Category -->\n          <div>\n            <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n              Categoria\n            </label>\n            <div class=\"flex space-x-2\">\n              <select v-model=\"form.category\"\n                      class=\"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\">\n                <option value=\"\">Seleziona categoria esistente</option>\n                <option v-for=\"category in categories\" :key=\"category\" :value=\"category\">\n                  {{ category }}\n                </option>\n              </select>\n              <button type=\"button\"\n                      @click=\"showNewCategory = !showNewCategory\"\n                      class=\"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700\">\n                {{ showNewCategory ? 'Annulla' : 'Nuova' }}\n              </button>\n            </div>\n            \n            <div v-if=\"showNewCategory\" class=\"mt-2\">\n              <input v-model=\"newCategory\"\n                     type=\"text\"\n                     placeholder=\"Nome nuova categoria\"\n                     @blur=\"addNewCategory\"\n                     @keyup.enter=\"addNewCategory\"\n                     class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\">\n            </div>\n          </div>\n\n          <!-- Description -->\n          <div>\n            <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n              Descrizione\n            </label>\n            <textarea v-model=\"form.description\"\n                      rows=\"3\"\n                      placeholder=\"Descrizione della competenza e come viene utilizzata...\"\n                      class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\"></textarea>\n          </div>\n\n          <!-- Error Message -->\n          <div v-if=\"error\" class=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4\">\n            <div class=\"flex\">\n              <svg class=\"w-5 h-5 text-red-400 mr-2 mt-0.5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clip-rule=\"evenodd\"></path>\n              </svg>\n              <div>\n                <h3 class=\"text-sm font-medium text-red-800 dark:text-red-200\">Errore nel salvataggio</h3>\n                <p class=\"text-sm text-red-700 dark:text-red-300 mt-1\">{{ error }}</p>\n              </div>\n            </div>\n          </div>\n\n          <!-- Modal Footer -->\n          <div class=\"flex items-center justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700\">\n            <button type=\"button\"\n                    @click=\"$emit('close')\"\n                    class=\"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\">\n              Annulla\n            </button>\n            <button type=\"submit\"\n                    :disabled=\"loading\"\n                    class=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50\">\n              <svg v-if=\"loading\" class=\"animate-spin -ml-1 mr-3 h-4 w-4 text-white inline\" fill=\"none\" viewBox=\"0 0 24 24\">\n                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\"></circle>\n                <path class=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n              </svg>\n              {{ loading ? 'Salvataggio...' : (skill ? 'Aggiorna' : 'Crea') }}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, onMounted } from 'vue'\n\n// Props\nconst props = defineProps({\n  skill: {\n    type: Object,\n    default: null\n  },\n  categories: {\n    type: Array,\n    default: () => []\n  }\n})\n\n// Emits\nconst emit = defineEmits(['close', 'saved'])\n\n// Reactive state\nconst loading = ref(false)\nconst error = ref(null)\nconst showNewCategory = ref(false)\nconst newCategory = ref('')\n\n// Form data\nconst form = ref({\n  name: '',\n  category: '',\n  description: ''\n})\n\n// Methods\nconst addNewCategory = () => {\n  if (newCategory.value.trim()) {\n    form.value.category = newCategory.value.trim()\n    newCategory.value = ''\n    showNewCategory.value = false\n  }\n}\n\nconst saveSkill = async () => {\n  loading.value = true\n  error.value = null\n\n  try {\n    // Prepare form data\n    const skillData = { ...form.value }\n    \n    // Convert empty strings to null for optional fields\n    Object.keys(skillData).forEach(key => {\n      if (skillData[key] === '') {\n        skillData[key] = null\n      }\n    })\n\n    const url = props.skill \n      ? `/api/personnel/skills/${props.skill.id}`\n      : '/api/personnel/skills'\n    \n    const method = props.skill ? 'PUT' : 'POST'\n\n    const response = await fetch(url, {\n      method,\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      credentials: 'include',\n      body: JSON.stringify(skillData)\n    })\n\n    if (!response.ok) {\n      throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n\n    if (data.success) {\n      emit('saved', data.data.skill)\n    } else {\n      throw new Error(data.message || 'Errore nel salvataggio competenza')\n    }\n  } catch (err) {\n    console.error('Error saving skill:', err)\n    error.value = err.message\n  } finally {\n    loading.value = false\n  }\n}\n\n// Lifecycle\nonMounted(() => {\n  if (props.skill) {\n    // Populate form with existing skill data\n    form.value = {\n      name: props.skill.name || '',\n      category: props.skill.category || '',\n      description: props.skill.description || ''\n    }\n  }\n})\n</script>\n\n<style scoped>\n/* Custom styles for modal */\n.modal-container {\n  max-height: 90vh;\n  overflow-y: auto;\n}\n\n/* Responsive adjustments */\n@media (max-width: 768px) {\n  .modal-container {\n    margin: 1rem;\n    width: calc(100% - 2rem);\n  }\n}\n</style>\n"}
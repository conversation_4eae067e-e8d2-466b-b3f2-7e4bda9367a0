var Do=Object.defineProperty;var Ai=n=>{throw TypeError(n)};var Ao=(n,t,e)=>t in n?Do(n,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):n[t]=e;var d=(n,t,e)=>Ao(n,typeof t!="symbol"?t+"":t,e),qn=(n,t,e)=>t.has(n)||Ai("Cannot "+e);var h=(n,t,e)=>(qn(n,t,"read from private field"),e?e.call(n):t.get(n)),Z=(n,t,e)=>t.has(n)?Ai("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(n):t.set(n,e),N=(n,t,e,s)=>(qn(n,t,"write to private field"),s?s.call(n,e):t.set(n,e),e),A=(n,t,e)=>(qn(n,t,"access private method"),e);var ln=(n,t,e,s)=>({set _(i){N(n,t,i,e)},get _(){return h(n,t,s)}});import{S as Gr,W as Et,e as Cs,u as jr,o as Wr}from"./BaseButton-rKFNr-KO.js";import{d as Ro,T as tn}from"./Content-CZt_q_72.js";import{ai as At,ak as yt,S as tt,i as et,s as st,b as ut,c as w,e as F,f as V,n as O,h as C,a as j,I as Se,J as Te,K as Ce,L as Ee,d as bt,M as Ie,g as Xt,j as ht,aj as yn,Q as J,q as xt,t as k,r as $t,u as _,y as I,z as M,B as D,am as Yr,D as pt,T as bs,G as Gt,H as en,aA as Hn,P as is,R as Ri,V as rs,W as os,X as as,aB as Es,aa as cn,w as ue,x as Ze,A as Qe,E as ie,F as Fo,a2 as le,a4 as Ne,a6 as No,a7 as mt,a0 as Po,_ as Oo,al as Xs,Z as Tn,Y as ki,aq as Lo,ag as Vr,C as Uo,ae as zo}from"./SpinnerAugment-BRymMBwV.js";import{h as ce,A as hs,E as Ft,T as Ve,p as Fi,q as Bn,r as _s,l as Hs,S as Ps,t as Os,u as Xr,v as qo,w as Gn,x as Ho,y as Ni,z as Pi,B as Bo,D as Oi,F as Go,G as Li,H as jo,I as Ui,J as jn,K as Wo}from"./open-in-new-window-_CQmfLgB.js";import{F as Yo}from"./folder-opened-qXv2xhk3.js";import{C as Vo,i as Xo,j as zi}from"./test_service_pb-B6vKXZrG.js";import{C as $e,P as ne,a as xe,I as As,E as Zo}from"./chat-types-NgqNgjwU.js";import{C as Qo,b as Ut,A as Ko,a as Jo}from"./types-BSMhNRWH.js";import{I as Ke}from"./IconButtonAugment-5yqT_m78.js";import{b as ta,C as ea,T as sa}from"./github-DCBOV_oD.js";import{D as Vt,C as na,T as ia}from"./index-C57dba63.js";import{T as ra}from"./TextAreaAugment-DJ6NCdxI.js";import{T as sn}from"./TextTooltipAugment-VmEmcMVL.js";import{P as oa,g as aa,d as la,e as qi,i as ca,f as ua}from"./diff-utils-UT8EbVpu.js";function da(n){var t;return((t=n.extraData)==null?void 0:t.isAutofix)===!0}function ys(n){var t;return((t=n.extraData)==null?void 0:t.isAgentConversation)===!0}var Ct=(n=>(n[n.active=0]="active",n[n.inactive=1]="inactive",n))(Ct||{}),ha=(n=>(n.normal="Normal",n.autofixCommand="AutofixCommand",n.autofixPrompt="AutofixPrompt",n))(ha||{});const Zr=25e4,Ju=2e4;class pa{constructor(t){d(this,"_enableEditableHistory",!1);d(this,"_enablePreferenceCollection",!1);d(this,"_enableRetrievalDataCollection",!1);d(this,"_enableDebugFeatures",!1);d(this,"_enableRichTextHistory",!1);d(this,"_modelDisplayNameToId",{});d(this,"_fullFeatured",!0);d(this,"_enableExternalSourcesInChat",!1);d(this,"_smallSyncThreshold",15);d(this,"_bigSyncThreshold",1e3);d(this,"_enableSmartPaste",!1);d(this,"_enableDirectApply",!1);d(this,"_summaryTitles",!1);d(this,"_suggestedEditsAvailable",!1);d(this,"_enableShareService",!1);d(this,"_maxTrackableFileCount",Zr);d(this,"_enableDesignSystemRichTextEditor",!1);d(this,"_enableSources",!1);d(this,"_enableChatMermaidDiagrams",!1);d(this,"_smartPastePrecomputeMode",Gr.visibleHover);d(this,"_useNewThreadsMenu",!1);d(this,"_enableChatMermaidDiagramsMinVersion",!1);d(this,"_enablePromptEnhancer",!1);d(this,"_idleNewSessionNotificationTimeoutMs");d(this,"_idleNewSessionMessageTimeoutMs");d(this,"_enableChatMultimodal",!1);d(this,"_enableAgentMode",!1);d(this,"_enableRichCheckpointInfo",!1);d(this,"_agentMemoriesFilePathName");d(this,"_userTier","unknown");d(this,"_eloModelConfiguration",{highPriorityModels:[],regularBattleModels:[],highPriorityThreshold:.5});d(this,"_truncateChatHistory",!1);d(this,"_enableBackgroundAgents",!1);d(this,"_enableVirtualizedMessageList",!1);d(this,"_customPersonalityPrompts",{});d(this,"_enablePersonalities",!1);d(this,"_enableRules",!1);d(this,"_memoryClassificationOnFirstToken",!1);d(this,"_isRemoteAgentWindow",!1);d(this,"_remoteAgentId");d(this,"_enableGenerateCommitMessage",!1);d(this,"_doUseNewDraftFunctionality",!1);d(this,"_modelRegistry",{});d(this,"_enableModelRegistry",!1);d(this,"_enableTaskList",!1);d(this,"_clientAnnouncement","");d(this,"_subscribers",new Set);d(this,"subscribe",t=>(this._subscribers.add(t),t(this),()=>{this._subscribers.delete(t)}));d(this,"update",t=>{this._enableEditableHistory=t.enableEditableHistory??this._enableEditableHistory,this._enablePreferenceCollection=t.enablePreferenceCollection??this._enablePreferenceCollection,this._enableRetrievalDataCollection=t.enableRetrievalDataCollection??this._enableRetrievalDataCollection,this._enableDebugFeatures=t.enableDebugFeatures??this._enableDebugFeatures,this._enableRichTextHistory=t.enableRichTextHistory??this._enableRichTextHistory,this._modelDisplayNameToId={...t.modelDisplayNameToId},this._fullFeatured=t.fullFeatured??this._fullFeatured,this._enableExternalSourcesInChat=t.enableExternalSourcesInChat??this._enableExternalSourcesInChat,this._smallSyncThreshold=t.smallSyncThreshold??this._smallSyncThreshold,this._bigSyncThreshold=t.bigSyncThreshold??this._bigSyncThreshold,this._enableSmartPaste=t.enableSmartPaste??this._enableSmartPaste,this._enableDirectApply=t.enableDirectApply??this._enableDirectApply,this._summaryTitles=t.summaryTitles??this._summaryTitles,this._suggestedEditsAvailable=t.suggestedEditsAvailable??this._suggestedEditsAvailable,this._enableShareService=t.enableShareService??this._enableShareService,this._maxTrackableFileCount=t.maxTrackableFileCount??this._maxTrackableFileCount,this._enableDesignSystemRichTextEditor=t.enableDesignSystemRichTextEditor??this._enableDesignSystemRichTextEditor,this._enableSources=t.enableSources??this._enableSources,this._enableChatMermaidDiagrams=t.enableChatMermaidDiagrams??this._enableChatMermaidDiagrams,this._smartPastePrecomputeMode=t.smartPastePrecomputeMode??this._smartPastePrecomputeMode,this._useNewThreadsMenu=t.useNewThreadsMenu??this._useNewThreadsMenu,this._enableChatMermaidDiagramsMinVersion=t.enableChatMermaidDiagramsMinVersion??this._enableChatMermaidDiagramsMinVersion,this._enablePromptEnhancer=t.enablePromptEnhancer??this._enablePromptEnhancer,this._idleNewSessionMessageTimeoutMs=t.idleNewSessionMessageTimeoutMs??(t.enableDebugFeatures?this._idleNewSessionMessageTimeoutMs??3e5:this._idleNewSessionMessageTimeoutMs),this._idleNewSessionNotificationTimeoutMs=t.idleNewSessionNotificationTimeoutMs??0,this._enableChatMultimodal=t.enableChatMultimodal??this._enableChatMultimodal,this._enableAgentMode=t.enableAgentMode??this._enableAgentMode,this._enableRichCheckpointInfo=t.enableRichCheckpointInfo??this._enableRichCheckpointInfo,this._agentMemoriesFilePathName=t.agentMemoriesFilePathName??this._agentMemoriesFilePathName,this._userTier=t.userTier??this._userTier,this._eloModelConfiguration=t.eloModelConfiguration??this._eloModelConfiguration,this._truncateChatHistory=t.truncateChatHistory??this._truncateChatHistory,this._enableBackgroundAgents=t.enableBackgroundAgents??this._enableBackgroundAgents,this._enableVirtualizedMessageList=t.enableVirtualizedMessageList??this._enableVirtualizedMessageList,this._customPersonalityPrompts=t.customPersonalityPrompts??this._customPersonalityPrompts,this._enablePersonalities=t.enablePersonalities??this._enablePersonalities,this._enableRules=t.enableRules??this._enableRules,this._memoryClassificationOnFirstToken=t.memoryClassificationOnFirstToken??this._memoryClassificationOnFirstToken,this._isRemoteAgentWindow=t.isRemoteAgentWindow??this._isRemoteAgentWindow,this._remoteAgentId=t.remoteAgentId??this._remoteAgentId,this._doUseNewDraftFunctionality=t.doUseNewDraftFunctionality??this._doUseNewDraftFunctionality,this._enableGenerateCommitMessage=t.enableGenerateCommitMessage??this._enableGenerateCommitMessage,this._modelRegistry=t.modelRegistry??this._modelRegistry,this._enableModelRegistry=t.enableModelRegistry??this._enableModelRegistry,this._enableTaskList=t.enableTaskList??this._enableTaskList,this._clientAnnouncement=t.clientAnnouncement??this._clientAnnouncement,this._subscribers.forEach(e=>e(this))});d(this,"isModelIdValid",t=>t!==void 0&&(Object.values(this._modelDisplayNameToId).includes(t)||Object.values(this._modelRegistry).includes(t??"")));d(this,"getModelDisplayName",t=>{if(t!==void 0)return Object.keys(this._modelDisplayNameToId).find(e=>this._modelDisplayNameToId[e]===t)});t&&this.update(t)}get enableEditableHistory(){return this._fullFeatured&&(this._enableEditableHistory||this._enableDebugFeatures)}get enablePreferenceCollection(){return this._enablePreferenceCollection}get enableRetrievalDataCollection(){return this._enableRetrievalDataCollection}get enableDebugFeatures(){return this._enableDebugFeatures}get enableGenerateCommitMessage(){return this._enableGenerateCommitMessage}get enableRichTextHistory(){return this._enableRichTextHistory||this._enableDebugFeatures}get modelDisplayNameToId(){return this._modelDisplayNameToId}get orderedModelDisplayNames(){return Object.keys(this._modelDisplayNameToId).sort((t,e)=>{const s=t.toLowerCase(),i=e.toLowerCase();return s==="default"&&i!=="default"?-1:i==="default"&&s!=="default"?1:t.localeCompare(e)})}get fullFeatured(){return this._fullFeatured}get enableExternalSourcesInChat(){return this._enableExternalSourcesInChat}get smallSyncThreshold(){return this._smallSyncThreshold}get bigSyncThreshold(){return this._bigSyncThreshold}get enableSmartPaste(){return this._enableDebugFeatures||this._enableSmartPaste}get enableDirectApply(){return this._enableDirectApply||this._enableDebugFeatures}get enableShareService(){return this._enableShareService}get summaryTitles(){return this._summaryTitles}get suggestedEditsAvailable(){return this._suggestedEditsAvailable}get maxTrackableFileCount(){return this._maxTrackableFileCount}get enableSources(){return this._enableDebugFeatures||this._enableSources}get enableChatMermaidDiagrams(){return this._enableDebugFeatures||this._enableChatMermaidDiagrams}get smartPastePrecomputeMode(){return this._smartPastePrecomputeMode}get useNewThreadsMenu(){return this._useNewThreadsMenu}get enableChatMermaidDiagramsMinVersion(){return this._enableChatMermaidDiagramsMinVersion}get enablePromptEnhancer(){return this._enablePromptEnhancer}get enableDesignSystemRichTextEditor(){return this._enableDesignSystemRichTextEditor}get idleNewSessionNotificationTimeoutMs(){return this._idleNewSessionNotificationTimeoutMs??0}get idleNewSessionMessageTimeoutMs(){return this._idleNewSessionMessageTimeoutMs??0}get enableChatMultimodal(){return this._enableChatMultimodal}get enableAgentMode(){return this._enableAgentMode}get enableRichCheckpointInfo(){return this._enableRichCheckpointInfo}get agentMemoriesFilePathName(){return this._agentMemoriesFilePathName}get userTier(){return this._userTier}get eloModelConfiguration(){return this._eloModelConfiguration}get truncateChatHistory(){return this._truncateChatHistory}get enableBackgroundAgents(){return this._enableBackgroundAgents}get doUseNewDraftFunctionality(){return this._doUseNewDraftFunctionality}get enableVirtualizedMessageList(){return this._enableVirtualizedMessageList||this._enableDebugFeatures}get customPersonalityPrompts(){return this._customPersonalityPrompts}get enablePersonalities(){return this._enablePersonalities||this._enableDebugFeatures}get enableRules(){return this._enableRules}get memoryClassificationOnFirstToken(){return this._memoryClassificationOnFirstToken}get isRemoteAgentWindow(){return this._isRemoteAgentWindow}get remoteAgentId(){return this._remoteAgentId}get modelRegistry(){return this._modelRegistry}get enableModelRegistry(){return this._enableModelRegistry}get enableTaskList(){return this._enableTaskList}get clientAnnouncement(){return this._clientAnnouncement}}function fa(n,t,e=1e3){let s=null,i=0;const r=At(t),o=()=>{const a=(()=>{const l=Date.now();if(s!==null&&l-i<e)return s;const c=n();return s=c,i=l,c})();r.set(a)};return{subscribe:r.subscribe,resetCache:()=>{s=null,o()},updateStore:o}}var Qr=(n=>(n[n.unset=0]="unset",n[n.positive=1]="positive",n[n.negative=2]="negative",n))(Qr||{});function ga(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}let ds={async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null};function Hi(n){ds=n}const Kr=/[&<>"']/,ma=new RegExp(Kr.source,"g"),Jr=/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,ba=new RegExp(Jr.source,"g"),va={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},Bi=n=>va[n];function se(n,t){if(t){if(Kr.test(n))return n.replace(ma,Bi)}else if(Jr.test(n))return n.replace(ba,Bi);return n}const _a=/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/gi;function ya(n){return n.replace(_a,(t,e)=>(e=e.toLowerCase())==="colon"?":":e.charAt(0)==="#"?e.charAt(1)==="x"?String.fromCharCode(parseInt(e.substring(2),16)):String.fromCharCode(+e.substring(1)):"")}const wa=/(^|[^\[])\^/g;function it(n,t){let e=typeof n=="string"?n:n.source;t=t||"";const s={replace:(i,r)=>{let o=typeof r=="string"?r:r.source;return o=o.replace(wa,"$1"),e=e.replace(i,o),s},getRegex:()=>new RegExp(e,t)};return s}function Gi(n){try{n=encodeURI(n).replace(/%25/g,"%")}catch{return null}return n}const Bs={exec:()=>null};function ji(n,t){const e=n.replace(/\|/g,(i,r,o)=>{let a=!1,l=r;for(;--l>=0&&o[l]==="\\";)a=!a;return a?"|":" |"}).split(/ \|/);let s=0;if(e[0].trim()||e.shift(),e.length>0&&!e[e.length-1].trim()&&e.pop(),t)if(e.length>t)e.splice(t);else for(;e.length<t;)e.push("");for(;s<e.length;s++)e[s]=e[s].trim().replace(/\\\|/g,"|");return e}function un(n,t,e){const s=n.length;if(s===0)return"";let i=0;for(;i<s;){const r=n.charAt(s-i-1);if(r!==t||e){if(r===t||!e)break;i++}else i++}return n.slice(0,s-i)}function Wi(n,t,e,s){const i=t.href,r=t.title?se(t.title):null,o=n[1].replace(/\\([\[\]])/g,"$1");if(n[0].charAt(0)!=="!"){s.state.inLink=!0;const a={type:"link",raw:e,href:i,title:r,text:o,tokens:s.inlineTokens(o)};return s.state.inLink=!1,a}return{type:"image",raw:e,href:i,title:r,text:se(o)}}class Cn{constructor(t){d(this,"options");d(this,"rules");d(this,"lexer");this.options=t||ds}space(t){const e=this.rules.block.newline.exec(t);if(e&&e[0].length>0)return{type:"space",raw:e[0]}}code(t){const e=this.rules.block.code.exec(t);if(e){const s=e[0].replace(/^ {1,4}/gm,"");return{type:"code",raw:e[0],codeBlockStyle:"indented",text:this.options.pedantic?s:un(s,`
`)}}}fences(t){const e=this.rules.block.fences.exec(t);if(e){const s=e[0],i=function(r,o){const a=r.match(/^(\s+)(?:```)/);if(a===null)return o;const l=a[1];return o.split(`
`).map(c=>{const u=c.match(/^\s+/);if(u===null)return c;const[p]=u;return p.length>=l.length?c.slice(l.length):c}).join(`
`)}(s,e[3]||"");return{type:"code",raw:s,lang:e[2]?e[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):e[2],text:i}}}heading(t){const e=this.rules.block.heading.exec(t);if(e){let s=e[2].trim();if(/#$/.test(s)){const i=un(s,"#");this.options.pedantic?s=i.trim():i&&!/ $/.test(i)||(s=i.trim())}return{type:"heading",raw:e[0],depth:e[1].length,text:s,tokens:this.lexer.inline(s)}}}hr(t){const e=this.rules.block.hr.exec(t);if(e)return{type:"hr",raw:e[0]}}blockquote(t){const e=this.rules.block.blockquote.exec(t);if(e){const s=un(e[0].replace(/^ *>[ \t]?/gm,""),`
`),i=this.lexer.state.top;this.lexer.state.top=!0;const r=this.lexer.blockTokens(s);return this.lexer.state.top=i,{type:"blockquote",raw:e[0],tokens:r,text:s}}}list(t){let e=this.rules.block.list.exec(t);if(e){let s=e[1].trim();const i=s.length>1,r={type:"list",raw:"",ordered:i,start:i?+s.slice(0,-1):"",loose:!1,items:[]};s=i?`\\d{1,9}\\${s.slice(-1)}`:`\\${s}`,this.options.pedantic&&(s=i?s:"[*+-]");const o=new RegExp(`^( {0,3}${s})((?:[	 ][^\\n]*)?(?:\\n|$))`);let a="",l="",c=!1;for(;t;){let u=!1;if(!(e=o.exec(t))||this.rules.block.hr.test(t))break;a=e[0],t=t.substring(a.length);let p=e[2].split(`
`,1)[0].replace(/^\t+/,y=>" ".repeat(3*y.length)),f=t.split(`
`,1)[0],g=0;this.options.pedantic?(g=2,l=p.trimStart()):(g=e[2].search(/[^ ]/),g=g>4?1:g,l=p.slice(g),g+=e[1].length);let m=!1;if(!p&&/^ *$/.test(f)&&(a+=f+`
`,t=t.substring(f.length+1),u=!0),!u){const y=new RegExp(`^ {0,${Math.min(3,g-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),b=new RegExp(`^ {0,${Math.min(3,g-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),$=new RegExp(`^ {0,${Math.min(3,g-1)}}(?:\`\`\`|~~~)`),P=new RegExp(`^ {0,${Math.min(3,g-1)}}#`);for(;t;){const S=t.split(`
`,1)[0];if(f=S,this.options.pedantic&&(f=f.replace(/^ {1,4}(?=( {4})*[^ ])/g,"  ")),$.test(f)||P.test(f)||y.test(f)||b.test(t))break;if(f.search(/[^ ]/)>=g||!f.trim())l+=`
`+f.slice(g);else{if(m||p.search(/[^ ]/)>=4||$.test(p)||P.test(p)||b.test(p))break;l+=`
`+f}m||f.trim()||(m=!0),a+=S+`
`,t=t.substring(S.length+1),p=f.slice(g)}}r.loose||(c?r.loose=!0:/\n *\n *$/.test(a)&&(c=!0));let v,x=null;this.options.gfm&&(x=/^\[[ xX]\] /.exec(l),x&&(v=x[0]!=="[ ] ",l=l.replace(/^\[[ xX]\] +/,""))),r.items.push({type:"list_item",raw:a,task:!!x,checked:v,loose:!1,text:l,tokens:[]}),r.raw+=a}r.items[r.items.length-1].raw=a.trimEnd(),r.items[r.items.length-1].text=l.trimEnd(),r.raw=r.raw.trimEnd();for(let u=0;u<r.items.length;u++)if(this.lexer.state.top=!1,r.items[u].tokens=this.lexer.blockTokens(r.items[u].text,[]),!r.loose){const p=r.items[u].tokens.filter(g=>g.type==="space"),f=p.length>0&&p.some(g=>/\n.*\n/.test(g.raw));r.loose=f}if(r.loose)for(let u=0;u<r.items.length;u++)r.items[u].loose=!0;return r}}html(t){const e=this.rules.block.html.exec(t);if(e)return{type:"html",block:!0,raw:e[0],pre:e[1]==="pre"||e[1]==="script"||e[1]==="style",text:e[0]}}def(t){const e=this.rules.block.def.exec(t);if(e){const s=e[1].toLowerCase().replace(/\s+/g," "),i=e[2]?e[2].replace(/^<(.*)>$/,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",r=e[3]?e[3].substring(1,e[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):e[3];return{type:"def",tag:s,raw:e[0],href:i,title:r}}}table(t){const e=this.rules.block.table.exec(t);if(!e||!/[:|]/.test(e[2]))return;const s=ji(e[1]),i=e[2].replace(/^\||\| *$/g,"").split("|"),r=e[3]&&e[3].trim()?e[3].replace(/\n[ \t]*$/,"").split(`
`):[],o={type:"table",raw:e[0],header:[],align:[],rows:[]};if(s.length===i.length){for(const a of i)/^ *-+: *$/.test(a)?o.align.push("right"):/^ *:-+: *$/.test(a)?o.align.push("center"):/^ *:-+ *$/.test(a)?o.align.push("left"):o.align.push(null);for(const a of s)o.header.push({text:a,tokens:this.lexer.inline(a)});for(const a of r)o.rows.push(ji(a,o.header.length).map(l=>({text:l,tokens:this.lexer.inline(l)})));return o}}lheading(t){const e=this.rules.block.lheading.exec(t);if(e)return{type:"heading",raw:e[0],depth:e[2].charAt(0)==="="?1:2,text:e[1],tokens:this.lexer.inline(e[1])}}paragraph(t){const e=this.rules.block.paragraph.exec(t);if(e){const s=e[1].charAt(e[1].length-1)===`
`?e[1].slice(0,-1):e[1];return{type:"paragraph",raw:e[0],text:s,tokens:this.lexer.inline(s)}}}text(t){const e=this.rules.block.text.exec(t);if(e)return{type:"text",raw:e[0],text:e[0],tokens:this.lexer.inline(e[0])}}escape(t){const e=this.rules.inline.escape.exec(t);if(e)return{type:"escape",raw:e[0],text:se(e[1])}}tag(t){const e=this.rules.inline.tag.exec(t);if(e)return!this.lexer.state.inLink&&/^<a /i.test(e[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&/^<\/a>/i.test(e[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&/^<(pre|code|kbd|script)(\s|>)/i.test(e[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(e[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:e[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:e[0]}}link(t){const e=this.rules.inline.link.exec(t);if(e){const s=e[2].trim();if(!this.options.pedantic&&/^</.test(s)){if(!/>$/.test(s))return;const o=un(s.slice(0,-1),"\\");if((s.length-o.length)%2==0)return}else{const o=function(a,l){if(a.indexOf(l[1])===-1)return-1;let c=0;for(let u=0;u<a.length;u++)if(a[u]==="\\")u++;else if(a[u]===l[0])c++;else if(a[u]===l[1]&&(c--,c<0))return u;return-1}(e[2],"()");if(o>-1){const a=(e[0].indexOf("!")===0?5:4)+e[1].length+o;e[2]=e[2].substring(0,o),e[0]=e[0].substring(0,a).trim(),e[3]=""}}let i=e[2],r="";if(this.options.pedantic){const o=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(i);o&&(i=o[1],r=o[3])}else r=e[3]?e[3].slice(1,-1):"";return i=i.trim(),/^</.test(i)&&(i=this.options.pedantic&&!/>$/.test(s)?i.slice(1):i.slice(1,-1)),Wi(e,{href:i&&i.replace(this.rules.inline.anyPunctuation,"$1"),title:r&&r.replace(this.rules.inline.anyPunctuation,"$1")},e[0],this.lexer)}}reflink(t,e){let s;if((s=this.rules.inline.reflink.exec(t))||(s=this.rules.inline.nolink.exec(t))){const i=e[(s[2]||s[1]).replace(/\s+/g," ").toLowerCase()];if(!i){const r=s[0].charAt(0);return{type:"text",raw:r,text:r}}return Wi(s,i,s[0],this.lexer)}}emStrong(t,e,s=""){let i=this.rules.inline.emStrongLDelim.exec(t);if(i&&!(i[3]&&s.match(/[\p{L}\p{N}]/u))&&(!(i[1]||i[2])||!s||this.rules.inline.punctuation.exec(s))){const r=[...i[0]].length-1;let o,a,l=r,c=0;const u=i[0][0]==="*"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(u.lastIndex=0,e=e.slice(-1*t.length+r);(i=u.exec(e))!=null;){if(o=i[1]||i[2]||i[3]||i[4]||i[5]||i[6],!o)continue;if(a=[...o].length,i[3]||i[4]){l+=a;continue}if((i[5]||i[6])&&r%3&&!((r+a)%3)){c+=a;continue}if(l-=a,l>0)continue;a=Math.min(a,a+l+c);const p=[...i[0]][0].length,f=t.slice(0,r+i.index+p+a);if(Math.min(r,a)%2){const m=f.slice(1,-1);return{type:"em",raw:f,text:m,tokens:this.lexer.inlineTokens(m)}}const g=f.slice(2,-2);return{type:"strong",raw:f,text:g,tokens:this.lexer.inlineTokens(g)}}}}codespan(t){const e=this.rules.inline.code.exec(t);if(e){let s=e[2].replace(/\n/g," ");const i=/[^ ]/.test(s),r=/^ /.test(s)&&/ $/.test(s);return i&&r&&(s=s.substring(1,s.length-1)),s=se(s,!0),{type:"codespan",raw:e[0],text:s}}}br(t){const e=this.rules.inline.br.exec(t);if(e)return{type:"br",raw:e[0]}}del(t){const e=this.rules.inline.del.exec(t);if(e)return{type:"del",raw:e[0],text:e[2],tokens:this.lexer.inlineTokens(e[2])}}autolink(t){const e=this.rules.inline.autolink.exec(t);if(e){let s,i;return e[2]==="@"?(s=se(e[1]),i="mailto:"+s):(s=se(e[1]),i=s),{type:"link",raw:e[0],text:s,href:i,tokens:[{type:"text",raw:s,text:s}]}}}url(t){var s;let e;if(e=this.rules.inline.url.exec(t)){let i,r;if(e[2]==="@")i=se(e[0]),r="mailto:"+i;else{let o;do o=e[0],e[0]=((s=this.rules.inline._backpedal.exec(e[0]))==null?void 0:s[0])??"";while(o!==e[0]);i=se(e[0]),r=e[1]==="www."?"http://"+e[0]:e[0]}return{type:"link",raw:e[0],text:i,href:r,tokens:[{type:"text",raw:i,text:i}]}}}inlineText(t){const e=this.rules.inline.text.exec(t);if(e){let s;return s=this.lexer.state.inRawBlock?e[0]:se(e[0]),{type:"text",raw:e[0],text:s}}}}const nn=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,to=/(?:[*+-]|\d{1,9}[.)])/,eo=it(/^(?!bull |blockCode|fences|blockquote|heading|html)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html))+?)\n {0,3}(=+|-+) *(?:\n+|$)/).replace(/bull/g,to).replace(/blockCode/g,/ {4}/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).getRegex(),xi=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,$i=/(?!\s*\])(?:\\.|[^\[\]\\])+/,ka=it(/^ {0,3}\[(label)\]: *(?:\n *)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n *)?| *\n *)(title))? *(?:\n+|$)/).replace("label",$i).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),xa=it(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,to).getRegex(),Pn="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",Si=/<!--(?:-?>|[\s\S]*?(?:-->|$))/,$a=it("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n *)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$))","i").replace("comment",Si).replace("tag",Pn).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),Yi=it(xi).replace("hr",nn).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Pn).getRegex(),Ti={blockquote:it(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",Yi).getRegex(),code:/^( {4}[^\n]+(?:\n(?: *(?:\n|$))*)?)+/,def:ka,fences:/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,heading:/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,hr:nn,html:$a,lheading:eo,list:xa,newline:/^(?: *(?:\n|$))+/,paragraph:Yi,table:Bs,text:/^[^\n]+/},Vi=it("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",nn).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code"," {4}[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Pn).getRegex(),Sa={...Ti,table:Vi,paragraph:it(xi).replace("hr",nn).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",Vi).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Pn).getRegex()},Ta={...Ti,html:it(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",Si).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:Bs,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:it(xi).replace("hr",nn).replace("heading",` *#{1,6} *[^
]`).replace("lheading",eo).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},so=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,no=/^( {2,}|\\)\n(?!\s*$)/,rn="\\p{P}\\p{S}",Ca=it(/^((?![*_])[\spunctuation])/,"u").replace(/punctuation/g,rn).getRegex(),Ea=it(/^(?:\*+(?:((?!\*)[punct])|[^\s*]))|^_+(?:((?!_)[punct])|([^\s_]))/,"u").replace(/punct/g,rn).getRegex(),Ia=it("^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)[punct](\\*+)(?=[\\s]|$)|[^punct\\s](\\*+)(?!\\*)(?=[punct\\s]|$)|(?!\\*)[punct\\s](\\*+)(?=[^punct\\s])|[\\s](\\*+)(?!\\*)(?=[punct])|(?!\\*)[punct](\\*+)(?!\\*)(?=[punct])|[^punct\\s](\\*+)(?=[^punct\\s])","gu").replace(/punct/g,rn).getRegex(),Ma=it("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)[punct](_+)(?=[\\s]|$)|[^punct\\s](_+)(?!_)(?=[punct\\s]|$)|(?!_)[punct\\s](_+)(?=[^punct\\s])|[\\s](_+)(?!_)(?=[punct])|(?!_)[punct](_+)(?!_)(?=[punct])","gu").replace(/punct/g,rn).getRegex(),Da=it(/\\([punct])/,"gu").replace(/punct/g,rn).getRegex(),Aa=it(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),Ra=it(Si).replace("(?:-->|$)","-->").getRegex(),Fa=it("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",Ra).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),En=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,Na=it(/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/).replace("label",En).replace("href",/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),Xi=it(/^!?\[(label)\]\[(ref)\]/).replace("label",En).replace("ref",$i).getRegex(),Zi=it(/^!?\[(ref)\](?:\[\])?/).replace("ref",$i).getRegex(),Ci={_backpedal:Bs,anyPunctuation:Da,autolink:Aa,blockSkip:/\[[^[\]]*?\]\([^\(\)]*?\)|`[^`]*?`|<[^<>]*?>/g,br:no,code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,del:Bs,emStrongLDelim:Ea,emStrongRDelimAst:Ia,emStrongRDelimUnd:Ma,escape:so,link:Na,nolink:Zi,punctuation:Ca,reflink:Xi,reflinkSearch:it("reflink|nolink(?!\\()","g").replace("reflink",Xi).replace("nolink",Zi).getRegex(),tag:Fa,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,url:Bs},Pa={...Ci,link:it(/^!?\[(label)\]\((.*?)\)/).replace("label",En).getRegex(),reflink:it(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",En).getRegex()},ii={...Ci,escape:it(so).replace("])","~|])").getRegex(),url:it(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])([\s\S]*?[^\s~])\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},Oa={...ii,br:it(no).replace("{2,}","*").getRegex(),text:it(ii.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},dn={normal:Ti,gfm:Sa,pedantic:Ta},Rs={normal:Ci,gfm:ii,breaks:Oa,pedantic:Pa};class De{constructor(t){d(this,"tokens");d(this,"options");d(this,"state");d(this,"tokenizer");d(this,"inlineQueue");this.tokens=[],this.tokens.links=Object.create(null),this.options=t||ds,this.options.tokenizer=this.options.tokenizer||new Cn,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const e={block:dn.normal,inline:Rs.normal};this.options.pedantic?(e.block=dn.pedantic,e.inline=Rs.pedantic):this.options.gfm&&(e.block=dn.gfm,this.options.breaks?e.inline=Rs.breaks:e.inline=Rs.gfm),this.tokenizer.rules=e}static get rules(){return{block:dn,inline:Rs}}static lex(t,e){return new De(e).lex(t)}static lexInline(t,e){return new De(e).inlineTokens(t)}lex(t){t=t.replace(/\r\n|\r/g,`
`),this.blockTokens(t,this.tokens);for(let e=0;e<this.inlineQueue.length;e++){const s=this.inlineQueue[e];this.inlineTokens(s.src,s.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(t,e=[]){let s,i,r,o;for(t=this.options.pedantic?t.replace(/\t/g,"    ").replace(/^ +$/gm,""):t.replace(/^( *)(\t+)/gm,(a,l,c)=>l+"    ".repeat(c.length));t;)if(!(this.options.extensions&&this.options.extensions.block&&this.options.extensions.block.some(a=>!!(s=a.call({lexer:this},t,e))&&(t=t.substring(s.raw.length),e.push(s),!0))))if(s=this.tokenizer.space(t))t=t.substring(s.raw.length),s.raw.length===1&&e.length>0?e[e.length-1].raw+=`
`:e.push(s);else if(s=this.tokenizer.code(t))t=t.substring(s.raw.length),i=e[e.length-1],!i||i.type!=="paragraph"&&i.type!=="text"?e.push(s):(i.raw+=`
`+s.raw,i.text+=`
`+s.text,this.inlineQueue[this.inlineQueue.length-1].src=i.text);else if(s=this.tokenizer.fences(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.heading(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.hr(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.blockquote(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.list(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.html(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.def(t))t=t.substring(s.raw.length),i=e[e.length-1],!i||i.type!=="paragraph"&&i.type!=="text"?this.tokens.links[s.tag]||(this.tokens.links[s.tag]={href:s.href,title:s.title}):(i.raw+=`
`+s.raw,i.text+=`
`+s.raw,this.inlineQueue[this.inlineQueue.length-1].src=i.text);else if(s=this.tokenizer.table(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.lheading(t))t=t.substring(s.raw.length),e.push(s);else{if(r=t,this.options.extensions&&this.options.extensions.startBlock){let a=1/0;const l=t.slice(1);let c;this.options.extensions.startBlock.forEach(u=>{c=u.call({lexer:this},l),typeof c=="number"&&c>=0&&(a=Math.min(a,c))}),a<1/0&&a>=0&&(r=t.substring(0,a+1))}if(this.state.top&&(s=this.tokenizer.paragraph(r)))i=e[e.length-1],o&&i.type==="paragraph"?(i.raw+=`
`+s.raw,i.text+=`
`+s.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=i.text):e.push(s),o=r.length!==t.length,t=t.substring(s.raw.length);else if(s=this.tokenizer.text(t))t=t.substring(s.raw.length),i=e[e.length-1],i&&i.type==="text"?(i.raw+=`
`+s.raw,i.text+=`
`+s.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=i.text):e.push(s);else if(t){const a="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(a);break}throw new Error(a)}}return this.state.top=!0,e}inline(t,e=[]){return this.inlineQueue.push({src:t,tokens:e}),e}inlineTokens(t,e=[]){let s,i,r,o,a,l,c=t;if(this.tokens.links){const u=Object.keys(this.tokens.links);if(u.length>0)for(;(o=this.tokenizer.rules.inline.reflinkSearch.exec(c))!=null;)u.includes(o[0].slice(o[0].lastIndexOf("[")+1,-1))&&(c=c.slice(0,o.index)+"["+"a".repeat(o[0].length-2)+"]"+c.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(o=this.tokenizer.rules.inline.blockSkip.exec(c))!=null;)c=c.slice(0,o.index)+"["+"a".repeat(o[0].length-2)+"]"+c.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;(o=this.tokenizer.rules.inline.anyPunctuation.exec(c))!=null;)c=c.slice(0,o.index)+"++"+c.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;t;)if(a||(l=""),a=!1,!(this.options.extensions&&this.options.extensions.inline&&this.options.extensions.inline.some(u=>!!(s=u.call({lexer:this},t,e))&&(t=t.substring(s.raw.length),e.push(s),!0))))if(s=this.tokenizer.escape(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.tag(t))t=t.substring(s.raw.length),i=e[e.length-1],i&&s.type==="text"&&i.type==="text"?(i.raw+=s.raw,i.text+=s.text):e.push(s);else if(s=this.tokenizer.link(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.reflink(t,this.tokens.links))t=t.substring(s.raw.length),i=e[e.length-1],i&&s.type==="text"&&i.type==="text"?(i.raw+=s.raw,i.text+=s.text):e.push(s);else if(s=this.tokenizer.emStrong(t,c,l))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.codespan(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.br(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.del(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.autolink(t))t=t.substring(s.raw.length),e.push(s);else if(this.state.inLink||!(s=this.tokenizer.url(t))){if(r=t,this.options.extensions&&this.options.extensions.startInline){let u=1/0;const p=t.slice(1);let f;this.options.extensions.startInline.forEach(g=>{f=g.call({lexer:this},p),typeof f=="number"&&f>=0&&(u=Math.min(u,f))}),u<1/0&&u>=0&&(r=t.substring(0,u+1))}if(s=this.tokenizer.inlineText(r))t=t.substring(s.raw.length),s.raw.slice(-1)!=="_"&&(l=s.raw.slice(-1)),a=!0,i=e[e.length-1],i&&i.type==="text"?(i.raw+=s.raw,i.text+=s.text):e.push(s);else if(t){const u="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(u);break}throw new Error(u)}}else t=t.substring(s.raw.length),e.push(s);return e}}class In{constructor(t){d(this,"options");this.options=t||ds}code(t,e,s){var r;const i=(r=(e||"").match(/^\S*/))==null?void 0:r[0];return t=t.replace(/\n$/,"")+`
`,i?'<pre><code class="language-'+se(i)+'">'+(s?t:se(t,!0))+`</code></pre>
`:"<pre><code>"+(s?t:se(t,!0))+`</code></pre>
`}blockquote(t){return`<blockquote>
${t}</blockquote>
`}html(t,e){return t}heading(t,e,s){return`<h${e}>${t}</h${e}>
`}hr(){return`<hr>
`}list(t,e,s){const i=e?"ol":"ul";return"<"+i+(e&&s!==1?' start="'+s+'"':"")+`>
`+t+"</"+i+`>
`}listitem(t,e,s){return`<li>${t}</li>
`}checkbox(t){return"<input "+(t?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph(t){return`<p>${t}</p>
`}table(t,e){return e&&(e=`<tbody>${e}</tbody>`),`<table>
<thead>
`+t+`</thead>
`+e+`</table>
`}tablerow(t){return`<tr>
${t}</tr>
`}tablecell(t,e){const s=e.header?"th":"td";return(e.align?`<${s} align="${e.align}">`:`<${s}>`)+t+`</${s}>
`}strong(t){return`<strong>${t}</strong>`}em(t){return`<em>${t}</em>`}codespan(t){return`<code>${t}</code>`}br(){return"<br>"}del(t){return`<del>${t}</del>`}link(t,e,s){const i=Gi(t);if(i===null)return s;let r='<a href="'+(t=i)+'"';return e&&(r+=' title="'+e+'"'),r+=">"+s+"</a>",r}image(t,e,s){const i=Gi(t);if(i===null)return s;let r=`<img src="${t=i}" alt="${s}"`;return e&&(r+=` title="${e}"`),r+=">",r}text(t){return t}}class Ei{strong(t){return t}em(t){return t}codespan(t){return t}del(t){return t}html(t){return t}text(t){return t}link(t,e,s){return""+s}image(t,e,s){return""+s}br(){return""}}class Ae{constructor(t){d(this,"options");d(this,"renderer");d(this,"textRenderer");this.options=t||ds,this.options.renderer=this.options.renderer||new In,this.renderer=this.options.renderer,this.renderer.options=this.options,this.textRenderer=new Ei}static parse(t,e){return new Ae(e).parse(t)}static parseInline(t,e){return new Ae(e).parseInline(t)}parse(t,e=!0){let s="";for(let i=0;i<t.length;i++){const r=t[i];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[r.type]){const o=r,a=this.options.extensions.renderers[o.type].call({parser:this},o);if(a!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(o.type)){s+=a||"";continue}}switch(r.type){case"space":continue;case"hr":s+=this.renderer.hr();continue;case"heading":{const o=r;s+=this.renderer.heading(this.parseInline(o.tokens),o.depth,ya(this.parseInline(o.tokens,this.textRenderer)));continue}case"code":{const o=r;s+=this.renderer.code(o.text,o.lang,!!o.escaped);continue}case"table":{const o=r;let a="",l="";for(let u=0;u<o.header.length;u++)l+=this.renderer.tablecell(this.parseInline(o.header[u].tokens),{header:!0,align:o.align[u]});a+=this.renderer.tablerow(l);let c="";for(let u=0;u<o.rows.length;u++){const p=o.rows[u];l="";for(let f=0;f<p.length;f++)l+=this.renderer.tablecell(this.parseInline(p[f].tokens),{header:!1,align:o.align[f]});c+=this.renderer.tablerow(l)}s+=this.renderer.table(a,c);continue}case"blockquote":{const o=r,a=this.parse(o.tokens);s+=this.renderer.blockquote(a);continue}case"list":{const o=r,a=o.ordered,l=o.start,c=o.loose;let u="";for(let p=0;p<o.items.length;p++){const f=o.items[p],g=f.checked,m=f.task;let v="";if(f.task){const x=this.renderer.checkbox(!!g);c?f.tokens.length>0&&f.tokens[0].type==="paragraph"?(f.tokens[0].text=x+" "+f.tokens[0].text,f.tokens[0].tokens&&f.tokens[0].tokens.length>0&&f.tokens[0].tokens[0].type==="text"&&(f.tokens[0].tokens[0].text=x+" "+f.tokens[0].tokens[0].text)):f.tokens.unshift({type:"text",text:x+" "}):v+=x+" "}v+=this.parse(f.tokens,c),u+=this.renderer.listitem(v,m,!!g)}s+=this.renderer.list(u,a,l);continue}case"html":{const o=r;s+=this.renderer.html(o.text,o.block);continue}case"paragraph":{const o=r;s+=this.renderer.paragraph(this.parseInline(o.tokens));continue}case"text":{let o=r,a=o.tokens?this.parseInline(o.tokens):o.text;for(;i+1<t.length&&t[i+1].type==="text";)o=t[++i],a+=`
`+(o.tokens?this.parseInline(o.tokens):o.text);s+=e?this.renderer.paragraph(a):a;continue}default:{const o='Token with "'+r.type+'" type was not found.';if(this.options.silent)return console.error(o),"";throw new Error(o)}}}return s}parseInline(t,e){e=e||this.renderer;let s="";for(let i=0;i<t.length;i++){const r=t[i];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[r.type]){const o=this.options.extensions.renderers[r.type].call({parser:this},r);if(o!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(r.type)){s+=o||"";continue}}switch(r.type){case"escape":{const o=r;s+=e.text(o.text);break}case"html":{const o=r;s+=e.html(o.text);break}case"link":{const o=r;s+=e.link(o.href,o.title,this.parseInline(o.tokens,e));break}case"image":{const o=r;s+=e.image(o.href,o.title,o.text);break}case"strong":{const o=r;s+=e.strong(this.parseInline(o.tokens,e));break}case"em":{const o=r;s+=e.em(this.parseInline(o.tokens,e));break}case"codespan":{const o=r;s+=e.codespan(o.text);break}case"br":s+=e.br();break;case"del":{const o=r;s+=e.del(this.parseInline(o.tokens,e));break}case"text":{const o=r;s+=e.text(o.text);break}default:{const o='Token with "'+r.type+'" type was not found.';if(this.options.silent)return console.error(o),"";throw new Error(o)}}}return s}}class Gs{constructor(t){d(this,"options");this.options=t||ds}preprocess(t){return t}postprocess(t){return t}processAllTokens(t){return t}}d(Gs,"passThroughHooks",new Set(["preprocess","postprocess","processAllTokens"]));var us,ri,io,zr;const es=new(zr=class{constructor(...n){Z(this,us);d(this,"defaults",{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null});d(this,"options",this.setOptions);d(this,"parse",A(this,us,ri).call(this,De.lex,Ae.parse));d(this,"parseInline",A(this,us,ri).call(this,De.lexInline,Ae.parseInline));d(this,"Parser",Ae);d(this,"Renderer",In);d(this,"TextRenderer",Ei);d(this,"Lexer",De);d(this,"Tokenizer",Cn);d(this,"Hooks",Gs);this.use(...n)}walkTokens(n,t){var s,i;let e=[];for(const r of n)switch(e=e.concat(t.call(this,r)),r.type){case"table":{const o=r;for(const a of o.header)e=e.concat(this.walkTokens(a.tokens,t));for(const a of o.rows)for(const l of a)e=e.concat(this.walkTokens(l.tokens,t));break}case"list":{const o=r;e=e.concat(this.walkTokens(o.items,t));break}default:{const o=r;(i=(s=this.defaults.extensions)==null?void 0:s.childTokens)!=null&&i[o.type]?this.defaults.extensions.childTokens[o.type].forEach(a=>{const l=o[a].flat(1/0);e=e.concat(this.walkTokens(l,t))}):o.tokens&&(e=e.concat(this.walkTokens(o.tokens,t)))}}return e}use(...n){const t=this.defaults.extensions||{renderers:{},childTokens:{}};return n.forEach(e=>{const s={...e};if(s.async=this.defaults.async||s.async||!1,e.extensions&&(e.extensions.forEach(i=>{if(!i.name)throw new Error("extension name required");if("renderer"in i){const r=t.renderers[i.name];t.renderers[i.name]=r?function(...o){let a=i.renderer.apply(this,o);return a===!1&&(a=r.apply(this,o)),a}:i.renderer}if("tokenizer"in i){if(!i.level||i.level!=="block"&&i.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");const r=t[i.level];r?r.unshift(i.tokenizer):t[i.level]=[i.tokenizer],i.start&&(i.level==="block"?t.startBlock?t.startBlock.push(i.start):t.startBlock=[i.start]:i.level==="inline"&&(t.startInline?t.startInline.push(i.start):t.startInline=[i.start]))}"childTokens"in i&&i.childTokens&&(t.childTokens[i.name]=i.childTokens)}),s.extensions=t),e.renderer){const i=this.defaults.renderer||new In(this.defaults);for(const r in e.renderer){if(!(r in i))throw new Error(`renderer '${r}' does not exist`);if(r==="options")continue;const o=r,a=e.renderer[o],l=i[o];i[o]=(...c)=>{let u=a.apply(i,c);return u===!1&&(u=l.apply(i,c)),u||""}}s.renderer=i}if(e.tokenizer){const i=this.defaults.tokenizer||new Cn(this.defaults);for(const r in e.tokenizer){if(!(r in i))throw new Error(`tokenizer '${r}' does not exist`);if(["options","rules","lexer"].includes(r))continue;const o=r,a=e.tokenizer[o],l=i[o];i[o]=(...c)=>{let u=a.apply(i,c);return u===!1&&(u=l.apply(i,c)),u}}s.tokenizer=i}if(e.hooks){const i=this.defaults.hooks||new Gs;for(const r in e.hooks){if(!(r in i))throw new Error(`hook '${r}' does not exist`);if(r==="options")continue;const o=r,a=e.hooks[o],l=i[o];Gs.passThroughHooks.has(r)?i[o]=c=>{if(this.defaults.async)return Promise.resolve(a.call(i,c)).then(p=>l.call(i,p));const u=a.call(i,c);return l.call(i,u)}:i[o]=(...c)=>{let u=a.apply(i,c);return u===!1&&(u=l.apply(i,c)),u}}s.hooks=i}if(e.walkTokens){const i=this.defaults.walkTokens,r=e.walkTokens;s.walkTokens=function(o){let a=[];return a.push(r.call(this,o)),i&&(a=a.concat(i.call(this,o))),a}}this.defaults={...this.defaults,...s}}),this}setOptions(n){return this.defaults={...this.defaults,...n},this}lexer(n,t){return De.lex(n,t??this.defaults)}parser(n,t){return Ae.parse(n,t??this.defaults)}},us=new WeakSet,ri=function(n,t){return(e,s)=>{const i={...s},r={...this.defaults,...i};this.defaults.async===!0&&i.async===!1&&(r.silent||console.warn("marked(): The async option was set to true by an extension. The async: false option sent to parse will be ignored."),r.async=!0);const o=A(this,us,io).call(this,!!r.silent,!!r.async);if(e==null)return o(new Error("marked(): input parameter is undefined or null"));if(typeof e!="string")return o(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(e)+", string expected"));if(r.hooks&&(r.hooks.options=r),r.async)return Promise.resolve(r.hooks?r.hooks.preprocess(e):e).then(a=>n(a,r)).then(a=>r.hooks?r.hooks.processAllTokens(a):a).then(a=>r.walkTokens?Promise.all(this.walkTokens(a,r.walkTokens)).then(()=>a):a).then(a=>t(a,r)).then(a=>r.hooks?r.hooks.postprocess(a):a).catch(o);try{r.hooks&&(e=r.hooks.preprocess(e));let a=n(e,r);r.hooks&&(a=r.hooks.processAllTokens(a)),r.walkTokens&&this.walkTokens(a,r.walkTokens);let l=t(a,r);return r.hooks&&(l=r.hooks.postprocess(l)),l}catch(a){return o(a)}}},io=function(n,t){return e=>{if(e.message+=`
Please report this to https://github.com/markedjs/marked.`,n){const s="<p>An error occurred:</p><pre>"+se(e.message+"",!0)+"</pre>";return t?Promise.resolve(s):s}if(t)return Promise.reject(e);throw e}},zr);function nt(n,t){return es.parse(n,t)}nt.options=nt.setOptions=function(n){return es.setOptions(n),nt.defaults=es.defaults,Hi(nt.defaults),nt},nt.getDefaults=ga,nt.defaults=ds,nt.use=function(...n){return es.use(...n),nt.defaults=es.defaults,Hi(nt.defaults),nt},nt.walkTokens=function(n,t){return es.walkTokens(n,t)},nt.parseInline=es.parseInline,nt.Parser=Ae,nt.parser=Ae.parse,nt.Renderer=In,nt.TextRenderer=Ei,nt.Lexer=De,nt.lexer=De.lex,nt.Tokenizer=Cn,nt.Hooks=Gs,nt.parse=nt,nt.options,nt.setOptions,nt.use,nt.walkTokens,nt.parseInline,Ae.parse,De.lex;const td=async(n,t)=>{if(!ys(n)||t.chatItemType!==void 0||!(t!=null&&t.request_message))return;const e=Qo.create();e.setFlag(Ut.start);try{await La(n,t,e)}catch(s){e.setFlag(Ut.exceptionThrown),console.error("Failed to classify and distill memories",s)}finally{e.setFlag(Ut.end),n.extensionClient.reportAgentSessionEvent({eventName:Ko.classifyAndDistill,conversationId:n.id,eventData:{classifyAndDistillData:e}})}},La=async(n,t,e)=>{const s=crypto.randomUUID();e.setRequestId(Ut.memoriesRequestId,s);const i=yt(n).id;e.setFlag(Ut.startSendSilentExchange);const{responseText:r,requestId:o}=await n.sendSilentExchange({model_id:n.selectedModelId??void 0,request_message:t.request_message,disableRetrieval:!0,disableSelectedCodeDetails:!0,memoriesInfo:{isClassifyAndDistill:!0}});if(e.setStringStats(Ut.sendSilentExchangeResponseStats,r),o?e.setRequestId(Ut.sendSilentExchangeRequestId,o):e.setFlag(Ut.noRequestId),yt(n).id!==i)return void e.setFlag(Ut.conversationChanged);let a;try{let c=r;try{const u=nt.lexer(r);u.length===1&&u[0].type==="code"&&u[0].text&&(c=u[0].text)}catch(u){console.warn("Markdown lexing failed during response parsing, attempting to parse as raw string:",u)}a=JSON.parse(c)}catch{throw e.setFlag(Ut.invalidResponse),new Error("Invalid response from classify and distill")}if(typeof a.explanation!="string"||typeof a.content!="string"||typeof a.worthRemembering!="boolean")throw e.setFlag(Ut.invalidResponse),new Error("Invalid response from classify and distill");e.setStringStats(Ut.explanationStats,a.explanation),e.setStringStats(Ut.contentStats,a.content),e.setFlag(Ut.worthRemembering,a.worthRemembering);const l=a.worthRemembering?a.content:void 0;l&&za(n,l,s,e)},ed=n=>{var s;const t=n.chatHistory.at(-1);if(!t||!ce(t))return hs.notRunning;if(!(t.status===Ft.success||t.status===Ft.failed||t.status===Ft.cancelled))return hs.running;const e=(((s=t.structured_output_nodes)==null?void 0:s.filter(i=>i.type===$e.TOOL_USE&&!!i.tool_use))??[]).at(-1);if(!e)return hs.notRunning;switch(n.getToolUseState(t.request_id,e.tool_use.tool_use_id).phase){case Ve.runnable:return hs.awaitingUserAction;case Ve.cancelled:return hs.notRunning;default:return hs.running}},oi=n=>ce(n)&&!!n.request_message,On=n=>n.chatHistory.findLast(t=>oi(t)),sd=n=>{const t=On(n);if(t!=null&&t.structured_output_nodes){const e=t.structured_output_nodes.find(s=>s.type===$e.AGENT_MEMORY);if(e)try{const{memoriesRequestId:s,memory:i}=JSON.parse(e.content);return{memoriesRequestId:s,memory:i}}catch(s){return void console.error("Failed to parse JSON from agent memory node",s)}}},nd=n=>Ua(n,t=>{var e;return!!((e=t.structured_output_nodes)!=null&&e.some(s=>{var i;return s.type===$e.TOOL_USE&&((i=s.tool_use)==null?void 0:i.tool_name)==="remember"}))}).length>0,Ua=(n,t)=>{const e=On(n);return e!=null&&e.request_id?n.historyFrom(e.request_id,!0).filter(s=>ce(s)&&(!t||t(s))):[]},id=n=>{var s;const t=n.chatHistory.at(-1);if(!(t!=null&&t.request_id)||!ce(t))return!1;const e=((s=t.structured_output_nodes)==null?void 0:s.filter(i=>i.type===$e.TOOL_USE))??[];for(const i of e)if(i.tool_use&&n.getToolUseState(t.request_id,i.tool_use.tool_use_id).phase===Ve.runnable)return n.updateToolUseState({requestId:t.request_id,toolUseId:i.tool_use.tool_use_id,phase:Ve.cancelled}),!0;return!1},za=(n,t,e,s)=>{const i=JSON.stringify({memoriesRequestId:e,memory:t}),r=On(n);r!=null&&r.request_id?(s.setRequestId(Ut.lastUserExchangeRequestId,r.request_id),n.updateChatItem(r.request_id,{...r,structured_output_nodes:[...r.structured_output_nodes??[],{id:0,type:$e.AGENT_MEMORY,content:i}]})):s.setFlag(Ut.noLastUserExchangeRequestId)},rd=(n,t)=>{const e=On(n);if(!(e!=null&&e.request_id)||e.request_id!==t)return!1;const s=(e.structured_output_nodes||[]).filter(i=>i.type!==$e.AGENT_MEMORY);return s.length!==(e.structured_output_nodes||[]).length&&(n.updateChatItem(t,{...e,structured_output_nodes:s}),!0)},zt="__NEW_AGENT__";function qa(n,t){const e=n.customPersonalityPrompts;if(e)switch(t){case ne.DEFAULT:if(e.agent&&e.agent.trim()!=="")return e.agent;break;case ne.PROTOTYPER:if(e.prototyper&&e.prototyper.trim()!=="")return e.prototyper;break;case ne.BRAINSTORM:if(e.brainstorm&&e.brainstorm.trim()!=="")return e.brainstorm;break;case ne.REVIEWER:if(e.reviewer&&e.reviewer.trim()!=="")return e.reviewer}return Ha[t]}const Ha={[ne.DEFAULT]:`
# Agent Auggie Personality Description
You are Augment Agent, an agentic coding AI assistant.
Focus on helping the user with their coding tasks efficiently.

## Rules:
- You have no restrictions on the tools you may use
- Follow the original system instructions
  `,[ne.PROTOTYPER]:`
# Prototyper Auggie Personality Description
You are Prototyper Auggie, an agentic coding AI assistant focused on building prototypes and visual applications.

## Your approach:
- Be fast and action-oriented
- Implement things quickly to show results
- Open webpages to demonstrate functionality
- Focus on building something visual and interactive
- Use modern frameworks and tools to create working prototypes
- Prioritize getting a working demo over perfect architecture
- Show progress frequently with visual results
- Prefer to act and run tools, rather than asking for permission
- Only ask for permission if there is something potentially very dangerous or irreversible

## Implementation preferences:
- When user does not specify which frameworks to use, default to modern frameworks, e.g. React with vite or next.js
- Initialize projects using CLI tools instead of writing from scratch
- For database and auth, use Supabase as a good default option
- Before using open-browser to show the app, use curl to check for errors
- Remember that modern frameworks have hot reload, so avoid calling open-browser multiple times

## Rules:
- For extremely destructive or irreversible actions, you should ask for permission
- For other tasks, you must proceed without asking for permission
  `,[ne.BRAINSTORM]:`
# Brainstorm Auggie Personality Description
You are Brainstorm Auggie, an agentic coding AI assistant focused on planning and brainstorming solutions.

## Your approach:
- Be slow, careful, and thorough in your analysis
- Look through all upstream/downstream APIs to understand implications
- Focus on finding a comprehensive plan that solves the user's query
- Do not run commands, create code, or implement solutions directly
- Your job is to be introspective and think deeply about the problem
- Brainstorm multiple approaches and evaluate their tradeoffs
- Consider edge cases and potential issues with each approach

## Planning preferences:
- Analyze the codebase thoroughly before suggesting changes
- Consider multiple implementation options with pros and cons
- Identify potential risks and challenges for each approach
- Create detailed, step-by-step plans for implementation
- Provide reasoning for architectural decisions
- Consider performance, maintainability, and scalability
- Do not execute the plan - your role is to provide guidance only

## Rules:
- Prefer information gathering and non-destructive tools
- Prefer non-destructive and non-modifying tools
- You must never execute code, modify the codebase, or make changes
- Consider using Mermaid diagrams to help visualize complex concepts
- Once you have a proposal, please examine it critically, and do a revision before finalizing
  `,[ne.REVIEWER]:`
# Reviewer Auggie Personality Description
You are Reviewer Auggie, an agentic coding AI assistant focused on reviewing code changes and identifying potential issues.

## Your approach:
- Act like a code detective to find potential bugs and issues
- Use git commands to analyze changes against the merge base
- Be super inquisitive and look for anything suspicious
- Build a mental model of what is happening in the code change
- Analyze API implications and downstream effects
- Guard the codebase from potential negative side effects
- Focus on understanding the changes from first principles

## Review preferences:
- Use git and GitHub tools to get code history information
- Compare changes against the logical base or merge base
- Look for edge cases and potential bugs
- Analyze API contracts and potential breaking changes
- Consider performance implications
- Check for security vulnerabilities
- Verify test coverage for the changes

## Rules:
- Use git commands and GitHub API to analyze code changes
- Be thorough and methodical in your analysis
- Focus on finding potential issues rather than implementing solutions
- Provide constructive feedback with specific examples
- Consider both the technical implementation and the broader impact
  `};class ct{constructor(t,e,s,i){d(this,"_state");d(this,"_subscribers",new Set);d(this,"_focusModel",new Yo);d(this,"_onSendExchangeListeners",[]);d(this,"_onNewConversationListeners",[]);d(this,"_onHistoryDeleteListeners",[]);d(this,"_onBeforeChangeConversationListeners",[]);d(this,"_totalCharactersCacheThrottleMs",1e3);d(this,"_totalCharactersStore");d(this,"subscribe",t=>(this._subscribers.add(t),t(this),()=>{this._subscribers.delete(t)}));d(this,"setConversation",(t,e=!0,s=!0)=>{const i=t.id!==this._state.id;i&&s&&(t.toolUseStates=Object.fromEntries(Object.entries(t.toolUseStates??{}).map(([o,a])=>{if(a.requestId&&a.toolUseId){const{requestId:l,toolUseId:c}=Fi(o);return l===a.requestId&&c===a.toolUseId||console.warn("Tool use state key does not match request and tool use IDs. Got key ",o,"but object has ",Bn(a)),[o,a]}return[o,{...a,...Fi(o)}]})),(t=this._notifyBeforeChangeConversation(this._state,t)).lastInteractedAtIso=new Date().toISOString()),e&&i&&this.isValid&&(this.saveDraftActiveContextIds(),this._unloadContextFromConversation(this._state));const r=ct.isEmpty(t);if(i&&r){const o=this._state.draftExchange;o&&(t.draftExchange=o)}return this._state=t,this._focusModel.setItems(this._state.chatHistory.filter(ce)),this._focusModel.initFocusIdx(-1),this._subscribers.forEach(o=>o(this)),this._saveConversation(this._state),i&&(this._loadContextFromConversation(t),this.loadDraftActiveContextIds(),this._onNewConversationListeners.forEach(o=>o())),!0});d(this,"update",t=>{this.setConversation({...this._state,...t}),this._totalCharactersStore.updateStore()});d(this,"toggleIsPinned",()=>{this.update({isPinned:!this.isPinned})});d(this,"setName",t=>{this.update({name:t})});d(this,"setSelectedModelId",t=>{this.update({selectedModelId:t})});d(this,"updateFeedback",(t,e)=>{this.update({feedbackStates:{...this._state.feedbackStates,[t]:e}})});d(this,"updateToolUseState",t=>{this.update({toolUseStates:{...this._state.toolUseStates,[Bn(t)]:t}})});d(this,"getToolUseState",(t,e)=>t===void 0||e===void 0||this.toolUseStates===void 0?{phase:Ve.unknown,requestId:t??"",toolUseId:e??""}:this.toolUseStates[Bn({requestId:t,toolUseId:e})]||{phase:Ve.new});d(this,"getLastToolUseState",()=>{var s,i;const t=this.lastExchange;if(!t)return{phase:Ve.unknown};const e=(((s=t==null?void 0:t.structured_output_nodes)==null?void 0:s.filter(r=>r.type===$e.TOOL_USE))??[]).at(-1);return e?this.getToolUseState(t.request_id,(i=e.tool_use)==null?void 0:i.tool_use_id):{phase:Ve.unknown}});d(this,"addExchange",t=>{const e=[...this._state.chatHistory,t];let s;ce(t)&&(s=t.request_id?{...this._state.feedbackStates,[t.request_id]:{selectedRating:Qr.unset,feedbackNote:""}}:void 0),this.update({chatHistory:e,...s?{feedbackStates:s}:{},lastUrl:void 0})});d(this,"resetShareUrl",()=>{this.update({lastUrl:void 0})});d(this,"updateExchangeById",(t,e,s=!1)=>{var a;const i=this.exchangeWithRequestId(e);if(i===null)return console.warn("No exchange with this request ID found."),!1;s&&t.response_text!==void 0&&(t.response_text=(i.response_text??"")+(t.response_text??"")),s&&(t.structured_output_nodes=[...i.structured_output_nodes??[],...t.structured_output_nodes??[]]),s&&t.workspace_file_chunks!==void 0&&(t.workspace_file_chunks=[...i.workspace_file_chunks??[],...t.workspace_file_chunks??[]]);const r=(a=(t.structured_output_nodes||[]).find(l=>l.type===$e.MAIN_TEXT_FINISHED))==null?void 0:a.content;r&&r!==t.response_text&&(t.response_text=r);let o=this._state.isShareable||_s({...i,...t});return this.update({chatHistory:this.chatHistory.map(l=>l.request_id===e?{...l,...t}:l),isShareable:o}),!0});d(this,"clearMessagesFromHistory",t=>{this.update({chatHistory:this.chatHistory.filter(e=>!e.request_id||!t.has(e.request_id))}),this._extensionClient.clearMetadataFor({requestIds:Array.from(t)})});d(this,"clearHistory",()=>{this._extensionClient.clearMetadataFor({requestIds:this.requestIds}),this.update({chatHistory:[]})});d(this,"clearHistoryFrom",async(t,e=!0)=>{const s=this.historyFrom(t,e),i=s.map(r=>r.request_id).filter(r=>r!==void 0);this.update({chatHistory:this.historyTo(t,!e)}),this._extensionClient.clearMetadataFor({requestIds:i}),s.forEach(r=>{this._onHistoryDeleteListeners.forEach(o=>o(r))})});d(this,"clearMessageFromHistory",t=>{this.update({chatHistory:this.chatHistory.filter(e=>e.request_id!==t)}),this._extensionClient.clearMetadataFor({requestIds:[t]})});d(this,"historyTo",(t,e=!1)=>{const s=this.chatHistory.findIndex(i=>i.request_id===t);return s===-1?[]:this.chatHistory.slice(0,e?s+1:s)});d(this,"historyFrom",(t,e=!0)=>{const s=this.chatHistory.findIndex(i=>i.request_id===t);return s===-1?[]:this.chatHistory.slice(e?s:s+1)});d(this,"resendLastExchange",async()=>{const t=this.lastExchange;if(t&&!this.awaitingReply)return this.resendTurn(t)});d(this,"resendTurn",t=>this.awaitingReply?Promise.resolve():(this._removeTurn(t),this.sendExchange({chatItemType:t.chatItemType,request_message:t.request_message,rich_text_json_repr:t.rich_text_json_repr,status:Ft.draft,mentioned_items:t.mentioned_items,structured_request_nodes:t.structured_request_nodes,disableSelectedCodeDetails:t.disableSelectedCodeDetails,chatHistory:t.chatHistory,model_id:t.model_id})));d(this,"_removeTurn",t=>{this.update({chatHistory:this.chatHistory.filter(e=>e!==t&&(!t.request_id||e.request_id!==t.request_id))})});d(this,"exchangeWithRequestId",t=>this.chatHistory.find(e=>e.request_id===t)||null);d(this,"resetTotalCharactersCache",()=>{this._totalCharactersStore.resetCache()});d(this,"markSeen",async t=>{if(!t.request_id||!this.chatHistory.find(s=>s.request_id===t.request_id))return;const e={seen_state:Ps.seen};this.update({chatHistory:this.chatHistory.map(s=>s.request_id===t.request_id?{...s,...e}:s)})});d(this,"createStructuredRequestNodes",t=>this._jsonToStructuredRequest(t));d(this,"saveDraftMentions",t=>{if(!this.draftExchange)return;const e=t.filter(s=>!s.personality);this.update({draftExchange:{...this.draftExchange,mentioned_items:e}})});d(this,"saveDraftActiveContextIds",()=>{const t=this._specialContextInputModel.recentActiveItems.map(e=>e.id);this.update({draftActiveContextIds:t})});d(this,"loadDraftActiveContextIds",()=>{const t=new Set(this.draftActiveContextIds??[]),e=this._specialContextInputModel.recentItems.filter(i=>t.has(i.id)||i.recentFile||i.selection||i.sourceFolder),s=this._specialContextInputModel.recentItems.filter(i=>!(t.has(i.id)||i.recentFile||i.selection||i.sourceFolder));this._specialContextInputModel.markItemsActive(e.reverse()),this._specialContextInputModel.markItemsInactive(s.reverse())});d(this,"saveDraftExchange",(t,e)=>{var o,a,l;const s=t!==((o=this.draftExchange)==null?void 0:o.request_message),i=e!==((a=this.draftExchange)==null?void 0:a.rich_text_json_repr);if(!s&&!i)return;const r=(l=this.draftExchange)==null?void 0:l.mentioned_items;this.update({draftExchange:{request_message:t,rich_text_json_repr:e,mentioned_items:r,status:Ft.draft}})});d(this,"clearDraftExchange",()=>{const t=this.draftExchange;return this.update({draftExchange:void 0}),t});d(this,"sendDraftExchange",()=>{if(this._extensionClient.triggerUsedChatMetric(),!this.canSendDraft||!this.draftExchange)return!1;const t=this.clearDraftExchange();if(!t)return!1;const e=this._chatFlagModel.enableChatMultimodal&&t.rich_text_json_repr?this._jsonToStructuredRequest(t.rich_text_json_repr):void 0;return this.sendExchange({...t,structured_request_nodes:e,model_id:this.selectedModelId??void 0}).then(()=>{var o,a;const s=!this.name&&this.chatHistory.length===1&&((o=this.firstExchange)==null?void 0:o.request_id)===this.chatHistory[0].request_id,i=ys(this)&&((a=this._state.extraData)==null?void 0:a.hasAgentOnboarded)&&(r=this.chatHistory,r.filter(l=>oi(l))).length===2;var r;this._chatFlagModel.summaryTitles&&(s||i)&&this.updateConversationTitle()}).finally(()=>{var s;ys(this)&&this._extensionClient.reportAgentRequestEvent({eventName:Jo.sentUserMessage,conversationId:this.id,requestId:((s=this.lastExchange)==null?void 0:s.request_id)??"UNKNOWN_REQUEST_ID",chatHistoryLength:this.chatHistory.length})}),this.focusModel.setFocusIdx(void 0),!0});d(this,"cancelMessage",async()=>{var t;this.canCancelMessage&&((t=this.lastExchange)!=null&&t.request_id)&&(this.updateExchangeById({status:Ft.cancelled},this.lastExchange.request_id),await this._extensionClient.cancelChatStream(this.lastExchange.request_id))});d(this,"sendInstructionExchange",async(t,e)=>{let s=`temp-fe-${crypto.randomUUID()}`;const i={status:Ft.sent,request_id:s,request_message:t,model_id:this.selectedModelId??void 0,structured_output_nodes:[],seen_state:Ps.unseen,timestamp:new Date().toISOString()};this.addExchange(i);for await(const r of this._extensionClient.sendInstructionMessage(i,e)){if(!this.updateExchangeById(r,s,!0))return;s=r.request_id||s}});d(this,"updateConversationTitle",async()=>{const{responseText:t}=await this.sendSummaryExchange();this.update({name:t})});d(this,"sendSummaryExchange",()=>{const t={status:Ft.sent,request_message:"Please provide a clear and concise summary of our conversation so far. The summary must be less than 6 words long. The summary must contain the key points of the conversation. The summary must be in the form of a title which will represent the conversation. The response should not include any additional formatting such as wrapping the response with quotation marks.",model_id:this.selectedModelId??void 0,chatItemType:Os.summaryTitle,disableRetrieval:!0,disableSelectedCodeDetails:!0};return this.sendSilentExchange(t)});d(this,"generateCommitMessage",async()=>{let t=`temp-fe-${crypto.randomUUID()}`;const e={status:Ft.sent,request_id:t,request_message:"Please generate a commit message based on the diff of my staged and unstaged changes.",model_id:this.selectedModelId??void 0,mentioned_items:[],seen_state:Ps.unseen,chatItemType:Os.generateCommitMessage,disableSelectedCodeDetails:!0,chatHistory:[],timestamp:new Date().toISOString()};this.addExchange(e);for await(const s of this._extensionClient.generateCommitMessage()){if(!this.updateExchangeById(s,t,!0))return;t=s.request_id||t}});d(this,"sendExchange",async(t,e=!1)=>{var o;this.updateLastInteraction();let s=`temp-fe-${crypto.randomUUID()}`,i=this._chatFlagModel.isModelIdValid(t.model_id)?t.model_id:void 0;if(this._chatFlagModel.doUseNewDraftFunctionality&&ct.isNew(this._state)){const a=crypto.randomUUID(),l=this._state.id;try{await this._extensionClient.migrateConversationId(l,a)}catch(c){console.error("Failed to migrate conversation checkpoints:",c)}this._state={...this._state,id:a},this._saveConversation(this._state,!0),this._extensionClient.setCurrentConversation(a),this._subscribers.forEach(c=>c(this))}t=Qi(t);let r={status:Ft.sent,request_id:s,request_message:t.request_message,rich_text_json_repr:t.rich_text_json_repr,model_id:i,mentioned_items:t.mentioned_items,structured_output_nodes:t.structured_output_nodes,seen_state:Ps.unseen,chatItemType:t.chatItemType,disableSelectedCodeDetails:t.disableSelectedCodeDetails,chatHistory:t.chatHistory,structured_request_nodes:t.structured_request_nodes,timestamp:new Date().toISOString()};this.addExchange(r),this._loadContextFromExchange(r),this._onSendExchangeListeners.forEach(a=>a(r)),r=await this._addIdeStateNode(r),this.updateExchangeById({structured_request_nodes:r.structured_request_nodes},s,!1);for await(const a of this.sendUserMessage(s,r,e)){if(((o=this.exchangeWithRequestId(s))==null?void 0:o.status)!==Ft.sent||!this.updateExchangeById(a,s,!0))return;s=a.request_id||s}});d(this,"sendSuggestedQuestion",t=>{this.sendExchange({request_message:t,status:Ft.draft}),this._extensionClient.triggerUsedChatMetric(),this._extensionClient.reportWebviewClientEvent(Vo.chatUseSuggestedQuestion)});d(this,"recoverAllExchanges",async()=>{await Promise.all(this.recoverableExchanges.map(this.recoverExchange))});d(this,"recoverExchange",async t=>{var i;if(!t.request_id||t.status!==Ft.sent)return;let e=t.request_id;const s=(i=t.structured_output_nodes)==null?void 0:i.filter(r=>r.type===$e.AGENT_MEMORY);this.updateExchangeById({...t,response_text:"",structured_output_nodes:s??[]},e);for await(const r of this.getChatStream(t)){if(!this.updateExchangeById(r,e,!0))return;e=r.request_id||e}});d(this,"_loadContextFromConversation",t=>{t.chatHistory.forEach(e=>{ce(e)&&this._loadContextFromExchange(e)})});d(this,"_loadContextFromExchange",t=>{t.mentioned_items&&(this._specialContextInputModel.updateItems(t.mentioned_items,[]),this._specialContextInputModel.markItemsActive(t.mentioned_items))});d(this,"_unloadContextFromConversation",t=>{t.chatHistory.forEach(e=>{ce(e)&&this._unloadContextFromExchange(e)})});d(this,"_unloadContextFromExchange",t=>{t.mentioned_items&&this._specialContextInputModel.updateItems([],t.mentioned_items)});d(this,"updateLastInteraction",()=>{this.update({lastInteractedAtIso:new Date().toISOString()})});d(this,"_jsonToStructuredRequest",t=>{const e=[],s=r=>{var a;const o=e.at(-1);if((o==null?void 0:o.type)===xe.TEXT){const l=((a=o.text_node)==null?void 0:a.content)??"",c={...o,text_node:{content:l+r}};e[e.length-1]=c}else e.push({id:e.length,type:xe.TEXT,text_node:{content:r}})},i=r=>{var o,a,l,c;if(r.type==="doc"||r.type==="paragraph")for(const u of r.content??[])i(u);else if(r.type==="hardBreak")s(`
`);else if(r.type==="text")s(r.text??"");else if(r.type==="image"){if(typeof((o=r.attrs)==null?void 0:o.src)!="string")return void console.error("Image source is not a string: ",(a=r.attrs)==null?void 0:a.src);if(r.attrs.isLoading)return;const u=(l=r.attrs)==null?void 0:l.title,p=this._fileNameToImageFormat(u);e.push({id:e.length,type:xe.IMAGE_ID,image_id_node:{image_id:r.attrs.src,format:p}})}else if(r.type==="mention"){const u=(c=r.attrs)==null?void 0:c.data;u&&Xr(u)?e.push({id:e.length,type:xe.TEXT,text_node:{content:qa(this._chatFlagModel,u.personality.type)}}):s(`@\`${(u==null?void 0:u.name)??(u==null?void 0:u.id)}\``)}};return i(t),e});this._extensionClient=t,this._chatFlagModel=e,this._specialContextInputModel=s,this._saveConversation=i,this._state={...ct.create()},this._totalCharactersStore=this._createTotalCharactersStore()}_createTotalCharactersStore(){return fa(()=>{let t=0;const e=this._state.chatHistory;return this._convertHistoryToExchanges(e).forEach(s=>{t+=JSON.stringify(s).length}),this._state.draftExchange&&(t+=JSON.stringify(this._state.draftExchange).length),t},0,this._totalCharactersCacheThrottleMs)}async decidePersonaType(){var t;try{return(((t=(await this._extensionClient.getWorkspaceInfo()).trackedFileCount)==null?void 0:t.reduce((s,i)=>s+i,0))||0)<=4?ne.PROTOTYPER:ne.DEFAULT}catch(e){return console.error("Error determining persona type:",e),ne.DEFAULT}}static create(t={}){const e=new Date().toISOString();return{id:t.id||crypto.randomUUID(),name:void 0,createdAtIso:e,lastInteractedAtIso:e,chatHistory:[],feedbackStates:{},toolUseStates:{},draftExchange:void 0,draftActiveContextIds:void 0,selectedModelId:void 0,requestIds:[],isPinned:!1,lastUrl:void 0,isShareable:!1,extraData:{},personaType:ne.DEFAULT,...t}}static toSentenceCase(t){return t.charAt(0).toUpperCase()+t.slice(1)}static getDisplayName(t){var i;const e=this._filterToExchanges(t);let s;return s=da(t)?"Autofix Chat":ys(t)?"New Agent":"New Chat",ct.toSentenceCase(t.name||((i=e[0])==null?void 0:i.request_message)||s)}static _filterToExchanges(t){return t.chatHistory.filter(e=>ce(e))}static isNew(t){return t.id===zt}static isEmpty(t){var e;return t.chatHistory.filter(s=>ce(s)).length===0&&!((e=t.draftExchange)!=null&&e.request_message)}static isNamed(t){return t.name!==void 0&&t.name!==""}static getTime(t,e){return e==="lastMessageTimestamp"?ct.lastMessageTimestamp(t):e==="lastInteractedAt"?ct.lastInteractedAt(t):ct.createdAt(t)}static createdAt(t){return new Date(t.createdAtIso)}static lastInteractedAt(t){return new Date(t.lastInteractedAtIso)}static lastMessageTimestamp(t){const e=this._filterToExchanges(t);if(e.length===0)return this.createdAt(t);const s=e[e.length-1];return s.timestamp?new Date(s.timestamp):this.createdAt(t)}static isValid(t){return t.id!==void 0&&(!ct.isEmpty(t)||ct.isNamed(t))}onBeforeChangeConversation(t){return this._onBeforeChangeConversationListeners.push(t),()=>{this._onBeforeChangeConversationListeners=this._onBeforeChangeConversationListeners.filter(e=>e!==t)}}_notifyBeforeChangeConversation(t,e){let s=e;for(const i of this._onBeforeChangeConversationListeners){const r=i(t,s);r!==void 0&&(s=r)}return s}get extraData(){return this._state.extraData}set extraData(t){this.update({extraData:t})}get focusModel(){return this._focusModel}get isValid(){return ct.isValid(this._state)}get id(){return this._state.id}get name(){return this._state.name}get personaType(){return this._state.personaType??ne.DEFAULT}get rootTaskUuid(){return this._state.rootTaskUuid}set rootTaskUuid(t){this.update({rootTaskUuid:t})}get displayName(){return ct.getDisplayName(this._state)}get createdAtIso(){return this._state.createdAtIso}get createdAt(){return ct.createdAt(this._state)}get chatHistory(){return this._state.chatHistory}get feedbackStates(){return this._state.feedbackStates}get toolUseStates(){return this._state.toolUseStates}get draftExchange(){return this._state.draftExchange}get selectedModelId(){return this._state.selectedModelId}get isPinned(){return!!this._state.isPinned}get extensionClient(){return this._extensionClient}addChatItem(t){this.addExchange(t)}get requestIds(){return this._state.chatHistory.map(t=>t.request_id).filter(t=>t!==void 0)}get hasDraft(){var s;const t=(((s=this.draftExchange)==null?void 0:s.request_message)??"").trim()!=="",e=this.hasImagesInDraft();return t||e}hasImagesInDraft(){var s;const t=(s=this.draftExchange)==null?void 0:s.rich_text_json_repr;if(!t)return!1;const e=i=>Array.isArray(i)?i.some(e):!!i&&(i.type==="image"||!(!i.content||!Array.isArray(i.content))&&i.content.some(e));return e(t)}get canSendDraft(){return this.hasDraft&&!this.awaitingReply}get canCancelMessage(){return this.awaitingReply}get firstExchange(){const t=ct._filterToExchanges(this);return t.length===0?null:t[0]}get lastExchange(){const t=ct._filterToExchanges(this);return t.length===0?null:t[t.length-1]}get canClearHistory(){return this._state.chatHistory.length!==0&&!this.awaitingReply}get recoverableExchanges(){return this._state.chatHistory.filter(t=>ce(t)&&t.status===Ft.sent)}get successfulMessages(){return this._state.chatHistory.filter(t=>_s(t)||Hs(t))}get totalCharactersStore(){return this._totalCharactersStore}_convertHistoryToExchanges(t){if(t.length===0)return[];const e=[];for(const s of t)if(_s(s))e.push(Ga(s));else if(Hs(s)&&s.fromTimestamp!==void 0&&s.toTimestamp!==void 0&&s.revertTarget){const i=Ba(s,1),r={request_message:"",response_text:"",request_id:s.request_id||crypto.randomUUID(),request_nodes:[i],response_nodes:[]};e.push(r)}return e}get awaitingReply(){return this.lastExchange!==null&&this.lastExchange.status===Ft.sent}get lastInteractedAtIso(){return this._state.lastInteractedAtIso}get draftActiveContextIds(){return this._state.draftActiveContextIds}async sendSilentExchange(t){const e=crypto.randomUUID();let s,i="";const r=await this._addIdeStateNode(Qi({...t,request_id:e,status:Ft.sent,timestamp:new Date().toISOString()}));for await(const o of this.sendUserMessage(e,r,!0))o.response_text&&(i+=o.response_text),o.request_id&&(s=o.request_id);return{responseText:i,requestId:s}}async*getChatStream(t){t.request_id&&(yield*this._extensionClient.getExistingChatStream(t,{flags:this._chatFlagModel}))}_createStreamStateHandlers(t,e,s){return[]}async*sendUserMessage(t,e,s){var u;const i=this._specialContextInputModel.chatActiveContext;let r;if(e.chatHistory!==void 0)r=e.chatHistory;else{let p=this.successfulMessages;if(e.chatItemType===Os.summaryTitle){const f=p.findIndex(g=>g.chatItemType!==Os.agentOnboarding&&oi(g));f!==-1&&(p=p.slice(f))}r=this._convertHistoryToExchanges(p)}let o=this.personaType;if(e.structured_request_nodes){const p=e.structured_request_nodes.find(f=>f.type===xe.CHANGE_PERSONALITY);p&&p.change_personality_node&&(o=p.change_personality_node.personality_type)}const a={text:e.request_message,chatHistory:r,silent:s,modelId:e.model_id,context:i,userSpecifiedFiles:i.userSpecifiedFiles,externalSourceIds:(u=i.externalSources)==null?void 0:u.map(p=>p.id),disableRetrieval:e.disableRetrieval??!1,disableSelectedCodeDetails:e.disableSelectedCodeDetails??!1,nodes:e.structured_request_nodes,memoriesInfo:e.memoriesInfo,personaType:o,conversationId:this.id,createdTimestamp:Date.now()},l=this._createStreamStateHandlers(t,a,{flags:this._chatFlagModel}),c=this._extensionClient.startChatStreamWithRetry(t,a,{flags:this._chatFlagModel});for await(const p of c){let f=p;for(const g of l)f=g.handleChunk(f)??f;yield f}for(const p of l)yield*p.handleComplete()}onSendExchange(t){return this._onSendExchangeListeners.push(t),()=>{this._onSendExchangeListeners=this._onSendExchangeListeners.filter(e=>e!==t)}}onNewConversation(t){return this._onNewConversationListeners.push(t),()=>{this._onNewConversationListeners=this._onNewConversationListeners.filter(e=>e!==t)}}onHistoryDelete(t){return this._onHistoryDeleteListeners.push(t),()=>{this._onHistoryDeleteListeners=this._onHistoryDeleteListeners.filter(e=>e!==t)}}updateChatItem(t,e){return this.chatHistory.find(s=>s.request_id===t)===null?(console.warn("No exchange with this request ID found."),!1):(this.update({chatHistory:this.chatHistory.map(s=>s.request_id===t?{...s,...e}:s)}),!0)}_fileNameToImageFormat(t){var s;switch((s=t.split(".").at(-1))==null?void 0:s.toLowerCase()){case"jpeg":case"jpg":return As.JPEG;case"png":return As.PNG;case"gif":return As.GIF;case"webp":return As.WEBP;default:return As.IMAGE_FORMAT_UNSPECIFIED}}async _addIdeStateNode(t){let e=(t.structured_request_nodes??[]).filter(i=>i.type!==xe.IDE_STATE);const s=await this._extensionClient.getChatRequestIdeState();return s?(e=[...e,{id:ro(e)+1,type:xe.IDE_STATE,ide_state_node:s}],{...t,structured_request_nodes:e}):t}}function Ba(n,t){const e=(Hs(n),n.fromTimestamp),s=(Hs(n),n.toTimestamp),i=Hs(n)&&n.revertTarget!==void 0;return{id:t,type:xe.CHECKPOINT_REF,checkpoint_ref_node:{request_id:n.request_id||"",from_timestamp:e,to_timestamp:s,source:i?Zo.CHECKPOINT_REVERT:void 0}}}function Ga(n){const t=(n.structured_output_nodes??[]).filter(e=>e.type===$e.RAW_RESPONSE||e.type===$e.TOOL_USE);return{request_message:n.request_message,response_text:n.response_text??"",request_id:n.request_id||"",request_nodes:n.structured_request_nodes??[],response_nodes:t}}function ro(n){return n.length>0?Math.max(...n.map(t=>t.id)):0}function Qi(n){var t;if(n.request_message.length>0&&!((t=n.structured_request_nodes)!=null&&t.some(e=>e.type===xe.TEXT))){let e=n.structured_request_nodes??[];return e=[...e,{id:ro(e)+1,type:xe.TEXT,text_node:{content:n.request_message}}],{...n,structured_request_nodes:e}}return n}class ja{constructor(t=!0,e=setTimeout){d(this,"_notify",new Set);d(this,"_clearTimeout",t=>{t.timeoutId&&clearTimeout(t.timeoutId)});d(this,"_schedule",t=>{if(!this._started||t.date&&(t.timeout=t.date.getTime()-Date.now(),t.timeout<0))return;const e=this._setTimeout;t.timeoutId=e(this._handle,t.timeout,t)});d(this,"_handle",t=>{t.notify(),t.date?this._notify.delete(t):t.once||this._schedule(t)});d(this,"dispose",()=>{this._notify.forEach(this._clearTimeout),this._notify.clear()});this._started=t,this._setTimeout=e}start(){return this._started||(this._started=!0,this._notify.forEach(this._schedule)),this}stop(){return this._started=!1,this._notify.forEach(this._clearTimeout),this}get isStarted(){return this._started}set isStarted(t){t?this.start():this.stop()}once(t,e){return this._register(t,e,!0)}interval(t,e){return this._register(t,e,!1)}at(t,e){return this._register(0,e,!1,typeof t=="number"?new Date(Date.now()+t):t)}reschedule(){this._notify.forEach(t=>{this._clearTimeout(t),this._schedule(t)})}_register(t,e,s,i){if(!t&&!i)return()=>{};const r={timeout:t,notify:e,once:s,date:i};return this._notify.add(r),this._schedule(r),()=>{this._clearTimeout(r),this._notify.delete(r)}}}class Wa{constructor(t=0,e=0,s=new ja,i=At("busy"),r=At(!1)){d(this,"unsubNotify");d(this,"unsubMessage");d(this,"activity",()=>{this.idleStatus.set("busy"),this.idleScheduler.reschedule()});d(this,"focus",t=>{this.focusAfterIdle.set(t)});this._idleNotifyTimeout=t,this._idleMessageTimeout=e,this.idleScheduler=s,this.idleStatus=i,this.focusAfterIdle=r,this.idleNotifyTimeout=t,this.idleMessageTimeout=e}set idleMessageTimeout(t){var e;this._idleMessageTimeout!==t&&(this._idleMessageTimeout=t,(e=this.unsubMessage)==null||e.call(this),this.unsubMessage=this.idleScheduler.once(t,()=>{this.idleStatus.set("idle-message")}))}set idleNotifyTimeout(t){var e;this._idleNotifyTimeout!==t&&(this._idleNotifyTimeout=t,(e=this.unsubNotify)==null||e.call(this),this.unsubNotify=this.idleScheduler.once(t,()=>{this.idleStatus.set("idle-notify")}))}get idleMessageTimeout(){return this._idleMessageTimeout}get idleNotifyTimeout(){return this._idleNotifyTimeout}get notifyEnabled(){return this._idleNotifyTimeout>0}get messageEnabled(){return this._idleMessageTimeout>0}dispose(){var t,e;(t=this.unsubNotify)==null||t.call(this),(e=this.unsubMessage)==null||e.call(this),this.idleScheduler.dispose(),this.idleStatus.set("busy"),this.focusAfterIdle.set(!1)}}const hn=At("idle");var Ya=(n=>(n.manual="manual",n.auto="auto",n))(Ya||{});class Va{constructor(t,e,s,i={}){d(this,"_state",{currentConversationId:void 0,conversations:{},agentExecutionMode:"manual",isPanelCollapsed:!0,displayedAnnouncements:[]});d(this,"extensionClient");d(this,"_chatFlagsModel");d(this,"_currConversationModel");d(this,"_chatModeModel");d(this,"subscribers",new Set);d(this,"idleMessageModel",new Wa);d(this,"isPanelCollapsed");d(this,"agentExecutionMode");d(this,"sortConversationsBy");d(this,"displayedAnnouncements");d(this,"onLoaded",async()=>{var s,i;const t=await this.extensionClient.getChatInitData(),e=!this._chatFlagsModel.doUseNewDraftFunctionality&&t.enableBackgroundAgents;this._chatFlagsModel.update({enableEditableHistory:t.enableEditableHistory??!1,enablePreferenceCollection:t.enablePreferenceCollection??!1,enableRetrievalDataCollection:t.enableRetrievalDataCollection??!1,enableDebugFeatures:t.enableDebugFeatures??!1,enableRichTextHistory:t.useRichTextHistory??!0,modelDisplayNameToId:t.modelDisplayNameToId??{},fullFeatured:t.fullFeatured??!0,isRemoteAgentWindow:!1,remoteAgentId:void 0,smallSyncThreshold:t.smallSyncThreshold??15,bigSyncThreshold:t.bigSyncThreshold??1e3,enableExternalSourcesInChat:t.enableExternalSourcesInChat??!1,enableSmartPaste:t.enableSmartPaste??!1,enableDirectApply:t.enableDirectApply??!1,summaryTitles:t.summaryTitles??!1,suggestedEditsAvailable:t.suggestedEditsAvailable??!1,enableShareService:t.enableShareService??!1,maxTrackableFileCount:t.maxTrackableFileCount??Zr,enableDesignSystemRichTextEditor:t.enableDesignSystemRichTextEditor??!1,enableSources:t.enableSources??!1,enableChatMermaidDiagrams:t.enableChatMermaidDiagrams??!1,smartPastePrecomputeMode:t.smartPastePrecomputeMode??Gr.visibleHover,useNewThreadsMenu:t.useNewThreadsMenu??!1,enableChatMermaidDiagramsMinVersion:t.enableChatMermaidDiagramsMinVersion??!1,idleNewSessionMessageTimeoutMs:t.idleNewSessionMessageTimeoutMs,idleNewSessionNotificationTimeoutMs:t.idleNewSessionNotificationTimeoutMs,enableChatMultimodal:t.enableChatMultimodal??!1,enableAgentMode:t.enableAgentMode??!1,agentMemoriesFilePathName:t.agentMemoriesFilePathName,enableRichCheckpointInfo:t.enableRichCheckpointInfo??!1,userTier:t.userTier??"unknown",truncateChatHistory:t.truncateChatHistory??!1,enableBackgroundAgents:t.enableBackgroundAgents??!1,enableVirtualizedMessageList:t.enableVirtualizedMessageList??!1,customPersonalityPrompts:t.customPersonalityPrompts??{},enablePersonalities:t.enablePersonalities??!1,enableRules:t.enableRules??!1,memoryClassificationOnFirstToken:t.memoryClassificationOnFirstToken??!1,enableGenerateCommitMessage:t.enableGenerateCommitMessage??!1,doUseNewDraftFunctionality:t.enableBackgroundAgents??!1,enablePromptEnhancer:t.enablePromptEnhancer??!1,modelRegistry:t.modelRegistry??{},enableModelRegistry:t.enableModelRegistry??!1,enableTaskList:t.enableTaskList??!1,clientAnnouncement:t.clientAnnouncement??""}),e&&this.onDoUseNewDraftFunctionalityChanged(),(i=(s=this.options).onLoaded)==null||i.call(s),this.notifySubscribers(),this.checkRemoteAgentStatus()});d(this,"checkRemoteAgentStatus",async()=>{if(this._chatFlagsModel.enableBackgroundAgents){const t=await this.extensionClient.getRemoteAgentStatus();this._chatFlagsModel.update({isRemoteAgentWindow:t.isRemoteAgentWindow,remoteAgentId:t.remoteAgentId}),this.notifySubscribers()}});d(this,"subscribe",t=>(this.subscribers.add(t),t(this),()=>{this.subscribers.delete(t)}));d(this,"initialize",t=>{this._state={...this._state,...this._host.getState()},t&&(this._state.conversations[t==null?void 0:t.id]=t),this._chatFlagsModel.fullFeatured&&((t==null?void 0:t.id)!==Gn&&this.currentConversationId!==Gn||(delete this._state.conversations[Gn],this.setCurrentConversationToWelcome())),this._chatFlagsModel.subscribe(e=>{this.idleMessageModel.idleNotifyTimeout=e.idleNewSessionNotificationTimeoutMs,this.idleMessageModel.idleMessageTimeout=e.idleNewSessionMessageTimeoutMs}),this._state.conversations=Object.fromEntries(Object.entries(this._state.conversations).filter(([e,s])=>e===zt||ct.isValid(s))),this.initializeIsShareableState(),t?this.setCurrentConversation(t.id):this.setCurrentConversation(this.currentConversationId),this.subscribe(()=>this.idleMessageModel.activity()),this.setState(this._state)});d(this,"initializeIsShareableState",()=>{const t={...this._state.conversations};for(const[e,s]of Object.entries(t)){if(s.isShareable)continue;const i=s.chatHistory.some(r=>_s(r));t[e]={...s,isShareable:i}}this._state.conversations=t});d(this,"updateChatState",t=>{this._state={...this._state,...t};const e=this._state.conversations,s=new Set;for(const[i,r]of Object.entries(e))r.isPinned&&s.add(i);this.setState(this._state),this.notifySubscribers()});d(this,"saveImmediate",()=>{this._host.setState(this._state)});d(this,"setState",Ro(t=>{this._host.setState({...t,isPanelCollapsed:yt(this.isPanelCollapsed),agentExecutionMode:yt(this.agentExecutionMode),sortConversationsBy:yt(this.sortConversationsBy),displayedAnnouncements:yt(this.displayedAnnouncements)})},1e3,{maxWait:15e3}));d(this,"notifySubscribers",()=>{this.subscribers.forEach(t=>t(this))});d(this,"withWebviewClientEvent",(t,e)=>(...s)=>(this.extensionClient.reportWebviewClientEvent(t),e(...s)));d(this,"onDoUseNewDraftFunctionalityChanged",()=>{const t=!!this._state.conversations[zt];if(this.currentConversationId&&this.currentConversationId!==zt&&this._state.conversations[this.currentConversationId]&&ct.isEmpty(this._state.conversations[this.currentConversationId])&&!t){const e={...this._state.conversations[this.currentConversationId],id:zt};this._state.conversations[zt]=e,this.deleteConversationIds(new Set([this.currentConversationId])),this._state.currentConversationId=zt,this._currConversationModel.setConversation(e)}});d(this,"setCurrentConversationToWelcome",()=>{this.setCurrentConversation(),this._currConversationModel.setName("Welcome to Augment"),this._currConversationModel.addChatItem({chatItemType:Os.educateFeatures,request_id:crypto.randomUUID(),seen_state:Ps.seen})});d(this,"popCurrentConversation",async()=>{var e,s;const t=this.currentConversationId;t&&await this.deleteConversation(t,((e=this.nextConversation)==null?void 0:e.id)??((s=this.previousConversation)==null?void 0:s.id))});d(this,"setCurrentConversation",async(t,e=!0,s)=>{let i;this.flags.doUseNewDraftFunctionality?(t===void 0&&(t=zt),i=this._state.conversations[t]??ct.create({personaType:await this._currConversationModel.decidePersonaType(),rootTaskUuid:s==null?void 0:s.newTaskUuid}),t===zt&&(i.id=zt),s!=null&&s.newTaskUuid&&(i.rootTaskUuid=s.newTaskUuid)):t===void 0?(this.deleteInvalidConversations(ys(this._currConversationModel)?"agent":"chat"),i=ct.create({personaType:await this._currConversationModel.decidePersonaType()})):i=this._state.conversations[t]??ct.create({personaType:await this._currConversationModel.decidePersonaType(),rootTaskUuid:s==null?void 0:s.newTaskUuid});const r=this.conversations[this._currConversationModel.id]===void 0;this._currConversationModel.setConversation(i,!r,e),this._currConversationModel.recoverAllExchanges(),this._currConversationModel.resetTotalCharactersCache()});d(this,"saveConversation",(t,e)=>{this.updateChatState({conversations:{...this._state.conversations,[t.id]:t},currentConversationId:t.id}),e&&delete this._state.conversations[zt]});d(this,"isConversationShareable",t=>{var e;return((e=this._state.conversations[t])==null?void 0:e.isShareable)??!0});d(this,"setSortConversationsBy",t=>{this.sortConversationsBy.set(t),this.updateChatState({})});d(this,"getConversationUrl",async t=>{const e=this._state.conversations[t];if(e.lastUrl)return e.lastUrl;hn.set("copying");const s=e==null?void 0:e.chatHistory,i=s.reduce((a,l)=>(_s(l)&&a.push({request_id:l.request_id||"",request_message:l.request_message,response_text:l.response_text||""}),a),[]);if(i.length===0)throw new Error("No chat history to share");const r=ct.getDisplayName(e),o=await this.extensionClient.saveChat(t,i,r);if(o.data){let a=o.data.url;return this.updateChatState({conversations:{...this._state.conversations,[t]:{...e,lastUrl:a}}}),a}throw new Error("Failed to create URL")});d(this,"shareConversation",async t=>{if(t!==void 0)try{const e=await this.getConversationUrl(t);if(!e)return void hn.set("idle");navigator.clipboard.writeText(e),hn.set("copied")}catch{hn.set("failed")}});d(this,"deleteConversations",async(t,e=void 0,s=[],i)=>{const r=t.length+s.length;if(await this.extensionClient.openConfirmationModal({title:"Delete Conversation",message:`Are you sure you want to delete ${r>1?"these conversations":"this conversation"}?`,confirmButtonText:"Delete",cancelButtonText:"Cancel"})){if(t.length>0){const o=new Set(t);this.deleteConversationIds(o)}if(s.length>0&&i)for(const o of s)try{await i.deleteAgent(o,!0)}catch(a){console.error(`Failed to delete remote agent ${o}:`,a)}this.currentConversationId&&t.includes(this.currentConversationId)&&this.setCurrentConversation(e)}});d(this,"deleteConversation",async(t,e=void 0)=>{await this.deleteConversations([t],e)});d(this,"deleteConversationIds",async t=>{var s;const e=[];for(const i of t){const r=((s=this._state.conversations[i])==null?void 0:s.requestIds)??[];e.push(...r)}for(const i of Object.values(this._state.conversations))if(t.has(i.id)){for(const o of i.chatHistory)ce(o)&&this.deleteImagesInExchange(o);const r=i.draftExchange;r&&this.deleteImagesInExchange(r)}this.updateChatState({conversations:Object.fromEntries(Object.entries(this._state.conversations).filter(([i])=>!t.has(i)))}),this.extensionClient.clearMetadataFor({requestIds:e,conversationIds:Array.from(t)})});d(this,"deleteImagesInExchange",t=>{const e=new Set([...t.rich_text_json_repr?this.findImagesInJson(t.rich_text_json_repr):[],...t.structured_request_nodes?this.findImagesInStructuredRequest(t.structured_request_nodes):[]]);for(const s of e)this.deleteImage(s)});d(this,"findImagesInJson",t=>{const e=[],s=i=>{var r;if(i.type==="image"&&((r=i.attrs)!=null&&r.src))e.push(i.attrs.src);else if((i.type==="doc"||i.type==="paragraph")&&i.content)for(const o of i.content)s(o)};return s(t),e});d(this,"findImagesInStructuredRequest",t=>t.reduce((e,s)=>(s.type===xe.IMAGE_ID&&s.image_id_node&&e.push(s.image_id_node.image_id),e),[]));d(this,"toggleConversationPinned",t=>{const e=this._state.conversations[t],s={...e,isPinned:!e.isPinned};this.updateChatState({conversations:{...this._state.conversations,[t]:s}}),t===this.currentConversationId&&this._currConversationModel.toggleIsPinned()});d(this,"renameConversation",(t,e)=>{const s={...this._state.conversations[t],name:e};this.updateChatState({conversations:{...this._state.conversations,[t]:s}}),t===this.currentConversationId&&this._currConversationModel.setName(e)});d(this,"setDraftConversationType",t=>{const e=this._state.conversations[zt];if(!e)return;let s={...e.extraData};t==="chat"?s={...s,isAgentConversation:!1,isRemoteAgentConversation:!1}:t==="localAgent"?s={...s,isAgentConversation:!0,isRemoteAgentConversation:!1}:t==="remoteAgent"&&(s={...s,isAgentConversation:!0,isRemoteAgentConversation:!0});const i={...e,extraData:s};this.updateChatState({conversations:{...this._state.conversations,[zt]:i}}),this._currConversationModel.extraData=s});d(this,"smartPaste",(t,e,s,i)=>{const r=this._currConversationModel.historyTo(t,!0).filter(o=>_s(o)).map(o=>({request_message:o.request_message,response_text:o.response_text||"",request_id:o.request_id||""}));this.extensionClient.smartPaste({generatedCode:e,chatHistory:r,targetFile:s??void 0,options:i})});d(this,"saveImage",async t=>await this.extensionClient.saveImage(t));d(this,"deleteImage",async t=>await this.extensionClient.deleteImage(t));d(this,"renderImage",async t=>await this.extensionClient.loadImage(t));this._asyncMsgSender=t,this._host=e,this._specialContextInputModel=s,this.options=i,this._chatFlagsModel=new pa(i.initialFlags),this.extensionClient=new qo(this._host,this._asyncMsgSender,this._chatFlagsModel),this._currConversationModel=new ct(this.extensionClient,this._chatFlagsModel,this._specialContextInputModel,this.saveConversation),this.initialize(i.initialConversation);const r=this._state.isPanelCollapsed??this._state.isAgentEditsCollapsed??this._state.isTaskListCollapsed??!0;this.isPanelCollapsed=At(r),this.agentExecutionMode=At(this._state.agentExecutionMode??"manual"),this.sortConversationsBy=At(this._state.sortConversationsBy??"lastMessageTimestamp"),this.displayedAnnouncements=At(this._state.displayedAnnouncements??[]),this.onLoaded()}setChatModeModel(t){this._chatModeModel=t}get flags(){return this._chatFlagsModel}get specialContextInputModel(){return this._specialContextInputModel}get currentConversationId(){return this._state.currentConversationId}get currentConversationModel(){return this._currConversationModel}get conversations(){return this._state.conversations}orderedConversations(t,e="desc",s){const i=t||this._state.sortConversationsBy||"lastMessageTimestamp";let r=Object.values(this._state.conversations);return s&&(r=r.filter(s)),r.sort((o,a)=>{const l=ct.getTime(o,i).getTime(),c=ct.getTime(a,i).getTime();return e==="asc"?l-c:c-l})}get nextConversation(){if(!this.currentConversationId)return;const t=this.orderedConversations(),e=t.findIndex(s=>s.id===this.currentConversationId);return t.length>e+1?t[e+1]:void 0}get previousConversation(){if(!this.currentConversationId)return;const t=this.orderedConversations(),e=t.findIndex(s=>s.id===this.currentConversationId);return e>0?t[e-1]:void 0}get host(){return this._host}deleteInvalidConversations(t="all"){const e=Object.keys(this.conversations).filter(s=>{if(s===zt)return!1;const i=!ct.isValid(this.conversations[s]),r=ys(this.conversations[s]);return i&&(t==="agent"&&r||t==="chat"&&!r||t==="all")});e.length&&this.deleteConversationIds(new Set(e))}get lastMessageTimestamp(){const t=this.currentConversationModel.lastExchange;return t==null?void 0:t.timestamp}handleMessageFromExtension(t){const e=t.data;if(e.type===Et.newThread){if("data"in e&&e.data){const s=e.data.mode;(async()=>(await this.setCurrentConversation(),s&&this._chatModeModel?s.toLowerCase()==="agent"?await this._chatModeModel.setToAgent("manual"):s.toLowerCase()==="chat"?this._chatModeModel.setToChat():console.warn("Unknown chat mode:",s):s&&console.warn("ChatModeModel not available, cannot set mode:",s)))()}else this.setCurrentConversation();return!0}return!1}}d(Va,"NEW_AGENT_KEY",zt);const ps=typeof performance=="object"&&performance&&typeof performance.now=="function"?performance:Date,Ki=new Set,ai=typeof process=="object"&&process?process:{},oo=(n,t,e,s)=>{typeof ai.emitWarning=="function"?ai.emitWarning(n,t,e,s):console.error(`[${e}] ${t}: ${n}`)};let Mn=globalThis.AbortController,Ji=globalThis.AbortSignal;var qr;if(Mn===void 0){Ji=class{constructor(){d(this,"onabort");d(this,"_onabort",[]);d(this,"reason");d(this,"aborted",!1)}addEventListener(e,s){this._onabort.push(s)}},Mn=class{constructor(){d(this,"signal",new Ji);t()}abort(e){var s,i;if(!this.signal.aborted){this.signal.reason=e,this.signal.aborted=!0;for(const r of this.signal._onabort)r(e);(i=(s=this.signal).onabort)==null||i.call(s,e)}}};let n=((qr=ai.env)==null?void 0:qr.LRU_CACHE_IGNORE_AC_WARNING)!=="1";const t=()=>{n&&(n=!1,oo("AbortController is not defined. If using lru-cache in node 14, load an AbortController polyfill from the `node-abort-controller` package. A minimal polyfill is provided for use by LRUCache.fetch(), but it should not be relied upon in other contexts (eg, passing it to other APIs that use AbortController/AbortSignal might have undesirable effects). You may disable this with LRU_CACHE_IGNORE_AC_WARNING=1 in the env.","NO_ABORT_CONTROLLER","ENOTSUP",t))}}const qe=n=>n&&n===Math.floor(n)&&n>0&&isFinite(n),ao=n=>qe(n)?n<=Math.pow(2,8)?Uint8Array:n<=Math.pow(2,16)?Uint16Array:n<=Math.pow(2,32)?Uint32Array:n<=Number.MAX_SAFE_INTEGER?wn:null:null;class wn extends Array{constructor(t){super(t),this.fill(0)}}var xs;const ns=class ns{constructor(t,e){d(this,"heap");d(this,"length");if(!h(ns,xs))throw new TypeError("instantiate Stack using Stack.create(n)");this.heap=new e(t),this.length=0}static create(t){const e=ao(t);if(!e)return[];N(ns,xs,!0);const s=new ns(t,e);return N(ns,xs,!1),s}push(t){this.heap[this.length++]=t}pop(){return this.heap[--this.length]}};xs=new WeakMap,Z(ns,xs,!1);let li=ns;var Hr,Br,ge,Kt,me,be,$s,Ss,Tt,ve,_t,lt,q,Ht,Jt,Lt,Mt,_e,Dt,ye,we,te,ke,Ye,Bt,E,ui,ls,Pe,Ks,ee,lo,cs,Ts,Js,He,Be,di,kn,xn,at,hi,Ls,Ge,pi;const Di=class Di{constructor(t){Z(this,E);Z(this,ge);Z(this,Kt);Z(this,me);Z(this,be);Z(this,$s);Z(this,Ss);d(this,"ttl");d(this,"ttlResolution");d(this,"ttlAutopurge");d(this,"updateAgeOnGet");d(this,"updateAgeOnHas");d(this,"allowStale");d(this,"noDisposeOnSet");d(this,"noUpdateTTL");d(this,"maxEntrySize");d(this,"sizeCalculation");d(this,"noDeleteOnFetchRejection");d(this,"noDeleteOnStaleGet");d(this,"allowStaleOnFetchAbort");d(this,"allowStaleOnFetchRejection");d(this,"ignoreFetchAbort");Z(this,Tt);Z(this,ve);Z(this,_t);Z(this,lt);Z(this,q);Z(this,Ht);Z(this,Jt);Z(this,Lt);Z(this,Mt);Z(this,_e);Z(this,Dt);Z(this,ye);Z(this,we);Z(this,te);Z(this,ke);Z(this,Ye);Z(this,Bt);Z(this,ls,()=>{});Z(this,Pe,()=>{});Z(this,Ks,()=>{});Z(this,ee,()=>!1);Z(this,cs,t=>{});Z(this,Ts,(t,e,s)=>{});Z(this,Js,(t,e,s,i)=>{if(s||i)throw new TypeError("cannot set size without setting maxSize or maxEntrySize on cache");return 0});d(this,Hr,"LRUCache");const{max:e=0,ttl:s,ttlResolution:i=1,ttlAutopurge:r,updateAgeOnGet:o,updateAgeOnHas:a,allowStale:l,dispose:c,disposeAfter:u,noDisposeOnSet:p,noUpdateTTL:f,maxSize:g=0,maxEntrySize:m=0,sizeCalculation:v,fetchMethod:x,memoMethod:y,noDeleteOnFetchRejection:b,noDeleteOnStaleGet:$,allowStaleOnFetchRejection:P,allowStaleOnFetchAbort:S,ignoreFetchAbort:W}=t;if(e!==0&&!qe(e))throw new TypeError("max option must be a nonnegative integer");const G=e?ao(e):Array;if(!G)throw new Error("invalid max value: "+e);if(N(this,ge,e),N(this,Kt,g),this.maxEntrySize=m||h(this,Kt),this.sizeCalculation=v,this.sizeCalculation){if(!h(this,Kt)&&!this.maxEntrySize)throw new TypeError("cannot set sizeCalculation without setting maxSize or maxEntrySize");if(typeof this.sizeCalculation!="function")throw new TypeError("sizeCalculation set to non-function")}if(y!==void 0&&typeof y!="function")throw new TypeError("memoMethod must be a function if defined");if(N(this,Ss,y),x!==void 0&&typeof x!="function")throw new TypeError("fetchMethod must be a function if specified");if(N(this,$s,x),N(this,Ye,!!x),N(this,_t,new Map),N(this,lt,new Array(e).fill(void 0)),N(this,q,new Array(e).fill(void 0)),N(this,Ht,new G(e)),N(this,Jt,new G(e)),N(this,Lt,0),N(this,Mt,0),N(this,_e,li.create(e)),N(this,Tt,0),N(this,ve,0),typeof c=="function"&&N(this,me,c),typeof u=="function"?(N(this,be,u),N(this,Dt,[])):(N(this,be,void 0),N(this,Dt,void 0)),N(this,ke,!!h(this,me)),N(this,Bt,!!h(this,be)),this.noDisposeOnSet=!!p,this.noUpdateTTL=!!f,this.noDeleteOnFetchRejection=!!b,this.allowStaleOnFetchRejection=!!P,this.allowStaleOnFetchAbort=!!S,this.ignoreFetchAbort=!!W,this.maxEntrySize!==0){if(h(this,Kt)!==0&&!qe(h(this,Kt)))throw new TypeError("maxSize must be a positive integer if specified");if(!qe(this.maxEntrySize))throw new TypeError("maxEntrySize must be a positive integer if specified");A(this,E,lo).call(this)}if(this.allowStale=!!l,this.noDeleteOnStaleGet=!!$,this.updateAgeOnGet=!!o,this.updateAgeOnHas=!!a,this.ttlResolution=qe(i)||i===0?i:1,this.ttlAutopurge=!!r,this.ttl=s||0,this.ttl){if(!qe(this.ttl))throw new TypeError("ttl must be a positive integer if specified");A(this,E,ui).call(this)}if(h(this,ge)===0&&this.ttl===0&&h(this,Kt)===0)throw new TypeError("At least one of max, maxSize, or ttl is required");if(!this.ttlAutopurge&&!h(this,ge)&&!h(this,Kt)){const R="LRU_CACHE_UNBOUNDED";(z=>!Ki.has(z))(R)&&(Ki.add(R),oo("TTL caching without ttlAutopurge, max, or maxSize can result in unbounded memory consumption.","UnboundedCacheWarning",R,Di))}}static unsafeExposeInternals(t){return{starts:h(t,we),ttls:h(t,te),sizes:h(t,ye),keyMap:h(t,_t),keyList:h(t,lt),valList:h(t,q),next:h(t,Ht),prev:h(t,Jt),get head(){return h(t,Lt)},get tail(){return h(t,Mt)},free:h(t,_e),isBackgroundFetch:e=>{var s;return A(s=t,E,at).call(s,e)},backgroundFetch:(e,s,i,r)=>{var o;return A(o=t,E,xn).call(o,e,s,i,r)},moveToTail:e=>{var s;return A(s=t,E,Ls).call(s,e)},indexes:e=>{var s;return A(s=t,E,He).call(s,e)},rindexes:e=>{var s;return A(s=t,E,Be).call(s,e)},isStale:e=>{var s;return h(s=t,ee).call(s,e)}}}get max(){return h(this,ge)}get maxSize(){return h(this,Kt)}get calculatedSize(){return h(this,ve)}get size(){return h(this,Tt)}get fetchMethod(){return h(this,$s)}get memoMethod(){return h(this,Ss)}get dispose(){return h(this,me)}get disposeAfter(){return h(this,be)}getRemainingTTL(t){return h(this,_t).has(t)?1/0:0}*entries(){for(const t of A(this,E,He).call(this))h(this,q)[t]===void 0||h(this,lt)[t]===void 0||A(this,E,at).call(this,h(this,q)[t])||(yield[h(this,lt)[t],h(this,q)[t]])}*rentries(){for(const t of A(this,E,Be).call(this))h(this,q)[t]===void 0||h(this,lt)[t]===void 0||A(this,E,at).call(this,h(this,q)[t])||(yield[h(this,lt)[t],h(this,q)[t]])}*keys(){for(const t of A(this,E,He).call(this)){const e=h(this,lt)[t];e===void 0||A(this,E,at).call(this,h(this,q)[t])||(yield e)}}*rkeys(){for(const t of A(this,E,Be).call(this)){const e=h(this,lt)[t];e===void 0||A(this,E,at).call(this,h(this,q)[t])||(yield e)}}*values(){for(const t of A(this,E,He).call(this))h(this,q)[t]===void 0||A(this,E,at).call(this,h(this,q)[t])||(yield h(this,q)[t])}*rvalues(){for(const t of A(this,E,Be).call(this))h(this,q)[t]===void 0||A(this,E,at).call(this,h(this,q)[t])||(yield h(this,q)[t])}[(Br=Symbol.iterator,Hr=Symbol.toStringTag,Br)](){return this.entries()}find(t,e={}){for(const s of A(this,E,He).call(this)){const i=h(this,q)[s],r=A(this,E,at).call(this,i)?i.__staleWhileFetching:i;if(r!==void 0&&t(r,h(this,lt)[s],this))return this.get(h(this,lt)[s],e)}}forEach(t,e=this){for(const s of A(this,E,He).call(this)){const i=h(this,q)[s],r=A(this,E,at).call(this,i)?i.__staleWhileFetching:i;r!==void 0&&t.call(e,r,h(this,lt)[s],this)}}rforEach(t,e=this){for(const s of A(this,E,Be).call(this)){const i=h(this,q)[s],r=A(this,E,at).call(this,i)?i.__staleWhileFetching:i;r!==void 0&&t.call(e,r,h(this,lt)[s],this)}}purgeStale(){let t=!1;for(const e of A(this,E,Be).call(this,{allowStale:!0}))h(this,ee).call(this,e)&&(A(this,E,Ge).call(this,h(this,lt)[e],"expire"),t=!0);return t}info(t){const e=h(this,_t).get(t);if(e===void 0)return;const s=h(this,q)[e],i=A(this,E,at).call(this,s)?s.__staleWhileFetching:s;if(i===void 0)return;const r={value:i};if(h(this,te)&&h(this,we)){const o=h(this,te)[e],a=h(this,we)[e];if(o&&a){const l=o-(ps.now()-a);r.ttl=l,r.start=Date.now()}}return h(this,ye)&&(r.size=h(this,ye)[e]),r}dump(){const t=[];for(const e of A(this,E,He).call(this,{allowStale:!0})){const s=h(this,lt)[e],i=h(this,q)[e],r=A(this,E,at).call(this,i)?i.__staleWhileFetching:i;if(r===void 0||s===void 0)continue;const o={value:r};if(h(this,te)&&h(this,we)){o.ttl=h(this,te)[e];const a=ps.now()-h(this,we)[e];o.start=Math.floor(Date.now()-a)}h(this,ye)&&(o.size=h(this,ye)[e]),t.unshift([s,o])}return t}load(t){this.clear();for(const[e,s]of t){if(s.start){const i=Date.now()-s.start;s.start=ps.now()-i}this.set(e,s.value,s)}}set(t,e,s={}){var f,g,m,v,x;if(e===void 0)return this.delete(t),this;const{ttl:i=this.ttl,start:r,noDisposeOnSet:o=this.noDisposeOnSet,sizeCalculation:a=this.sizeCalculation,status:l}=s;let{noUpdateTTL:c=this.noUpdateTTL}=s;const u=h(this,Js).call(this,t,e,s.size||0,a);if(this.maxEntrySize&&u>this.maxEntrySize)return l&&(l.set="miss",l.maxEntrySizeExceeded=!0),A(this,E,Ge).call(this,t,"set"),this;let p=h(this,Tt)===0?void 0:h(this,_t).get(t);if(p===void 0)p=h(this,Tt)===0?h(this,Mt):h(this,_e).length!==0?h(this,_e).pop():h(this,Tt)===h(this,ge)?A(this,E,kn).call(this,!1):h(this,Tt),h(this,lt)[p]=t,h(this,q)[p]=e,h(this,_t).set(t,p),h(this,Ht)[h(this,Mt)]=p,h(this,Jt)[p]=h(this,Mt),N(this,Mt,p),ln(this,Tt)._++,h(this,Ts).call(this,p,u,l),l&&(l.set="add"),c=!1;else{A(this,E,Ls).call(this,p);const y=h(this,q)[p];if(e!==y){if(h(this,Ye)&&A(this,E,at).call(this,y)){y.__abortController.abort(new Error("replaced"));const{__staleWhileFetching:b}=y;b===void 0||o||(h(this,ke)&&((f=h(this,me))==null||f.call(this,b,t,"set")),h(this,Bt)&&((g=h(this,Dt))==null||g.push([b,t,"set"])))}else o||(h(this,ke)&&((m=h(this,me))==null||m.call(this,y,t,"set")),h(this,Bt)&&((v=h(this,Dt))==null||v.push([y,t,"set"])));if(h(this,cs).call(this,p),h(this,Ts).call(this,p,u,l),h(this,q)[p]=e,l){l.set="replace";const b=y&&A(this,E,at).call(this,y)?y.__staleWhileFetching:y;b!==void 0&&(l.oldValue=b)}}else l&&(l.set="update")}if(i===0||h(this,te)||A(this,E,ui).call(this),h(this,te)&&(c||h(this,Ks).call(this,p,i,r),l&&h(this,Pe).call(this,l,p)),!o&&h(this,Bt)&&h(this,Dt)){const y=h(this,Dt);let b;for(;b=y==null?void 0:y.shift();)(x=h(this,be))==null||x.call(this,...b)}return this}pop(){var t;try{for(;h(this,Tt);){const e=h(this,q)[h(this,Lt)];if(A(this,E,kn).call(this,!0),A(this,E,at).call(this,e)){if(e.__staleWhileFetching)return e.__staleWhileFetching}else if(e!==void 0)return e}}finally{if(h(this,Bt)&&h(this,Dt)){const e=h(this,Dt);let s;for(;s=e==null?void 0:e.shift();)(t=h(this,be))==null||t.call(this,...s)}}}has(t,e={}){const{updateAgeOnHas:s=this.updateAgeOnHas,status:i}=e,r=h(this,_t).get(t);if(r!==void 0){const o=h(this,q)[r];if(A(this,E,at).call(this,o)&&o.__staleWhileFetching===void 0)return!1;if(!h(this,ee).call(this,r))return s&&h(this,ls).call(this,r),i&&(i.has="hit",h(this,Pe).call(this,i,r)),!0;i&&(i.has="stale",h(this,Pe).call(this,i,r))}else i&&(i.has="miss");return!1}peek(t,e={}){const{allowStale:s=this.allowStale}=e,i=h(this,_t).get(t);if(i===void 0||!s&&h(this,ee).call(this,i))return;const r=h(this,q)[i];return A(this,E,at).call(this,r)?r.__staleWhileFetching:r}async fetch(t,e={}){const{allowStale:s=this.allowStale,updateAgeOnGet:i=this.updateAgeOnGet,noDeleteOnStaleGet:r=this.noDeleteOnStaleGet,ttl:o=this.ttl,noDisposeOnSet:a=this.noDisposeOnSet,size:l=0,sizeCalculation:c=this.sizeCalculation,noUpdateTTL:u=this.noUpdateTTL,noDeleteOnFetchRejection:p=this.noDeleteOnFetchRejection,allowStaleOnFetchRejection:f=this.allowStaleOnFetchRejection,ignoreFetchAbort:g=this.ignoreFetchAbort,allowStaleOnFetchAbort:m=this.allowStaleOnFetchAbort,context:v,forceRefresh:x=!1,status:y,signal:b}=e;if(!h(this,Ye))return y&&(y.fetch="get"),this.get(t,{allowStale:s,updateAgeOnGet:i,noDeleteOnStaleGet:r,status:y});const $={allowStale:s,updateAgeOnGet:i,noDeleteOnStaleGet:r,ttl:o,noDisposeOnSet:a,size:l,sizeCalculation:c,noUpdateTTL:u,noDeleteOnFetchRejection:p,allowStaleOnFetchRejection:f,allowStaleOnFetchAbort:m,ignoreFetchAbort:g,status:y,signal:b};let P=h(this,_t).get(t);if(P===void 0){y&&(y.fetch="miss");const S=A(this,E,xn).call(this,t,P,$,v);return S.__returned=S}{const S=h(this,q)[P];if(A(this,E,at).call(this,S)){const z=s&&S.__staleWhileFetching!==void 0;return y&&(y.fetch="inflight",z&&(y.returnedStale=!0)),z?S.__staleWhileFetching:S.__returned=S}const W=h(this,ee).call(this,P);if(!x&&!W)return y&&(y.fetch="hit"),A(this,E,Ls).call(this,P),i&&h(this,ls).call(this,P),y&&h(this,Pe).call(this,y,P),S;const G=A(this,E,xn).call(this,t,P,$,v),R=G.__staleWhileFetching!==void 0&&s;return y&&(y.fetch=W?"stale":"refresh",R&&W&&(y.returnedStale=!0)),R?G.__staleWhileFetching:G.__returned=G}}async forceFetch(t,e={}){const s=await this.fetch(t,e);if(s===void 0)throw new Error("fetch() returned undefined");return s}memo(t,e={}){const s=h(this,Ss);if(!s)throw new Error("no memoMethod provided to constructor");const{context:i,forceRefresh:r,...o}=e,a=this.get(t,o);if(!r&&a!==void 0)return a;const l=s(t,a,{options:o,context:i});return this.set(t,l,o),l}get(t,e={}){const{allowStale:s=this.allowStale,updateAgeOnGet:i=this.updateAgeOnGet,noDeleteOnStaleGet:r=this.noDeleteOnStaleGet,status:o}=e,a=h(this,_t).get(t);if(a!==void 0){const l=h(this,q)[a],c=A(this,E,at).call(this,l);return o&&h(this,Pe).call(this,o,a),h(this,ee).call(this,a)?(o&&(o.get="stale"),c?(o&&s&&l.__staleWhileFetching!==void 0&&(o.returnedStale=!0),s?l.__staleWhileFetching:void 0):(r||A(this,E,Ge).call(this,t,"expire"),o&&s&&(o.returnedStale=!0),s?l:void 0)):(o&&(o.get="hit"),c?l.__staleWhileFetching:(A(this,E,Ls).call(this,a),i&&h(this,ls).call(this,a),l))}o&&(o.get="miss")}delete(t){return A(this,E,Ge).call(this,t,"delete")}clear(){return A(this,E,pi).call(this,"delete")}};ge=new WeakMap,Kt=new WeakMap,me=new WeakMap,be=new WeakMap,$s=new WeakMap,Ss=new WeakMap,Tt=new WeakMap,ve=new WeakMap,_t=new WeakMap,lt=new WeakMap,q=new WeakMap,Ht=new WeakMap,Jt=new WeakMap,Lt=new WeakMap,Mt=new WeakMap,_e=new WeakMap,Dt=new WeakMap,ye=new WeakMap,we=new WeakMap,te=new WeakMap,ke=new WeakMap,Ye=new WeakMap,Bt=new WeakMap,E=new WeakSet,ui=function(){const t=new wn(h(this,ge)),e=new wn(h(this,ge));N(this,te,t),N(this,we,e),N(this,Ks,(r,o,a=ps.now())=>{if(e[r]=o!==0?a:0,t[r]=o,o!==0&&this.ttlAutopurge){const l=setTimeout(()=>{h(this,ee).call(this,r)&&A(this,E,Ge).call(this,h(this,lt)[r],"expire")},o+1);l.unref&&l.unref()}}),N(this,ls,r=>{e[r]=t[r]!==0?ps.now():0}),N(this,Pe,(r,o)=>{if(t[o]){const a=t[o],l=e[o];if(!a||!l)return;r.ttl=a,r.start=l,r.now=s||i();const c=r.now-l;r.remainingTTL=a-c}});let s=0;const i=()=>{const r=ps.now();if(this.ttlResolution>0){s=r;const o=setTimeout(()=>s=0,this.ttlResolution);o.unref&&o.unref()}return r};this.getRemainingTTL=r=>{const o=h(this,_t).get(r);if(o===void 0)return 0;const a=t[o],l=e[o];return!a||!l?1/0:a-((s||i())-l)},N(this,ee,r=>{const o=e[r],a=t[r];return!!a&&!!o&&(s||i())-o>a})},ls=new WeakMap,Pe=new WeakMap,Ks=new WeakMap,ee=new WeakMap,lo=function(){const t=new wn(h(this,ge));N(this,ve,0),N(this,ye,t),N(this,cs,e=>{N(this,ve,h(this,ve)-t[e]),t[e]=0}),N(this,Js,(e,s,i,r)=>{if(A(this,E,at).call(this,s))return 0;if(!qe(i)){if(!r)throw new TypeError("invalid size value (must be positive integer). When maxSize or maxEntrySize is used, sizeCalculation or size must be set.");if(typeof r!="function")throw new TypeError("sizeCalculation must be a function");if(i=r(s,e),!qe(i))throw new TypeError("sizeCalculation return invalid (expect positive integer)")}return i}),N(this,Ts,(e,s,i)=>{if(t[e]=s,h(this,Kt)){const r=h(this,Kt)-t[e];for(;h(this,ve)>r;)A(this,E,kn).call(this,!0)}N(this,ve,h(this,ve)+t[e]),i&&(i.entrySize=s,i.totalCalculatedSize=h(this,ve))})},cs=new WeakMap,Ts=new WeakMap,Js=new WeakMap,He=function*({allowStale:t=this.allowStale}={}){if(h(this,Tt))for(let e=h(this,Mt);A(this,E,di).call(this,e)&&(!t&&h(this,ee).call(this,e)||(yield e),e!==h(this,Lt));)e=h(this,Jt)[e]},Be=function*({allowStale:t=this.allowStale}={}){if(h(this,Tt))for(let e=h(this,Lt);A(this,E,di).call(this,e)&&(!t&&h(this,ee).call(this,e)||(yield e),e!==h(this,Mt));)e=h(this,Ht)[e]},di=function(t){return t!==void 0&&h(this,_t).get(h(this,lt)[t])===t},kn=function(t){var r,o;const e=h(this,Lt),s=h(this,lt)[e],i=h(this,q)[e];return h(this,Ye)&&A(this,E,at).call(this,i)?i.__abortController.abort(new Error("evicted")):(h(this,ke)||h(this,Bt))&&(h(this,ke)&&((r=h(this,me))==null||r.call(this,i,s,"evict")),h(this,Bt)&&((o=h(this,Dt))==null||o.push([i,s,"evict"]))),h(this,cs).call(this,e),t&&(h(this,lt)[e]=void 0,h(this,q)[e]=void 0,h(this,_e).push(e)),h(this,Tt)===1?(N(this,Lt,N(this,Mt,0)),h(this,_e).length=0):N(this,Lt,h(this,Ht)[e]),h(this,_t).delete(s),ln(this,Tt)._--,e},xn=function(t,e,s,i){const r=e===void 0?void 0:h(this,q)[e];if(A(this,E,at).call(this,r))return r;const o=new Mn,{signal:a}=s;a==null||a.addEventListener("abort",()=>o.abort(a.reason),{signal:o.signal});const l={signal:o.signal,options:s,context:i},c=(g,m=!1)=>{const{aborted:v}=o.signal,x=s.ignoreFetchAbort&&g!==void 0;if(s.status&&(v&&!m?(s.status.fetchAborted=!0,s.status.fetchError=o.signal.reason,x&&(s.status.fetchAbortIgnored=!0)):s.status.fetchResolved=!0),v&&!x&&!m)return u(o.signal.reason);const y=p;return h(this,q)[e]===p&&(g===void 0?y.__staleWhileFetching?h(this,q)[e]=y.__staleWhileFetching:A(this,E,Ge).call(this,t,"fetch"):(s.status&&(s.status.fetchUpdated=!0),this.set(t,g,l.options))),g},u=g=>{const{aborted:m}=o.signal,v=m&&s.allowStaleOnFetchAbort,x=v||s.allowStaleOnFetchRejection,y=x||s.noDeleteOnFetchRejection,b=p;if(h(this,q)[e]===p&&(!y||b.__staleWhileFetching===void 0?A(this,E,Ge).call(this,t,"fetch"):v||(h(this,q)[e]=b.__staleWhileFetching)),x)return s.status&&b.__staleWhileFetching!==void 0&&(s.status.returnedStale=!0),b.__staleWhileFetching;if(b.__returned===b)throw g};s.status&&(s.status.fetchDispatched=!0);const p=new Promise((g,m)=>{var x;const v=(x=h(this,$s))==null?void 0:x.call(this,t,r,l);v&&v instanceof Promise&&v.then(y=>g(y===void 0?void 0:y),m),o.signal.addEventListener("abort",()=>{s.ignoreFetchAbort&&!s.allowStaleOnFetchAbort||(g(void 0),s.allowStaleOnFetchAbort&&(g=y=>c(y,!0)))})}).then(c,g=>(s.status&&(s.status.fetchRejected=!0,s.status.fetchError=g),u(g))),f=Object.assign(p,{__abortController:o,__staleWhileFetching:r,__returned:void 0});return e===void 0?(this.set(t,f,{...l.options,status:void 0}),e=h(this,_t).get(t)):h(this,q)[e]=f,f},at=function(t){if(!h(this,Ye))return!1;const e=t;return!!e&&e instanceof Promise&&e.hasOwnProperty("__staleWhileFetching")&&e.__abortController instanceof Mn},hi=function(t,e){h(this,Jt)[e]=t,h(this,Ht)[t]=e},Ls=function(t){t!==h(this,Mt)&&(t===h(this,Lt)?N(this,Lt,h(this,Ht)[t]):A(this,E,hi).call(this,h(this,Jt)[t],h(this,Ht)[t]),A(this,E,hi).call(this,h(this,Mt),t),N(this,Mt,t))},Ge=function(t,e){var i,r,o,a;let s=!1;if(h(this,Tt)!==0){const l=h(this,_t).get(t);if(l!==void 0)if(s=!0,h(this,Tt)===1)A(this,E,pi).call(this,e);else{h(this,cs).call(this,l);const c=h(this,q)[l];if(A(this,E,at).call(this,c)?c.__abortController.abort(new Error("deleted")):(h(this,ke)||h(this,Bt))&&(h(this,ke)&&((i=h(this,me))==null||i.call(this,c,t,e)),h(this,Bt)&&((r=h(this,Dt))==null||r.push([c,t,e]))),h(this,_t).delete(t),h(this,lt)[l]=void 0,h(this,q)[l]=void 0,l===h(this,Mt))N(this,Mt,h(this,Jt)[l]);else if(l===h(this,Lt))N(this,Lt,h(this,Ht)[l]);else{const u=h(this,Jt)[l];h(this,Ht)[u]=h(this,Ht)[l];const p=h(this,Ht)[l];h(this,Jt)[p]=h(this,Jt)[l]}ln(this,Tt)._--,h(this,_e).push(l)}}if(h(this,Bt)&&((o=h(this,Dt))!=null&&o.length)){const l=h(this,Dt);let c;for(;c=l==null?void 0:l.shift();)(a=h(this,be))==null||a.call(this,...c)}return s},pi=function(t){var e,s,i;for(const r of A(this,E,Be).call(this,{allowStale:!0})){const o=h(this,q)[r];if(A(this,E,at).call(this,o))o.__abortController.abort(new Error("deleted"));else{const a=h(this,lt)[r];h(this,ke)&&((e=h(this,me))==null||e.call(this,o,a,t)),h(this,Bt)&&((s=h(this,Dt))==null||s.push([o,a,t]))}}if(h(this,_t).clear(),h(this,q).fill(void 0),h(this,lt).fill(void 0),h(this,te)&&h(this,we)&&(h(this,te).fill(0),h(this,we).fill(0)),h(this,ye)&&h(this,ye).fill(0),N(this,Lt,0),N(this,Mt,0),h(this,_e).length=0,N(this,ve,0),N(this,Tt,0),h(this,Bt)&&h(this,Dt)){const r=h(this,Dt);let o;for(;o=r==null?void 0:r.shift();)(i=h(this,be))==null||i.call(this,...o)}};let ci=Di;class od{constructor(){d(this,"_syncStatus",{status:Xo.done,foldersProgress:[]});d(this,"_syncEnabledState",zi.initializing);d(this,"_workspaceGuidelines",[]);d(this,"_openUserGuidelinesInput",!1);d(this,"_userGuidelines");d(this,"_contextStore",new Xa);d(this,"_prevOpenFiles",[]);d(this,"_disableContext",!1);d(this,"_enableAgentMemories",!1);d(this,"subscribers",new Set);d(this,"subscribe",t=>(this.subscribers.add(t),t(this),()=>{this.subscribers.delete(t)}));d(this,"handleMessageFromExtension",t=>{const e=t.data;switch(e.type){case Et.sourceFoldersUpdated:this.onSourceFoldersUpdated(e.data.sourceFolders);break;case Et.sourceFoldersSyncStatus:this.onSyncStatusUpdated(e.data);break;case Et.fileRangesSelected:this.updateSelections(e.data);break;case Et.currentlyOpenFiles:this.setCurrentlyOpenFiles(e.data);break;case Et.syncEnabledState:this.onSyncEnabledStateUpdate(e.data);break;case Et.updateGuidelinesState:this.onGuidelinesStateUpdate(e.data);break;default:return!1}return!0});d(this,"onSourceFoldersUpdated",t=>{const e=this.sourceFolders;t=this.updateSourceFoldersWithGuidelines(t),this._contextStore.update(t.map(s=>({sourceFolder:s,status:Ct.active,label:s.folderRoot,showWarning:s.guidelinesOverLimit,id:s.folderRoot+String(s.guidelinesEnabled)+String(s.guidelinesOverLimit)})),e,s=>s.id),this.notifySubscribers()});d(this,"onSyncStatusUpdated",t=>{this._syncStatus=t,this.notifySubscribers()});d(this,"disableContext",()=>{this._disableContext=!0,this.notifySubscribers()});d(this,"enableContext",()=>{this._disableContext=!1,this.notifySubscribers()});d(this,"addFile",t=>{this.addFiles([t])});d(this,"addFiles",t=>{this.updateFiles(t,[])});d(this,"removeFile",t=>{this.removeFiles([t])});d(this,"removeFiles",t=>{this.updateFiles([],t)});d(this,"updateItems",(t,e)=>{this.updateItemsInplace(t,e),this.notifySubscribers()});d(this,"updateItemsInplace",(t,e)=>{this._contextStore.update(t,e,s=>s.id)});d(this,"updateFiles",(t,e)=>{const s=o=>({file:o,...jn(o)}),i=t.map(s),r=e.map(s);this._contextStore.update(i,r,o=>o.id),this.notifySubscribers()});d(this,"updateRules",(t,e)=>{const s=o=>({rule:o,...Wo(o)}),i=t.map(s),r=e.map(s);this._contextStore.update(i,r,o=>o.id),this.notifySubscribers()});d(this,"enableAgentMemories",()=>{this._enableAgentMemories=!0,this.notifySubscribers()});d(this,"disableAgentMemories",()=>{this._enableAgentMemories=!1,this.notifySubscribers()});d(this,"setCurrentlyOpenFiles",t=>{const e=t.map(i=>({recentFile:i,...jn(i)})),s=this._prevOpenFiles;this._prevOpenFiles=e,this._contextStore.update(e,s,i=>i.id),s.forEach(i=>{const r=this._contextStore.peekKey(i.id);r!=null&&r.recentFile&&(r.file=r.recentFile,delete r.recentFile)}),e.forEach(i=>{const r=this._contextStore.peekKey(i.id);r!=null&&r.file&&(r.recentFile=r.file,delete r.file)}),this.notifySubscribers()});d(this,"onSyncEnabledStateUpdate",t=>{this._syncEnabledState=t,this.notifySubscribers()});d(this,"updateUserGuidelines",t=>{const e=this.userGuidelines,s={userGuidelines:t,label:"User Guidelines",id:"userGuidelines",status:Ct.active,referenceCount:1,showWarning:t.overLimit};this._contextStore.update([s],e,i=>{var r,o;return i.id+String((r=i.userGuidelines)==null?void 0:r.enabled)+String((o=i.userGuidelines)==null?void 0:o.overLimit)}),this.notifySubscribers()});d(this,"onGuidelinesStateUpdate",t=>{this._userGuidelines=t.userGuidelines,this._workspaceGuidelines=t.workspaceGuidelines??[];const e=t.userGuidelines;e&&this.updateUserGuidelines(e),this.onSourceFoldersUpdated(this.sourceFolders.map(s=>s.sourceFolder))});d(this,"updateSourceFoldersWithGuidelines",t=>t.map(e=>{const s=this._workspaceGuidelines.find(i=>i.workspaceFolder===e.folderRoot);return{...e,guidelinesEnabled:(s==null?void 0:s.enabled)??!1,guidelinesOverLimit:(s==null?void 0:s.overLimit)??!1,guidelinesLengthLimit:(s==null?void 0:s.lengthLimit)??2e3}}));d(this,"toggleStatus",t=>{this._contextStore.toggleStatus(t.id),this.notifySubscribers()});d(this,"updateExternalSources",(t,e)=>{this._contextStore.update(t,e,s=>s.id),this.notifySubscribers()});d(this,"clearFiles",()=>{this._contextStore.update([],this.files,t=>t.id),this.notifySubscribers()});d(this,"updateSelections",t=>{const e=this._contextStore.values.filter(Pi);this._contextStore.update(t.map(s=>({selection:s,...jn(s)})),e,s=>s.id),this.notifySubscribers()});d(this,"maybeHandleDelete",({editor:t})=>{if(t.state.selection.empty&&t.state.selection.$anchor.pos===1&&this.recentActiveItems.length>0){const e=this.recentActiveItems[0];return this.markInactive(e),!0}return!1});d(this,"markInactive",t=>{this.markItemsInactive([t])});d(this,"markItemsInactive",t=>{t.forEach(e=>{this._contextStore.setStatus(e.id,Ct.inactive)}),this.notifySubscribers()});d(this,"markAllInactive",()=>{this.markItemsInactive(this.recentActiveItems)});d(this,"markActive",t=>{this.markItemsActive([t])});d(this,"markItemsActive",t=>{t.forEach(e=>{this._contextStore.setStatus(e.id,Ct.active)}),this.notifySubscribers()});d(this,"markAllActive",()=>{this.markItemsActive(this.recentInactiveItems)});d(this,"unpin",t=>{this._contextStore.unpin(t.id),this.notifySubscribers()});d(this,"togglePinned",t=>{this._contextStore.togglePinned(t.id),this.notifySubscribers()});d(this,"notifySubscribers",()=>{this.subscribers.forEach(t=>t(this))});this.clearFiles()}get files(){return this._disableContext?[]:this._contextStore.values.filter(t=>Ho(t)&&!Ni(t))}get recentFiles(){return this._disableContext?[]:this._contextStore.values.filter(Ni)}get userGuidelinesText(){var t;return((t=this._userGuidelines)==null?void 0:t.contents)??""}get selections(){return this._disableContext?[]:this._contextStore.values.filter(Pi)}get folders(){return this._disableContext?[]:this._contextStore.values.filter(Bo)}get sourceFolders(){return this._disableContext?[]:this._contextStore.values.filter(Oi)}get externalSources(){return this._disableContext?[]:this._contextStore.values.filter(Go)}get userGuidelines(){return this._contextStore.values.filter(Li)}get agentMemories(){return[{...jo,status:this._enableAgentMemories?Ct.active:Ct.inactive,referenceCount:1}]}get rules(){return this._contextStore.values.filter(t=>Ui(t))}get activeFiles(){return this._disableContext?[]:this.files.filter(t=>t.status===Ct.active)}get activeRecentFiles(){return this._disableContext?[]:this.recentFiles.filter(t=>t.status===Ct.active)}get activeExternalSources(){return this._disableContext?[]:this.externalSources.filter(t=>t.status===Ct.active)}get activeSelections(){return this._disableContext?[]:this.selections.filter(t=>t.status===Ct.active)}get activeSourceFolders(){return this._disableContext?[]:this.sourceFolders.filter(t=>t.status===Ct.active)}get activeRules(){return this._disableContext?[]:this.rules.filter(t=>t.status===Ct.active)}get syncStatus(){return this._syncStatus.status}get syncEnabledState(){return this._syncEnabledState}get syncProgress(){var l;if(this.syncEnabledState===zi.disabled||!this._syncStatus.foldersProgress)return;const t=this._syncStatus.foldersProgress.filter(c=>c.progress!==void 0);if(t.length===0)return;const e=t.reduce((c,u)=>{var p;return c+(((p=u==null?void 0:u.progress)==null?void 0:p.trackedFiles)??0)},0),s=t.reduce((c,u)=>{var p;return c+(((p=u==null?void 0:u.progress)==null?void 0:p.backlogSize)??0)},0),i=Math.max(e,0),r=Math.min(Math.max(s,0),i),o=i-r,a=[];for(const c of t)(l=c==null?void 0:c.progress)!=null&&l.newlyTracked&&a.push(c.folderRoot);return{status:this._syncStatus.status,totalFiles:i,syncedCount:o,backlogSize:r,newlyTrackedFolders:a}}get contextCounts(){return this._contextStore.values.length??0}get chatActiveContext(){return{userSpecifiedFiles:[...this.activeFiles.map(t=>({rootPath:t.file.repoRoot,relPath:t.file.pathName}))],ruleFiles:this.activeRules.map(t=>t.rule),recentFiles:this.activeRecentFiles.map(t=>({rootPath:t.recentFile.repoRoot,relPath:t.recentFile.pathName})),externalSources:this.activeExternalSources.map(t=>t.externalSource),selections:this.activeSelections.map(t=>t.selection),sourceFolders:this.activeSourceFolders.map(t=>({rootPath:t.sourceFolder.folderRoot,relPath:""}))}}get recentItems(){return this._disableContext?this.userGuidelines:[...this._contextStore.values.filter(t=>!(Oi(t)||Li(t)||Xr(t)||Ui(t))),...this.sourceFolders,...this.rules,...this.userGuidelines,...this.agentMemories]}get recentActiveItems(){return this.recentItems.filter(t=>t.status===Ct.active)}get recentInactiveItems(){return this.recentItems.filter(t=>t.status===Ct.inactive)}get isContextDisabled(){return this._disableContext}}class Xa{constructor(){d(this,"_cache",new ci({max:1e3}));d(this,"peekKey",t=>this._cache.get(t,{updateAgeOnGet:!1}));d(this,"clear",()=>{this._cache.clear()});d(this,"update",(t,e,s)=>{t.forEach(i=>this.addInPlace(i,s)),e.forEach(i=>this.removeInPlace(i,s))});d(this,"removeFromStore",(t,e)=>{const s=e(t);this._cache.delete(s)});d(this,"addInPlace",(t,e)=>{const s=e(t),i=t.referenceCount??1,r=this._cache.get(s),o=t.status??(r==null?void 0:r.status)??Ct.active;r?(r.referenceCount+=i,r.status=o,r.pinned=t.pinned??r.pinned,r.showWarning=t.showWarning??r.showWarning):this._cache.set(s,{...t,pinned:void 0,referenceCount:i,status:o})});d(this,"removeInPlace",(t,e)=>{const s=e(t),i=this._cache.get(s);i&&(i.referenceCount-=1,i.referenceCount===0&&this._cache.delete(s))});d(this,"setStatus",(t,e)=>{const s=this._cache.get(t);s&&(s.status=e)});d(this,"togglePinned",t=>{const e=this._cache.peek(t);e&&(e.pinned?this.unpin(t):this.pin(t))});d(this,"pin",t=>{const e=this._cache.peek(t);e&&!e.pinned&&(e.pinned=!0,e.referenceCount+=1)});d(this,"unpin",t=>{const e=this._cache.peek(t);e&&e.pinned&&(e.pinned=!1,e.referenceCount-=1,e.referenceCount===0&&this._cache.delete(t))});d(this,"toggleStatus",t=>{const e=this._cache.get(t);e&&(e.status=e.status===Ct.active?Ct.inactive:Ct.active)})}get store(){return Object.fromEntries(this._cache.entries())}get values(){return[...this._cache.values()]}}class Za{constructor(t){d(this,"_githubTimeoutMs",3e4);this._asyncMsgSender=t}async listUserRepos(){try{const t=await this._asyncMsgSender.send({type:Et.listGithubReposForAuthenticatedUserRequest},this._githubTimeoutMs);return{repos:t.data.repos,error:t.data.error,isDevDeploy:t.data.isDevDeploy}}catch(t){return console.error("Failed to list user repos:",t),{repos:[],error:`Error: ${t instanceof Error?t.message:String(t)}`}}}async listRepoBranches(t,e){try{const s=await this._asyncMsgSender.send({type:Et.listGithubRepoBranchesRequest,data:{repo:t,page:e}},this._githubTimeoutMs);return{branches:s.data.branches,hasNextPage:s.data.hasNextPage,nextPage:s.data.nextPage,error:s.data.error,isDevDeploy:s.data.isDevDeploy}}catch(s){return console.error("Failed to list repo branches:",s),{branches:[],hasNextPage:!1,nextPage:0,error:`Error: ${s instanceof Error?s.message:String(s)}`}}}async getGithubRepo(t){try{const e=await this._asyncMsgSender.send({type:Et.getGithubRepoRequest,data:{repo:t}},this._githubTimeoutMs);return{repo:e.data.repo,error:e.data.error,isDevDeploy:e.data.isDevDeploy}}catch(e){return console.error("Failed to get GitHub repo:",e),{repo:t,error:`Error: ${e instanceof Error?e.message:String(e)}`}}}async getCurrentLocalBranch(){try{return(await this._asyncMsgSender.send({type:Et.getCurrentLocalBranchRequest},1e4)).data.branch}catch(t){return void console.error("Failed to get current local branch:",t)}}async listBranches(t=""){try{return{branches:(await this._asyncMsgSender.send({type:Et.getGitBranchesRequest,data:{prefix:t}},1e4)).data.branches}}catch(e){return console.error("Failed to fetch branches:",e),{branches:[]}}}async getWorkspaceDiff(t){try{return(await this._asyncMsgSender.send({type:Et.getWorkspaceDiffRequest,data:{branchName:t}},1e4)).data.diff}catch(e){return console.error("Failed to get workspace diff:",e),""}}async getRemoteUrl(){try{return{remoteUrl:(await this._asyncMsgSender.send({type:Et.getRemoteUrlRequest},1e4)).data.remoteUrl}}catch(t){return console.error("Failed to get remote url:",t),{remoteUrl:"",error:`Error: ${t instanceof Error?t.message:String(t)}`}}}async fetch(){try{await this._asyncMsgSender.send({type:Et.gitFetchRequest},1e4)}catch(t){console.error("Failed to fetch remote branch:",t)}}async isGitRepository(){try{return(await this._asyncMsgSender.send({type:Et.isGitRepositoryRequest},1e4)).data.isGitRepository}catch(t){return console.error("Failed to check if is git repository:",t),!1}}async isGithubAuthenticated(){try{return(await this._asyncMsgSender.send({type:Et.isGithubAuthenticatedRequest},this._githubTimeoutMs)).data.isAuthenticated}catch(t){return console.error("Failed to check GitHub authentication status:",t),!1}}async authenticateGithub(){try{const t=await this._asyncMsgSender.send({type:Et.authenticateGithubRequest},this._githubTimeoutMs);return{success:t.data.success,message:t.data.message}}catch(t){return console.error("Failed to authenticate with GitHub:",t),{success:!1,message:`Error: ${t instanceof Error?t.message:String(t)}`}}}async revokeGithubAccess(){try{const t=await this._asyncMsgSender.send({type:Et.revokeGithubAccessRequest},1e4);return{success:t.data.success,message:t.data.message}}catch(t){return console.error("Failed to revoke GitHub access:",t),{success:!1,message:`Error: ${t instanceof Error?t.message:String(t)}`}}}}d(Za,"key","gitReferenceModel");const Qa={doHideStatusBars:!1,doHideSlashActions:!1,doHideAtMentions:!1,doHideNewThreadButton:!1,doHideMultimodalActions:!1,doHideContextBar:!1,doShowTurnSelector:!1},Ka={doHideStatusBars:!0,doHideSlashActions:!0,doHideAtMentions:!0,doHideNewThreadButton:!0,doHideMultimodalActions:!0,doHideContextBar:!0,doShowTurnSelector:!0},ad="selectedTurnIndex";function ld(n,t){let e=Qa;return n!=null&&n.isActive&&(e=Ka,t&&(e.doHideAtMentions=!1)),e}function Ja(n){let t,e;return{c(){t=ut("svg"),e=ut("path"),w(e,"fill-rule","evenodd"),w(e,"clip-rule","evenodd"),w(e,"d","M3.13523 8.84197C3.3241 9.04343 3.64052 9.05363 3.84197 8.86477L7.5 5.43536L11.158 8.86477C11.3595 9.05363 11.6759 9.04343 11.8648 8.84197C12.0536 8.64051 12.0434 8.32409 11.842 8.13523L7.84197 4.38523C7.64964 4.20492 7.35036 4.20492 7.15803 4.38523L3.15803 8.13523C2.95657 8.32409 2.94637 8.64051 3.13523 8.84197Z"),w(e,"fill","currentColor"),w(t,"width","15"),w(t,"height","15"),w(t,"viewBox","0 0 15 15"),w(t,"fill","none"),w(t,"xmlns","http://www.w3.org/2000/svg")},m(s,i){F(s,t,i),V(t,e)},p:O,i:O,o:O,d(s){s&&C(t)}}}class cd extends tt{constructor(t){super(),et(this,t,null,Ja,st,{})}}function tl(n){let t,e,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},n[0]],i={};for(let r=0;r<s.length;r+=1)i=j(i,s[r]);return{c(){t=ut("svg"),e=new Se(!0),this.h()},l(r){t=Te(r,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var o=Ce(t);e=Ee(o,!0),o.forEach(C),this.h()},h(){e.a=null,bt(t,i)},m(r,o){Ie(r,t,o),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M248.4 84.3c1.6-2.7 4.5-4.3 7.6-4.3s6 1.6 7.6 4.3L461.9 410c1.4 2.3 2.1 4.9 2.1 7.5 0 8-6.5 14.5-14.5 14.5h-387c-8 0-14.5-6.5-14.5-14.5 0-2.7.7-5.3 2.1-7.5zm-41-25L9.1 385c-6 9.8-9.1 21-9.1 32.5C0 452 28 480 62.5 480h387c34.5 0 62.5-28 62.5-62.5 0-11.5-3.2-22.7-9.1-32.5L304.6 59.3C294.3 42.4 275.9 32 256 32s-38.3 10.4-48.6 27.3M288 368a32 32 0 1 0-64 0 32 32 0 1 0 64 0m-8-184c0-13.3-10.7-24-24-24s-24 10.7-24 24v96c0 13.3 10.7 24 24 24s24-10.7 24-24z"/>',t)},p(r,[o]){bt(t,i=Xt(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&o&&r[0]]))},i:O,o:O,d(r){r&&C(t)}}}function el(n,t,e){return n.$$set=s=>{e(0,t=j(j({},t),ht(s)))},[t=ht(t)]}class ud extends tt{constructor(t){super(),et(this,t,el,tl,st,{})}}function sl(n){let t,e;return{c(){t=ut("svg"),e=ut("path"),w(e,"fill-rule","evenodd"),w(e,"clip-rule","evenodd"),w(e,"d","M11.4669 3.72684C11.7558 3.91574 11.8369 4.30308 11.648 4.59198L7.39799 11.092C7.29783 11.2452 7.13556 11.3467 6.95402 11.3699C6.77247 11.3931 6.58989 11.3355 6.45446 11.2124L3.70446 8.71241C3.44905 8.48022 3.43023 8.08494 3.66242 7.82953C3.89461 7.57412 4.28989 7.55529 4.5453 7.78749L6.75292 9.79441L10.6018 3.90792C10.7907 3.61902 11.178 3.53795 11.4669 3.72684Z"),w(e,"fill","currentColor"),w(t,"width","15"),w(t,"height","15"),w(t,"viewBox","0 0 15 15"),w(t,"fill","none"),w(t,"xmlns","http://www.w3.org/2000/svg")},m(s,i){F(s,t,i),V(t,e)},p:O,i:O,o:O,d(s){s&&C(t)}}}class dd extends tt{constructor(t){super(),et(this,t,null,sl,st,{})}}function tr(n){return n.replace(/\.git$/,"")}function hd(n){if(!n)return"";const t=n.match(/github\.com\/([^/]+)\/([^/]+)/);if(t)return t[2].replace(".git","");const e=n.split("/").filter(Boolean);return(e.length>0?e[e.length-1]:"").replace(".git","")}function pd(n){if(!n)return"";const t=n.match(/github\.com\/([^/]+)\/([^/]+)/);if(t)return`${t[1]}/${t[2].replace(".git","")}`;const e=n.split("/").filter(Boolean);return e.length>1?`${e[e.length-2]}/${e[e.length-1].replace(".git","")}`:e.length>0?e[e.length-1].replace(".git",""):""}function fd(n){return yn(n,t=>e=>{if(!t)return!0;const s=tr(t),i=tr(function(r){var o,a,l;return((l=(a=(o=r.workspace_setup)==null?void 0:o.starting_files)==null?void 0:a.github_commit_ref)==null?void 0:l.repository_url)||""}(e));return!!i&&!!s&&i!==s})}function nl(n){let t,e,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},n[0]],i={};for(let r=0;r<s.length;r+=1)i=j(i,s[r]);return{c(){t=ut("svg"),e=new Se(!0),this.h()},l(r){t=Te(r,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var o=Ce(t);e=Ee(o,!0),o.forEach(C),this.h()},h(){e.a=null,bt(t,i)},m(r,o){Ie(r,t,o),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M448 128c0 53-43 96-96 96-28.9 0-54.8-12.8-72.4-33l-89.7 44.9c1.4 6.5 2.1 13.2 2.1 20.1s-.7 13.6-2.1 20.1l89.7 44.9c17.6-20.2 43.5-33 72.4-33 53 0 96 43 96 96s-43 96-96 96-96-43-96-96c0-6.9.7-13.6 2.1-20.1L168.4 319c-17.6 20.2-43.5 33-72.4 33-53 0-96-43-96-96s43-96 96-96c28.9 0 54.8 12.8 72.4 33l89.7-44.9c-1.4-6.5-2.1-13.2-2.1-20.1 0-53 43-96 96-96s96 43 96 96M96 304a48 48 0 1 0 0-96 48 48 0 1 0 0 96m304-176a48 48 0 1 0-96 0 48 48 0 1 0 96 0m-48 304a48 48 0 1 0 0-96 48 48 0 1 0 0 96"/>',t)},p(r,[o]){bt(t,i=Xt(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},1&o&&r[0]]))},i:O,o:O,d(r){r&&C(t)}}}function il(n,t,e){return n.$$set=s=>{e(0,t=j(j({},t),ht(s)))},[t=ht(t)]}class gd extends tt{constructor(t){super(),et(this,t,il,nl,st,{})}}function rl(n){let t,e;return{c(){t=ut("svg"),e=ut("path"),w(e,"fill-rule","evenodd"),w(e,"clip-rule","evenodd"),w(e,"d","M1.43555 8.19985C1.43555 4.29832 4.59837 1.1355 8.4999 1.1355C12.4014 1.1355 15.5642 4.29832 15.5642 8.19985C15.5642 12.1013 12.4014 15.2642 8.4999 15.2642C4.59837 15.2642 1.43555 12.1013 1.43555 8.19985ZM8.4999 2.14883C5.15802 2.14883 2.44889 4.85797 2.44889 8.19985C2.44889 11.5417 5.15802 14.2509 8.4999 14.2509C11.8418 14.2509 14.5509 11.5417 14.5509 8.19985C14.5509 4.85797 11.8418 2.14883 8.4999 2.14883ZM11.0105 5.68952C11.2187 5.8978 11.2187 6.23549 11.0105 6.44377L9.25427 8.19997L11.0105 9.95619C11.2187 10.1645 11.2187 10.5022 11.0105 10.7104C10.8022 10.9187 10.4645 10.9187 10.2562 10.7104L8.50002 8.95422L6.74382 10.7104C6.53554 10.9187 6.19784 10.9187 5.98957 10.7104C5.78129 10.5022 5.78129 10.1645 5.98957 9.95619L7.74578 8.19997L5.98957 6.44377C5.78129 6.23549 5.78129 5.8978 5.98957 5.68952C6.19784 5.48124 6.53554 5.48124 6.74382 5.68952L8.50002 7.44573L10.2562 5.68952C10.4645 5.48124 10.8022 5.48124 11.0105 5.68952Z"),w(e,"fill","currentColor"),w(t,"width","17"),w(t,"height","17"),w(t,"viewBox","0 0 17 17"),w(t,"fill","none"),w(t,"xmlns","http://www.w3.org/2000/svg")},m(s,i){F(s,t,i),V(t,e)},p:O,i:O,o:O,d(s){s&&C(t)}}}class ol extends tt{constructor(t){super(),et(this,t,null,rl,st,{})}}function al(n){let t,e;return{c(){t=ut("svg"),e=ut("path"),w(e,"fill-rule","evenodd"),w(e,"clip-rule","evenodd"),w(e,"d","M7.49991 0.877075C3.84222 0.877075 0.877075 3.84222 0.877075 7.49991C0.877075 11.1576 3.84222 14.1227 7.49991 14.1227C11.1576 14.1227 14.1227 11.1576 14.1227 7.49991C14.1227 3.84222 11.1576 0.877075 7.49991 0.877075ZM3.85768 3.15057C4.84311 2.32448 6.11342 1.82708 7.49991 1.82708C10.6329 1.82708 13.1727 4.36689 13.1727 7.49991C13.1727 8.88638 12.6753 10.1567 11.8492 11.1421L3.85768 3.15057ZM3.15057 3.85768C2.32448 4.84311 1.82708 6.11342 1.82708 7.49991C1.82708 10.6329 4.36689 13.1727 7.49991 13.1727C8.88638 13.1727 10.1567 12.6753 11.1421 11.8492L3.15057 3.85768Z"),w(e,"fill","currentColor"),w(t,"width","15"),w(t,"height","15"),w(t,"viewBox","0 0 15 15"),w(t,"fill","none"),w(t,"xmlns","http://www.w3.org/2000/svg")},m(s,i){F(s,t,i),V(t,e)},p:O,i:O,o:O,d(s){s&&C(t)}}}class ll extends tt{constructor(t){super(),et(this,t,null,al,st,{})}}function cl(n){let t,e,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},n[0]],i={};for(let r=0;r<s.length;r+=1)i=j(i,s[r]);return{c(){t=ut("svg"),e=new Se(!0),this.h()},l(r){t=Te(r,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var o=Ce(t);e=Ee(o,!0),o.forEach(C),this.h()},h(){e.a=null,bt(t,i)},m(r,o){Ie(r,t,o),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M256 48a208 208 0 1 1 0 416 208 208 0 1 1 0-416m0 464a256 256 0 1 0 0-512 256 256 0 1 0 0 512m113-303c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-111 111-47-47c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9l64 64c9.4 9.4 24.6 9.4 33.9 0z"/>',t)},p(r,[o]){bt(t,i=Xt(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&o&&r[0]]))},i:O,o:O,d(r){r&&C(t)}}}function ul(n,t,e){return n.$$set=s=>{e(0,t=j(j({},t),ht(s)))},[t=ht(t)]}class co extends tt{constructor(t){super(),et(this,t,ul,cl,st,{})}}function dl(n){let t,e;return t=new ll({}),{c(){I(t.$$.fragment)},m(s,i){M(t,s,i),e=!0},i(s){e||(_(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){D(t,s)}}}function hl(n){let t,e;return t=new ol({}),{c(){I(t.$$.fragment)},m(s,i){M(t,s,i),e=!0},i(s){e||(_(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){D(t,s)}}}function pl(n){let t,e;return t=new co({}),{c(){I(t.$$.fragment)},m(s,i){M(t,s,i),e=!0},i(s){e||(_(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){D(t,s)}}}function fl(n){let t,e;return t=new Yr({props:{size:1}}),{c(){I(t.$$.fragment)},m(s,i){M(t,s,i),e=!0},i(s){e||(_(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){D(t,s)}}}function gl(n){let t,e,s,i,r;const o=[fl,pl,hl,dl],a=[];function l(c,u){return c[0]==="loading"?0:c[0]==="success"?1:c[0]==="error"?2:c[0]==="skipped"?3:-1}return~(e=l(n))&&(s=a[e]=o[e](n)),{c(){t=J("div"),s&&s.c(),w(t,"class",i="c-setup-script-command-status c-setup-script-command-status--"+n[0]+" svelte-1azgu93")},m(c,u){F(c,t,u),~e&&a[e].m(t,null),r=!0},p(c,[u]){let p=e;e=l(c),e!==p&&(s&&(xt(),k(a[p],1,1,()=>{a[p]=null}),$t()),~e?(s=a[e],s||(s=a[e]=o[e](c),s.c()),_(s,1),s.m(t,null)):s=null),(!r||1&u&&i!==(i="c-setup-script-command-status c-setup-script-command-status--"+c[0]+" svelte-1azgu93"))&&w(t,"class",i)},i(c){r||(_(s),r=!0)},o(c){k(s),r=!1},d(c){c&&C(t),~e&&a[e].d()}}}function ml(n,t,e){let{commandResult:s}=t;return n.$$set=i=>{"commandResult"in i&&e(0,s=i.commandResult)},[s]}class md extends tt{constructor(t){super(),et(this,t,ml,gl,st,{commandResult:0})}}function er(n){let t,e;return t=new bs({props:{class:"edit-item__added-lines",size:1,$$slots:{default:[bl]},$$scope:{ctx:n}}}),{c(){I(t.$$.fragment)},m(s,i){M(t,s,i),e=!0},p(s,i){const r={};5&i&&(r.$$scope={dirty:i,ctx:s}),t.$set(r)},i(s){e||(_(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){D(t,s)}}}function bl(n){let t,e;return{c(){t=Gt("+"),e=Gt(n[0])},m(s,i){F(s,t,i),F(s,e,i)},p(s,i){1&i&&en(e,s[0])},d(s){s&&(C(t),C(e))}}}function sr(n){let t,e;return t=new bs({props:{class:"edit-item__removed-lines",size:1,$$slots:{default:[vl]},$$scope:{ctx:n}}}),{c(){I(t.$$.fragment)},m(s,i){M(t,s,i),e=!0},p(s,i){const r={};6&i&&(r.$$scope={dirty:i,ctx:s}),t.$set(r)},i(s){e||(_(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){D(t,s)}}}function vl(n){let t,e;return{c(){t=Gt("-"),e=Gt(n[1])},m(s,i){F(s,t,i),F(s,e,i)},p(s,i){2&i&&en(e,s[1])},d(s){s&&(C(t),C(e))}}}function _l(n){let t,e,s,i=n[0]>0&&er(n),r=n[1]>0&&sr(n);return{c(){t=J("div"),i&&i.c(),e=pt(),r&&r.c(),w(t,"class","edit-item__changes svelte-1k8sltp")},m(o,a){F(o,t,a),i&&i.m(t,null),V(t,e),r&&r.m(t,null),s=!0},p(o,[a]){o[0]>0?i?(i.p(o,a),1&a&&_(i,1)):(i=er(o),i.c(),_(i,1),i.m(t,e)):i&&(xt(),k(i,1,1,()=>{i=null}),$t()),o[1]>0?r?(r.p(o,a),2&a&&_(r,1)):(r=sr(o),r.c(),_(r,1),r.m(t,null)):r&&(xt(),k(r,1,1,()=>{r=null}),$t())},i(o){s||(_(i),_(r),s=!0)},o(o){k(i),k(r),s=!1},d(o){o&&C(t),i&&i.d(),r&&r.d()}}}function yl(n,t,e){let{totalAddedLines:s=0}=t,{totalRemovedLines:i=0}=t;return n.$$set=r=>{"totalAddedLines"in r&&e(0,s=r.totalAddedLines),"totalRemovedLines"in r&&e(1,i=r.totalRemovedLines)},[s,i]}class bd extends tt{constructor(t){super(),et(this,t,yl,_l,st,{totalAddedLines:0,totalRemovedLines:1})}}function wl(n){let t,e;return{c(){t=ut("svg"),e=ut("path"),w(e,"fill-rule","evenodd"),w(e,"clip-rule","evenodd"),w(e,"d","M3.5 2.82672C3.5 2.55058 3.72386 2.32672 4 2.32672H9.79289L12.5 5.03383V12.8267C12.5 13.1028 12.2761 13.3267 12 13.3267H4C3.72386 13.3267 3.5 13.1028 3.5 12.8267V2.82672ZM4 1.32672C3.17157 1.32672 2.5 1.99829 2.5 2.82672V12.8267C2.5 13.6551 3.17157 14.3267 4 14.3267H12C12.8284 14.3267 13.5 13.6551 13.5 12.8267V4.93027C13.5 4.73136 13.421 4.5406 13.2803 4.39994L10.3535 1.47317C10.2598 1.3794 10.1326 1.32672 10 1.32672H4ZM10.25 6.6595C10.5261 6.6595 10.75 6.43564 10.75 6.1595C10.75 5.88336 10.5261 5.6595 10.25 5.6595H8.49996L8.49996 3.9095C8.49996 3.6334 8.2761 3.4095 7.99996 3.4095C7.72382 3.4095 7.49996 3.6334 7.49996 3.9095V5.6595H5.74996C5.47386 5.6595 5.24996 5.88336 5.24996 6.1595C5.24996 6.43564 5.47386 6.6595 5.74996 6.6595L7.49996 6.6595V8.4095C7.49996 8.68564 7.72382 8.9095 7.99996 8.9095C8.2761 8.9095 8.49996 8.68564 8.49996 8.4095V6.6595H10.25ZM10.4999 11.4188C10.4999 11.695 10.2761 11.9188 9.99993 11.9188H5.99993C5.72379 11.9188 5.49993 11.695 5.49993 11.4188C5.49993 11.1427 5.72379 10.9188 5.99993 10.9188H9.99993C10.2761 10.9188 10.4999 11.1427 10.4999 11.4188Z"),w(e,"fill","currentColor"),w(t,"width","15"),w(t,"height","15"),w(t,"viewBox","0 0 15 15"),w(t,"fill","none"),w(t,"xmlns","http://www.w3.org/2000/svg")},m(s,i){F(s,t,i),V(t,e)},p:O,i:O,o:O,d(s){s&&C(t)}}}class vd extends tt{constructor(t){super(),et(this,t,null,wl,st,{})}}function kl(n){let t,e,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},n[0]],i={};for(let r=0;r<s.length;r+=1)i=j(i,s[r]);return{c(){t=ut("svg"),e=new Se(!0),this.h()},l(r){t=Te(r,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var o=Ce(t);e=Ee(o,!0),o.forEach(C),this.h()},h(){e.a=null,bt(t,i)},m(r,o){Ie(r,t,o),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M153.8 72.1c8.9-9.9 8.1-25-1.8-33.9s-25-8.1-33.9 1.8l-55 61.1L41 79c-9.4-9.3-24.6-9.3-34 0s-9.4 24.6 0 33.9l40 40c4.7 4.7 11 7.2 17.6 7s12.8-3 17.2-7.9l72-80zm0 160c8.9-9.9 8.1-25-1.8-33.9s-25-8.1-33.9 1.8l-55 61.1L41 239c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9l40 40c4.7 4.7 11 7.2 17.6 7s12.8-3 17.2-7.9l72-80zM216 120h272c13.3 0 24-10.7 24-24s-10.7-24-24-24H216c-13.3 0-24 10.7-24 24s10.7 24 24 24m-24 136c0 13.3 10.7 24 24 24h272c13.3 0 24-10.7 24-24s-10.7-24-24-24H216c-13.3 0-24 10.7-24 24m-32 160c0 13.3 10.7 24 24 24h304c13.3 0 24-10.7 24-24s-10.7-24-24-24H184c-13.3 0-24 10.7-24 24m-64 0a32 32 0 1 0-64 0 32 32 0 1 0 64 0"/>',t)},p(r,[o]){bt(t,i=Xt(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&o&&r[0]]))},i:O,o:O,d(r){r&&C(t)}}}function xl(n,t,e){return n.$$set=s=>{e(0,t=j(j({},t),ht(s)))},[t=ht(t)]}class _d extends tt{constructor(t){super(),et(this,t,xl,kl,st,{})}}var B=(n=>(n.NOT_STARTED="NOT_STARTED",n.IN_PROGRESS="IN_PROGRESS",n.CANCELLED="CANCELLED",n.COMPLETE="COMPLETE",n))(B||{}),kt=(n=>(n.USER="USER",n.AGENT="AGENT",n))(kt||{});const uo={[B.NOT_STARTED]:"[ ]",[B.IN_PROGRESS]:"[/]",[B.COMPLETE]:"[x]",[B.CANCELLED]:"[-]"};function ho(n,t){if(n.uuid===t)return n;if(n.subTasksData)for(const e of n.subTasksData){const s=ho(e,t);if(s)return s}}function Dn(n,t=!1){return po(n,t).join(`
`)}function po(n,t=!1){const e=`${uo[n.state]} UUID:${n.uuid} NAME:${n.name} DESCRIPTION:${n.description}`;return t||!n.subTasksData||n.subTasksData.length===0?[e]:[e,...(n.subTasksData||[]).map(s=>po(s,t).map(i=>`-${i}`)).flat()]}function fi(n,t){var s;const e=(s=n.subTasksData)==null?void 0:s.map(i=>fi(i,t));return{...n,uuid:t!=null&&t.keepUuid?n.uuid:crypto.randomUUID(),subTasks:(e==null?void 0:e.map(i=>i.uuid))||[],subTasksData:e}}function nr(n){if(!n.trim())throw new Error("Empty markdown");const t=n.split(`
`);function e(){for(;t.length>0;){const o=t.shift(),a=$l(o);try{return{task:fo(o,a),level:a}}catch{}}}const s=e();if(!s)throw new Error("No root task found");const i=[s.task];let r;for(;r=e();){const o=i[r.level-1];if(!o)throw new Error(`Invalid markdown: level ${r.level+1} has no parent
Line: ${r.task.name} is missing a parent
Current tasks: 
${Dn(s.task)}`);o.subTasksData&&o.subTasks||(o.subTasks=[],o.subTasksData=[]),o.subTasksData.push(r.task),o.subTasks.push(r.task.uuid),i[r.level]=r.task,i.splice(r.level+1)}return s.task}function $l(n){const t=n.trimStart().match(/^-+/);return t?t[0].length:0}function fo(n,t){const e=n.trimStart().substring(t),s=e.match(/^\[([ x\-/?])\]/);if(!s)throw new Error(`Invalid task line: ${n} (missing state)`);const i=s[1],r=Object.entries(uo).reduce((u,[p,f])=>(u[f.substring(1,2)]=p,u),{})[i]||B.NOT_STARTED,o=e.match(/(?:uuid|UUID):([^]*?)(?=(?:name|NAME):)(?:name|NAME):([^]*?)(?=(?:description|DESCRIPTION):)(?:description|DESCRIPTION):(.*)$/i);if(!o){const u=/\b(?:uuid|UUID):/i.test(e),p=/\b(?:name|NAME):/i.test(e),f=/\b(?:description|DESCRIPTION):/i.test(e);if(!u||!p||!f)throw new Error(`Invalid task line: ${n} (missing required fields)`);const g=e.toLowerCase().indexOf("uuid:"),m=e.toLowerCase().indexOf("name:"),v=e.toLowerCase().indexOf("description:");throw g<m&&m<v?new Error(`Invalid task line: ${n} (invalid format)`):new Error(`Invalid task line: ${n} (incorrect field order)`)}let a=o[1].trim();const l=o[2].trim(),c=o[3].trim();if(!a||!l)throw new Error(`Invalid task line: ${n} (missing required fields)`);return a==="NEW_UUID"&&(a=crypto.randomUUID()),{uuid:a,name:l,description:c,state:r,subTasks:[],lastUpdated:Date.now(),lastUpdatedBy:kt.USER}}const Is=n=>({uuid:crypto.randomUUID(),name:"New Task",description:"New task description",state:B.NOT_STARTED,subTasks:[],lastUpdated:Date.now(),lastUpdatedBy:kt.USER,...n}),ir=Is({name:"Task 1.1",description:"This is the first sub task",state:B.IN_PROGRESS}),rr=Is({name:"Task 1.2.1",description:"This is a nested sub task, child of Task 1.2",state:B.NOT_STARTED}),or=Is({name:"Task 1.2.2",description:"This is another nested sub task, child of Task 1.2",state:B.IN_PROGRESS}),ar=Is({name:"Task 1.2",description:"This is the second sub task",state:B.COMPLETE,subTasks:[rr.uuid,or.uuid],subTasksData:[rr,or]}),lr=Is({name:"Task 1.3",description:"This is the third sub task",state:B.CANCELLED}),Sl=Dn(Is({name:"Task 1",description:"This is the first task",state:B.NOT_STARTED,subTasks:[ir.uuid,ar.uuid,lr.uuid],subTasksData:[ir,ar,lr]}));function go(n){const t=n.split(`
`);let e=null;const s={created:[],updated:[],deleted:[]};for(const i of t){const r=i.trim();if(r!=="## Created Tasks")if(r!=="## Updated Tasks")if(r!=="## Deleted Tasks"){if(e&&(r.startsWith("[ ]")||r.startsWith("[/]")||r.startsWith("[x]")||r.startsWith("[-]")))try{const o=fo(r,0);o&&s[e].push(o)}catch{}}else e="deleted";else e="updated";else e="created"}return s}function yd(n){const t=n.match(/Created: (\d+), Updated: (\d+), Deleted: (\d+)/);if(t)return{created:parseInt(t[1],10),updated:parseInt(t[2],10),deleted:parseInt(t[3],10)};const e=go(mo(n));return{created:e.created.length,updated:e.updated.length,deleted:e.deleted.length}}function mo(n){const t=n.indexOf("# Task Changes");if(t===-1)return"";const e=n.substring(t),s=[`
New and Updated Tasks:`,`
Remember:`,`

---`];let i=e.length;for(const a of s){const l=e.indexOf(a);l!==-1&&l<i&&(i=l)}const r=e.substring(0,i),o=r.indexOf(`
`);return o===-1?"":r.substring(o+1).trim()}function wd(n){return go(mo(n))}const Vs=class Vs{constructor(t,e,s,i){d(this,"_disposables",[]);d(this,"rootTaskUuid");d(this,"_rootTask",At(void 0));d(this,"rootTask",Hn(this._rootTask));d(this,"_isEnhancing",At(!1));d(this,"isEnhancing",Hn(this._isEnhancing));d(this,"_isImportingExporting",At(!1));d(this,"isImportingExporting",Hn(this._isImportingExporting));d(this,"canShowTaskList");d(this,"_refreshInterval");d(this,"_backlinks",At({}));d(this,"uuidToTask",yn(this._rootTask,t=>{const e=new Map;if(!t)return e;const s={},i=t?[t]:[];for(;i.length>0;){const r=i.pop();if(e.set(r.uuid,r),r.subTasksData){i.push(...r.subTasksData);for(const o of r.subTasks)s[o]=r.uuid}}return this._backlinks.set(s),e}));this._chatModel=t,this._extensionClient=e,this._conversationModel=s,this._agentConversationModel=i,this.rootTaskUuid=yn(this._conversationModel,r=>r.rootTaskUuid),this._disposables.push({dispose:this.rootTaskUuid.subscribe(async r=>{r&&(this._extensionClient.setCurrentRootTaskUuid(r),await this.refreshTasks())})}),this._disposables.push({dispose:this._conversationModel.onNewConversation(async()=>{await this._maybeInitializeConversationRootTask()})}),this.canShowTaskList=yn([this._chatModel.flags,this._agentConversationModel.isCurrConversationAgentic],([r,o])=>r.enableTaskList&&o),this._disposables.push({dispose:this.canShowTaskList.subscribe(r=>{r?this._startRefreshInterval():this._stopRefreshInterval()})}),this._maybeInitializeConversationRootTask()}static createReadOnlyStore(t,e=!0){const s=At(t),i=new Map;if(t){const r=[t];for(;r.length>0;){const o=r.pop();i.set(o.uuid,o),o.subTasksData&&r.push(...o.subTasksData)}}return{rootTaskUuid:At(t==null?void 0:t.uuid),rootTask:s,isEnhancing:At(e),canShowTaskList:At(!0),uuidToTask:At(i),createTask:()=>Promise.resolve(""),updateTask:()=>Promise.resolve(),getHydratedTask:()=>Promise.resolve(void 0),cloneHydratedTask:()=>Promise.resolve(void 0),refreshTasks:()=>Promise.resolve(),updateTaskListStatuses:()=>Promise.resolve(),syncTaskListWithConversation:()=>Promise.resolve(),deleteTask:()=>Promise.resolve(),cleanupEmptyAndCancelledTasks:()=>Promise.resolve(),getParentTask:()=>{},addNewTaskAfter:()=>Promise.resolve(void 0),saveHydratedTask:()=>Promise.resolve(),runHydratedTask:()=>Promise.resolve(),dispose:()=>{},handleMessageFromExtension:()=>!1,runAllTasks:()=>Promise.resolve(),exportTask:()=>Promise.resolve(),exportTasksToMarkdown:()=>Promise.resolve(),importTasksFromMarkdown:()=>Promise.resolve(),isImportingExporting:At(!1),setEnhancing:()=>{}}}dispose(){for(const t of this._disposables)t.dispose();this._disposables=[]}handleMessageFromExtension(t){return!1}async createTask(t,e,s){const i=await this._extensionClient.createTask(t,e,s);return await this.refreshTasks(),i}async updateTask(t,e,s){await this._extensionClient.updateTask(t,e,s),await this.refreshTasks()}async getHydratedTask(t){return this._extensionClient.getHydratedTask(t)}async refreshTasks(){const t=yt(this.rootTaskUuid);if(t){const e=await this._extensionClient.getHydratedTask(t);this._rootTask.set(e)}else this._rootTask.set(void 0)}async syncTaskListWithConversation(t){await this._updateTaskList(t,"Update the task list to reflect the current state of the conversation. Add any new tasks that have been created, update the status of existing tasks, and remove any tasks that are no longer relevant. The updated task list should reflect the current state of the conversation. If the tasks are not detailed enough, please add new tasks to fill in the steps you think are necessary, provide more details by adding more information to the description, or add sub-tasks",["You should add new tasks if any tasks are missing.","You should update the details of tasks if their details are outdated.","You should update the status of tasks if their status is outdated.","You should remove any tasks that are no longer relevant by not including them in the task list."])}async updateTaskListStatuses(t){await this._updateTaskList(t,"Update the status of each task in the list to reflect the current state of the conversation.",["You may update the status of tasks if necessary.","Do not add any new tasks.","Do not remove any tasks.","Do not update the details of any tasks."])}async _maybeInitializeConversationRootTask(){const t=yt(this.rootTaskUuid);if(t)return t;const e=yt(this._conversationModel),s=e.id,i=`Conversation: ${e.name||"New Chat"}`,r=`Root task for conversation ${s}`,o=await this._extensionClient.createTask(i,r);return this._conversationModel.rootTaskUuid=o,this._extensionClient.setCurrentRootTaskUuid(o),o}async _updateTaskList(t,e="",s=[]){if(!yt(this._isEnhancing))try{this._isEnhancing.set(!0);const i=yt(this._rootTask);if(!i)return;const r=ho(i,t);if(!r)return;const o=Dn(r),a=e+`
Follow these rules when updating the task list:
`+(s==null?void 0:s.join(`
`))+`
Maintain the hierarchical structure, given by the \`Example task list structure\`, with proper indentation. If a task is new, give it a UUID of "NEW_UUID". Always structure each task with [ ] for not started, [/] for in progress, [x] for completed, and [-] for cancelled. 
Example task list structure: 
`+Sl+`

Current working task list - This is ACTUAL working task list to use, read from, and modify:
`+o+`

Only output the updated markdown without any additional explanation. Do not include any sentences before or after the markdown, ONLY the markdown itself. Do not use a tool call, just return the markdown in plaintext, without tools, or anything else. Just plaintext markdown.`,{responseText:l}=await this._conversationModel.sendSilentExchange({request_message:a,disableSelectedCodeDetails:!0});console.log("Updating task list for conversation",yt(this._conversationModel).id),console.log({instructions:a,currentTaskListMarkdown:o,enhancedTaskList:l});const c=nr(l);c.uuid=r.uuid;const{created:u,updated:p,deleted:f}=await this._extensionClient.updateHydratedTask(c,kt.AGENT);console.log("Task tree update results:",{created:u,updated:p,deleted:f})}finally{this._isEnhancing.set(!1),await this.refreshTasks()}}getParentTask(t){const e=yt(this._backlinks)[t];if(e)return yt(this.uuidToTask).get(e)}async addNewTaskAfter(t,e){const s=this.getParentTask(t);if(!s)return;const i=s.subTasks.indexOf(t);if(i===-1)return;const r=await this.cloneHydratedTask(e);return r?(s.subTasks.splice(i+1,0,r.uuid),await this.saveHydratedTask(s),r):void 0}async deleteTask(t){var i;const e=yt(this._backlinks)[t];if(!e)return;const s=await this.getHydratedTask(e);s&&(s.subTasks=s.subTasks.filter(r=>r!==t),s.subTasksData=(i=s.subTasksData)==null?void 0:i.filter(r=>r.uuid!==t),await this.updateTask(s.uuid,{subTasks:s.subTasks},kt.USER))}async cleanupEmptyAndCancelledTasks(){const t=yt(this.rootTask);if(!t)return;const e=function(s){const i=[],r=[s];for(;r.length>0;){const o=r.pop();i.push(o),o.subTasksData&&r.push(...o.subTasksData)}return i}(t).filter(s=>s.uuid!==t.uuid).filter(s=>{if(s.state===B.CANCELLED)return!0;const i=!s.name||s.name.trim()==="",r=!s.description||s.description.trim()==="";return i&&r});for(const s of e.reverse())await this.deleteTask(s.uuid);await this.refreshTasks()}async saveHydratedTask(t){await this._extensionClient.updateHydratedTask(t,kt.USER),await this.refreshTasks()}async cloneHydratedTask(t){const e=fi(t),s=await this.createTask(e.name,e.description);if(s)return e.uuid=s,await this._extensionClient.updateHydratedTask(e,kt.USER),await this.getHydratedTask(s)}async runAllTasks(){const t=yt(this._rootTask);t&&this.runHydratedTask(t,{message:"Run all tasks for the task list: "})}async runHydratedTask(t,e){const s=this._chatModel.currentConversationId;if(await this._agentConversationModel.interruptAgent(),s!==this._chatModel.currentConversationId)return;if(e!=null&&e.newConversation){const r=await this.cloneHydratedTask(t);if(!r)return;const o=await this.createTask(t.name,t.description);if(await this.saveHydratedTask({uuid:o,name:t.name,description:t.description,state:B.NOT_STARTED,subTasks:[r.uuid],subTasksData:[r],lastUpdated:Date.now(),lastUpdatedBy:kt.USER}),s!==this._chatModel.currentConversationId)return;await this._chatModel.setCurrentConversation(void 0,!0,{newTaskUuid:o})}const i={type:"doc",content:[{type:"paragraph",content:[{type:"text",text:(e==null?void 0:e.message)??"Please shift focus to work on task: "},{type:"mention",attrs:{id:`UUID:${t.uuid} NAME:${t.name} DESCRIPTION:${t.description}`,label:t.name,data:{...t,id:`UUID:${t.uuid} NAME:${t.name} DESCRIPTION:${t.description}`,label:t.name}}}]}]};await this._chatModel.currentConversationModel.sendExchange({request_message:"",rich_text_json_repr:i,structured_request_nodes:this._chatModel.currentConversationModel.createStructuredRequestNodes(i),status:Ft.draft,model_id:this._chatModel.currentConversationModel.selectedModelId??void 0}),await this.refreshTasks()}_startRefreshInterval(){this._refreshInterval=setInterval(async()=>{await this.refreshTasks()},Vs.REFRESH_INTERVAL_MS)}setEnhancing(t){this._isEnhancing.set(t)}_stopRefreshInterval(){this._refreshInterval&&(clearInterval(this._refreshInterval),this._refreshInterval=void 0)}async exportTask(t,e){try{this._isImportingExporting.set(!0);const s=(e==null?void 0:e.format)||"markdown";let i,r,o;if(s!=="markdown")throw new Error(`Unsupported export format: ${s}`);i=Dn(t,e==null?void 0:e.shallow),r="md",(e==null?void 0:e.fileName)?o=e.fileName:o=`${((e==null?void 0:e.baseName)||t.name||"Task").replace(/[/:*?"<>|\s]/g,"_")}_${new Date().toISOString().replace(/[:.]/g,"-").slice(0,19)}.${r}`,this._extensionClient.createFile(i,o)}catch(s){console.error("Error exporting task:",s)}finally{this._isImportingExporting.set(!1)}}async exportTasksToMarkdown(){const t=yt(this._rootTask);if(!t)return;const e=yt(this._conversationModel).name||"Tasks";await this.exportTask(t,{baseName:e,format:"markdown"})}async importTasksFromMarkdown(){try{this._isImportingExporting.set(!0);const t=document.createElement("input");t.type="file",t.accept=".md,text/markdown",t.style.display="none";const e=new Promise(i=>{t.onchange=()=>{i(t.files?t.files[0]:null)},t.oncancel=()=>{i(null)}});document.body.appendChild(t),t.click();const s=await e;if(document.body.removeChild(t),s){const i=await s.text();await this._processImportedFileContent(i)}}catch(t){console.error("Error importing tasks from markdown:",t)}finally{this._isImportingExporting.set(!1)}}async _processImportedFileContent(t){try{if(!t.trim())return void console.error("Empty file content");const e=nr(t);if(!e)return void console.error("Failed to parse task tree from markdown");const s=yt(this._rootTask);if(!s)return void console.error("No root task found");const i=(e.subTasksData||[]).map(u=>fi(u,{keepUuid:!1})),r=s.subTasksData||[],o=s.subTasks||[],a=[...r,...i],l=[...o,...i.map(u=>u.uuid)],c={...s,subTasks:l,subTasksData:a};await this._extensionClient.updateHydratedTask(c,kt.USER),await this.refreshTasks()}catch(e){console.error("Error processing imported file content:",e)}}};d(Vs,"key","currentConversationTaskStore"),d(Vs,"REFRESH_INTERVAL_MS",2e3);let gi=Vs;function Tl(n){let t,e,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 128 512"},n[0]],i={};for(let r=0;r<s.length;r+=1)i=j(i,s[r]);return{c(){t=ut("svg"),e=new Se(!0),this.h()},l(r){t=Te(r,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var o=Ce(t);e=Ee(o,!0),o.forEach(C),this.h()},h(){e.a=null,bt(t,i)},m(r,o){Ie(r,t,o),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M64 368a48 48 0 1 0 0 96 48 48 0 1 0 0-96m0-160a48 48 0 1 0 0 96 48 48 0 1 0 0-96m48-112a48 48 0 1 0-96 0 48 48 0 1 0 96 0"/>',t)},p(r,[o]){bt(t,i=Xt(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 128 512"},1&o&&r[0]]))},i:O,o:O,d(r){r&&C(t)}}}function Cl(n,t,e){return n.$$set=s=>{e(0,t=j(j({},t),ht(s)))},[t=ht(t)]}class El extends tt{constructor(t){super(),et(this,t,Cl,Tl,st,{})}}function Il(n){let t,e,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 576 512"},n[0]],i={};for(let r=0;r<s.length;r+=1)i=j(i,s[r]);return{c(){t=ut("svg"),e=new Se(!0),this.h()},l(r){t=Te(r,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var o=Ce(t);e=Ee(o,!0),o.forEach(C),this.h()},h(){e.a=null,bt(t,i)},m(r,o){Ie(r,t,o),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M336 448c0 8.8-7.2 16-16 16H64c-8.8 0-16-7.2-16-16V64c0-8.8 7.2-16 16-16h160v80c0 17.7 14.3 32 32 32h80v96h48V154.5c0-17-6.7-33.3-18.7-45.3l-90.6-90.5C262.7 6.7 246.5 0 229.5 0H64C28.7 0 0 28.7 0 64v384c0 35.3 28.7 64 64 64h256c35.3 0 64-28.7 64-64v-80h-48zm153-233c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9l39 39-278.1.1c-13.3 0-24 10.7-24 24s10.7 24 24 24h278.1l-39 39c-9.4 9.4-9.4 24.6 0 33.9s24.6 9.4 33.9 0l80-80c9.4-9.4 9.4-24.6 0-33.9z"/>',t)},p(r,[o]){bt(t,i=Xt(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 576 512"},1&o&&r[0]]))},i:O,o:O,d(r){r&&C(t)}}}function Ml(n,t,e){return n.$$set=s=>{e(0,t=j(j({},t),ht(s)))},[t=ht(t)]}class bo extends tt{constructor(t){super(),et(this,t,Ml,Il,st,{})}}function Dl(n){let t,e,s;const i=n[5].default,r=is(i,n,n[4],null);return{c(){t=J("div"),r&&r.c(),w(t,"class",e=Ri(n[1])+" svelte-1uzel5q"),w(t,"role","status"),w(t,"aria-label",n[0])},m(o,a){F(o,t,a),r&&r.m(t,null),s=!0},p(o,[a]){r&&r.p&&(!s||16&a)&&rs(r,i,o,o[4],s?as(i,o[4],a,null):os(o[4]),null),(!s||2&a&&e!==(e=Ri(o[1])+" svelte-1uzel5q"))&&w(t,"class",e),(!s||1&a)&&w(t,"aria-label",o[0])},i(o){s||(_(r,o),s=!0)},o(o){k(r,o),s=!1},d(o){o&&C(t),r&&r.d(o)}}}function Al(n,t,e){let s,{$$slots:i={},$$scope:r}=t,{color:o="neutral"}=t,{size:a=1}=t,{weight:l="medium"}=t;return n.$$set=c=>{"color"in c&&e(0,o=c.color),"size"in c&&e(2,a=c.size),"weight"in c&&e(3,l=c.weight),"$$scope"in c&&e(4,r=c.$$scope)},n.$$.update=()=>{13&n.$$.dirty&&e(1,s=["c-status-badge",`c-status-badge--${o}`,`c-status-badge--size-${a}`,`c-text--size-${a}`,`c-text--weight-${l}`].join(" "))},[o,s,a,l,r,i]}class kd extends tt{constructor(t){super(),et(this,t,Al,Dl,st,{color:0,size:2,weight:3})}}function Rl(n){let t,e,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},n[0]],i={};for(let r=0;r<s.length;r+=1)i=j(i,s[r]);return{c(){t=ut("svg"),e=new Se(!0),this.h()},l(r){t=Te(r,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var o=Ce(t);e=Ee(o,!0),o.forEach(C),this.h()},h(){e.a=null,bt(t,i)},m(r,o){Ie(r,t,o),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M464 256a208 208 0 1 0-416 0 208 208 0 1 0 416 0M0 256a256 256 0 1 1 512 0 256 256 0 1 1-512 0"/>',t)},p(r,[o]){bt(t,i=Xt(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&o&&r[0]]))},i:O,o:O,d(r){r&&C(t)}}}function Fl(n,t,e){return n.$$set=s=>{e(0,t=j(j({},t),ht(s)))},[t=ht(t)]}class Nl extends tt{constructor(t){super(),et(this,t,Fl,Rl,st,{})}}function Pl(n){let t,e,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},n[0]],i={};for(let r=0;r<s.length;r+=1)i=j(i,s[r]);return{c(){t=ut("svg"),e=new Se(!0),this.h()},l(r){t=Te(r,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var o=Ce(t);e=Ee(o,!0),o.forEach(C),this.h()},h(){e.a=null,bt(t,i)},m(r,o){Ie(r,t,o),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M464 256c0-114.9-93.1-208-208-208v416c114.9 0 208-93.1 208-208M0 256a256 256 0 1 1 512 0 256 256 0 1 1-512 0"/>',t)},p(r,[o]){bt(t,i=Xt(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&o&&r[0]]))},i:O,o:O,d(r){r&&C(t)}}}function Ol(n,t,e){return n.$$set=s=>{e(0,t=j(j({},t),ht(s)))},[t=ht(t)]}class Ll extends tt{constructor(t){super(),et(this,t,Ol,Pl,st,{})}}function mi(n){switch(n){case B.IN_PROGRESS:return"info";case B.COMPLETE:return"success";case B.CANCELLED:return"error";case B.NOT_STARTED:default:return"neutral"}}function cr(n){switch(n){case B.IN_PROGRESS:return"In Progress";case B.COMPLETE:return"Completed";case B.CANCELLED:return"Cancelled";case B.NOT_STARTED:default:return"Not Started"}}function Zs(n){switch(n){case B.IN_PROGRESS:return Ll;case B.COMPLETE:return co;case B.CANCELLED:return ta;case B.NOT_STARTED:default:return Nl}}function Ul(n){let t,e,s;var i=n[2];function r(o,a){return{props:{width:o[1],height:o[3]}}}return i&&(e=Es(i,r(n))),{c(){t=J("span"),e&&I(e.$$.fragment),w(t,"class","c-task-icon svelte-fnkvyj"),cn(t,"--icon-color","var(--ds-color-"+mi(n[0])+"-9)"),cn(t,"--icon-size",n[1])},m(o,a){F(o,t,a),e&&M(e,t,null),s=!0},p(o,[a]){if(4&a&&i!==(i=o[2])){if(e){xt();const l=e;k(l.$$.fragment,1,0,()=>{D(l,1)}),$t()}i?(e=Es(i,r(o)),I(e.$$.fragment),_(e.$$.fragment,1),M(e,t,null)):e=null}else if(i){const l={};2&a&&(l.width=o[1]),8&a&&(l.height=o[3]),e.$set(l)}(!s||1&a)&&cn(t,"--icon-color","var(--ds-color-"+mi(o[0])+"-9)"),(!s||2&a)&&cn(t,"--icon-size",o[1])},i(o){s||(e&&_(e.$$.fragment,o),s=!0)},o(o){e&&k(e.$$.fragment,o),s=!1},d(o){o&&C(t),e&&D(e)}}}function zl(n,t,e){let s,i,r,{taskState:o}=t,{size:a=1}=t;return n.$$set=l=>{"taskState"in l&&e(0,o=l.taskState),"size"in l&&e(4,a=l.size)},n.$$.update=()=>{16&n.$$.dirty&&e(1,s={1:"12px",2:"14px",3:"16px",4:"18px"}[a]),2&n.$$.dirty&&e(3,i=s),1&n.$$.dirty&&e(2,r=Zs(o))},[o,s,r,i,a]}class Ii extends tt{constructor(t){super(),et(this,t,zl,Ul,st,{taskState:0,size:4})}}function ur(n,t,e){const s=n.slice();return s[12]=t[e],s}function ql(n){let t;const e=n[8].default,s=is(e,n,n[11],null);return{c(){s&&s.c()},m(i,r){s&&s.m(i,r),t=!0},p(i,r){s&&s.p&&(!t||2048&r)&&rs(s,e,i,i[11],t?as(e,i[11],r,null):os(i[11]),null)},i(i){t||(_(s,i),t=!0)},o(i){k(s,i),t=!1},d(i){s&&s.d(i)}}}function Hl(n){let t,e,s=cr(n[12])+"";return{c(){t=Gt(s),e=pt()},m(i,r){F(i,t,r),F(i,e,r)},p(i,r){128&r&&s!==(s=cr(i[12])+"")&&en(t,s)},d(i){i&&(C(t),C(e))}}}function Bl(n){let t,e;return t=new Ii({props:{slot:"iconLeft",taskState:n[12],size:1}}),{c(){I(t.$$.fragment)},m(s,i){M(t,s,i),e=!0},p(s,i){const r={};128&i&&(r.taskState=s[12]),t.$set(r)},i(s){e||(_(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){D(t,s)}}}function dr(n){let t,e;function s(){return n[9](n[12])}return t=new Vt.Item({props:{onSelect:s,highlight:n[12]===n[1],disabled:n[3]||n[12]===n[1],class:"c-task-dropdown-item c-task-dropdown-item--"+n[12],$$slots:{iconLeft:[Bl],default:[Hl]},$$scope:{ctx:n}}}),{c(){I(t.$$.fragment)},m(i,r){M(t,i,r),e=!0},p(i,r){n=i;const o={};207&r&&(o.onSelect=s),130&r&&(o.highlight=n[12]===n[1]),138&r&&(o.disabled=n[3]||n[12]===n[1]),128&r&&(o.class="c-task-dropdown-item c-task-dropdown-item--"+n[12]),2176&r&&(o.$$scope={dirty:r,ctx:n}),t.$set(o)},i(i){e||(_(t.$$.fragment,i),e=!0)},o(i){k(t.$$.fragment,i),e=!1},d(i){D(t,i)}}}function Gl(n){let t,e,s=Cs(n[7]),i=[];for(let o=0;o<s.length;o+=1)i[o]=dr(ur(n,s,o));const r=o=>k(i[o],1,1,()=>{i[o]=null});return{c(){for(let o=0;o<i.length;o+=1)i[o].c();t=ie()},m(o,a){for(let l=0;l<i.length;l+=1)i[l]&&i[l].m(o,a);F(o,t,a),e=!0},p(o,a){if(207&a){let l;for(s=Cs(o[7]),l=0;l<s.length;l+=1){const c=ur(o,s,l);i[l]?(i[l].p(c,a),_(i[l],1)):(i[l]=dr(c),i[l].c(),_(i[l],1),i[l].m(t.parentNode,t))}for(xt(),l=s.length;l<i.length;l+=1)r(l);$t()}},i(o){if(!e){for(let a=0;a<s.length;a+=1)_(i[a]);e=!0}},o(o){i=i.filter(Boolean);for(let a=0;a<i.length;a+=1)k(i[a]);e=!1},d(o){o&&C(t),Fo(i,o)}}}function jl(n){let t,e,s,i;return t=new Vt.Trigger({props:{$$slots:{default:[ql]},$$scope:{ctx:n}}}),s=new Vt.Content({props:{size:1,side:n[5],align:n[4],$$slots:{default:[Gl]},$$scope:{ctx:n}}}),{c(){I(t.$$.fragment),e=pt(),I(s.$$.fragment)},m(r,o){M(t,r,o),F(r,e,o),M(s,r,o),i=!0},p(r,o){const a={};2048&o&&(a.$$scope={dirty:o,ctx:r}),t.$set(a);const l={};32&o&&(l.side=r[5]),16&o&&(l.align=r[4]),2255&o&&(l.$$scope={dirty:o,ctx:r}),s.$set(l)},i(r){i||(_(t.$$.fragment,r),_(s.$$.fragment,r),i=!0)},o(r){k(t.$$.fragment,r),k(s.$$.fragment,r),i=!1},d(r){r&&C(e),D(t,r),D(s,r)}}}function Wl(n){let t,e,s;function i(o){n[10](o)}let r={$$slots:{default:[jl]},$$scope:{ctx:n}};return n[6]!==void 0&&(r.requestClose=n[6]),t=new Vt.Root({props:r}),ue.push(()=>Ze(t,"requestClose",i)),{c(){I(t.$$.fragment)},m(o,a){M(t,o,a),s=!0},p(o,[a]){const l={};2303&a&&(l.$$scope={dirty:a,ctx:o}),!e&&64&a&&(e=!0,l.requestClose=o[6],Qe(()=>e=!1)),t.$set(l)},i(o){s||(_(t.$$.fragment,o),s=!0)},o(o){k(t.$$.fragment,o),s=!1},d(o){D(t,o)}}}function Yl(n,t,e){let s,i,{$$slots:r={},$$scope:o}=t,{taskUuid:a}=t,{taskState:l}=t,{taskStore:c}=t,{disabled:u=!1}=t,{align:p="end"}=t,{side:f="bottom"}=t;return n.$$set=g=>{"taskUuid"in g&&e(0,a=g.taskUuid),"taskState"in g&&e(1,l=g.taskState),"taskStore"in g&&e(2,c=g.taskStore),"disabled"in g&&e(3,u=g.disabled),"align"in g&&e(4,p=g.align),"side"in g&&e(5,f=g.side),"$$scope"in g&&e(11,o=g.$$scope)},e(7,s=Object.values(B)),[a,l,c,u,p,f,i,s,r,g=>{u||g===l||(c.updateTask(a,{state:g},kt.USER),i==null||i())},function(g){i=g,e(6,i)},o]}class Vl extends tt{constructor(t){super(),et(this,t,Yl,Wl,st,{taskUuid:0,taskState:1,taskStore:2,disabled:3,align:4,side:5})}}const Xl=n=>({item:16&n}),hr=n=>({item:n[4]}),Zl=n=>({item:16&n}),pr=n=>({item:n[4]}),Ql=n=>({item:16&n}),fr=n=>({item:n[4]}),Kl=n=>({item:16&n}),gr=n=>({item:n[4]});function mr(n){let t,e;return t=new Ke({props:{class:"c-draggable-list-item__expand-collapse-button",size:1,variant:"ghost",color:"neutral","aria-expanded":n[0],"aria-label":n[0]?"Collapse":"Expand",$$slots:{default:[ec]},$$scope:{ctx:n}}}),t.$on("click",n[9]),{c(){I(t.$$.fragment)},m(s,i){M(t,s,i),e=!0},p(s,i){const r={};1&i&&(r["aria-expanded"]=s[0]),1&i&&(r["aria-label"]=s[0]?"Collapse":"Expand"),2097153&i&&(r.$$scope={dirty:i,ctx:s}),t.$set(r)},i(s){e||(_(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){D(t,s)}}}function Jl(n){let t,e;return t=new na({}),{c(){I(t.$$.fragment)},m(s,i){M(t,s,i),e=!0},i(s){e||(_(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){D(t,s)}}}function tc(n){let t,e;return t=new ea({}),{c(){I(t.$$.fragment)},m(s,i){M(t,s,i),e=!0},i(s){e||(_(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){D(t,s)}}}function ec(n){let t,e,s,i;const r=[tc,Jl],o=[];function a(l,c){return l[0]?0:1}return t=a(n),e=o[t]=r[t](n),{c(){e.c(),s=ie()},m(l,c){o[t].m(l,c),F(l,s,c),i=!0},p(l,c){let u=t;t=a(l),t!==u&&(xt(),k(o[u],1,1,()=>{o[u]=null}),$t(),e=o[t],e||(e=o[t]=r[t](l),e.c()),_(e,1),e.m(s.parentNode,s))},i(l){i||(_(e),i=!0)},o(l){k(e),i=!1},d(l){l&&C(s),o[t].d(l)}}}function sc(n){let t,e,s,i,r,o,a,l,c,u,p,f,g,m;const v=n[10].handle,x=is(v,n,n[21],gr);let y=n[6]&&mr(n);const b=n[10]["header-contents"],$=is(b,n,n[21],fr),P=n[10].actions,S=is(P,n,n[21],pr),W=n[10].contents,G=is(W,n,n[21],hr);return{c(){t=J("div"),e=J("div"),s=J("div"),x&&x.c(),i=pt(),y&&y.c(),r=pt(),o=J("div"),$&&$.c(),a=pt(),l=J("div"),S&&S.c(),c=pt(),u=J("div"),G&&G.c(),w(s,"class","c-draggable-list-item__handle svelte-3e5xer"),w(o,"class","c-draggable-list-item__main svelte-3e5xer"),w(l,"class","c-draggable-list-item__actions"),w(e,"class","c-draggable-list-item__content svelte-3e5xer"),w(u,"class","c-draggable-list-item__contents"),le(u,"c-draggable-list-item__show-connectors",n[8]),w(t,"class",p="c-draggable-list-item "+n[2]+" svelte-3e5xer"),w(t,"id",n[3]),w(t,"data-item-id",n[3]),w(t,"data-testid","draggable-list-item"),w(t,"tabindex","0"),w(t,"role","button"),le(t,"is-disabled",n[5]),le(t,"has-nested-items",n[6]),le(t,"is-expanded",n[0]),le(t,"is-selected",n[7])},m(R,z){F(R,t,z),V(t,e),V(e,s),x&&x.m(s,null),V(e,i),y&&y.m(e,null),V(e,r),V(e,o),$&&$.m(o,null),V(e,a),V(e,l),S&&S.m(l,null),V(t,c),V(t,u),G&&G.m(u,null),n[20](t),f=!0,g||(m=[Ne(t,"mousedown",n[11]),Ne(t,"click",n[12]),Ne(t,"keydown",n[13]),Ne(t,"keyup",n[14]),Ne(t,"keypress",n[15]),Ne(t,"focus",n[16]),Ne(t,"blur",n[17]),Ne(t,"focusin",n[18]),Ne(t,"focusout",n[19])],g=!0)},p(R,[z]){x&&x.p&&(!f||2097168&z)&&rs(x,v,R,R[21],f?as(v,R[21],z,Kl):os(R[21]),gr),R[6]?y?(y.p(R,z),64&z&&_(y,1)):(y=mr(R),y.c(),_(y,1),y.m(e,r)):y&&(xt(),k(y,1,1,()=>{y=null}),$t()),$&&$.p&&(!f||2097168&z)&&rs($,b,R,R[21],f?as(b,R[21],z,Ql):os(R[21]),fr),S&&S.p&&(!f||2097168&z)&&rs(S,P,R,R[21],f?as(P,R[21],z,Zl):os(R[21]),pr),G&&G.p&&(!f||2097168&z)&&rs(G,W,R,R[21],f?as(W,R[21],z,Xl):os(R[21]),hr),(!f||256&z)&&le(u,"c-draggable-list-item__show-connectors",R[8]),(!f||4&z&&p!==(p="c-draggable-list-item "+R[2]+" svelte-3e5xer"))&&w(t,"class",p),(!f||8&z)&&w(t,"id",R[3]),(!f||8&z)&&w(t,"data-item-id",R[3]),(!f||36&z)&&le(t,"is-disabled",R[5]),(!f||68&z)&&le(t,"has-nested-items",R[6]),(!f||5&z)&&le(t,"is-expanded",R[0]),(!f||132&z)&&le(t,"is-selected",R[7])},i(R){f||(_(x,R),_(y),_($,R),_(S,R),_(G,R),f=!0)},o(R){k(x,R),k(y),k($,R),k(S,R),k(G,R),f=!1},d(R){R&&C(t),x&&x.d(R),y&&y.d(),$&&$.d(R),S&&S.d(R),G&&G.d(R),n[20](null),g=!1,No(m)}}}function nc(n,t,e){let{$$slots:s={},$$scope:i}=t,{class:r=""}=t,{id:o}=t,{item:a}=t,{disabled:l=!1}=t,{hasNestedItems:c=!1}=t,{expanded:u=!0}=t,{selected:p=!1}=t,{showConnectors:f=!0}=t,{element:g}=t;return n.$$set=m=>{"class"in m&&e(2,r=m.class),"id"in m&&e(3,o=m.id),"item"in m&&e(4,a=m.item),"disabled"in m&&e(5,l=m.disabled),"hasNestedItems"in m&&e(6,c=m.hasNestedItems),"expanded"in m&&e(0,u=m.expanded),"selected"in m&&e(7,p=m.selected),"showConnectors"in m&&e(8,f=m.showConnectors),"element"in m&&e(1,g=m.element),"$$scope"in m&&e(21,i=m.$$scope)},[u,g,r,o,a,l,c,p,f,function(){e(0,u=!u)},s,function(m){mt.call(this,n,m)},function(m){mt.call(this,n,m)},function(m){mt.call(this,n,m)},function(m){mt.call(this,n,m)},function(m){mt.call(this,n,m)},function(m){mt.call(this,n,m)},function(m){mt.call(this,n,m)},function(m){mt.call(this,n,m)},function(m){mt.call(this,n,m)},function(m){ue[m?"unshift":"push"](()=>{g=m,e(1,g)})},i]}class ic extends tt{constructor(t){super(),et(this,t,nc,sc,st,{class:2,id:3,item:4,disabled:5,hasNestedItems:6,expanded:0,selected:7,showConnectors:8,element:1})}}function rc(n){let t,e,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 256 512"},n[0]],i={};for(let r=0;r<s.length;r+=1)i=j(i,s[r]);return{c(){t=ut("svg"),e=new Se(!0),this.h()},l(r){t=Te(r,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var o=Ce(t);e=Ee(o,!0),o.forEach(C),this.h()},h(){e.a=null,bt(t,i)},m(r,o){Ie(r,t,o),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M64 128a32 32 0 1 0 0-64 32 32 0 1 0 0 64m0 160a32 32 0 1 0 0-64 32 32 0 1 0 0 64m32 128a32 32 0 1 0-64 0 32 32 0 1 0 64 0m96-288a32 32 0 1 0 0-64 32 32 0 1 0 0 64m32 128a32 32 0 1 0-64 0 32 32 0 1 0 64 0m-32 192a32 32 0 1 0 0-64 32 32 0 1 0 0 64"/>',t)},p(r,[o]){bt(t,i=Xt(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 256 512"},1&o&&r[0]]))},i:O,o:O,d(r){r&&C(t)}}}function oc(n,t,e){return n.$$set=s=>{e(0,t=j(j({},t),ht(s)))},[t=ht(t)]}class ac extends tt{constructor(t){super(),et(this,t,oc,rc,st,{})}}function br(n,t){var e=Object.keys(n);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(n);t&&(s=s.filter(function(i){return Object.getOwnPropertyDescriptor(n,i).enumerable})),e.push.apply(e,s)}return e}function Re(n){for(var t=1;t<arguments.length;t++){var e=arguments[t]!=null?arguments[t]:{};t%2?br(Object(e),!0).forEach(function(s){lc(n,s,e[s])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):br(Object(e)).forEach(function(s){Object.defineProperty(n,s,Object.getOwnPropertyDescriptor(e,s))})}return n}function bi(n){return bi=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},bi(n)}function lc(n,t,e){return t in n?Object.defineProperty(n,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):n[t]=e,n}function Oe(){return Oe=Object.assign||function(n){for(var t=1;t<arguments.length;t++){var e=arguments[t];for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&(n[s]=e[s])}return n},Oe.apply(this,arguments)}function cc(n,t){if(n==null)return{};var e,s,i=function(o,a){if(o==null)return{};var l,c,u={},p=Object.keys(o);for(c=0;c<p.length;c++)l=p[c],a.indexOf(l)>=0||(u[l]=o[l]);return u}(n,t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);for(s=0;s<r.length;s++)e=r[s],t.indexOf(e)>=0||Object.prototype.propertyIsEnumerable.call(n,e)&&(i[e]=n[e])}return i}function Le(n){if(typeof window<"u"&&window.navigator)return!!navigator.userAgent.match(n)}var Ue=Le(/(?:Trident.*rv[ :]?11\.|msie|iemobile|Windows Phone)/i),on=Le(/Edge/i),vr=Le(/firefox/i),js=Le(/safari/i)&&!Le(/chrome/i)&&!Le(/android/i),Mi=Le(/iP(ad|od|hone)/i),vo=Le(/chrome/i)&&Le(/android/i),_o={capture:!1,passive:!1};function Q(n,t,e){n.addEventListener(t,e,!Ue&&_o)}function X(n,t,e){n.removeEventListener(t,e,!Ue&&_o)}function An(n,t){if(t){if(t[0]===">"&&(t=t.substring(1)),n)try{if(n.matches)return n.matches(t);if(n.msMatchesSelector)return n.msMatchesSelector(t);if(n.webkitMatchesSelector)return n.webkitMatchesSelector(t)}catch{return!1}return!1}}function yo(n){return n.host&&n!==document&&n.host.nodeType?n.host:n.parentNode}function fe(n,t,e,s){if(n){e=e||document;do{if(t!=null&&(t[0]===">"?n.parentNode===e&&An(n,t):An(n,t))||s&&n===e)return n;if(n===e)break}while(n=yo(n))}return null}var Ws,_r=/\s+/g;function Zt(n,t,e){if(n&&t)if(n.classList)n.classList[e?"add":"remove"](t);else{var s=(" "+n.className+" ").replace(_r," ").replace(" "+t+" "," ");n.className=(s+(e?" "+t:"")).replace(_r," ")}}function U(n,t,e){var s=n&&n.style;if(s){if(e===void 0)return document.defaultView&&document.defaultView.getComputedStyle?e=document.defaultView.getComputedStyle(n,""):n.currentStyle&&(e=n.currentStyle),t===void 0?e:e[t];t in s||t.indexOf("webkit")!==-1||(t="-webkit-"+t),s[t]=e+(typeof e=="string"?"":"px")}}function ws(n,t){var e="";if(typeof n=="string")e=n;else do{var s=U(n,"transform");s&&s!=="none"&&(e=s+" "+e)}while(!t&&(n=n.parentNode));var i=window.DOMMatrix||window.WebKitCSSMatrix||window.CSSMatrix||window.MSCSSMatrix;return i&&new i(e)}function yr(n,t,e){if(n){var s=n.getElementsByTagName(t),i=0,r=s.length;if(e)for(;i<r;i++)e(s[i],i);return s}return[]}function Fe(){var n=document.scrollingElement;return n||document.documentElement}function wt(n,t,e,s,i){if(n.getBoundingClientRect||n===window){var r,o,a,l,c,u,p;if(n!==window&&n.parentNode&&n!==Fe()?(o=(r=n.getBoundingClientRect()).top,a=r.left,l=r.bottom,c=r.right,u=r.height,p=r.width):(o=0,a=0,l=window.innerHeight,c=window.innerWidth,u=window.innerHeight,p=window.innerWidth),(t||e)&&n!==window&&(i=i||n.parentNode,!Ue))do if(i&&i.getBoundingClientRect&&(U(i,"transform")!=="none"||e&&U(i,"position")!=="static")){var f=i.getBoundingClientRect();o-=f.top+parseInt(U(i,"border-top-width")),a-=f.left+parseInt(U(i,"border-left-width")),l=o+r.height,c=a+r.width;break}while(i=i.parentNode);if(s&&n!==window){var g=ws(i||n),m=g&&g.a,v=g&&g.d;g&&(l=(o/=v)+(u/=v),c=(a/=m)+(p/=m))}return{top:o,left:a,bottom:l,right:c,width:p,height:u}}}function wr(n,t,e){for(var s=Xe(n,!0),i=wt(n)[t];s;){if(!(i>=wt(s)[e]))return s;if(s===Fe())break;s=Xe(s,!1)}return!1}function ks(n,t,e,s){for(var i=0,r=0,o=n.children;r<o.length;){if(o[r].style.display!=="none"&&o[r]!==L.ghost&&(s||o[r]!==L.dragged)&&fe(o[r],e.draggable,n,!1)){if(i===t)return o[r];i++}r++}return null}function vi(n,t){for(var e=n.lastElementChild;e&&(e===L.ghost||U(e,"display")==="none"||t&&!An(e,t));)e=e.previousElementSibling;return e||null}function ae(n,t){var e=0;if(!n||!n.parentNode)return-1;for(;n=n.previousElementSibling;)n.nodeName.toUpperCase()==="TEMPLATE"||n===L.clone||t&&!An(n,t)||e++;return e}function kr(n){var t=0,e=0,s=Fe();if(n)do{var i=ws(n),r=i.a,o=i.d;t+=n.scrollLeft*r,e+=n.scrollTop*o}while(n!==s&&(n=n.parentNode));return[t,e]}function Xe(n,t){if(!n||!n.getBoundingClientRect)return Fe();var e=n,s=!1;do if(e.clientWidth<e.scrollWidth||e.clientHeight<e.scrollHeight){var i=U(e);if(e.clientWidth<e.scrollWidth&&(i.overflowX=="auto"||i.overflowX=="scroll")||e.clientHeight<e.scrollHeight&&(i.overflowY=="auto"||i.overflowY=="scroll")){if(!e.getBoundingClientRect||e===document.body)return Fe();if(s||t)return e;s=!0}}while(e=e.parentNode);return Fe()}function Wn(n,t){return Math.round(n.top)===Math.round(t.top)&&Math.round(n.left)===Math.round(t.left)&&Math.round(n.height)===Math.round(t.height)&&Math.round(n.width)===Math.round(t.width)}function wo(n,t){return function(){if(!Ws){var e=arguments;e.length===1?n.call(this,e[0]):n.apply(this,e),Ws=setTimeout(function(){Ws=void 0},t)}}}function ko(n,t,e){n.scrollLeft+=t,n.scrollTop+=e}function xr(n){var t=window.Polymer,e=window.jQuery||window.Zepto;return t&&t.dom?t.dom(n).cloneNode(!0):e?e(n).clone(!0)[0]:n.cloneNode(!0)}function $r(n,t,e){var s={};return Array.from(n.children).forEach(function(i){var r,o,a,l;if(fe(i,t.draggable,n,!1)&&!i.animated&&i!==e){var c=wt(i);s.left=Math.min((r=s.left)!==null&&r!==void 0?r:1/0,c.left),s.top=Math.min((o=s.top)!==null&&o!==void 0?o:1/0,c.top),s.right=Math.max((a=s.right)!==null&&a!==void 0?a:-1/0,c.right),s.bottom=Math.max((l=s.bottom)!==null&&l!==void 0?l:-1/0,c.bottom)}}),s.width=s.right-s.left,s.height=s.bottom-s.top,s.x=s.left,s.y=s.top,s}var Yt="Sortable"+new Date().getTime();function uc(){var n,t=[];return{captureAnimationState:function(){t=[],this.options.animation&&[].slice.call(this.el.children).forEach(function(e){if(U(e,"display")!=="none"&&e!==L.ghost){t.push({target:e,rect:wt(e)});var s=Re({},t[t.length-1].rect);if(e.thisAnimationDuration){var i=ws(e,!0);i&&(s.top-=i.f,s.left-=i.e)}e.fromRect=s}})},addAnimationState:function(e){t.push(e)},removeAnimationState:function(e){t.splice(function(s,i){for(var r in s)if(s.hasOwnProperty(r)){for(var o in i)if(i.hasOwnProperty(o)&&i[o]===s[r][o])return Number(r)}return-1}(t,{target:e}),1)},animateAll:function(e){var s=this;if(!this.options.animation)return clearTimeout(n),void(typeof e=="function"&&e());var i=!1,r=0;t.forEach(function(o){var a=0,l=o.target,c=l.fromRect,u=wt(l),p=l.prevFromRect,f=l.prevToRect,g=o.rect,m=ws(l,!0);m&&(u.top-=m.f,u.left-=m.e),l.toRect=u,l.thisAnimationDuration&&Wn(p,u)&&!Wn(c,u)&&(g.top-u.top)/(g.left-u.left)==(c.top-u.top)/(c.left-u.left)&&(a=function(v,x,y,b){return Math.sqrt(Math.pow(x.top-v.top,2)+Math.pow(x.left-v.left,2))/Math.sqrt(Math.pow(x.top-y.top,2)+Math.pow(x.left-y.left,2))*b.animation}(g,p,f,s.options)),Wn(u,c)||(l.prevFromRect=c,l.prevToRect=u,a||(a=s.options.animation),s.animate(l,g,u,a)),a&&(i=!0,r=Math.max(r,a),clearTimeout(l.animationResetTimer),l.animationResetTimer=setTimeout(function(){l.animationTime=0,l.prevFromRect=null,l.fromRect=null,l.prevToRect=null,l.thisAnimationDuration=null},a),l.thisAnimationDuration=a)}),clearTimeout(n),i?n=setTimeout(function(){typeof e=="function"&&e()},r):typeof e=="function"&&e(),t=[]},animate:function(e,s,i,r){if(r){U(e,"transition",""),U(e,"transform","");var o=ws(this.el),a=o&&o.a,l=o&&o.d,c=(s.left-i.left)/(a||1),u=(s.top-i.top)/(l||1);e.animatingX=!!c,e.animatingY=!!u,U(e,"transform","translate3d("+c+"px,"+u+"px,0)"),this.forRepaintDummy=function(p){return p.offsetWidth}(e),U(e,"transition","transform "+r+"ms"+(this.options.easing?" "+this.options.easing:"")),U(e,"transform","translate3d(0,0,0)"),typeof e.animated=="number"&&clearTimeout(e.animated),e.animated=setTimeout(function(){U(e,"transition",""),U(e,"transform",""),e.animated=!1,e.animatingX=!1,e.animatingY=!1},r)}}}}var fs=[],Yn={initializeByDefault:!0},Qs={mount:function(n){for(var t in Yn)Yn.hasOwnProperty(t)&&!(t in n)&&(n[t]=Yn[t]);fs.forEach(function(e){if(e.pluginName===n.pluginName)throw"Sortable: Cannot mount plugin ".concat(n.pluginName," more than once")}),fs.push(n)},pluginEvent:function(n,t,e){var s=this;this.eventCanceled=!1,e.cancel=function(){s.eventCanceled=!0};var i=n+"Global";fs.forEach(function(r){t[r.pluginName]&&(t[r.pluginName][i]&&t[r.pluginName][i](Re({sortable:t},e)),t.options[r.pluginName]&&t[r.pluginName][n]&&t[r.pluginName][n](Re({sortable:t},e)))})},initializePlugins:function(n,t,e,s){for(var i in fs.forEach(function(o){var a=o.pluginName;if(n.options[a]||o.initializeByDefault){var l=new o(n,t,n.options);l.sortable=n,l.options=n.options,n[a]=l,Oe(e,l.defaults)}}),n.options)if(n.options.hasOwnProperty(i)){var r=this.modifyOption(n,i,n.options[i]);r!==void 0&&(n.options[i]=r)}},getEventProperties:function(n,t){var e={};return fs.forEach(function(s){typeof s.eventProperties=="function"&&Oe(e,s.eventProperties.call(t[s.pluginName],n))}),e},modifyOption:function(n,t,e){var s;return fs.forEach(function(i){n[i.pluginName]&&i.optionListeners&&typeof i.optionListeners[t]=="function"&&(s=i.optionListeners[t].call(n[i.pluginName],e))}),s}},dc=["evt"],Wt=function(n,t){var e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},s=e.evt,i=cc(e,dc);Qs.pluginEvent.bind(L)(n,t,Re({dragEl:T,parentEl:ft,ghostEl:H,rootEl:dt,nextEl:ss,lastDownEl:$n,cloneEl:gt,cloneHidden:We,dragStarted:Us,putSortable:Rt,activeSortable:L.active,originalEvent:s,oldIndex:vs,oldDraggableIndex:Ys,newIndex:Qt,newDraggableIndex:je,hideGhostForTarget:To,unhideGhostForTarget:Co,cloneNowHidden:function(){We=!0},cloneNowShown:function(){We=!1},dispatchSortableEvent:function(r){qt({sortable:t,name:r,originalEvent:s})}},i))};function qt(n){(function(t){var e=t.sortable,s=t.rootEl,i=t.name,r=t.targetEl,o=t.cloneEl,a=t.toEl,l=t.fromEl,c=t.oldIndex,u=t.newIndex,p=t.oldDraggableIndex,f=t.newDraggableIndex,g=t.originalEvent,m=t.putSortable,v=t.extraEventProperties;if(e=e||s&&s[Yt]){var x,y=e.options,b="on"+i.charAt(0).toUpperCase()+i.substr(1);!window.CustomEvent||Ue||on?(x=document.createEvent("Event")).initEvent(i,!0,!0):x=new CustomEvent(i,{bubbles:!0,cancelable:!0}),x.to=a||s,x.from=l||s,x.item=r||s,x.clone=o,x.oldIndex=c,x.newIndex=u,x.oldDraggableIndex=p,x.newDraggableIndex=f,x.originalEvent=g,x.pullMode=m?m.lastPutMode:void 0;var $=Re(Re({},v),Qs.getEventProperties(i,e));for(var P in $)x[P]=$[P];s&&s.dispatchEvent(x),y[b]&&y[b].call(e,x)}})(Re({putSortable:Rt,cloneEl:gt,targetEl:T,rootEl:dt,oldIndex:vs,oldDraggableIndex:Ys,newIndex:Qt,newDraggableIndex:je},n))}var T,ft,H,dt,ss,$n,gt,We,vs,Qt,Ys,je,pn,Rt,Je,pe,Vn,Xn,Sr,Tr,Us,gs,Fs,fn,Ot,ms=!1,Rn=!1,Fn=[],Ns=!1,gn=!1,Zn=[],_i=!1,mn=[],Ln=typeof document<"u",bn=Mi,Cr=on||Ue?"cssFloat":"float",hc=Ln&&!vo&&!Mi&&"draggable"in document.createElement("div"),xo=function(){if(Ln){if(Ue)return!1;var n=document.createElement("x");return n.style.cssText="pointer-events:auto",n.style.pointerEvents==="auto"}}(),$o=function(n,t){var e=U(n),s=parseInt(e.width)-parseInt(e.paddingLeft)-parseInt(e.paddingRight)-parseInt(e.borderLeftWidth)-parseInt(e.borderRightWidth),i=ks(n,0,t),r=ks(n,1,t),o=i&&U(i),a=r&&U(r),l=o&&parseInt(o.marginLeft)+parseInt(o.marginRight)+wt(i).width,c=a&&parseInt(a.marginLeft)+parseInt(a.marginRight)+wt(r).width;if(e.display==="flex")return e.flexDirection==="column"||e.flexDirection==="column-reverse"?"vertical":"horizontal";if(e.display==="grid")return e.gridTemplateColumns.split(" ").length<=1?"vertical":"horizontal";if(i&&o.float&&o.float!=="none"){var u=o.float==="left"?"left":"right";return!r||a.clear!=="both"&&a.clear!==u?"horizontal":"vertical"}return i&&(o.display==="block"||o.display==="flex"||o.display==="table"||o.display==="grid"||l>=s&&e[Cr]==="none"||r&&e[Cr]==="none"&&l+c>s)?"vertical":"horizontal"},So=function(n){function t(i,r){return function(o,a,l,c){var u=o.options.group.name&&a.options.group.name&&o.options.group.name===a.options.group.name;if(i==null&&(r||u))return!0;if(i==null||i===!1)return!1;if(r&&i==="clone")return i;if(typeof i=="function")return t(i(o,a,l,c),r)(o,a,l,c);var p=(r?o:a).options.group.name;return i===!0||typeof i=="string"&&i===p||i.join&&i.indexOf(p)>-1}}var e={},s=n.group;s&&bi(s)=="object"||(s={name:s}),e.name=s.name,e.checkPull=t(s.pull,!0),e.checkPut=t(s.put),e.revertClone=s.revertClone,n.group=e},To=function(){!xo&&H&&U(H,"display","none")},Co=function(){!xo&&H&&U(H,"display","")};Ln&&!vo&&document.addEventListener("click",function(n){if(Rn)return n.preventDefault(),n.stopPropagation&&n.stopPropagation(),n.stopImmediatePropagation&&n.stopImmediatePropagation(),Rn=!1,!1},!0);var ts=function(n){if(T){n=n.touches?n.touches[0]:n;var t=(i=n.clientX,r=n.clientY,Fn.some(function(a){var l=a[Yt].options.emptyInsertThreshold;if(l&&!vi(a)){var c=wt(a),u=i>=c.left-l&&i<=c.right+l,p=r>=c.top-l&&r<=c.bottom+l;return u&&p?o=a:void 0}}),o);if(t){var e={};for(var s in n)n.hasOwnProperty(s)&&(e[s]=n[s]);e.target=e.rootEl=t,e.preventDefault=void 0,e.stopPropagation=void 0,t[Yt]._onDragOver(e)}}var i,r,o},pc=function(n){T&&T.parentNode[Yt]._isOutsideThisEl(n.target)};function L(n,t){if(!n||!n.nodeType||n.nodeType!==1)throw"Sortable: `el` must be an HTMLElement, not ".concat({}.toString.call(n));this.el=n,this.options=t=Oe({},t),n[Yt]=this;var e={group:null,sort:!0,disabled:!1,store:null,handle:null,draggable:/^[uo]l$/i.test(n.nodeName)?">li":">*",swapThreshold:1,invertSwap:!1,invertedSwapThreshold:null,removeCloneOnHide:!0,direction:function(){return $o(n,this.options)},ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dragClass:"sortable-drag",ignore:"a, img",filter:null,preventOnFilter:!0,animation:0,easing:null,setData:function(r,o){r.setData("Text",o.textContent)},dropBubble:!1,dragoverBubble:!1,dataIdAttr:"data-id",delay:0,delayOnTouchOnly:!1,touchStartThreshold:(Number.parseInt?Number:window).parseInt(window.devicePixelRatio,10)||1,forceFallback:!1,fallbackClass:"sortable-fallback",fallbackOnBody:!1,fallbackTolerance:0,fallbackOffset:{x:0,y:0},supportPointer:L.supportPointer!==!1&&"PointerEvent"in window&&(!js||Mi),emptyInsertThreshold:5};for(var s in Qs.initializePlugins(this,n,e),e)!(s in t)&&(t[s]=e[s]);for(var i in So(t),this)i.charAt(0)==="_"&&typeof this[i]=="function"&&(this[i]=this[i].bind(this));this.nativeDraggable=!t.forceFallback&&hc,this.nativeDraggable&&(this.options.touchStartThreshold=1),t.supportPointer?Q(n,"pointerdown",this._onTapStart):(Q(n,"mousedown",this._onTapStart),Q(n,"touchstart",this._onTapStart)),this.nativeDraggable&&(Q(n,"dragover",this),Q(n,"dragenter",this)),Fn.push(this.el),t.store&&t.store.get&&this.sort(t.store.get(this)||[]),Oe(this,uc())}function vn(n,t,e,s,i,r,o,a){var l,c,u=n[Yt],p=u.options.onMove;return!window.CustomEvent||Ue||on?(l=document.createEvent("Event")).initEvent("move",!0,!0):l=new CustomEvent("move",{bubbles:!0,cancelable:!0}),l.to=t,l.from=n,l.dragged=e,l.draggedRect=s,l.related=i||t,l.relatedRect=r||wt(t),l.willInsertAfter=a,l.originalEvent=o,n.dispatchEvent(l),p&&(c=p.call(u,l,o)),c}function Qn(n){n.draggable=!1}function fc(){_i=!1}function gc(n){for(var t=n.tagName+n.className+n.src+n.href+n.textContent,e=t.length,s=0;e--;)s+=t.charCodeAt(e);return s.toString(36)}function _n(n){return setTimeout(n,0)}function Kn(n){return clearTimeout(n)}L.prototype={constructor:L,_isOutsideThisEl:function(n){this.el.contains(n)||n===this.el||(gs=null)},_getDirection:function(n,t){return typeof this.options.direction=="function"?this.options.direction.call(this,n,t,T):this.options.direction},_onTapStart:function(n){if(n.cancelable){var t=this,e=this.el,s=this.options,i=s.preventOnFilter,r=n.type,o=n.touches&&n.touches[0]||n.pointerType&&n.pointerType==="touch"&&n,a=(o||n).target,l=n.target.shadowRoot&&(n.path&&n.path[0]||n.composedPath&&n.composedPath()[0])||a,c=s.filter;if(function(u){mn.length=0;for(var p=u.getElementsByTagName("input"),f=p.length;f--;){var g=p[f];g.checked&&mn.push(g)}}(e),!T&&!(/mousedown|pointerdown/.test(r)&&n.button!==0||s.disabled)&&!l.isContentEditable&&(this.nativeDraggable||!js||!a||a.tagName.toUpperCase()!=="SELECT")&&!((a=fe(a,s.draggable,e,!1))&&a.animated||$n===a)){if(vs=ae(a),Ys=ae(a,s.draggable),typeof c=="function"){if(c.call(this,n,a,this))return qt({sortable:t,rootEl:l,name:"filter",targetEl:a,toEl:e,fromEl:e}),Wt("filter",t,{evt:n}),void(i&&n.preventDefault())}else if(c&&(c=c.split(",").some(function(u){if(u=fe(l,u.trim(),e,!1))return qt({sortable:t,rootEl:u,name:"filter",targetEl:a,fromEl:e,toEl:e}),Wt("filter",t,{evt:n}),!0})))return void(i&&n.preventDefault());s.handle&&!fe(l,s.handle,e,!1)||this._prepareDragStart(n,o,a)}}},_prepareDragStart:function(n,t,e){var s,i=this,r=i.el,o=i.options,a=r.ownerDocument;if(e&&!T&&e.parentNode===r){var l=wt(e);if(dt=r,ft=(T=e).parentNode,ss=T.nextSibling,$n=e,pn=o.group,L.dragged=T,Je={target:T,clientX:(t||n).clientX,clientY:(t||n).clientY},Sr=Je.clientX-l.left,Tr=Je.clientY-l.top,this._lastX=(t||n).clientX,this._lastY=(t||n).clientY,T.style["will-change"]="all",s=function(){Wt("delayEnded",i,{evt:n}),L.eventCanceled?i._onDrop():(i._disableDelayedDragEvents(),!vr&&i.nativeDraggable&&(T.draggable=!0),i._triggerDragStart(n,t),qt({sortable:i,name:"choose",originalEvent:n}),Zt(T,o.chosenClass,!0))},o.ignore.split(",").forEach(function(c){yr(T,c.trim(),Qn)}),Q(a,"dragover",ts),Q(a,"mousemove",ts),Q(a,"touchmove",ts),o.supportPointer?(Q(a,"pointerup",i._onDrop),!this.nativeDraggable&&Q(a,"pointercancel",i._onDrop)):(Q(a,"mouseup",i._onDrop),Q(a,"touchend",i._onDrop),Q(a,"touchcancel",i._onDrop)),vr&&this.nativeDraggable&&(this.options.touchStartThreshold=4,T.draggable=!0),Wt("delayStart",this,{evt:n}),!o.delay||o.delayOnTouchOnly&&!t||this.nativeDraggable&&(on||Ue))s();else{if(L.eventCanceled)return void this._onDrop();o.supportPointer?(Q(a,"pointerup",i._disableDelayedDrag),Q(a,"pointercancel",i._disableDelayedDrag)):(Q(a,"mouseup",i._disableDelayedDrag),Q(a,"touchend",i._disableDelayedDrag),Q(a,"touchcancel",i._disableDelayedDrag)),Q(a,"mousemove",i._delayedDragTouchMoveHandler),Q(a,"touchmove",i._delayedDragTouchMoveHandler),o.supportPointer&&Q(a,"pointermove",i._delayedDragTouchMoveHandler),i._dragStartTimer=setTimeout(s,o.delay)}}},_delayedDragTouchMoveHandler:function(n){var t=n.touches?n.touches[0]:n;Math.max(Math.abs(t.clientX-this._lastX),Math.abs(t.clientY-this._lastY))>=Math.floor(this.options.touchStartThreshold/(this.nativeDraggable&&window.devicePixelRatio||1))&&this._disableDelayedDrag()},_disableDelayedDrag:function(){T&&Qn(T),clearTimeout(this._dragStartTimer),this._disableDelayedDragEvents()},_disableDelayedDragEvents:function(){var n=this.el.ownerDocument;X(n,"mouseup",this._disableDelayedDrag),X(n,"touchend",this._disableDelayedDrag),X(n,"touchcancel",this._disableDelayedDrag),X(n,"pointerup",this._disableDelayedDrag),X(n,"pointercancel",this._disableDelayedDrag),X(n,"mousemove",this._delayedDragTouchMoveHandler),X(n,"touchmove",this._delayedDragTouchMoveHandler),X(n,"pointermove",this._delayedDragTouchMoveHandler)},_triggerDragStart:function(n,t){t=t||n.pointerType=="touch"&&n,!this.nativeDraggable||t?this.options.supportPointer?Q(document,"pointermove",this._onTouchMove):Q(document,t?"touchmove":"mousemove",this._onTouchMove):(Q(T,"dragend",this),Q(dt,"dragstart",this._onDragStart));try{document.selection?_n(function(){document.selection.empty()}):window.getSelection().removeAllRanges()}catch{}},_dragStarted:function(n,t){if(ms=!1,dt&&T){Wt("dragStarted",this,{evt:t}),this.nativeDraggable&&Q(document,"dragover",pc);var e=this.options;!n&&Zt(T,e.dragClass,!1),Zt(T,e.ghostClass,!0),L.active=this,n&&this._appendGhost(),qt({sortable:this,name:"start",originalEvent:t})}else this._nulling()},_emulateDragOver:function(){if(pe){this._lastX=pe.clientX,this._lastY=pe.clientY,To();for(var n=document.elementFromPoint(pe.clientX,pe.clientY),t=n;n&&n.shadowRoot&&(n=n.shadowRoot.elementFromPoint(pe.clientX,pe.clientY))!==t;)t=n;if(T.parentNode[Yt]._isOutsideThisEl(n),t)do{if(t[Yt]&&t[Yt]._onDragOver({clientX:pe.clientX,clientY:pe.clientY,target:n,rootEl:t})&&!this.options.dragoverBubble)break;n=t}while(t=yo(t));Co()}},_onTouchMove:function(n){if(Je){var t=this.options,e=t.fallbackTolerance,s=t.fallbackOffset,i=n.touches?n.touches[0]:n,r=H&&ws(H,!0),o=H&&r&&r.a,a=H&&r&&r.d,l=bn&&Ot&&kr(Ot),c=(i.clientX-Je.clientX+s.x)/(o||1)+(l?l[0]-Zn[0]:0)/(o||1),u=(i.clientY-Je.clientY+s.y)/(a||1)+(l?l[1]-Zn[1]:0)/(a||1);if(!L.active&&!ms){if(e&&Math.max(Math.abs(i.clientX-this._lastX),Math.abs(i.clientY-this._lastY))<e)return;this._onDragStart(n,!0)}if(H){r?(r.e+=c-(Vn||0),r.f+=u-(Xn||0)):r={a:1,b:0,c:0,d:1,e:c,f:u};var p="matrix(".concat(r.a,",").concat(r.b,",").concat(r.c,",").concat(r.d,",").concat(r.e,",").concat(r.f,")");U(H,"webkitTransform",p),U(H,"mozTransform",p),U(H,"msTransform",p),U(H,"transform",p),Vn=c,Xn=u,pe=i}n.cancelable&&n.preventDefault()}},_appendGhost:function(){if(!H){var n=this.options.fallbackOnBody?document.body:dt,t=wt(T,!0,bn,!0,n),e=this.options;if(bn){for(Ot=n;U(Ot,"position")==="static"&&U(Ot,"transform")==="none"&&Ot!==document;)Ot=Ot.parentNode;Ot!==document.body&&Ot!==document.documentElement?(Ot===document&&(Ot=Fe()),t.top+=Ot.scrollTop,t.left+=Ot.scrollLeft):Ot=Fe(),Zn=kr(Ot)}Zt(H=T.cloneNode(!0),e.ghostClass,!1),Zt(H,e.fallbackClass,!0),Zt(H,e.dragClass,!0),U(H,"transition",""),U(H,"transform",""),U(H,"box-sizing","border-box"),U(H,"margin",0),U(H,"top",t.top),U(H,"left",t.left),U(H,"width",t.width),U(H,"height",t.height),U(H,"opacity","0.8"),U(H,"position",bn?"absolute":"fixed"),U(H,"zIndex","100000"),U(H,"pointerEvents","none"),L.ghost=H,n.appendChild(H),U(H,"transform-origin",Sr/parseInt(H.style.width)*100+"% "+Tr/parseInt(H.style.height)*100+"%")}},_onDragStart:function(n,t){var e=this,s=n.dataTransfer,i=e.options;Wt("dragStart",this,{evt:n}),L.eventCanceled?this._onDrop():(Wt("setupClone",this),L.eventCanceled||((gt=xr(T)).removeAttribute("id"),gt.draggable=!1,gt.style["will-change"]="",this._hideClone(),Zt(gt,this.options.chosenClass,!1),L.clone=gt),e.cloneId=_n(function(){Wt("clone",e),L.eventCanceled||(e.options.removeCloneOnHide||dt.insertBefore(gt,T),e._hideClone(),qt({sortable:e,name:"clone"}))}),!t&&Zt(T,i.dragClass,!0),t?(Rn=!0,e._loopId=setInterval(e._emulateDragOver,50)):(X(document,"mouseup",e._onDrop),X(document,"touchend",e._onDrop),X(document,"touchcancel",e._onDrop),s&&(s.effectAllowed="move",i.setData&&i.setData.call(e,s,T)),Q(document,"drop",e),U(T,"transform","translateZ(0)")),ms=!0,e._dragStartId=_n(e._dragStarted.bind(e,t,n)),Q(document,"selectstart",e),Us=!0,window.getSelection().removeAllRanges(),js&&U(document.body,"user-select","none"))},_onDragOver:function(n){var t,e,s,i,r=this.el,o=n.target,a=this.options,l=a.group,c=L.active,u=pn===l,p=a.sort,f=Rt||c,g=this,m=!1;if(!_i){if(n.preventDefault!==void 0&&n.cancelable&&n.preventDefault(),o=fe(o,a.draggable,r,!0),It("dragOver"),L.eventCanceled)return m;if(T.contains(n.target)||o.animated&&o.animatingX&&o.animatingY||g._ignoreWhileAnimating===o)return Pt(!1);if(Rn=!1,c&&!a.disabled&&(u?p||(s=ft!==dt):Rt===this||(this.lastPutMode=pn.checkPull(this,c,T,n))&&l.checkPut(this,c,T,n))){if(i=this._getDirection(n,o)==="vertical",t=wt(T),It("dragOverValid"),L.eventCanceled)return m;if(s)return ft=dt,Nt(),this._hideClone(),It("revert"),L.eventCanceled||(ss?dt.insertBefore(T,ss):dt.appendChild(T)),Pt(!0);var v=vi(r,a.draggable);if(!v||function(K,jt,rt){var St=wt(vi(rt.el,rt.options.draggable)),de=$r(rt.el,rt.options,H),oe=10;return jt?K.clientX>de.right+oe||K.clientY>St.bottom&&K.clientX>St.left:K.clientY>de.bottom+oe||K.clientX>St.right&&K.clientY>St.top}(n,i,this)&&!v.animated){if(v===T)return Pt(!1);if(v&&r===n.target&&(o=v),o&&(e=wt(o)),vn(dt,r,T,t,o,e,n,!!o)!==!1)return Nt(),v&&v.nextSibling?r.insertBefore(T,v.nextSibling):r.appendChild(T),ft=r,Me(),Pt(!0)}else if(v&&function(K,jt,rt){var St=wt(ks(rt.el,0,rt.options,!0)),de=$r(rt.el,rt.options,H),oe=10;return jt?K.clientX<de.left-oe||K.clientY<St.top&&K.clientX<St.right:K.clientY<de.top-oe||K.clientY<St.bottom&&K.clientX<St.left}(n,i,this)){var x=ks(r,0,a,!0);if(x===T)return Pt(!1);if(e=wt(o=x),vn(dt,r,T,t,o,e,n,!1)!==!1)return Nt(),r.insertBefore(T,x),ft=r,Me(),Pt(!0)}else if(o.parentNode===r){e=wt(o);var y,b,$,P=T.parentNode!==r,S=!function(K,jt,rt){var St=rt?K.left:K.top,de=rt?K.right:K.bottom,oe=rt?K.width:K.height,Ms=rt?jt.left:jt.top,Un=rt?jt.right:jt.bottom,he=rt?jt.width:jt.height;return St===Ms||de===Un||St+oe/2===Ms+he/2}(T.animated&&T.toRect||t,o.animated&&o.toRect||e,i),W=i?"top":"left",G=wr(o,"top","top")||wr(T,"top","top"),R=G?G.scrollTop:void 0;if(gs!==o&&(b=e[W],Ns=!1,gn=!S&&a.invertSwap||P),y=function(K,jt,rt,St,de,oe,Ms,Un){var he=St?K.clientY:K.clientX,ze=St?rt.height:rt.width,Ds=St?rt.top:rt.left,an=St?rt.bottom:rt.right,zn=!1;if(!Ms){if(Un&&fn<ze*de){if(!Ns&&(Fs===1?he>Ds+ze*oe/2:he<an-ze*oe/2)&&(Ns=!0),Ns)zn=!0;else if(Fs===1?he<Ds+fn:he>an-fn)return-Fs}else if(he>Ds+ze*(1-de)/2&&he<an-ze*(1-de)/2)return function(Mo){return ae(T)<ae(Mo)?1:-1}(jt)}return(zn=zn||Ms)&&(he<Ds+ze*oe/2||he>an-ze*oe/2)?he>Ds+ze/2?1:-1:0}(n,o,e,i,S?1:a.swapThreshold,a.invertedSwapThreshold==null?a.swapThreshold:a.invertedSwapThreshold,gn,gs===o),y!==0){var z=ae(T);do z-=y,$=ft.children[z];while($&&(U($,"display")==="none"||$===H))}if(y===0||$===o)return Pt(!1);gs=o,Fs=y;var re=o.nextElementSibling,Y=!1,ot=vn(dt,r,T,t,o,e,n,Y=y===1);if(ot!==!1)return ot!==1&&ot!==-1||(Y=ot===1),_i=!0,setTimeout(fc,30),Nt(),Y&&!re?r.appendChild(T):o.parentNode.insertBefore(T,Y?re:o),G&&ko(G,0,R-G.scrollTop),ft=T.parentNode,b===void 0||gn||(fn=Math.abs(b-wt(o)[W])),Me(),Pt(!0)}if(r.contains(T))return Pt(!1)}return!1}function It(K,jt){Wt(K,g,Re({evt:n,isOwner:u,axis:i?"vertical":"horizontal",revert:s,dragRect:t,targetRect:e,canSort:p,fromSortable:f,target:o,completed:Pt,onMove:function(rt,St){return vn(dt,r,T,t,rt,wt(rt),n,St)},changed:Me},jt))}function Nt(){It("dragOverAnimationCapture"),g.captureAnimationState(),g!==f&&f.captureAnimationState()}function Pt(K){return It("dragOverCompleted",{insertion:K}),K&&(u?c._hideClone():c._showClone(g),g!==f&&(Zt(T,Rt?Rt.options.ghostClass:c.options.ghostClass,!1),Zt(T,a.ghostClass,!0)),Rt!==g&&g!==L.active?Rt=g:g===L.active&&Rt&&(Rt=null),f===g&&(g._ignoreWhileAnimating=o),g.animateAll(function(){It("dragOverAnimationComplete"),g._ignoreWhileAnimating=null}),g!==f&&(f.animateAll(),f._ignoreWhileAnimating=null)),(o===T&&!T.animated||o===r&&!o.animated)&&(gs=null),a.dragoverBubble||n.rootEl||o===document||(T.parentNode[Yt]._isOutsideThisEl(n.target),!K&&ts(n)),!a.dragoverBubble&&n.stopPropagation&&n.stopPropagation(),m=!0}function Me(){Qt=ae(T),je=ae(T,a.draggable),qt({sortable:g,name:"change",toEl:r,newIndex:Qt,newDraggableIndex:je,originalEvent:n})}},_ignoreWhileAnimating:null,_offMoveEvents:function(){X(document,"mousemove",this._onTouchMove),X(document,"touchmove",this._onTouchMove),X(document,"pointermove",this._onTouchMove),X(document,"dragover",ts),X(document,"mousemove",ts),X(document,"touchmove",ts)},_offUpEvents:function(){var n=this.el.ownerDocument;X(n,"mouseup",this._onDrop),X(n,"touchend",this._onDrop),X(n,"pointerup",this._onDrop),X(n,"pointercancel",this._onDrop),X(n,"touchcancel",this._onDrop),X(document,"selectstart",this)},_onDrop:function(n){var t=this.el,e=this.options;Qt=ae(T),je=ae(T,e.draggable),Wt("drop",this,{evt:n}),ft=T&&T.parentNode,Qt=ae(T),je=ae(T,e.draggable),L.eventCanceled||(ms=!1,gn=!1,Ns=!1,clearInterval(this._loopId),clearTimeout(this._dragStartTimer),Kn(this.cloneId),Kn(this._dragStartId),this.nativeDraggable&&(X(document,"drop",this),X(t,"dragstart",this._onDragStart)),this._offMoveEvents(),this._offUpEvents(),js&&U(document.body,"user-select",""),U(T,"transform",""),n&&(Us&&(n.cancelable&&n.preventDefault(),!e.dropBubble&&n.stopPropagation()),H&&H.parentNode&&H.parentNode.removeChild(H),(dt===ft||Rt&&Rt.lastPutMode!=="clone")&&gt&&gt.parentNode&&gt.parentNode.removeChild(gt),T&&(this.nativeDraggable&&X(T,"dragend",this),Qn(T),T.style["will-change"]="",Us&&!ms&&Zt(T,Rt?Rt.options.ghostClass:this.options.ghostClass,!1),Zt(T,this.options.chosenClass,!1),qt({sortable:this,name:"unchoose",toEl:ft,newIndex:null,newDraggableIndex:null,originalEvent:n}),dt!==ft?(Qt>=0&&(qt({rootEl:ft,name:"add",toEl:ft,fromEl:dt,originalEvent:n}),qt({sortable:this,name:"remove",toEl:ft,originalEvent:n}),qt({rootEl:ft,name:"sort",toEl:ft,fromEl:dt,originalEvent:n}),qt({sortable:this,name:"sort",toEl:ft,originalEvent:n})),Rt&&Rt.save()):Qt!==vs&&Qt>=0&&(qt({sortable:this,name:"update",toEl:ft,originalEvent:n}),qt({sortable:this,name:"sort",toEl:ft,originalEvent:n})),L.active&&(Qt!=null&&Qt!==-1||(Qt=vs,je=Ys),qt({sortable:this,name:"end",toEl:ft,originalEvent:n}),this.save())))),this._nulling()},_nulling:function(){Wt("nulling",this),dt=T=ft=H=ss=gt=$n=We=Je=pe=Us=Qt=je=vs=Ys=gs=Fs=Rt=pn=L.dragged=L.ghost=L.clone=L.active=null,mn.forEach(function(n){n.checked=!0}),mn.length=Vn=Xn=0},handleEvent:function(n){switch(n.type){case"drop":case"dragend":this._onDrop(n);break;case"dragenter":case"dragover":T&&(this._onDragOver(n),function(t){t.dataTransfer&&(t.dataTransfer.dropEffect="move"),t.cancelable&&t.preventDefault()}(n));break;case"selectstart":n.preventDefault()}},toArray:function(){for(var n,t=[],e=this.el.children,s=0,i=e.length,r=this.options;s<i;s++)fe(n=e[s],r.draggable,this.el,!1)&&t.push(n.getAttribute(r.dataIdAttr)||gc(n));return t},sort:function(n,t){var e={},s=this.el;this.toArray().forEach(function(i,r){var o=s.children[r];fe(o,this.options.draggable,s,!1)&&(e[i]=o)},this),t&&this.captureAnimationState(),n.forEach(function(i){e[i]&&(s.removeChild(e[i]),s.appendChild(e[i]))}),t&&this.animateAll()},save:function(){var n=this.options.store;n&&n.set&&n.set(this)},closest:function(n,t){return fe(n,t||this.options.draggable,this.el,!1)},option:function(n,t){var e=this.options;if(t===void 0)return e[n];var s=Qs.modifyOption(this,n,t);e[n]=s!==void 0?s:t,n==="group"&&So(e)},destroy:function(){Wt("destroy",this);var n=this.el;n[Yt]=null,X(n,"mousedown",this._onTapStart),X(n,"touchstart",this._onTapStart),X(n,"pointerdown",this._onTapStart),this.nativeDraggable&&(X(n,"dragover",this),X(n,"dragenter",this)),Array.prototype.forEach.call(n.querySelectorAll("[draggable]"),function(t){t.removeAttribute("draggable")}),this._onDrop(),this._disableDelayedDragEvents(),Fn.splice(Fn.indexOf(this.el),1),this.el=n=null},_hideClone:function(){if(!We){if(Wt("hideClone",this),L.eventCanceled)return;U(gt,"display","none"),this.options.removeCloneOnHide&&gt.parentNode&&gt.parentNode.removeChild(gt),We=!0}},_showClone:function(n){if(n.lastPutMode==="clone"){if(We){if(Wt("showClone",this),L.eventCanceled)return;T.parentNode!=dt||this.options.group.revertClone?ss?dt.insertBefore(gt,ss):dt.appendChild(gt):dt.insertBefore(gt,T),this.options.group.revertClone&&this.animate(T,gt),U(gt,"display",""),We=!1}}else this._hideClone()}},Ln&&Q(document,"touchmove",function(n){(L.active||ms)&&n.cancelable&&n.preventDefault()}),L.utils={on:Q,off:X,css:U,find:yr,is:function(n,t){return!!fe(n,t,n,!1)},extend:function(n,t){if(n&&t)for(var e in t)t.hasOwnProperty(e)&&(n[e]=t[e]);return n},throttle:wo,closest:fe,toggleClass:Zt,clone:xr,index:ae,nextTick:_n,cancelNextTick:Kn,detectDirection:$o,getChild:ks,expando:Yt},L.get=function(n){return n[Yt]},L.mount=function(){for(var n=arguments.length,t=new Array(n),e=0;e<n;e++)t[e]=arguments[e];t[0].constructor===Array&&(t=t[0]),t.forEach(function(s){if(!s.prototype||!s.prototype.constructor)throw"Sortable: Mounted plugin must be a constructor function, not ".concat({}.toString.call(s));s.utils&&(L.utils=Re(Re({},L.utils),s.utils)),Qs.mount(s)})},L.create=function(n,t){return new L(n,t)},L.version="1.15.6";var zs,yi,Jn,ti,Nn,qs,vt=[],wi=!1;function Sn(){vt.forEach(function(n){clearInterval(n.pid)}),vt=[]}function Er(){clearInterval(qs)}var ei=wo(function(n,t,e,s){if(t.scroll){var i,r=(n.touches?n.touches[0]:n).clientX,o=(n.touches?n.touches[0]:n).clientY,a=t.scrollSensitivity,l=t.scrollSpeed,c=Fe(),u=!1;yi!==e&&(yi=e,Sn(),zs=t.scroll,i=t.scrollFn,zs===!0&&(zs=Xe(e,!0)));var p=0,f=zs;do{var g=f,m=wt(g),v=m.top,x=m.bottom,y=m.left,b=m.right,$=m.width,P=m.height,S=void 0,W=void 0,G=g.scrollWidth,R=g.scrollHeight,z=U(g),re=g.scrollLeft,Y=g.scrollTop;g===c?(S=$<G&&(z.overflowX==="auto"||z.overflowX==="scroll"||z.overflowX==="visible"),W=P<R&&(z.overflowY==="auto"||z.overflowY==="scroll"||z.overflowY==="visible")):(S=$<G&&(z.overflowX==="auto"||z.overflowX==="scroll"),W=P<R&&(z.overflowY==="auto"||z.overflowY==="scroll"));var ot=S&&(Math.abs(b-r)<=a&&re+$<G)-(Math.abs(y-r)<=a&&!!re),It=W&&(Math.abs(x-o)<=a&&Y+P<R)-(Math.abs(v-o)<=a&&!!Y);if(!vt[p])for(var Nt=0;Nt<=p;Nt++)vt[Nt]||(vt[Nt]={});vt[p].vx==ot&&vt[p].vy==It&&vt[p].el===g||(vt[p].el=g,vt[p].vx=ot,vt[p].vy=It,clearInterval(vt[p].pid),ot==0&&It==0||(u=!0,vt[p].pid=setInterval((function(){s&&this.layer===0&&L.active._onTouchMove(Nn);var Pt=vt[this.layer].vy?vt[this.layer].vy*l:0,Me=vt[this.layer].vx?vt[this.layer].vx*l:0;typeof i=="function"&&i.call(L.dragged.parentNode[Yt],Me,Pt,n,Nn,vt[this.layer].el)!=="continue"||ko(vt[this.layer].el,Me,Pt)}).bind({layer:p}),24))),p++}while(t.bubbleScroll&&f!==c&&(f=Xe(f,!1)));wi=u}},30),Ir=function(n){var t=n.originalEvent,e=n.putSortable,s=n.dragEl,i=n.activeSortable,r=n.dispatchSortableEvent,o=n.hideGhostForTarget,a=n.unhideGhostForTarget;if(t){var l=e||i;o();var c=t.changedTouches&&t.changedTouches.length?t.changedTouches[0]:t,u=document.elementFromPoint(c.clientX,c.clientY);a(),l&&!l.el.contains(u)&&(r("spill"),this.onSpill({dragEl:s,putSortable:e}))}};function si(){}function ni(){}si.prototype={startIndex:null,dragStart:function(n){var t=n.oldDraggableIndex;this.startIndex=t},onSpill:function(n){var t=n.dragEl,e=n.putSortable;this.sortable.captureAnimationState(),e&&e.captureAnimationState();var s=ks(this.sortable.el,this.startIndex,this.options);s?this.sortable.el.insertBefore(t,s):this.sortable.el.appendChild(t),this.sortable.animateAll(),e&&e.animateAll()},drop:Ir},Oe(si,{pluginName:"revertOnSpill"}),ni.prototype={onSpill:function(n){var t=n.dragEl,e=n.putSortable||this.sortable;e.captureAnimationState(),t.parentNode&&t.parentNode.removeChild(t),e.animateAll()},drop:Ir},Oe(ni,{pluginName:"removeOnSpill"}),L.mount(new function(){function n(){for(var t in this.defaults={scroll:!0,forceAutoScrollFallback:!1,scrollSensitivity:30,scrollSpeed:10,bubbleScroll:!0},this)t.charAt(0)==="_"&&typeof this[t]=="function"&&(this[t]=this[t].bind(this))}return n.prototype={dragStarted:function(t){var e=t.originalEvent;this.sortable.nativeDraggable?Q(document,"dragover",this._handleAutoScroll):this.options.supportPointer?Q(document,"pointermove",this._handleFallbackAutoScroll):e.touches?Q(document,"touchmove",this._handleFallbackAutoScroll):Q(document,"mousemove",this._handleFallbackAutoScroll)},dragOverCompleted:function(t){var e=t.originalEvent;this.options.dragOverBubble||e.rootEl||this._handleAutoScroll(e)},drop:function(){this.sortable.nativeDraggable?X(document,"dragover",this._handleAutoScroll):(X(document,"pointermove",this._handleFallbackAutoScroll),X(document,"touchmove",this._handleFallbackAutoScroll),X(document,"mousemove",this._handleFallbackAutoScroll)),Er(),Sn(),clearTimeout(Ws),Ws=void 0},nulling:function(){Nn=yi=zs=wi=qs=Jn=ti=null,vt.length=0},_handleFallbackAutoScroll:function(t){this._handleAutoScroll(t,!0)},_handleAutoScroll:function(t,e){var s=this,i=(t.touches?t.touches[0]:t).clientX,r=(t.touches?t.touches[0]:t).clientY,o=document.elementFromPoint(i,r);if(Nn=t,e||this.options.forceAutoScrollFallback||on||Ue||js){ei(t,this.options,o,e);var a=Xe(o,!0);!wi||qs&&i===Jn&&r===ti||(qs&&Er(),qs=setInterval(function(){var l=Xe(document.elementFromPoint(i,r),!0);l!==a&&(a=l,Sn()),ei(t,s.options,l,e)},10),Jn=i,ti=r)}else{if(!this.options.bubbleScroll||Xe(o,!0)===Fe())return void Sn();ei(t,this.options,Xe(o,!1),!1)}}},Oe(n,{pluginName:"scroll",initializeByDefault:!0})}),L.mount(ni,si);const mc="c-draggable-list-item__sortable-ghost",bc="c-draggable-list-item__sortable-chosen",vc="c-draggable-list-item__sortable-drag",_c=n=>({items:4&n,onEnd:8&n}),Mr=n=>({items:n[2],onEnd:n[3]});function yc(n){let t,e,s;const i=n[12].items,r=is(i,n,n[11],Mr);return{c(){t=J("div"),r&&r.c(),w(t,"class",e="c-draggable-list "+n[0]+" svelte-5aopnc"),w(t,"id",n[1]),w(t,"data-list-id",n[1]),w(t,"data-testid","draggable-list")},m(o,a){F(o,t,a),r&&r.m(t,null),n[13](t),s=!0},p(o,[a]){r&&r.p&&(!s||2060&a)&&rs(r,i,o,o[11],s?as(i,o[11],a,_c):os(o[11]),Mr),(!s||1&a&&e!==(e="c-draggable-list "+o[0]+" svelte-5aopnc"))&&w(t,"class",e),(!s||2&a)&&w(t,"id",o[1]),(!s||2&a)&&w(t,"data-list-id",o[1])},i(o){s||(_(r,o),s=!0)},o(o){k(r,o),s=!1},d(o){o&&C(t),r&&r.d(o),n[13](null)}}}function wc(n,t,e){let s,{$$slots:i={},$$scope:r}=t,{class:o=""}=t,{id:a}=t,{items:l=[]}=t,{options:c={}}=t,{disabled:u=!1}=t,{useHandle:p=!0}=t,{onEnd:f=()=>{}}=t,g=null,m={...c},v=u;function x(){try{g&&typeof g.destroy=="function"&&g.destroy()}catch(b){console.error("Error destroying Sortable instance:",b)}finally{e(8,g=null)}}async function y(){if(await Xs(),x(),s)try{e(8,g=new L(s,{animation:150,fallbackOnBody:!0,swapThreshold:.65,ghostClass:mc,chosenClass:bc,dragClass:vc,disabled:u,group:c.group||"nested",dataIdAttr:"data-item-id",handle:p?".c-draggable-list-item__handle":void 0,...c,onEnd:f}))}catch(b){console.error("Error initializing Sortable:",b),e(8,g=null)}}return Po(y),Oo(()=>{x(),e(4,s=null),e(9,m={})}),n.$$set=b=>{"class"in b&&e(0,o=b.class),"id"in b&&e(1,a=b.id),"items"in b&&e(2,l=b.items),"options"in b&&e(5,c=b.options),"disabled"in b&&e(6,u=b.disabled),"useHandle"in b&&e(7,p=b.useHandle),"onEnd"in b&&e(3,f=b.onEnd),"$$scope"in b&&e(11,r=b.$$scope)},n.$$.update=()=>{if(1360&n.$$.dirty&&g&&s&&u!==v)try{typeof g.option=="function"&&(g.option("disabled",u),e(10,v=u))}catch(b){console.error("Error updating Sortable disabled state:",b)}if(816&n.$$.dirty&&g&&s&&JSON.stringify(c)!==JSON.stringify(m))try{e(9,m={...c}),y()}catch(b){console.error("Error updating Sortable options:",b)}},[o,a,l,f,s,c,u,p,g,m,v,r,i,function(b){ue[b?"unshift":"push"](()=>{s=b,e(4,s)})}]}class Eo extends tt{constructor(t){super(),et(this,t,wc,yc,st,{class:0,id:1,items:2,options:5,disabled:6,useHandle:7,onEnd:3})}}function kc(n){let t;return{c(){t=Gt("Last Updated:")},m(e,s){F(e,t,s)},d(e){e&&C(t)}}}function xc(n){let t,e=new Date(n[0].lastUpdated).toLocaleString()+"";return{c(){t=Gt(e)},m(s,i){F(s,t,i)},p(s,i){1&i&&e!==(e=new Date(s[0].lastUpdated).toLocaleString()+"")&&en(t,e)},d(s){s&&C(t)}}}function $c(n){let t;return{c(){t=Gt("Updated By:")},m(e,s){F(e,t,s)},d(e){e&&C(t)}}}function Sc(n){let t,e=n[0].lastUpdatedBy+"";return{c(){t=Gt(e)},m(s,i){F(s,t,i)},p(s,i){1&i&&e!==(e=s[0].lastUpdatedBy+"")&&en(t,e)},d(s){s&&C(t)}}}function Tc(n){let t,e,s,i,r,o,a,l,c,u,p,f,g,m,v,x,y,b,$,P,S,W,G;function R(Y){n[7](Y)}function z(Y){n[8](Y)}let re={size:1,variant:"surface",placeholder:"Add task description...",disabled:!n[1],rows:1,resize:"vertical"};return n[3]!==void 0&&(re.textInput=n[3]),n[2]!==void 0&&(re.value=n[2]),i=new ra({props:re}),ue.push(()=>Ze(i,"textInput",R)),ue.push(()=>Ze(i,"value",z)),i.$on("keydown",n[5]),i.$on("blur",n[4]),f=new bs({props:{size:1,weight:"medium",color:"neutral",$$slots:{default:[kc]},$$scope:{ctx:n}}}),v=new bs({props:{size:1,color:"secondary",$$slots:{default:[xc]},$$scope:{ctx:n}}}),$=new bs({props:{size:1,weight:"medium",color:"neutral",$$slots:{default:[$c]},$$scope:{ctx:n}}}),W=new bs({props:{size:1,color:"secondary",$$slots:{default:[Sc]},$$scope:{ctx:n}}}),{c(){t=J("div"),e=J("div"),s=J("div"),I(i.$$.fragment),a=pt(),l=J("div"),c=J("div"),u=J("div"),p=J("div"),I(f.$$.fragment),g=pt(),m=J("div"),I(v.$$.fragment),x=pt(),y=J("div"),b=J("div"),I($.$$.fragment),P=pt(),S=J("div"),I(W.$$.fragment),w(s,"class","c-task-details__section c-task-details__description-contents svelte-1txf08z"),w(p,"class","c-task-details__metadata-label svelte-1txf08z"),w(m,"class","c-task-details__metadata-value svelte-1txf08z"),w(u,"class","c-task-details__metadata-row svelte-1txf08z"),w(b,"class","c-task-details__metadata-label svelte-1txf08z"),w(S,"class","c-task-details__metadata-value svelte-1txf08z"),w(y,"class","c-task-details__metadata-row svelte-1txf08z"),w(c,"class","c-task-details__metadata svelte-1txf08z"),w(l,"class","c-task-details__section svelte-1txf08z"),w(e,"class","c-task-details__content svelte-1txf08z"),w(t,"class","c-task-details svelte-1txf08z")},m(Y,ot){F(Y,t,ot),V(t,e),V(e,s),M(i,s,null),V(e,a),V(e,l),V(l,c),V(c,u),V(u,p),M(f,p,null),V(u,g),V(u,m),M(v,m,null),V(c,x),V(c,y),V(y,b),M($,b,null),V(y,P),V(y,S),M(W,S,null),G=!0},p(Y,[ot]){const It={};2&ot&&(It.disabled=!Y[1]),!r&&8&ot&&(r=!0,It.textInput=Y[3],Qe(()=>r=!1)),!o&&4&ot&&(o=!0,It.value=Y[2],Qe(()=>o=!1)),i.$set(It);const Nt={};1024&ot&&(Nt.$$scope={dirty:ot,ctx:Y}),f.$set(Nt);const Pt={};1025&ot&&(Pt.$$scope={dirty:ot,ctx:Y}),v.$set(Pt);const Me={};1024&ot&&(Me.$$scope={dirty:ot,ctx:Y}),$.$set(Me);const K={};1025&ot&&(K.$$scope={dirty:ot,ctx:Y}),W.$set(K)},i(Y){G||(_(i.$$.fragment,Y),_(f.$$.fragment,Y),_(v.$$.fragment,Y),_($.$$.fragment,Y),_(W.$$.fragment,Y),G=!0)},o(Y){k(i.$$.fragment,Y),k(f.$$.fragment,Y),k(v.$$.fragment,Y),k($.$$.fragment,Y),k(W.$$.fragment,Y),G=!1},d(Y){Y&&C(t),D(i),D(f),D(v),D($),D(W)}}}function Cc(n,t,e){let s,{task:i}=t,{taskStore:r}=t,{editable:o=!0}=t,a=i.description;function l(){a.trim()!==i.description&&r.updateTask(i.uuid,{description:a.trim()},kt.USER),s==null||s.blur()}return n.$$set=c=>{"task"in c&&e(0,i=c.task),"taskStore"in c&&e(6,r=c.taskStore),"editable"in c&&e(1,o=c.editable)},[i,o,a,s,l,function(c){c.key!=="Enter"||c.shiftKey||c.ctrlKey||c.metaKey?c.key==="Escape"&&(c.preventDefault(),c.stopPropagation(),e(2,a=i.description),s==null||s.blur()):(c.preventDefault(),c.stopPropagation(),l())},r,function(c){s=c,e(3,s)},function(c){a=c,e(2,a)}]}class Ec extends tt{constructor(t){super(),et(this,t,Cc,Tc,st,{task:0,taskStore:6,editable:1})}}function Ic(n){let t,e;const s=[{size:n[1]},{variant:"ghost"},{color:n[6]},{disabled:n[5]},n[7]];let i={$$slots:{default:[Dc]},$$scope:{ctx:n}};for(let r=0;r<s.length;r+=1)i=j(i,s[r]);return t=new Ke({props:i}),t.$on("click",n[8]),t.$on("keyup",n[9]),t.$on("keydown",n[10]),t.$on("mousedown",n[11]),t.$on("mouseover",n[12]),t.$on("focus",n[13]),t.$on("mouseleave",n[14]),t.$on("blur",n[15]),t.$on("contextmenu",n[16]),{c(){I(t.$$.fragment)},m(r,o){M(t,r,o),e=!0},p(r,o){const a=226&o?Xt(s,[2&o&&{size:r[1]},s[1],64&o&&{color:r[6]},32&o&&{disabled:r[5]},128&o&&ki(r[7])]):{};131075&o&&(a.$$scope={dirty:o,ctx:r}),t.$set(a)},i(r){e||(_(t.$$.fragment,r),e=!0)},o(r){k(t.$$.fragment,r),e=!1},d(r){D(t,r)}}}function Mc(n){let t,e,s;return e=new Vl({props:{taskUuid:n[2],taskState:n[0],taskStore:n[3],disabled:n[5],$$slots:{default:[Rc]},$$scope:{ctx:n}}}),{c(){t=J("div"),I(e.$$.fragment),w(t,"class","c-task-icon-button")},m(i,r){F(i,t,r),M(e,t,null),s=!0},p(i,r){const o={};4&r&&(o.taskUuid=i[2]),1&r&&(o.taskState=i[0]),8&r&&(o.taskStore=i[3]),32&r&&(o.disabled=i[5]),131299&r&&(o.$$scope={dirty:r,ctx:i}),e.$set(o)},i(i){s||(_(e.$$.fragment,i),s=!0)},o(i){k(e.$$.fragment,i),s=!1},d(i){i&&C(t),D(e)}}}function Dc(n){let t,e;return t=new Ii({props:{taskState:n[0],size:n[1]}}),{c(){I(t.$$.fragment)},m(s,i){M(t,s,i),e=!0},p(s,i){const r={};1&i&&(r.taskState=s[0]),2&i&&(r.size=s[1]),t.$set(r)},i(s){e||(_(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){D(t,s)}}}function Ac(n){let t,e;return t=new Ii({props:{taskState:n[0],size:n[1]}}),{c(){I(t.$$.fragment)},m(s,i){M(t,s,i),e=!0},p(s,i){const r={};1&i&&(r.taskState=s[0]),2&i&&(r.size=s[1]),t.$set(r)},i(s){e||(_(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){D(t,s)}}}function Rc(n){let t,e;const s=[{size:n[1]},{variant:"ghost"},{color:n[6]},{disabled:n[5]},n[7]];let i={$$slots:{default:[Ac]},$$scope:{ctx:n}};for(let r=0;r<s.length;r+=1)i=j(i,s[r]);return t=new Ke({props:i}),{c(){I(t.$$.fragment)},m(r,o){M(t,r,o),e=!0},p(r,o){const a=226&o?Xt(s,[2&o&&{size:r[1]},s[1],64&o&&{color:r[6]},32&o&&{disabled:r[5]},128&o&&ki(r[7])]):{};131075&o&&(a.$$scope={dirty:o,ctx:r}),t.$set(a)},i(r){e||(_(t.$$.fragment,r),e=!0)},o(r){k(t.$$.fragment,r),e=!1},d(r){D(t,r)}}}function Fc(n){let t,e,s,i;const r=[Mc,Ic],o=[];function a(l,c){return!l[4]&&l[2]&&l[3]?0:1}return t=a(n),e=o[t]=r[t](n),{c(){e.c(),s=ie()},m(l,c){o[t].m(l,c),F(l,s,c),i=!0},p(l,[c]){let u=t;t=a(l),t===u?o[t].p(l,c):(xt(),k(o[u],1,1,()=>{o[u]=null}),$t(),e=o[t],e?e.p(l,c):(e=o[t]=r[t](l),e.c()),_(e,1),e.m(s.parentNode,s))},i(l){i||(_(e),i=!0)},o(l){k(e),i=!1},d(l){l&&C(s),o[t].d(l)}}}function Nc(n,t,e){let s;const i=["taskState","size","taskUuid","taskStore","hideDropdown","disabled"];let r=Tn(t,i),{taskState:o}=t,{size:a=1}=t,{taskUuid:l}=t,{taskStore:c}=t,{hideDropdown:u=!1}=t,{disabled:p=!1}=t;return n.$$set=f=>{t=j(j({},t),ht(f)),e(7,r=Tn(t,i)),"taskState"in f&&e(0,o=f.taskState),"size"in f&&e(1,a=f.size),"taskUuid"in f&&e(2,l=f.taskUuid),"taskStore"in f&&e(3,c=f.taskStore),"hideDropdown"in f&&e(4,u=f.hideDropdown),"disabled"in f&&e(5,p=f.disabled)},n.$$.update=()=>{1&n.$$.dirty&&e(6,s=mi(o))},[o,a,l,c,u,p,s,r,function(f){mt.call(this,n,f)},function(f){mt.call(this,n,f)},function(f){mt.call(this,n,f)},function(f){mt.call(this,n,f)},function(f){mt.call(this,n,f)},function(f){mt.call(this,n,f)},function(f){mt.call(this,n,f)},function(f){mt.call(this,n,f)},function(f){mt.call(this,n,f)}]}class Pc extends tt{constructor(t){super(),et(this,t,Nc,Fc,st,{taskState:0,size:1,taskUuid:2,taskStore:3,hideDropdown:4,disabled:5})}}function Oc(n){let t,e,s,i,r,o;const a=[{size:n[2]},{variant:n[1]},{color:n[3]},{placeholder:n[0]},n[11]];function l(p){n[18](p)}function c(p){n[19](p)}let u={};for(let p=0;p<a.length;p+=1)u=j(u,a[p]);return n[7]!==void 0&&(u.textInput=n[7]),n[6]!==void 0&&(u.value=n[6]),e=new ia({props:u}),ue.push(()=>Ze(e,"textInput",l)),ue.push(()=>Ze(e,"value",c)),e.$on("keydown",n[10]),e.$on("focus",n[9]),e.$on("blur",n[20]),e.$on("keydown",n[21]),e.$on("click",n[22]),e.$on("blur",n[23]),e.$on("focus",n[24]),{c(){t=J("div"),I(e.$$.fragment),w(t,"class",r="c-editable-text "+n[4]+" svelte-jooyia")},m(p,f){F(p,t,f),M(e,t,null),n[25](t),o=!0},p(p,[f]){const g=2063&f?Xt(a,[4&f&&{size:p[2]},2&f&&{variant:p[1]},8&f&&{color:p[3]},1&f&&{placeholder:p[0]},2048&f&&ki(p[11])]):{};!s&&128&f&&(s=!0,g.textInput=p[7],Qe(()=>s=!1)),!i&&64&f&&(i=!0,g.value=p[6],Qe(()=>i=!1)),e.$set(g),(!o||16&f&&r!==(r="c-editable-text "+p[4]+" svelte-jooyia"))&&w(t,"class",r)},i(p){o||(_(e.$$.fragment,p),o=!0)},o(p){k(e.$$.fragment,p),o=!1},d(p){p&&C(t),D(e),n[25](null)}}}function Lc(n,t,e){const s=["value","disabled","placeholder","clickToEdit","variant","size","color","class","editing","startEdit","acceptEdit","cancelEdit"];let i=Tn(t,s);const r=Lo();let o,a,{value:l=""}=t,{disabled:c=!1}=t,{placeholder:u=""}=t,{clickToEdit:p=!1}=t,{variant:f="surface"}=t,{size:g=2}=t,{color:m}=t,{class:v=""}=t,{editing:x=!1}=t,y=l;async function b(S){c||!o||x||(e(6,y=l),r("startEdit",{value:l}),o.focus(),S!=null&&S.selectAll&&(await Xs(),o==null||o.select()),e(13,x=!0))}function $(){const S=l,W=y.trim();S!==W?(e(12,l=W),r("acceptEdit",{oldValue:S,newValue:W}),document.activeElement===o&&(o==null||o.blur()),e(13,x=!1)):P()}function P(){e(6,y=l),r("cancelEdit",{value:l}),document.activeElement===o&&(o==null||o.blur()),e(13,x=!1)}return n.$$set=S=>{t=j(j({},t),ht(S)),e(11,i=Tn(t,s)),"value"in S&&e(12,l=S.value),"disabled"in S&&e(14,c=S.disabled),"placeholder"in S&&e(0,u=S.placeholder),"clickToEdit"in S&&e(15,p=S.clickToEdit),"variant"in S&&e(1,f=S.variant),"size"in S&&e(2,g=S.size),"color"in S&&e(3,m=S.color),"class"in S&&e(4,v=S.class),"editing"in S&&e(13,x=S.editing)},[u,f,g,m,v,$,y,o,a,function(){!p||c||x||b()},function(S){S.key==="Enter"?$():S.key==="Escape"&&P()},i,l,x,c,p,b,P,function(S){o=S,e(7,o)},function(S){y=S,e(6,y)},()=>$(),function(S){mt.call(this,n,S)},function(S){mt.call(this,n,S)},function(S){mt.call(this,n,S)},function(S){mt.call(this,n,S)},function(S){ue[S?"unshift":"push"](()=>{a=S,e(8,a)})}]}class Uc extends tt{constructor(t){super(),et(this,t,Lc,Oc,st,{value:12,disabled:14,placeholder:0,clickToEdit:15,variant:1,size:2,color:3,class:4,editing:13,startEdit:16,acceptEdit:5,cancelEdit:17})}get startEdit(){return this.$$.ctx[16]}get acceptEdit(){return this.$$.ctx[5]}get cancelEdit(){return this.$$.ctx[17]}}function zc(n){let t,e,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},n[0]],i={};for(let r=0;r<s.length;r+=1)i=j(i,s[r]);return{c(){t=ut("svg"),e=new Se(!0),this.h()},l(r){t=Te(r,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var o=Ce(t);e=Ee(o,!0),o.forEach(C),this.h()},h(){e.a=null,bt(t,i)},m(r,o){Ie(r,t,o),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M448 224c35.3 0 64-28.7 64-64V96c0-35.3-28.7-64-64-64H64C28.7 32 0 60.7 0 96v64c0 35.3 28.7 64 64 64h168v86.1l-23-23c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9l64 64c9.4 9.4 24.6 9.4 33.9 0l64-64c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-23 23V224h168zM64 288c-35.3 0-64 28.7-64 64v64c0 35.3 28.7 64 64 64h384c35.3 0 64-28.7 64-64v-64c0-35.3-28.7-64-64-64h-74.3c4.8 16 2.2 33.8-7.7 48h82c8.8 0 16 7.2 16 16v64c0 8.8-7.2 16-16 16H64c-8.8 0-16-7.2-16-16v-64c0-8.8 7.2-16 16-16h82c-9.9-14.2-12.5-32-7.7-48z"/>',t)},p(r,[o]){bt(t,i=Xt(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&o&&r[0]]))},i:O,o:O,d(r){r&&C(t)}}}function qc(n,t,e){return n.$$set=s=>{e(0,t=j(j({},t),ht(s)))},[t=ht(t)]}class Hc extends tt{constructor(t){super(),et(this,t,qc,zc,st,{})}}function Bc(n){let t,e,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},n[0]],i={};for(let r=0;r<s.length;r+=1)i=j(i,s[r]);return{c(){t=ut("svg"),e=new Se(!0),this.h()},l(r){t=Te(r,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var o=Ce(t);e=Ee(o,!0),o.forEach(C),this.h()},h(){e.a=null,bt(t,i)},m(r,o){Ie(r,t,o),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M0 96c0-35.3 28.7-64 64-64h384c35.3 0 64 28.7 64 64v64c0 35.3-28.7 64-64 64H152v96c0 22.1 17.9 40 40 40h32v-8c0-35.3 28.7-64 64-64h160c35.3 0 64 28.7 64 64v64c0 35.3-28.7 64-64 64H288c-35.3 0-64-28.7-64-64v-8h-32c-48.6 0-88-39.4-88-88v-96H64c-35.3 0-64-28.7-64-64zm448 240H288c-8.8 0-16 7.2-16 16v64c0 8.8 7.2 16 16 16h160c8.8 0 16-7.2 16-16v-64c0-8.8-7.2-16-16-16"/>',t)},p(r,[o]){bt(t,i=Xt(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&o&&r[0]]))},i:O,o:O,d(r){r&&C(t)}}}function Gc(n,t,e){return n.$$set=s=>{e(0,t=j(j({},t),ht(s)))},[t=ht(t)]}class jc extends tt{constructor(t){super(),et(this,t,Gc,Bc,st,{})}}function Wc(n){let t,e;return t=new El({}),{c(){I(t.$$.fragment)},m(s,i){M(t,s,i),e=!0},i(s){e||(_(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){D(t,s)}}}function Yc(n){let t,e;return t=new Yr({props:{size:1}}),{c(){I(t.$$.fragment)},m(s,i){M(t,s,i),e=!0},i(s){e||(_(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){D(t,s)}}}function Vc(n){let t,e,s,i;const r=[Yc,Wc],o=[];function a(l,c){return l[3]?0:1}return t=a(n),e=o[t]=r[t](n),{c(){e.c(),s=ie()},m(l,c){o[t].m(l,c),F(l,s,c),i=!0},p(l,c){let u=t;t=a(l),t!==u&&(xt(),k(o[u],1,1,()=>{o[u]=null}),$t(),e=o[t],e||(e=o[t]=r[t](l),e.c()),_(e,1),e.m(s.parentNode,s))},i(l){i||(_(e),i=!0)},o(l){k(e),i=!1},d(l){l&&C(s),o[t].d(l)}}}function Xc(n){let t,e;return t=new Ke({props:{size:1,variant:"ghost",color:"neutral",disabled:!n[0]||n[3],class:"c-task-tree-item-actions__trigger",$$slots:{default:[Vc]},$$scope:{ctx:n}}}),{c(){I(t.$$.fragment)},m(s,i){M(t,s,i),e=!0},p(s,i){const r={};9&i&&(r.disabled=!s[0]||s[3]),4104&i&&(r.$$scope={dirty:i,ctx:s}),t.$set(r)},i(s){e||(_(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){D(t,s)}}}function Zc(n){let t,e;return t=new sn({props:{content:"More actions",triggerOn:[tn.Hover],$$slots:{default:[Xc]},$$scope:{ctx:n}}}),{c(){I(t.$$.fragment)},m(s,i){M(t,s,i),e=!0},p(s,i){const r={};4105&i&&(r.$$scope={dirty:i,ctx:s}),t.$set(r)},i(s){e||(_(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){D(t,s)}}}function Qc(n){let t;return{c(){t=Gt("Add")},m(e,s){F(e,t,s)},d(e){e&&C(t)}}}function Kc(n){let t;return{c(){t=Gt("Add Task After")},m(e,s){F(e,t,s)},d(e){e&&C(t)}}}function Jc(n){let t,e;return t=new Hc({props:{slot:"iconLeft"}}),{c(){I(t.$$.fragment)},m(s,i){M(t,s,i),e=!0},p:O,i(s){e||(_(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){D(t,s)}}}function tu(n){let t;return{c(){t=Gt("Add Child Task")},m(e,s){F(e,t,s)},d(e){e&&C(t)}}}function eu(n){let t,e;return t=new jc({props:{slot:"iconLeft"}}),{c(){I(t.$$.fragment)},m(s,i){M(t,s,i),e=!0},p:O,i(s){e||(_(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){D(t,s)}}}function su(n){let t;return{c(){t=Gt("Export")},m(e,s){F(e,t,s)},d(e){e&&C(t)}}}function nu(n){let t,e;return t=new Vt.Item({props:{onSelect:n[5],disabled:n[3]||!n[0],$$slots:{iconLeft:[ou],default:[ru]},$$scope:{ctx:n}}}),{c(){I(t.$$.fragment)},m(s,i){M(t,s,i),e=!0},p(s,i){const r={};9&i&&(r.disabled=s[3]||!s[0]),4096&i&&(r.$$scope={dirty:i,ctx:s}),t.$set(r)},i(s){e||(_(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){D(t,s)}}}function iu(n){let t,e;return t=new Vt.Item({props:{onSelect:n[6],disabled:n[3]||!n[0],$$slots:{iconLeft:[lu],default:[au]},$$scope:{ctx:n}}}),{c(){I(t.$$.fragment)},m(s,i){M(t,s,i),e=!0},p(s,i){const r={};9&i&&(r.disabled=s[3]||!s[0]),4096&i&&(r.$$scope={dirty:i,ctx:s}),t.$set(r)},i(s){e||(_(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){D(t,s)}}}function ru(n){let t;return{c(){t=Gt("Export Task")},m(e,s){F(e,t,s)},d(e){e&&C(t)}}}function ou(n){let t,e;return t=new bo({props:{slot:"iconLeft"}}),{c(){I(t.$$.fragment)},m(s,i){M(t,s,i),e=!0},p:O,i(s){e||(_(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){D(t,s)}}}function au(n){let t;return{c(){t=Gt("Export Task Tree")},m(e,s){F(e,t,s)},d(e){e&&C(t)}}}function lu(n){let t,e;return t=new bo({props:{slot:"iconLeft"}}),{c(){I(t.$$.fragment)},m(s,i){M(t,s,i),e=!0},p:O,i(s){e||(_(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){D(t,s)}}}function cu(n){let t,e,s,i,r,o,a,l,c,u,p,f,g,m;t=new Vt.Label({props:{$$slots:{default:[Qc]},$$scope:{ctx:n}}}),s=new Vt.Item({props:{onSelect:n[7],disabled:n[3]||!n[0],$$slots:{iconLeft:[Jc],default:[Kc]},$$scope:{ctx:n}}}),r=new Vt.Item({props:{onSelect:n[8],disabled:n[3]||!n[0],$$slots:{iconLeft:[eu],default:[tu]},$$scope:{ctx:n}}}),a=new Vt.Separator({}),c=new Vt.Label({props:{$$slots:{default:[su]},$$scope:{ctx:n}}});const v=[iu,nu],x=[];function y(b,$){return b[2]?0:1}return p=y(n),f=x[p]=v[p](n),{c(){I(t.$$.fragment),e=pt(),I(s.$$.fragment),i=pt(),I(r.$$.fragment),o=pt(),I(a.$$.fragment),l=pt(),I(c.$$.fragment),u=pt(),f.c(),g=ie()},m(b,$){M(t,b,$),F(b,e,$),M(s,b,$),F(b,i,$),M(r,b,$),F(b,o,$),M(a,b,$),F(b,l,$),M(c,b,$),F(b,u,$),x[p].m(b,$),F(b,g,$),m=!0},p(b,$){const P={};4096&$&&(P.$$scope={dirty:$,ctx:b}),t.$set(P);const S={};9&$&&(S.disabled=b[3]||!b[0]),4096&$&&(S.$$scope={dirty:$,ctx:b}),s.$set(S);const W={};9&$&&(W.disabled=b[3]||!b[0]),4096&$&&(W.$$scope={dirty:$,ctx:b}),r.$set(W);const G={};4096&$&&(G.$$scope={dirty:$,ctx:b}),c.$set(G);let R=p;p=y(b),p===R?x[p].p(b,$):(xt(),k(x[R],1,1,()=>{x[R]=null}),$t(),f=x[p],f?f.p(b,$):(f=x[p]=v[p](b),f.c()),_(f,1),f.m(g.parentNode,g))},i(b){m||(_(t.$$.fragment,b),_(s.$$.fragment,b),_(r.$$.fragment,b),_(a.$$.fragment,b),_(c.$$.fragment,b),_(f),m=!0)},o(b){k(t.$$.fragment,b),k(s.$$.fragment,b),k(r.$$.fragment,b),k(a.$$.fragment,b),k(c.$$.fragment,b),k(f),m=!1},d(b){b&&(C(e),C(i),C(o),C(l),C(u),C(g)),D(t,b),D(s,b),D(r,b),D(a,b),D(c,b),x[p].d(b)}}}function uu(n){let t,e,s,i;return t=new Vt.Trigger({props:{$$slots:{default:[Zc]},$$scope:{ctx:n}}}),s=new Vt.Content({props:{size:1,side:"bottom",align:"end",$$slots:{default:[cu]},$$scope:{ctx:n}}}),{c(){I(t.$$.fragment),e=pt(),I(s.$$.fragment)},m(r,o){M(t,r,o),F(r,e,o),M(s,r,o),i=!0},p(r,o){const a={};4105&o&&(a.$$scope={dirty:o,ctx:r}),t.$set(a);const l={};4109&o&&(l.$$scope={dirty:o,ctx:r}),s.$set(l)},i(r){i||(_(t.$$.fragment,r),_(s.$$.fragment,r),i=!0)},o(r){k(t.$$.fragment,r),k(s.$$.fragment,r),i=!1},d(r){r&&C(e),D(t,r),D(s,r)}}}function du(n){let t,e,s;function i(o){n[11](o)}let r={$$slots:{default:[uu]},$$scope:{ctx:n}};return n[1]!==void 0&&(r.requestClose=n[1]),t=new Vt.Root({props:r}),ue.push(()=>Ze(t,"requestClose",i)),{c(){I(t.$$.fragment)},m(o,a){M(t,o,a),s=!0},p(o,[a]){const l={};4109&a&&(l.$$scope={dirty:a,ctx:o}),!e&&2&a&&(e=!0,l.requestClose=o[1],Qe(()=>e=!1)),t.$set(l)},i(o){s||(_(t.$$.fragment,o),s=!0)},o(o){k(t.$$.fragment,o),s=!1},d(o){D(t,o)}}}function hu(n,t,e){let s,i,{task:r}=t,{taskStore:o}=t,{editable:a=!0}=t;const{isImportingExporting:l}=o;let c;return Vr(n,l,u=>e(3,i=u)),n.$$set=u=>{"task"in u&&e(9,r=u.task),"taskStore"in u&&e(10,o=u.taskStore),"editable"in u&&e(0,a=u.editable)},n.$$.update=()=>{512&n.$$.dirty&&e(2,s=r.subTasksData&&r.subTasksData.length>0)},[a,c,s,i,l,async function(){!i&&a&&(await o.exportTask(r,{baseName:r.name}),c==null||c())},async function(){!i&&a&&(await o.exportTask(r,{baseName:`${r.name}_tree`}),c==null||c())},async function(){if(i||!a)return;const u={uuid:crypto.randomUUID(),name:"",description:"",state:B.NOT_STARTED,subTasks:[],lastUpdated:Date.now(),lastUpdatedBy:kt.USER},p=await o.addNewTaskAfter(r.uuid,u);if(p){await Xs();const f=document.getElementById(`task-${p.uuid}`),g=f==null?void 0:f.querySelector(".c-task-tree-item__name-editable"),m=g==null?void 0:g.querySelector("input");m==null||m.focus()}c==null||c()},async function(){if(i||!a)return;const u=await o.createTask("","",r.uuid);if(u){await Xs();const p=document.getElementById(`task-${u}`),f=p==null?void 0:p.querySelector(".c-task-tree-item__name-editable"),g=f==null?void 0:f.querySelector("input");g==null||g.focus()}c==null||c()},r,o,function(u){c=u,e(1,c)}]}class pu extends tt{constructor(t){super(),et(this,t,hu,du,st,{task:9,taskStore:10,editable:0})}}function Dr(n){let t,e;return t=new sn({props:{content:"Run task",triggerOn:[tn.Hover],$$slots:{default:[gu]},$$scope:{ctx:n}}}),{c(){I(t.$$.fragment)},m(s,i){M(t,s,i),e=!0},p(s,i){const r={};16386&i&&(r.$$scope={dirty:i,ctx:s}),t.$set(r)},i(s){e||(_(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){D(t,s)}}}function fu(n){let t,e;return t=new oa({}),{c(){I(t.$$.fragment)},m(s,i){M(t,s,i),e=!0},i(s){e||(_(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){D(t,s)}}}function gu(n){let t,e;return t=new Ke({props:{size:1,variant:"ghost",color:"accent",disabled:!n[1],class:"c-task-action-button c-task-action-button--run",$$slots:{default:[fu]},$$scope:{ctx:n}}}),t.$on("click",n[7]),{c(){I(t.$$.fragment)},m(s,i){M(t,s,i),e=!0},p(s,i){const r={};2&i&&(r.disabled=!s[1]),16384&i&&(r.$$scope={dirty:i,ctx:s}),t.$set(r)},i(s){e||(_(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){D(t,s)}}}function Ar(n){let t,e;return t=new sn({props:{content:"Complete task",triggerOn:[tn.Hover],$$slots:{default:[bu]},$$scope:{ctx:n}}}),{c(){I(t.$$.fragment)},m(s,i){M(t,s,i),e=!0},p(s,i){const r={};16386&i&&(r.$$scope={dirty:i,ctx:s}),t.$set(r)},i(s){e||(_(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){D(t,s)}}}function mu(n){let t,e,s;var i=Zs(B.COMPLETE);return i&&(t=Es(i,{})),{c(){t&&I(t.$$.fragment),e=ie()},m(r,o){t&&M(t,r,o),F(r,e,o),s=!0},p(r,o){if(i!==(i=Zs(B.COMPLETE))){if(t){xt();const a=t;k(a.$$.fragment,1,0,()=>{D(a,1)}),$t()}i?(t=Es(i,{}),I(t.$$.fragment),_(t.$$.fragment,1),M(t,e.parentNode,e)):t=null}},i(r){s||(t&&_(t.$$.fragment,r),s=!0)},o(r){t&&k(t.$$.fragment,r),s=!1},d(r){r&&C(e),t&&D(t,r)}}}function bu(n){let t,e;return t=new Ke({props:{size:1,variant:"ghost",color:"success",disabled:!n[1],class:"c-task-action-button c-task-action-button--complete",$$slots:{default:[mu]},$$scope:{ctx:n}}}),t.$on("click",n[8]),{c(){I(t.$$.fragment)},m(s,i){M(t,s,i),e=!0},p(s,i){const r={};2&i&&(r.disabled=!s[1]),16384&i&&(r.$$scope={dirty:i,ctx:s}),t.$set(r)},i(s){e||(_(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){D(t,s)}}}function Rr(n){let t,e;return t=new sn({props:{content:"Mark as Todo",triggerOn:[tn.Hover],$$slots:{default:[_u]},$$scope:{ctx:n}}}),{c(){I(t.$$.fragment)},m(s,i){M(t,s,i),e=!0},p(s,i){const r={};16386&i&&(r.$$scope={dirty:i,ctx:s}),t.$set(r)},i(s){e||(_(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){D(t,s)}}}function vu(n){let t,e,s;var i=Zs(B.NOT_STARTED);return i&&(t=Es(i,{})),{c(){t&&I(t.$$.fragment),e=ie()},m(r,o){t&&M(t,r,o),F(r,e,o),s=!0},p(r,o){if(i!==(i=Zs(B.NOT_STARTED))){if(t){xt();const a=t;k(a.$$.fragment,1,0,()=>{D(a,1)}),$t()}i?(t=Es(i,{}),I(t.$$.fragment),_(t.$$.fragment,1),M(t,e.parentNode,e)):t=null}},i(r){s||(t&&_(t.$$.fragment,r),s=!0)},o(r){t&&k(t.$$.fragment,r),s=!1},d(r){r&&C(e),t&&D(t,r)}}}function _u(n){let t,e;return t=new Ke({props:{size:1,variant:"ghost",color:"accent",disabled:!n[1],class:"c-task-action-button c-task-action-button--complete",$$slots:{default:[vu]},$$scope:{ctx:n}}}),t.$on("click",n[9]),{c(){I(t.$$.fragment)},m(s,i){M(t,s,i),e=!0},p(s,i){const r={};2&i&&(r.disabled=!s[1]),16384&i&&(r.$$scope={dirty:i,ctx:s}),t.$set(r)},i(s){e||(_(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){D(t,s)}}}function yu(n){let t,e;return t=new sa({}),{c(){I(t.$$.fragment)},m(s,i){M(t,s,i),e=!0},i(s){e||(_(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){D(t,s)}}}function wu(n){let t,e;return t=new Ke({props:{size:1,variant:"ghost",color:"error",disabled:!n[1],class:"c-task-action-button c-task-action-button--delete",$$slots:{default:[yu]},$$scope:{ctx:n}}}),t.$on("click",n[10]),{c(){I(t.$$.fragment)},m(s,i){M(t,s,i),e=!0},p(s,i){const r={};2&i&&(r.disabled=!s[1]),16384&i&&(r.$$scope={dirty:i,ctx:s}),t.$set(r)},i(s){e||(_(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){D(t,s)}}}function Fr(n){let t,e;return t=new pu({props:{task:n[5],taskStore:n[0],editable:n[1]}}),{c(){I(t.$$.fragment)},m(s,i){M(t,s,i),e=!0},p(s,i){const r={};32&i&&(r.task=s[5]),1&i&&(r.taskStore=s[0]),2&i&&(r.editable=s[1]),t.$set(r)},i(s){e||(_(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){D(t,s)}}}function ku(n){let t,e,s,i,r,o,a,l=n[4]&&Dr(n),c=n[3]&&Ar(n),u=n[2]&&Rr(n);r=new sn({props:{content:"Delete task",triggerOn:[tn.Hover],$$slots:{default:[wu]},$$scope:{ctx:n}}});let p=n[5]&&Fr(n);return{c(){t=J("div"),l&&l.c(),e=pt(),c&&c.c(),s=pt(),u&&u.c(),i=pt(),I(r.$$.fragment),o=pt(),p&&p.c(),w(t,"class","c-task-action-buttons svelte-55fcbs")},m(f,g){F(f,t,g),l&&l.m(t,null),V(t,e),c&&c.m(t,null),V(t,s),u&&u.m(t,null),V(t,i),M(r,t,null),V(t,o),p&&p.m(t,null),a=!0},p(f,[g]){f[4]?l?(l.p(f,g),16&g&&_(l,1)):(l=Dr(f),l.c(),_(l,1),l.m(t,e)):l&&(xt(),k(l,1,1,()=>{l=null}),$t()),f[3]?c?(c.p(f,g),8&g&&_(c,1)):(c=Ar(f),c.c(),_(c,1),c.m(t,s)):c&&(xt(),k(c,1,1,()=>{c=null}),$t()),f[2]?u?(u.p(f,g),4&g&&_(u,1)):(u=Rr(f),u.c(),_(u,1),u.m(t,i)):u&&(xt(),k(u,1,1,()=>{u=null}),$t());const m={};16386&g&&(m.$$scope={dirty:g,ctx:f}),r.$set(m),f[5]?p?(p.p(f,g),32&g&&_(p,1)):(p=Fr(f),p.c(),_(p,1),p.m(t,null)):p&&(xt(),k(p,1,1,()=>{p=null}),$t())},i(f){a||(_(l),_(c),_(u),_(r.$$.fragment,f),_(p),a=!0)},o(f){k(l),k(c),k(u),k(r.$$.fragment,f),k(p),a=!1},d(f){f&&C(t),l&&l.d(),c&&c.d(),u&&u.d(),D(r),p&&p.d()}}}function xu(n,t,e){let s,i,r,o,a,l,c=O;n.$$.on_destroy.push(()=>c());let{taskUuid:u}=t,{taskState:p}=t,{taskStore:f}=t,{editable:g=!0}=t;return n.$$set=m=>{"taskUuid"in m&&e(11,u=m.taskUuid),"taskState"in m&&e(12,p=m.taskState),"taskStore"in m&&e(0,f=m.taskStore),"editable"in m&&e(1,g=m.editable)},n.$$.update=()=>{1&n.$$.dirty&&(e(6,s=f.uuidToTask),c(),c=Uo(s,m=>e(13,l=m))),10240&n.$$.dirty&&e(5,i=l.get(u)),4096&n.$$.dirty&&e(4,r=p===B.NOT_STARTED),4096&n.$$.dirty&&e(3,o=p===B.IN_PROGRESS),4096&n.$$.dirty&&e(2,a=p===B.COMPLETE||p===B.CANCELLED)},[f,g,a,o,r,i,s,async function(){if(!g)return;const m=l.get(u);m&&(await f.updateTask(u,{state:B.IN_PROGRESS},kt.USER),await f.runHydratedTask(m))},async function(){g&&await f.updateTask(u,{state:B.COMPLETE},kt.USER)},async function(){g&&await f.updateTask(u,{state:B.NOT_STARTED},kt.USER)},async function(){g&&await f.deleteTask(u)},u,p,l]}class $u extends tt{constructor(t){super(),et(this,t,xu,ku,st,{taskUuid:11,taskState:12,taskStore:0,editable:1})}}function Su(n,t=new Set){const e={...n,isVisible:!1,subTasksData:[]};let s=!1;if(n.subTasksData&&n.subTasksData.length>0){const r=[];for(const o of n.subTasksData){const a=Su(o,t);r.push(a),a.isVisible&&(s=!0)}e.subTasksData=r}const i=function(r,o){return o.size===0||o.has(r.state)}(n,t);return e.isVisible=i||s,e}function Nr(n,t,e){const s=n.slice();return s[19]=t[e],s}function Pr(n,t,e){const s=n.slice();return s[19]=t[e],s}function Tu(n){let t,e,s,i;function r(l){n[14](l)}function o(l){n[15](l)}let a={class:"c-task-tree-item",item:n[0],id:`task-${n[0].uuid}`,hasNestedItems:!!n[0].subTasksData&&n[0].subTasksData.length>0,disabled:!n[2],$$slots:{contents:[Au],actions:[Mu],"header-contents":[Iu],handle:[Eu]},$$scope:{ctx:n}};return n[5]!==void 0&&(a.element=n[5]),n[6]!==void 0&&(a.expanded=n[6]),t=new ic({props:a}),ue.push(()=>Ze(t,"element",r)),ue.push(()=>Ze(t,"expanded",o)),{c(){I(t.$$.fragment)},m(l,c){M(t,l,c),i=!0},p(l,c){const u={};1&c&&(u.item=l[0]),1&c&&(u.id=`task-${l[0].uuid}`),1&c&&(u.hasNestedItems=!!l[0].subTasksData&&l[0].subTasksData.length>0),4&c&&(u.disabled=!l[2]),16777687&c&&(u.$$scope={dirty:c,ctx:l}),!e&&32&c&&(e=!0,u.element=l[5],Qe(()=>e=!1)),!s&&64&c&&(s=!0,u.expanded=l[6],Qe(()=>s=!1)),t.$set(u)},i(l){i||(_(t.$$.fragment,l),i=!0)},o(l){k(t.$$.fragment,l),i=!1},d(l){D(t,l)}}}function Cu(n){let t,e,s;return e=new Eo({props:{id:`list-${n[0].uuid}`,items:n[7],onEnd:n[12],disabled:!n[2],$$slots:{items:[Ru,({items:i})=>({18:i}),({items:i})=>i?262144:0]},$$scope:{ctx:n}}}),{c(){t=J("div"),I(e.$$.fragment),w(t,"class","c-task-tree-root-children svelte-1xjgc45")},m(i,r){F(i,t,r),M(e,t,null),s=!0},p(i,r){const o={};1&r&&(o.id=`list-${i[0].uuid}`),128&r&&(o.items=i[7]),4&r&&(o.disabled=!i[2]),17039366&r&&(o.$$scope={dirty:r,ctx:i}),e.$set(o)},i(i){s||(_(e.$$.fragment,i),s=!0)},o(i){k(e.$$.fragment,i),s=!1},d(i){i&&C(t),D(e)}}}function Eu(n){let t,e;return t=new ac({props:{slot:"handle",width:"8px",height:"10px"}}),{c(){I(t.$$.fragment)},m(s,i){M(t,s,i),e=!0},p:O,i(s){e||(_(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){D(t,s)}}}function Iu(n){let t,e,s,i,r,o,a;s=new Pc({props:{taskState:n[0].state,taskUuid:n[0].uuid,taskStore:n[1],disabled:!n[2],size:1}});let l={class:"c-task-tree-item__name-editable",value:n[0].name,placeholder:"Name this task...",size:1,disabled:!n[2],clickToEdit:!0};return o=new Uc({props:l}),n[13](o),o.$on("keydown",n[11]),o.$on("blur",n[10]),{c(){t=J("div"),e=J("div"),I(s.$$.fragment),i=pt(),r=J("div"),I(o.$$.fragment),w(e,"class","c-task-tree-item__status-cell svelte-1xjgc45"),w(r,"class","c-task-tree-item__name svelte-1xjgc45"),le(r,"c-task-tree-item__text--cancelled",n[8]),w(t,"slot","header-contents"),w(t,"class","c-task-tree-item__header svelte-1xjgc45")},m(c,u){F(c,t,u),V(t,e),M(s,e,null),V(t,i),V(t,r),M(o,r,null),a=!0},p(c,u){const p={};1&u&&(p.taskState=c[0].state),1&u&&(p.taskUuid=c[0].uuid),2&u&&(p.taskStore=c[1]),4&u&&(p.disabled=!c[2]),s.$set(p);const f={};1&u&&(f.value=c[0].name),4&u&&(f.disabled=!c[2]),o.$set(f),(!a||256&u)&&le(r,"c-task-tree-item__text--cancelled",c[8])},i(c){a||(_(s.$$.fragment,c),_(o.$$.fragment,c),a=!0)},o(c){k(s.$$.fragment,c),k(o.$$.fragment,c),a=!1},d(c){c&&C(t),D(s),n[13](null),D(o)}}}function Mu(n){let t,e,s;return e=new $u({props:{taskUuid:n[0].uuid,taskState:n[0].state,taskStore:n[1],editable:n[2]}}),{c(){t=J("div"),I(e.$$.fragment),w(t,"class","c-task-tree-item__action-buttons svelte-1xjgc45"),w(t,"slot","actions")},m(i,r){F(i,t,r),M(e,t,null),s=!0},p(i,r){const o={};1&r&&(o.taskUuid=i[0].uuid),1&r&&(o.taskState=i[0].state),2&r&&(o.taskStore=i[1]),4&r&&(o.editable=i[2]),e.$set(o)},i(i){s||(_(e.$$.fragment,i),s=!0)},o(i){k(e.$$.fragment,i),s=!1},d(i){i&&C(t),D(e)}}}function Or(n){let t,e,s;return e=new Eo({props:{id:`list-${n[0].uuid}`,items:n[7],disabled:!n[2],useHandle:!1,onEnd:n[12],$$slots:{items:[Du,({items:i})=>({18:i}),({items:i})=>i?262144:0]},$$scope:{ctx:n}}}),{c(){t=J("div"),I(e.$$.fragment),w(t,"class","c-task-tree-item__subtasks")},m(i,r){F(i,t,r),M(e,t,null),s=!0},p(i,r){const o={};1&r&&(o.id=`list-${i[0].uuid}`),128&r&&(o.items=i[7]),4&r&&(o.disabled=!i[2]),17039366&r&&(o.$$scope={dirty:r,ctx:i}),e.$set(o)},i(i){s||(_(e.$$.fragment,i),s=!0)},o(i){k(e.$$.fragment,i),s=!1},d(i){i&&C(t),D(e)}}}function Lr(n,t){let e,s,i;return s=new Io({props:{taskStore:t[1],task:t[19],editable:t[2],isRootTask:!1}}),{key:n,first:null,c(){e=ie(),I(s.$$.fragment),this.first=e},m(r,o){F(r,e,o),M(s,r,o),i=!0},p(r,o){t=r;const a={};2&o&&(a.taskStore=t[1]),262144&o&&(a.task=t[19]),4&o&&(a.editable=t[2]),s.$set(a)},i(r){i||(_(s.$$.fragment,r),i=!0)},o(r){k(s.$$.fragment,r),i=!1},d(r){r&&C(e),D(s,r)}}}function Du(n){let t,e,s=[],i=new Map,r=Cs(n[18]);const o=a=>a[19].uuid;for(let a=0;a<r.length;a+=1){let l=Nr(n,r,a),c=o(l);i.set(c,s[a]=Lr(c,l))}return{c(){for(let a=0;a<s.length;a+=1)s[a].c();t=ie()},m(a,l){for(let c=0;c<s.length;c+=1)s[c]&&s[c].m(a,l);F(a,t,l),e=!0},p(a,l){262150&l&&(r=Cs(a[18]),xt(),s=jr(s,l,o,1,a,r,i,t.parentNode,Wr,Lr,t,Nr),$t())},i(a){if(!e){for(let l=0;l<r.length;l+=1)_(s[l]);e=!0}},o(a){for(let l=0;l<s.length;l+=1)k(s[l]);e=!1},d(a){a&&C(t);for(let l=0;l<s.length;l+=1)s[l].d(a)}}}function Au(n){let t,e,s,i,r;e=new Ec({props:{task:n[0],taskStore:n[1],editable:n[2]}});let o=n[7]&&n[7].length>0&&n[6]&&Or(n);return{c(){t=J("div"),I(e.$$.fragment),s=pt(),o&&o.c(),i=ie(),w(t,"class","c-task-tree-item__details")},m(a,l){F(a,t,l),M(e,t,null),F(a,s,l),o&&o.m(a,l),F(a,i,l),r=!0},p(a,l){const c={};1&l&&(c.task=a[0]),2&l&&(c.taskStore=a[1]),4&l&&(c.editable=a[2]),e.$set(c),a[7]&&a[7].length>0&&a[6]?o?(o.p(a,l),192&l&&_(o,1)):(o=Or(a),o.c(),_(o,1),o.m(i.parentNode,i)):o&&(xt(),k(o,1,1,()=>{o=null}),$t())},i(a){r||(_(e.$$.fragment,a),_(o),r=!0)},o(a){k(e.$$.fragment,a),k(o),r=!1},d(a){a&&(C(t),C(s),C(i)),D(e),o&&o.d(a)}}}function Ur(n,t){let e,s,i;return s=new Io({props:{taskStore:t[1],task:t[19],editable:t[2],isRootTask:!1}}),{key:n,first:null,c(){e=ie(),I(s.$$.fragment),this.first=e},m(r,o){F(r,e,o),M(s,r,o),i=!0},p(r,o){t=r;const a={};2&o&&(a.taskStore=t[1]),262144&o&&(a.task=t[19]),4&o&&(a.editable=t[2]),s.$set(a)},i(r){i||(_(s.$$.fragment,r),i=!0)},o(r){k(s.$$.fragment,r),i=!1},d(r){r&&C(e),D(s,r)}}}function Ru(n){let t,e,s=[],i=new Map,r=Cs(n[18]);const o=a=>a[19].uuid;for(let a=0;a<r.length;a+=1){let l=Pr(n,r,a),c=o(l);i.set(c,s[a]=Ur(c,l))}return{c(){for(let a=0;a<s.length;a+=1)s[a].c();t=ie()},m(a,l){for(let c=0;c<s.length;c+=1)s[c]&&s[c].m(a,l);F(a,t,l),e=!0},p(a,l){262150&l&&(r=Cs(a[18]),xt(),s=jr(s,l,o,1,a,r,i,t.parentNode,Wr,Ur,t,Pr),$t())},i(a){if(!e){for(let l=0;l<r.length;l+=1)_(s[l]);e=!0}},o(a){for(let l=0;l<s.length;l+=1)k(s[l]);e=!1},d(a){a&&C(t);for(let l=0;l<s.length;l+=1)s[l].d(a)}}}function Fu(n){let t,e,s,i;const r=[Cu,Tu],o=[];function a(l,c){return l[3]?0:l[0].isVisible?1:-1}return~(t=a(n))&&(e=o[t]=r[t](n)),{c(){e&&e.c(),s=ie()},m(l,c){~t&&o[t].m(l,c),F(l,s,c),i=!0},p(l,[c]){let u=t;t=a(l),t===u?~t&&o[t].p(l,c):(e&&(xt(),k(o[u],1,1,()=>{o[u]=null}),$t()),~t?(e=o[t],e?e.p(l,c):(e=o[t]=r[t](l),e.c()),_(e,1),e.m(s.parentNode,s)):e=null)},i(l){i||(_(e),i=!0)},o(l){k(e),i=!1},d(l){l&&C(s),~t&&o[t].d(l)}}}function Nu(n,t,e){let s,i,r,{task:o}=t,{taskStore:a=zo(gi.key)}=t,{editable:l=!0}=t,{isRootTask:c=!0}=t;const{uuidToTask:u}=a;let p,f;Vr(n,u,v=>e(16,r=v));let g=!1;async function m(v){const x=v.trim();x!==o.name&&x&&await a.updateTask(o.uuid,{name:x},kt.USER)}return n.$$set=v=>{"task"in v&&e(0,o=v.task),"taskStore"in v&&e(1,a=v.taskStore),"editable"in v&&e(2,l=v.editable),"isRootTask"in v&&e(3,c=v.isRootTask)},n.$$.update=()=>{1&n.$$.dirty&&e(8,s=o.state===B.CANCELLED),1&n.$$.dirty&&e(7,i=function(v){var x;return((x=v.subTasksData)==null?void 0:x.filter(y=>y.isVisible))||[]}(o))},[o,a,l,c,p,f,g,i,s,u,async function(v){var y;const x=(y=v.target)==null?void 0:y.value;await m(x||"")},async function(v){var x,y;if(!(v.shiftKey||v.ctrlKey||v.metaKey))switch(v.key){case"Enter":{const b=(x=v.target)==null?void 0:x.value;await m(b||"");const $=await a.addNewTaskAfter(o.uuid,{uuid:"new-task",name:"",description:"",state:B.NOT_STARTED,subTasks:[],lastUpdated:Date.now(),lastUpdatedBy:kt.USER});if(!$)return;await Xs();const P=document.getElementById(`task-${$.uuid}`);if(!P)return;const S=P.querySelector(".c-task-tree-item__name-editable");if(!S)return;const W=S.querySelector("input");if(!W)return;W.focus(),W.select();break}case"Tab":{const b=(y=v.target)==null?void 0:y.value;await m(b||"");break}}},async v=>{var W,G,R,z,re,Y,ot,It;if(!v.from.id||!v.to.id||!v.item.id||v.oldIndex===void 0||v.newIndex===void 0)return;const{fromParentTaskUuid:x,toParentTaskUuid:y,targetUuid:b}=function(Nt){return{fromParentTaskUuid:Nt.from.id.replace(/^list-/,""),toParentTaskUuid:Nt.to.id.replace(/^list-/,""),targetUuid:Nt.item.id.replace(/^task-/,"")}}(v);if(x===y&&v.oldIndex===v.newIndex)return;const $=r.get(x),P=r.get(y),S=r.get(b);return $&&P&&S?x!==y?(v.from.appendChild(v.item),(W=$==null?void 0:$.subTasks)==null||W.splice(v.oldIndex,1),(G=$==null?void 0:$.subTasksData)==null||G.splice(v.oldIndex,1),(R=P==null?void 0:P.subTasks)==null||R.splice(v.newIndex,0,b),(z=P==null?void 0:P.subTasksData)==null||z.splice(v.newIndex,0,S),await a.updateTask($.uuid,{subTasks:$.subTasks},kt.USER),void await a.updateTask(P.uuid,{subTasks:P.subTasks},kt.USER)):((re=$==null?void 0:$.subTasks)==null||re.splice(v.oldIndex,1),(Y=$==null?void 0:$.subTasks)==null||Y.splice(v.newIndex,0,b),(ot=$==null?void 0:$.subTasksData)==null||ot.splice(v.oldIndex,1),(It=$==null?void 0:$.subTasksData)==null||It.splice(v.newIndex,0,S),void await a.updateTask($.uuid,{subTasks:$.subTasks},kt.USER)):void 0},function(v){ue[v?"unshift":"push"](()=>{p=v,e(4,p)})},function(v){f=v,e(5,f)},function(v){g=v,e(6,g)}]}class Io extends tt{constructor(t){super(),et(this,t,Nu,Fu,st,{task:0,taskStore:1,editable:2,isRootTask:3})}}function Pu(n){let t,e;return{c(){t=ut("svg"),e=ut("path"),w(e,"fill-rule","evenodd"),w(e,"clip-rule","evenodd"),w(e,"d","M1.90321 7.29677C1.90321 10.341 4.11041 12.4147 6.58893 12.8439C6.87255 12.893 7.06266 13.1627 7.01355 13.4464C6.96444 13.73 6.69471 13.9201 6.41109 13.871C3.49942 13.3668 0.86084 10.9127 0.86084 7.29677C0.860839 5.76009 1.55996 4.55245 2.37639 3.63377C2.96124 2.97568 3.63034 2.44135 4.16846 2.03202L2.53205 2.03202C2.25591 2.03202 2.03205 1.80816 2.03205 1.53202C2.03205 1.25588 2.25591 1.03202 2.53205 1.03202L5.53205 1.03202C5.80819 1.03202 6.03205 1.25588 6.03205 1.53202L6.03205 4.53202C6.03205 4.80816 5.80819 5.03202 5.53205 5.03202C5.25591 5.03202 5.03205 4.80816 5.03205 4.53202L5.03205 2.68645L5.03054 2.68759L5.03045 2.68766L5.03044 2.68767L5.03043 2.68767C4.45896 3.11868 3.76059 3.64538 3.15554 4.3262C2.44102 5.13021 1.90321 6.10154 1.90321 7.29677ZM13.0109 7.70321C13.0109 4.69115 10.8505 2.6296 8.40384 2.17029C8.12093 2.11718 7.93465 1.84479 7.98776 1.56188C8.04087 1.27898 8.31326 1.0927 8.59616 1.14581C11.4704 1.68541 14.0532 4.12605 14.0532 7.70321C14.0532 9.23988 13.3541 10.4475 12.5377 11.3662C11.9528 12.0243 11.2837 12.5586 10.7456 12.968L12.3821 12.968C12.6582 12.968 12.8821 13.1918 12.8821 13.468C12.8821 13.7441 12.6582 13.968 12.3821 13.968L9.38205 13.968C9.10591 13.968 8.88205 13.7441 8.88205 13.468L8.88205 10.468C8.88205 10.1918 9.10591 9.96796 9.38205 9.96796C9.65819 9.96796 9.88205 10.1918 9.88205 10.468L9.88205 12.3135L9.88362 12.3123C10.4551 11.8813 11.1535 11.3546 11.7585 10.6738C12.4731 9.86976 13.0109 8.89844 13.0109 7.70321Z"),w(e,"fill","currentColor"),w(t,"width","15"),w(t,"height","15"),w(t,"viewBox","0 0 15 15"),w(t,"fill","none"),w(t,"xmlns","http://www.w3.org/2000/svg")},m(s,i){F(s,t,i),V(t,e)},p:O,i:O,o:O,d(s){s&&C(t)}}}class xd extends tt{constructor(t){super(),et(this,t,null,Pu,st,{})}}class $d{static generateDiff(t,e,s,i){return aa(t,e,s,i)}static generateDiffs(t){return la(t)}static getDiffStats(t){return qi(t)}static getDiffObjectStats(t){return qi(t.diff)}static isNewFile(t){return ca(t)}static isDeletedFile(t){return ua(t)}}function Ou(n){let t,e;return{c(){t=ut("svg"),e=ut("path"),w(e,"d","M14.5 3H7.70996L6.85999 2.15002L6.51001 2H1.51001L1.01001 2.5V6.5V13.5L1.51001 14H14.51L15.01 13.5V9V3.5L14.5 3ZM13.99 11.49V13H1.98999V11.49V7.48999V7H6.47998L6.82996 6.84998L7.68994 5.98999H14V7.48999L13.99 11.49ZM13.99 5H7.48999L7.14001 5.15002L6.28003 6.01001H2V3.01001H6.29004L7.14001 3.85999L7.5 4.01001H14L13.99 5Z"),w(e,"fill","#C5C5C5"),w(t,"width","16"),w(t,"height","16"),w(t,"viewBox","0 0 16 16"),w(t,"fill","none"),w(t,"xmlns","http://www.w3.org/2000/svg")},m(s,i){F(s,t,i),V(t,e)},p:O,i:O,o:O,d(s){s&&C(t)}}}class Sd extends tt{constructor(t){super(),et(this,t,null,Ou,st,{})}}export{yd as $,Ya as A,kd as B,Va as C,Ju as D,bd as E,bo as F,Za as G,Vl as H,Ii as I,B as J,Su as K,_d as L,Io as M,zt as N,$d as O,ad as P,fd as Q,ha as R,od as S,ud as T,xd as U,Sd as V,Qr as W,co as X,ol as Y,wd as Z,kt as _,dd as a,nr as a0,ll as b,ed as c,ct as d,td as e,On as f,sd as g,nd as h,ys as i,Ga as j,id as k,cd as l,Ct as m,ld as n,da as o,hd as p,pd as q,rd as r,gd as s,md as t,vd as u,gi as v,Dn as w,El as x,mi as y,cr as z};

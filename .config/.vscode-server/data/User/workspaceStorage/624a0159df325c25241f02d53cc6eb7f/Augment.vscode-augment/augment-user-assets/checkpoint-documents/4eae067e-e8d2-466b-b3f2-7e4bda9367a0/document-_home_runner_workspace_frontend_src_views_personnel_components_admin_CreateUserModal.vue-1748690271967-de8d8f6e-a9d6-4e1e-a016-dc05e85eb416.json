{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/personnel/components/admin/CreateUserModal.vue"}, "modifiedCode": "<template>\n  <!-- <PERSON><PERSON> Backdrop -->\n  <div class=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\" @click=\"$emit('close')\">\n    <!-- Modal Container -->\n    <div class=\"relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white dark:bg-gray-800\" @click.stop>\n      <!-- <PERSON><PERSON> Header -->\n      <div class=\"flex items-center justify-between pb-4 border-b border-gray-200 dark:border-gray-700\">\n        <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n          Crea Nuovo Dipendente\n        </h3>\n        <button @click=\"$emit('close')\" class=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\">\n          <svg class=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\"></path>\n          </svg>\n        </button>\n      </div>\n\n      <!-- Modal Body -->\n      <div class=\"mt-6\">\n        <form @submit.prevent=\"createUser\" class=\"space-y-6\">\n          <!-- Basic Information -->\n          <div>\n            <h4 class=\"text-md font-medium text-gray-900 dark:text-white mb-4\">Informazioni Base</h4>\n            <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Nome *\n                </label>\n                <input v-model=\"form.first_name\"\n                       type=\"text\"\n                       required\n                       class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\">\n              </div>\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Cognome *\n                </label>\n                <input v-model=\"form.last_name\"\n                       type=\"text\"\n                       required\n                       class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\">\n              </div>\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Username *\n                </label>\n                <input v-model=\"form.username\"\n                       type=\"text\"\n                       required\n                       class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\">\n              </div>\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Email *\n                </label>\n                <input v-model=\"form.email\"\n                       type=\"email\"\n                       required\n                       class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\">\n              </div>\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Telefono\n                </label>\n                <input v-model=\"form.phone\"\n                       type=\"tel\"\n                       class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\">\n              </div>\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Posizione\n                </label>\n                <input v-model=\"form.position\"\n                       type=\"text\"\n                       class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\">\n              </div>\n            </div>\n          </div>\n\n          <!-- Role and Department -->\n          <div>\n            <h4 class=\"text-md font-medium text-gray-900 dark:text-white mb-4\">Ruolo e Dipartimento</h4>\n            <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Ruolo\n                </label>\n                <select v-model=\"form.role\"\n                        class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\">\n                  <option value=\"employee\">Dipendente</option>\n                  <option value=\"manager\">Manager</option>\n                  <option value=\"admin\">Admin</option>\n                </select>\n              </div>\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Dipartimento\n                </label>\n                <select v-model=\"form.department_id\"\n                        class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\">\n                  <option value=\"\">Seleziona dipartimento</option>\n                  <option v-for=\"dept in departments\" :key=\"dept.id\" :value=\"dept.id\">\n                    {{ dept.name }}\n                  </option>\n                </select>\n              </div>\n            </div>\n          </div>\n\n          <!-- Contract Information -->\n          <div>\n            <h4 class=\"text-md font-medium text-gray-900 dark:text-white mb-4\">Informazioni Contrattuali</h4>\n            <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Data Assunzione\n                </label>\n                <input v-model=\"form.hire_date\"\n                       type=\"date\"\n                       class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\">\n              </div>\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Tipo Contratto\n                </label>\n                <select v-model=\"form.employment_type\"\n                        class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\">\n                  <option value=\"full_time\">Tempo pieno</option>\n                  <option value=\"part_time\">Part-time</option>\n                  <option value=\"contractor\">Consulente</option>\n                  <option value=\"intern\">Stagista</option>\n                </select>\n              </div>\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Modalità Lavoro\n                </label>\n                <select v-model=\"form.work_location\"\n                        class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\">\n                  <option value=\"\">Seleziona modalità</option>\n                  <option value=\"office\">Ufficio</option>\n                  <option value=\"remote\">Remoto</option>\n                  <option value=\"hybrid\">Ibrido</option>\n                </select>\n              </div>\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Ore Settimanali\n                </label>\n                <input v-model.number=\"form.weekly_hours\"\n                       type=\"number\"\n                       step=\"0.5\"\n                       min=\"0\"\n                       max=\"60\"\n                       class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\">\n              </div>\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Fine Periodo Prova\n                </label>\n                <input v-model=\"form.probation_end_date\"\n                       type=\"date\"\n                       class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\">\n              </div>\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Scadenza Contratto\n                </label>\n                <input v-model=\"form.contract_end_date\"\n                       type=\"date\"\n                       class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\">\n              </div>\n            </div>\n          </div>\n\n          <!-- Salary Information -->\n          <div>\n            <h4 class=\"text-md font-medium text-gray-900 dark:text-white mb-4\">Informazioni Economiche</h4>\n            <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Stipendio Annuo\n                </label>\n                <input v-model.number=\"form.salary\"\n                       type=\"number\"\n                       step=\"100\"\n                       min=\"0\"\n                       class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\">\n              </div>\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Valuta\n                </label>\n                <select v-model=\"form.salary_currency\"\n                        class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\">\n                  <option value=\"EUR\">EUR</option>\n                  <option value=\"USD\">USD</option>\n                  <option value=\"GBP\">GBP</option>\n                </select>\n              </div>\n            </div>\n          </div>\n\n          <!-- Emergency Contact -->\n          <div>\n            <h4 class=\"text-md font-medium text-gray-900 dark:text-white mb-4\">Contatto di Emergenza</h4>\n            <div class=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Nome Contatto\n                </label>\n                <input v-model=\"form.emergency_contact_name\"\n                       type=\"text\"\n                       class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\">\n              </div>\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Telefono Emergenza\n                </label>\n                <input v-model=\"form.emergency_contact_phone\"\n                       type=\"tel\"\n                       class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\">\n              </div>\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Relazione\n                </label>\n                <input v-model=\"form.emergency_contact_relationship\"\n                       type=\"text\"\n                       placeholder=\"es. Coniuge, Genitore...\"\n                       class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\">\n              </div>\n            </div>\n          </div>\n\n          <!-- Password Option -->\n          <div>\n            <h4 class=\"text-md font-medium text-gray-900 dark:text-white mb-4\">Password</h4>\n            <div class=\"flex items-center space-x-4\">\n              <label class=\"flex items-center\">\n                <input v-model=\"generatePassword\"\n                       type=\"checkbox\"\n                       class=\"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50\">\n                <span class=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">\n                  Genera password temporanea automaticamente\n                </span>\n              </label>\n            </div>\n            <div v-if=\"!generatePassword\" class=\"mt-3\">\n              <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                Password Personalizzata\n              </label>\n              <input v-model=\"form.password\"\n                     type=\"password\"\n                     class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\">\n            </div>\n          </div>\n\n          <!-- Error Message -->\n          <div v-if=\"error\" class=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4\">\n            <div class=\"flex\">\n              <svg class=\"w-5 h-5 text-red-400 mr-2 mt-0.5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clip-rule=\"evenodd\"></path>\n              </svg>\n              <div>\n                <h3 class=\"text-sm font-medium text-red-800 dark:text-red-200\">Errore nella creazione</h3>\n                <p class=\"text-sm text-red-700 dark:text-red-300 mt-1\">{{ error }}</p>\n              </div>\n            </div>\n          </div>\n\n          <!-- Modal Footer -->\n          <div class=\"flex items-center justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700\">\n            <button type=\"button\"\n                    @click=\"$emit('close')\"\n                    class=\"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\">\n              Annulla\n            </button>\n            <button type=\"submit\"\n                    :disabled=\"loading\"\n                    class=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50\">\n              <svg v-if=\"loading\" class=\"animate-spin -ml-1 mr-3 h-4 w-4 text-white inline\" fill=\"none\" viewBox=\"0 0 24 24\">\n                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\"></circle>\n                <path class=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n              </svg>\n              {{ loading ? 'Creazione...' : 'Crea Dipendente' }}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  </div>\n</template>\n"}
{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/personnel/components/admin/DepartmentModal.vue"}, "modifiedCode": "<template>\n  <!-- <PERSON><PERSON> Backdrop -->\n  <div class=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\" @click=\"$emit('close')\">\n    <!-- Modal Container -->\n    <div class=\"relative top-20 mx-auto p-5 border w-11/12 max-w-2xl shadow-lg rounded-md bg-white dark:bg-gray-800\" @click.stop>\n      <!-- <PERSON><PERSON> Header -->\n      <div class=\"flex items-center justify-between pb-4 border-b border-gray-200 dark:border-gray-700\">\n        <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n          {{ department ? 'Modifica Dipartimento' : 'Nuovo Dipartimento' }}\n        </h3>\n        <button @click=\"$emit('close')\" class=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\">\n          <svg class=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\"></path>\n          </svg>\n        </button>\n      </div>\n\n      <!-- Modal Body -->\n      <div class=\"mt-6\">\n        <form @submit.prevent=\"saveDepartment\" class=\"space-y-6\">\n          <!-- Basic Information -->\n          <div>\n            <h4 class=\"text-md font-medium text-gray-900 dark:text-white mb-4\">Informazioni Base</h4>\n            <div class=\"grid grid-cols-1 gap-4\">\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Nome Dipartimento *\n                </label>\n                <input v-model=\"form.name\"\n                       type=\"text\"\n                       required\n                       class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\">\n              </div>\n              \n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Descrizione\n                </label>\n                <textarea v-model=\"form.description\"\n                          rows=\"3\"\n                          class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\"></textarea>\n              </div>\n            </div>\n          </div>\n\n          <!-- Management -->\n          <div>\n            <h4 class=\"text-md font-medium text-gray-900 dark:text-white mb-4\">Gestione</h4>\n            <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Manager\n                </label>\n                <select v-model=\"form.manager_id\"\n                        class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\">\n                  <option value=\"\">Seleziona manager</option>\n                  <option v-for=\"manager in managers\" :key=\"manager.id\" :value=\"manager.id\">\n                    {{ manager.full_name }}\n                  </option>\n                </select>\n              </div>\n              \n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Dipartimento Padre\n                </label>\n                <select v-model=\"form.parent_id\"\n                        class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\">\n                  <option value=\"\">Nessun padre (dipartimento principale)</option>\n                  <option v-for=\"dept in availableParents\" :key=\"dept.id\" :value=\"dept.id\">\n                    {{ dept.name }}\n                  </option>\n                </select>\n              </div>\n            </div>\n          </div>\n\n          <!-- Budget -->\n          <div>\n            <h4 class=\"text-md font-medium text-gray-900 dark:text-white mb-4\">Budget</h4>\n            <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Budget Annuale (€)\n                </label>\n                <input v-model.number=\"form.budget\"\n                       type=\"number\"\n                       step=\"100\"\n                       min=\"0\"\n                       class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\">\n              </div>\n              \n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Codice Dipartimento\n                </label>\n                <input v-model=\"form.code\"\n                       type=\"text\"\n                       placeholder=\"es. IT, HR, SALES\"\n                       class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\">\n              </div>\n            </div>\n          </div>\n\n          <!-- Error Message -->\n          <div v-if=\"error\" class=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4\">\n            <div class=\"flex\">\n              <svg class=\"w-5 h-5 text-red-400 mr-2 mt-0.5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clip-rule=\"evenodd\"></path>\n              </svg>\n              <div>\n                <h3 class=\"text-sm font-medium text-red-800 dark:text-red-200\">Errore nel salvataggio</h3>\n                <p class=\"text-sm text-red-700 dark:text-red-300 mt-1\">{{ error }}</p>\n              </div>\n            </div>\n          </div>\n\n          <!-- Modal Footer -->\n          <div class=\"flex items-center justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700\">\n            <button type=\"button\"\n                    @click=\"$emit('close')\"\n                    class=\"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\">\n              Annulla\n            </button>\n            <button type=\"submit\"\n                    :disabled=\"loading\"\n                    class=\"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50\">\n              <svg v-if=\"loading\" class=\"animate-spin -ml-1 mr-3 h-4 w-4 text-white inline\" fill=\"none\" viewBox=\"0 0 24 24\">\n                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\"></circle>\n                <path class=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n              </svg>\n              {{ loading ? 'Salvataggio...' : (department ? 'Aggiorna' : 'Crea') }}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted } from 'vue'\n\n// Props\nconst props = defineProps({\n  department: {\n    type: Object,\n    default: null\n  },\n  managers: {\n    type: Array,\n    default: () => []\n  },\n  departments: {\n    type: Array,\n    default: () => []\n  }\n})\n\n// Emits\nconst emit = defineEmits(['close', 'saved'])\n\n// Reactive state\nconst loading = ref(false)\nconst error = ref(null)\n\n// Form data\nconst form = ref({\n  name: '',\n  description: '',\n  manager_id: '',\n  parent_id: '',\n  budget: null,\n  code: ''\n})\n\n// Computed properties\nconst availableParents = computed(() => {\n  // Exclude current department and its children to prevent circular references\n  if (!props.department) {\n    return props.departments\n  }\n  \n  return props.departments.filter(dept => \n    dept.id !== props.department.id &&\n    dept.parent_id !== props.department.id\n  )\n})\n\n// Methods\nconst saveDepartment = async () => {\n  loading.value = true\n  error.value = null\n\n  try {\n    // Prepare form data\n    const departmentData = { ...form.value }\n    \n    // Convert empty strings to null for optional fields\n    Object.keys(departmentData).forEach(key => {\n      if (departmentData[key] === '') {\n        departmentData[key] = null\n      }\n    })\n\n    const url = props.department \n      ? `/api/personnel/departments/${props.department.id}`\n      : '/api/personnel/departments'\n    \n    const method = props.department ? 'PUT' : 'POST'\n\n    const response = await fetch(url, {\n      method,\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      credentials: 'include',\n      body: JSON.stringify(departmentData)\n    })\n\n    if (!response.ok) {\n      throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n\n    if (data.success) {\n      emit('saved', data.data.department)\n    } else {\n      throw new Error(data.message || 'Errore nel salvataggio dipartimento')\n    }\n  } catch (err) {\n    console.error('Error saving department:', err)\n    error.value = err.message\n  } finally {\n    loading.value = false\n  }\n}\n\n// Lifecycle\nonMounted(() => {\n  if (props.department) {\n    // Populate form with existing department data\n    form.value = {\n      name: props.department.name || '',\n      description: props.department.description || '',\n      manager_id: props.department.manager_id || '',\n      parent_id: props.department.parent_id || '',\n      budget: props.department.budget || null,\n      code: props.department.code || ''\n    }\n  }\n})\n</script>\n\n<style scoped>\n/* Custom styles for modal */\n.modal-container {\n  max-height: 90vh;\n  overflow-y: auto;\n}\n\n/* Responsive adjustments */\n@media (max-width: 768px) {\n  .modal-container {\n    margin: 1rem;\n    width: calc(100% - 2rem);\n  }\n}\n</style>\n"}
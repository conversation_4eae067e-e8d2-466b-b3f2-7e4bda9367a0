
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for frontend/src/views/personnel/DepartmentView.vue</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../../prettify.css" />
    <link rel="stylesheet" href="../../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../../index.html">All files</a> / <a href="index.html">frontend/src/views/personnel</a> DepartmentView.vue</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/314</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/314</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a>
<a name='L205'></a><a href='#L205'>205</a>
<a name='L206'></a><a href='#L206'>206</a>
<a name='L207'></a><a href='#L207'>207</a>
<a name='L208'></a><a href='#L208'>208</a>
<a name='L209'></a><a href='#L209'>209</a>
<a name='L210'></a><a href='#L210'>210</a>
<a name='L211'></a><a href='#L211'>211</a>
<a name='L212'></a><a href='#L212'>212</a>
<a name='L213'></a><a href='#L213'>213</a>
<a name='L214'></a><a href='#L214'>214</a>
<a name='L215'></a><a href='#L215'>215</a>
<a name='L216'></a><a href='#L216'>216</a>
<a name='L217'></a><a href='#L217'>217</a>
<a name='L218'></a><a href='#L218'>218</a>
<a name='L219'></a><a href='#L219'>219</a>
<a name='L220'></a><a href='#L220'>220</a>
<a name='L221'></a><a href='#L221'>221</a>
<a name='L222'></a><a href='#L222'>222</a>
<a name='L223'></a><a href='#L223'>223</a>
<a name='L224'></a><a href='#L224'>224</a>
<a name='L225'></a><a href='#L225'>225</a>
<a name='L226'></a><a href='#L226'>226</a>
<a name='L227'></a><a href='#L227'>227</a>
<a name='L228'></a><a href='#L228'>228</a>
<a name='L229'></a><a href='#L229'>229</a>
<a name='L230'></a><a href='#L230'>230</a>
<a name='L231'></a><a href='#L231'>231</a>
<a name='L232'></a><a href='#L232'>232</a>
<a name='L233'></a><a href='#L233'>233</a>
<a name='L234'></a><a href='#L234'>234</a>
<a name='L235'></a><a href='#L235'>235</a>
<a name='L236'></a><a href='#L236'>236</a>
<a name='L237'></a><a href='#L237'>237</a>
<a name='L238'></a><a href='#L238'>238</a>
<a name='L239'></a><a href='#L239'>239</a>
<a name='L240'></a><a href='#L240'>240</a>
<a name='L241'></a><a href='#L241'>241</a>
<a name='L242'></a><a href='#L242'>242</a>
<a name='L243'></a><a href='#L243'>243</a>
<a name='L244'></a><a href='#L244'>244</a>
<a name='L245'></a><a href='#L245'>245</a>
<a name='L246'></a><a href='#L246'>246</a>
<a name='L247'></a><a href='#L247'>247</a>
<a name='L248'></a><a href='#L248'>248</a>
<a name='L249'></a><a href='#L249'>249</a>
<a name='L250'></a><a href='#L250'>250</a>
<a name='L251'></a><a href='#L251'>251</a>
<a name='L252'></a><a href='#L252'>252</a>
<a name='L253'></a><a href='#L253'>253</a>
<a name='L254'></a><a href='#L254'>254</a>
<a name='L255'></a><a href='#L255'>255</a>
<a name='L256'></a><a href='#L256'>256</a>
<a name='L257'></a><a href='#L257'>257</a>
<a name='L258'></a><a href='#L258'>258</a>
<a name='L259'></a><a href='#L259'>259</a>
<a name='L260'></a><a href='#L260'>260</a>
<a name='L261'></a><a href='#L261'>261</a>
<a name='L262'></a><a href='#L262'>262</a>
<a name='L263'></a><a href='#L263'>263</a>
<a name='L264'></a><a href='#L264'>264</a>
<a name='L265'></a><a href='#L265'>265</a>
<a name='L266'></a><a href='#L266'>266</a>
<a name='L267'></a><a href='#L267'>267</a>
<a name='L268'></a><a href='#L268'>268</a>
<a name='L269'></a><a href='#L269'>269</a>
<a name='L270'></a><a href='#L270'>270</a>
<a name='L271'></a><a href='#L271'>271</a>
<a name='L272'></a><a href='#L272'>272</a>
<a name='L273'></a><a href='#L273'>273</a>
<a name='L274'></a><a href='#L274'>274</a>
<a name='L275'></a><a href='#L275'>275</a>
<a name='L276'></a><a href='#L276'>276</a>
<a name='L277'></a><a href='#L277'>277</a>
<a name='L278'></a><a href='#L278'>278</a>
<a name='L279'></a><a href='#L279'>279</a>
<a name='L280'></a><a href='#L280'>280</a>
<a name='L281'></a><a href='#L281'>281</a>
<a name='L282'></a><a href='#L282'>282</a>
<a name='L283'></a><a href='#L283'>283</a>
<a name='L284'></a><a href='#L284'>284</a>
<a name='L285'></a><a href='#L285'>285</a>
<a name='L286'></a><a href='#L286'>286</a>
<a name='L287'></a><a href='#L287'>287</a>
<a name='L288'></a><a href='#L288'>288</a>
<a name='L289'></a><a href='#L289'>289</a>
<a name='L290'></a><a href='#L290'>290</a>
<a name='L291'></a><a href='#L291'>291</a>
<a name='L292'></a><a href='#L292'>292</a>
<a name='L293'></a><a href='#L293'>293</a>
<a name='L294'></a><a href='#L294'>294</a>
<a name='L295'></a><a href='#L295'>295</a>
<a name='L296'></a><a href='#L296'>296</a>
<a name='L297'></a><a href='#L297'>297</a>
<a name='L298'></a><a href='#L298'>298</a>
<a name='L299'></a><a href='#L299'>299</a>
<a name='L300'></a><a href='#L300'>300</a>
<a name='L301'></a><a href='#L301'>301</a>
<a name='L302'></a><a href='#L302'>302</a>
<a name='L303'></a><a href='#L303'>303</a>
<a name='L304'></a><a href='#L304'>304</a>
<a name='L305'></a><a href='#L305'>305</a>
<a name='L306'></a><a href='#L306'>306</a>
<a name='L307'></a><a href='#L307'>307</a>
<a name='L308'></a><a href='#L308'>308</a>
<a name='L309'></a><a href='#L309'>309</a>
<a name='L310'></a><a href='#L310'>310</a>
<a name='L311'></a><a href='#L311'>311</a>
<a name='L312'></a><a href='#L312'>312</a>
<a name='L313'></a><a href='#L313'>313</a>
<a name='L314'></a><a href='#L314'>314</a>
<a name='L315'></a><a href='#L315'>315</a></td><td class="line-coverage quiet"><span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js"><span class="cstat-no" title="statement not covered" >&lt;template&gt;<span class="fstat-no" title="function not covered" ><span class="branch-0 cbranch-no" title="branch not covered" ></span></span></span>
<span class="cstat-no" title="statement not covered" >  &lt;div class="department-view"&gt;</span>
<span class="cstat-no" title="statement not covered" >    &lt;!-- Loading State --&gt;</span>
<span class="cstat-no" title="statement not covered" >    &lt;div v-if="loading" class="flex justify-center items-center h-64"&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >    &lt;!-- Error State --&gt;</span>
<span class="cstat-no" title="statement not covered" >    &lt;div v-else-if="error" class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6"&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;div class="flex"&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;svg class="w-5 h-5 text-red-400 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20"&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"&gt;&lt;/path&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/svg&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;h3 class="text-sm font-medium text-red-800 dark:text-red-200"&gt;Errore nel caricamento del dipartimento&lt;/h3&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;p class="text-sm text-red-700 dark:text-red-300 mt-1"&gt;{{ error }}&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >    &lt;!-- Department Content --&gt;</span>
<span class="cstat-no" title="statement not covered" >    &lt;div v-else-if="department" class="space-y-6"&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;!-- Header --&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;div class="bg-gradient-to-r from-blue-500 to-purple-600 px-6 py-8"&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;div class="flex items-center justify-between"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div class="flex items-center space-x-4"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;!-- Back Button --&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;router-link to="/app/personnel/departments"</span>
<span class="cstat-no" title="statement not covered" >                           class="text-white hover:text-blue-100 transition-colors duration-200"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd"&gt;&lt;/path&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/svg&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/router-link&gt;</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >              &lt;!-- Department Info --&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div class="flex items-center space-x-3"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;div class="w-12 h-12 bg-white rounded-lg flex items-center justify-center shadow-lg"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20"&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z" clip-rule="evenodd"&gt;&lt;/path&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/svg&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;h1 class="text-3xl font-bold text-white"&gt;{{ department.name }}&lt;/h1&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;p v-if="department.description" class="text-blue-100 text-lg"&gt;{{ department.description }}&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >                &lt;!-- Department Metadata --&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div class="flex items-center space-x-6 mt-4"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;div v-if="department.manager" class="flex items-center text-white"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20"&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"&gt;&lt;/path&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/svg&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;span class="text-sm"&gt;Manager: {{ department.manager.full_name }}&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;div class="flex items-center text-white"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20"&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"&gt;&lt;/path&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/svg&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;span class="text-sm"&gt;{{ department.employee_count }} dipendenti&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;div v-if="department.budget" class="flex items-center text-white"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20"&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"&gt;&lt;/path&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clip-rule="evenodd"&gt;&lt;/path&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/svg&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;span class="text-sm"&gt;Budget: {{ formatCurrency(department.budget) }}&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >            &lt;!-- Actions --&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div v-if="canManageDepartments" class="flex space-x-3"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;button @click="editMode = !editMode"</span>
<span class="cstat-no" title="statement not covered" >                      class="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-colors duration-200"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;svg class="w-5 h-5 inline mr-2" fill="currentColor" viewBox="0 0 20 20"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"&gt;&lt;/path&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/svg&gt;</span>
<span class="cstat-no" title="statement not covered" >                {{ editMode ? 'Annulla' : 'Modifica' }}</span>
<span class="cstat-no" title="statement not covered" >              &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >      &lt;!-- Tab Navigation --&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;div class="border-b border-gray-200 dark:border-gray-700"&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;nav class="-mb-px flex space-x-8 px-6" aria-label="Tabs"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;button v-for="tab in tabs" :key="tab.id"</span>
<span class="cstat-no" title="statement not covered" >                    @click="activeTab = tab.id"</span>
<span class="cstat-no" title="statement not covered" >                    :class="[</span>
<span class="cstat-no" title="statement not covered" >                      activeTab === tab.id</span>
<span class="cstat-no" title="statement not covered" >                        ? 'border-blue-500 text-blue-600 dark:text-blue-400'</span>
<span class="cstat-no" title="statement not covered" >                        : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300',</span>
<span class="cstat-no" title="statement not covered" >                      'whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center'</span>
<span class="cstat-no" title="statement not covered" >                    ]"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;!-- Tab Icons --&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;svg v-if="tab.id === 'employees'" class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"&gt;&lt;/path&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/svg&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;svg v-else-if="tab.id === 'subdepartments'" class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;path fill-rule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 0l-2 2a1 1 0 101.414 1.414L8 10.414l1.293 1.293a1 1 0 001.414 0l4-4z" clip-rule="evenodd"&gt;&lt;/path&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/svg&gt;</span>
<span class="cstat-no" title="statement not covered" >              {{ tab.name }}</span>
<span class="cstat-no" title="statement not covered" >              &lt;span v-if="tab.count !== undefined"</span>
<span class="cstat-no" title="statement not covered" >                    class="ml-2 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 py-0.5 px-2 rounded-full text-xs"&gt;</span>
<span class="cstat-no" title="statement not covered" >                {{ tab.count }}</span>
<span class="cstat-no" title="statement not covered" >              &lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/nav&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >        &lt;!-- Tab Content --&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;div class="p-6"&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;!-- Employees Tab --&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;div v-if="activeTab === 'employees'"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div v-if="department.employees &amp;&amp; department.employees.length &gt; 0" class="space-y-4"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div v-for="employee in department.employees" :key="employee.id"</span>
<span class="cstat-no" title="statement not covered" >                   class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div class="flex items-center justify-between"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;div class="flex items-center space-x-4"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div class="w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center"&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;svg class="w-5 h-5 text-gray-600 dark:text-gray-300" fill="currentColor" viewBox="0 0 20 20"&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"&gt;&lt;/path&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;/svg&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;h4 class="font-medium text-gray-900 dark:text-white"&gt;{{ employee.full_name }}&lt;/h4&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;p class="text-sm text-gray-500 dark:text-gray-400"&gt;{{ employee.position || 'Posizione non specificata' }}&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;p class="text-sm text-gray-500 dark:text-gray-400"&gt;{{ employee.email }}&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;div class="flex items-center space-x-2"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;span :class="[</span>
<span class="cstat-no" title="statement not covered" >                      'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',</span>
<span class="cstat-no" title="statement not covered" >                      employee.is_active ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'</span>
<span class="cstat-no" title="statement not covered" >                    ]"&gt;</span>
<span class="cstat-no" title="statement not covered" >                      {{ employee.is_active ? 'Attivo' : 'Inattivo' }}</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;router-link :to="`/app/personnel/${employee.id}`"</span>
<span class="cstat-no" title="statement not covered" >                                 class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300"&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;path d="M10 12a2 2 0 100-4 2 2 0 000 4z"&gt;&lt;/path&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"&gt;&lt;/path&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;/svg&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/router-link&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div v-else class="text-center py-8"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" /&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/svg&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white"&gt;Nessun dipendente&lt;/h3&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;p class="mt-1 text-sm text-gray-500 dark:text-gray-400"&gt;Non ci sono dipendenti assegnati a questo dipartimento.&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >          &lt;!-- Subdepartments Tab --&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;div v-if="activeTab === 'subdepartments'"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div v-if="department.subdepartments &amp;&amp; department.subdepartments.length &gt; 0" class="space-y-4"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div v-for="subdept in department.subdepartments" :key="subdept.id"</span>
<span class="cstat-no" title="statement not covered" >                   class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div class="flex items-center justify-between"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;div class="flex items-center space-x-4"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div class="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center"&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="currentColor" viewBox="0 0 20 20"&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z" clip-rule="evenodd"&gt;&lt;/path&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;/svg&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;h4 class="font-medium text-gray-900 dark:text-white"&gt;{{ subdept.name }}&lt;/h4&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;p class="text-sm text-gray-500 dark:text-gray-400"&gt;{{ subdept.employee_count }} dipendenti&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;router-link :to="`/app/personnel/departments/${subdept.id}`"</span>
<span class="cstat-no" title="statement not covered" >                               class="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;path d="M10 12a2 2 0 100-4 2 2 0 000 4z"&gt;&lt;/path&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"&gt;&lt;/path&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/svg&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/router-link&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div v-else class="text-center py-8"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" /&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/svg&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white"&gt;Nessun sotto-dipartimento&lt;/h3&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;p class="mt-1 text-sm text-gray-500 dark:text-gray-400"&gt;Non ci sono sotto-dipartimenti per questo dipartimento.&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >  &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >&lt;/template&gt;</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >&lt;script setup&gt;</span>
<span class="cstat-no" title="statement not covered" >import { ref, computed, onMounted, watch } from 'vue'</span>
<span class="cstat-no" title="statement not covered" >import { useRoute, useRouter } from 'vue-router'</span>
<span class="cstat-no" title="statement not covered" >import { usePersonnelStore } from '@/stores/personnel'</span>
<span class="cstat-no" title="statement not covered" >import { usePermissions } from '@/composables/usePermissions'</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >// Router</span>
<span class="cstat-no" title="statement not covered" >const route = useRoute()</span>
<span class="cstat-no" title="statement not covered" >const router = useRouter()</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >// Stores</span>
<span class="cstat-no" title="statement not covered" >const personnelStore = usePersonnelStore()</span>
<span class="cstat-no" title="statement not covered" >const { hasPermission } = usePermissions()</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >// Reactive state</span>
<span class="cstat-no" title="statement not covered" >const department = ref(null)</span>
<span class="cstat-no" title="statement not covered" >const loading = ref(false)</span>
<span class="cstat-no" title="statement not covered" >const error = ref(null)</span>
<span class="cstat-no" title="statement not covered" >const editMode = ref(false)</span>
<span class="cstat-no" title="statement not covered" >const activeTab = ref('employees')</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >// Computed properties</span>
<span class="cstat-no" title="statement not covered" >const canManageDepartments = computed(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  try {</span>
<span class="cstat-no" title="statement not covered" >    // Fix: hasPermission is a computed that returns a function</span>
<span class="cstat-no" title="statement not covered" >    return hasPermission.value &amp;&amp; typeof hasPermission.value === 'function' ? hasPermission.value('manage_users') : false</span>
<span class="cstat-no" title="statement not covered" >  } catch (e) {</span>
<span class="cstat-no" title="statement not covered" >    console.warn('Permission check failed:', e)</span>
<span class="cstat-no" title="statement not covered" >    return false</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >})</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >const tabs = computed(() =&gt; [</span>
<span class="cstat-no" title="statement not covered" >  {</span>
<span class="cstat-no" title="statement not covered" >    id: 'employees',</span>
<span class="cstat-no" title="statement not covered" >    name: 'Dipendenti',</span>
<span class="cstat-no" title="statement not covered" >    count: department.value?.employees?.length || 0</span>
<span class="cstat-no" title="statement not covered" >  },</span>
<span class="cstat-no" title="statement not covered" >  {</span>
<span class="cstat-no" title="statement not covered" >    id: 'subdepartments',</span>
<span class="cstat-no" title="statement not covered" >    name: 'Sotto-dipartimenti',</span>
<span class="cstat-no" title="statement not covered" >    count: department.value?.subdepartments?.length || 0</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >])</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >const stats = computed(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  if (!department.value) return {}</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >  return {</span>
<span class="cstat-no" title="statement not covered" >    activeProjects: 0, // TODO: Implement when projects API is available</span>
<span class="cstat-no" title="statement not covered" >    budgetUsagePercentage: department.value.budget ? Math.round((department.value.budget_used || 0) / department.value.budget * 100) : 0</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >})</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >// Utility functions</span>
<span class="cstat-no" title="statement not covered" >const formatCurrency = (amount) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  return new Intl.NumberFormat('it-IT', {</span>
<span class="cstat-no" title="statement not covered" >    style: 'currency',</span>
<span class="cstat-no" title="statement not covered" >    currency: 'EUR'</span>
<span class="cstat-no" title="statement not covered" >  }).format(amount)</span>
<span class="cstat-no" title="statement not covered" >}</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >// API functions</span>
<span class="cstat-no" title="statement not covered" >const fetchDepartment = async (deptId) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  loading.value = true</span>
<span class="cstat-no" title="statement not covered" >  error.value = null</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >  try {</span>
<span class="cstat-no" title="statement not covered" >    const response = await fetch(`/api/personnel/departments/${deptId}`, {</span>
<span class="cstat-no" title="statement not covered" >      credentials: 'include'</span>
<span class="cstat-no" title="statement not covered" >    })</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >    if (!response.ok) {</span>
<span class="cstat-no" title="statement not covered" >      throw new Error(`HTTP ${response.status}: ${response.statusText}`)</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >    const data = await response.json()</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >    if (data.success) {</span>
<span class="cstat-no" title="statement not covered" >      department.value = data.data.department</span>
<span class="cstat-no" title="statement not covered" >    } else {</span>
<span class="cstat-no" title="statement not covered" >      throw new Error(data.message || 'Errore nel caricamento del dipartimento')</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  } catch (err) {</span>
<span class="cstat-no" title="statement not covered" >    console.error('Error fetching department:', err)</span>
<span class="cstat-no" title="statement not covered" >    error.value = err.message</span>
<span class="cstat-no" title="statement not covered" >  } finally {</span>
<span class="cstat-no" title="statement not covered" >    loading.value = false</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >}</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >// Lifecycle</span>
<span class="cstat-no" title="statement not covered" >onMounted(async () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  const deptId = route.params.id</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >  if (!deptId) {</span>
<span class="cstat-no" title="statement not covered" >    error.value = 'ID dipartimento non specificato'</span>
<span class="cstat-no" title="statement not covered" >    return</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >  await fetchDepartment(deptId)</span>
<span class="cstat-no" title="statement not covered" >})</span>
<span class="cstat-no" title="statement not covered" ></span>
<span class="cstat-no" title="statement not covered" >// Watch for route changes</span>
<span class="cstat-no" title="statement not covered" >watch(() =&gt; route.params.id, async (newId) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  if (newId) {</span>
<span class="cstat-no" title="statement not covered" >    await fetchDepartment(newId)</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >})</span>
<span class="cstat-no" title="statement not covered" >&lt;/script&gt;</span>
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-05-31T12:30:44.601Z
            </div>
        <script src="../../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../../sorter.js"></script>
        <script src="../../../../block-navigation.js"></script>
    </body>
</html>
    
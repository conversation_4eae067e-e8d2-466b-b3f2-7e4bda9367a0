{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/personnel/Personnel.vue"}, "originalCode": "<template>\n  <div>\n    <!-- Header -->\n    <div class=\"mb-6\">\n      <div class=\"flex flex-col sm:flex-row sm:items-center sm:justify-between\">\n        <div>\n          <div class=\"flex items-center\">\n            <svg class=\"w-8 h-8 text-blue-600 dark:text-blue-400 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path d=\"M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z\"></path>\n            </svg>\n            <h1 class=\"text-2xl font-bold text-gray-900 dark:text-white\">Personale</h1>\n          </div>\n          <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\n            Gest<PERSON>ci il team e le competenze aziendali\n          </p>\n        </div>\n        <div class=\"mt-4 sm:mt-0 flex flex-wrap gap-3\">\n          <router-link\n            to=\"/app/personnel/directory\"\n            class=\"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700\"\n          >\n            <svg class=\"w-4 h-4 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fill-rule=\"evenodd\" d=\"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z\" clip-rule=\"evenodd\"></path>\n            </svg>\n            Directory\n          </router-link>\n          <router-link\n            to=\"/app/personnel/orgchart\"\n            class=\"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700\"\n          >\n            <svg class=\"w-4 h-4 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fill-rule=\"evenodd\" d=\"M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 0l-2 2a1 1 0 101.414 1.414L8 10.414l1.293 1.293a1 1 0 001.414 0l4-4z\" clip-rule=\"evenodd\"></path>\n            </svg>\n            Organigramma\n          </router-link>\n          <router-link\n            to=\"/app/personnel/skills\"\n            class=\"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700\"\n          >\n            <svg class=\"w-4 h-4 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"></path>\n            </svg>\n            Competenze\n          </router-link>\n          <router-link\n            v-if=\"hasAdminAccess\"\n            to=\"/app/personnel/admin\"\n            class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700\"\n          >\n            <svg class=\"w-4 h-4 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fill-rule=\"evenodd\" d=\"M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z\" clip-rule=\"evenodd\"></path>\n            </svg>\n            Amministrazione\n          </router-link>\n        </div>\n      </div>\n    </div>\n\n    <!-- Filtri -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg mb-6\">\n      <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n        <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">Filtri</h3>\n      </div>\n      <div class=\"p-6\">\n        <div class=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          <!-- Filtro Dipartimento -->\n          <div>\n            <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Dipartimento\n            </label>\n            <select\n              v-model=\"filters.department\"\n              @change=\"applyFilters\"\n              class=\"w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white\"\n            >\n              <option value=\"\">Tutti i dipartimenti</option>\n              <option\n                v-for=\"dept in departments\"\n                :key=\"dept.id\"\n                :value=\"dept.id\"\n              >\n                {{ dept.name }}\n              </option>\n            </select>\n          </div>\n\n          <!-- Filtro Competenza -->\n          <div>\n            <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Competenza\n            </label>\n            <select\n              v-model=\"filters.skill\"\n              @change=\"applyFilters\"\n              class=\"w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white\"\n            >\n              <option value=\"\">Tutte le competenze</option>\n              <option\n                v-for=\"skill in skills\"\n                :key=\"skill.id\"\n                :value=\"skill.id\"\n              >\n                {{ skill.name }} ({{ skill.category }})\n              </option>\n            </select>\n          </div>\n\n          <!-- Ricerca -->\n          <div>\n            <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Ricerca\n            </label>\n            <input\n              v-model=\"searchQuery\"\n              @input=\"debounceSearch\"\n              type=\"text\"\n              placeholder=\"Nome, email, posizione...\"\n              class=\"w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white\"\n            >\n          </div>\n        </div>\n\n        <!-- Pulsante reset filtri -->\n        <div v-if=\"hasActiveFilters\" class=\"mt-4 flex justify-end\">\n          <button\n            @click=\"clearFilters\"\n            class=\"inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700\"\n          >\n            <svg class=\"w-4 h-4 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fill-rule=\"evenodd\" d=\"M9 2a1 1 0 000 2h2a1 1 0 100-2H9z\" clip-rule=\"evenodd\"></path>\n              <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414L8.586 12l-2.293 2.293a1 1 0 101.414 1.414L10 13.414l2.293 2.293a1 1 0 001.414-1.414L11.414 12l2.293-2.293z\" clip-rule=\"evenodd\"></path>\n            </svg>\n            Pulisci filtri\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Lista Personale -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg\">\n      <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n        <div class=\"flex items-center justify-between\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n            Team ({{ pagination.total }} dipendenti{{ hasActiveFilters ? ' - filtrati' : '' }})\n          </h3>\n\n          <div v-if=\"hasActiveFilters\" class=\"text-sm text-gray-500 dark:text-gray-400\">\n            <span\n              v-if=\"searchQuery\"\n              class=\"inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 mr-2\"\n            >\n              <svg class=\"w-3 h-3 mr-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fill-rule=\"evenodd\" d=\"M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z\" clip-rule=\"evenodd\"></path>\n              </svg>\n              \"{{ searchQuery }}\"\n            </span>\n            <span\n              v-if=\"selectedDepartment\"\n              class=\"inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 mr-2\"\n            >\n              🏢 {{ selectedDepartment.name }}\n            </span>\n            <span\n              v-if=\"selectedSkill\"\n              class=\"inline-flex items-center px-2 py-1 rounded-full text-xs bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200\"\n            >\n              🎯 {{ selectedSkill.name }}\n            </span>\n          </div>\n        </div>\n      </div>\n\n      <!-- Loading State -->\n      <div v-if=\"loading\" class=\"p-6\">\n        <div class=\"flex items-center justify-center\">\n          <div class=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"></div>\n          <span class=\"ml-2 text-gray-600 dark:text-gray-400\">Caricamento...</span>\n        </div>\n      </div>\n\n      <!-- Error State -->\n      <div v-else-if=\"error\" class=\"p-6\">\n        <div class=\"bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md p-4\">\n          <div class=\"flex\">\n            <div class=\"flex-shrink-0\">\n              <svg class=\"h-5 w-5 text-red-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clip-rule=\"evenodd\" />\n              </svg>\n            </div>\n            <div class=\"ml-3\">\n              <h3 class=\"text-sm font-medium text-red-800 dark:text-red-200\">\n                Errore nel caricamento\n              </h3>\n              <div class=\"mt-2 text-sm text-red-700 dark:text-red-300\">\n                {{ error }}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Users Grid -->\n      <div v-else-if=\"users.length > 0\" class=\"overflow-hidden\">\n        <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6\">\n          <div\n            v-for=\"user in users\"\n            :key=\"user.id\"\n            class=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-6 hover:shadow-md transition-shadow duration-200\"\n          >\n            <!-- Avatar e Info Base -->\n            <div class=\"flex items-center mb-4\">\n              <div class=\"flex-shrink-0\">\n                <img\n                  v-if=\"user.profile_image\"\n                  :src=\"user.profile_image\"\n                  :alt=\"user.full_name\"\n                  class=\"h-12 w-12 rounded-full\"\n                >\n                <div\n                  v-else\n                  class=\"h-12 w-12 rounded-full bg-primary-500 dark:bg-primary-600 flex items-center justify-center text-white text-lg font-medium\"\n                >\n                  {{ getUserInitials(user) }}\n                </div>\n              </div>\n              <div class=\"ml-4 flex-1\">\n                <h4 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                  {{ user.full_name }}\n                </h4>\n                <p class=\"text-sm text-gray-500 dark:text-gray-400\">\n                  {{ user.position || 'Posizione non specificata' }}\n                </p>\n              </div>\n            </div>\n\n            <!-- Dettagli -->\n            <div class=\"space-y-2 mb-4\">\n              <div class=\"flex items-center text-sm\">\n                <span class=\"text-gray-500 dark:text-gray-400 w-20\">Email:</span>\n                <span class=\"text-gray-900 dark:text-white\">{{ user.email }}</span>\n              </div>\n              <div class=\"flex items-center text-sm\">\n                <span class=\"text-gray-500 dark:text-gray-400 w-20\">Ruolo:</span>\n                <span :class=\"getRoleClass(user.role)\">\n                  {{ getRoleLabel(user.role) }}\n                </span>\n              </div>\n              <div v-if=\"user.department\" class=\"flex items-center text-sm\">\n                <span class=\"text-gray-500 dark:text-gray-400 w-20\">Dipart.:</span>\n                <span class=\"text-gray-900 dark:text-white\">{{ user.department.name }}</span>\n              </div>\n            </div>\n\n            <!-- Competenze -->\n            <div v-if=\"user.skills && user.skills.length > 0\" class=\"mb-4\">\n              <p class=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">Competenze:</p>\n              <div class=\"flex flex-wrap gap-1\">\n                <span\n                  v-for=\"skill in user.skills.slice(0, 3)\"\n                  :key=\"skill.id\"\n                  class=\"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200\"\n                >\n                  {{ skill.name }}\n                </span>\n                <span\n                  v-if=\"user.skills.length > 3\"\n                  class=\"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-600 dark:bg-gray-600 dark:text-gray-300\"\n                >\n                  +{{ user.skills.length - 3 }}\n                </span>\n              </div>\n            </div>\n\n            <!-- Azioni -->\n            <div class=\"flex space-x-2\">\n              <router-link\n                :to=\"`/app/personnel/${user.id}`\"\n                class=\"flex-1 text-center px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700\"\n              >\n                👤 Profilo\n              </router-link>\n              <a\n                v-if=\"user.phone\"\n                :href=\"`tel:${user.phone}`\"\n                class=\"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700\"\n              >\n                📞\n              </a>\n              <a\n                :href=\"`mailto:${user.email}`\"\n                class=\"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700\"\n              >\n                ✉️\n              </a>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Empty State -->\n      <div v-else class=\"text-center py-12\">\n        <div class=\"text-gray-400 dark:text-gray-500 text-6xl mb-4\">👥</div>\n        <h3 class=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">Nessun dipendente trovato</h3>\n        <p class=\"text-gray-500 dark:text-gray-400\">\n          {{ hasActiveFilters ? 'Prova a modificare i filtri di ricerca' : 'Non ci sono dipendenti da visualizzare' }}\n        </p>\n      </div>\n\n      <!-- Paginazione -->\n      <div v-if=\"pagination.pages > 1\" class=\"px-6 py-4 border-t border-gray-200 dark:border-gray-700\">\n        <div class=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\n          <!-- Info risultati -->\n          <div class=\"text-sm text-gray-700 dark:text-gray-300\">\n            Mostrando <span class=\"font-medium\">{{ (pagination.page - 1) * pagination.per_page + 1 }}</span> -\n            <span class=\"font-medium\">{{ Math.min(pagination.page * pagination.per_page, pagination.total) }}</span>\n            di <span class=\"font-medium\">{{ pagination.total }}</span> dipendenti\n          </div>\n\n          <!-- Controlli paginazione -->\n          <nav class=\"flex items-center space-x-1\">\n            <!-- Precedente -->\n            <button\n              v-if=\"pagination.page > 1\"\n              @click=\"changePage(pagination.page - 1)\"\n              class=\"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n            >\n              ← Precedente\n            </button>\n            <span\n              v-else\n              class=\"px-3 py-2 border border-gray-200 dark:border-gray-700 rounded-md text-sm font-medium text-gray-400 dark:text-gray-600 bg-gray-50 dark:bg-gray-900 cursor-not-allowed\"\n            >\n              ← Precedente\n            </span>\n\n            <!-- Numeri pagina -->\n            <template v-for=\"page in getPageNumbers()\" :key=\"page\">\n              <button\n                v-if=\"page !== '...'\"\n                @click=\"changePage(page)\"\n                :class=\"[\n                  'px-3 py-2 border rounded-md text-sm font-medium transition-colors',\n                  page === pagination.page\n                    ? 'border-primary-500 dark:border-primary-400 text-white bg-primary-600 dark:bg-primary-500'\n                    : 'border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700'\n                ]\"\n              >\n                {{ page }}\n              </button>\n              <span v-else class=\"px-2 py-2 text-sm text-gray-500 dark:text-gray-400\">…</span>\n            </template>\n\n            <!-- Successiva -->\n            <button\n              v-if=\"pagination.page < pagination.pages\"\n              @click=\"changePage(pagination.page + 1)\"\n              class=\"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n            >\n              Successiva →\n            </button>\n            <span\n              v-else\n              class=\"px-3 py-2 border border-gray-200 dark:border-gray-700 rounded-md text-sm font-medium text-gray-400 dark:text-gray-600 bg-gray-50 dark:bg-gray-900 cursor-not-allowed\"\n            >\n              Successiva →\n            </span>\n          </nav>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted, watch } from 'vue'\nimport { useRoute, useRouter } from 'vue-router'\nimport { usePersonnelStore } from '@/stores/personnel'\nimport { usePermissions } from '@/composables/usePermissions'\n\nconst route = useRoute()\nconst router = useRouter()\nconst personnelStore = usePersonnelStore()\nconst { hasPermission } = usePermissions()\n\n// Reactive refs\nconst searchQuery = ref('')\nconst searchTimeout = ref(null)\n\n// Store state\nconst {\n  users,\n  departments,\n  skills,\n  loading,\n  error,\n  filters,\n  pagination\n} = personnelStore\n\n// Computed properties\nconst hasAdminAccess = computed(() => hasPermission.value('admin_access'))\n\nconst hasActiveFilters = computed(() => {\n  return searchQuery.value ||\n         filters.department ||\n         filters.skill ||\n         filters.role\n})\n\nconst selectedDepartment = computed(() => {\n  if (!filters.department) return null\n  return departments.find(d => d.id === filters.department)\n})\n\nconst selectedSkill = computed(() => {\n  if (!filters.skill) return null\n  return skills.find(s => s.id === filters.skill)\n})\n\n// Helper functions\nconst getUserInitials = (user) => {\n  const first = user.first_name?.[0] || ''\n  const last = user.last_name?.[0] || ''\n  return (first + last).toUpperCase()\n}\n\nconst getRoleLabel = (role) => {\n  const labels = {\n    'admin': 'Admin',\n    'manager': 'Manager',\n    'employee': 'Dipendente',\n    'human_resources': 'HR',\n    'project_manager': 'PM'\n  }\n  return labels[role] || role\n}\n\nconst getRoleClass = (role) => {\n  const classes = {\n    'admin': 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',\n    'manager': 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',\n    'human_resources': 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',\n    'project_manager': 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'\n  }\n  return classes[role] || 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'\n}\n\nconst getPageNumbers = () => {\n  const pages = []\n  const current = pagination.page\n  const total = pagination.pages\n\n  // Always show first page\n  if (total > 0) pages.push(1)\n\n  // Show pages around current\n  for (let i = Math.max(2, current - 1); i <= Math.min(total - 1, current + 1); i++) {\n    if (!pages.includes(i)) pages.push(i)\n  }\n\n  // Show last page\n  if (total > 1 && !pages.includes(total)) {\n    if (pages[pages.length - 1] < total - 1) pages.push('...')\n    pages.push(total)\n  }\n\n  return pages\n}\n\n// Methods\nconst debounceSearch = () => {\n  clearTimeout(searchTimeout.value)\n  searchTimeout.value = setTimeout(() => {\n    applyFilters()\n  }, 500)\n}\n\nconst applyFilters = async () => {\n  // Update store filters\n  personnelStore.setFilter('search', searchQuery.value)\n\n  // Reset to page 1 when applying filters\n  personnelStore.setPagination(1)\n\n  // Fetch with new filters\n  await loadUsers()\n}\n\nconst clearFilters = async () => {\n  searchQuery.value = ''\n  personnelStore.clearFilters()\n  personnelStore.setPagination(1)\n  await loadUsers()\n}\n\nconst changePage = async (page) => {\n  personnelStore.setPagination(page)\n  await loadUsers()\n}\n\nconst loadUsers = async () => {\n  const params = {\n    page: pagination.page,\n    per_page: pagination.per_page\n  }\n\n  // Add filters\n  if (searchQuery.value) params.search = searchQuery.value\n  if (filters.department) params.department_id = filters.department\n  if (filters.skill) params.skills = filters.skill\n  if (filters.role) params.role = filters.role\n\n  await personnelStore.fetchUsers(params)\n}\n\nconst loadInitialData = async () => {\n  // Load departments and skills for filters\n  await Promise.all([\n    personnelStore.fetchDepartments(),\n    personnelStore.fetchSkills()\n  ])\n\n  // Parse URL parameters for initial filters\n  const urlParams = route.query\n  if (urlParams.search) searchQuery.value = urlParams.search\n  if (urlParams.department) personnelStore.setFilter('department', parseInt(urlParams.department))\n  if (urlParams.skill) personnelStore.setFilter('skill', parseInt(urlParams.skill))\n  if (urlParams.page) personnelStore.setPagination(parseInt(urlParams.page))\n\n  // Load users with initial filters\n  await loadUsers()\n}\n\n// Watchers\nwatch(() => route.query, (newQuery) => {\n  // Update filters when URL changes (for browser back/forward)\n  if (newQuery.search !== searchQuery.value) {\n    searchQuery.value = newQuery.search || ''\n  }\n}, { deep: true })\n\n// Lifecycle\nonMounted(() => {\n  loadInitialData()\n})\n</script>", "modifiedCode": "<template>\n  <div>\n    <!-- Header -->\n    <div class=\"mb-6\">\n      <div class=\"flex flex-col sm:flex-row sm:items-center sm:justify-between\">\n        <div>\n          <div class=\"flex items-center\">\n            <svg class=\"w-8 h-8 text-blue-600 dark:text-blue-400 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path d=\"M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z\"></path>\n            </svg>\n            <h1 class=\"text-2xl font-bold text-gray-900 dark:text-white\">Personale</h1>\n          </div>\n          <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\n            Gest<PERSON>ci il team e le competenze aziendali\n          </p>\n        </div>\n        <div class=\"mt-4 sm:mt-0 flex flex-wrap gap-3\">\n          <router-link\n            to=\"/app/personnel/directory\"\n            class=\"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700\"\n          >\n            <svg class=\"w-4 h-4 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fill-rule=\"evenodd\" d=\"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z\" clip-rule=\"evenodd\"></path>\n            </svg>\n            Directory\n          </router-link>\n          <router-link\n            to=\"/app/personnel/orgchart\"\n            class=\"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700\"\n          >\n            <svg class=\"w-4 h-4 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fill-rule=\"evenodd\" d=\"M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 0l-2 2a1 1 0 101.414 1.414L8 10.414l1.293 1.293a1 1 0 001.414 0l4-4z\" clip-rule=\"evenodd\"></path>\n            </svg>\n            Organigramma\n          </router-link>\n          <router-link\n            to=\"/app/personnel/skills\"\n            class=\"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700\"\n          >\n            <svg class=\"w-4 h-4 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"></path>\n            </svg>\n            Competenze\n          </router-link>\n          <router-link\n            v-if=\"hasAdminAccess\"\n            to=\"/app/personnel/admin\"\n            class=\"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700\"\n          >\n            <svg class=\"w-4 h-4 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fill-rule=\"evenodd\" d=\"M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z\" clip-rule=\"evenodd\"></path>\n            </svg>\n            Amministrazione\n          </router-link>\n        </div>\n      </div>\n    </div>\n\n    <!-- Filtri -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg mb-6\">\n      <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n        <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">Filtri</h3>\n      </div>\n      <div class=\"p-6\">\n        <div class=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          <!-- Filtro Dipartimento -->\n          <div>\n            <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Dipartimento\n            </label>\n            <select\n              v-model=\"filters.department\"\n              @change=\"applyFilters\"\n              class=\"w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white\"\n            >\n              <option value=\"\">Tutti i dipartimenti</option>\n              <option\n                v-for=\"dept in departments\"\n                :key=\"dept.id\"\n                :value=\"dept.id\"\n              >\n                {{ dept.name }}\n              </option>\n            </select>\n          </div>\n\n          <!-- Filtro Competenza -->\n          <div>\n            <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Competenza\n            </label>\n            <select\n              v-model=\"filters.skill\"\n              @change=\"applyFilters\"\n              class=\"w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white\"\n            >\n              <option value=\"\">Tutte le competenze</option>\n              <option\n                v-for=\"skill in skills\"\n                :key=\"skill.id\"\n                :value=\"skill.id\"\n              >\n                {{ skill.name }} ({{ skill.category }})\n              </option>\n            </select>\n          </div>\n\n          <!-- Ricerca -->\n          <div>\n            <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Ricerca\n            </label>\n            <input\n              v-model=\"searchQuery\"\n              @input=\"debounceSearch\"\n              type=\"text\"\n              placeholder=\"Nome, email, posizione...\"\n              class=\"w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white\"\n            >\n          </div>\n        </div>\n\n        <!-- Pulsante reset filtri -->\n        <div v-if=\"hasActiveFilters\" class=\"mt-4 flex justify-end\">\n          <button\n            @click=\"clearFilters\"\n            class=\"inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700\"\n          >\n            <svg class=\"w-4 h-4 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fill-rule=\"evenodd\" d=\"M9 2a1 1 0 000 2h2a1 1 0 100-2H9z\" clip-rule=\"evenodd\"></path>\n              <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414L8.586 12l-2.293 2.293a1 1 0 101.414 1.414L10 13.414l2.293 2.293a1 1 0 001.414-1.414L11.414 12l2.293-2.293z\" clip-rule=\"evenodd\"></path>\n            </svg>\n            Pulisci filtri\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Lista Personale -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg\">\n      <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n        <div class=\"flex items-center justify-between\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n            Team ({{ pagination.total }} dipendenti{{ hasActiveFilters ? ' - filtrati' : '' }})\n          </h3>\n\n          <div v-if=\"hasActiveFilters\" class=\"text-sm text-gray-500 dark:text-gray-400\">\n            <span\n              v-if=\"searchQuery\"\n              class=\"inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 mr-2\"\n            >\n              <svg class=\"w-3 h-3 mr-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fill-rule=\"evenodd\" d=\"M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z\" clip-rule=\"evenodd\"></path>\n              </svg>\n              \"{{ searchQuery }}\"\n            </span>\n            <span\n              v-if=\"selectedDepartment\"\n              class=\"inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 mr-2\"\n            >\n              <svg class=\"w-3 h-3 mr-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fill-rule=\"evenodd\" d=\"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z\" clip-rule=\"evenodd\"></path>\n              </svg>\n              {{ selectedDepartment.name }}\n            </span>\n            <span\n              v-if=\"selectedSkill\"\n              class=\"inline-flex items-center px-2 py-1 rounded-full text-xs bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200\"\n            >\n              🎯 {{ selectedSkill.name }}\n            </span>\n          </div>\n        </div>\n      </div>\n\n      <!-- Loading State -->\n      <div v-if=\"loading\" class=\"p-6\">\n        <div class=\"flex items-center justify-center\">\n          <div class=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"></div>\n          <span class=\"ml-2 text-gray-600 dark:text-gray-400\">Caricamento...</span>\n        </div>\n      </div>\n\n      <!-- Error State -->\n      <div v-else-if=\"error\" class=\"p-6\">\n        <div class=\"bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md p-4\">\n          <div class=\"flex\">\n            <div class=\"flex-shrink-0\">\n              <svg class=\"h-5 w-5 text-red-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clip-rule=\"evenodd\" />\n              </svg>\n            </div>\n            <div class=\"ml-3\">\n              <h3 class=\"text-sm font-medium text-red-800 dark:text-red-200\">\n                Errore nel caricamento\n              </h3>\n              <div class=\"mt-2 text-sm text-red-700 dark:text-red-300\">\n                {{ error }}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Users Grid -->\n      <div v-else-if=\"users.length > 0\" class=\"overflow-hidden\">\n        <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6\">\n          <div\n            v-for=\"user in users\"\n            :key=\"user.id\"\n            class=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-6 hover:shadow-md transition-shadow duration-200\"\n          >\n            <!-- Avatar e Info Base -->\n            <div class=\"flex items-center mb-4\">\n              <div class=\"flex-shrink-0\">\n                <img\n                  v-if=\"user.profile_image\"\n                  :src=\"user.profile_image\"\n                  :alt=\"user.full_name\"\n                  class=\"h-12 w-12 rounded-full\"\n                >\n                <div\n                  v-else\n                  class=\"h-12 w-12 rounded-full bg-primary-500 dark:bg-primary-600 flex items-center justify-center text-white text-lg font-medium\"\n                >\n                  {{ getUserInitials(user) }}\n                </div>\n              </div>\n              <div class=\"ml-4 flex-1\">\n                <h4 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                  {{ user.full_name }}\n                </h4>\n                <p class=\"text-sm text-gray-500 dark:text-gray-400\">\n                  {{ user.position || 'Posizione non specificata' }}\n                </p>\n              </div>\n            </div>\n\n            <!-- Dettagli -->\n            <div class=\"space-y-2 mb-4\">\n              <div class=\"flex items-center text-sm\">\n                <span class=\"text-gray-500 dark:text-gray-400 w-20\">Email:</span>\n                <span class=\"text-gray-900 dark:text-white\">{{ user.email }}</span>\n              </div>\n              <div class=\"flex items-center text-sm\">\n                <span class=\"text-gray-500 dark:text-gray-400 w-20\">Ruolo:</span>\n                <span :class=\"getRoleClass(user.role)\">\n                  {{ getRoleLabel(user.role) }}\n                </span>\n              </div>\n              <div v-if=\"user.department\" class=\"flex items-center text-sm\">\n                <span class=\"text-gray-500 dark:text-gray-400 w-20\">Dipart.:</span>\n                <span class=\"text-gray-900 dark:text-white\">{{ user.department.name }}</span>\n              </div>\n            </div>\n\n            <!-- Competenze -->\n            <div v-if=\"user.skills && user.skills.length > 0\" class=\"mb-4\">\n              <p class=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">Competenze:</p>\n              <div class=\"flex flex-wrap gap-1\">\n                <span\n                  v-for=\"skill in user.skills.slice(0, 3)\"\n                  :key=\"skill.id\"\n                  class=\"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200\"\n                >\n                  {{ skill.name }}\n                </span>\n                <span\n                  v-if=\"user.skills.length > 3\"\n                  class=\"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-600 dark:bg-gray-600 dark:text-gray-300\"\n                >\n                  +{{ user.skills.length - 3 }}\n                </span>\n              </div>\n            </div>\n\n            <!-- Azioni -->\n            <div class=\"flex space-x-2\">\n              <router-link\n                :to=\"`/app/personnel/${user.id}`\"\n                class=\"flex-1 text-center px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700\"\n              >\n                👤 Profilo\n              </router-link>\n              <a\n                v-if=\"user.phone\"\n                :href=\"`tel:${user.phone}`\"\n                class=\"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700\"\n              >\n                📞\n              </a>\n              <a\n                :href=\"`mailto:${user.email}`\"\n                class=\"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700\"\n              >\n                ✉️\n              </a>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Empty State -->\n      <div v-else class=\"text-center py-12\">\n        <div class=\"text-gray-400 dark:text-gray-500 text-6xl mb-4\">👥</div>\n        <h3 class=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">Nessun dipendente trovato</h3>\n        <p class=\"text-gray-500 dark:text-gray-400\">\n          {{ hasActiveFilters ? 'Prova a modificare i filtri di ricerca' : 'Non ci sono dipendenti da visualizzare' }}\n        </p>\n      </div>\n\n      <!-- Paginazione -->\n      <div v-if=\"pagination.pages > 1\" class=\"px-6 py-4 border-t border-gray-200 dark:border-gray-700\">\n        <div class=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\n          <!-- Info risultati -->\n          <div class=\"text-sm text-gray-700 dark:text-gray-300\">\n            Mostrando <span class=\"font-medium\">{{ (pagination.page - 1) * pagination.per_page + 1 }}</span> -\n            <span class=\"font-medium\">{{ Math.min(pagination.page * pagination.per_page, pagination.total) }}</span>\n            di <span class=\"font-medium\">{{ pagination.total }}</span> dipendenti\n          </div>\n\n          <!-- Controlli paginazione -->\n          <nav class=\"flex items-center space-x-1\">\n            <!-- Precedente -->\n            <button\n              v-if=\"pagination.page > 1\"\n              @click=\"changePage(pagination.page - 1)\"\n              class=\"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n            >\n              ← Precedente\n            </button>\n            <span\n              v-else\n              class=\"px-3 py-2 border border-gray-200 dark:border-gray-700 rounded-md text-sm font-medium text-gray-400 dark:text-gray-600 bg-gray-50 dark:bg-gray-900 cursor-not-allowed\"\n            >\n              ← Precedente\n            </span>\n\n            <!-- Numeri pagina -->\n            <template v-for=\"page in getPageNumbers()\" :key=\"page\">\n              <button\n                v-if=\"page !== '...'\"\n                @click=\"changePage(page)\"\n                :class=\"[\n                  'px-3 py-2 border rounded-md text-sm font-medium transition-colors',\n                  page === pagination.page\n                    ? 'border-primary-500 dark:border-primary-400 text-white bg-primary-600 dark:bg-primary-500'\n                    : 'border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700'\n                ]\"\n              >\n                {{ page }}\n              </button>\n              <span v-else class=\"px-2 py-2 text-sm text-gray-500 dark:text-gray-400\">…</span>\n            </template>\n\n            <!-- Successiva -->\n            <button\n              v-if=\"pagination.page < pagination.pages\"\n              @click=\"changePage(pagination.page + 1)\"\n              class=\"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\"\n            >\n              Successiva →\n            </button>\n            <span\n              v-else\n              class=\"px-3 py-2 border border-gray-200 dark:border-gray-700 rounded-md text-sm font-medium text-gray-400 dark:text-gray-600 bg-gray-50 dark:bg-gray-900 cursor-not-allowed\"\n            >\n              Successiva →\n            </span>\n          </nav>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted, watch } from 'vue'\nimport { useRoute, useRouter } from 'vue-router'\nimport { usePersonnelStore } from '@/stores/personnel'\nimport { usePermissions } from '@/composables/usePermissions'\n\nconst route = useRoute()\nconst router = useRouter()\nconst personnelStore = usePersonnelStore()\nconst { hasPermission } = usePermissions()\n\n// Reactive refs\nconst searchQuery = ref('')\nconst searchTimeout = ref(null)\n\n// Store state\nconst {\n  users,\n  departments,\n  skills,\n  loading,\n  error,\n  filters,\n  pagination\n} = personnelStore\n\n// Computed properties\nconst hasAdminAccess = computed(() => hasPermission.value('admin_access'))\n\nconst hasActiveFilters = computed(() => {\n  return searchQuery.value ||\n         filters.department ||\n         filters.skill ||\n         filters.role\n})\n\nconst selectedDepartment = computed(() => {\n  if (!filters.department) return null\n  return departments.find(d => d.id === filters.department)\n})\n\nconst selectedSkill = computed(() => {\n  if (!filters.skill) return null\n  return skills.find(s => s.id === filters.skill)\n})\n\n// Helper functions\nconst getUserInitials = (user) => {\n  const first = user.first_name?.[0] || ''\n  const last = user.last_name?.[0] || ''\n  return (first + last).toUpperCase()\n}\n\nconst getRoleLabel = (role) => {\n  const labels = {\n    'admin': 'Admin',\n    'manager': 'Manager',\n    'employee': 'Dipendente',\n    'human_resources': 'HR',\n    'project_manager': 'PM'\n  }\n  return labels[role] || role\n}\n\nconst getRoleClass = (role) => {\n  const classes = {\n    'admin': 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',\n    'manager': 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',\n    'human_resources': 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',\n    'project_manager': 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'\n  }\n  return classes[role] || 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'\n}\n\nconst getPageNumbers = () => {\n  const pages = []\n  const current = pagination.page\n  const total = pagination.pages\n\n  // Always show first page\n  if (total > 0) pages.push(1)\n\n  // Show pages around current\n  for (let i = Math.max(2, current - 1); i <= Math.min(total - 1, current + 1); i++) {\n    if (!pages.includes(i)) pages.push(i)\n  }\n\n  // Show last page\n  if (total > 1 && !pages.includes(total)) {\n    if (pages[pages.length - 1] < total - 1) pages.push('...')\n    pages.push(total)\n  }\n\n  return pages\n}\n\n// Methods\nconst debounceSearch = () => {\n  clearTimeout(searchTimeout.value)\n  searchTimeout.value = setTimeout(() => {\n    applyFilters()\n  }, 500)\n}\n\nconst applyFilters = async () => {\n  // Update store filters\n  personnelStore.setFilter('search', searchQuery.value)\n\n  // Reset to page 1 when applying filters\n  personnelStore.setPagination(1)\n\n  // Fetch with new filters\n  await loadUsers()\n}\n\nconst clearFilters = async () => {\n  searchQuery.value = ''\n  personnelStore.clearFilters()\n  personnelStore.setPagination(1)\n  await loadUsers()\n}\n\nconst changePage = async (page) => {\n  personnelStore.setPagination(page)\n  await loadUsers()\n}\n\nconst loadUsers = async () => {\n  const params = {\n    page: pagination.page,\n    per_page: pagination.per_page\n  }\n\n  // Add filters\n  if (searchQuery.value) params.search = searchQuery.value\n  if (filters.department) params.department_id = filters.department\n  if (filters.skill) params.skills = filters.skill\n  if (filters.role) params.role = filters.role\n\n  await personnelStore.fetchUsers(params)\n}\n\nconst loadInitialData = async () => {\n  // Load departments and skills for filters\n  await Promise.all([\n    personnelStore.fetchDepartments(),\n    personnelStore.fetchSkills()\n  ])\n\n  // Parse URL parameters for initial filters\n  const urlParams = route.query\n  if (urlParams.search) searchQuery.value = urlParams.search\n  if (urlParams.department) personnelStore.setFilter('department', parseInt(urlParams.department))\n  if (urlParams.skill) personnelStore.setFilter('skill', parseInt(urlParams.skill))\n  if (urlParams.page) personnelStore.setPagination(parseInt(urlParams.page))\n\n  // Load users with initial filters\n  await loadUsers()\n}\n\n// Watchers\nwatch(() => route.query, (newQuery) => {\n  // Update filters when URL changes (for browser back/forward)\n  if (newQuery.search !== searchQuery.value) {\n    searchQuery.value = newQuery.search || ''\n  }\n}, { deep: true })\n\n// Lifecycle\nonMounted(() => {\n  loadInitialData()\n})\n</script>"}
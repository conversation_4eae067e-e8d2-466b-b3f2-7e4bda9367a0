{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/personnel/components/admin/SkillsManagement.vue"}, "originalCode": "<template>\n  <div class=\"space-y-6\">\n    <!-- Header with Stats -->\n    <div class=\"flex flex-col sm:flex-row sm:items-center sm:justify-between\">\n      <div>\n        <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">Gestione Competenze</h3>\n        <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\n          Gestisci il catalogo delle competenze aziendali\n        </p>\n      </div>\n      <div class=\"mt-4 sm:mt-0 flex items-center space-x-3\">\n        <router-link to=\"/app/personnel/skills\"\n                     class=\"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700\">\n          <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\"></path>\n          </svg>\n          Matrice Completa\n        </router-link>\n        <button @click=\"showCreateModal = true\"\n                class=\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700\">\n          <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\"></path>\n          </svg>\n          Nuova Competenza\n        </button>\n      </div>\n    </div>\n\n    <!-- Quick Stats -->\n    <div class=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n      <div class=\"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg\">\n        <div class=\"p-5\">\n          <div class=\"flex items-center\">\n            <div class=\"flex-shrink-0\">\n              <svg class=\"h-6 w-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"></path>\n              </svg>\n            </div>\n            <div class=\"ml-5 w-0 flex-1\">\n              <dl>\n                <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                  Competenze Totali\n                </dt>\n                <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                  {{ skills.length }}\n                </dd>\n              </dl>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg\">\n        <div class=\"p-5\">\n          <div class=\"flex items-center\">\n            <div class=\"flex-shrink-0\">\n              <svg class=\"h-6 w-6 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z\"></path>\n              </svg>\n            </div>\n            <div class=\"ml-5 w-0 flex-1\">\n              <dl>\n                <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                  Categorie\n                </dt>\n                <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                  {{ categories.length }}\n                </dd>\n              </dl>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg\">\n        <div class=\"p-5\">\n          <div class=\"flex items-center\">\n            <div class=\"flex-shrink-0\">\n              <svg class=\"h-6 w-6 text-purple-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"></path>\n              </svg>\n            </div>\n            <div class=\"ml-5 w-0 flex-1\">\n              <dl>\n                <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                  Competenze Assegnate\n                </dt>\n                <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                  {{ totalAssignments }}\n                </dd>\n              </dl>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg\">\n        <div class=\"p-5\">\n          <div class=\"flex items-center\">\n            <div class=\"flex-shrink-0\">\n              <svg class=\"h-6 w-6 text-yellow-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 10V3L4 14h7v7l9-11h-7z\"></path>\n              </svg>\n            </div>\n            <div class=\"ml-5 w-0 flex-1\">\n              <dl>\n                <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                  Livello Medio\n                </dt>\n                <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                  {{ averageLevel.toFixed(1) }}\n                </dd>\n              </dl>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Search and Filters -->\n    <div class=\"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0\">\n      <div class=\"flex-1 max-w-lg\">\n        <div class=\"relative\">\n          <svg class=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"></path>\n          </svg>\n          <input v-model=\"searchQuery\"\n                 type=\"text\"\n                 placeholder=\"Cerca competenze...\"\n                 class=\"pl-10 pr-4 py-2 w-full border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\">\n        </div>\n      </div>\n\n      <div class=\"flex items-center space-x-3\">\n        <select v-model=\"selectedCategory\"\n                class=\"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\">\n          <option value=\"\">Tutte le categorie</option>\n          <option v-for=\"category in categories\" :key=\"category\" :value=\"category\">\n            {{ category }}\n          </option>\n        </select>\n\n        <button @click=\"loadSkills\"\n                :disabled=\"loading\"\n                class=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50\">\n          <svg v-if=\"loading\" class=\"animate-spin w-4 h-4\" fill=\"none\" viewBox=\"0 0 24 24\">\n            <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\"></circle>\n            <path class=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n          </svg>\n          <svg v-else class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"></path>\n          </svg>\n        </button>\n      </div>\n    </div>\n\n    <!-- Skills Grid -->\n    <div v-if=\"filteredSkills.length > 0\" class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n      <div v-for=\"skill in filteredSkills\" :key=\"skill.id\"\n           class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6 hover:shadow-lg transition-shadow\">\n        <div class=\"flex items-start justify-between\">\n          <div class=\"flex-1\">\n            <h4 class=\"text-lg font-medium text-gray-900 dark:text-white\">{{ skill.name }}</h4>\n            <p v-if=\"skill.category\" class=\"text-sm text-blue-600 dark:text-blue-400 mt-1\">{{ skill.category }}</p>\n            <p v-if=\"skill.description\" class=\"text-sm text-gray-500 dark:text-gray-400 mt-2\">{{ skill.description }}</p>\n\n            <div class=\"mt-4 flex items-center justify-between\">\n              <div class=\"flex items-center text-sm text-gray-500 dark:text-gray-400\">\n                <svg class=\"w-4 h-4 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"></path>\n                </svg>\n                {{ skill.user_count }} dipendenti\n              </div>\n\n              <div class=\"flex items-center space-x-2\">\n                <button @click=\"editSkill(skill)\"\n                        class=\"text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300\">\n                  <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"></path>\n                  </svg>\n                </button>\n\n                <button @click=\"deleteSkill(skill)\"\n                        :disabled=\"skill.user_count > 0\"\n                        :class=\"skill.user_count > 0 ? 'text-gray-400 cursor-not-allowed' : 'text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300'\">\n                  <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"></path>\n                  </svg>\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Empty State -->\n    <div v-else-if=\"!loading\" class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-12 text-center\">\n      <svg class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"></path>\n      </svg>\n      <h3 class=\"mt-2 text-sm font-medium text-gray-900 dark:text-white\">Nessuna competenza trovata</h3>\n      <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\n        {{ searchQuery || selectedCategory ? 'Prova a modificare i filtri di ricerca.' : 'Inizia creando la prima competenza.' }}\n      </p>\n      <div class=\"mt-6\">\n        <button @click=\"showCreateModal = true\"\n                class=\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700\">\n          <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\"></path>\n          </svg>\n          Crea Prima Competenza\n        </button>\n      </div>\n    </div>\n\n    <!-- Loading State -->\n    <div v-if=\"loading\" class=\"flex justify-center items-center h-64\">\n      <div class=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n    </div>\n\n    <!-- Create/Edit Modal -->\n    <SkillModal\n      v-if=\"showCreateModal || showEditModal\"\n      :skill=\"editingSkill\"\n      :categories=\"categories\"\n      @close=\"closeModal\"\n      @saved=\"onSkillSaved\"\n    />\n  </div>\n</template>", "modifiedCode": "<template>\n  <div class=\"space-y-6\">\n    <!-- Header with Stats -->\n    <div class=\"flex flex-col sm:flex-row sm:items-center sm:justify-between\">\n      <div>\n        <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">Gestione Competenze</h3>\n        <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\n          Gestisci il catalogo delle competenze aziendali\n        </p>\n      </div>\n      <div class=\"mt-4 sm:mt-0 flex items-center space-x-3\">\n        <router-link to=\"/app/personnel/skills\"\n                     class=\"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700\">\n          <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\"></path>\n          </svg>\n          Matrice Completa\n        </router-link>\n        <button @click=\"showCreateModal = true\"\n                class=\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700\">\n          <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\"></path>\n          </svg>\n          Nuova Competenza\n        </button>\n      </div>\n    </div>\n\n    <!-- Quick Stats -->\n    <div class=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n      <div class=\"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg\">\n        <div class=\"p-5\">\n          <div class=\"flex items-center\">\n            <div class=\"flex-shrink-0\">\n              <svg class=\"h-6 w-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"></path>\n              </svg>\n            </div>\n            <div class=\"ml-5 w-0 flex-1\">\n              <dl>\n                <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                  Competenze Totali\n                </dt>\n                <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                  {{ skills.length }}\n                </dd>\n              </dl>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg\">\n        <div class=\"p-5\">\n          <div class=\"flex items-center\">\n            <div class=\"flex-shrink-0\">\n              <svg class=\"h-6 w-6 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z\"></path>\n              </svg>\n            </div>\n            <div class=\"ml-5 w-0 flex-1\">\n              <dl>\n                <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                  Categorie\n                </dt>\n                <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                  {{ categories.length }}\n                </dd>\n              </dl>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg\">\n        <div class=\"p-5\">\n          <div class=\"flex items-center\">\n            <div class=\"flex-shrink-0\">\n              <svg class=\"h-6 w-6 text-purple-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"></path>\n              </svg>\n            </div>\n            <div class=\"ml-5 w-0 flex-1\">\n              <dl>\n                <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                  Competenze Assegnate\n                </dt>\n                <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                  {{ totalAssignments }}\n                </dd>\n              </dl>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg\">\n        <div class=\"p-5\">\n          <div class=\"flex items-center\">\n            <div class=\"flex-shrink-0\">\n              <svg class=\"h-6 w-6 text-yellow-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 10V3L4 14h7v7l9-11h-7z\"></path>\n              </svg>\n            </div>\n            <div class=\"ml-5 w-0 flex-1\">\n              <dl>\n                <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                  Livello Medio\n                </dt>\n                <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                  {{ averageLevel.toFixed(1) }}\n                </dd>\n              </dl>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Search and Filters -->\n    <div class=\"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0\">\n      <div class=\"flex-1 max-w-lg\">\n        <div class=\"relative\">\n          <svg class=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"></path>\n          </svg>\n          <input v-model=\"searchQuery\"\n                 type=\"text\"\n                 placeholder=\"Cerca competenze...\"\n                 class=\"pl-10 pr-4 py-2 w-full border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\">\n        </div>\n      </div>\n\n      <div class=\"flex items-center space-x-3\">\n        <select v-model=\"selectedCategory\"\n                class=\"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\">\n          <option value=\"\">Tutte le categorie</option>\n          <option v-for=\"category in categories\" :key=\"category\" :value=\"category\">\n            {{ category }}\n          </option>\n        </select>\n\n        <button @click=\"loadSkills\"\n                :disabled=\"loading\"\n                class=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50\">\n          <svg v-if=\"loading\" class=\"animate-spin w-4 h-4\" fill=\"none\" viewBox=\"0 0 24 24\">\n            <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\"></circle>\n            <path class=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n          </svg>\n          <svg v-else class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"></path>\n          </svg>\n        </button>\n      </div>\n    </div>\n\n    <!-- Skills Grid -->\n    <div v-if=\"filteredSkills.length > 0\" class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n      <div v-for=\"skill in filteredSkills\" :key=\"skill.id\"\n           class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6 hover:shadow-lg transition-shadow\">\n        <div class=\"flex items-start justify-between\">\n          <div class=\"flex-1\">\n            <h4 class=\"text-lg font-medium text-gray-900 dark:text-white\">{{ skill.name }}</h4>\n            <p v-if=\"skill.category\" class=\"text-sm text-blue-600 dark:text-blue-400 mt-1\">{{ skill.category }}</p>\n            <p v-if=\"skill.description\" class=\"text-sm text-gray-500 dark:text-gray-400 mt-2\">{{ skill.description }}</p>\n\n            <div class=\"mt-4 flex items-center justify-between\">\n              <div class=\"flex items-center text-sm text-gray-500 dark:text-gray-400\">\n                <svg class=\"w-4 h-4 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"></path>\n                </svg>\n                {{ skill.user_count }} dipendenti\n              </div>\n\n              <div class=\"flex items-center space-x-2\">\n                <button @click=\"editSkill(skill)\"\n                        class=\"text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300\">\n                  <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"></path>\n                  </svg>\n                </button>\n\n                <button @click=\"deleteSkill(skill)\"\n                        :disabled=\"skill.user_count > 0\"\n                        :class=\"skill.user_count > 0 ? 'text-gray-400 cursor-not-allowed' : 'text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300'\">\n                  <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"></path>\n                  </svg>\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Empty State -->\n    <div v-else-if=\"!loading\" class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-12 text-center\">\n      <svg class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"></path>\n      </svg>\n      <h3 class=\"mt-2 text-sm font-medium text-gray-900 dark:text-white\">Nessuna competenza trovata</h3>\n      <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\n        {{ searchQuery || selectedCategory ? 'Prova a modificare i filtri di ricerca.' : 'Inizia creando la prima competenza.' }}\n      </p>\n      <div class=\"mt-6\">\n        <button @click=\"showCreateModal = true\"\n                class=\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700\">\n          <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\"></path>\n          </svg>\n          Crea Prima Competenza\n        </button>\n      </div>\n    </div>\n\n    <!-- Loading State -->\n    <div v-if=\"loading\" class=\"flex justify-center items-center h-64\">\n      <div class=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n    </div>\n\n    <!-- Create/Edit Modal -->\n    <SkillModal\n      v-if=\"showCreateModal || showEditModal\"\n      :skill=\"editingSkill\"\n      :categories=\"categories\"\n      @close=\"closeModal\"\n      @saved=\"onSkillSaved\"\n    />\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted } from 'vue'\nimport SkillModal from './SkillModal.vue'\n\n// Emits\nconst emit = defineEmits(['skill-created', 'skill-updated', 'skill-deleted'])\n\n// Reactive state\nconst skills = ref([])\nconst categories = ref([])\nconst loading = ref(false)\nconst searchQuery = ref('')\nconst selectedCategory = ref('')\nconst showCreateModal = ref(false)\nconst showEditModal = ref(false)\nconst editingSkill = ref(null)\n\n// Computed properties\nconst filteredSkills = computed(() => {\n  let filtered = skills.value\n\n  // Apply search filter\n  if (searchQuery.value.trim()) {\n    const search = searchQuery.value.toLowerCase()\n    filtered = filtered.filter(skill =>\n      skill.name.toLowerCase().includes(search) ||\n      (skill.description && skill.description.toLowerCase().includes(search)) ||\n      (skill.category && skill.category.toLowerCase().includes(search))\n    )\n  }\n\n  // Apply category filter\n  if (selectedCategory.value) {\n    filtered = filtered.filter(skill => skill.category === selectedCategory.value)\n  }\n\n  return filtered\n})\n\nconst totalAssignments = computed(() => {\n  return skills.value.reduce((sum, skill) => sum + (skill.user_count || 0), 0)\n})\n\nconst averageLevel = computed(() => {\n  const skillsWithUsers = skills.value.filter(skill => skill.user_count > 0)\n  if (skillsWithUsers.length === 0) return 0\n\n  // This is a simplified calculation - in reality you'd need user skill data\n  return 3.2 // Placeholder average\n})\n\n// Methods\nconst loadSkills = async () => {\n  loading.value = true\n\n  try {\n    const response = await fetch('/api/personnel/skills', {\n      credentials: 'include'\n    })\n\n    if (!response.ok) {\n      throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n\n    if (data.success) {\n      skills.value = data.data.skills || []\n      categories.value = data.data.categories || []\n    } else {\n      throw new Error(data.message || 'Errore nel caricamento competenze')\n    }\n  } catch (err) {\n    console.error('Error loading skills:', err)\n  } finally {\n    loading.value = false\n  }\n}\n\nconst editSkill = (skill) => {\n  editingSkill.value = { ...skill }\n  showEditModal.value = true\n}\n\nconst deleteSkill = async (skill) => {\n  if (skill.user_count > 0) {\n    alert('Impossibile eliminare una competenza assegnata a dipendenti')\n    return\n  }\n\n  if (!confirm(`Sei sicuro di voler eliminare la competenza \"${skill.name}\"?`)) {\n    return\n  }\n\n  try {\n    const response = await fetch(`/api/personnel/skills/${skill.id}`, {\n      method: 'DELETE',\n      credentials: 'include'\n    })\n\n    if (!response.ok) {\n      throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n\n    if (data.success) {\n      await loadSkills()\n      emit('skill-deleted', skill)\n    } else {\n      throw new Error(data.message || 'Errore nell\\'eliminazione')\n    }\n  } catch (err) {\n    console.error('Error deleting skill:', err)\n    alert('Errore nell\\'eliminazione: ' + err.message)\n  }\n}\n\nconst closeModal = () => {\n  showCreateModal.value = false\n  showEditModal.value = false\n  editingSkill.value = null\n}\n\nconst onSkillSaved = async (skill) => {\n  closeModal()\n  await loadSkills()\n\n  if (editingSkill.value) {\n    emit('skill-updated', skill)\n  } else {\n    emit('skill-created', skill)\n  }\n}\n\n// Lifecycle\nonMounted(() => {\n  loadSkills()\n})\n</script>"}
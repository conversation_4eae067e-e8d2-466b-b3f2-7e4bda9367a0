import{e as _,r as u,f as g}from"./vendor.js";const C=_("personnel",()=>{const c=u([]),d=u([]),v=u([]),p=u(null),l=u(!1),n=u(null),o=u({search:"",department:null,skill:null,role:null,location:null,sort:"name"}),i=u({page:1,per_page:20,total:0,pages:0}),m=g(()=>{let e=c.value;if(o.value.search){const a=o.value.search.toLowerCase();e=e.filter(r=>{var t,s,f;return((t=r.full_name)==null?void 0:t.toLowerCase().includes(a))||((s=r.email)==null?void 0:s.toLowerCase().includes(a))||((f=r.position)==null?void 0:f.toLowerCase().includes(a))})}return o.value.department&&(e=e.filter(a=>a.department_id===o.value.department)),o.value.skill&&(e=e.filter(a=>{var r;return(r=a.skills)==null?void 0:r.some(t=>t.id===o.value.skill)})),o.value.role&&(e=e.filter(a=>a.role===o.value.role)),e}),w=g(()=>{const e=(a=null)=>d.value.filter(r=>r.parent_id===a).map(r=>({...r,children:e(r.id)}));return e()}),E=async(e={})=>{l.value=!0,n.value=null;try{const a=new URLSearchParams({page:e.page||i.value.page,per_page:e.per_page||i.value.per_page,...e}),r=await fetch(`/api/personnel/users?${a}`,{credentials:"include"});if(!r.ok)throw new Error(`HTTP ${r.status}: ${r.statusText}`);const t=await r.json();if(t.success){c.value=t.data.users||[];const s=t.data.pagination||{};i.value={page:s.page||1,per_page:s.per_page||20,total:s.total||0,pages:s.pages||0}}else throw new Error(t.message||"Errore nel caricamento utenti")}catch(a){n.value=a.message,console.error("Errore fetchUsers:",a)}finally{l.value=!1}},y=async e=>{l.value=!0,n.value=null;try{const a=await fetch(`/api/personnel/users/${e}`,{credentials:"include"});if(!a.ok)throw new Error(`HTTP ${a.status}: ${a.statusText}`);const r=await a.json();if(r.success)return p.value=r.data.user,r.data.user;throw new Error(r.message||"Errore nel caricamento utente")}catch(a){throw n.value=a.message,console.error("Errore fetchUser:",a),a}finally{l.value=!1}},T=async(e,a)=>{var r;l.value=!0,n.value=null;try{const t=await fetch(`/api/personnel/users/${e}`,{method:"PUT",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(a)});if(!t.ok)throw new Error(`HTTP ${t.status}: ${t.statusText}`);const s=await t.json();if(s.success){const f=c.value.findIndex(U=>U.id===e);return f!==-1&&(c.value[f]={...c.value[f],...s.data.user}),((r=p.value)==null?void 0:r.id)===e&&(p.value={...p.value,...s.data.user}),s.data.user}else throw new Error(s.message||"Errore nell'aggiornamento utente")}catch(t){throw n.value=t.message,console.error("Errore updateUser:",t),t}finally{l.value=!1}},k=async()=>{l.value=!0,n.value=null;try{const e=await fetch("/api/personnel/departments",{credentials:"include"});if(!e.ok)throw new Error(`HTTP ${e.status}: ${e.statusText}`);const a=await e.json();if(a.success)d.value=a.data.departments||[];else throw new Error(a.message||"Errore nel caricamento dipartimenti")}catch(e){n.value=e.message,console.error("Errore fetchDepartments:",e)}finally{l.value=!1}},$=async()=>{l.value=!0,n.value=null;try{const e=await fetch("/api/personnel/skills",{credentials:"include"});if(!e.ok)throw new Error(`HTTP ${e.status}: ${e.statusText}`);const a=await e.json();if(a.success)v.value=a.data.skills||[];else throw new Error(a.message||"Errore nel caricamento competenze")}catch(e){n.value=e.message,console.error("Errore fetchSkills:",e)}finally{l.value=!1}},P=(e,a)=>{o.value[e]=a},h=()=>{o.value={search:"",department:null,skill:null,role:null,location:null,sort:"name"}};return{users:c,departments:d,skills:v,currentUser:p,loading:l,error:n,filters:o,pagination:i,filteredUsers:m,departmentTree:w,fetchUsers:E,fetchUser:y,updateUser:T,fetchDepartments:k,fetchSkills:$,setFilter:P,clearFilters:h,setPagination:(e,a=null)=>{i.value.page=e,a&&(i.value.per_page=a)},$reset:()=>{c.value=[],d.value=[],v.value=[],p.value=null,l.value=!1,n.value=null,h(),i.value={page:1,per_page:20,total:0,pages:0}}}});export{C as u};

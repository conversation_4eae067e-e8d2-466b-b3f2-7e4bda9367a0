import{r as u,f as h,A as F,c as a,j as e,I as P,g as x,m as B,v as L,x as I,H as G,t as i,F as U,k as R,l as Q,b as q,o as l,a as J,i as K}from"./vendor.js";import{c as O,a as W}from"./app.js";const X={class:"department-list"},Y={class:"flex justify-between items-center mb-6"},Z={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-4 mb-6"},ee={class:"flex flex-col sm:flex-row gap-4"},te={class:"flex-1"},re={class:"relative"},se={class:"sm:w-48"},ae={class:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6"},le={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-4"},oe={class:"flex items-center"},ie={class:"ml-4"},de={class:"text-2xl font-semibold text-gray-900 dark:text-white"},ne={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-4"},ue={class:"flex items-center"},ce={class:"ml-4"},ge={class:"text-2xl font-semibold text-gray-900 dark:text-white"},xe={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-4"},ve={class:"flex items-center"},me={class:"ml-4"},pe={class:"text-2xl font-semibold text-gray-900 dark:text-white"},he={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-4"},fe={class:"flex items-center"},ye={class:"ml-4"},ke={class:"text-2xl font-semibold text-gray-900 dark:text-white"},we={key:0,class:"flex justify-center items-center h-64"},be={key:1,class:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6"},_e={class:"flex"},ze={class:"text-sm text-red-700 dark:text-red-300 mt-1"},Ce={key:2,class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},Me={class:"overflow-x-auto"},De={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},Be={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},Le={class:"px-6 py-4 whitespace-nowrap"},Ee={class:"flex items-center"},He={class:"ml-4"},Te={class:"text-sm font-medium text-gray-900 dark:text-white"},Ve={key:0,class:"text-sm text-gray-500 dark:text-gray-400"},je={class:"px-6 py-4 whitespace-nowrap"},$e={key:0,class:"flex items-center"},Ne={class:"ml-3"},Se={class:"text-sm font-medium text-gray-900 dark:text-white"},Ae={class:"text-sm text-gray-500 dark:text-gray-400"},Fe={key:1,class:"text-sm text-gray-500 dark:text-gray-400"},Pe={class:"px-6 py-4 whitespace-nowrap"},Ie={class:"flex items-center"},Ge={class:"text-sm font-medium text-gray-900 dark:text-white"},Ue={class:"px-6 py-4 whitespace-nowrap"},Re={key:0,class:"text-sm text-gray-900 dark:text-white"},Qe={key:1,class:"text-sm text-gray-500 dark:text-gray-400"},qe={class:"px-6 py-4 whitespace-nowrap"},Je={key:0,class:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"},Ke={key:1,class:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"},Oe={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},We={class:"flex items-center justify-end space-x-2"},Xe=["onClick"],Ye=["onClick"],Ze={key:3,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-12 text-center"},et={class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},tt={key:0,class:"mt-6"},lt={__name:"DepartmentList",setup(rt){Q(),O();const{hasPermission:E}=W(),d=u([]),f=u(!1),v=u(null),c=u(""),n=u(""),y=u(!1),H=u(null),m=h(()=>E("manage_users")),z=h(()=>{let r=d.value;if(c.value.trim()){const t=c.value.toLowerCase();r=r.filter(o=>o.name.toLowerCase().includes(t)||o.description&&o.description.toLowerCase().includes(t)||o.manager&&o.manager.full_name.toLowerCase().includes(t))}return n.value&&(n.value==="root"?r=r.filter(t=>!t.parent_id):n.value==="sub"&&(r=r.filter(t=>t.parent_id))),r}),k=h(()=>c.value.trim()!==""||n.value!==""),p=h(()=>{const r=d.value.length,t=d.value.filter(g=>g.manager_id).length,o=d.value.reduce((g,b)=>g+(b.user_count||0),0),s=(g,b=null,M=0)=>{const D=g.filter(_=>_.parent_id===b);return D.length===0?M:Math.max(...D.map(_=>s(g,_.id,M+1)))},w=s(d.value)+1;return{totalDepartments:r,managersAssigned:t,totalEmployees:o,hierarchyLevels:w}}),T=r=>new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(r);let C=null;const V=()=>{clearTimeout(C),C=setTimeout(()=>{},300)},j=async()=>{f.value=!0,v.value=null;try{const r=await fetch("/api/personnel/departments",{credentials:"include"});if(!r.ok)throw new Error(`HTTP ${r.status}: ${r.statusText}`);const t=await r.json();if(t.success)d.value=t.data.departments||[];else throw new Error(t.message||"Errore nel caricamento dei dipartimenti")}catch(r){console.error("Error fetching departments:",r),v.value=r.message}finally{f.value=!1}},$=async r=>{if(confirm(`Sei sicuro di voler eliminare il dipartimento "${r.name}"?`))try{const t=await fetch(`/api/personnel/departments/${r.id}`,{method:"DELETE",credentials:"include"});if(!t.ok)throw new Error(`HTTP ${t.status}: ${t.statusText}`);const o=await t.json();if(o.success)d.value=d.value.filter(s=>s.id!==r.id),alert(o.message||"Dipartimento eliminato con successo");else throw new Error(o.message||"Errore nell'eliminazione del dipartimento")}catch(t){console.error("Error deleting department:",t),alert("Errore nell'eliminazione del dipartimento: "+t.message)}},N=r=>{H.value=r,y.value=!0},S=()=>{},A=()=>{c.value="",n.value=""};return F(()=>{j()}),(r,t)=>{const o=q("router-link");return l(),a("div",X,[e("div",Y,[t[5]||(t[5]=P('<div><div class="flex items-center"><svg class="w-8 h-8 text-blue-600 dark:text-blue-400 mr-3" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z" clip-rule="evenodd"></path></svg><h1 class="text-2xl font-bold text-gray-900 dark:text-white">Gestione Dipartimenti</h1></div><p class="text-gray-600 dark:text-gray-400 mt-1">Gestisci la struttura organizzativa aziendale</p></div>',1)),m.value?(l(),a("button",{key:0,onClick:t[0]||(t[0]=s=>y.value=!0),class:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors duration-200"},t[4]||(t[4]=[e("svg",{class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z","clip-rule":"evenodd"})],-1),B(" Nuovo Dipartimento ")]))):x("",!0)]),e("div",Z,[e("div",ee,[e("div",te,[e("div",re,[t[6]||(t[6]=e("svg",{class:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z","clip-rule":"evenodd"})],-1)),L(e("input",{"onUpdate:modelValue":t[1]||(t[1]=s=>c.value=s),onInput:V,type:"text",placeholder:"Cerca dipartimenti...",class:"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,544),[[I,c.value]])])]),e("div",se,[L(e("select",{"onUpdate:modelValue":t[2]||(t[2]=s=>n.value=s),onChange:S,class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},t[7]||(t[7]=[e("option",{value:""},"Tutti i livelli",-1),e("option",{value:"root"},"Solo dipartimenti principali",-1),e("option",{value:"sub"},"Solo sotto-dipartimenti",-1)]),544),[[G,n.value]])]),k.value?(l(),a("button",{key:0,onClick:A,class:"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"}," Pulisci Filtri ")):x("",!0)])]),e("div",ae,[e("div",le,[e("div",oe,[t[9]||(t[9]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-blue-600 dark:text-blue-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z","clip-rule":"evenodd"})])])],-1)),e("div",ie,[t[8]||(t[8]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400"},"Totale Dipartimenti",-1)),e("p",de,i(p.value.totalDepartments),1)])])]),e("div",ne,[e("div",ue,[t[11]||(t[11]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-green-600 dark:text-green-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z","clip-rule":"evenodd"})])])],-1)),e("div",ce,[t[10]||(t[10]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400"},"Manager Assegnati",-1)),e("p",ge,i(p.value.managersAssigned),1)])])]),e("div",xe,[e("div",ve,[t[13]||(t[13]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-purple-600 dark:text-purple-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z","clip-rule":"evenodd"})])])],-1)),e("div",me,[t[12]||(t[12]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400"},"Totale Dipendenti",-1)),e("p",pe,i(p.value.totalEmployees),1)])])]),e("div",he,[e("div",fe,[t[15]||(t[15]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-yellow-100 dark:bg-yellow-900 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-yellow-600 dark:text-yellow-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z","clip-rule":"evenodd"})])])],-1)),e("div",ye,[t[14]||(t[14]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400"},"Livelli Gerarchia",-1)),e("p",ke,i(p.value.hierarchyLevels),1)])])])]),f.value?(l(),a("div",we,t[16]||(t[16]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):v.value?(l(),a("div",be,[e("div",_e,[t[18]||(t[18]=e("svg",{class:"w-5 h-5 text-red-400 mr-2 mt-0.5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z","clip-rule":"evenodd"})],-1)),e("div",null,[t[17]||(t[17]=e("h3",{class:"text-sm font-medium text-red-800 dark:text-red-200"},"Errore nel caricamento dei dipartimenti",-1)),e("p",ze,i(v.value),1)])])])):z.value.length>0?(l(),a("div",Ce,[e("div",Me,[e("table",De,[t[25]||(t[25]=e("thead",{class:"bg-gray-50 dark:bg-gray-700"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Dipartimento "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Manager "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Dipendenti "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Budget "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Gerarchia "),e("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Azioni ")])],-1)),e("tbody",Be,[(l(!0),a(U,null,R(z.value,s=>(l(),a("tr",{key:s.id,class:"hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"},[e("td",Le,[e("div",Ee,[t[19]||(t[19]=e("div",{class:"flex-shrink-0 w-8 h-8"},[e("div",{class:"w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-blue-600 dark:text-blue-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z","clip-rule":"evenodd"})])])],-1)),e("div",He,[e("div",Te,i(s.name),1),s.description?(l(),a("div",Ve,i(s.description),1)):x("",!0)])])]),e("td",je,[s.manager?(l(),a("div",$e,[t[20]||(t[20]=e("div",{class:"flex-shrink-0 w-8 h-8"},[e("div",{class:"w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-gray-600 dark:text-gray-300",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z","clip-rule":"evenodd"})])])],-1)),e("div",Ne,[e("div",Se,i(s.manager.full_name),1),e("div",Ae,i(s.manager.email),1)])])):(l(),a("span",Fe,"Nessun manager"))]),e("td",Pe,[e("div",Ie,[e("span",Ge,i(s.user_count),1),t[21]||(t[21]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400 ml-1"},"dipendenti",-1))])]),e("td",Ue,[s.budget?(l(),a("span",Re,i(T(s.budget)),1)):(l(),a("span",Qe,"Non specificato"))]),e("td",qe,[s.parent_id?(l(),a("span",Je," Sotto-dipartimento ")):(l(),a("span",Ke," Dipartimento principale "))]),e("td",Oe,[e("div",We,[J(o,{to:`/app/personnel/departments/${s.id}`,class:"text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 transition-colors duration-200"},{default:K(()=>t[22]||(t[22]=[e("svg",{class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M10 12a2 2 0 100-4 2 2 0 000 4z"}),e("path",{"fill-rule":"evenodd",d:"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z","clip-rule":"evenodd"})],-1)])),_:2,__:[22]},1032,["to"]),m.value?(l(),a("button",{key:0,onClick:w=>N(s),class:"text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 transition-colors duration-200"},t[23]||(t[23]=[e("svg",{class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"})],-1)]),8,Xe)):x("",!0),m.value&&s.user_count===0?(l(),a("button",{key:1,onClick:w=>$(s),class:"text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 transition-colors duration-200"},t[24]||(t[24]=[e("svg",{class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M9 2a1 1 0 000 2h2a1 1 0 100-2H9z","clip-rule":"evenodd"}),e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414L8.586 12l-2.293 2.293a1 1 0 101.414 1.414L10 13.414l2.293 2.293a1 1 0 001.414-1.414L11.414 12l2.293-2.293z","clip-rule":"evenodd"})],-1)]),8,Ye)):x("",!0)])])]))),128))])])])])):(l(),a("div",Ze,[t[27]||(t[27]=e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})],-1)),t[28]||(t[28]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun dipartimento trovato",-1)),e("p",et,i(k.value?"Prova a modificare i filtri di ricerca.":"Inizia creando il primo dipartimento."),1),m.value&&!k.value?(l(),a("div",tt,[e("button",{onClick:t[3]||(t[3]=s=>y.value=!0),class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},t[26]||(t[26]=[e("svg",{class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z","clip-rule":"evenodd"})],-1),B(" Crea Primo Dipartimento ")]))])):x("",!0)]))])}}};export{lt as default};

{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/personnel/components/admin/UsersManagement.vue"}, "originalCode": "<template>\n  <div class=\"space-y-6\">\n    <!-- Header with Search and Filters -->\n    <div class=\"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0\">\n      <div class=\"flex-1 max-w-lg\">\n        <div class=\"relative\">\n          <svg class=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"></path>\n          </svg>\n          <input v-model=\"searchQuery\"\n                 type=\"text\"\n                 placeholder=\"Cerca dipendenti...\"\n                 class=\"pl-10 pr-4 py-2 w-full border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\">\n        </div>\n      </div>\n\n      <div class=\"flex items-center space-x-3\">\n        <!-- Filters -->\n        <select v-model=\"selectedDepartment\"\n                class=\"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\">\n          <option value=\"\">Tutti i dipartimenti</option>\n          <option v-for=\"dept in departments\" :key=\"dept.id\" :value=\"dept.id\">\n            {{ dept.name }}\n          </option>\n        </select>\n\n        <select v-model=\"selectedRole\"\n                class=\"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\">\n          <option value=\"\">Tutti i ruoli</option>\n          <option value=\"admin\">Admin</option>\n          <option value=\"manager\">Manager</option>\n          <option value=\"employee\">Dipendente</option>\n        </select>\n\n        <button @click=\"loadUsers\"\n                :disabled=\"loading\"\n                class=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50\">\n          <svg v-if=\"loading\" class=\"animate-spin w-4 h-4\" fill=\"none\" viewBox=\"0 0 24 24\">\n            <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\"></circle>\n            <path class=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n          </svg>\n          <svg v-else class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"></path>\n          </svg>\n        </button>\n      </div>\n    </div>\n\n    <!-- Loading State -->\n    <div v-if=\"loading && !users.length\" class=\"flex justify-center items-center h-64\">\n      <div class=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n    </div>\n\n    <!-- Error State -->\n    <div v-else-if=\"error\" class=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4\">\n      <div class=\"flex\">\n        <svg class=\"w-5 h-5 text-red-400 mr-2 mt-0.5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n          <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clip-rule=\"evenodd\"></path>\n        </svg>\n        <div>\n          <h3 class=\"text-sm font-medium text-red-800 dark:text-red-200\">Errore nel caricamento</h3>\n          <p class=\"text-sm text-red-700 dark:text-red-300 mt-1\">{{ error }}</p>\n        </div>\n      </div>\n    </div>\n\n    <!-- Users Table -->\n    <div v-else-if=\"users.length > 0\" class=\"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden\">\n      <div class=\"overflow-x-auto\">\n        <table class=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\n          <thead class=\"bg-gray-50 dark:bg-gray-700\">\n            <tr>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Dipendente\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Ruolo\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Dipartimento\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Contratto\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Stato\n              </th>\n              <th class=\"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Azioni\n              </th>\n            </tr>\n          </thead>\n          <tbody class=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\n            <tr v-for=\"user in paginatedUsers\" :key=\"user.id\" class=\"hover:bg-gray-50 dark:hover:bg-gray-700\">\n              <!-- User Info -->\n              <td class=\"px-6 py-4 whitespace-nowrap\">\n                <div class=\"flex items-center\">\n                  <div class=\"flex-shrink-0 w-10 h-10\">\n                    <div class=\"w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center\">\n                      <img v-if=\"user.profile_image\"\n                           :src=\"user.profile_image\"\n                           :alt=\"user.full_name\"\n                           class=\"w-10 h-10 rounded-full object-cover\">\n                      <svg v-else class=\"w-5 h-5 text-gray-600 dark:text-gray-300\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fill-rule=\"evenodd\" d=\"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\" clip-rule=\"evenodd\"></path>\n                      </svg>\n                    </div>\n                  </div>\n                  <div class=\"ml-4\">\n                    <div class=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      {{ user.full_name }}\n                    </div>\n                    <div class=\"text-sm text-gray-500 dark:text-gray-400\">\n                      {{ user.email }}\n                    </div>\n                    <div v-if=\"user.position\" class=\"text-xs text-gray-400 dark:text-gray-500\">\n                      {{ user.position }}\n                    </div>\n                  </div>\n                </div>\n              </td>\n\n              <!-- Role -->\n              <td class=\"px-6 py-4 whitespace-nowrap\">\n                <span :class=\"[\n                  'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',\n                  user.role === 'admin' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :\n                  user.role === 'manager' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :\n                  'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'\n                ]\">\n                  {{ getRoleLabel(user.role) }}\n                </span>\n              </td>\n\n              <!-- Department -->\n              <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                {{ user.department?.name || 'Nessun dipartimento' }}\n              </td>\n\n              <!-- Contract Info -->\n              <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\">\n                <div class=\"space-y-1\">\n                  <div v-if=\"user.hire_date\">\n                    Assunto: {{ formatDate(user.hire_date) }}\n                  </div>\n                  <div v-if=\"user.profile?.employment_type\">\n                    {{ getEmploymentTypeLabel(user.profile.employment_type) }}\n                  </div>\n                  <div v-if=\"user.profile?.contract_end_date\"\n                       :class=\"isContractExpiringSoon(user.profile.contract_end_date) ? 'text-red-600 dark:text-red-400 font-medium' : ''\">\n                    Scade: {{ formatDate(user.profile.contract_end_date) }}\n                  </div>\n                </div>\n              </td>\n\n              <!-- Status -->\n              <td class=\"px-6 py-4 whitespace-nowrap\">\n                <span :class=\"[\n                  'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',\n                  user.is_active ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :\n                  'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'\n                ]\">\n                  {{ user.is_active ? 'Attivo' : 'Disattivato' }}\n                </span>\n              </td>\n\n              <!-- Actions -->\n              <td class=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                <div class=\"flex items-center justify-end space-x-2\">\n                  <router-link :to=\"`/app/personnel/${user.id}`\"\n                               class=\"text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300\">\n                    <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"></path>\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"></path>\n                    </svg>\n                  </router-link>\n\n                  <button @click=\"editUser(user)\"\n                          class=\"text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300\">\n                    <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"></path>\n                    </svg>\n                  </button>\n\n                  <button @click=\"resetPassword(user)\"\n                          class=\"text-yellow-600 dark:text-yellow-400 hover:text-yellow-900 dark:hover:text-yellow-300\">\n                    <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z\"></path>\n                    </svg>\n                  </button>\n\n                  <button @click=\"toggleUserStatus(user)\"\n                          :class=\"user.is_active ? 'text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300' : 'text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300'\">\n                    <svg v-if=\"user.is_active\" class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728\"></path>\n                    </svg>\n                    <svg v-else class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n                    </svg>\n                  </button>\n                </div>\n              </td>\n            </tr>\n          </tbody>\n        </table>\n      </div>\n\n      <!-- Pagination -->\n      <div v-if=\"totalPages > 1\" class=\"bg-white dark:bg-gray-800 px-4 py-3 border-t border-gray-200 dark:border-gray-700 sm:px-6\">\n        <div class=\"flex items-center justify-between\">\n          <div class=\"flex-1 flex justify-between sm:hidden\">\n            <button @click=\"previousPage\"\n                    :disabled=\"currentPage === 1\"\n                    class=\"relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50\">\n              Precedente\n            </button>\n            <button @click=\"nextPage\"\n                    :disabled=\"currentPage === totalPages\"\n                    class=\"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50\">\n              Successiva\n            </button>\n          </div>\n          <div class=\"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between\">\n            <div>\n              <p class=\"text-sm text-gray-700 dark:text-gray-300\">\n                Mostrando <span class=\"font-medium\">{{ (currentPage - 1) * itemsPerPage + 1 }}</span> a\n                <span class=\"font-medium\">{{ Math.min(currentPage * itemsPerPage, filteredUsers.length) }}</span> di\n                <span class=\"font-medium\">{{ filteredUsers.length }}</span> risultati\n              </p>\n            </div>\n            <div>\n              <nav class=\"relative z-0 inline-flex rounded-md shadow-sm -space-x-px\">\n                <button @click=\"previousPage\"\n                        :disabled=\"currentPage === 1\"\n                        class=\"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50\">\n                  <svg class=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fill-rule=\"evenodd\" d=\"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z\" clip-rule=\"evenodd\"></path>\n                  </svg>\n                </button>\n\n                <button v-for=\"page in visiblePages\"\n                        :key=\"page\"\n                        @click=\"currentPage = page\"\n                        :class=\"[\n                          'relative inline-flex items-center px-4 py-2 border text-sm font-medium',\n                          page === currentPage\n                            ? 'z-10 bg-blue-50 dark:bg-blue-900 border-blue-500 text-blue-600 dark:text-blue-200'\n                            : 'bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700'\n                        ]\">\n                  {{ page }}\n                </button>\n\n                <button @click=\"nextPage\"\n                        :disabled=\"currentPage === totalPages\"\n                        class=\"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50\">\n                  <svg class=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fill-rule=\"evenodd\" d=\"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\" clip-rule=\"evenodd\"></path>\n                  </svg>\n                </button>\n              </nav>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Empty State -->\n    <div v-else class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-12 text-center\">\n      <svg class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\n      </svg>\n      <h3 class=\"mt-2 text-sm font-medium text-gray-900 dark:text-white\">Nessun dipendente trovato</h3>\n      <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\n        {{ hasActiveFilters ? 'Prova a modificare i filtri di ricerca.' : 'Inizia creando il primo dipendente.' }}\n      </p>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted, watch } from 'vue'\n\n// Emits\nconst emit = defineEmits(['user-created', 'user-updated', 'user-deleted'])\n\n// Reactive state\nconst users = ref([])\nconst departments = ref([])\nconst loading = ref(false)\nconst error = ref(null)\nconst searchQuery = ref('')\nconst selectedDepartment = ref('')\nconst selectedRole = ref('')\nconst currentPage = ref(1)\nconst itemsPerPage = ref(20)\n\n// Computed properties\nconst filteredUsers = computed(() => {\n  let filtered = users.value\n\n  // Apply search filter\n  if (searchQuery.value.trim()) {\n    const search = searchQuery.value.toLowerCase()\n    filtered = filtered.filter(user =>\n      user.full_name.toLowerCase().includes(search) ||\n      user.email.toLowerCase().includes(search) ||\n      (user.position && user.position.toLowerCase().includes(search)) ||\n      (user.department && user.department.name.toLowerCase().includes(search))\n    )\n  }\n\n  // Apply department filter\n  if (selectedDepartment.value) {\n    filtered = filtered.filter(user => user.department_id == selectedDepartment.value)\n  }\n\n  // Apply role filter\n  if (selectedRole.value) {\n    filtered = filtered.filter(user => user.role === selectedRole.value)\n  }\n\n  return filtered\n})\n\nconst paginatedUsers = computed(() => {\n  const start = (currentPage.value - 1) * itemsPerPage.value\n  const end = start + itemsPerPage.value\n  return filteredUsers.value.slice(start, end)\n})\n\nconst totalPages = computed(() => {\n  return Math.ceil(filteredUsers.value.length / itemsPerPage.value)\n})\n\nconst visiblePages = computed(() => {\n  const pages = []\n  const total = totalPages.value\n  const current = currentPage.value\n\n  if (total <= 7) {\n    for (let i = 1; i <= total; i++) {\n      pages.push(i)\n    }\n  } else {\n    if (current <= 4) {\n      for (let i = 1; i <= 5; i++) {\n        pages.push(i)\n      }\n      pages.push('...')\n      pages.push(total)\n    } else if (current >= total - 3) {\n      pages.push(1)\n      pages.push('...')\n      for (let i = total - 4; i <= total; i++) {\n        pages.push(i)\n      }\n    } else {\n      pages.push(1)\n      pages.push('...')\n      for (let i = current - 1; i <= current + 1; i++) {\n        pages.push(i)\n      }\n      pages.push('...')\n      pages.push(total)\n    }\n  }\n\n  return pages.filter(page => page !== '...' || pages.indexOf(page) === pages.lastIndexOf(page))\n})\n\nconst hasActiveFilters = computed(() => {\n  return searchQuery.value.trim() !== '' || selectedDepartment.value !== '' || selectedRole.value !== ''\n})\n\n// Methods\nconst loadUsers = async () => {\n  loading.value = true\n  error.value = null\n\n  try {\n    const response = await fetch('/api/personnel/users', {\n      credentials: 'include'\n    })\n\n    if (!response.ok) {\n      throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n\n    if (data.success) {\n      users.value = data.data.users || []\n    } else {\n      throw new Error(data.message || 'Errore nel caricamento utenti')\n    }\n  } catch (err) {\n    console.error('Error loading users:', err)\n    error.value = err.message\n  } finally {\n    loading.value = false\n  }\n}\n\nconst loadDepartments = async () => {\n  try {\n    const response = await fetch('/api/personnel/departments', {\n      credentials: 'include'\n    })\n\n    if (!response.ok) {\n      throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n\n    if (data.success) {\n      departments.value = data.data.departments || []\n    }\n  } catch (err) {\n    console.error('Error loading departments:', err)\n  }\n}\n\nconst editUser = (user) => {\n  // TODO: Open edit modal\n  console.log('Edit user:', user)\n}\n\nconst resetPassword = async (user) => {\n  if (!confirm(`Sei sicuro di voler resettare la password di ${user.full_name}?`)) {\n    return\n  }\n\n  try {\n    const response = await fetch(`/api/personnel/admin/users/${user.id}/reset-password`, {\n      method: 'POST',\n      credentials: 'include'\n    })\n\n    if (!response.ok) {\n      throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n\n    if (data.success) {\n      alert(`Password resettata per ${user.full_name}. Nuova password temporanea: ${data.data.temporary_password}`)\n    } else {\n      throw new Error(data.message || 'Errore nel reset password')\n    }\n  } catch (err) {\n    console.error('Error resetting password:', err)\n    alert('Errore nel reset della password: ' + err.message)\n  }\n}\n\nconst toggleUserStatus = async (user) => {\n  const action = user.is_active ? 'disattivare' : 'riattivare'\n  if (!confirm(`Sei sicuro di voler ${action} ${user.full_name}?`)) {\n    return\n  }\n\n  try {\n    if (!user.is_active) {\n      // Reactivate user - update via PUT\n      const response = await fetch(`/api/personnel/users/${user.id}`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        credentials: 'include',\n        body: JSON.stringify({ is_active: true })\n      })\n\n      if (!response.ok) {\n        throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n      }\n\n      const data = await response.json()\n      if (data.success) {\n        user.is_active = true\n        emit('user-updated', user)\n      }\n    } else {\n      // Deactivate user - use DELETE\n      const response = await fetch(`/api/personnel/admin/users/${user.id}`, {\n        method: 'DELETE',\n        credentials: 'include'\n      })\n\n      if (!response.ok) {\n        throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n      }\n\n      const data = await response.json()\n      if (data.success) {\n        user.is_active = false\n        emit('user-deleted', user)\n      }\n    }\n  } catch (err) {\n    console.error('Error toggling user status:', err)\n    alert('Errore nell\\'operazione: ' + err.message)\n  }\n}\n\nconst previousPage = () => {\n  if (currentPage.value > 1) {\n    currentPage.value--\n  }\n}\n\nconst nextPage = () => {\n  if (currentPage.value < totalPages.value) {\n    currentPage.value++\n  }\n}\n\nconst getRoleLabel = (role) => {\n  const labels = {\n    'admin': 'Admin',\n    'manager': 'Manager',\n    'employee': 'Dipendente'\n  }\n  return labels[role] || role\n}\n\nconst getEmploymentTypeLabel = (type) => {\n  const labels = {\n    'full_time': 'Tempo pieno',\n    'part_time': 'Part-time',\n    'contractor': 'Consulente',\n    'intern': 'Stagista'\n  }\n  return labels[type] || type\n}\n\nconst formatDate = (dateString) => {\n  if (!dateString) return ''\n  const date = new Date(dateString)\n  return date.toLocaleDateString('it-IT', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric'\n  })\n}\n\nconst isContractExpiringSoon = (contractEndDate) => {\n  if (!contractEndDate) return false\n  const today = new Date()\n  const endDate = new Date(contractEndDate)\n  const diffTime = endDate - today\n  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))\n  return diffDays <= 90 && diffDays >= 0\n}\n\n// Watchers\nwatch([searchQuery, selectedDepartment, selectedRole], () => {\n  currentPage.value = 1\n})\n\n// Lifecycle\nonMounted(async () => {\n  await Promise.all([\n    loadUsers(),\n    loadDepartments()\n  ])\n})\n</script>\n\n<style scoped>\n/* Custom styles for users management */\n.table-container {\n  max-height: 600px;\n  overflow-y: auto;\n}\n\n/* Responsive adjustments */\n@media (max-width: 768px) {\n  .table-container {\n    max-height: none;\n  }\n}\n</style>\n", "modifiedCode": "<template>\n  <div class=\"space-y-6\">\n    <!-- Header with Search and Filters -->\n    <div class=\"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0\">\n      <div class=\"flex-1 max-w-lg\">\n        <div class=\"relative\">\n          <svg class=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"></path>\n          </svg>\n          <input v-model=\"searchQuery\"\n                 type=\"text\"\n                 placeholder=\"Cerca dipendenti...\"\n                 class=\"pl-10 pr-4 py-2 w-full border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\">\n        </div>\n      </div>\n\n      <div class=\"flex items-center space-x-3\">\n        <!-- Filters -->\n        <select v-model=\"selectedDepartment\"\n                class=\"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\">\n          <option value=\"\">Tutti i dipartimenti</option>\n          <option v-for=\"dept in departments\" :key=\"dept.id\" :value=\"dept.id\">\n            {{ dept.name }}\n          </option>\n        </select>\n\n        <select v-model=\"selectedRole\"\n                class=\"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\">\n          <option value=\"\">Tutti i ruoli</option>\n          <option value=\"admin\">Admin</option>\n          <option value=\"manager\">Manager</option>\n          <option value=\"employee\">Dipendente</option>\n        </select>\n\n        <!-- Items per page -->\n        <select v-model=\"itemsPerPage\"\n                class=\"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\">\n          <option :value=\"10\">10 per pagina</option>\n          <option :value=\"20\">20 per pagina</option>\n          <option :value=\"50\">50 per pagina</option>\n          <option :value=\"100\">100 per pagina</option>\n        </select>\n\n        <button @click=\"loadUsers\"\n                :disabled=\"loading\"\n                class=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50\">\n          <svg v-if=\"loading\" class=\"animate-spin w-4 h-4\" fill=\"none\" viewBox=\"0 0 24 24\">\n            <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\"></circle>\n            <path class=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n          </svg>\n          <svg v-else class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"></path>\n          </svg>\n        </button>\n      </div>\n    </div>\n\n    <!-- Loading State -->\n    <div v-if=\"loading && !users.length\" class=\"flex justify-center items-center h-64\">\n      <div class=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n    </div>\n\n    <!-- Error State -->\n    <div v-else-if=\"error\" class=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4\">\n      <div class=\"flex\">\n        <svg class=\"w-5 h-5 text-red-400 mr-2 mt-0.5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n          <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clip-rule=\"evenodd\"></path>\n        </svg>\n        <div>\n          <h3 class=\"text-sm font-medium text-red-800 dark:text-red-200\">Errore nel caricamento</h3>\n          <p class=\"text-sm text-red-700 dark:text-red-300 mt-1\">{{ error }}</p>\n        </div>\n      </div>\n    </div>\n\n    <!-- Users Table -->\n    <div v-else-if=\"users.length > 0\" class=\"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden\">\n      <div class=\"overflow-x-auto\">\n        <table class=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\n          <thead class=\"bg-gray-50 dark:bg-gray-700\">\n            <tr>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Dipendente\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Ruolo\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Dipartimento\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Contratto\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Stato\n              </th>\n              <th class=\"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Azioni\n              </th>\n            </tr>\n          </thead>\n          <tbody class=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\n            <tr v-for=\"user in paginatedUsers\" :key=\"user.id\" class=\"hover:bg-gray-50 dark:hover:bg-gray-700\">\n              <!-- User Info -->\n              <td class=\"px-6 py-4 whitespace-nowrap\">\n                <div class=\"flex items-center\">\n                  <div class=\"flex-shrink-0 w-10 h-10\">\n                    <div class=\"w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center\">\n                      <img v-if=\"user.profile_image\"\n                           :src=\"user.profile_image\"\n                           :alt=\"user.full_name\"\n                           class=\"w-10 h-10 rounded-full object-cover\">\n                      <svg v-else class=\"w-5 h-5 text-gray-600 dark:text-gray-300\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fill-rule=\"evenodd\" d=\"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\" clip-rule=\"evenodd\"></path>\n                      </svg>\n                    </div>\n                  </div>\n                  <div class=\"ml-4\">\n                    <div class=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      {{ user.full_name }}\n                    </div>\n                    <div class=\"text-sm text-gray-500 dark:text-gray-400\">\n                      {{ user.email }}\n                    </div>\n                    <div v-if=\"user.position\" class=\"text-xs text-gray-400 dark:text-gray-500\">\n                      {{ user.position }}\n                    </div>\n                  </div>\n                </div>\n              </td>\n\n              <!-- Role -->\n              <td class=\"px-6 py-4 whitespace-nowrap\">\n                <span :class=\"[\n                  'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',\n                  user.role === 'admin' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :\n                  user.role === 'manager' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :\n                  'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'\n                ]\">\n                  {{ getRoleLabel(user.role) }}\n                </span>\n              </td>\n\n              <!-- Department -->\n              <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                {{ user.department?.name || 'Nessun dipartimento' }}\n              </td>\n\n              <!-- Contract Info -->\n              <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\">\n                <div class=\"space-y-1\">\n                  <div v-if=\"user.hire_date\">\n                    Assunto: {{ formatDate(user.hire_date) }}\n                  </div>\n                  <div v-if=\"user.profile?.employment_type\">\n                    {{ getEmploymentTypeLabel(user.profile.employment_type) }}\n                  </div>\n                  <div v-if=\"user.profile?.contract_end_date\"\n                       :class=\"isContractExpiringSoon(user.profile.contract_end_date) ? 'text-red-600 dark:text-red-400 font-medium' : ''\">\n                    Scade: {{ formatDate(user.profile.contract_end_date) }}\n                  </div>\n                </div>\n              </td>\n\n              <!-- Status -->\n              <td class=\"px-6 py-4 whitespace-nowrap\">\n                <span :class=\"[\n                  'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',\n                  user.is_active ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :\n                  'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'\n                ]\">\n                  {{ user.is_active ? 'Attivo' : 'Disattivato' }}\n                </span>\n              </td>\n\n              <!-- Actions -->\n              <td class=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                <div class=\"flex items-center justify-end space-x-2\">\n                  <router-link :to=\"`/app/personnel/${user.id}`\"\n                               class=\"text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300\">\n                    <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"></path>\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"></path>\n                    </svg>\n                  </router-link>\n\n                  <button @click=\"editUser(user)\"\n                          class=\"text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300\">\n                    <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"></path>\n                    </svg>\n                  </button>\n\n                  <button @click=\"resetPassword(user)\"\n                          class=\"text-yellow-600 dark:text-yellow-400 hover:text-yellow-900 dark:hover:text-yellow-300\">\n                    <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z\"></path>\n                    </svg>\n                  </button>\n\n                  <button @click=\"toggleUserStatus(user)\"\n                          :class=\"user.is_active ? 'text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300' : 'text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300'\">\n                    <svg v-if=\"user.is_active\" class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728\"></path>\n                    </svg>\n                    <svg v-else class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n                    </svg>\n                  </button>\n                </div>\n              </td>\n            </tr>\n          </tbody>\n        </table>\n      </div>\n\n      <!-- Pagination -->\n      <div v-if=\"totalPages > 1\" class=\"bg-white dark:bg-gray-800 px-4 py-3 border-t border-gray-200 dark:border-gray-700 sm:px-6\">\n        <div class=\"flex items-center justify-between\">\n          <div class=\"flex-1 flex justify-between sm:hidden\">\n            <button @click=\"previousPage\"\n                    :disabled=\"currentPage === 1\"\n                    class=\"relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50\">\n              Precedente\n            </button>\n            <button @click=\"nextPage\"\n                    :disabled=\"currentPage === totalPages\"\n                    class=\"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50\">\n              Successiva\n            </button>\n          </div>\n          <div class=\"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between\">\n            <div>\n              <p class=\"text-sm text-gray-700 dark:text-gray-300\">\n                Mostrando <span class=\"font-medium\">{{ (currentPage - 1) * itemsPerPage + 1 }}</span> a\n                <span class=\"font-medium\">{{ Math.min(currentPage * itemsPerPage, filteredUsers.length) }}</span> di\n                <span class=\"font-medium\">{{ filteredUsers.length }}</span> risultati\n              </p>\n            </div>\n            <div>\n              <nav class=\"relative z-0 inline-flex rounded-md shadow-sm -space-x-px\">\n                <button @click=\"previousPage\"\n                        :disabled=\"currentPage === 1\"\n                        class=\"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50\">\n                  <svg class=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fill-rule=\"evenodd\" d=\"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z\" clip-rule=\"evenodd\"></path>\n                  </svg>\n                </button>\n\n                <button v-for=\"page in visiblePages\"\n                        :key=\"page\"\n                        @click=\"currentPage = page\"\n                        :class=\"[\n                          'relative inline-flex items-center px-4 py-2 border text-sm font-medium',\n                          page === currentPage\n                            ? 'z-10 bg-blue-50 dark:bg-blue-900 border-blue-500 text-blue-600 dark:text-blue-200'\n                            : 'bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700'\n                        ]\">\n                  {{ page }}\n                </button>\n\n                <button @click=\"nextPage\"\n                        :disabled=\"currentPage === totalPages\"\n                        class=\"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50\">\n                  <svg class=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fill-rule=\"evenodd\" d=\"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\" clip-rule=\"evenodd\"></path>\n                  </svg>\n                </button>\n              </nav>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Empty State -->\n    <div v-else class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-12 text-center\">\n      <svg class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\n      </svg>\n      <h3 class=\"mt-2 text-sm font-medium text-gray-900 dark:text-white\">Nessun dipendente trovato</h3>\n      <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\n        {{ hasActiveFilters ? 'Prova a modificare i filtri di ricerca.' : 'Inizia creando il primo dipendente.' }}\n      </p>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted, watch } from 'vue'\n\n// Emits\nconst emit = defineEmits(['user-created', 'user-updated', 'user-deleted'])\n\n// Reactive state\nconst users = ref([])\nconst departments = ref([])\nconst loading = ref(false)\nconst error = ref(null)\nconst searchQuery = ref('')\nconst selectedDepartment = ref('')\nconst selectedRole = ref('')\nconst currentPage = ref(1)\nconst itemsPerPage = ref(20)\n\n// Computed properties\nconst filteredUsers = computed(() => {\n  let filtered = users.value\n\n  // Apply search filter\n  if (searchQuery.value.trim()) {\n    const search = searchQuery.value.toLowerCase()\n    filtered = filtered.filter(user =>\n      user.full_name.toLowerCase().includes(search) ||\n      user.email.toLowerCase().includes(search) ||\n      (user.position && user.position.toLowerCase().includes(search)) ||\n      (user.department && user.department.name.toLowerCase().includes(search))\n    )\n  }\n\n  // Apply department filter\n  if (selectedDepartment.value) {\n    filtered = filtered.filter(user => user.department_id == selectedDepartment.value)\n  }\n\n  // Apply role filter\n  if (selectedRole.value) {\n    filtered = filtered.filter(user => user.role === selectedRole.value)\n  }\n\n  return filtered\n})\n\nconst paginatedUsers = computed(() => {\n  const start = (currentPage.value - 1) * itemsPerPage.value\n  const end = start + itemsPerPage.value\n  return filteredUsers.value.slice(start, end)\n})\n\nconst totalPages = computed(() => {\n  return Math.ceil(filteredUsers.value.length / itemsPerPage.value)\n})\n\nconst visiblePages = computed(() => {\n  const pages = []\n  const total = totalPages.value\n  const current = currentPage.value\n\n  if (total <= 7) {\n    for (let i = 1; i <= total; i++) {\n      pages.push(i)\n    }\n  } else {\n    if (current <= 4) {\n      for (let i = 1; i <= 5; i++) {\n        pages.push(i)\n      }\n      pages.push('...')\n      pages.push(total)\n    } else if (current >= total - 3) {\n      pages.push(1)\n      pages.push('...')\n      for (let i = total - 4; i <= total; i++) {\n        pages.push(i)\n      }\n    } else {\n      pages.push(1)\n      pages.push('...')\n      for (let i = current - 1; i <= current + 1; i++) {\n        pages.push(i)\n      }\n      pages.push('...')\n      pages.push(total)\n    }\n  }\n\n  return pages.filter(page => page !== '...' || pages.indexOf(page) === pages.lastIndexOf(page))\n})\n\nconst hasActiveFilters = computed(() => {\n  return searchQuery.value.trim() !== '' || selectedDepartment.value !== '' || selectedRole.value !== ''\n})\n\n// Methods\nconst loadUsers = async () => {\n  loading.value = true\n  error.value = null\n\n  try {\n    const response = await fetch('/api/personnel/users', {\n      credentials: 'include'\n    })\n\n    if (!response.ok) {\n      throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n\n    if (data.success) {\n      users.value = data.data.users || []\n    } else {\n      throw new Error(data.message || 'Errore nel caricamento utenti')\n    }\n  } catch (err) {\n    console.error('Error loading users:', err)\n    error.value = err.message\n  } finally {\n    loading.value = false\n  }\n}\n\nconst loadDepartments = async () => {\n  try {\n    const response = await fetch('/api/personnel/departments', {\n      credentials: 'include'\n    })\n\n    if (!response.ok) {\n      throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n\n    if (data.success) {\n      departments.value = data.data.departments || []\n    }\n  } catch (err) {\n    console.error('Error loading departments:', err)\n  }\n}\n\nconst editUser = (user) => {\n  // TODO: Open edit modal\n  console.log('Edit user:', user)\n}\n\nconst resetPassword = async (user) => {\n  if (!confirm(`Sei sicuro di voler resettare la password di ${user.full_name}?`)) {\n    return\n  }\n\n  try {\n    const response = await fetch(`/api/personnel/admin/users/${user.id}/reset-password`, {\n      method: 'POST',\n      credentials: 'include'\n    })\n\n    if (!response.ok) {\n      throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n\n    if (data.success) {\n      alert(`Password resettata per ${user.full_name}. Nuova password temporanea: ${data.data.temporary_password}`)\n    } else {\n      throw new Error(data.message || 'Errore nel reset password')\n    }\n  } catch (err) {\n    console.error('Error resetting password:', err)\n    alert('Errore nel reset della password: ' + err.message)\n  }\n}\n\nconst toggleUserStatus = async (user) => {\n  const action = user.is_active ? 'disattivare' : 'riattivare'\n  if (!confirm(`Sei sicuro di voler ${action} ${user.full_name}?`)) {\n    return\n  }\n\n  try {\n    if (!user.is_active) {\n      // Reactivate user - update via PUT\n      const response = await fetch(`/api/personnel/users/${user.id}`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        credentials: 'include',\n        body: JSON.stringify({ is_active: true })\n      })\n\n      if (!response.ok) {\n        throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n      }\n\n      const data = await response.json()\n      if (data.success) {\n        user.is_active = true\n        emit('user-updated', user)\n      }\n    } else {\n      // Deactivate user - use DELETE\n      const response = await fetch(`/api/personnel/admin/users/${user.id}`, {\n        method: 'DELETE',\n        credentials: 'include'\n      })\n\n      if (!response.ok) {\n        throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n      }\n\n      const data = await response.json()\n      if (data.success) {\n        user.is_active = false\n        emit('user-deleted', user)\n      }\n    }\n  } catch (err) {\n    console.error('Error toggling user status:', err)\n    alert('Errore nell\\'operazione: ' + err.message)\n  }\n}\n\nconst previousPage = () => {\n  if (currentPage.value > 1) {\n    currentPage.value--\n  }\n}\n\nconst nextPage = () => {\n  if (currentPage.value < totalPages.value) {\n    currentPage.value++\n  }\n}\n\nconst getRoleLabel = (role) => {\n  const labels = {\n    'admin': 'Admin',\n    'manager': 'Manager',\n    'employee': 'Dipendente'\n  }\n  return labels[role] || role\n}\n\nconst getEmploymentTypeLabel = (type) => {\n  const labels = {\n    'full_time': 'Tempo pieno',\n    'part_time': 'Part-time',\n    'contractor': 'Consulente',\n    'intern': 'Stagista'\n  }\n  return labels[type] || type\n}\n\nconst formatDate = (dateString) => {\n  if (!dateString) return ''\n  const date = new Date(dateString)\n  return date.toLocaleDateString('it-IT', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric'\n  })\n}\n\nconst isContractExpiringSoon = (contractEndDate) => {\n  if (!contractEndDate) return false\n  const today = new Date()\n  const endDate = new Date(contractEndDate)\n  const diffTime = endDate - today\n  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))\n  return diffDays <= 90 && diffDays >= 0\n}\n\n// Watchers\nwatch([searchQuery, selectedDepartment, selectedRole], () => {\n  currentPage.value = 1\n})\n\n// Lifecycle\nonMounted(async () => {\n  await Promise.all([\n    loadUsers(),\n    loadDepartments()\n  ])\n})\n</script>\n\n<style scoped>\n/* Custom styles for users management */\n.table-container {\n  max-height: 600px;\n  overflow-y: auto;\n}\n\n/* Responsive adjustments */\n@media (max-width: 768px) {\n  .table-container {\n    max-height: none;\n  }\n}\n</style>\n"}
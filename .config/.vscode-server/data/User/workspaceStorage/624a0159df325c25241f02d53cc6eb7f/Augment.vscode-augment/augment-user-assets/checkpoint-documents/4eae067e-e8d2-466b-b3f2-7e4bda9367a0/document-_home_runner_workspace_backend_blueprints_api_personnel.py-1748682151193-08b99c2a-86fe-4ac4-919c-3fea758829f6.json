{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/personnel.py"}, "originalCode": "\"\"\"\nAPI endpoints for personnel management.\nProvides REST API for users, departments, skills, and organization chart.\n\"\"\"\n\nfrom flask import Blueprint, request, jsonify\nfrom flask_login import current_user\nfrom sqlalchemy import or_, and_, func\nfrom sqlalchemy.orm import joinedload\n\nfrom extensions import db\nfrom models import User, Department, Skill, UserSkill, UserProfile\nfrom utils.api_utils import (\n    api_response, get_pagination_params, api_permission_required,\n    handle_api_error, api_login_required\n)\nfrom utils.permissions import (\n    PERMISSION_VIEW_PERSONNEL_DATA, PERMISSION_EDIT_PERSONNEL_DATA, PERMISSION_MANAGE_USERS\n)\n\n# Create blueprint\napi_personnel = Blueprint('api_personnel', __name__, url_prefix='/personnel')\n\n@api_personnel.route('/users', methods=['GET'])\n@api_login_required\n@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)\ndef get_users():\n    \"\"\"\n    Get list of users with filtering, pagination, and search.\n\n    Query Parameters:\n    - page: Page number (default: 1)\n    - per_page: Items per page (default: 20)\n    - search: Search in name, username, email\n    - department_id: Filter by department\n    - role: Filter by role\n    - is_active: Filter by active status\n    - skills: Filter by skills (comma-separated skill IDs)\n    \"\"\"\n    try:\n        # Get pagination parameters\n        page, per_page = get_pagination_params()\n\n        # Build base query\n        query = User.query.options(\n            joinedload(User.department_obj),\n            joinedload(User.detailed_skills).joinedload(UserSkill.skill),\n            joinedload(User.profile)\n        )\n\n        # Apply filters\n        search = request.args.get('search', '').strip()\n        if search:\n            search_filter = or_(\n                User.first_name.ilike(f'%{search}%'),\n                User.last_name.ilike(f'%{search}%'),\n                User.username.ilike(f'%{search}%'),\n                User.email.ilike(f'%{search}%')\n            )\n            query = query.filter(search_filter)\n\n        # Department filter\n        department_id = request.args.get('department_id', type=int)\n        if department_id:\n            query = query.filter(User.department_id == department_id)\n\n        # Role filter\n        role = request.args.get('role')\n        if role:\n            query = query.filter(User.role == role)\n\n        # Active status filter\n        is_active = request.args.get('is_active')\n        if is_active is not None:\n            is_active_bool = is_active.lower() in ['true', '1', 'yes']\n            query = query.filter(User.is_active == is_active_bool)\n\n        # Skills filter\n        skills = request.args.get('skills')\n        if skills:\n            skill_ids = [int(id.strip()) for id in skills.split(',') if id.strip().isdigit()]\n            if skill_ids:\n                query = query.join(UserSkill).filter(UserSkill.skill_id.in_(skill_ids))\n\n        # Order by\n        order_by = request.args.get('order_by', 'last_name')\n        order_dir = request.args.get('order_dir', 'asc')\n\n        if hasattr(User, order_by):\n            order_column = getattr(User, order_by)\n            if order_dir.lower() == 'desc':\n                order_column = order_column.desc()\n            query = query.order_by(order_column)\n        else:\n            query = query.order_by(User.last_name.asc())\n\n        # Execute pagination\n        pagination = query.paginate(\n            page=page, per_page=per_page, error_out=False\n        )\n\n        # Serialize users\n        users_data = []\n        for user in pagination.items:\n            user_data = {\n                'id': user.id,\n                'username': user.username,\n                'email': user.email,\n                'first_name': user.first_name,\n                'last_name': user.last_name,\n                'full_name': user.full_name,\n                'role': user.role,\n                'department_id': user.department_id,\n                'department_name': user.department_obj.name if user.department_obj else None,\n                'position': user.position,\n                'hire_date': user.hire_date.isoformat() if user.hire_date else None,\n                'phone': user.phone,\n                'profile_image': user.profile_image,\n                'is_active': user.is_active,\n                'last_login': user.last_login.isoformat() if user.last_login else None,\n                'skills': [\n                    {\n                        'id': us.skill.id,\n                        'name': us.skill.name,\n                        'category': us.skill.category,\n                        'proficiency_level': us.proficiency_level,\n                        'years_experience': us.years_experience\n                    }\n                    for us in user.detailed_skills\n                ] if user.detailed_skills else [],\n                'profile_completion': user.profile.profile_completion if user.profile else 0.0\n            }\n            users_data.append(user_data)\n\n        return api_response(\n            data={\n                'users': users_data,\n                'pagination': {\n                    'page': pagination.page,\n                    'pages': pagination.pages,\n                    'per_page': pagination.per_page,\n                    'total': pagination.total,\n                    'has_next': pagination.has_next,\n                    'has_prev': pagination.has_prev\n                }\n            },\n            message=f\"Retrieved {len(users_data)} users\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_personnel.route('/users/<int:user_id>', methods=['GET'])\n@api_login_required\n@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)\ndef get_user(user_id):\n    \"\"\"\n    Get detailed information about a specific user.\n    \"\"\"\n    try:\n        user = User.query.options(\n            joinedload(User.department_obj),\n            joinedload(User.detailed_skills).joinedload(UserSkill.skill),\n            joinedload(User.profile),\n            joinedload(User.projects)\n        ).get_or_404(user_id)\n\n        # Serialize user with full details\n        user_data = {\n            'id': user.id,\n            'username': user.username,\n            'email': user.email,\n            'first_name': user.first_name,\n            'last_name': user.last_name,\n            'full_name': user.full_name,\n            'role': user.role,\n            'department_id': user.department_id,\n            'department': {\n                'id': user.department_obj.id,\n                'name': user.department_obj.name,\n                'description': user.department_obj.description\n            } if user.department_obj else None,\n            'position': user.position,\n            'hire_date': user.hire_date.isoformat() if user.hire_date else None,\n            'phone': user.phone,\n            'profile_image': user.profile_image,\n            'bio': user.bio,\n            'is_active': user.is_active,\n            'dark_mode': user.dark_mode,\n            'created_at': user.created_at.isoformat() if user.created_at else None,\n            'last_login': user.last_login.isoformat() if user.last_login else None,\n            'skills': [\n                {\n                    'id': us.skill.id,\n                    'name': us.skill.name,\n                    'category': us.skill.category,\n                    'description': us.skill.description,\n                    'proficiency_level': us.proficiency_level,\n                    'years_experience': us.years_experience,\n                    'certified': us.is_certified,\n                    'last_used': us.certification_date.isoformat() if us.certification_date else None\n                }\n                for us in user.detailed_skills\n            ] if user.detailed_skills else [],\n            'projects': [\n                {\n                    'id': project.id,\n                    'name': project.name,\n                    'status': project.status,\n                    'role': 'team_member'  # Could be enhanced with actual role from project_team table\n                }\n                for project in user.projects\n            ] if user.projects else [],\n            'profile': {\n                'employee_id': user.profile.employee_id,\n                'job_title': user.profile.job_title,\n                'birth_date': user.profile.birth_date.isoformat() if user.profile.birth_date else None,\n                'address': user.profile.address,\n                'emergency_contact_name': user.profile.emergency_contact_name,\n                'emergency_contact_phone': user.profile.emergency_contact_phone,\n                'emergency_contact_relationship': user.profile.emergency_contact_relationship,\n                'employment_type': user.profile.employment_type,\n                'work_location': user.profile.work_location,\n                'weekly_hours': user.profile.weekly_hours,\n                'daily_hours': user.profile.daily_hours,\n                'current_cv_path': user.profile.current_cv_path,\n                'cv_last_updated': user.profile.cv_last_updated.isoformat() if user.profile.cv_last_updated else None,\n                'profile_completion': user.profile.profile_completion,\n                'notes': user.profile.notes if current_user.role in ['admin', 'human_resources'] else None,\n                'created_at': user.profile.created_at.isoformat() if user.profile.created_at else None,\n                'updated_at': user.profile.updated_at.isoformat() if user.profile.updated_at else None\n            } if user.profile else None\n        }\n\n        return api_response(\n            data={'user': user_data},\n            message=f\"Retrieved user {user.full_name}\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_personnel.route('/users/<int:user_id>', methods=['PUT'])\n@api_login_required\n@api_permission_required(PERMISSION_EDIT_PERSONNEL_DATA)\ndef update_user_profile(user_id):\n    \"\"\"\n    Update user profile information.\n    Allows updating both User and UserProfile fields.\n    \"\"\"\n    try:\n        # Check if user can edit this profile\n        user = User.query.get_or_404(user_id)\n\n        # Permission check: own profile or admin/HR\n        if user.id != current_user.id and not current_user.role in ['admin', 'human_resources']:\n            return api_response(\n                success=False,\n                message=\"Non hai il permesso di modificare questo profilo\",\n                status_code=403\n            )\n\n        data = request.get_json()\n        if not data:\n            return api_response(\n                success=False,\n                message=\"Nessun dato fornito\",\n                status_code=400\n            )\n\n        # Update User fields\n        user_fields = ['first_name', 'last_name', 'phone', 'bio', 'position']\n        for field in user_fields:\n            if field in data:\n                setattr(user, field, data[field])\n\n        # Get or create UserProfile\n        profile = user.profile\n        if not profile:\n            profile = UserProfile(user_id=user.id)\n            db.session.add(profile)\n\n        # Update UserProfile fields\n        profile_fields = [\n            'employee_id', 'job_title', 'birth_date', 'address',\n            'emergency_contact_name', 'emergency_contact_phone', 'emergency_contact_relationship',\n            'employment_type', 'work_location', 'weekly_hours', 'daily_hours'\n        ]\n\n        for field in profile_fields:\n            if field in data:\n                if field == 'birth_date' and data[field]:\n                    # Handle date conversion\n                    from datetime import datetime\n                    profile.birth_date = datetime.strptime(data[field], '%Y-%m-%d').date()\n                else:\n                    setattr(profile, field, data[field])\n\n        # Recalculate profile completion\n        profile.calculate_completion()\n\n        # Save changes\n        db.session.commit()\n\n        # Return updated user data\n        user_data = {\n            'id': user.id,\n            'username': user.username,\n            'email': user.email,\n            'first_name': user.first_name,\n            'last_name': user.last_name,\n            'full_name': user.full_name,\n            'role': user.role,\n            'department_id': user.department_id,\n            'department': {\n                'id': user.department_obj.id,\n                'name': user.department_obj.name,\n                'description': user.department_obj.description\n            } if user.department_obj else None,\n            'position': user.position,\n            'hire_date': user.hire_date.isoformat() if user.hire_date else None,\n            'phone': user.phone,\n            'profile_image': user.profile_image,\n            'bio': user.bio,\n            'is_active': user.is_active,\n            'profile': {\n                'employee_id': profile.employee_id,\n                'job_title': profile.job_title,\n                'birth_date': profile.birth_date.isoformat() if profile.birth_date else None,\n                'address': profile.address,\n                'emergency_contact_name': profile.emergency_contact_name,\n                'emergency_contact_phone': profile.emergency_contact_phone,\n                'emergency_contact_relationship': profile.emergency_contact_relationship,\n                'employment_type': profile.employment_type,\n                'work_location': profile.work_location,\n                'weekly_hours': profile.weekly_hours,\n                'daily_hours': profile.daily_hours,\n                'profile_completion': profile.profile_completion\n            }\n        }\n\n        return api_response(\n            data={'user': user_data},\n            message=\"Profilo aggiornato con successo\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_personnel.route('/departments', methods=['GET'])\n@api_login_required\ndef get_departments():\n    \"\"\"\n    Get list of departments with organization chart data.\n    \"\"\"\n    try:\n        departments = Department.query.options(\n            joinedload(Department.manager)\n        ).all()\n\n        departments_data = []\n        for dept in departments:\n            dept_data = {\n                'id': dept.id,\n                'name': dept.name,\n                'description': dept.description,\n                'manager_id': dept.manager_id,\n                'manager': {\n                    'id': dept.manager.id,\n                    'full_name': dept.manager.full_name,\n                    'email': dept.manager.email\n                } if dept.manager else None,\n                'user_count': dept.employees.count() if dept.employees else 0,\n                'users': [\n                    {\n                        'id': user.id,\n                        'full_name': user.full_name,\n                        'position': user.position,\n                        'email': user.email,\n                        'is_active': user.is_active\n                    }\n                    for user in dept.employees\n                ] if dept.employees else [],\n                'created_at': dept.created_at.isoformat() if dept.created_at else None\n            }\n            departments_data.append(dept_data)\n\n        return api_response(\n            data={'departments': departments_data},\n            message=f\"Retrieved {len(departments_data)} departments\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_personnel.route('/departments', methods=['POST'])\n@api_login_required\n@api_permission_required(PERMISSION_MANAGE_USERS)\ndef create_department():\n    \"\"\"\n    Create a new department.\n    \"\"\"\n    try:\n        data = request.get_json()\n\n        # Validate required fields\n        if not data.get('name'):\n            return api_response(\n                success=False,\n                message='Nome dipartimento richiesto',\n                status_code=400\n            )\n\n        # Check if department name already exists\n        existing_dept = Department.query.filter_by(name=data['name']).first()\n        if existing_dept:\n            return api_response(\n                success=False,\n                message='Un dipartimento con questo nome esiste già',\n                status_code=400\n            )\n\n        # Create new department\n        department = Department(\n            name=data['name'],\n            description=data.get('description', ''),\n            manager_id=data.get('manager_id'),\n            parent_id=data.get('parent_id'),\n            budget=data.get('budget', 0.0)\n        )\n\n        db.session.add(department)\n        db.session.commit()\n\n        # Return created department data\n        dept_data = {\n            'id': department.id,\n            'name': department.name,\n            'description': department.description,\n            'manager_id': department.manager_id,\n            'parent_id': department.parent_id,\n            'budget': department.budget,\n            'user_count': 0,\n            'created_at': department.created_at.isoformat()\n        }\n\n        return api_response(\n            data={'department': dept_data},\n            message=f\"Dipartimento '{department.name}' creato con successo\"\n        )\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n@api_personnel.route('/departments/<int:dept_id>', methods=['GET'])\n@api_login_required\n@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)\ndef get_department(dept_id):\n    \"\"\"\n    Get detailed information about a specific department.\n    \"\"\"\n    try:\n        department = Department.query.options(\n            joinedload(Department.manager)\n        ).get_or_404(dept_id)\n\n        dept_data = {\n            'id': department.id,\n            'name': department.name,\n            'description': department.description,\n            'manager_id': department.manager_id,\n            'parent_id': department.parent_id,\n            'budget': department.budget,\n            'manager': {\n                'id': department.manager.id,\n                'full_name': department.manager.full_name,\n                'email': department.manager.email,\n                'position': department.manager.position\n            } if department.manager else None,\n            'parent': {\n                'id': department.parent.id,\n                'name': department.parent.name\n            } if department.parent_id and hasattr(department, 'parent') and department.parent else None,\n            'employees': [\n                {\n                    'id': emp.id,\n                    'full_name': emp.full_name,\n                    'email': emp.email,\n                    'position': emp.position,\n                    'is_active': emp.is_active,\n                    'hire_date': emp.hire_date.isoformat() if emp.hire_date else None\n                }\n                for emp in department.employees if emp.is_active\n            ],\n            'subdepartments': [\n                {\n                    'id': sub.id,\n                    'name': sub.name,\n                    'employee_count': getattr(sub, 'employee_count', 0)\n                }\n                for sub in department.subdepartments if getattr(sub, 'is_active', True)\n            ],\n            'employee_count': len([emp for emp in department.employees if emp.is_active]),\n            'created_at': department.created_at.isoformat() if department.created_at else None,\n            'updated_at': department.updated_at.isoformat() if department.updated_at else None\n        }\n\n        return api_response(\n            data={'department': dept_data},\n            message=f\"Retrieved department {department.name}\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_personnel.route('/departments/<int:dept_id>', methods=['PUT'])\n@api_login_required\n@api_permission_required(PERMISSION_MANAGE_USERS)\ndef update_department(dept_id):\n    \"\"\"\n    Update an existing department.\n    \"\"\"\n    try:\n        department = Department.query.get_or_404(dept_id)\n        data = request.get_json()\n\n        # Validate name if provided\n        if 'name' in data and data['name']:\n            # Check if name already exists (excluding current department)\n            existing_dept = Department.query.filter(\n                Department.name == data['name'],\n                Department.id != dept_id\n            ).first()\n            if existing_dept:\n                return api_response(\n                    success=False,\n                    message='Un dipartimento con questo nome esiste già',\n                    status_code=400\n                )\n            department.name = data['name']\n\n        # Update other fields\n        if 'description' in data:\n            department.description = data['description']\n        if 'manager_id' in data:\n            department.manager_id = data['manager_id']\n        if 'parent_id' in data:\n            department.parent_id = data['parent_id']\n        if 'budget' in data:\n            department.budget = data['budget']\n\n        db.session.commit()\n\n        # Return updated department data\n        dept_data = {\n            'id': department.id,\n            'name': department.name,\n            'description': department.description,\n            'manager_id': department.manager_id,\n            'parent_id': department.parent_id,\n            'budget': department.budget,\n            'employee_count': len([emp for emp in department.employees if emp.is_active]),\n            'updated_at': department.updated_at.isoformat() if department.updated_at else None\n        }\n\n        return api_response(\n            data={'department': dept_data},\n            message=f\"Dipartimento '{department.name}' aggiornato con successo\"\n        )\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n@api_personnel.route('/departments/<int:dept_id>', methods=['DELETE'])\n@api_login_required\n@api_permission_required(PERMISSION_MANAGE_USERS)\ndef delete_department(dept_id):\n    \"\"\"\n    Delete a department (soft delete by setting is_active=False).\n    \"\"\"\n    try:\n        department = Department.query.get_or_404(dept_id)\n\n        # Check if department has employees\n        active_employees = len([emp for emp in department.employees if emp.is_active])\n        if active_employees > 0:\n            return api_response(\n                success=False,\n                message='Impossibile eliminare un dipartimento con dipendenti assegnati',\n                status_code=400\n            )\n\n        # Check if department has subdepartments\n        active_subdepartments = len([sub for sub in department.subdepartments if getattr(sub, 'is_active', True)])\n        if active_subdepartments > 0:\n            return api_response(\n                success=False,\n                message='Impossibile eliminare un dipartimento con sotto-dipartimenti',\n                status_code=400\n            )\n\n        # Soft delete\n        department.is_active = False\n        db.session.commit()\n\n        return api_response(\n            message=f\"Dipartimento '{department.name}' eliminato con successo\"\n        )\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n@api_personnel.route('/skills', methods=['GET'])\n@api_login_required\n@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)\ndef get_skills():\n    \"\"\"\n    Get list of skills with usage statistics.\n    \"\"\"\n    try:\n        # Get skills with user count\n        skills_query = db.session.query(\n            Skill,\n            func.count(UserSkill.user_id).label('user_count')\n        ).outerjoin(UserSkill).group_by(Skill.id)\n\n        category = request.args.get('category')\n        if category:\n            skills_query = skills_query.filter(Skill.category == category)\n\n        search = request.args.get('search', '').strip()\n        if search:\n            skills_query = skills_query.filter(\n                or_(\n                    Skill.name.ilike(f'%{search}%'),\n                    Skill.description.ilike(f'%{search}%')\n                )\n            )\n\n        skills_data = []\n        for skill, user_count in skills_query.all():\n            skill_data = {\n                'id': skill.id,\n                'name': skill.name,\n                'category': skill.category,\n                'description': skill.description,\n                'user_count': user_count,\n                'users': [\n                    {\n                        'id': us.user.id,\n                        'full_name': us.user.full_name,\n                        'proficiency_level': us.proficiency_level,\n                        'years_experience': us.years_experience\n                    }\n                    for us in skill.user_skills\n                ] if hasattr(skill, 'user_skills') else []\n            }\n            skills_data.append(skill_data)\n\n        # Get categories for filter\n        categories = db.session.query(Skill.category).distinct().all()\n        categories_list = [cat[0] for cat in categories if cat[0]]\n\n        return api_response(\n            data={\n                'skills': skills_data,\n                'categories': categories_list\n            },\n            message=f\"Retrieved {len(skills_data)} skills\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n", "modifiedCode": "\"\"\"\nAPI endpoints for personnel management.\nProvides REST API for users, departments, skills, and organization chart.\n\"\"\"\n\nfrom flask import Blueprint, request, jsonify\nfrom flask_login import current_user\nfrom sqlalchemy import or_, and_, func\nfrom sqlalchemy.orm import joinedload\n\nfrom extensions import db\nfrom models import User, Department, Skill, UserSkill, UserProfile\nfrom utils.api_utils import (\n    api_response, get_pagination_params, api_permission_required,\n    handle_api_error, api_login_required\n)\nfrom utils.permissions import (\n    PERMISSION_VIEW_PERSONNEL_DATA, PERMISSION_EDIT_PERSONNEL_DATA, PERMISSION_MANAGE_USERS\n)\n\n# Create blueprint\napi_personnel = Blueprint('api_personnel', __name__, url_prefix='/personnel')\n\n@api_personnel.route('/users', methods=['GET'])\n@api_login_required\n@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)\ndef get_users():\n    \"\"\"\n    Get list of users with filtering, pagination, and search.\n\n    Query Parameters:\n    - page: Page number (default: 1)\n    - per_page: Items per page (default: 20)\n    - search: Search in name, username, email\n    - department_id: Filter by department\n    - role: Filter by role\n    - is_active: Filter by active status\n    - skills: Filter by skills (comma-separated skill IDs)\n    \"\"\"\n    try:\n        # Get pagination parameters\n        page, per_page = get_pagination_params()\n\n        # Build base query\n        query = User.query.options(\n            joinedload(User.department_obj),\n            joinedload(User.detailed_skills).joinedload(UserSkill.skill),\n            joinedload(User.profile)\n        )\n\n        # Apply filters\n        search = request.args.get('search', '').strip()\n        if search:\n            search_filter = or_(\n                User.first_name.ilike(f'%{search}%'),\n                User.last_name.ilike(f'%{search}%'),\n                User.username.ilike(f'%{search}%'),\n                User.email.ilike(f'%{search}%')\n            )\n            query = query.filter(search_filter)\n\n        # Department filter\n        department_id = request.args.get('department_id', type=int)\n        if department_id:\n            query = query.filter(User.department_id == department_id)\n\n        # Role filter\n        role = request.args.get('role')\n        if role:\n            query = query.filter(User.role == role)\n\n        # Active status filter\n        is_active = request.args.get('is_active')\n        if is_active is not None:\n            is_active_bool = is_active.lower() in ['true', '1', 'yes']\n            query = query.filter(User.is_active == is_active_bool)\n\n        # Skills filter\n        skills = request.args.get('skills')\n        if skills:\n            skill_ids = [int(id.strip()) for id in skills.split(',') if id.strip().isdigit()]\n            if skill_ids:\n                query = query.join(UserSkill).filter(UserSkill.skill_id.in_(skill_ids))\n\n        # Order by\n        order_by = request.args.get('order_by', 'last_name')\n        order_dir = request.args.get('order_dir', 'asc')\n\n        if hasattr(User, order_by):\n            order_column = getattr(User, order_by)\n            if order_dir.lower() == 'desc':\n                order_column = order_column.desc()\n            query = query.order_by(order_column)\n        else:\n            query = query.order_by(User.last_name.asc())\n\n        # Execute pagination\n        pagination = query.paginate(\n            page=page, per_page=per_page, error_out=False\n        )\n\n        # Serialize users\n        users_data = []\n        for user in pagination.items:\n            user_data = {\n                'id': user.id,\n                'username': user.username,\n                'email': user.email,\n                'first_name': user.first_name,\n                'last_name': user.last_name,\n                'full_name': user.full_name,\n                'role': user.role,\n                'department_id': user.department_id,\n                'department_name': user.department_obj.name if user.department_obj else None,\n                'position': user.position,\n                'hire_date': user.hire_date.isoformat() if user.hire_date else None,\n                'phone': user.phone,\n                'profile_image': user.profile_image,\n                'is_active': user.is_active,\n                'last_login': user.last_login.isoformat() if user.last_login else None,\n                'skills': [\n                    {\n                        'id': us.skill.id,\n                        'name': us.skill.name,\n                        'category': us.skill.category,\n                        'proficiency_level': us.proficiency_level,\n                        'years_experience': us.years_experience\n                    }\n                    for us in user.detailed_skills\n                ] if user.detailed_skills else [],\n                'profile_completion': user.profile.profile_completion if user.profile else 0.0\n            }\n            users_data.append(user_data)\n\n        return api_response(\n            data={\n                'users': users_data,\n                'pagination': {\n                    'page': pagination.page,\n                    'pages': pagination.pages,\n                    'per_page': pagination.per_page,\n                    'total': pagination.total,\n                    'has_next': pagination.has_next,\n                    'has_prev': pagination.has_prev\n                }\n            },\n            message=f\"Retrieved {len(users_data)} users\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_personnel.route('/users/<int:user_id>', methods=['GET'])\n@api_login_required\n@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)\ndef get_user(user_id):\n    \"\"\"\n    Get detailed information about a specific user.\n    \"\"\"\n    try:\n        user = User.query.options(\n            joinedload(User.department_obj),\n            joinedload(User.detailed_skills).joinedload(UserSkill.skill),\n            joinedload(User.profile),\n            joinedload(User.projects)\n        ).get_or_404(user_id)\n\n        # Ricalcola il completamento del profilo se esiste\n        if user.profile:\n            user.profile.calculate_completion()\n            db.session.commit()\n\n        # Serialize user with full details\n        user_data = {\n            'id': user.id,\n            'username': user.username,\n            'email': user.email,\n            'first_name': user.first_name,\n            'last_name': user.last_name,\n            'full_name': user.full_name,\n            'role': user.role,\n            'department_id': user.department_id,\n            'department': {\n                'id': user.department_obj.id,\n                'name': user.department_obj.name,\n                'description': user.department_obj.description\n            } if user.department_obj else None,\n            'position': user.position,\n            'hire_date': user.hire_date.isoformat() if user.hire_date else None,\n            'phone': user.phone,\n            'profile_image': user.profile_image,\n            'bio': user.bio,\n            'is_active': user.is_active,\n            'dark_mode': user.dark_mode,\n            'created_at': user.created_at.isoformat() if user.created_at else None,\n            'last_login': user.last_login.isoformat() if user.last_login else None,\n            'skills': [\n                {\n                    'id': us.skill.id,\n                    'name': us.skill.name,\n                    'category': us.skill.category,\n                    'description': us.skill.description,\n                    'proficiency_level': us.proficiency_level,\n                    'years_experience': us.years_experience,\n                    'certified': us.is_certified,\n                    'last_used': us.certification_date.isoformat() if us.certification_date else None\n                }\n                for us in user.detailed_skills\n            ] if user.detailed_skills else [],\n            'projects': [\n                {\n                    'id': project.id,\n                    'name': project.name,\n                    'status': project.status,\n                    'role': 'team_member'  # Could be enhanced with actual role from project_team table\n                }\n                for project in user.projects\n            ] if user.projects else [],\n            'profile': {\n                'employee_id': user.profile.employee_id,\n                'job_title': user.profile.job_title,\n                'birth_date': user.profile.birth_date.isoformat() if user.profile.birth_date else None,\n                'address': user.profile.address,\n                'emergency_contact_name': user.profile.emergency_contact_name,\n                'emergency_contact_phone': user.profile.emergency_contact_phone,\n                'emergency_contact_relationship': user.profile.emergency_contact_relationship,\n                'employment_type': user.profile.employment_type,\n                'work_location': user.profile.work_location,\n                'weekly_hours': user.profile.weekly_hours,\n                'daily_hours': user.profile.daily_hours,\n                'current_cv_path': user.profile.current_cv_path,\n                'cv_last_updated': user.profile.cv_last_updated.isoformat() if user.profile.cv_last_updated else None,\n                'profile_completion': user.profile.profile_completion,\n                'notes': user.profile.notes if current_user.role in ['admin', 'human_resources'] else None,\n                'created_at': user.profile.created_at.isoformat() if user.profile.created_at else None,\n                'updated_at': user.profile.updated_at.isoformat() if user.profile.updated_at else None\n            } if user.profile else None\n        }\n\n        return api_response(\n            data={'user': user_data},\n            message=f\"Retrieved user {user.full_name}\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_personnel.route('/users/<int:user_id>', methods=['PUT'])\n@api_login_required\n@api_permission_required(PERMISSION_EDIT_PERSONNEL_DATA)\ndef update_user_profile(user_id):\n    \"\"\"\n    Update user profile information.\n    Allows updating both User and UserProfile fields.\n    \"\"\"\n    try:\n        # Check if user can edit this profile\n        user = User.query.get_or_404(user_id)\n\n        # Permission check: own profile or admin/HR\n        if user.id != current_user.id and not current_user.role in ['admin', 'human_resources']:\n            return api_response(\n                success=False,\n                message=\"Non hai il permesso di modificare questo profilo\",\n                status_code=403\n            )\n\n        data = request.get_json()\n        if not data:\n            return api_response(\n                success=False,\n                message=\"Nessun dato fornito\",\n                status_code=400\n            )\n\n        # Update User fields\n        user_fields = ['first_name', 'last_name', 'phone', 'bio', 'position']\n        for field in user_fields:\n            if field in data:\n                setattr(user, field, data[field])\n\n        # Get or create UserProfile\n        profile = user.profile\n        if not profile:\n            profile = UserProfile(user_id=user.id)\n            db.session.add(profile)\n\n        # Update UserProfile fields\n        profile_fields = [\n            'employee_id', 'job_title', 'birth_date', 'address',\n            'emergency_contact_name', 'emergency_contact_phone', 'emergency_contact_relationship',\n            'employment_type', 'work_location', 'weekly_hours', 'daily_hours'\n        ]\n\n        for field in profile_fields:\n            if field in data:\n                if field == 'birth_date' and data[field]:\n                    # Handle date conversion\n                    from datetime import datetime\n                    profile.birth_date = datetime.strptime(data[field], '%Y-%m-%d').date()\n                else:\n                    setattr(profile, field, data[field])\n\n        # Recalculate profile completion\n        profile.calculate_completion()\n\n        # Save changes\n        db.session.commit()\n\n        # Return updated user data\n        user_data = {\n            'id': user.id,\n            'username': user.username,\n            'email': user.email,\n            'first_name': user.first_name,\n            'last_name': user.last_name,\n            'full_name': user.full_name,\n            'role': user.role,\n            'department_id': user.department_id,\n            'department': {\n                'id': user.department_obj.id,\n                'name': user.department_obj.name,\n                'description': user.department_obj.description\n            } if user.department_obj else None,\n            'position': user.position,\n            'hire_date': user.hire_date.isoformat() if user.hire_date else None,\n            'phone': user.phone,\n            'profile_image': user.profile_image,\n            'bio': user.bio,\n            'is_active': user.is_active,\n            'profile': {\n                'employee_id': profile.employee_id,\n                'job_title': profile.job_title,\n                'birth_date': profile.birth_date.isoformat() if profile.birth_date else None,\n                'address': profile.address,\n                'emergency_contact_name': profile.emergency_contact_name,\n                'emergency_contact_phone': profile.emergency_contact_phone,\n                'emergency_contact_relationship': profile.emergency_contact_relationship,\n                'employment_type': profile.employment_type,\n                'work_location': profile.work_location,\n                'weekly_hours': profile.weekly_hours,\n                'daily_hours': profile.daily_hours,\n                'profile_completion': profile.profile_completion\n            }\n        }\n\n        return api_response(\n            data={'user': user_data},\n            message=\"Profilo aggiornato con successo\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_personnel.route('/departments', methods=['GET'])\n@api_login_required\ndef get_departments():\n    \"\"\"\n    Get list of departments with organization chart data.\n    \"\"\"\n    try:\n        departments = Department.query.options(\n            joinedload(Department.manager)\n        ).all()\n\n        departments_data = []\n        for dept in departments:\n            dept_data = {\n                'id': dept.id,\n                'name': dept.name,\n                'description': dept.description,\n                'manager_id': dept.manager_id,\n                'manager': {\n                    'id': dept.manager.id,\n                    'full_name': dept.manager.full_name,\n                    'email': dept.manager.email\n                } if dept.manager else None,\n                'user_count': dept.employees.count() if dept.employees else 0,\n                'users': [\n                    {\n                        'id': user.id,\n                        'full_name': user.full_name,\n                        'position': user.position,\n                        'email': user.email,\n                        'is_active': user.is_active\n                    }\n                    for user in dept.employees\n                ] if dept.employees else [],\n                'created_at': dept.created_at.isoformat() if dept.created_at else None\n            }\n            departments_data.append(dept_data)\n\n        return api_response(\n            data={'departments': departments_data},\n            message=f\"Retrieved {len(departments_data)} departments\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_personnel.route('/departments', methods=['POST'])\n@api_login_required\n@api_permission_required(PERMISSION_MANAGE_USERS)\ndef create_department():\n    \"\"\"\n    Create a new department.\n    \"\"\"\n    try:\n        data = request.get_json()\n\n        # Validate required fields\n        if not data.get('name'):\n            return api_response(\n                success=False,\n                message='Nome dipartimento richiesto',\n                status_code=400\n            )\n\n        # Check if department name already exists\n        existing_dept = Department.query.filter_by(name=data['name']).first()\n        if existing_dept:\n            return api_response(\n                success=False,\n                message='Un dipartimento con questo nome esiste già',\n                status_code=400\n            )\n\n        # Create new department\n        department = Department(\n            name=data['name'],\n            description=data.get('description', ''),\n            manager_id=data.get('manager_id'),\n            parent_id=data.get('parent_id'),\n            budget=data.get('budget', 0.0)\n        )\n\n        db.session.add(department)\n        db.session.commit()\n\n        # Return created department data\n        dept_data = {\n            'id': department.id,\n            'name': department.name,\n            'description': department.description,\n            'manager_id': department.manager_id,\n            'parent_id': department.parent_id,\n            'budget': department.budget,\n            'user_count': 0,\n            'created_at': department.created_at.isoformat()\n        }\n\n        return api_response(\n            data={'department': dept_data},\n            message=f\"Dipartimento '{department.name}' creato con successo\"\n        )\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n@api_personnel.route('/departments/<int:dept_id>', methods=['GET'])\n@api_login_required\n@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)\ndef get_department(dept_id):\n    \"\"\"\n    Get detailed information about a specific department.\n    \"\"\"\n    try:\n        department = Department.query.options(\n            joinedload(Department.manager)\n        ).get_or_404(dept_id)\n\n        dept_data = {\n            'id': department.id,\n            'name': department.name,\n            'description': department.description,\n            'manager_id': department.manager_id,\n            'parent_id': department.parent_id,\n            'budget': department.budget,\n            'manager': {\n                'id': department.manager.id,\n                'full_name': department.manager.full_name,\n                'email': department.manager.email,\n                'position': department.manager.position\n            } if department.manager else None,\n            'parent': {\n                'id': department.parent.id,\n                'name': department.parent.name\n            } if department.parent_id and hasattr(department, 'parent') and department.parent else None,\n            'employees': [\n                {\n                    'id': emp.id,\n                    'full_name': emp.full_name,\n                    'email': emp.email,\n                    'position': emp.position,\n                    'is_active': emp.is_active,\n                    'hire_date': emp.hire_date.isoformat() if emp.hire_date else None\n                }\n                for emp in department.employees if emp.is_active\n            ],\n            'subdepartments': [\n                {\n                    'id': sub.id,\n                    'name': sub.name,\n                    'employee_count': getattr(sub, 'employee_count', 0)\n                }\n                for sub in department.subdepartments if getattr(sub, 'is_active', True)\n            ],\n            'employee_count': len([emp for emp in department.employees if emp.is_active]),\n            'created_at': department.created_at.isoformat() if department.created_at else None,\n            'updated_at': department.updated_at.isoformat() if department.updated_at else None\n        }\n\n        return api_response(\n            data={'department': dept_data},\n            message=f\"Retrieved department {department.name}\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_personnel.route('/departments/<int:dept_id>', methods=['PUT'])\n@api_login_required\n@api_permission_required(PERMISSION_MANAGE_USERS)\ndef update_department(dept_id):\n    \"\"\"\n    Update an existing department.\n    \"\"\"\n    try:\n        department = Department.query.get_or_404(dept_id)\n        data = request.get_json()\n\n        # Validate name if provided\n        if 'name' in data and data['name']:\n            # Check if name already exists (excluding current department)\n            existing_dept = Department.query.filter(\n                Department.name == data['name'],\n                Department.id != dept_id\n            ).first()\n            if existing_dept:\n                return api_response(\n                    success=False,\n                    message='Un dipartimento con questo nome esiste già',\n                    status_code=400\n                )\n            department.name = data['name']\n\n        # Update other fields\n        if 'description' in data:\n            department.description = data['description']\n        if 'manager_id' in data:\n            department.manager_id = data['manager_id']\n        if 'parent_id' in data:\n            department.parent_id = data['parent_id']\n        if 'budget' in data:\n            department.budget = data['budget']\n\n        db.session.commit()\n\n        # Return updated department data\n        dept_data = {\n            'id': department.id,\n            'name': department.name,\n            'description': department.description,\n            'manager_id': department.manager_id,\n            'parent_id': department.parent_id,\n            'budget': department.budget,\n            'employee_count': len([emp for emp in department.employees if emp.is_active]),\n            'updated_at': department.updated_at.isoformat() if department.updated_at else None\n        }\n\n        return api_response(\n            data={'department': dept_data},\n            message=f\"Dipartimento '{department.name}' aggiornato con successo\"\n        )\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n@api_personnel.route('/departments/<int:dept_id>', methods=['DELETE'])\n@api_login_required\n@api_permission_required(PERMISSION_MANAGE_USERS)\ndef delete_department(dept_id):\n    \"\"\"\n    Delete a department (soft delete by setting is_active=False).\n    \"\"\"\n    try:\n        department = Department.query.get_or_404(dept_id)\n\n        # Check if department has employees\n        active_employees = len([emp for emp in department.employees if emp.is_active])\n        if active_employees > 0:\n            return api_response(\n                success=False,\n                message='Impossibile eliminare un dipartimento con dipendenti assegnati',\n                status_code=400\n            )\n\n        # Check if department has subdepartments\n        active_subdepartments = len([sub for sub in department.subdepartments if getattr(sub, 'is_active', True)])\n        if active_subdepartments > 0:\n            return api_response(\n                success=False,\n                message='Impossibile eliminare un dipartimento con sotto-dipartimenti',\n                status_code=400\n            )\n\n        # Soft delete\n        department.is_active = False\n        db.session.commit()\n\n        return api_response(\n            message=f\"Dipartimento '{department.name}' eliminato con successo\"\n        )\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n@api_personnel.route('/skills', methods=['GET'])\n@api_login_required\n@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)\ndef get_skills():\n    \"\"\"\n    Get list of skills with usage statistics.\n    \"\"\"\n    try:\n        # Get skills with user count\n        skills_query = db.session.query(\n            Skill,\n            func.count(UserSkill.user_id).label('user_count')\n        ).outerjoin(UserSkill).group_by(Skill.id)\n\n        category = request.args.get('category')\n        if category:\n            skills_query = skills_query.filter(Skill.category == category)\n\n        search = request.args.get('search', '').strip()\n        if search:\n            skills_query = skills_query.filter(\n                or_(\n                    Skill.name.ilike(f'%{search}%'),\n                    Skill.description.ilike(f'%{search}%')\n                )\n            )\n\n        skills_data = []\n        for skill, user_count in skills_query.all():\n            skill_data = {\n                'id': skill.id,\n                'name': skill.name,\n                'category': skill.category,\n                'description': skill.description,\n                'user_count': user_count,\n                'users': [\n                    {\n                        'id': us.user.id,\n                        'full_name': us.user.full_name,\n                        'proficiency_level': us.proficiency_level,\n                        'years_experience': us.years_experience\n                    }\n                    for us in skill.user_skills\n                ] if hasattr(skill, 'user_skills') else []\n            }\n            skills_data.append(skill_data)\n\n        # Get categories for filter\n        categories = db.session.query(Skill.category).distinct().all()\n        categories_list = [cat[0] for cat in categories if cat[0]]\n\n        return api_response(\n            data={\n                'skills': skills_data,\n                'categories': categories_list\n            },\n            message=f\"Retrieved {len(skills_data)} skills\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n"}
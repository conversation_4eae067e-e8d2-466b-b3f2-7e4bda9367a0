"""
Test per verificare che le fixture funzionino correttamente.
"""

import pytest
from models import User, Department, Skill


class TestFixtures:
    """Test per verificare il funzionamento delle fixture."""

    def test_app_fixture(self, app):
        """Test che la fixture app funzioni."""
        assert app is not None
        assert app.config['TESTING'] is True

    def test_client_fixture(self, client):
        """Test che la fixture client funzioni."""
        assert client is not None
        response = client.get('/')
        # Non importa il codice di risposta, basta che non ci siano errori

    def test_auth_fixture(self, auth):
        """Test che la fixture auth funzioni."""
        assert auth is not None
        assert hasattr(auth, 'login')
        assert hasattr(auth, 'logout')

    def test_auth_login(self, auth):
        """Test che il login tramite fixture auth funzioni."""
        response = auth.login()
        # Il login dovrebbe funzionare (200 o redirect)
        assert response.status_code in [200, 302]

    def test_created_user_fixture(self, created_user, app):
        """Test che la fixture created_user funzioni."""
        assert created_user is not None
        assert isinstance(created_user, int)
        
        with app.app_context():
            user = User.query.get(created_user)
            assert user is not None
            assert user.username == 'testuser'

    def test_sample_departments_fixture(self, sample_departments, app):
        """Test che la fixture sample_departments funzioni."""
        assert sample_departments is not None
        assert len(sample_departments) > 0
        
        with app.app_context():
            dept_count = Department.query.count()
            assert dept_count >= len(sample_departments)

    def test_sample_skills_fixture(self, sample_skills, app):
        """Test che la fixture sample_skills funzioni."""
        assert sample_skills is not None
        assert len(sample_skills) > 0
        
        with app.app_context():
            skill_count = Skill.query.count()
            assert skill_count >= len(sample_skills)

    def test_sample_users_fixture(self, sample_users, app):
        """Test che la fixture sample_users funzioni."""
        assert sample_users is not None
        assert len(sample_users) > 0
        
        with app.app_context():
            # Verifica che ci sia almeno un admin
            admin = User.query.filter_by(role='admin').first()
            assert admin is not None
            assert admin.username == 'admin'

    def test_database_isolation(self, app, init_database):
        """Test che i test siano isolati a livello di database."""
        with app.app_context():
            # Crea un utente di test
            user = User(
                username='isolation_test',
                email='<EMAIL>',
                first_name='Isolation',
                last_name='Test'
            )
            user.set_password('testpass')
            from extensions import db
            db.session.add(user)
            db.session.commit()
            
            # Verifica che esista
            found_user = User.query.filter_by(username='isolation_test').first()
            assert found_user is not None

    def test_database_isolation_cleanup(self, app):
        """Test che il cleanup del database funzioni tra i test."""
        with app.app_context():
            # L'utente del test precedente non dovrebbe esistere più
            # (a meno che non sia stato creato da una fixture)
            user = User.query.filter_by(username='isolation_test').first()
            # Questo potrebbe essere None se il cleanup funziona correttamente


class TestAuthenticationFlow:
    """Test per verificare il flusso di autenticazione."""

    def test_login_logout_flow(self, auth):
        """Test del flusso completo login/logout."""
        # Login
        login_response = auth.login()
        assert login_response.status_code in [200, 302]
        
        # Logout
        logout_response = auth.logout()
        assert logout_response.status_code in [200, 302]

    def test_login_with_custom_credentials(self, auth, sample_users):
        """Test login con credenziali personalizzate."""
        # Prova login con utente manager
        response = auth.login(username='manager', password='password')
        assert response.status_code in [200, 302]

    def test_login_failure(self, auth):
        """Test login con credenziali errate."""
        response = auth.login(username='nonexistent', password='wrongpass')
        # Dovrebbe fallire ma non crashare
        assert response.status_code in [200, 302, 401, 403]


class TestDatabaseFixtures:
    """Test per verificare le fixture del database."""

    def test_test_client_fixture(self, test_client, app):
        """Test che la fixture test_client funzioni."""
        assert test_client is not None
        assert isinstance(test_client, int)
        
        with app.app_context():
            from models import Client
            client = Client.query.get(test_client)
            assert client is not None
            assert client.name == 'Test Client'

    def test_test_project_fixture(self, test_project, app):
        """Test che la fixture test_project funzioni."""
        assert test_project is not None
        assert isinstance(test_project, int)
        
        with app.app_context():
            from models import Project
            project = Project.query.get(test_project)
            assert project is not None
            assert project.name == 'Test Project'

    def test_test_kpi_fixture(self, test_kpi, app):
        """Test che la fixture test_kpi funzioni."""
        assert test_kpi is not None
        assert isinstance(test_kpi, int)
        
        with app.app_context():
            from models import KPI
            kpi = KPI.query.get(test_kpi)
            assert kpi is not None
            assert kpi.name == 'Test KPI'


class TestComplexFixtures:
    """Test per fixture più complesse con dipendenze."""

    def test_sample_user_skills_fixture(self, sample_user_skills, app):
        """Test che la fixture sample_user_skills funzioni."""
        if sample_user_skills:  # Potrebbe essere vuota se non ci sono abbastanza utenti/skill
            assert len(sample_user_skills) > 0
            
            with app.app_context():
                from models import UserSkill
                user_skill_count = UserSkill.query.count()
                assert user_skill_count >= len(sample_user_skills)

    def test_sample_user_profiles_fixture(self, sample_user_profiles, app):
        """Test che la fixture sample_user_profiles funzioni."""
        if sample_user_profiles:  # Potrebbe essere vuota se non ci sono abbastanza utenti
            assert len(sample_user_profiles) > 0
            
            with app.app_context():
                from models import UserProfile
                profile_count = UserProfile.query.count()
                assert profile_count >= len(sample_user_profiles)

    def test_all_fixtures_together(self, app, auth, sample_users, sample_departments, 
                                 sample_skills, test_client, test_project):
        """Test che tutte le fixture funzionino insieme."""
        # Verifica che tutte le fixture siano state create
        assert app is not None
        assert auth is not None
        assert sample_users is not None
        assert sample_departments is not None
        assert sample_skills is not None
        assert test_client is not None
        assert test_project is not None
        
        # Verifica che il login funzioni
        response = auth.login()
        assert response.status_code in [200, 302]
        
        with app.app_context():
            # Verifica che i dati siano nel database
            user_count = User.query.count()
            dept_count = Department.query.count()
            skill_count = Skill.query.count()
            
            assert user_count > 0
            assert dept_count > 0
            assert skill_count > 0

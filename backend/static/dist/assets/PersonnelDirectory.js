import{r as i,f as L,A as F,c as s,j as e,I as U,a as h,i as p,b as I,g as x,v as z,x as O,H as T,F as f,k as y,n as $,m as g,t as d,o as l}from"./vendor.js";import{u as Q}from"./personnel.js";const q={class:"personnel-directory"},G={class:"flex justify-between items-center mb-6"},J={class:"flex space-x-3"},K={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-4 mb-6"},R={class:"flex flex-col lg:flex-row gap-4"},W={class:"flex-1"},X={class:"relative"},Y={class:"lg:w-48"},Z=["value"],ee={class:"lg:w-48"},te=["value"],re={class:"flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1"},ae={key:0,class:"flex items-center gap-2 mt-4 pt-4 border-t border-gray-200 dark:border-gray-700"},se={key:0,class:"inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"},le={key:1,class:"inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"},oe={key:2,class:"inline-flex items-center px-2 py-1 rounded-full text-xs bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200"},de={key:0,class:"flex justify-center items-center h-64"},ie={key:1,class:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6"},ne={class:"flex"},ue={class:"text-sm text-red-700 dark:text-red-300 mt-1"},ce={key:2},ge={key:0,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"},xe={class:"p-6"},ve={class:"flex items-center space-x-4"},he={class:"flex-shrink-0"},pe={class:"w-16 h-16 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center"},me=["src","alt"],fe={key:1,class:"w-8 h-8 text-gray-600 dark:text-gray-300",fill:"currentColor",viewBox:"0 0 20 20"},ye={class:"flex-1 min-w-0"},ke={class:"text-lg font-medium text-gray-900 dark:text-white truncate"},we={class:"text-sm text-gray-500 dark:text-gray-400 truncate"},be={key:0,class:"text-sm text-gray-500 dark:text-gray-400 truncate"},_e={class:"mt-4 space-y-2"},ze={class:"flex items-center text-sm text-gray-600 dark:text-gray-400"},Ce=["href"],Me={key:0,class:"flex items-center text-sm text-gray-600 dark:text-gray-400"},Ve=["href"],Be={class:"mt-4 flex space-x-2"},He=["href"],Le={key:1,class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},Te={class:"overflow-x-auto"},$e={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},Pe={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},Ee={class:"px-6 py-4 whitespace-nowrap"},je={class:"flex items-center"},Ae={class:"flex-shrink-0 w-10 h-10"},Ne={class:"w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center"},De=["src","alt"],Se={key:1,class:"w-5 h-5 text-gray-600 dark:text-gray-300",fill:"currentColor",viewBox:"0 0 20 20"},Fe={class:"ml-4"},Ue={class:"text-sm font-medium text-gray-900 dark:text-white"},Ie={class:"text-sm text-gray-500 dark:text-gray-400"},Oe={class:"px-6 py-4 whitespace-nowrap"},Qe={class:"text-sm text-gray-900 dark:text-white"},qe={class:"px-6 py-4 whitespace-nowrap"},Ge={class:"text-sm text-gray-900 dark:text-white"},Je={class:"px-6 py-4 whitespace-nowrap"},Ke={class:"flex space-x-2"},Re=["href"],We=["href"],Xe={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},Ye={key:3,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-12 text-center"},Ze={class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},at={__name:"PersonnelDirectory",setup(et){Q();const C=i([]),k=i([]),w=i([]),b=i(!1),m=i(null),n=i(""),u=i(""),c=i(""),v=i("grid"),_=L(()=>{let a=C.value;if(n.value.trim()){const t=n.value.toLowerCase();a=a.filter(o=>o.full_name.toLowerCase().includes(t)||o.email.toLowerCase().includes(t)||o.position&&o.position.toLowerCase().includes(t)||o.department&&o.department.name.toLowerCase().includes(t))}return u.value&&(a=a.filter(t=>t.department_id==u.value)),c.value&&(a=a.filter(t=>t.skills&&t.skills.some(o=>o.id==c.value))),a}),M=L(()=>n.value.trim()!==""||u.value!==""||c.value!==""),P=a=>{const t=k.value.find(o=>o.id==a);return t?t.name:""},E=a=>{const t=w.value.find(o=>o.id==a);return t?t.name:""};let V=null;const j=()=>{clearTimeout(V),V=setTimeout(()=>{},300)},A=async()=>{b.value=!0,m.value=null;try{const a=await fetch("/api/personnel/users",{credentials:"include"});if(!a.ok)throw new Error(`HTTP ${a.status}: ${a.statusText}`);const t=await a.json();if(t.success)C.value=t.data.users||[];else throw new Error(t.message||"Errore nel caricamento dei dipendenti")}catch(a){console.error("Error fetching employees:",a),m.value=a.message}finally{b.value=!1}},N=async()=>{try{const a=await fetch("/api/personnel/departments",{credentials:"include"});if(!a.ok)throw new Error(`HTTP ${a.status}: ${a.statusText}`);const t=await a.json();t.success&&(k.value=t.data.departments||[])}catch(a){console.error("Error fetching departments:",a)}},D=async()=>{try{const a=await fetch("/api/personnel/skills",{credentials:"include"});if(!a.ok)throw new Error(`HTTP ${a.status}: ${a.statusText}`);const t=await a.json();t.success&&(w.value=t.data.skills||[])}catch(a){console.error("Error fetching skills:",a)}},B=()=>{},S=()=>{n.value="",u.value="",c.value=""};return F(async()=>{await Promise.all([A(),N(),D()])}),(a,t)=>{const o=I("router-link");return l(),s("div",q,[e("div",G,[t[8]||(t[8]=U('<div class="flex items-center"><svg class="w-8 h-8 text-blue-600 dark:text-blue-400 mr-3" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z" clip-rule="evenodd"></path></svg><div><h1 class="text-2xl font-bold text-gray-900 dark:text-white">Directory Aziendale</h1><p class="text-gray-600 dark:text-gray-400 mt-1">Trova e contatta i colleghi</p></div></div>',1)),e("div",J,[h(o,{to:"/app/personnel/admin",class:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"},{default:p(()=>t[5]||(t[5]=[e("svg",{class:"w-4 h-4 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z","clip-rule":"evenodd"})],-1),g(" Amministrazione ")])),_:1,__:[5]}),h(o,{to:"/app/personnel/orgchart",class:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"},{default:p(()=>t[6]||(t[6]=[e("svg",{class:"w-4 h-4 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 0l-2 2a1 1 0 101.414 1.414L8 10.414l1.293 1.293a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})],-1),g(" Organigramma ")])),_:1,__:[6]}),h(o,{to:"/app/personnel/skills",class:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"},{default:p(()=>t[7]||(t[7]=[e("svg",{class:"w-4 h-4 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})],-1),g(" Competenze ")])),_:1,__:[7]})])]),e("div",K,[e("div",R,[e("div",W,[e("div",X,[t[9]||(t[9]=e("svg",{class:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z","clip-rule":"evenodd"})],-1)),z(e("input",{"onUpdate:modelValue":t[0]||(t[0]=r=>n.value=r),onInput:j,type:"text",placeholder:"Cerca per nome, email, posizione...",class:"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,544),[[O,n.value]])])]),e("div",Y,[z(e("select",{"onUpdate:modelValue":t[1]||(t[1]=r=>u.value=r),onChange:B,class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},[t[10]||(t[10]=e("option",{value:""},"Tutti i dipartimenti",-1)),(l(!0),s(f,null,y(k.value,r=>(l(),s("option",{key:r.id,value:r.id},d(r.name),9,Z))),128))],544),[[T,u.value]])]),e("div",ee,[z(e("select",{"onUpdate:modelValue":t[2]||(t[2]=r=>c.value=r),onChange:B,class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},[t[11]||(t[11]=e("option",{value:""},"Tutte le competenze",-1)),(l(!0),s(f,null,y(w.value,r=>(l(),s("option",{key:r.id,value:r.id},d(r.name),9,te))),128))],544),[[T,c.value]])]),e("div",re,[e("button",{onClick:t[3]||(t[3]=r=>v.value="grid"),class:$(["px-3 py-1 rounded-md text-sm font-medium transition-colors duration-200",v.value==="grid"?"bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm":"text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"])},t[12]||(t[12]=[e("svg",{class:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"})],-1)]),2),e("button",{onClick:t[4]||(t[4]=r=>v.value="list"),class:$(["px-3 py-1 rounded-md text-sm font-medium transition-colors duration-200",v.value==="list"?"bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm":"text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"])},t[13]||(t[13]=[e("svg",{class:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z","clip-rule":"evenodd"})],-1)]),2)])]),M.value?(l(),s("div",ae,[t[17]||(t[17]=e("span",{class:"text-sm text-gray-600 dark:text-gray-400"},"Filtri attivi:",-1)),n.value?(l(),s("span",se,[t[14]||(t[14]=e("svg",{class:"w-3 h-3 mr-1",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z","clip-rule":"evenodd"})],-1)),g(' "'+d(n.value)+'" ',1)])):x("",!0),u.value?(l(),s("span",le,[t[15]||(t[15]=e("svg",{class:"w-3 h-3 mr-1",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z","clip-rule":"evenodd"})],-1)),g(" "+d(P(u.value)),1)])):x("",!0),c.value?(l(),s("span",oe,[t[16]||(t[16]=e("svg",{class:"w-3 h-3 mr-1",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})],-1)),g(" "+d(E(c.value)),1)])):x("",!0),e("button",{onClick:S,class:"text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 underline"}," Pulisci filtri ")])):x("",!0)]),b.value?(l(),s("div",de,t[18]||(t[18]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):m.value?(l(),s("div",ie,[e("div",ne,[t[20]||(t[20]=e("svg",{class:"w-5 h-5 text-red-400 mr-2 mt-0.5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z","clip-rule":"evenodd"})],-1)),e("div",null,[t[19]||(t[19]=e("h3",{class:"text-sm font-medium text-red-800 dark:text-red-200"},"Errore nel caricamento della directory",-1)),e("p",ue,d(m.value),1)])])])):_.value.length>0?(l(),s("div",ce,[v.value==="grid"?(l(),s("div",ge,[(l(!0),s(f,null,y(_.value,r=>(l(),s("div",{key:r.id,class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden hover:shadow-lg transition-shadow duration-200"},[e("div",xe,[e("div",ve,[e("div",he,[e("div",pe,[r.profile_image?(l(),s("img",{key:0,src:r.profile_image,alt:r.full_name,class:"w-16 h-16 rounded-full object-cover"},null,8,me)):(l(),s("svg",fe,t[21]||(t[21]=[e("path",{"fill-rule":"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z","clip-rule":"evenodd"},null,-1)])))])]),e("div",ye,[e("h3",ke,d(r.full_name),1),e("p",we,d(r.position||"Posizione non specificata"),1),r.department?(l(),s("p",be,d(r.department.name),1)):x("",!0)])]),e("div",_e,[e("div",ze,[t[22]||(t[22]=e("svg",{class:"w-4 h-4 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"}),e("path",{d:"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"})],-1)),e("a",{href:`mailto:${r.email}`,class:"hover:text-blue-600 dark:hover:text-blue-400 truncate"},d(r.email),9,Ce)]),r.phone?(l(),s("div",Me,[t[23]||(t[23]=e("svg",{class:"w-4 h-4 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"})],-1)),e("a",{href:`tel:${r.phone}`,class:"hover:text-blue-600 dark:hover:text-blue-400"},d(r.phone),9,Ve)])):x("",!0)]),e("div",Be,[h(o,{to:`/app/personnel/${r.id}`,class:"flex-1 bg-blue-600 hover:bg-blue-700 text-white text-center px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200"},{default:p(()=>t[24]||(t[24]=[g(" Visualizza Profilo ")])),_:2,__:[24]},1032,["to"]),e("a",{href:`mailto:${r.email}`,class:"bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200"},t[25]||(t[25]=[e("svg",{class:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"}),e("path",{d:"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"})],-1)]),8,He)])])]))),128))])):(l(),s("div",Le,[e("div",Te,[e("table",$e,[t[30]||(t[30]=e("thead",{class:"bg-gray-50 dark:bg-gray-700"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Dipendente "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Posizione "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Dipartimento "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Contatti "),e("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Azioni ")])],-1)),e("tbody",Pe,[(l(!0),s(f,null,y(_.value,r=>{var H;return l(),s("tr",{key:r.id,class:"hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"},[e("td",Ee,[e("div",je,[e("div",Ae,[e("div",Ne,[r.profile_image?(l(),s("img",{key:0,src:r.profile_image,alt:r.full_name,class:"w-10 h-10 rounded-full object-cover"},null,8,De)):(l(),s("svg",Se,t[26]||(t[26]=[e("path",{"fill-rule":"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z","clip-rule":"evenodd"},null,-1)])))])]),e("div",Fe,[e("div",Ue,d(r.full_name),1),e("div",Ie,d(r.email),1)])])]),e("td",Oe,[e("div",Qe,d(r.position||"Posizione non specificata"),1)]),e("td",qe,[e("div",Ge,d(((H=r.department)==null?void 0:H.name)||"Nessun dipartimento"),1)]),e("td",Je,[e("div",Ke,[e("a",{href:`mailto:${r.email}`,class:"text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300"},t[27]||(t[27]=[e("svg",{class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"}),e("path",{d:"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"})],-1)]),8,Re),r.phone?(l(),s("a",{key:0,href:`tel:${r.phone}`,class:"text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300"},t[28]||(t[28]=[e("svg",{class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"})],-1)]),8,We)):x("",!0)])]),e("td",Xe,[h(o,{to:`/app/personnel/${r.id}`,class:"text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300"},{default:p(()=>t[29]||(t[29]=[g(" Visualizza Profilo ")])),_:2,__:[29]},1032,["to"])])])}),128))])])])]))])):(l(),s("div",Ye,[t[31]||(t[31]=e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})],-1)),t[32]||(t[32]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun dipendente trovato",-1)),e("p",Ze,d(M.value?"Prova a modificare i filtri di ricerca.":"Non ci sono dipendenti nella directory."),1)]))])}}};export{at as default};

{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/personnel/components/SkillCell.vue"}, "modifiedCode": "<template>\n  <div @click=\"$emit('click')\" \n       :class=\"[\n         'skill-cell cursor-pointer rounded-lg p-2 transition-all duration-200',\n         cellClasses\n       ]\">\n    <!-- Skill Level Stars -->\n    <div class=\"flex justify-center mb-1\">\n      <div class=\"flex space-x-0.5\">\n        <svg v-for=\"i in 5\" \n             :key=\"i\"\n             :class=\"[\n               'w-3 h-3',\n               i <= userSkill.proficiency_level ? 'text-yellow-400' : 'text-gray-300 dark:text-gray-600'\n             ]\"\n             fill=\"currentColor\" \n             viewBox=\"0 0 20 20\">\n          <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"></path>\n        </svg>\n      </div>\n    </div>\n    \n    <!-- Level Number -->\n    <div class=\"text-center\">\n      <span v-if=\"userSkill.proficiency_level > 0\" \n            :class=\"[\n              'text-xs font-medium px-1.5 py-0.5 rounded',\n              levelClasses\n            ]\">\n        {{ userSkill.proficiency_level }}\n      </span>\n      <span v-else class=\"text-xs text-gray-400 dark:text-gray-600\">-</span>\n    </div>\n    \n    <!-- Additional Info (Years/Certification) -->\n    <div v-if=\"userSkill.proficiency_level > 0\" class=\"mt-1 flex justify-center space-x-1\">\n      <!-- Years Experience -->\n      <span v-if=\"userSkill.years_experience > 0\" \n            class=\"text-xs text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-1 rounded\">\n        {{ userSkill.years_experience }}y\n      </span>\n      \n      <!-- Certification Badge -->\n      <span v-if=\"userSkill.is_certified\" \n            class=\"text-xs text-green-700 dark:text-green-300 bg-green-100 dark:bg-green-900 px-1 rounded\"\n            :title=\"userSkill.certification_name || 'Certificato'\">\n        ✓\n      </span>\n      \n      <!-- Assessment Type -->\n      <span v-if=\"userSkill.manager_assessed\" \n            class=\"text-xs text-blue-700 dark:text-blue-300 bg-blue-100 dark:bg-blue-900 px-1 rounded\"\n            title=\"Valutato dal manager\">\n        M\n      </span>\n      <span v-else-if=\"userSkill.self_assessed\" \n            class=\"text-xs text-purple-700 dark:text-purple-300 bg-purple-100 dark:bg-purple-900 px-1 rounded\"\n            title=\"Auto-valutato\">\n        S\n      </span>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { computed } from 'vue'\n\n// Props\nconst props = defineProps({\n  userSkill: {\n    type: Object,\n    required: true\n  },\n  skill: {\n    type: Object,\n    required: true\n  }\n})\n\n// Emits\nconst emit = defineEmits(['click'])\n\n// Computed properties\nconst cellClasses = computed(() => {\n  const level = props.userSkill.proficiency_level\n  \n  if (level === 0) {\n    return 'bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 border border-gray-200 dark:border-gray-700'\n  }\n  \n  const baseClasses = 'hover:shadow-md transform hover:scale-105'\n  \n  switch (level) {\n    case 1:\n      return `${baseClasses} bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 hover:bg-red-100 dark:hover:bg-red-900/30`\n    case 2:\n      return `${baseClasses} bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 hover:bg-orange-100 dark:hover:bg-orange-900/30`\n    case 3:\n      return `${baseClasses} bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 hover:bg-yellow-100 dark:hover:bg-yellow-900/30`\n    case 4:\n      return `${baseClasses} bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 hover:bg-blue-100 dark:hover:bg-blue-900/30`\n    case 5:\n      return `${baseClasses} bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 hover:bg-green-100 dark:hover:bg-green-900/30`\n    default:\n      return `${baseClasses} bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700`\n  }\n})\n\nconst levelClasses = computed(() => {\n  const level = props.userSkill.proficiency_level\n  \n  switch (level) {\n    case 1:\n      return 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200'\n    case 2:\n      return 'bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200'\n    case 3:\n      return 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200'\n    case 4:\n      return 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200'\n    case 5:\n      return 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200'\n    default:\n      return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200'\n  }\n})\n</script>\n\n<style scoped>\n.skill-cell {\n  min-height: 60px;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n}\n\n.skill-cell:hover {\n  z-index: 10;\n  position: relative;\n}\n\n/* Tooltip styles */\n.skill-cell[title]:hover::after {\n  content: attr(title);\n  position: absolute;\n  bottom: 100%;\n  left: 50%;\n  transform: translateX(-50%);\n  background: rgba(0, 0, 0, 0.8);\n  color: white;\n  padding: 4px 8px;\n  border-radius: 4px;\n  font-size: 12px;\n  white-space: nowrap;\n  z-index: 20;\n  margin-bottom: 4px;\n}\n\n.skill-cell[title]:hover::before {\n  content: '';\n  position: absolute;\n  bottom: 100%;\n  left: 50%;\n  transform: translateX(-50%);\n  border: 4px solid transparent;\n  border-top-color: rgba(0, 0, 0, 0.8);\n  z-index: 20;\n}\n</style>\n"}
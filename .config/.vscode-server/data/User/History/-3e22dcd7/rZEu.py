"""
Test suite for Personnel API endpoints.
Tests for users, departments, and skills management.
"""

import pytest
import json
from datetime import datetime, date
from flask import url_for

from models import User, Department, Skill, UserSkill, UserProfile
from extensions import db


class TestPersonnelUsersAPI:
    """Test cases for /api/personnel/users endpoints."""

    def test_get_users_success(self, client, auth, sample_users):
        """Test successful retrieval of users list."""
        # Login as admin to have proper permissions
        auth.login(username='admin', password='password')

        response = client.get('/api/personnel/users')
        assert response.status_code == 200

        data = json.loads(response.data)
        assert data['success'] is True
        assert 'users' in data['data']
        assert 'pagination' in data['data']
        assert len(data['data']['users']) > 0

        # Check user structure
        user = data['data']['users'][0]
        required_fields = [
            'id', 'username', 'email', 'first_name', 'last_name',
            'full_name', 'role', 'is_active'
        ]
        for field in required_fields:
            assert field in user

    def test_get_users_with_pagination(self, client, auth, sample_users):
        """Test users list with pagination parameters."""
        auth.login(username='admin', password='password')

        response = client.get('/api/personnel/users?page=1&per_page=2')
        assert response.status_code == 200

        data = json.loads(response.data)
        assert data['success'] is True
        assert len(data['data']['users']) <= 2
        assert data['data']['pagination']['per_page'] == 2
        assert data['data']['pagination']['page'] == 1

    def test_get_users_with_search(self, client, auth, sample_users):
        """Test users list with search parameter."""
        auth.login(username='admin', password='password')

        # Search for a specific user
        response = client.get('/api/personnel/users?search=admin')
        assert response.status_code == 200

        data = json.loads(response.data)
        assert data['success'] is True

        # Check that search results contain the search term
        for user in data['data']['users']:
            user_text = f"{user['username']} {user['first_name']} {user['last_name']} {user['email']}".lower()
            assert 'admin' in user_text

    def test_get_users_with_department_filter(self, client, auth, sample_users, sample_departments):
        """Test users list filtered by department."""
        auth.login(username='admin', password='password')

        # Get first department ID from database to avoid DetachedInstanceError
        from models import Department
        dept = Department.query.first()
        if not dept:
            pytest.skip("No departments found in database")

        response = client.get(f'/api/personnel/users?department_id={dept.id}')
        assert response.status_code == 200

        data = json.loads(response.data)
        assert data['success'] is True

        # All users should belong to the specified department
        for user in data['data']['users']:
            if user['department_id'] is not None:
                assert user['department_id'] == dept.id

    def test_get_users_with_role_filter(self, client, auth, sample_users):
        """Test users list filtered by role."""
        auth.login(username='admin', password='password')

        response = client.get('/api/personnel/users?role=admin')
        assert response.status_code == 200

        data = json.loads(response.data)
        assert data['success'] is True

        # All users should have admin role
        for user in data['data']['users']:
            assert user['role'] == 'admin'

    def test_get_users_with_active_filter(self, client, auth, sample_users):
        """Test users list filtered by active status."""
        auth.login(username='admin', password='password')

        response = client.get('/api/personnel/users?is_active=true')
        assert response.status_code == 200

        data = json.loads(response.data)
        assert data['success'] is True

        # All users should be active
        for user in data['data']['users']:
            assert user['is_active'] is True

    def test_get_users_with_skills_filter(self, client, auth, sample_users, sample_skills):
        """Test users list filtered by skills."""
        auth.login(username='admin', password='password')

        # Get first skill ID from database to avoid DetachedInstanceError
        from models import Skill
        skill = Skill.query.first()
        if not skill:
            pytest.skip("No skills found in database")

        response = client.get(f'/api/personnel/users?skills={skill.id}')
        assert response.status_code == 200

        data = json.loads(response.data)
        assert data['success'] is True

        # Users should have the specified skill
        for user in data['data']['users']:
            skill_ids = [s['id'] for s in user['skills']]
            if skill_ids:  # Only check if user has skills
                assert skill.id in skill_ids

    def test_get_users_with_ordering(self, client, auth, sample_users):
        """Test users list with custom ordering."""
        auth.login(username='admin', password='password')

        # Test ascending order by first name
        response = client.get('/api/personnel/users?order_by=first_name&order_dir=asc')
        assert response.status_code == 200

        data = json.loads(response.data)
        assert data['success'] is True

        # Check that results are ordered
        if len(data['data']['users']) > 1:
            first_names = [user['first_name'] for user in data['data']['users']]
            assert first_names == sorted(first_names)

    def test_get_users_unauthorized(self, client, auth):
        """Test users list access without authentication."""
        # Ensure user is logged out
        auth.logout()

        response = client.get('/api/personnel/users')
        assert response.status_code == 401

    def test_get_users_forbidden(self, client, auth):
        """Test users list access without proper permissions."""
        # Login as user without VIEW_PERSONNEL_DATA permission
        auth.login(username='employee', password='password')

        response = client.get('/api/personnel/users')
        assert response.status_code == 403

    def test_get_user_by_id_success(self, client, auth, sample_users):
        """Test successful retrieval of specific user."""
        auth.login(username='admin', password='password')

        # Get first user ID from database to avoid DetachedInstanceError
        from models import User
        user = User.query.first()
        if not user:
            pytest.skip("No users found in database")

        response = client.get(f'/api/personnel/users/{user.id}')
        assert response.status_code == 200

        data = json.loads(response.data)
        assert data['success'] is True
        assert 'user' in data['data']

        user_data = data['data']['user']
        assert user_data['id'] == user.id
        assert user_data['username'] == user.username
        assert user_data['email'] == user.email

        # Check detailed fields
        detailed_fields = [
            'bio', 'dark_mode', 'created_at', 'skills', 'projects', 'profile'
        ]
        for field in detailed_fields:
            assert field in user_data

    def test_get_user_by_id_not_found(self, client, auth):
        """Test retrieval of non-existent user."""
        auth.login(username='admin', password='password')

        response = client.get('/api/personnel/users/99999')
        assert response.status_code == 404

    def test_get_user_by_id_unauthorized(self, client, auth, sample_users):
        """Test user retrieval without authentication."""
        # Ensure user is logged out
        auth.logout()

        # Get first user ID from database to avoid DetachedInstanceError
        from models import User
        user = User.query.first()
        if not user:
            pytest.skip("No users found in database")

        response = client.get(f'/api/personnel/users/{user.id}')
        assert response.status_code == 401


class TestPersonnelDepartmentsAPI:
    """Test cases for /api/personnel/departments endpoints."""

    def test_get_departments_success(self, client, auth, sample_departments):
        """Test successful retrieval of departments list."""
        auth.login()

        response = client.get('/api/personnel/departments')
        assert response.status_code == 200

        data = json.loads(response.data)
        assert data['success'] is True
        assert 'departments' in data['data']
        assert len(data['data']['departments']) > 0

        # Check department structure
        dept = data['data']['departments'][0]
        required_fields = [
            'id', 'name', 'description', 'manager_id', 'user_count', 'users'
        ]
        for field in required_fields:
            assert field in dept

    def test_get_departments_with_manager_info(self, client, auth, sample_departments, sample_users):
        """Test departments list includes manager information."""
        auth.login(username='admin', password='password')

        # Get fresh instances from database to avoid DetachedInstanceError
        from models import Department, User
        dept = Department.query.first()
        manager = User.query.first()
        if not dept or not manager:
            pytest.skip("No departments or users found in database")

        dept.manager_id = manager.id
        db.session.commit()

        response = client.get('/api/personnel/departments')
        assert response.status_code == 200

        data = json.loads(response.data)
        dept_data = next(d for d in data['data']['departments'] if d['id'] == dept.id)

        assert dept_data['manager_id'] == manager.id
        assert dept_data['manager'] is not None
        assert dept_data['manager']['id'] == manager.id
        assert dept_data['manager']['full_name'] == manager.full_name

    def test_get_departments_with_users(self, client, auth, sample_departments, sample_users):
        """Test departments list includes user information."""
        auth.login(username='admin', password='password')

        # Get fresh instances from database to avoid DetachedInstanceError
        from models import Department, User
        dept = Department.query.first()
        user = User.query.first()
        if not dept or not user:
            pytest.skip("No departments or users found in database")

        user.department_id = dept.id
        db.session.commit()

        response = client.get('/api/personnel/departments')
        assert response.status_code == 200

        data = json.loads(response.data)
        dept_data = next(d for d in data['data']['departments'] if d['id'] == dept.id)

        assert dept_data['user_count'] >= 1
        assert len(dept_data['users']) >= 1

        user_data = next(u for u in dept_data['users'] if u['id'] == user.id)
        assert user_data['full_name'] == user.full_name
        assert user_data['position'] == user.position

    def test_get_departments_unauthorized(self, client, auth):
        """Test departments list access without authentication."""
        # Ensure user is logged out
        auth.logout()

        response = client.get('/api/personnel/departments')
        assert response.status_code == 401


class TestPersonnelSkillsAPI:
    """Test cases for /api/personnel/skills endpoints."""

    def test_get_skills_success(self, client, auth, sample_skills):
        """Test successful retrieval of skills list."""
        auth.login()

        response = client.get('/api/personnel/skills')
        assert response.status_code == 200

        data = json.loads(response.data)
        assert data['success'] is True
        assert 'skills' in data['data']
        assert 'categories' in data['data']
        assert len(data['data']['skills']) > 0

        # Check skill structure
        skill = data['data']['skills'][0]
        required_fields = [
            'id', 'name', 'category', 'description', 'user_count', 'users'
        ]
        for field in required_fields:
            assert field in skill

    def test_get_skills_with_category_filter(self, client, auth, sample_skills):
        """Test skills list filtered by category."""
        auth.login(username='admin', password='password')

        # Get first skill's category from database to avoid DetachedInstanceError
        from models import Skill
        skill = Skill.query.first()
        if not skill or not skill.category:
            pytest.skip("No skills with category found in database")
        category = skill.category

        response = client.get(f'/api/personnel/skills?category={category}')
        assert response.status_code == 200

        data = json.loads(response.data)
        assert data['success'] is True

        # All skills should belong to the specified category
        for skill_data in data['data']['skills']:
            assert skill_data['category'] == category

    def test_get_skills_with_search(self, client, auth, sample_skills):
        """Test skills list with search parameter."""
        auth.login(username='admin', password='password')

        # Get first skill from database to avoid DetachedInstanceError
        from models import Skill
        skill = Skill.query.first()
        if not skill:
            pytest.skip("No skills found in database")
        search_term = skill.name[:3]  # First 3 characters

        response = client.get(f'/api/personnel/skills?search={search_term}')
        assert response.status_code == 200

        data = json.loads(response.data)
        assert data['success'] is True

        # Check that search results contain the search term
        for skill_data in data['data']['skills']:
            skill_text = f"{skill_data['name']} {skill_data['description'] or ''}".lower()
            assert search_term.lower() in skill_text

    def test_get_skills_categories(self, client, auth, sample_skills):
        """Test that skills endpoint returns available categories."""
        auth.login(username='admin', password='password')

        response = client.get('/api/personnel/skills')
        assert response.status_code == 200

        data = json.loads(response.data)
        assert data['success'] is True
        assert 'categories' in data['data']
        assert isinstance(data['data']['categories'], list)

        # Categories should match skills categories from database
        from models import Skill
        skills = Skill.query.all()
        skill_categories = set(skill.category for skill in skills if skill.category)
        returned_categories = set(data['data']['categories'])
        assert skill_categories.issubset(returned_categories)

    def test_get_skills_unauthorized(self, client, auth):
        """Test skills list access without authentication."""
        # Ensure user is logged out
        auth.logout()

        response = client.get('/api/personnel/skills')
        assert response.status_code == 401

    def test_get_skills_forbidden(self, client, auth):
        """Test skills list access without proper permissions."""
        # Login as user without VIEW_PERSONNEL_DATA permission
        auth.login(username='employee', password='password')

        response = client.get('/api/personnel/skills')
        assert response.status_code == 403


class TestPersonnelBulkOperationsAPI:
    """Test cases for bulk operations endpoints."""

    def test_export_full_data_success(self, client, auth, sample_users):
        """Test successful export of full personnel data."""
        auth.login()  # Uses default admin credentials from fixture

        response = client.get('/api/personnel/export')
        assert response.status_code == 200
        assert response.headers['Content-Type'] == 'text/csv'
        assert 'attachment' in response.headers['Content-Disposition']

        # Check CSV content
        csv_content = response.data.decode('utf-8')
        assert 'Email' in csv_content
        assert 'Nome' in csv_content
        assert 'Cognome' in csv_content

    def test_export_contacts_data_success(self, client, auth, sample_users):
        """Test successful export of contacts data."""
        auth.login(username='admin', password='password')

        response = client.get('/api/personnel/export/contacts')
        assert response.status_code == 200
        assert response.headers['Content-Type'] == 'text/csv'
        assert 'contacts-export' in response.headers['Content-Disposition']

        # Check CSV content
        csv_content = response.data.decode('utf-8')
        assert 'Nome' in csv_content
        assert 'Cognome' in csv_content
        assert 'Email' in csv_content

    def test_export_unauthorized(self, client, auth):
        """Test export access without authentication."""
        auth.logout()

        response = client.get('/api/personnel/export')
        assert response.status_code == 401

        response = client.get('/api/personnel/export/contacts')
        assert response.status_code == 401

    def test_export_forbidden(self, client, auth):
        """Test export access without proper permissions."""
        auth.login(username='employee', password='password')

        response = client.get('/api/personnel/export')
        assert response.status_code == 403

    def test_import_csv_success(self, client, auth, sample_departments):
        """Test successful CSV import."""
        auth.login(username='admin', password='password')

        # Create CSV content
        csv_content = """email,first_name,last_name,phone,department_id,role,is_active
<EMAIL>,Test,User1,+39 ************,1,employee,true
<EMAIL>,Test,User2,+39 ************,1,employee,true"""

        # Create file-like object
        from io import BytesIO
        csv_file = BytesIO(csv_content.encode('utf-8'))
        csv_file.name = 'test_import.csv'

        response = client.post('/api/personnel/import',
                             data={'file': (csv_file, 'test_import.csv')},
                             content_type='multipart/form-data')

        assert response.status_code == 200

        data = json.loads(response.data)
        assert data['success'] is True
        assert 'imported' in data['data']
        assert 'total' in data['data']
        assert data['data']['imported'] >= 0
        assert data['data']['total'] == 2

    def test_import_csv_invalid_file(self, client, auth):
        """Test CSV import with invalid file."""
        auth.login(username='admin', password='password')

        # Test with no file
        response = client.post('/api/personnel/import')
        assert response.status_code == 400

        # Test with non-CSV file
        from io import BytesIO
        txt_file = BytesIO(b'not a csv file')
        txt_file.name = 'test.txt'

        response = client.post('/api/personnel/import',
                             data={'file': (txt_file, 'test.txt')},
                             content_type='multipart/form-data')
        assert response.status_code == 400

    def test_import_csv_validation_errors(self, client, auth):
        """Test CSV import with validation errors."""
        auth.login(username='admin', password='password')

        # Create CSV with missing required fields
        csv_content = """email,first_name,last_name
,Test,User1
<EMAIL>,,User2"""

        from io import BytesIO
        csv_file = BytesIO(csv_content.encode('utf-8'))
        csv_file.name = 'test_import.csv'

        response = client.post('/api/personnel/import',
                             data={'file': (csv_file, 'test_import.csv')},
                             content_type='multipart/form-data')

        assert response.status_code == 200

        data = json.loads(response.data)
        assert data['success'] is True
        assert 'errors' in data['data']
        assert len(data['data']['errors']) > 0

    def test_import_unauthorized(self, client, auth):
        """Test import access without authentication."""
        auth.logout()

        response = client.post('/api/personnel/import')
        assert response.status_code == 401

    def test_import_forbidden(self, client, auth):
        """Test import access without proper permissions."""
        auth.login(username='employee', password='password')

        response = client.post('/api/personnel/import')
        assert response.status_code == 403

    def test_verify_data_integrity_success(self, client, auth, sample_users, sample_departments):
        """Test successful data integrity verification."""
        auth.login(username='admin', password='password')

        response = client.get('/api/personnel/verify')
        assert response.status_code == 200

        data = json.loads(response.data)
        assert data['success'] is True
        assert 'issues' in data['data']
        assert isinstance(data['data']['issues'], list)

    def test_verify_data_integrity_with_issues(self, client, auth, sample_users):
        """Test data integrity verification with issues found."""
        auth.login(username='admin', password='password')

        # Create user with missing email to trigger integrity issue
        from models import User
        user_no_email = User(
            username='no_email_user',
            first_name='No',
            last_name='Email',
            role='employee',
            is_active=True
        )
        user_no_email.set_password('password')
        db.session.add(user_no_email)
        db.session.commit()

        response = client.get('/api/personnel/verify')
        assert response.status_code == 200

        data = json.loads(response.data)
        assert data['success'] is True
        assert len(data['data']['issues']) > 0

        # Should find the user without email
        issues_text = ' '.join(data['data']['issues'])
        assert 'senza email' in issues_text

    def test_verify_unauthorized(self, client, auth):
        """Test verify access without authentication."""
        auth.logout()

        response = client.get('/api/personnel/verify')
        assert response.status_code == 401

    def test_verify_forbidden(self, client, auth):
        """Test verify access without proper permissions."""
        auth.login(username='employee', password='password')

        response = client.get('/api/personnel/verify')
        assert response.status_code == 403


class TestPersonnelSkillsCRUD:
    """Test cases for skills CRUD operations."""

    def test_create_skill_success(self, client, auth):
        """Test successful skill creation."""
        auth.login(username='admin', password='password')

        skill_data = {
            'name': 'New Skill',
            'category': 'Testing',
            'description': 'A test skill'
        }

        response = client.post('/api/personnel/skills',
                             data=json.dumps(skill_data),
                             content_type='application/json')

        assert response.status_code == 200

        data = json.loads(response.data)
        assert data['success'] is True
        assert 'skill' in data['data']
        assert data['data']['skill']['name'] == 'New Skill'
        assert data['data']['skill']['category'] == 'Testing'

    def test_create_skill_duplicate_name(self, client, auth, sample_skills):
        """Test skill creation with duplicate name."""
        auth.login(username='admin', password='password')

        # Get existing skill name
        from models import Skill
        existing_skill = Skill.query.first()
        if not existing_skill:
            pytest.skip("No skills found in database")

        skill_data = {
            'name': existing_skill.name,
            'category': 'Testing'
        }

        response = client.post('/api/personnel/skills',
                             data=json.dumps(skill_data),
                             content_type='application/json')

        assert response.status_code == 400

    def test_update_skill_success(self, client, auth, sample_skills):
        """Test successful skill update."""
        auth.login(username='admin', password='password')

        from models import Skill
        skill = Skill.query.first()
        if not skill:
            pytest.skip("No skills found in database")

        update_data = {
            'name': 'Updated Skill Name',
            'description': 'Updated description'
        }

        response = client.put(f'/api/personnel/skills/{skill.id}',
                            data=json.dumps(update_data),
                            content_type='application/json')

        assert response.status_code == 200

        data = json.loads(response.data)
        assert data['success'] is True
        assert data['data']['skill']['name'] == 'Updated Skill Name'

    def test_delete_skill_success(self, client, auth):
        """Test successful skill deletion."""
        auth.login(username='admin', password='password')

        # Create a skill to delete
        from models import Skill
        skill = Skill(name='To Delete', category='Test')
        db.session.add(skill)
        db.session.commit()

        response = client.delete(f'/api/personnel/skills/{skill.id}')
        assert response.status_code == 200

        data = json.loads(response.data)
        assert data['success'] is True

        # Verify skill is deleted
        deleted_skill = Skill.query.get(skill.id)
        assert deleted_skill is None

    def test_delete_skill_with_users(self, client, auth, sample_skills, sample_users):
        """Test skill deletion when assigned to users."""
        auth.login(username='admin', password='password')

        from models import Skill, User, UserSkill
        skill = Skill.query.first()
        user = User.query.first()

        if not skill or not user:
            pytest.skip("No skills or users found in database")

        # Assign skill to user
        user_skill = UserSkill(user_id=user.id, skill_id=skill.id, level=3)
        db.session.add(user_skill)
        db.session.commit()

        response = client.delete(f'/api/personnel/skills/{skill.id}')
        assert response.status_code == 400

        data = json.loads(response.data)
        assert data['success'] is False
        assert 'assegnata' in data['message']

    def test_skills_crud_unauthorized(self, client, auth):
        """Test skills CRUD operations without authentication."""
        auth.logout()

        # Test create
        response = client.post('/api/personnel/skills')
        assert response.status_code == 401

        # Test update
        response = client.put('/api/personnel/skills/1')
        assert response.status_code == 401

        # Test delete
        response = client.delete('/api/personnel/skills/1')
        assert response.status_code == 401

    def test_skills_crud_forbidden(self, client, auth):
        """Test skills CRUD operations without proper permissions."""
        auth.login(username='employee', password='password')

        # Test create
        response = client.post('/api/personnel/skills')
        assert response.status_code == 403

        # Test update
        response = client.put('/api/personnel/skills/1')
        assert response.status_code == 403

        # Test delete
        response = client.delete('/api/personnel/skills/1')
        assert response.status_code == 403

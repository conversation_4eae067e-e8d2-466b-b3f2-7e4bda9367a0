{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "specs/vue_professional_migration.md"}, "originalCode": "# DatPortal Vue.js Migration - Status Report\n\n## Stato Corrente (Maggio 2025)\n\n### ✅ COMPLETATO - ARCHITETTURA BASE\n**Framework e Tooling:**\n- ✅ Vue.js 3 + Vite + SFC (Single File Components)\n- ✅ Vue Router 4 con layout modulari e SPA navigation\n- ✅ Tailwind CSS responsive + dark mode completo\n- ✅ Stores Pinia (auth, tenant, projects, personnel)\n- ✅ Build system ottimizzato (asset fissi, no hash)\n- ✅ Sistema autenticazione completa (login/register/reset password)\n- ✅ Sistema RBAC backend completo (5 ruoli, 25+ permessi)\n\n**Layout e UI Components:**\n- ✅ AppLayout con sidebar collassabile e responsive\n- ✅ AppHeader con breadcrumbs e quick actions\n- ✅ AppSidebar con navigazione modulare e icone SVG coerenti\n- ✅ SidebarFooter con menu utente (z-index fixed)\n- ✅ Dark mode toggle funzionante\n- ✅ Sistema di notifiche e feedback utente\n\n### ✅ COMPLETATO - MODULI BUSINESS\n\n**Dashboard Module:**\n- ✅ Dashboard.vue completa con dati PostgreSQL reali\n- ✅ KPI cards con metriche autentiche\n- ✅ Grafici Chart.js integrati\n- ✅ Sistema caching per performance\n\n**Projects Module - CRUD COMPLETO:**\n- ✅ Lista progetti (`/app/projects`) con filtri e ricerca\n- ✅ Dettaglio progetto (`/app/projects/:id`) con 7 tab funzionali\n- ✅ Creazione progetti (`/app/projects/create`) - Form completo\n- ✅ Modifica progetti (`/app/projects/:id/edit`) - Form + eliminazione\n- ✅ API DELETE progetti implementata\n- ✅ API Clienti completa per form progetti\n\n**Project View - Tab Completi:**\n- ✅ Tab Panoramica: Informazioni generali e metriche\n- ✅ Tab Task: Lista task con filtri e gestione\n- ✅ Tab Gantt: Timeline interattiva con linea \"oggi\" corretta\n- ✅ Tab Team: Gestione membri progetto\n- ✅ Tab Timesheet: Registrazione ore lavorate\n- ✅ Tab KPI & Analytics: Metriche avanzate con configurazione\n- ✅ Tab Spese: Gestione completa con tutti i campi del modello\n\n**Expenses Management - COMPLETO:**\n- ✅ ProjectExpenses.vue con visualizzazione completa\n- ✅ ExpenseModal.vue con form completo (tutti i campi del modello)\n- ✅ Campi: billing_type, status, receipt_path, categorie\n- ✅ Upload ricevute con validazione dimensione\n- ✅ Helper functions per etichette e styling\n- ✅ API expenses registrata e funzionante\n\n**User Management:**\n- ✅ Pagina Profilo (`/app/profile`) - Gestione dati personali\n- ✅ Pagina Impostazioni (`/app/settings`) - Preferenze e cambio password\n- ✅ API `/api/auth/profile`, `/api/auth/settings`, `/api/auth/change-password`\n- ✅ Menu utente funzionante con links corretti\n\n**Personnel/HR Module - SPRINT 1 COMPLETATO:**\n- ✅ PersonnelList.vue (`/app/personnel`) - Lista dipendenti isofunzionale\n- ✅ Store Pinia personnel.js - Gestione stato HR completo\n- ✅ Routing completo per tutte le sottopagine Personnel\n- ✅ Sidebar con menu Personnel e icone SVG coerenti\n- ✅ Componenti placeholder per Sprint 2 e 3\n- ✅ API Personnel complete (users, departments, skills)\n- ✅ Filtri avanzati: dipartimento, competenze, ricerca\n- ✅ Paginazione funzionante con conteggio corretto\n- ✅ Grid responsive con card dipendenti\n- ✅ Dark mode supportato completamente\n\n### ❌ DA COMPLETARE - PROSSIMI MODULI\n\n**Personnel/HR Module - SPRINT 2 (PRIORITÀ ALTA):**\n- ⏳ PersonnelProfile.vue - Dettaglio dipendente con tab (progetti, task, competenze, timesheet, CV)\n- ⏳ DepartmentList.vue - Gestione dipartimenti con gerarchia\n- ⏳ DepartmentView.vue - Dashboard dipartimento con KPI\n- ⏳ PersonnelDirectory.vue - Directory aziendale compatta\n\n**Personnel/HR Module - SPRINT 3:**\n- ⏳ PersonnelOrgChart.vue - Organigramma interattivo\n- ⏳ SkillsMatrix.vue - Matrice competenze utenti vs skills\n- ⏳ PersonnelAdmin.vue - Amministrazione HR avanzata\n\n**Admin Module:**\n- Gestione utenti e ruoli\n- Configurazioni sistema\n- Template KPI e configurazioni globali\n\n**Tasks Module Standalone:**\n- Vista kanban indipendente\n- Gestione task cross-project\n\n### ✅ PROBLEMI RISOLTI\n- ✅ Errori JavaScript assets (MIME type text/html → application/javascript)\n- ✅ API expenses registrata correttamente nell'app\n- ✅ Gantt chart linea \"oggi\" posizionata correttamente\n- ✅ Favicon aggiunto e funzionante\n- ✅ Tabella project_team duplicate definition (extend_existing=True)\n- ✅ Menu utente dropdown z-index (fixed positioning)\n- ✅ Tab Spese con tutti i campi del modello ProjectExpense\n- ✅ API `/api/auth/settings` 404 error\n- ✅ CRUD Progetti completo (Create, Read, Update, Delete)\n- ✅ Funzione `formatHours` arrotondamento decimali KPI\n- ✅ Conteggio dipendenti Personnel (pagination mapping fix)\n- ✅ Icone sidebar coerenti (rimosse emoji colorate)\n\n## Roadmap Q2 2025\n\n### ✅ FASE 1 COMPLETATA - Projects & Personnel Base\n**Projects Module:** CRUD completo, 7 tab funzionali, gestione spese\n**Personnel Sprint 1:** Lista dipendenti, filtri, paginazione, store Pinia\n\n### 🔄 FASE 2 IN CORSO - Personnel/HR Completo\n**Sprint 2:** PersonnelProfile, DepartmentList, DepartmentView, PersonnelDirectory\n**Sprint 3:** PersonnelOrgChart, SkillsMatrix, PersonnelAdmin\n\n### ⏳ FASE 3 PIANIFICATA - Admin & Tasks\n**Admin Module:** Gestione utenti, configurazioni sistema, template KPI\n**Tasks Module:** Vista kanban standalone, gestione task cross-project\n\n## File Chiave - Architettura Corrente\n\n### ✅ Frontend Vue.js - Struttura Consolidata\n**Layout e Routing:**\n- `frontend/src/App.vue` - App principale con router-view\n- `frontend/src/components/layout/AppLayout.vue` - Layout SPA con sidebar\n- `frontend/src/components/layout/AppHeader.vue` - Header con breadcrumbs\n- `frontend/src/components/layout/AppSidebar.vue` - Navigazione modulare\n- `frontend/src/components/layout/SidebarFooter.vue` - Menu utente (z-index fixed)\n\n**Projects Module (COMPLETO):**\n- `frontend/src/views/projects/ProjectList.vue` - Lista progetti\n- `frontend/src/views/projects/ProjectView.vue` - Dettaglio con 7 tab\n- `frontend/src/views/projects/ProjectCreate.vue` - Creazione progetti\n- `frontend/src/views/projects/ProjectEdit.vue` - Modifica progetti\n- `frontend/src/views/projects/components/ProjectExpenses.vue` - Gestione spese\n- `frontend/src/views/projects/components/ExpenseModal.vue` - Form spese completo\n- `frontend/src/views/projects/components/ProjectGantt.vue` - Timeline interattiva\n\n**User Management:**\n- `frontend/src/views/user/Profile.vue` - Gestione profilo\n- `frontend/src/views/user/Settings.vue` - Impostazioni utente\n\n**Stores e Composables:**\n- `frontend/src/stores/auth.js` - Autenticazione e sessioni\n- `frontend/src/stores/projects.js` - Store progetti con caching\n- `frontend/src/composables/usePermissions.js` - Sistema autorizzazioni\n\n### ✅ Backend Flask - API Complete\n**Modelli Dati:**\n- `backend/models.py` - Tutti i modelli (User, Project, ProjectExpense, etc.)\n\n**API Blueprints:**\n- `backend/blueprints/api/auth.py` - Autenticazione + profilo + settings\n- `backend/blueprints/api/projects.py` - CRUD progetti completo\n- `backend/blueprints/api/expenses.py` - Gestione spese progetti\n- `backend/blueprints/api/clients.py` - Gestione clienti\n- `backend/app.py` - Registrazione blueprint e configurazione\n\n### 🔄 Legacy da Migrare - Personnel Module\n**Template Legacy da Analizzare:**\n- `backend/legacy/templates/personnel/` - Sistema HR esistente\n- `backend/legacy/routes/personnel.py` - Logica business legacy\n- `backend/legacy/templates/personnel/list.html` - Lista dipendenti\n- `backend/legacy/templates/personnel/view.html` - Dettaglio dipendente\n- `backend/legacy/templates/personnel/edit.html` - Form modifica\n\n**Modelli Personnel Esistenti:**\n- `backend/models.py` - User, UserProfile, Department (già disponibili)\n- Verificare modelli Skills, Competencies, Organization Chart\n", "modifiedCode": "# DatPortal Vue.js Migration - Status Report\n\n## Stato Corrente (Maggio 2025)\n\n### ✅ COMPLETATO - ARCHITETTURA BASE\n**Framework e Tooling:**\n- ✅ Vue.js 3 + Vite + SFC (Single File Components)\n- ✅ Vue Router 4 con layout modulari e SPA navigation\n- ✅ Tailwind CSS responsive + dark mode completo\n- ✅ Stores Pinia (auth, tenant, projects, personnel)\n- ✅ Build system ottimizzato (asset fissi, no hash)\n- ✅ Sistema autenticazione completa (login/register/reset password)\n- ✅ Sistema RBAC backend completo (5 ruoli, 25+ permessi)\n\n**Layout e UI Components:**\n- ✅ AppLayout con sidebar collassabile e responsive\n- ✅ AppHeader con breadcrumbs e quick actions\n- ✅ AppSidebar con navigazione modulare e icone SVG coerenti\n- ✅ SidebarFooter con menu utente (z-index fixed)\n- ✅ Dark mode toggle funzionante\n- ✅ Sistema di notifiche e feedback utente\n\n### ✅ COMPLETATO - MODULI BUSINESS\n\n**Dashboard Module:**\n- ✅ Dashboard.vue completa con dati PostgreSQL reali\n- ✅ KPI cards con metriche autentiche\n- ✅ Grafici Chart.js integrati\n- ✅ Sistema caching per performance\n\n**Projects Module - CRUD COMPLETO:**\n- ✅ Lista progetti (`/app/projects`) con filtri e ricerca\n- ✅ Dettaglio progetto (`/app/projects/:id`) con 7 tab funzionali\n- ✅ Creazione progetti (`/app/projects/create`) - Form completo\n- ✅ Modifica progetti (`/app/projects/:id/edit`) - Form + eliminazione\n- ✅ API DELETE progetti implementata\n- ✅ API Clienti completa per form progetti\n\n**Project View - Tab Completi:**\n- ✅ Tab Panoramica: Informazioni generali e metriche\n- ✅ Tab Task: Lista task con filtri e gestione\n- ✅ Tab Gantt: Timeline interattiva con linea \"oggi\" corretta\n- ✅ Tab Team: Gestione membri progetto\n- ✅ Tab Timesheet: Registrazione ore lavorate\n- ✅ Tab KPI & Analytics: Metriche avanzate con configurazione\n- ✅ Tab Spese: Gestione completa con tutti i campi del modello\n\n**Expenses Management - COMPLETO:**\n- ✅ ProjectExpenses.vue con visualizzazione completa\n- ✅ ExpenseModal.vue con form completo (tutti i campi del modello)\n- ✅ Campi: billing_type, status, receipt_path, categorie\n- ✅ Upload ricevute con validazione dimensione\n- ✅ Helper functions per etichette e styling\n- ✅ API expenses registrata e funzionante\n\n**User Management:**\n- ✅ Pagina Profilo (`/app/profile`) - Gestione dati personali\n- ✅ Pagina Impostazioni (`/app/settings`) - Preferenze e cambio password\n- ✅ API `/api/auth/profile`, `/api/auth/settings`, `/api/auth/change-password`\n- ✅ Menu utente funzionante con links corretti\n\n**Personnel/HR Module - SPRINT 1 COMPLETATO:**\n- ✅ PersonnelList.vue (`/app/personnel`) - Lista dipendenti isofunzionale\n- ✅ Store Pinia personnel.js - Gestione stato HR completo\n- ✅ Routing completo per tutte le sottopagine Personnel\n- ✅ Sidebar con menu Personnel e icone SVG coerenti\n- ✅ Componenti placeholder per Sprint 2 e 3\n- ✅ API Personnel complete (users, departments, skills)\n- ✅ Filtri avanzati: dipartimento, competenze, ricerca\n- ✅ Paginazione funzionante con conteggio corretto\n- ✅ Grid responsive con card dipendenti\n- ✅ Dark mode supportato completamente\n\n### ❌ DA COMPLETARE - PROSSIMI MODULI\n\n**Personnel/HR Module - SPRINT 2 (PRIORITÀ ALTA):**\n- ⏳ PersonnelProfile.vue - Dettaglio dipendente con tab (progetti, task, competenze, timesheet, CV)\n- ⏳ DepartmentList.vue - Gestione dipartimenti con gerarchia\n- ⏳ DepartmentView.vue - Dashboard dipartimento con KPI\n- ⏳ PersonnelDirectory.vue - Directory aziendale compatta\n\n**Personnel/HR Module - SPRINT 3:**\n- ⏳ PersonnelOrgChart.vue - Organigramma interattivo\n- ⏳ SkillsMatrix.vue - Matrice competenze utenti vs skills\n- ⏳ PersonnelAdmin.vue - Amministrazione HR avanzata\n\n**Admin Module:**\n- Gestione utenti e ruoli\n- Configurazioni sistema\n- Template KPI e configurazioni globali\n\n**Tasks Module Standalone:**\n- Vista kanban indipendente\n- Gestione task cross-project\n\n### ✅ PROBLEMI RISOLTI\n- ✅ Errori JavaScript assets (MIME type text/html → application/javascript)\n- ✅ API expenses registrata correttamente nell'app\n- ✅ Gantt chart linea \"oggi\" posizionata correttamente\n- ✅ Favicon aggiunto e funzionante\n- ✅ Tabella project_team duplicate definition (extend_existing=True)\n- ✅ Menu utente dropdown z-index (fixed positioning)\n- ✅ Tab Spese con tutti i campi del modello ProjectExpense\n- ✅ API `/api/auth/settings` 404 error\n- ✅ CRUD Progetti completo (Create, Read, Update, Delete)\n- ✅ Funzione `formatHours` arrotondamento decimali KPI\n- ✅ Conteggio dipendenti Personnel (pagination mapping fix)\n- ✅ Icone sidebar coerenti (rimosse emoji colorate)\n\n## Roadmap Q2 2025\n\n### ✅ FASE 1 COMPLETATA - Projects & Personnel Base\n**Projects Module:** CRUD completo, 7 tab funzionali, gestione spese\n**Personnel Sprint 1:** Lista dipendenti, filtri, paginazione, store Pinia\n\n### 🔄 FASE 2 IN CORSO - Personnel/HR Completo\n**Sprint 2:** PersonnelProfile, DepartmentList, DepartmentView, PersonnelDirectory\n**Sprint 3:** PersonnelOrgChart, SkillsMatrix, PersonnelAdmin\n\n### ⏳ FASE 3 PIANIFICATA - Admin & Tasks\n**Admin Module:** Gestione utenti, configurazioni sistema, template KPI\n**Tasks Module:** Vista kanban standalone, gestione task cross-project\n\n## File Chiave - Architettura Corrente\n\n### ✅ Frontend Vue.js - Struttura Consolidata\n**Layout e Routing:**\n- `frontend/src/App.vue` - App principale con router-view\n- `frontend/src/components/layout/AppLayout.vue` - Layout SPA con sidebar\n- `frontend/src/components/layout/AppSidebar.vue` - Navigazione modulare con icone SVG\n- `frontend/src/components/layout/SidebarIcon.vue` - Sistema icone coerente\n- `frontend/src/components/layout/SidebarNavItemCollapsible.vue` - Submenu con icone\n\n**Projects Module (COMPLETO):**\n- `frontend/src/views/projects/ProjectList.vue` - Lista progetti\n- `frontend/src/views/projects/ProjectView.vue` - Dettaglio con 7 tab\n- `frontend/src/views/projects/ProjectCreate.vue` - Creazione progetti\n- `frontend/src/views/projects/ProjectEdit.vue` - Modifica progetti\n- `frontend/src/views/projects/components/ProjectExpenses.vue` - Gestione spese\n- `frontend/src/views/projects/components/ExpenseModal.vue` - Form spese completo\n\n**Personnel Module (SPRINT 1 COMPLETO):**\n- `frontend/src/views/personnel/Personnel.vue` - Lista dipendenti isofunzionale\n- `frontend/src/stores/personnel.js` - Store Pinia completo\n- `frontend/src/views/personnel/PersonnelProfile.vue` - Placeholder Sprint 2\n- `frontend/src/views/personnel/DepartmentList.vue` - Placeholder Sprint 2\n\n**User Management:**\n- `frontend/src/views/user/Profile.vue` - Gestione profilo\n- `frontend/src/views/user/Settings.vue` - Impostazioni utente\n\n**Stores e Composables:**\n- `frontend/src/stores/auth.js` - Autenticazione e sessioni\n- `frontend/src/stores/projects.js` - Store progetti con caching\n- `frontend/src/stores/personnel.js` - Store personnel con filtri e paginazione\n- `frontend/src/composables/usePermissions.js` - Sistema autorizzazioni\n\n### ✅ Backend Flask - API Complete\n**Modelli Dati:**\n- `backend/models.py` - User, Project, ProjectExpense, Department, Skill, UserSkill\n\n**API Blueprints:**\n- `backend/blueprints/api/auth.py` - Autenticazione + profilo + settings\n- `backend/blueprints/api/projects.py` - CRUD progetti completo\n- `backend/blueprints/api/expenses.py` - Gestione spese progetti\n- `backend/blueprints/api/personnel.py` - API Personnel complete (users, departments, skills)\n- `backend/blueprints/api/clients.py` - Gestione clienti\n- `backend/app.py` - Registrazione blueprint e configurazione\n\n### ✅ Personnel Module - Migrazione Completata (Sprint 1)\n**Funzionalità Implementate:**\n- Lista dipendenti con grid responsive\n- Filtri: dipartimento, competenze, ricerca con debounce\n- Paginazione completa con conteggio corretto\n- Card dipendenti con avatar, info base, competenze\n- Dark mode supportato\n- API integration completa\n\n**Prossimi Sprint:**\n- Sprint 2: PersonnelProfile, DepartmentList, DepartmentView, PersonnelDirectory\n- Sprint 3: PersonnelOrgChart, SkillsMatrix, PersonnelAdmin\n"}
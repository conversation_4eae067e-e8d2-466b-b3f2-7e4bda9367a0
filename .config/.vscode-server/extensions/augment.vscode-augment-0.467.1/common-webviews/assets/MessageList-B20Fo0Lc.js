import{S as he,i as ye,s as we,Q as H,G as V,D as b,c as B,e as _,f as A,a4 as _e,af as ke,ab as Z,H as Me,n as q,h as k,a0 as Se,_ as Le,y as S,a2 as D,z as C,u as $,q as E,t as f,r as I,B as R,ae as oe,ag as K,a5 as X,F as ve,a6 as Ee,C as O,w as Ie,E as z}from"./SpinnerAugment-BRymMBwV.js";import{e as U,u as qe,o as be}from"./BaseButton-rKFNr-KO.js";import{G as Fe,g as Ae,t as Ne,a as je,M as He,A as Be,b as Ge,c as ze,S as Te,d as We,e as De,R as Ue,C as Pe,f as Qe,U as Je,h as Ce,E as Ke,i as Oe,j as Ve}from"./MessageListBottomButton-nvMJgZFN.js";import"./Content-CZt_q_72.js";import{R as Xe,S as Y,i as se,a as Ye,b as Ze,c as et,d as tt,e as rt,f as nt,h as ot,j as st,k as at,l as lt,E as it}from"./open-in-new-window-_CQmfLgB.js";import"./folder-Dee44ws-.js";import"./isObjectLike-CaDwJzWd.js";import{S as mt}from"./main-panel-Cc1uNcRa.js";import{aF as ut,aG as ct}from"./AugmentMessage-D6u8uU-A.js";import{s as $t}from"./types-DvVg976p.js";import"./MaterialIcon-D3827jJW.js";import"./keypress-DD1aQVr0.js";import"./autofix-state-d-ymFdyn.js";import"./Keybindings-BL1lLIIt.js";import"./pen-to-square-DsQhBKje.js";import"./exclamation-triangle-B1zowysU.js";import"./CardAugment-BpvKVhgc.js";import"./TextTooltipAugment-VmEmcMVL.js";import"./IconButtonAugment-5yqT_m78.js";import"./index-C57dba63.js";import"./augment-logo-Cb6FLr8P.js";import"./ButtonAugment-B4rD0Iq1.js";import"./expand-DqlmSj23.js";import"./folder-opened-qXv2xhk3.js";import"./diff-utils-UT8EbVpu.js";import"./toggleHighContrast-CwIv4U26.js";import"./preload-helper-Dv6uf1Os.js";import"./index-C59i2ECO.js";import"./layer-group-DxRJbFig.js";import"./github-DCBOV_oD.js";import"./types-LfaCSdmF.js";import"./globals-D0QH3NT1.js";import"./chat-types-NgqNgjwU.js";import"./test_service_pb-B6vKXZrG.js";import"./file-paths-BcSg4gks.js";import"./types-BSMhNRWH.js";import"./TextAreaAugment-DJ6NCdxI.js";import"./design-system-init-BYyh-Ygh.js";import"./StatusIndicator-bsJDs3Ra.js";import"./index-DNgdG9gK.js";import"./await_block-B6zp5aG7.js";import"./ellipsis-bUrc34Ic.js";import"./Filespan-V8rQ2geT.js";import"./lodash-xZzKttBF.js";import"./terminal-BinWa3Yp.js";import"./VSCodeCodicon-C_4gSY6s.js";import"./chat-flags-model-D7CElkH1.js";import"./mcp-logo-CaRmgfKF.js";import"./IconFilePath-DzNJjZOv.js";import"./LanguageIcon-CLfcbvMk.js";import"./next-edit-types-904A5ehg.js";import"./magnifying-glass-Dmvedn_X.js";import"./chevron-down-B8lwMNrs.js";function ae(o){let t,r;return{c(){t=V("Retrying in "),r=V(o[1])},m(e,n){_(e,t,n),_(e,r,n)},p(e,n){2&n&&Me(r,e[1])},d(e){e&&(k(t),k(r))}}}function pt(o){let t,r,e,n,s,m,u,l=o[2]&&o[1]&&ae(o);return{c(){t=H("span"),r=V(o[3]),e=b(),l&&l.c(),n=b(),s=H("button"),s.textContent="Retry now",B(t,"class","c-remote-agent-error svelte-3enrey")},m(i,a){_(i,t,a),A(t,r),A(t,e),l&&l.m(t,null),A(t,n),A(t,s),m||(u=_e(s,"click",ke(function(){Z(o[0])&&o[0].apply(this,arguments)})),m=!0)},p(i,[a]){o=i,8&a&&Me(r,o[3]),o[2]&&o[1]?l?l.p(o,a):(l=ae(o),l.c(),l.m(t,n)):l&&(l.d(1),l=null)},i:q,o:q,d(i){i&&k(t),l&&l.d(),m=!1,u()}}}function gt(o,t,r){let e,n,s,m,u,{error:l}=t,{onRetry:i}=t;function a(){u&&(u(),u=void 0),e&&(u=$t(e,c=>{r(1,m=c)}))}return Se(a),Le(()=>{u==null||u()}),o.$$set=c=>{"error"in c&&r(4,l=c.error),"onRetry"in c&&r(0,i=c.onRetry)},o.$$.update=()=>{16&o.$$.dirty&&r(5,{retryAt:e,errorMessage:n}=l,e,(r(3,n),r(4,l))),32&o.$$.dirty&&r(2,s=e!==void 0),32&o.$$.dirty&&e&&a()},[i,m,s,n,l,e]}class Re extends he{constructor(t){super(),ye(this,t,gt,pt,we,{error:4,onRetry:0})}}function le(o,t,r){const e=o.slice();e[36]=t[r],e[39]=r;const n=e[39]+1===e[11].length;return e[37]=n,e}function ie(o,t,r){const e=o.slice();e[40]=t[r].turn,e[41]=t[r].idx;const n=e[41]+1===e[12].length;return e[42]=n,e}function me(o){let t,r;return t=new Be({}),{c(){S(t.$$.fragment)},m(e,n){C(t,e,n),r=!0},i(e){r||($(t.$$.fragment,e),r=!0)},o(e){f(t.$$.fragment,e),r=!1},d(e){R(t,e)}}}function ft(o){let t,r,e,n;const s=[xt,Rt],m=[];function u(l,i){return l[16].enableRichCheckpointInfo?0:1}return t=u(o),r=m[t]=s[t](o),{c(){r.c(),e=z()},m(l,i){m[t].m(l,i),_(l,e,i),n=!0},p(l,i){let a=t;t=u(l),t===a?m[t].p(l,i):(E(),f(m[a],1,1,()=>{m[a]=null}),I(),r=m[t],r?r.p(l,i):(r=m[t]=s[t](l),r.c()),$(r,1),r.m(e.parentNode,e))},i(l){n||($(r),n=!0)},o(l){f(r),n=!1},d(l){l&&k(e),m[t].d(l)}}}function dt(o){let t,r;return t=new Pe({props:{group:o[36],chatModel:o[1],turn:o[40],turnIndex:o[41],isLastTurn:o[42],messageListContainer:o[0]}}),{c(){S(t.$$.fragment)},m(e,n){C(t,e,n),r=!0},p(e,n){const s={};2048&n[0]&&(s.group=e[36]),2&n[0]&&(s.chatModel=e[1]),2048&n[0]&&(s.turn=e[40]),2048&n[0]&&(s.turnIndex=e[41]),6144&n[0]&&(s.isLastTurn=e[42]),1&n[0]&&(s.messageListContainer=e[0]),t.$set(s)},i(e){r||($(t.$$.fragment,e),r=!0)},o(e){f(t.$$.fragment,e),r=!1},d(e){R(t,e)}}}function ht(o){let t,r;return t=new Qe({props:{stage:o[40].stage,iterationId:o[40].iterationId,stageCount:o[40].stageCount}}),{c(){S(t.$$.fragment)},m(e,n){C(t,e,n),r=!0},p(e,n){const s={};2048&n[0]&&(s.stage=e[40].stage),2048&n[0]&&(s.iterationId=e[40].iterationId),2048&n[0]&&(s.stageCount=e[40].stageCount),t.$set(s)},i(e){r||($(t.$$.fragment,e),r=!0)},o(e){f(t.$$.fragment,e),r=!1},d(e){R(t,e)}}}function yt(o){let t,r;return t=new Je({props:{chatModel:o[1],msg:o[40].response_text??""}}),{c(){S(t.$$.fragment)},m(e,n){C(t,e,n),r=!0},p(e,n){const s={};2&n[0]&&(s.chatModel=e[1]),2048&n[0]&&(s.msg=e[40].response_text??""),t.$set(s)},i(e){r||($(t.$$.fragment,e),r=!0)},o(e){f(t.$$.fragment,e),r=!1},d(e){R(t,e)}}}function wt(o){let t,r;return t=new ut({props:{group:o[36],markdown:o[40].response_text??"",messageListContainer:o[0]}}),{c(){S(t.$$.fragment)},m(e,n){C(t,e,n),r=!0},p(e,n){const s={};2048&n[0]&&(s.group=e[36]),2048&n[0]&&(s.markdown=e[40].response_text??""),1&n[0]&&(s.messageListContainer=e[0]),t.$set(s)},i(e){r||($(t.$$.fragment,e),r=!0)},o(e){f(t.$$.fragment,e),r=!1},d(e){R(t,e)}}}function Mt(o){let t,r;function e(){return o[29](o[40])}return t=new Ce({props:{turn:o[40],preamble:mt,resendTurn:e,$$slots:{default:[_t]},$$scope:{ctx:o}}}),{c(){S(t.$$.fragment)},m(n,s){C(t,n,s),r=!0},p(n,s){o=n;const m={};2048&s[0]&&(m.turn=o[40]),2052&s[0]&&(m.resendTurn=e),34816&s[0]|16384&s[1]&&(m.$$scope={dirty:s,ctx:o}),t.$set(m)},i(n){r||($(t.$$.fragment,n),r=!0)},o(n){f(t.$$.fragment,n),r=!1},d(n){R(t,n)}}}function St(o){let t,r;return t=new Ke({props:{flagsModel:o[13],turn:o[40]}}),{c(){S(t.$$.fragment)},m(e,n){C(t,e,n),r=!0},p(e,n){const s={};8192&n[0]&&(s.flagsModel=e[13]),2048&n[0]&&(s.turn=e[40]),t.$set(s)},i(e){r||($(t.$$.fragment,e),r=!0)},o(e){f(t.$$.fragment,e),r=!1},d(e){R(t,e)}}}function Ct(o){let t,r;return t=new Ce({props:{turn:o[40]}}),{c(){S(t.$$.fragment)},m(e,n){C(t,e,n),r=!0},p(e,n){const s={};2048&n[0]&&(s.turn=e[40]),t.$set(s)},i(e){r||($(t.$$.fragment,e),r=!0)},o(e){f(t.$$.fragment,e),r=!1},d(e){R(t,e)}}}function Rt(o){let t,r;return t=new Oe({props:{turn:o[40]}}),{c(){S(t.$$.fragment)},m(e,n){C(t,e,n),r=!0},p(e,n){const s={};2048&n[0]&&(s.turn=e[40]),t.$set(s)},i(e){r||($(t.$$.fragment,e),r=!0)},o(e){f(t.$$.fragment,e),r=!1},d(e){R(t,e)}}}function xt(o){let t,r;return t=new Ve({props:{turn:o[40]}}),{c(){S(t.$$.fragment)},m(e,n){C(t,e,n),r=!0},p(e,n){const s={};2048&n[0]&&(s.turn=e[40]),t.$set(s)},i(e){r||($(t.$$.fragment,e),r=!0)},o(e){f(t.$$.fragment,e),r=!1},d(e){R(t,e)}}}function _t(o){let t,r;return t=new ct({props:{conversationModel:o[15],turn:o[40]}}),{c(){S(t.$$.fragment)},m(e,n){C(t,e,n),r=!0},p(e,n){const s={};32768&n[0]&&(s.conversationModel=e[15]),2048&n[0]&&(s.turn=e[40]),t.$set(s)},i(e){r||($(t.$$.fragment,e),r=!0)},o(e){f(t.$$.fragment,e),r=!1},d(e){R(t,e)}}}function ue(o){let t,r,e,n;function s(){return o[30](o[40])}return{c(){t=H("div"),B(t,"class","c-msg-list__turn-seen")},m(m,u){_(m,t,u),e||(n=X(r=ze.call(null,t,{onSeen:s,track:o[40].seen_state!==Y.seen})),e=!0)},p(m,u){o=m,r&&Z(r.update)&&2048&u[0]&&r.update.call(null,{onSeen:s,track:o[40].seen_state!==Y.seen})},d(m){m&&k(t),e=!1,n()}}}function ce(o,t){let r,e,n,s,m,u,l,i,a,c,p,g,w,h,v=se(t[40]);const N=[Ct,St,Mt,wt,yt,ht,dt,ft],L=[];function G(y,M){return 2048&M[0]&&(e=null),2048&M[0]&&(n=null),2048&M[0]&&(s=null),2048&M[0]&&(m=null),2048&M[0]&&(u=null),2048&M[0]&&(l=null),2048&M[0]&&(i=null),2048&M[0]&&(a=null),e==null&&(e=!!Ye(y[40])),e?0:(n==null&&(n=!!Ze(y[40])),n?1:(s==null&&(s=!!et(y[40])),s?2:(m==null&&(m=!!tt(y[40])),m?3:(u==null&&(u=!!rt(y[40])),u?4:(l==null&&(l=!!nt(y[40])),l?5:(i==null&&(i=!!(ot(y[40])||st(y[40])||at(y[40]))),i?6:(a==null&&(a=!(!lt(y[40])||y[40].status!==it.success)),a?7:-1)))))))}~(c=G(t,[-1,-1]))&&(p=L[c]=N[c](t));let x=v&&ue(t);return{key:o,first:null,c(){r=z(),p&&p.c(),g=b(),x&&x.c(),w=z(),this.first=r},m(y,M){_(y,r,M),~c&&L[c].m(y,M),_(y,g,M),x&&x.m(y,M),_(y,w,M),h=!0},p(y,M){let F=c;c=G(t=y,M),c===F?~c&&L[c].p(t,M):(p&&(E(),f(L[F],1,1,()=>{L[F]=null}),I()),~c?(p=L[c],p?p.p(t,M):(p=L[c]=N[c](t),p.c()),$(p,1),p.m(g.parentNode,g)):p=null),2048&M[0]&&(v=se(t[40])),v?x?x.p(t,M):(x=ue(t),x.c(),x.m(w.parentNode,w)):x&&(x.d(1),x=null)},i(y){h||($(p),h=!0)},o(y){f(p),h=!1},d(y){y&&(k(r),k(g),k(w)),~c&&L[c].d(y),x&&x.d(y)}}}function $e(o){let t,r,e,n,s;const m=[It,Et,vt,Lt,kt],u=[];function l(a,c){return a[8]?0:a[5].retryMessage?1:a[5].showGeneratingResponse?2:a[5].showAwaitingUserInput?3:a[5].showStopped?4:-1}~(t=l(o))&&(r=u[t]=m[t](o));let i=o[5].showRunningSpacer&&pe();return{c(){r&&r.c(),e=b(),i&&i.c(),n=z()},m(a,c){~t&&u[t].m(a,c),_(a,e,c),i&&i.m(a,c),_(a,n,c),s=!0},p(a,c){let p=t;t=l(a),t===p?~t&&u[t].p(a,c):(r&&(E(),f(u[p],1,1,()=>{u[p]=null}),I()),~t?(r=u[t],r?r.p(a,c):(r=u[t]=m[t](a),r.c()),$(r,1),r.m(e.parentNode,e)):r=null),a[5].showRunningSpacer?i||(i=pe(),i.c(),i.m(n.parentNode,n)):i&&(i.d(1),i=null)},i(a){s||($(r),s=!0)},o(a){f(r),s=!1},d(a){a&&(k(e),k(n)),~t&&u[t].d(a),i&&i.d(a)}}}function kt(o){let t,r;return t=new Te({}),{c(){S(t.$$.fragment)},m(e,n){C(t,e,n),r=!0},p:q,i(e){r||($(t.$$.fragment,e),r=!0)},o(e){f(t.$$.fragment,e),r=!1},d(e){R(t,e)}}}function Lt(o){let t,r;return t=new We({}),{c(){S(t.$$.fragment)},m(e,n){C(t,e,n),r=!0},p:q,i(e){r||($(t.$$.fragment,e),r=!0)},o(e){f(t.$$.fragment,e),r=!1},d(e){R(t,e)}}}function vt(o){let t,r;return t=new De({}),{c(){S(t.$$.fragment)},m(e,n){C(t,e,n),r=!0},p:q,i(e){r||($(t.$$.fragment,e),r=!0)},o(e){f(t.$$.fragment,e),r=!1},d(e){R(t,e)}}}function Et(o){let t,r;return t=new Ue({props:{message:o[5].retryMessage}}),{c(){S(t.$$.fragment)},m(e,n){C(t,e,n),r=!0},p(e,n){const s={};32&n[0]&&(s.message=e[5].retryMessage),t.$set(s)},i(e){r||($(t.$$.fragment,e),r=!0)},o(e){f(t.$$.fragment,e),r=!1},d(e){R(t,e)}}}function It(o){let t,r;return t=new Re({props:{error:o[8].error,onRetry:o[8].onRetry}}),{c(){S(t.$$.fragment)},m(e,n){C(t,e,n),r=!0},p(e,n){const s={};256&n[0]&&(s.error=e[8].error),256&n[0]&&(s.onRetry=e[8].onRetry),t.$set(s)},i(e){r||($(t.$$.fragment,e),r=!0)},o(e){f(t.$$.fragment,e),r=!1},d(e){R(t,e)}}}function pe(o){let t;return{c(){t=H("div"),B(t,"class","c-agent-running-spacer svelte-t9khzq")},m(r,e){_(r,t,e)},d(r){r&&k(t)}}}function qt(o){let t,r,e,n=[],s=new Map,m=U(o[36]);const u=i=>i[40].request_id??`no-request-id-${i[41]}`;for(let i=0;i<m.length;i+=1){let a=ie(o,m,i),c=u(a);s.set(c,n[i]=ce(c,a))}let l=o[37]&&$e(o);return{c(){for(let i=0;i<n.length;i+=1)n[i].c();t=b(),l&&l.c(),r=z()},m(i,a){for(let c=0;c<n.length;c+=1)n[c]&&n[c].m(i,a);_(i,t,a),l&&l.m(i,a),_(i,r,a),e=!0},p(i,a){8501255&a[0]&&(m=U(i[36]),E(),n=qe(n,a,u,1,i,m,s,t.parentNode,be,ce,t,ie),I()),i[37]?l?(l.p(i,a),2048&a[0]&&$(l,1)):(l=$e(i),l.c(),$(l,1),l.m(r.parentNode,r)):l&&(E(),f(l,1,1,()=>{l=null}),I())},i(i){if(!e){for(let a=0;a<m.length;a+=1)$(n[a]);$(l),e=!0}},o(i){for(let a=0;a<n.length;a+=1)f(n[a]);f(l),e=!1},d(i){i&&(k(t),k(r));for(let a=0;a<n.length;a+=1)n[a].d(i);l&&l.d(i)}}}function ge(o){let t,r;return t=new Ge({props:{class:"c-msg-list__item--grouped",chatModel:o[1],isLastItem:o[37],userControlsScroll:o[3],requestId:o[36][0].turn.request_id,releaseScroll:o[31],messageListContainer:o[0],minHeight:o[37]?o[7]:0,$$slots:{default:[qt]},$$scope:{ctx:o}}}),{c(){S(t.$$.fragment)},m(e,n){C(t,e,n),r=!0},p(e,n){const s={};2&n[0]&&(s.chatModel=e[1]),2048&n[0]&&(s.isLastItem=e[37]),8&n[0]&&(s.userControlsScroll=e[3]),2048&n[0]&&(s.requestId=e[36][0].turn.request_id),8&n[0]&&(s.releaseScroll=e[31]),1&n[0]&&(s.messageListContainer=e[0]),2176&n[0]&&(s.minHeight=e[37]?e[7]:0),112935&n[0]|16384&n[1]&&(s.$$scope={dirty:n,ctx:e}),t.$set(s)},i(e){r||($(t.$$.fragment,e),r=!0)},o(e){f(t.$$.fragment,e),r=!1},d(e){R(t,e)}}}function fe(o){let t,r;return t=new Re({props:{error:o[8].error,onRetry:o[8].onRetry}}),{c(){S(t.$$.fragment)},m(e,n){C(t,e,n),r=!0},p(e,n){const s={};256&n[0]&&(s.error=e[8].error),256&n[0]&&(s.onRetry=e[8].onRetry),t.$set(s)},i(e){r||($(t.$$.fragment,e),r=!0)},o(e){f(t.$$.fragment,e),r=!1},d(e){R(t,e)}}}function bt(o){let t,r,e,n,s,m,u,l=o[9]&&me(),i=U(o[11]),a=[];for(let g=0;g<i.length;g+=1)a[g]=ge(le(o,i,g));const c=g=>f(a[g],1,1,()=>{a[g]=null});let p=!o[12].length&&o[8]&&fe(o);return{c(){t=H("div"),l&&l.c(),r=b();for(let g=0;g<a.length;g+=1)a[g].c();e=b(),p&&p.c(),B(t,"class","c-msg-list svelte-t9khzq"),D(t,"c-msg-list--minimal",!o[16].fullFeatured)},m(g,w){_(g,t,w),l&&l.m(t,null),A(t,r);for(let h=0;h<a.length;h+=1)a[h]&&a[h].m(t,null);A(t,e),p&&p.m(t,null),o[32](t),s=!0,m||(u=[X(Ne.call(null,t,{onScrollIntoBottom:o[20],onScrollAwayFromBottom:o[21],onScroll:o[33]})),X(n=je.call(null,t,{onHeightChange:o[34]}))],m=!0)},p(g,w){if(g[9]?l?512&w[0]&&$(l,1):(l=me(),l.c(),$(l,1),l.m(t,r)):l&&(E(),f(l,1,1,()=>{l=null}),I()),8501679&w[0]){let h;for(i=U(g[11]),h=0;h<i.length;h+=1){const v=le(g,i,h);a[h]?(a[h].p(v,w),$(a[h],1)):(a[h]=ge(v),a[h].c(),$(a[h],1),a[h].m(t,e))}for(E(),h=i.length;h<a.length;h+=1)c(h);I()}!g[12].length&&g[8]?p?(p.p(g,w),4352&w[0]&&$(p,1)):(p=fe(g),p.c(),$(p,1),p.m(t,null)):p&&(E(),f(p,1,1,()=>{p=null}),I()),n&&Z(n.update)&&16&w[0]&&n.update.call(null,{onHeightChange:g[34]}),(!s||65536&w[0])&&D(t,"c-msg-list--minimal",!g[16].fullFeatured)},i(g){if(!s){$(l);for(let w=0;w<i.length;w+=1)$(a[w]);$(p),s=!0}},o(g){f(l),a=a.filter(Boolean);for(let w=0;w<a.length;w+=1)f(a[w]);f(p),s=!1},d(g){g&&k(t),l&&l.d(),ve(a,g),p&&p.d(),o[32](null),m=!1,Ee(u)}}}function de(o){let t,r;return t=new He({props:{messageListElement:o[0],showScrollDown:o[6]}}),{c(){S(t.$$.fragment)},m(e,n){C(t,e,n),r=!0},p(e,n){const s={};1&n[0]&&(s.messageListElement=e[0]),64&n[0]&&(s.showScrollDown=e[6]),t.$set(s)},i(e){r||($(t.$$.fragment,e),r=!0)},o(e){f(t.$$.fragment,e),r=!1},d(e){R(t,e)}}}function Ft(o){let t,r,e,n;r=new Fe({props:{$$slots:{default:[bt]},$$scope:{ctx:o}}});let s=o[10]&&de(o);return{c(){t=H("div"),S(r.$$.fragment),e=b(),s&&s.c(),B(t,"class","c-msg-list-container svelte-t9khzq"),B(t,"data-testid","chat-message-list"),D(t,"c-msg-list--minimal",!o[16].fullFeatured)},m(m,u){_(m,t,u),C(r,t,null),A(t,e),s&&s.m(t,null),n=!0},p(m,u){const l={};113599&u[0]|16384&u[1]&&(l.$$scope={dirty:u,ctx:m}),r.$set(l),m[10]?s?(s.p(m,u),1024&u[0]&&$(s,1)):(s=de(m),s.c(),$(s,1),s.m(t,null)):s&&(E(),f(s,1,1,()=>{s=null}),I()),(!n||65536&u[0])&&D(t,"c-msg-list--minimal",!m[16].fullFeatured)},i(m){n||($(r.$$.fragment,m),$(s),n=!0)},o(m){f(r.$$.fragment,m),f(s),n=!1},d(m){m&&k(t),R(r),s&&s.d()}}}function At(o,t,r){let e,n,s,m,u,l,i,a,c,p,g,w,h,v,N,L,G,x=q,y=q,M=()=>(y(),y=O(T,d=>r(28,L=d)),T),F=q;o.$$.on_destroy.push(()=>x()),o.$$.on_destroy.push(()=>y()),o.$$.on_destroy.push(()=>F());let{chatModel:T}=t;M();let{onboardingWorkspaceModel:P}=t,{msgListElement:W}=t;const xe=oe("agentConversationModel"),{agentExchangeStatus:ee,isCurrConversationAgentic:te}=xe;K(o,ee,d=>r(27,N=d)),K(o,te,d=>r(26,v=d));const re=oe(Xe.key);K(o,re,d=>r(25,h=d));let j=!1;function Q(){r(3,j=!0)}Se(()=>{var d;((d=w.lastExchange)==null?void 0:d.seen_state)===Y.unseen&&Q()});let J=0;const ne=d=>w.markSeen(d);return o.$$set=d=>{"chatModel"in d&&M(r(1,T=d.chatModel)),"onboardingWorkspaceModel"in d&&r(2,P=d.onboardingWorkspaceModel),"msgListElement"in d&&r(0,W=d.msgListElement)},o.$$.update=()=>{268435456&o.$$.dirty[0]&&(r(14,e=L.currentConversationModel),x(),x=O(e,d=>r(15,w=d))),268435456&o.$$.dirty[0]&&(r(13,n=L.flags),F(),F=O(n,d=>r(16,G=d))),503316480&o.$$.dirty[0]&&r(24,s=Ae(L,N,v,h)),16777216&o.$$.dirty[0]&&r(12,m=s.chatHistory),16777216&o.$$.dirty[0]&&r(11,u=s.groupedChatHistory),16777216&o.$$.dirty[0]&&r(5,l=s.lastGroupConfig),16777216&o.$$.dirty[0]&&r(10,i=s.doShowFloatingButtons),16777216&o.$$.dirty[0]&&r(9,a=s.doShowAgentSetupLogs),32&o.$$.dirty[0]&&r(8,c=l.remoteAgentErrorConfig),16&o.$$.dirty[0]&&r(7,p=J),8&o.$$.dirty[0]&&r(6,g=j)},[W,T,P,j,J,l,g,p,c,a,i,u,m,n,e,w,G,ee,te,re,function(){r(3,j=!1)},function(){r(3,j=!0)},Q,ne,s,h,v,N,L,d=>P.retryProjectSummary(d),d=>ne(d),()=>r(3,j=!0),function(d){Ie[d?"unshift":"push"](()=>{W=d,r(0,W)})},d=>{d<=1&&Q()},d=>r(4,J=d)]}class Ar extends he{constructor(t){super(),ye(this,t,At,Ft,we,{chatModel:1,onboardingWorkspaceModel:2,msgListElement:0},null,[-1,-1])}}export{Ar as default};

{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/personnel/components/DepartmentNode.vue"}, "modifiedCode": "<template>\n  <div class=\"department-node\">\n    <!-- Department Header -->\n    <div class=\"department-header bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm p-4 mb-4 min-w-80\">\n      <div class=\"flex items-center justify-between\">\n        <div class=\"flex-1\">\n          <div class=\"flex items-center\">\n            <button v-if=\"department.subdepartments.length > 0\"\n                    @click=\"toggleExpansion\"\n                    class=\"mr-2 p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\">\n              <svg :class=\"[\n                'w-4 h-4 text-gray-500 transition-transform',\n                isExpanded ? 'transform rotate-90' : ''\n              ]\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fill-rule=\"evenodd\" d=\"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\" clip-rule=\"evenodd\"></path>\n              </svg>\n            </button>\n            \n            <div class=\"flex items-center\">\n              <div class=\"w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mr-3\">\n                <svg class=\"w-6 h-6 text-blue-600 dark:text-blue-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fill-rule=\"evenodd\" d=\"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z\" clip-rule=\"evenodd\"></path>\n                </svg>\n              </div>\n              \n              <div>\n                <h3 class=\"text-lg font-semibold text-gray-900 dark:text-white\">{{ department.name }}</h3>\n                <p v-if=\"department.description\" class=\"text-sm text-gray-500 dark:text-gray-400\">{{ department.description }}</p>\n                <div class=\"flex items-center mt-1 space-x-4 text-xs text-gray-500 dark:text-gray-400\">\n                  <span>{{ department.employee_count }} dipendenti</span>\n                  <span v-if=\"department.budget > 0\">Budget: €{{ formatCurrency(department.budget) }}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n        \n        <!-- Manager Info -->\n        <div v-if=\"department.manager\" class=\"ml-4 flex items-center\">\n          <div class=\"text-right mr-3\">\n            <p class=\"text-sm font-medium text-gray-900 dark:text-white\">{{ department.manager.full_name }}</p>\n            <p class=\"text-xs text-gray-500 dark:text-gray-400\">Manager</p>\n          </div>\n          <div class=\"w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center\">\n            <svg class=\"w-4 h-4 text-purple-600 dark:text-purple-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fill-rule=\"evenodd\" d=\"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\" clip-rule=\"evenodd\"></path>\n            </svg>\n          </div>\n        </div>\n      </div>\n      \n      <!-- Employees -->\n      <div v-if=\"department.employees.length > 0\" class=\"mt-4 pt-4 border-t border-gray-200 dark:border-gray-700\">\n        <div class=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3\">\n          <div v-for=\"employee in visibleEmployees\" \n               :key=\"employee.id\"\n               @click=\"$emit('employee-click', employee)\"\n               class=\"flex items-center p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors\">\n            <div class=\"w-8 h-8 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center mr-3\">\n              <svg class=\"w-4 h-4 text-gray-500 dark:text-gray-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fill-rule=\"evenodd\" d=\"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\" clip-rule=\"evenodd\"></path>\n              </svg>\n            </div>\n            <div class=\"flex-1 min-w-0\">\n              <p class=\"text-sm font-medium text-gray-900 dark:text-white truncate\">{{ employee.full_name }}</p>\n              <p class=\"text-xs text-gray-500 dark:text-gray-400 truncate\">{{ employee.position || 'Dipendente' }}</p>\n            </div>\n            <div v-if=\"employee.is_manager\" class=\"ml-2\">\n              <span class=\"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200\">\n                Manager\n              </span>\n            </div>\n          </div>\n        </div>\n        \n        <!-- Show More/Less Button -->\n        <div v-if=\"department.employees.length > maxVisibleEmployees\" class=\"mt-3 text-center\">\n          <button @click=\"showAllEmployees = !showAllEmployees\"\n                  class=\"text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300\">\n            {{ showAllEmployees ? 'Mostra meno' : `Mostra altri ${department.employees.length - maxVisibleEmployees}` }}\n          </button>\n        </div>\n      </div>\n    </div>\n    \n    <!-- Subdepartments -->\n    <div v-if=\"isExpanded && department.subdepartments.length > 0\" class=\"ml-8 space-y-4\">\n      <div class=\"relative\">\n        <!-- Connection Lines -->\n        <div class=\"absolute -left-4 top-0 bottom-0 w-px bg-gray-300 dark:bg-gray-600\"></div>\n        <div class=\"absolute -left-4 top-6 w-4 h-px bg-gray-300 dark:bg-gray-600\"></div>\n        \n        <DepartmentNode \n          v-for=\"subdept in department.subdepartments\" \n          :key=\"subdept.id\"\n          :department=\"subdept\"\n          :expanded=\"expanded\"\n          :search-query=\"searchQuery\"\n          @toggle-node=\"$emit('toggle-node', $event)\"\n          @employee-click=\"$emit('employee-click', $event)\"\n        />\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed } from 'vue'\n\n// Props\nconst props = defineProps({\n  department: {\n    type: Object,\n    required: true\n  },\n  expanded: {\n    type: Set,\n    required: true\n  },\n  searchQuery: {\n    type: String,\n    default: ''\n  }\n})\n\n// Emits\nconst emit = defineEmits(['toggle-node', 'employee-click'])\n\n// Reactive state\nconst showAllEmployees = ref(false)\nconst maxVisibleEmployees = 6\n\n// Computed properties\nconst isExpanded = computed(() => props.expanded.has(props.department.id))\n\nconst visibleEmployees = computed(() => {\n  if (showAllEmployees.value || props.department.employees.length <= maxVisibleEmployees) {\n    return props.department.employees\n  }\n  return props.department.employees.slice(0, maxVisibleEmployees)\n})\n\n// Methods\nconst toggleExpansion = () => {\n  emit('toggle-node', props.department.id)\n}\n\nconst formatCurrency = (amount) => {\n  return new Intl.NumberFormat('it-IT').format(amount)\n}\n</script>\n\n<style scoped>\n.department-node {\n  position: relative;\n}\n\n.orgchart-tree {\n  font-family: inherit;\n}\n</style>\n"}
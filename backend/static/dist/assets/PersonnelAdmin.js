import{r as f,f as T,w as ae,A as L,c as l,o,j as e,v as x,x as h,H as B,F as z,k as M,t as c,g as b,n as V,a as D,i as P,b as A,m as w,I,z as S,s as Q,C as oe,h as le}from"./vendor.js";import{_ as E}from"./app.js";const ne={class:"space-y-6"},de={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0"},ie={class:"flex-1 max-w-lg"},ue={class:"relative"},me={class:"flex items-center space-x-3"},ce=["value"],ge=["disabled"],pe={key:0,class:"animate-spin w-4 h-4",fill:"none",viewBox:"0 0 24 24"},ve={key:1,class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},xe={key:0,class:"flex justify-center items-center h-64"},ye={key:1,class:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4"},be={class:"flex"},fe={class:"text-sm text-red-700 dark:text-red-300 mt-1"},ke={key:2,class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},he={class:"overflow-x-auto"},we={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},_e={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},$e={class:"px-6 py-4 whitespace-nowrap"},Ce={class:"flex items-center"},ze={class:"flex-shrink-0 w-10 h-10"},Me={class:"w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center"},je=["src","alt"],Ve={key:1,class:"w-5 h-5 text-gray-600 dark:text-gray-300",fill:"currentColor",viewBox:"0 0 20 20"},De={class:"ml-4"},Be={class:"text-sm font-medium text-gray-900 dark:text-white"},Ue={class:"text-sm text-gray-500 dark:text-gray-400"},He={key:0,class:"text-xs text-gray-400 dark:text-gray-500"},Te={class:"px-6 py-4 whitespace-nowrap"},Ee={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},Se={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"},Le={class:"space-y-1"},Pe={key:0},Ae={key:1},Ie={class:"px-6 py-4 whitespace-nowrap"},Re={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},Ne={class:"flex items-center justify-end space-x-2"},Oe=["onClick"],Ge=["onClick"],Fe=["onClick"],qe={key:0,class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Je={key:1,class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Qe={key:0,class:"bg-white dark:bg-gray-800 px-4 py-3 border-t border-gray-200 dark:border-gray-700 sm:px-6"},Ke={class:"flex items-center justify-between"},We={class:"flex-1 flex justify-between sm:hidden"},Xe=["disabled"],Ye=["disabled"],Ze={class:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between"},et={class:"text-sm text-gray-700 dark:text-gray-300"},tt={class:"font-medium"},rt={class:"font-medium"},st={class:"font-medium"},at={class:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px"},ot=["disabled"],lt=["onClick"],nt=["disabled"],dt={key:3,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-12 text-center"},it={class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},ut={__name:"UsersManagement",emits:["user-created","user-updated","user-deleted"],setup(p,{emit:_}){const v=_,m=f([]),k=f([]),y=f(!1),i=f(null),a=f(""),j=f(""),$=f(""),u=f(1),t=f(20),s=T(()=>{let n=m.value;if(a.value.trim()){const r=a.value.toLowerCase();n=n.filter(g=>g.full_name.toLowerCase().includes(r)||g.email.toLowerCase().includes(r)||g.position&&g.position.toLowerCase().includes(r)||g.department&&g.department.name.toLowerCase().includes(r))}return j.value&&(n=n.filter(r=>r.department_id==j.value)),$.value&&(n=n.filter(r=>r.role===$.value)),n}),C=T(()=>{const n=(u.value-1)*t.value,r=n+t.value;return s.value.slice(n,r)}),H=T(()=>Math.ceil(s.value.length/t.value)),K=T(()=>{const n=[],r=H.value,g=u.value;if(r<=7)for(let d=1;d<=r;d++)n.push(d);else if(g<=4){for(let d=1;d<=5;d++)n.push(d);n.push("..."),n.push(r)}else if(g>=r-3){n.push(1),n.push("...");for(let d=r-4;d<=r;d++)n.push(d)}else{n.push(1),n.push("...");for(let d=g-1;d<=g+1;d++)n.push(d);n.push("..."),n.push(r)}return n.filter(d=>d!=="..."||n.indexOf(d)===n.lastIndexOf(d))}),W=T(()=>a.value.trim()!==""||j.value!==""||$.value!==""),R=async()=>{y.value=!0,i.value=null;try{const n=await fetch("/api/personnel/users",{credentials:"include"});if(!n.ok)throw new Error(`HTTP ${n.status}: ${n.statusText}`);const r=await n.json();if(r.success)m.value=r.data.users||[];else throw new Error(r.message||"Errore nel caricamento utenti")}catch(n){console.error("Error loading users:",n),i.value=n.message}finally{y.value=!1}},X=async()=>{try{const n=await fetch("/api/personnel/departments",{credentials:"include"});if(!n.ok)throw new Error(`HTTP ${n.status}: ${n.statusText}`);const r=await n.json();r.success&&(k.value=r.data.departments||[])}catch(n){console.error("Error loading departments:",n)}},Y=n=>{console.log("Edit user:",n)},Z=async n=>{if(confirm(`Sei sicuro di voler resettare la password di ${n.full_name}?`))try{const r=await fetch(`/api/personnel/admin/users/${n.id}/reset-password`,{method:"POST",credentials:"include"});if(!r.ok)throw new Error(`HTTP ${r.status}: ${r.statusText}`);const g=await r.json();if(g.success)alert(`Password resettata per ${n.full_name}. Nuova password temporanea: ${g.data.temporary_password}`);else throw new Error(g.message||"Errore nel reset password")}catch(r){console.error("Error resetting password:",r),alert("Errore nel reset della password: "+r.message)}},ee=async n=>{const r=n.is_active?"disattivare":"riattivare";if(confirm(`Sei sicuro di voler ${r} ${n.full_name}?`))try{if(n.is_active){const g=await fetch(`/api/personnel/admin/users/${n.id}`,{method:"DELETE",credentials:"include"});if(!g.ok)throw new Error(`HTTP ${g.status}: ${g.statusText}`);(await g.json()).success&&(n.is_active=!1,v("user-deleted",n))}else{const g=await fetch(`/api/personnel/users/${n.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({is_active:!0})});if(!g.ok)throw new Error(`HTTP ${g.status}: ${g.statusText}`);(await g.json()).success&&(n.is_active=!0,v("user-updated",n))}}catch(g){console.error("Error toggling user status:",g),alert("Errore nell'operazione: "+g.message)}},N=()=>{u.value>1&&u.value--},O=()=>{u.value<H.value&&u.value++},te=n=>({admin:"Admin",manager:"Manager",employee:"Dipendente"})[n]||n,re=n=>({full_time:"Tempo pieno",part_time:"Part-time",contractor:"Consulente",intern:"Stagista"})[n]||n,G=n=>n?new Date(n).toLocaleDateString("it-IT",{year:"numeric",month:"short",day:"numeric"}):"",se=n=>{if(!n)return!1;const r=new Date,d=new Date(n)-r,U=Math.ceil(d/(1e3*60*60*24));return U<=90&&U>=0};return ae([a,j,$],()=>{u.value=1}),L(async()=>{await Promise.all([R(),X()])}),(n,r)=>{const g=A("router-link");return o(),l("div",ne,[e("div",de,[e("div",ie,[e("div",ue,[r[3]||(r[3]=e("svg",{class:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1)),x(e("input",{"onUpdate:modelValue":r[0]||(r[0]=d=>a.value=d),type:"text",placeholder:"Cerca dipendenti...",class:"pl-10 pr-4 py-2 w-full border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[h,a.value]])])]),e("div",me,[x(e("select",{"onUpdate:modelValue":r[1]||(r[1]=d=>j.value=d),class:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},[r[4]||(r[4]=e("option",{value:""},"Tutti i dipartimenti",-1)),(o(!0),l(z,null,M(k.value,d=>(o(),l("option",{key:d.id,value:d.id},c(d.name),9,ce))),128))],512),[[B,j.value]]),x(e("select",{"onUpdate:modelValue":r[2]||(r[2]=d=>$.value=d),class:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},r[5]||(r[5]=[e("option",{value:""},"Tutti i ruoli",-1),e("option",{value:"admin"},"Admin",-1),e("option",{value:"manager"},"Manager",-1),e("option",{value:"employee"},"Dipendente",-1)]),512),[[B,$.value]]),e("button",{onClick:R,disabled:y.value,class:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"},[y.value?(o(),l("svg",pe,r[6]||(r[6]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):(o(),l("svg",ve,r[7]||(r[7]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"},null,-1)])))],8,ge)])]),y.value&&!m.value.length?(o(),l("div",xe,r[8]||(r[8]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):i.value?(o(),l("div",ye,[e("div",be,[r[10]||(r[10]=e("svg",{class:"w-5 h-5 text-red-400 mr-2 mt-0.5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z","clip-rule":"evenodd"})],-1)),e("div",null,[r[9]||(r[9]=e("h3",{class:"text-sm font-medium text-red-800 dark:text-red-200"},"Errore nel caricamento",-1)),e("p",fe,c(i.value),1)])])])):m.value.length>0?(o(),l("div",ke,[e("div",he,[e("table",we,[r[17]||(r[17]=e("thead",{class:"bg-gray-50 dark:bg-gray-700"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Dipendente "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Ruolo "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Dipartimento "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Contratto "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Stato "),e("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Azioni ")])],-1)),e("tbody",_e,[(o(!0),l(z,null,M(C.value,d=>{var U,F,q;return o(),l("tr",{key:d.id,class:"hover:bg-gray-50 dark:hover:bg-gray-700"},[e("td",$e,[e("div",Ce,[e("div",ze,[e("div",Me,[d.profile_image?(o(),l("img",{key:0,src:d.profile_image,alt:d.full_name,class:"w-10 h-10 rounded-full object-cover"},null,8,je)):(o(),l("svg",Ve,r[11]||(r[11]=[e("path",{"fill-rule":"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z","clip-rule":"evenodd"},null,-1)])))])]),e("div",De,[e("div",Be,c(d.full_name),1),e("div",Ue,c(d.email),1),d.position?(o(),l("div",He,c(d.position),1)):b("",!0)])])]),e("td",Te,[e("span",{class:V(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",d.role==="admin"?"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200":d.role==="manager"?"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200":"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"])},c(te(d.role)),3)]),e("td",Ee,c(((U=d.department)==null?void 0:U.name)||"Nessun dipartimento"),1),e("td",Se,[e("div",Le,[d.hire_date?(o(),l("div",Pe," Assunto: "+c(G(d.hire_date)),1)):b("",!0),(F=d.profile)!=null&&F.employment_type?(o(),l("div",Ae,c(re(d.profile.employment_type)),1)):b("",!0),(q=d.profile)!=null&&q.contract_end_date?(o(),l("div",{key:2,class:V(se(d.profile.contract_end_date)?"text-red-600 dark:text-red-400 font-medium":"")}," Scade: "+c(G(d.profile.contract_end_date)),3)):b("",!0)])]),e("td",Ie,[e("span",{class:V(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",d.is_active?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200":"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"])},c(d.is_active?"Attivo":"Disattivato"),3)]),e("td",Re,[e("div",Ne,[D(g,{to:`/app/personnel/${d.id}`,class:"text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300"},{default:P(()=>r[12]||(r[12]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})],-1)])),_:2,__:[12]},1032,["to"]),e("button",{onClick:J=>Y(d),class:"text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300"},r[13]||(r[13]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1)]),8,Oe),e("button",{onClick:J=>Z(d),class:"text-yellow-600 dark:text-yellow-400 hover:text-yellow-900 dark:hover:text-yellow-300"},r[14]||(r[14]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"})],-1)]),8,Ge),e("button",{onClick:J=>ee(d),class:V(d.is_active?"text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300":"text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300")},[d.is_active?(o(),l("svg",qe,r[15]||(r[15]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"},null,-1)]))):(o(),l("svg",Je,r[16]||(r[16]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"},null,-1)])))],10,Fe)])])])}),128))])])]),H.value>1?(o(),l("div",Qe,[e("div",Ke,[e("div",We,[e("button",{onClick:N,disabled:u.value===1,class:"relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"}," Precedente ",8,Xe),e("button",{onClick:O,disabled:u.value===H.value,class:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"}," Successiva ",8,Ye)]),e("div",Ze,[e("div",null,[e("p",et,[r[18]||(r[18]=w(" Mostrando ")),e("span",tt,c((u.value-1)*t.value+1),1),r[19]||(r[19]=w(" a ")),e("span",rt,c(Math.min(u.value*t.value,s.value.length)),1),r[20]||(r[20]=w(" di ")),e("span",st,c(s.value.length),1),r[21]||(r[21]=w(" risultati "))])]),e("div",null,[e("nav",at,[e("button",{onClick:N,disabled:u.value===1,class:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"},r[22]||(r[22]=[e("svg",{class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z","clip-rule":"evenodd"})],-1)]),8,ot),(o(!0),l(z,null,M(K.value,d=>(o(),l("button",{key:d,onClick:U=>u.value=d,class:V(["relative inline-flex items-center px-4 py-2 border text-sm font-medium",d===u.value?"z-10 bg-blue-50 dark:bg-blue-900 border-blue-500 text-blue-600 dark:text-blue-200":"bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700"])},c(d),11,lt))),128)),e("button",{onClick:O,disabled:u.value===H.value,class:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"},r[23]||(r[23]=[e("svg",{class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})],-1)]),8,nt)])])])])])):b("",!0)])):(o(),l("div",dt,[r[24]||(r[24]=e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})],-1)),r[25]||(r[25]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun dipendente trovato",-1)),e("p",it,c(W.value?"Prova a modificare i filtri di ricerca.":"Inizia creando il primo dipendente."),1)]))])}}},mt=E(ut,[["__scopeId","data-v-1187e011"]]),ct={class:"space-y-6"},gt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},pt={class:"text-center py-12"},vt={class:"mt-4"},xt={__name:"DepartmentsManagement",emits:["department-created","department-updated","department-deleted"],setup(p,{emit:_}){return(v,m)=>{const k=A("router-link");return o(),l("div",ct,[m[4]||(m[4]=I('<div class="flex flex-col sm:flex-row sm:items-center sm:justify-between"><div><h3 class="text-lg font-medium text-gray-900 dark:text-white">Gestione Dipartimenti</h3><p class="mt-1 text-sm text-gray-500 dark:text-gray-400"> Gestisci la struttura organizzativa aziendale </p></div><button class="mt-4 sm:mt-0 inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"><svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path></svg> Nuovo Dipartimento </button></div>',1)),e("div",gt,[e("div",pt,[m[1]||(m[1]=e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})],-1)),m[2]||(m[2]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Gestione Dipartimenti",-1)),m[3]||(m[3]=e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"}," Funzionalità in fase di implementazione... ",-1)),e("div",vt,[D(k,{to:"/app/personnel/departments",class:"text-blue-600 dark:text-blue-400 hover:underline"},{default:P(()=>m[0]||(m[0]=[w(" Vai alla gestione dipartimenti esistente → ")])),_:1,__:[0]})])])])])}}},yt={class:"space-y-6"},bt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},ft={class:"text-center py-12"},kt={class:"mt-4"},ht={__name:"SkillsManagement",emits:["skill-created","skill-updated","skill-deleted"],setup(p,{emit:_}){return(v,m)=>{const k=A("router-link");return o(),l("div",yt,[m[4]||(m[4]=I('<div class="flex flex-col sm:flex-row sm:items-center sm:justify-between"><div><h3 class="text-lg font-medium text-gray-900 dark:text-white">Gestione Competenze</h3><p class="mt-1 text-sm text-gray-500 dark:text-gray-400"> Gestisci il catalogo delle competenze aziendali </p></div><button class="mt-4 sm:mt-0 inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"><svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path></svg> Nuova Competenza </button></div>',1)),e("div",bt,[e("div",ft,[m[1]||(m[1]=e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})],-1)),m[2]||(m[2]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Gestione Competenze",-1)),m[3]||(m[3]=e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"}," Funzionalità in fase di implementazione... ",-1)),e("div",kt,[D(k,{to:"/app/personnel/skills",class:"text-blue-600 dark:text-blue-400 hover:underline"},{default:P(()=>m[0]||(m[0]=[w(" Vai alla matrice competenze esistente → ")])),_:1,__:[0]})])])])])}}},wt={class:"space-y-6"},_t={key:0,class:"grid grid-cols-1 md:grid-cols-3 gap-6"},$t={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},Ct={class:"p-5"},zt={class:"flex items-center"},Mt={class:"ml-5 w-0 flex-1"},jt={class:"text-lg font-medium text-gray-900 dark:text-white"},Vt={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},Dt={class:"p-5"},Bt={class:"flex items-center"},Ut={class:"ml-5 w-0 flex-1"},Ht={class:"text-lg font-medium text-gray-900 dark:text-white"},Tt={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},Et={class:"p-5"},St={class:"flex items-center"},Lt={class:"ml-5 w-0 flex-1"},Pt={class:"text-lg font-medium text-gray-900 dark:text-white"},At={key:1,class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},It={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Rt={class:"space-y-3"},Nt={class:"flex items-center"},Ot={class:"text-sm text-gray-700 dark:text-gray-300"},Gt={class:"flex items-center"},Ft={class:"text-sm font-medium text-gray-900 dark:text-white mr-2"},qt={class:"w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2"},Jt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Qt={class:"space-y-3 max-h-64 overflow-y-auto"},Kt={class:"flex items-center"},Wt={class:"text-sm text-gray-700 dark:text-gray-300 truncate"},Xt={class:"flex items-center"},Yt={class:"text-sm font-medium text-gray-900 dark:text-white mr-2"},Zt={class:"w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2"},er={key:2,class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},tr={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},rr={class:"space-y-3"},sr={class:"flex items-center"},ar={class:"text-sm text-gray-700 dark:text-gray-300"},or={class:"flex items-center"},lr={class:"text-sm font-medium text-gray-900 dark:text-white mr-2"},nr={class:"w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2"},dr={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},ir={key:0,class:"space-y-3 max-h-64 overflow-y-auto"},ur={class:"text-sm text-gray-700 dark:text-gray-300 truncate"},mr={class:"text-sm font-medium text-gray-900 dark:text-white"},cr={key:1,class:"text-center py-8"},gr={class:"flex justify-center"},pr=["disabled"],vr={key:0,class:"animate-spin -ml-1 mr-3 h-4 w-4 text-white",fill:"none",viewBox:"0 0 24 24"},xr={key:1,class:"-ml-1 mr-3 h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},yr={key:3,class:"flex justify-center items-center h-64"},br={key:4,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-12 text-center"},fr={__name:"AnalyticsDashboard",props:{analytics:{type:Object,default:null},loading:{type:Boolean,default:!1}},emits:["refresh"],setup(p,{emit:_}){const v=y=>({admin:"Admin",manager:"Manager",employee:"Dipendente"})[y]||y,m=y=>({full_time:"Tempo pieno",part_time:"Part-time",contractor:"Consulente",intern:"Stagista"})[y]||y,k=y=>y?new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(y):"€0";return(y,i)=>(o(),l("div",wt,[p.analytics?(o(),l("div",_t,[e("div",$t,[e("div",Ct,[e("div",zt,[i[2]||(i[2]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-6 w-6 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})])],-1)),e("div",Mt,[e("dl",null,[i[1]||(i[1]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Dipendenti Totali ",-1)),e("dd",jt,c(p.analytics.overview.total_users),1)])])])])]),e("div",Vt,[e("div",Dt,[e("div",Bt,[i[4]||(i[4]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-6 w-6 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})])],-1)),e("div",Ut,[e("dl",null,[i[3]||(i[3]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Dipartimenti ",-1)),e("dd",Ht,c(p.analytics.overview.total_departments),1)])])])])]),e("div",Tt,[e("div",Et,[e("div",St,[i[6]||(i[6]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-6 w-6 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"})])],-1)),e("div",Lt,[e("dl",null,[i[5]||(i[5]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Assunzioni Recenti (90gg) ",-1)),e("dd",Pt,c(p.analytics.overview.recent_hires),1)])])])])])])):b("",!0),p.analytics?(o(),l("div",At,[e("div",It,[i[7]||(i[7]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Distribuzione per Ruolo",-1)),e("div",Rt,[(o(!0),l(z,null,M(p.analytics.users_by_role,a=>(o(),l("div",{key:a.role,class:"flex items-center justify-between"},[e("div",Nt,[e("div",{class:V(["w-3 h-3 rounded-full mr-3",a.role==="admin"?"bg-red-500":a.role==="manager"?"bg-blue-500":"bg-gray-500"])},null,2),e("span",Ot,c(v(a.role)),1)]),e("div",Gt,[e("span",Ft,c(a.count),1),e("div",qt,[e("div",{class:V(["h-2 rounded-full",a.role==="admin"?"bg-red-500":a.role==="manager"?"bg-blue-500":"bg-gray-500"]),style:S({width:`${a.count/p.analytics.overview.total_users*100}%`})},null,6)])])]))),128))])]),e("div",Jt,[i[9]||(i[9]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Distribuzione per Dipartimento",-1)),e("div",Qt,[(o(!0),l(z,null,M(p.analytics.users_by_department,a=>(o(),l("div",{key:a.department,class:"flex items-center justify-between"},[e("div",Kt,[i[8]||(i[8]=e("div",{class:"w-3 h-3 rounded-full bg-indigo-500 mr-3"},null,-1)),e("span",Wt,c(a.department),1)]),e("div",Xt,[e("span",Yt,c(a.count),1),e("div",Zt,[e("div",{class:"h-2 rounded-full bg-indigo-500",style:S({width:`${a.count/p.analytics.overview.total_users*100}%`})},null,4)])])]))),128))])])])):b("",!0),p.analytics?(o(),l("div",er,[e("div",tr,[i[11]||(i[11]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Tipi di Contratto",-1)),e("div",rr,[(o(!0),l(z,null,M(p.analytics.employment_types,a=>(o(),l("div",{key:a.type,class:"flex items-center justify-between"},[e("div",sr,[i[10]||(i[10]=e("div",{class:"w-3 h-3 rounded-full bg-green-500 mr-3"},null,-1)),e("span",ar,c(m(a.type)),1)]),e("div",or,[e("span",lr,c(a.count),1),e("div",nr,[e("div",{class:"h-2 rounded-full bg-green-500",style:S({width:`${a.count/p.analytics.overview.total_users*100}%`})},null,4)])])]))),128))])]),e("div",dr,[i[13]||(i[13]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Stipendio Medio per Dipartimento",-1)),p.analytics.avg_salary_by_department.length>0?(o(),l("div",ir,[(o(!0),l(z,null,M(p.analytics.avg_salary_by_department,a=>(o(),l("div",{key:a.department,class:"flex items-center justify-between"},[e("span",ur,c(a.department),1),e("span",mr,c(k(a.avg_salary)),1)]))),128))])):(o(),l("div",cr,i[12]||(i[12]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),e("p",{class:"mt-2 text-sm text-gray-500 dark:text-gray-400"},"Nessun dato stipendio disponibile",-1)])))])])):b("",!0),e("div",gr,[e("button",{onClick:i[0]||(i[0]=a=>y.$emit("refresh")),disabled:p.loading,class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"},[p.loading?(o(),l("svg",vr,i[14]||(i[14]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):(o(),l("svg",xr,i[15]||(i[15]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"},null,-1)]))),i[16]||(i[16]=w(" Aggiorna Dati "))],8,pr)]),p.loading&&!p.analytics?(o(),l("div",yr,i[17]||(i[17]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):b("",!0),!p.loading&&!p.analytics?(o(),l("div",br,i[18]||(i[18]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun dato disponibile",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},' Clicca su "Aggiorna Dati" per caricare le analytics. ',-1)]))):b("",!0)]))}},kr=E(fr,[["__scopeId","data-v-ed21a1db"]]),hr={class:"space-y-6"},wr={__name:"BulkOperations",emits:["operation-completed"],setup(p,{emit:_}){return(v,m)=>(o(),l("div",hr,m[0]||(m[0]=[I('<div><h3 class="text-lg font-medium text-gray-900 dark:text-white">Operazioni di Massa</h3><p class="mt-1 text-sm text-gray-500 dark:text-gray-400"> Import/Export dati e operazioni bulk </p></div><div class="grid grid-cols-1 md:grid-cols-2 gap-6"><div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6"><div class="flex items-center mb-4"><svg class="w-6 h-6 text-green-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path></svg><h4 class="text-lg font-medium text-gray-900 dark:text-white">Import Dipendenti</h4></div><p class="text-sm text-gray-500 dark:text-gray-400 mb-4"> Importa dipendenti da file CSV con dati contrattuali completi </p><div class="space-y-3"><button class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"><svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg> Scarica Template CSV </button><button class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700"><svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path></svg> Carica File CSV </button></div></div><div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6"><div class="flex items-center mb-4"><svg class="w-6 h-6 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path></svg><h4 class="text-lg font-medium text-gray-900 dark:text-white">Export Dati</h4></div><p class="text-sm text-gray-500 dark:text-gray-400 mb-4"> Esporta dati del personale in vari formati </p><div class="space-y-3"><button class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"><svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg> Export Completo CSV </button><button class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"><svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg> Export Solo Contatti </button></div></div><div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6"><div class="flex items-center mb-4"><svg class="w-6 h-6 text-purple-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path></svg><h4 class="text-lg font-medium text-gray-900 dark:text-white">Aggiornamenti di Massa</h4></div><p class="text-sm text-gray-500 dark:text-gray-400 mb-4"> Applica modifiche a più dipendenti contemporaneamente </p><div class="space-y-3"><button class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700"><svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path></svg> Assegnazione Dipartimenti </button><button class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"><svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg> Assegnazione Competenze </button></div></div><div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6"><div class="flex items-center mb-4"><svg class="w-6 h-6 text-red-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path></svg><h4 class="text-lg font-medium text-gray-900 dark:text-white">Pulizia Dati</h4></div><p class="text-sm text-gray-500 dark:text-gray-400 mb-4"> Strumenti per la manutenzione e pulizia dei dati </p><div class="space-y-3"><button class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"><svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg> Verifica Integrità Dati </button><button class="w-full inline-flex justify-center items-center px-4 py-2 border border-red-300 dark:border-red-600 shadow-sm text-sm font-medium rounded-md text-red-700 dark:text-red-300 bg-white dark:bg-gray-800 hover:bg-red-50 dark:hover:bg-red-900/20"><svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path></svg> Rimuovi Utenti Inattivi </button></div></div></div><div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4"><div class="flex"><svg class="w-5 h-5 text-yellow-400 mr-3 mt-0.5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg><div><h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">Funzionalità in Sviluppo</h3><p class="text-sm text-yellow-700 dark:text-yellow-300 mt-1"> Le operazioni di massa sono in fase di implementazione. Alcune funzionalità potrebbero non essere ancora disponibili. </p></div></div></div>',3)])))}},_r={class:"flex items-center justify-between pb-4 border-b border-gray-200 dark:border-gray-700"},$r={class:"mt-6"},Cr={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},zr={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Mr=["value"],jr={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Vr={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Dr={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},Br={class:"flex items-center space-x-4"},Ur={class:"flex items-center"},Hr={key:0,class:"mt-3"},Tr={key:0,class:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4"},Er={class:"flex"},Sr={class:"text-sm text-red-700 dark:text-red-300 mt-1"},Lr={class:"flex items-center justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700"},Pr=["disabled"],Ar={key:0,class:"animate-spin -ml-1 mr-3 h-4 w-4 text-white inline",fill:"none",viewBox:"0 0 24 24"},Ir={__name:"CreateUserModal",emits:["close","user-created"],setup(p,{emit:_}){const v=_,m=f(!1),k=f(null),y=f([]),i=f(!0),a=f({first_name:"",last_name:"",username:"",email:"",phone:"",position:"",role:"employee",department_id:"",hire_date:"",employment_type:"full_time",work_location:"",weekly_hours:40,probation_end_date:"",contract_end_date:"",salary:null,salary_currency:"EUR",emergency_contact_name:"",emergency_contact_phone:"",emergency_contact_relationship:"",password:""}),j=async()=>{try{const u=await fetch("/api/personnel/departments",{credentials:"include"});if(!u.ok)throw new Error(`HTTP ${u.status}: ${u.statusText}`);const t=await u.json();t.success&&(y.value=t.data.departments||[])}catch(u){console.error("Error loading departments:",u)}},$=async()=>{m.value=!0,k.value=null;try{const u={...a.value};i.value&&delete u.password,Object.keys(u).forEach(C=>{u[C]===""&&(u[C]=null)});const t=await fetch("/api/personnel/admin/users",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(u)});if(!t.ok)throw new Error(`HTTP ${t.status}: ${t.statusText}`);const s=await t.json();if(s.success)v("user-created",s.data.user),i.value&&s.data.temporary_password&&alert(`Utente creato con successo!
Password temporanea: ${s.data.temporary_password}`);else throw new Error(s.message||"Errore nella creazione utente")}catch(u){console.error("Error creating user:",u),k.value=u.message}finally{m.value=!1}};return L(()=>{j();const u=new Date().toISOString().split("T")[0];a.value.hire_date=u}),(u,t)=>(o(),l("div",{class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:t[24]||(t[24]=s=>u.$emit("close"))},[e("div",{class:"relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:t[23]||(t[23]=Q(()=>{},["stop"]))},[e("div",_r,[t[26]||(t[26]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Crea Nuovo Dipendente ",-1)),e("button",{onClick:t[0]||(t[0]=s=>u.$emit("close")),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},t[25]||(t[25]=[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),e("div",$r,[e("form",{onSubmit:Q($,["prevent"]),class:"space-y-6"},[e("div",null,[t[33]||(t[33]=e("h4",{class:"text-md font-medium text-gray-900 dark:text-white mb-4"},"Informazioni Base",-1)),e("div",Cr,[e("div",null,[t[27]||(t[27]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Nome * ",-1)),x(e("input",{"onUpdate:modelValue":t[1]||(t[1]=s=>a.value.first_name=s),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[h,a.value.first_name]])]),e("div",null,[t[28]||(t[28]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Cognome * ",-1)),x(e("input",{"onUpdate:modelValue":t[2]||(t[2]=s=>a.value.last_name=s),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[h,a.value.last_name]])]),e("div",null,[t[29]||(t[29]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Username * ",-1)),x(e("input",{"onUpdate:modelValue":t[3]||(t[3]=s=>a.value.username=s),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[h,a.value.username]])]),e("div",null,[t[30]||(t[30]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Email * ",-1)),x(e("input",{"onUpdate:modelValue":t[4]||(t[4]=s=>a.value.email=s),type:"email",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[h,a.value.email]])]),e("div",null,[t[31]||(t[31]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Telefono ",-1)),x(e("input",{"onUpdate:modelValue":t[5]||(t[5]=s=>a.value.phone=s),type:"tel",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[h,a.value.phone]])]),e("div",null,[t[32]||(t[32]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Posizione ",-1)),x(e("input",{"onUpdate:modelValue":t[6]||(t[6]=s=>a.value.position=s),type:"text",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[h,a.value.position]])])])]),e("div",null,[t[38]||(t[38]=e("h4",{class:"text-md font-medium text-gray-900 dark:text-white mb-4"},"Ruolo e Dipartimento",-1)),e("div",zr,[e("div",null,[t[35]||(t[35]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Ruolo ",-1)),x(e("select",{"onUpdate:modelValue":t[7]||(t[7]=s=>a.value.role=s),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},t[34]||(t[34]=[e("option",{value:"employee"},"Dipendente",-1),e("option",{value:"manager"},"Manager",-1),e("option",{value:"admin"},"Admin",-1)]),512),[[B,a.value.role]])]),e("div",null,[t[37]||(t[37]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Dipartimento ",-1)),x(e("select",{"onUpdate:modelValue":t[8]||(t[8]=s=>a.value.department_id=s),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},[t[36]||(t[36]=e("option",{value:""},"Seleziona dipartimento",-1)),(o(!0),l(z,null,M(y.value,s=>(o(),l("option",{key:s.id,value:s.id},c(s.name),9,Mr))),128))],512),[[B,a.value.department_id]])])])]),e("div",null,[t[47]||(t[47]=e("h4",{class:"text-md font-medium text-gray-900 dark:text-white mb-4"},"Informazioni Contrattuali",-1)),e("div",jr,[e("div",null,[t[39]||(t[39]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Data Assunzione ",-1)),x(e("input",{"onUpdate:modelValue":t[9]||(t[9]=s=>a.value.hire_date=s),type:"date",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[h,a.value.hire_date]])]),e("div",null,[t[41]||(t[41]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Tipo Contratto ",-1)),x(e("select",{"onUpdate:modelValue":t[10]||(t[10]=s=>a.value.employment_type=s),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},t[40]||(t[40]=[e("option",{value:"full_time"},"Tempo pieno",-1),e("option",{value:"part_time"},"Part-time",-1),e("option",{value:"contractor"},"Consulente",-1),e("option",{value:"intern"},"Stagista",-1)]),512),[[B,a.value.employment_type]])]),e("div",null,[t[43]||(t[43]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Modalità Lavoro ",-1)),x(e("select",{"onUpdate:modelValue":t[11]||(t[11]=s=>a.value.work_location=s),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},t[42]||(t[42]=[e("option",{value:""},"Seleziona modalità",-1),e("option",{value:"office"},"Ufficio",-1),e("option",{value:"remote"},"Remoto",-1),e("option",{value:"hybrid"},"Ibrido",-1)]),512),[[B,a.value.work_location]])]),e("div",null,[t[44]||(t[44]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Ore Settimanali ",-1)),x(e("input",{"onUpdate:modelValue":t[12]||(t[12]=s=>a.value.weekly_hours=s),type:"number",step:"0.5",min:"0",max:"60",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[h,a.value.weekly_hours,void 0,{number:!0}]])]),e("div",null,[t[45]||(t[45]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Fine Periodo Prova ",-1)),x(e("input",{"onUpdate:modelValue":t[13]||(t[13]=s=>a.value.probation_end_date=s),type:"date",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[h,a.value.probation_end_date]])]),e("div",null,[t[46]||(t[46]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Scadenza Contratto ",-1)),x(e("input",{"onUpdate:modelValue":t[14]||(t[14]=s=>a.value.contract_end_date=s),type:"date",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[h,a.value.contract_end_date]])])])]),e("div",null,[t[51]||(t[51]=e("h4",{class:"text-md font-medium text-gray-900 dark:text-white mb-4"},"Informazioni Economiche",-1)),e("div",Vr,[e("div",null,[t[48]||(t[48]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Stipendio Annuo ",-1)),x(e("input",{"onUpdate:modelValue":t[15]||(t[15]=s=>a.value.salary=s),type:"number",step:"100",min:"0",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[h,a.value.salary,void 0,{number:!0}]])]),e("div",null,[t[50]||(t[50]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Valuta ",-1)),x(e("select",{"onUpdate:modelValue":t[16]||(t[16]=s=>a.value.salary_currency=s),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},t[49]||(t[49]=[e("option",{value:"EUR"},"EUR",-1),e("option",{value:"USD"},"USD",-1),e("option",{value:"GBP"},"GBP",-1)]),512),[[B,a.value.salary_currency]])])])]),e("div",null,[t[55]||(t[55]=e("h4",{class:"text-md font-medium text-gray-900 dark:text-white mb-4"},"Contatto di Emergenza",-1)),e("div",Dr,[e("div",null,[t[52]||(t[52]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Nome Contatto ",-1)),x(e("input",{"onUpdate:modelValue":t[17]||(t[17]=s=>a.value.emergency_contact_name=s),type:"text",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[h,a.value.emergency_contact_name]])]),e("div",null,[t[53]||(t[53]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Telefono Emergenza ",-1)),x(e("input",{"onUpdate:modelValue":t[18]||(t[18]=s=>a.value.emergency_contact_phone=s),type:"tel",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[h,a.value.emergency_contact_phone]])]),e("div",null,[t[54]||(t[54]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Relazione ",-1)),x(e("input",{"onUpdate:modelValue":t[19]||(t[19]=s=>a.value.emergency_contact_relationship=s),type:"text",placeholder:"es. Coniuge, Genitore...",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[h,a.value.emergency_contact_relationship]])])])]),e("div",null,[t[58]||(t[58]=e("h4",{class:"text-md font-medium text-gray-900 dark:text-white mb-4"},"Password",-1)),e("div",Br,[e("label",Ur,[x(e("input",{"onUpdate:modelValue":t[20]||(t[20]=s=>i.value=s),type:"checkbox",class:"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"},null,512),[[oe,i.value]]),t[56]||(t[56]=e("span",{class:"ml-2 text-sm text-gray-700 dark:text-gray-300"}," Genera password temporanea automaticamente ",-1))])]),i.value?b("",!0):(o(),l("div",Hr,[t[57]||(t[57]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Password Personalizzata ",-1)),x(e("input",{"onUpdate:modelValue":t[21]||(t[21]=s=>a.value.password=s),type:"password",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[h,a.value.password]])]))]),k.value?(o(),l("div",Tr,[e("div",Er,[t[60]||(t[60]=e("svg",{class:"w-5 h-5 text-red-400 mr-2 mt-0.5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z","clip-rule":"evenodd"})],-1)),e("div",null,[t[59]||(t[59]=e("h3",{class:"text-sm font-medium text-red-800 dark:text-red-200"},"Errore nella creazione",-1)),e("p",Sr,c(k.value),1)])])])):b("",!0),e("div",Lr,[e("button",{type:"button",onClick:t[22]||(t[22]=s=>u.$emit("close")),class:"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"}," Annulla "),e("button",{type:"submit",disabled:m.value,class:"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"},[m.value?(o(),l("svg",Ar,t[61]||(t[61]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):b("",!0),w(" "+c(m.value?"Creazione...":"Crea Dipendente"),1)],8,Pr)])],32)])])]))}},Rr=E(Ir,[["__scopeId","data-v-86e643b3"]]),Nr={class:"space-y-6"},Or={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},Gr={class:"mt-4 sm:mt-0 flex items-center space-x-3"},Fr=["disabled"],qr={key:0,class:"space-y-4"},Jr={key:0,class:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4"},Qr={class:"flex"},Kr={class:"flex-1"},Wr={class:"mt-2 text-sm text-red-700 dark:text-red-300"},Xr={class:"mb-2"},Yr={class:"list-disc list-inside space-y-1"},Zr={key:0,class:"mt-2 text-xs"},es={key:1,class:"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4"},ts={class:"flex"},rs={class:"flex-1"},ss={class:"mt-2 text-sm text-yellow-700 dark:text-yellow-300"},as={class:"mb-2"},os={class:"list-disc list-inside space-y-1"},ls={key:0,class:"mt-2 text-xs"},ns={class:"border-b border-gray-200 dark:border-gray-700"},ds={class:"-mb-px flex space-x-8"},is=["onClick"],us={key:0,class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},ms={key:1,class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},cs={key:2,class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},gs={key:3,class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},ps={key:4,class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},vs={class:"mt-6"},xs={key:0},ys={key:1},bs={key:2},fs={key:3},ks={key:4},hs={__name:"PersonnelAdmin",setup(p){const _=f(!1),v=f(null),m=f("users"),k=f(!1),y=f([{id:"users",name:"Gestione Utenti"},{id:"departments",name:"Dipartimenti"},{id:"skills",name:"Competenze"},{id:"analytics",name:"Analytics"},{id:"bulk",name:"Operazioni Bulk"}]),i=async()=>{_.value=!0;try{const u=await fetch("/api/personnel/admin/analytics",{credentials:"include"});if(!u.ok)throw new Error(`HTTP ${u.status}: ${u.statusText}`);const t=await u.json();if(t.success)v.value=t.data;else throw new Error(t.message||"Errore nel caricamento analytics")}catch(u){console.error("Error loading analytics:",u)}finally{_.value=!1}},a=async()=>{try{const u=await fetch("/api/personnel/export",{credentials:"include"});if(!u.ok)throw new Error(`HTTP ${u.status}: ${u.statusText}`);const t=await u.blob(),s=window.URL.createObjectURL(t),C=document.createElement("a");C.href=s,C.download=`personnel-export-${new Date().toISOString().split("T")[0]}.csv`,document.body.appendChild(C),C.click(),document.body.removeChild(C),window.URL.revokeObjectURL(s)}catch(u){console.error("Error exporting data:",u)}},j=u=>{k.value=!1,i()},$=u=>u?new Date(u).toLocaleDateString("it-IT",{year:"numeric",month:"short",day:"numeric"}):"";return L(()=>{i()}),(u,t)=>(o(),l("div",Nr,[e("div",Or,[t[4]||(t[4]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white flex items-center"},[e("svg",{class:"w-8 h-8 mr-3 text-purple-600",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z","clip-rule":"evenodd"})]),w(" Amministrazione Personnel ")]),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"}," Gestione completa del personale e dati contrattuali ")],-1)),e("div",Gr,[e("button",{onClick:t[0]||(t[0]=s=>k.value=!0),class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},t[2]||(t[2]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),w(" Nuovo Dipendente ")])),e("button",{onClick:a,disabled:_.value,class:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"},t[3]||(t[3]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),w(" Esporta Dati ")]),8,Fr)])]),v.value&&(v.value.alerts.expiring_contracts.length>0||v.value.alerts.ending_probation.length>0)?(o(),l("div",qr,[v.value.alerts.expiring_contracts.length>0?(o(),l("div",Jr,[e("div",Qr,[t[6]||(t[6]=e("svg",{class:"w-5 h-5 text-red-400 mr-3 mt-0.5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z","clip-rule":"evenodd"})],-1)),e("div",Kr,[t[5]||(t[5]=e("h3",{class:"text-sm font-medium text-red-800 dark:text-red-200"},"Contratti in Scadenza",-1)),e("div",Wr,[e("p",Xr,c(v.value.alerts.expiring_contracts.length)+" contratti scadranno nei prossimi 90 giorni:",1),e("ul",Yr,[(o(!0),l(z,null,M(v.value.alerts.expiring_contracts.slice(0,3),s=>(o(),l("li",{key:s.user_id},[e("strong",null,c(s.full_name),1),w(" - "+c($(s.contract_end_date))+" ("+c(s.days_remaining)+" giorni) ",1)]))),128))]),v.value.alerts.expiring_contracts.length>3?(o(),l("p",Zr," +"+c(v.value.alerts.expiring_contracts.length-3)+" altri contratti ",1)):b("",!0)])])])])):b("",!0),v.value.alerts.ending_probation.length>0?(o(),l("div",es,[e("div",ts,[t[8]||(t[8]=e("svg",{class:"w-5 h-5 text-yellow-400 mr-3 mt-0.5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z","clip-rule":"evenodd"})],-1)),e("div",rs,[t[7]||(t[7]=e("h3",{class:"text-sm font-medium text-yellow-800 dark:text-yellow-200"},"Periodi di Prova in Scadenza",-1)),e("div",ss,[e("p",as,c(v.value.alerts.ending_probation.length)+" periodi di prova termineranno nei prossimi 30 giorni:",1),e("ul",os,[(o(!0),l(z,null,M(v.value.alerts.ending_probation.slice(0,3),s=>(o(),l("li",{key:s.user_id},[e("strong",null,c(s.full_name),1),w(" - "+c($(s.probation_end_date))+" ("+c(s.days_remaining)+" giorni) ",1)]))),128))]),v.value.alerts.ending_probation.length>3?(o(),l("p",ls," +"+c(v.value.alerts.ending_probation.length-3)+" altri periodi di prova ",1)):b("",!0)])])])])):b("",!0)])):b("",!0),e("div",ns,[e("nav",ds,[(o(!0),l(z,null,M(y.value,s=>(o(),l("button",{key:s.id,onClick:C=>m.value=s.id,class:V(["py-2 px-1 border-b-2 font-medium text-sm flex items-center",m.value===s.id?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600"])},[s.id==="users"?(o(),l("svg",us,t[9]||(t[9]=[e("path",{d:"M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"},null,-1)]))):s.id==="departments"?(o(),l("svg",ms,t[10]||(t[10]=[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z","clip-rule":"evenodd"},null,-1)]))):s.id==="skills"?(o(),l("svg",cs,t[11]||(t[11]=[e("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"},null,-1)]))):s.id==="analytics"?(o(),l("svg",gs,t[12]||(t[12]=[e("path",{d:"M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"},null,-1)]))):(o(),l("svg",ps,t[13]||(t[13]=[e("path",{"fill-rule":"evenodd",d:"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z","clip-rule":"evenodd"},null,-1)]))),w(" "+c(s.name),1)],10,is))),128))])]),e("div",vs,[m.value==="users"?(o(),l("div",xs,[D(mt,{onUserCreated:i,onUserUpdated:i,onUserDeleted:i})])):m.value==="departments"?(o(),l("div",ys,[D(xt,{onDepartmentCreated:i,onDepartmentUpdated:i,onDepartmentDeleted:i})])):m.value==="skills"?(o(),l("div",bs,[D(ht,{onSkillCreated:i,onSkillUpdated:i,onSkillDeleted:i})])):m.value==="analytics"?(o(),l("div",fs,[D(kr,{analytics:v.value,loading:_.value,onRefresh:i},null,8,["analytics","loading"])])):m.value==="bulk"?(o(),l("div",ks,[D(wr,{onOperationCompleted:i})])):b("",!0)]),k.value?(o(),le(Rr,{key:1,onClose:t[1]||(t[1]=s=>k.value=!1),onUserCreated:j})):b("",!0)]))}},$s=E(hs,[["__scopeId","data-v-2701ad16"]]);export{$s as default};

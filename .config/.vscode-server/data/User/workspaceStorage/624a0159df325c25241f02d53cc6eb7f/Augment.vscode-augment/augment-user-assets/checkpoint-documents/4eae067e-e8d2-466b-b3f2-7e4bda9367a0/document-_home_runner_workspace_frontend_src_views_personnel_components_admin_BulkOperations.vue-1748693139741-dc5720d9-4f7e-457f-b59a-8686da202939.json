{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/personnel/components/admin/BulkOperations.vue"}, "originalCode": "<template>\n  <div class=\"space-y-6\">\n    <!-- Header -->\n    <div>\n      <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">Operazioni di Massa</h3>\n      <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\n        Import/Export dati e operazioni bulk\n      </p>\n    </div>\n\n    <!-- Operations Grid -->\n    <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n      <!-- Import Users -->\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center mb-4\">\n          <svg class=\"w-6 h-6 text-green-600 mr-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10\"></path>\n          </svg>\n          <h4 class=\"text-lg font-medium text-gray-900 dark:text-white\">Import Dipendenti</h4>\n        </div>\n        <p class=\"text-sm text-gray-500 dark:text-gray-400 mb-4\">\n          Importa dipendenti da file CSV con dati contrattuali completi\n        </p>\n        <div class=\"space-y-3\">\n          <button @click=\"downloadTemplate\"\n                  :disabled=\"loading.template\"\n                  class=\"w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50\">\n            <svg v-if=\"loading.template\" class=\"animate-spin w-4 h-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\">\n              <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\"></circle>\n              <path class=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n            </svg>\n            <svg v-else class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path>\n            </svg>\n            {{ loading.template ? 'Generando...' : 'Scarica Template CSV' }}\n          </button>\n\n          <div class=\"relative\">\n            <input ref=\"fileInput\"\n                   type=\"file\"\n                   accept=\".csv\"\n                   @change=\"handleFileUpload\"\n                   class=\"hidden\">\n            <button @click=\"$refs.fileInput.click()\"\n                    :disabled=\"loading.import\"\n                    class=\"w-full inline-flex justify-center items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 disabled:opacity-50\">\n              <svg v-if=\"loading.import\" class=\"animate-spin w-4 h-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\">\n                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\"></circle>\n                <path class=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n              </svg>\n              <svg v-else class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10\"></path>\n              </svg>\n              {{ loading.import ? 'Importando...' : 'Carica File CSV' }}\n            </button>\n          </div>\n\n          <!-- Import Progress -->\n          <div v-if=\"importProgress.show\" class=\"mt-3 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg\">\n            <div class=\"flex items-center justify-between mb-2\">\n              <span class=\"text-sm font-medium text-blue-800 dark:text-blue-200\">Import in corso...</span>\n              <span class=\"text-sm text-blue-600 dark:text-blue-400\">{{ importProgress.processed }}/{{ importProgress.total }}</span>\n            </div>\n            <div class=\"w-full bg-blue-200 dark:bg-blue-800 rounded-full h-2\">\n              <div class=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\n                   :style=\"{ width: `${(importProgress.processed / importProgress.total) * 100}%` }\"></div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Export Data -->\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center mb-4\">\n          <svg class=\"w-6 h-6 text-blue-600 mr-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"></path>\n          </svg>\n          <h4 class=\"text-lg font-medium text-gray-900 dark:text-white\">Export Dati</h4>\n        </div>\n        <p class=\"text-sm text-gray-500 dark:text-gray-400 mb-4\">\n          Esporta dati del personale in vari formati\n        </p>\n        <div class=\"space-y-3\">\n          <button @click=\"exportData('full')\"\n                  :disabled=\"loading.export\"\n                  class=\"w-full inline-flex justify-center items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50\">\n            <svg v-if=\"loading.export\" class=\"animate-spin w-4 h-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\">\n              <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\"></circle>\n              <path class=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n            </svg>\n            <svg v-else class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path>\n            </svg>\n            {{ loading.export ? 'Esportando...' : 'Export Completo CSV' }}\n          </button>\n\n          <button @click=\"exportData('contacts')\"\n                  :disabled=\"loading.export\"\n                  class=\"w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50\">\n            <svg v-if=\"loading.export\" class=\"animate-spin w-4 h-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\">\n              <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\"></circle>\n              <path class=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n            </svg>\n            <svg v-else class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path>\n            </svg>\n            {{ loading.export ? 'Esportando...' : 'Export Solo Contatti' }}\n          </button>\n\n          <button @click=\"showExportModal = true\"\n                  class=\"w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700\">\n            <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4\"></path>\n            </svg>\n            Export Personalizzato\n          </button>\n        </div>\n      </div>\n\n      <!-- Bulk Updates -->\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center mb-4\">\n          <svg class=\"w-6 h-6 text-purple-600 mr-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"></path>\n          </svg>\n          <h4 class=\"text-lg font-medium text-gray-900 dark:text-white\">Aggiornamenti di Massa</h4>\n        </div>\n        <p class=\"text-sm text-gray-500 dark:text-gray-400 mb-4\">\n          Applica modifiche a più dipendenti contemporaneamente\n        </p>\n        <div class=\"space-y-3\">\n          <button @click=\"showBulkDepartmentModal = true\"\n                  class=\"w-full inline-flex justify-center items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700\">\n            <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"></path>\n            </svg>\n            Assegnazione Dipartimenti\n          </button>\n\n          <button @click=\"showBulkSkillsModal = true\"\n                  class=\"w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700\">\n            <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"></path>\n            </svg>\n            Assegnazione Competenze\n          </button>\n\n          <button @click=\"showBulkRoleModal = true\"\n                  class=\"w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700\">\n            <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\"></path>\n            </svg>\n            Modifica Ruoli\n          </button>\n        </div>\n      </div>\n\n      <!-- Data Cleanup -->\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center mb-4\">\n          <svg class=\"w-6 h-6 text-red-600 mr-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"></path>\n          </svg>\n          <h4 class=\"text-lg font-medium text-gray-900 dark:text-white\">Pulizia Dati</h4>\n        </div>\n        <p class=\"text-sm text-gray-500 dark:text-gray-400 mb-4\">\n          Strumenti per la manutenzione e pulizia dei dati\n        </p>\n        <div class=\"space-y-3\">\n          <button @click=\"verifyDataIntegrity\"\n                  :disabled=\"loading.verify\"\n                  class=\"w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50\">\n            <svg v-if=\"loading.verify\" class=\"animate-spin w-4 h-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\">\n              <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\"></circle>\n              <path class=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n            </svg>\n            <svg v-else class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n            </svg>\n            {{ loading.verify ? 'Verificando...' : 'Verifica Integrità Dati' }}\n          </button>\n\n          <button @click=\"showCleanupModal = true\"\n                  class=\"w-full inline-flex justify-center items-center px-4 py-2 border border-red-300 dark:border-red-600 shadow-sm text-sm font-medium rounded-md text-red-700 dark:text-red-300 bg-white dark:bg-gray-800 hover:bg-red-50 dark:hover:bg-red-900/20\">\n            <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"></path>\n            </svg>\n            Rimuovi Utenti Inattivi\n          </button>\n\n          <button @click=\"showDataCleanupModal = true\"\n                  class=\"w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700\">\n            <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"></path>\n            </svg>\n            Pulizia Avanzata\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Status Messages -->\n    <div class=\"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4\">\n      <div class=\"flex\">\n        <svg class=\"w-5 h-5 text-yellow-400 mr-3 mt-0.5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n          <path fill-rule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clip-rule=\"evenodd\"></path>\n        </svg>\n        <div>\n          <h3 class=\"text-sm font-medium text-yellow-800 dark:text-yellow-200\">Funzionalità in Sviluppo</h3>\n          <p class=\"text-sm text-yellow-700 dark:text-yellow-300 mt-1\">\n            Le operazioni di massa sono in fase di implementazione. Alcune funzionalità potrebbero non essere ancora disponibili.\n          </p>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\n// Emits\nconst emit = defineEmits(['operation-completed'])\n\n// Placeholder component - will be implemented\n</script>\n", "modifiedCode": "<template>\n  <div class=\"space-y-6\">\n    <!-- Header -->\n    <div>\n      <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">Operazioni di Massa</h3>\n      <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\n        Import/Export dati e operazioni bulk\n      </p>\n    </div>\n\n    <!-- Operations Grid -->\n    <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n      <!-- Import Users -->\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center mb-4\">\n          <svg class=\"w-6 h-6 text-green-600 mr-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10\"></path>\n          </svg>\n          <h4 class=\"text-lg font-medium text-gray-900 dark:text-white\">Import Dipendenti</h4>\n        </div>\n        <p class=\"text-sm text-gray-500 dark:text-gray-400 mb-4\">\n          Importa dipendenti da file CSV con dati contrattuali completi\n        </p>\n        <div class=\"space-y-3\">\n          <button @click=\"downloadTemplate\"\n                  :disabled=\"loading.template\"\n                  class=\"w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50\">\n            <svg v-if=\"loading.template\" class=\"animate-spin w-4 h-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\">\n              <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\"></circle>\n              <path class=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n            </svg>\n            <svg v-else class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path>\n            </svg>\n            {{ loading.template ? 'Generando...' : 'Scarica Template CSV' }}\n          </button>\n\n          <div class=\"relative\">\n            <input ref=\"fileInput\"\n                   type=\"file\"\n                   accept=\".csv\"\n                   @change=\"handleFileUpload\"\n                   class=\"hidden\">\n            <button @click=\"$refs.fileInput.click()\"\n                    :disabled=\"loading.import\"\n                    class=\"w-full inline-flex justify-center items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 disabled:opacity-50\">\n              <svg v-if=\"loading.import\" class=\"animate-spin w-4 h-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\">\n                <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\"></circle>\n                <path class=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n              </svg>\n              <svg v-else class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10\"></path>\n              </svg>\n              {{ loading.import ? 'Importando...' : 'Carica File CSV' }}\n            </button>\n          </div>\n\n          <!-- Import Progress -->\n          <div v-if=\"importProgress.show\" class=\"mt-3 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg\">\n            <div class=\"flex items-center justify-between mb-2\">\n              <span class=\"text-sm font-medium text-blue-800 dark:text-blue-200\">Import in corso...</span>\n              <span class=\"text-sm text-blue-600 dark:text-blue-400\">{{ importProgress.processed }}/{{ importProgress.total }}</span>\n            </div>\n            <div class=\"w-full bg-blue-200 dark:bg-blue-800 rounded-full h-2\">\n              <div class=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\n                   :style=\"{ width: `${(importProgress.processed / importProgress.total) * 100}%` }\"></div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Export Data -->\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center mb-4\">\n          <svg class=\"w-6 h-6 text-blue-600 mr-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\"></path>\n          </svg>\n          <h4 class=\"text-lg font-medium text-gray-900 dark:text-white\">Export Dati</h4>\n        </div>\n        <p class=\"text-sm text-gray-500 dark:text-gray-400 mb-4\">\n          Esporta dati del personale in vari formati\n        </p>\n        <div class=\"space-y-3\">\n          <button @click=\"exportData('full')\"\n                  :disabled=\"loading.export\"\n                  class=\"w-full inline-flex justify-center items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50\">\n            <svg v-if=\"loading.export\" class=\"animate-spin w-4 h-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\">\n              <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\"></circle>\n              <path class=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n            </svg>\n            <svg v-else class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path>\n            </svg>\n            {{ loading.export ? 'Esportando...' : 'Export Completo CSV' }}\n          </button>\n\n          <button @click=\"exportData('contacts')\"\n                  :disabled=\"loading.export\"\n                  class=\"w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50\">\n            <svg v-if=\"loading.export\" class=\"animate-spin w-4 h-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\">\n              <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\"></circle>\n              <path class=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n            </svg>\n            <svg v-else class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path>\n            </svg>\n            {{ loading.export ? 'Esportando...' : 'Export Solo Contatti' }}\n          </button>\n\n          <button @click=\"showExportModal = true\"\n                  class=\"w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700\">\n            <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4\"></path>\n            </svg>\n            Export Personalizzato\n          </button>\n        </div>\n      </div>\n\n      <!-- Bulk Updates -->\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center mb-4\">\n          <svg class=\"w-6 h-6 text-purple-600 mr-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"></path>\n          </svg>\n          <h4 class=\"text-lg font-medium text-gray-900 dark:text-white\">Aggiornamenti di Massa</h4>\n        </div>\n        <p class=\"text-sm text-gray-500 dark:text-gray-400 mb-4\">\n          Applica modifiche a più dipendenti contemporaneamente\n        </p>\n        <div class=\"space-y-3\">\n          <button @click=\"showBulkDepartmentModal = true\"\n                  class=\"w-full inline-flex justify-center items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700\">\n            <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"></path>\n            </svg>\n            Assegnazione Dipartimenti\n          </button>\n\n          <button @click=\"showBulkSkillsModal = true\"\n                  class=\"w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700\">\n            <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"></path>\n            </svg>\n            Assegnazione Competenze\n          </button>\n\n          <button @click=\"showBulkRoleModal = true\"\n                  class=\"w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700\">\n            <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\"></path>\n            </svg>\n            Modifica Ruoli\n          </button>\n        </div>\n      </div>\n\n      <!-- Data Cleanup -->\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center mb-4\">\n          <svg class=\"w-6 h-6 text-red-600 mr-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"></path>\n          </svg>\n          <h4 class=\"text-lg font-medium text-gray-900 dark:text-white\">Pulizia Dati</h4>\n        </div>\n        <p class=\"text-sm text-gray-500 dark:text-gray-400 mb-4\">\n          Strumenti per la manutenzione e pulizia dei dati\n        </p>\n        <div class=\"space-y-3\">\n          <button @click=\"verifyDataIntegrity\"\n                  :disabled=\"loading.verify\"\n                  class=\"w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50\">\n            <svg v-if=\"loading.verify\" class=\"animate-spin w-4 h-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\">\n              <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\"></circle>\n              <path class=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n            </svg>\n            <svg v-else class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n            </svg>\n            {{ loading.verify ? 'Verificando...' : 'Verifica Integrità Dati' }}\n          </button>\n\n          <button @click=\"showCleanupModal = true\"\n                  class=\"w-full inline-flex justify-center items-center px-4 py-2 border border-red-300 dark:border-red-600 shadow-sm text-sm font-medium rounded-md text-red-700 dark:text-red-300 bg-white dark:bg-gray-800 hover:bg-red-50 dark:hover:bg-red-900/20\">\n            <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"></path>\n            </svg>\n            Rimuovi Utenti Inattivi\n          </button>\n\n          <button @click=\"showDataCleanupModal = true\"\n                  class=\"w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700\">\n            <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"></path>\n            </svg>\n            Pulizia Avanzata\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Status Messages -->\n    <div v-if=\"statusMessage\" class=\"mt-6 p-4 rounded-lg\" :class=\"statusMessage.type === 'success' ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800' : 'bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800'\">\n      <div class=\"flex\">\n        <svg v-if=\"statusMessage.type === 'success'\" class=\"w-5 h-5 text-green-400 mr-2 mt-0.5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n          <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clip-rule=\"evenodd\"></path>\n        </svg>\n        <svg v-else class=\"w-5 h-5 text-red-400 mr-2 mt-0.5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n          <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clip-rule=\"evenodd\"></path>\n        </svg>\n        <div>\n          <h3 class=\"text-sm font-medium\" :class=\"statusMessage.type === 'success' ? 'text-green-800 dark:text-green-200' : 'text-red-800 dark:text-red-200'\">\n            {{ statusMessage.title }}\n          </h3>\n          <p class=\"text-sm mt-1\" :class=\"statusMessage.type === 'success' ? 'text-green-700 dark:text-green-300' : 'text-red-700 dark:text-red-300'\">\n            {{ statusMessage.message }}\n          </p>\n          <div v-if=\"statusMessage.details && statusMessage.details.length > 0\" class=\"mt-2\">\n            <ul class=\"text-xs space-y-1\" :class=\"statusMessage.type === 'success' ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'\">\n              <li v-for=\"detail in statusMessage.details\" :key=\"detail\">• {{ detail }}</li>\n            </ul>\n          </div>\n        </div>\n        <button @click=\"statusMessage = null\" class=\"ml-auto\">\n          <svg class=\"w-4 h-4\" :class=\"statusMessage.type === 'success' ? 'text-green-400' : 'text-red-400'\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\"></path>\n          </svg>\n        </button>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref } from 'vue'\n\n// Emits\nconst emit = defineEmits(['operation-completed'])\n\n// Reactive state\nconst loading = ref({\n  template: false,\n  import: false,\n  export: false,\n  verify: false\n})\n\nconst importProgress = ref({\n  show: false,\n  processed: 0,\n  total: 0\n})\n\nconst statusMessage = ref(null)\n\n// Modal states\nconst showExportModal = ref(false)\nconst showBulkDepartmentModal = ref(false)\nconst showBulkSkillsModal = ref(false)\nconst showBulkRoleModal = ref(false)\nconst showCleanupModal = ref(false)\nconst showDataCleanupModal = ref(false)\n\n// Methods\nconst downloadTemplate = async () => {\n  loading.value.template = true\n\n  try {\n    // Generate CSV template with all required fields\n    const headers = [\n      'email', 'first_name', 'last_name', 'phone', 'department_id',\n      'hire_date', 'employment_type', 'salary', 'role', 'is_active'\n    ]\n\n    const csvContent = headers.join(',') + '\\n' +\n      '<EMAIL>,Mario,Rossi,+39 ************,1,2024-01-15,full_time,45000,employee,true\\n' +\n      '<EMAIL>,Giulia,Bianchi,+39 ************,2,2024-02-01,part_time,30000,manager,true'\n\n    // Create and download file\n    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })\n    const link = document.createElement('a')\n    const url = URL.createObjectURL(blob)\n    link.setAttribute('href', url)\n    link.setAttribute('download', 'template_dipendenti.csv')\n    link.style.visibility = 'hidden'\n    document.body.appendChild(link)\n    link.click()\n    document.body.removeChild(link)\n\n    showStatusMessage('success', 'Template scaricato', 'Il template CSV è stato scaricato con successo.')\n  } catch (err) {\n    console.error('Error downloading template:', err)\n    showStatusMessage('error', 'Errore download', 'Impossibile scaricare il template.')\n  } finally {\n    loading.value.template = false\n  }\n}\n\nconst handleFileUpload = async (event) => {\n  const file = event.target.files[0]\n  if (!file) return\n\n  loading.value.import = true\n  importProgress.value.show = true\n  importProgress.value.processed = 0\n\n  try {\n    const formData = new FormData()\n    formData.append('file', file)\n\n    const response = await fetch('/api/personnel/import', {\n      method: 'POST',\n      credentials: 'include',\n      body: formData\n    })\n\n    if (!response.ok) {\n      throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n\n    if (data.success) {\n      showStatusMessage('success', 'Import completato',\n        `Importati ${data.data.imported} dipendenti su ${data.data.total} righe processate.`,\n        data.data.errors || [])\n      emit('operation-completed', 'import')\n    } else {\n      throw new Error(data.message || 'Errore durante l\\'import')\n    }\n  } catch (err) {\n    console.error('Error importing file:', err)\n    showStatusMessage('error', 'Errore import', err.message)\n  } finally {\n    loading.value.import = false\n    importProgress.value.show = false\n    // Reset file input\n    event.target.value = ''\n  }\n}\n\nconst exportData = async (type) => {\n  loading.value.export = true\n\n  try {\n    const endpoint = type === 'full' ? '/api/personnel/export' : '/api/personnel/export/contacts'\n\n    const response = await fetch(endpoint, {\n      credentials: 'include'\n    })\n\n    if (!response.ok) {\n      throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n    }\n\n    // Get filename from response headers or use default\n    const contentDisposition = response.headers.get('content-disposition')\n    let filename = `export_${type}_${new Date().toISOString().split('T')[0]}.csv`\n\n    if (contentDisposition) {\n      const filenameMatch = contentDisposition.match(/filename=\"(.+)\"/)\n      if (filenameMatch) {\n        filename = filenameMatch[1]\n      }\n    }\n\n    const blob = await response.blob()\n    const url = window.URL.createObjectURL(blob)\n    const link = document.createElement('a')\n    link.href = url\n    link.download = filename\n    document.body.appendChild(link)\n    link.click()\n    document.body.removeChild(link)\n    window.URL.revokeObjectURL(url)\n\n    showStatusMessage('success', 'Export completato', `I dati sono stati esportati in ${filename}`)\n  } catch (err) {\n    console.error('Error exporting data:', err)\n    showStatusMessage('error', 'Errore export', err.message)\n  } finally {\n    loading.value.export = false\n  }\n}\n\nconst verifyDataIntegrity = async () => {\n  loading.value.verify = true\n\n  try {\n    const response = await fetch('/api/personnel/verify', {\n      credentials: 'include'\n    })\n\n    if (!response.ok) {\n      throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n\n    if (data.success) {\n      const issues = data.data.issues || []\n      if (issues.length === 0) {\n        showStatusMessage('success', 'Verifica completata', 'Nessun problema di integrità rilevato.')\n      } else {\n        showStatusMessage('error', 'Problemi rilevati',\n          `Trovati ${issues.length} problemi di integrità dati.`, issues)\n      }\n    } else {\n      throw new Error(data.message || 'Errore durante la verifica')\n    }\n  } catch (err) {\n    console.error('Error verifying data:', err)\n    showStatusMessage('error', 'Errore verifica', err.message)\n  } finally {\n    loading.value.verify = false\n  }\n}\n\nconst showStatusMessage = (type, title, message, details = []) => {\n  statusMessage.value = { type, title, message, details }\n\n  // Auto-hide success messages after 5 seconds\n  if (type === 'success') {\n    setTimeout(() => {\n      statusMessage.value = null\n    }, 5000)\n  }\n}\n\nconst handleCustomExport = (config) => {\n  showExportModal.value = false\n  // Handle custom export configuration\n  console.log('Custom export config:', config)\n}\n\nconst handleBulkOperationCompleted = (operation) => {\n  showStatusMessage('success', 'Operazione completata',\n    `L'operazione di massa \"${operation}\" è stata completata con successo.`)\n  emit('operation-completed', operation)\n}\n\nconst handleCleanupCompleted = (operation) => {\n  showStatusMessage('success', 'Pulizia completata',\n    `L'operazione di pulizia \"${operation}\" è stata completata con successo.`)\n  emit('operation-completed', operation)\n}\n</script>\n"}
{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/personnel/PersonnelDirectory.vue"}, "originalCode": "<template>\n  <div class=\"personnel-directory\">\n    <!-- Header -->\n    <div class=\"flex justify-between items-center mb-6\">\n      <div class=\"flex items-center\">\n        <svg class=\"w-8 h-8 text-blue-600 dark:text-blue-400 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n          <path fill-rule=\"evenodd\" d=\"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z\" clip-rule=\"evenodd\"></path>\n        </svg>\n        <div>\n          <h1 class=\"text-2xl font-bold text-gray-900 dark:text-white\">Directory Aziendale</h1>\n          <p class=\"text-gray-600 dark:text-gray-400 mt-1\">Trova e contatta i colleghi</p>\n        </div>\n      </div>\n      <router-link to=\"/app/personnel\"\n                   class=\"text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200\">\n        <svg class=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n          <path fill-rule=\"evenodd\" d=\"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z\" clip-rule=\"evenodd\"></path>\n        </svg>\n      </router-link>\n    </div>\n\n    <!-- Search and Filters -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-4 mb-6\">\n      <div class=\"flex flex-col lg:flex-row gap-4\">\n        <!-- Search -->\n        <div class=\"flex-1\">\n          <div class=\"relative\">\n            <svg class=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fill-rule=\"evenodd\" d=\"M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z\" clip-rule=\"evenodd\"></path>\n            </svg>\n            <input v-model=\"searchQuery\"\n                   @input=\"debouncedSearch\"\n                   type=\"text\"\n                   placeholder=\"Cerca per nome, email, posizione...\"\n                   class=\"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\">\n          </div>\n        </div>\n\n        <!-- Department Filter -->\n        <div class=\"lg:w-48\">\n          <select v-model=\"selectedDepartment\"\n                  @change=\"applyFilters\"\n                  class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\">\n            <option value=\"\">Tutti i dipartimenti</option>\n            <option v-for=\"dept in departments\" :key=\"dept.id\" :value=\"dept.id\">\n              {{ dept.name }}\n            </option>\n          </select>\n        </div>\n\n        <!-- View Toggle -->\n        <div class=\"flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1\">\n          <button @click=\"viewMode = 'grid'\"\n                  :class=\"[\n                    'px-3 py-1 rounded-md text-sm font-medium transition-colors duration-200',\n                    viewMode === 'grid'\n                      ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'\n                      : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'\n                  ]\">\n            <svg class=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path d=\"M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z\"></path>\n            </svg>\n          </button>\n          <button @click=\"viewMode = 'list'\"\n                  :class=\"[\n                    'px-3 py-1 rounded-md text-sm font-medium transition-colors duration-200',\n                    viewMode === 'list'\n                      ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'\n                      : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'\n                  ]\">\n            <svg class=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fill-rule=\"evenodd\" d=\"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z\" clip-rule=\"evenodd\"></path>\n            </svg>\n          </button>\n        </div>\n      </div>\n\n      <!-- Active Filters -->\n      <div v-if=\"hasActiveFilters\" class=\"flex items-center gap-2 mt-4 pt-4 border-t border-gray-200 dark:border-gray-700\">\n        <span class=\"text-sm text-gray-600 dark:text-gray-400\">Filtri attivi:</span>\n        <span v-if=\"searchQuery\"\n              class=\"inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200\">\n          <svg class=\"w-3 h-3 mr-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fill-rule=\"evenodd\" d=\"M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z\" clip-rule=\"evenodd\"></path>\n          </svg>\n          \"{{ searchQuery }}\"\n        </span>\n        <span v-if=\"selectedDepartment\"\n              class=\"inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200\">\n          <svg class=\"w-3 h-3 mr-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fill-rule=\"evenodd\" d=\"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z\" clip-rule=\"evenodd\"></path>\n          </svg>\n          {{ getDepartmentName(selectedDepartment) }}\n        </span>\n        <button @click=\"clearFilters\"\n                class=\"text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 underline\">\n          Pulisci filtri\n        </button>\n      </div>\n    </div>\n\n    <!-- Loading State -->\n    <div v-if=\"loading\" class=\"flex justify-center items-center h-64\">\n      <div class=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n    </div>\n\n    <!-- Error State -->\n    <div v-else-if=\"error\" class=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6\">\n      <div class=\"flex\">\n        <svg class=\"w-5 h-5 text-red-400 mr-2 mt-0.5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n          <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clip-rule=\"evenodd\"></path>\n        </svg>\n        <div>\n          <h3 class=\"text-sm font-medium text-red-800 dark:text-red-200\">Errore nel caricamento della directory</h3>\n          <p class=\"text-sm text-red-700 dark:text-red-300 mt-1\">{{ error }}</p>\n        </div>\n      </div>\n    </div>\n\n    <!-- Directory Content -->\n    <div v-else-if=\"filteredEmployees.length > 0\">\n      <!-- Grid View -->\n      <div v-if=\"viewMode === 'grid'\" class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n        <div v-for=\"employee in filteredEmployees\" :key=\"employee.id\"\n             class=\"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden hover:shadow-lg transition-shadow duration-200\">\n          <!-- Employee Card -->\n          <div class=\"p-6\">\n            <div class=\"flex items-center space-x-4\">\n              <div class=\"flex-shrink-0\">\n                <div class=\"w-16 h-16 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center\">\n                  <img v-if=\"employee.profile_image\"\n                       :src=\"employee.profile_image\"\n                       :alt=\"employee.full_name\"\n                       class=\"w-16 h-16 rounded-full object-cover\">\n                  <svg v-else class=\"w-8 h-8 text-gray-600 dark:text-gray-300\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fill-rule=\"evenodd\" d=\"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\" clip-rule=\"evenodd\"></path>\n                  </svg>\n                </div>\n              </div>\n              <div class=\"flex-1 min-w-0\">\n                <h3 class=\"text-lg font-medium text-gray-900 dark:text-white truncate\">\n                  {{ employee.full_name }}\n                </h3>\n                <p class=\"text-sm text-gray-500 dark:text-gray-400 truncate\">\n                  {{ employee.position || 'Posizione non specificata' }}\n                </p>\n                <p v-if=\"employee.department\" class=\"text-sm text-gray-500 dark:text-gray-400 truncate\">\n                  {{ employee.department.name }}\n                </p>\n              </div>\n            </div>\n\n            <!-- Contact Info -->\n            <div class=\"mt-4 space-y-2\">\n              <div class=\"flex items-center text-sm text-gray-600 dark:text-gray-400\">\n                <svg class=\"w-4 h-4 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path d=\"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z\"></path>\n                  <path d=\"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z\"></path>\n                </svg>\n                <a :href=\"`mailto:${employee.email}`\" class=\"hover:text-blue-600 dark:hover:text-blue-400 truncate\">\n                  {{ employee.email }}\n                </a>\n              </div>\n              <div v-if=\"employee.phone\" class=\"flex items-center text-sm text-gray-600 dark:text-gray-400\">\n                <svg class=\"w-4 h-4 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path d=\"M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z\"></path>\n                </svg>\n                <a :href=\"`tel:${employee.phone}`\" class=\"hover:text-blue-600 dark:hover:text-blue-400\">\n                  {{ employee.phone }}\n                </a>\n              </div>\n            </div>\n\n            <!-- Actions -->\n            <div class=\"mt-4 flex space-x-2\">\n              <router-link :to=\"`/app/personnel/${employee.id}`\"\n                           class=\"flex-1 bg-blue-600 hover:bg-blue-700 text-white text-center px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200\">\n                Visualizza Profilo\n              </router-link>\n              <a :href=\"`mailto:${employee.email}`\"\n                 class=\"bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200\">\n                <svg class=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path d=\"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z\"></path>\n                  <path d=\"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z\"></path>\n                </svg>\n              </a>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- List View -->\n      <div v-else class=\"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden\">\n        <div class=\"overflow-x-auto\">\n          <table class=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\n            <thead class=\"bg-gray-50 dark:bg-gray-700\">\n              <tr>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                  Dipendente\n                </th>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                  Posizione\n                </th>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                  Dipartimento\n                </th>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                  Contatti\n                </th>\n                <th class=\"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                  Azioni\n                </th>\n              </tr>\n            </thead>\n            <tbody class=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\n              <tr v-for=\"employee in filteredEmployees\" :key=\"employee.id\"\n                  class=\"hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200\">\n                <!-- Employee Info -->\n                <td class=\"px-6 py-4 whitespace-nowrap\">\n                  <div class=\"flex items-center\">\n                    <div class=\"flex-shrink-0 w-10 h-10\">\n                      <div class=\"w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center\">\n                        <img v-if=\"employee.profile_image\"\n                             :src=\"employee.profile_image\"\n                             :alt=\"employee.full_name\"\n                             class=\"w-10 h-10 rounded-full object-cover\">\n                        <svg v-else class=\"w-5 h-5 text-gray-600 dark:text-gray-300\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                          <path fill-rule=\"evenodd\" d=\"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\" clip-rule=\"evenodd\"></path>\n                        </svg>\n                      </div>\n                    </div>\n                    <div class=\"ml-4\">\n                      <div class=\"text-sm font-medium text-gray-900 dark:text-white\">\n                        {{ employee.full_name }}\n                      </div>\n                      <div class=\"text-sm text-gray-500 dark:text-gray-400\">\n                        {{ employee.email }}\n                      </div>\n                    </div>\n                  </div>\n                </td>\n\n                <!-- Position -->\n                <td class=\"px-6 py-4 whitespace-nowrap\">\n                  <div class=\"text-sm text-gray-900 dark:text-white\">\n                    {{ employee.position || 'Posizione non specificata' }}\n                  </div>\n                </td>\n\n                <!-- Department -->\n                <td class=\"px-6 py-4 whitespace-nowrap\">\n                  <div class=\"text-sm text-gray-900 dark:text-white\">\n                    {{ employee.department?.name || 'Nessun dipartimento' }}\n                  </div>\n                </td>\n\n                <!-- Contacts -->\n                <td class=\"px-6 py-4 whitespace-nowrap\">\n                  <div class=\"flex space-x-2\">\n                    <a :href=\"`mailto:${employee.email}`\"\n                       class=\"text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300\">\n                      <svg class=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path d=\"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z\"></path>\n                        <path d=\"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z\"></path>\n                      </svg>\n                    </a>\n                    <a v-if=\"employee.phone\"\n                       :href=\"`tel:${employee.phone}`\"\n                       class=\"text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300\">\n                      <svg class=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path d=\"M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z\"></path>\n                      </svg>\n                    </a>\n                  </div>\n                </td>\n\n                <!-- Actions -->\n                <td class=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                  <router-link :to=\"`/app/personnel/${employee.id}`\"\n                               class=\"text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300\">\n                    Visualizza Profilo\n                  </router-link>\n                </td>\n              </tr>\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n\n    <!-- Empty State -->\n    <div v-else class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-12 text-center\">\n      <svg class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\n      </svg>\n      <h3 class=\"mt-2 text-sm font-medium text-gray-900 dark:text-white\">Nessun dipendente trovato</h3>\n      <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\n        {{ hasActiveFilters ? 'Prova a modificare i filtri di ricerca.' : 'Non ci sono dipendenti nella directory.' }}\n      </p>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted } from 'vue'\nimport { usePersonnelStore } from '@/stores/personnel'\n\n// Stores\nconst personnelStore = usePersonnelStore()\n\n// Reactive state\nconst employees = ref([])\nconst departments = ref([])\nconst loading = ref(false)\nconst error = ref(null)\nconst searchQuery = ref('')\nconst selectedDepartment = ref('')\nconst viewMode = ref('grid') // 'grid' or 'list'\n\n// Computed properties\nconst filteredEmployees = computed(() => {\n  let filtered = employees.value\n\n  // Apply search filter\n  if (searchQuery.value.trim()) {\n    const search = searchQuery.value.toLowerCase()\n    filtered = filtered.filter(emp =>\n      emp.full_name.toLowerCase().includes(search) ||\n      emp.email.toLowerCase().includes(search) ||\n      (emp.position && emp.position.toLowerCase().includes(search)) ||\n      (emp.department && emp.department.name.toLowerCase().includes(search))\n    )\n  }\n\n  // Apply department filter\n  if (selectedDepartment.value) {\n    filtered = filtered.filter(emp => emp.department_id == selectedDepartment.value)\n  }\n\n  return filtered\n})\n\nconst hasActiveFilters = computed(() => {\n  return searchQuery.value.trim() !== '' || selectedDepartment.value !== ''\n})\n\n// Utility functions\nconst getDepartmentName = (deptId) => {\n  const dept = departments.value.find(d => d.id == deptId)\n  return dept ? dept.name : ''\n}\n\nlet searchTimeout = null\nconst debouncedSearch = () => {\n  clearTimeout(searchTimeout)\n  searchTimeout = setTimeout(() => {\n    // Search is reactive, no need to do anything here\n  }, 300)\n}\n\n// API functions\nconst fetchEmployees = async () => {\n  loading.value = true\n  error.value = null\n\n  try {\n    const response = await fetch('/api/personnel/users', {\n      credentials: 'include'\n    })\n\n    if (!response.ok) {\n      throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n\n    if (data.success) {\n      employees.value = data.data.users || []\n    } else {\n      throw new Error(data.message || 'Errore nel caricamento dei dipendenti')\n    }\n  } catch (err) {\n    console.error('Error fetching employees:', err)\n    error.value = err.message\n  } finally {\n    loading.value = false\n  }\n}\n\nconst fetchDepartments = async () => {\n  try {\n    const response = await fetch('/api/personnel/departments', {\n      credentials: 'include'\n    })\n\n    if (!response.ok) {\n      throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n\n    if (data.success) {\n      departments.value = data.data.departments || []\n    }\n  } catch (err) {\n    console.error('Error fetching departments:', err)\n    // Non bloccare l'interfaccia per errori sui dipartimenti\n  }\n}\n\n// Event handlers\nconst applyFilters = () => {\n  // Filters are reactive, no need to do anything here\n}\n\nconst clearFilters = () => {\n  searchQuery.value = ''\n  selectedDepartment.value = ''\n}\n\n// Lifecycle\nonMounted(async () => {\n  await Promise.all([\n    fetchEmployees(),\n    fetchDepartments()\n  ])\n})\n</script>", "modifiedCode": "<template>\n  <div class=\"personnel-directory\">\n    <!-- Header -->\n    <div class=\"flex justify-between items-center mb-6\">\n      <div class=\"flex items-center\">\n        <svg class=\"w-8 h-8 text-blue-600 dark:text-blue-400 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n          <path fill-rule=\"evenodd\" d=\"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z\" clip-rule=\"evenodd\"></path>\n        </svg>\n        <div>\n          <h1 class=\"text-2xl font-bold text-gray-900 dark:text-white\">Directory Aziendale</h1>\n          <p class=\"text-gray-600 dark:text-gray-400 mt-1\">Trova e contatta i colleghi</p>\n        </div>\n      </div>\n      <div class=\"flex space-x-3\">\n        <router-link\n          to=\"/app/personnel/admin\"\n          class=\"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700\"\n        >\n          <svg class=\"w-4 h-4 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fill-rule=\"evenodd\" d=\"M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z\" clip-rule=\"evenodd\"></path>\n          </svg>\n          Amministrazione\n        </router-link>\n        <router-link\n          to=\"/app/personnel/orgchart\"\n          class=\"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700\"\n        >\n          <svg class=\"w-4 h-4 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fill-rule=\"evenodd\" d=\"M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 0l-2 2a1 1 0 101.414 1.414L8 10.414l1.293 1.293a1 1 0 001.414 0l4-4z\" clip-rule=\"evenodd\"></path>\n          </svg>\n          Organigramma\n        </router-link>\n        <router-link\n          to=\"/app/personnel/skills\"\n          class=\"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700\"\n        >\n          <svg class=\"w-4 h-4 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"></path>\n          </svg>\n          Competenze\n        </router-link>\n      </div>\n    </div>\n\n    <!-- Search and Filters -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-4 mb-6\">\n      <div class=\"flex flex-col lg:flex-row gap-4\">\n        <!-- Search -->\n        <div class=\"flex-1\">\n          <div class=\"relative\">\n            <svg class=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fill-rule=\"evenodd\" d=\"M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z\" clip-rule=\"evenodd\"></path>\n            </svg>\n            <input v-model=\"searchQuery\"\n                   @input=\"debouncedSearch\"\n                   type=\"text\"\n                   placeholder=\"Cerca per nome, email, posizione...\"\n                   class=\"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\">\n          </div>\n        </div>\n\n        <!-- Department Filter -->\n        <div class=\"lg:w-48\">\n          <select v-model=\"selectedDepartment\"\n                  @change=\"applyFilters\"\n                  class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\">\n            <option value=\"\">Tutti i dipartimenti</option>\n            <option v-for=\"dept in departments\" :key=\"dept.id\" :value=\"dept.id\">\n              {{ dept.name }}\n            </option>\n          </select>\n        </div>\n\n        <!-- View Toggle -->\n        <div class=\"flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1\">\n          <button @click=\"viewMode = 'grid'\"\n                  :class=\"[\n                    'px-3 py-1 rounded-md text-sm font-medium transition-colors duration-200',\n                    viewMode === 'grid'\n                      ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'\n                      : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'\n                  ]\">\n            <svg class=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path d=\"M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z\"></path>\n            </svg>\n          </button>\n          <button @click=\"viewMode = 'list'\"\n                  :class=\"[\n                    'px-3 py-1 rounded-md text-sm font-medium transition-colors duration-200',\n                    viewMode === 'list'\n                      ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'\n                      : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'\n                  ]\">\n            <svg class=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fill-rule=\"evenodd\" d=\"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z\" clip-rule=\"evenodd\"></path>\n            </svg>\n          </button>\n        </div>\n      </div>\n\n      <!-- Active Filters -->\n      <div v-if=\"hasActiveFilters\" class=\"flex items-center gap-2 mt-4 pt-4 border-t border-gray-200 dark:border-gray-700\">\n        <span class=\"text-sm text-gray-600 dark:text-gray-400\">Filtri attivi:</span>\n        <span v-if=\"searchQuery\"\n              class=\"inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200\">\n          <svg class=\"w-3 h-3 mr-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fill-rule=\"evenodd\" d=\"M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z\" clip-rule=\"evenodd\"></path>\n          </svg>\n          \"{{ searchQuery }}\"\n        </span>\n        <span v-if=\"selectedDepartment\"\n              class=\"inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200\">\n          <svg class=\"w-3 h-3 mr-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fill-rule=\"evenodd\" d=\"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z\" clip-rule=\"evenodd\"></path>\n          </svg>\n          {{ getDepartmentName(selectedDepartment) }}\n        </span>\n        <button @click=\"clearFilters\"\n                class=\"text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 underline\">\n          Pulisci filtri\n        </button>\n      </div>\n    </div>\n\n    <!-- Loading State -->\n    <div v-if=\"loading\" class=\"flex justify-center items-center h-64\">\n      <div class=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n    </div>\n\n    <!-- Error State -->\n    <div v-else-if=\"error\" class=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6\">\n      <div class=\"flex\">\n        <svg class=\"w-5 h-5 text-red-400 mr-2 mt-0.5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n          <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clip-rule=\"evenodd\"></path>\n        </svg>\n        <div>\n          <h3 class=\"text-sm font-medium text-red-800 dark:text-red-200\">Errore nel caricamento della directory</h3>\n          <p class=\"text-sm text-red-700 dark:text-red-300 mt-1\">{{ error }}</p>\n        </div>\n      </div>\n    </div>\n\n    <!-- Directory Content -->\n    <div v-else-if=\"filteredEmployees.length > 0\">\n      <!-- Grid View -->\n      <div v-if=\"viewMode === 'grid'\" class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n        <div v-for=\"employee in filteredEmployees\" :key=\"employee.id\"\n             class=\"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden hover:shadow-lg transition-shadow duration-200\">\n          <!-- Employee Card -->\n          <div class=\"p-6\">\n            <div class=\"flex items-center space-x-4\">\n              <div class=\"flex-shrink-0\">\n                <div class=\"w-16 h-16 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center\">\n                  <img v-if=\"employee.profile_image\"\n                       :src=\"employee.profile_image\"\n                       :alt=\"employee.full_name\"\n                       class=\"w-16 h-16 rounded-full object-cover\">\n                  <svg v-else class=\"w-8 h-8 text-gray-600 dark:text-gray-300\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fill-rule=\"evenodd\" d=\"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\" clip-rule=\"evenodd\"></path>\n                  </svg>\n                </div>\n              </div>\n              <div class=\"flex-1 min-w-0\">\n                <h3 class=\"text-lg font-medium text-gray-900 dark:text-white truncate\">\n                  {{ employee.full_name }}\n                </h3>\n                <p class=\"text-sm text-gray-500 dark:text-gray-400 truncate\">\n                  {{ employee.position || 'Posizione non specificata' }}\n                </p>\n                <p v-if=\"employee.department\" class=\"text-sm text-gray-500 dark:text-gray-400 truncate\">\n                  {{ employee.department.name }}\n                </p>\n              </div>\n            </div>\n\n            <!-- Contact Info -->\n            <div class=\"mt-4 space-y-2\">\n              <div class=\"flex items-center text-sm text-gray-600 dark:text-gray-400\">\n                <svg class=\"w-4 h-4 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path d=\"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z\"></path>\n                  <path d=\"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z\"></path>\n                </svg>\n                <a :href=\"`mailto:${employee.email}`\" class=\"hover:text-blue-600 dark:hover:text-blue-400 truncate\">\n                  {{ employee.email }}\n                </a>\n              </div>\n              <div v-if=\"employee.phone\" class=\"flex items-center text-sm text-gray-600 dark:text-gray-400\">\n                <svg class=\"w-4 h-4 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path d=\"M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z\"></path>\n                </svg>\n                <a :href=\"`tel:${employee.phone}`\" class=\"hover:text-blue-600 dark:hover:text-blue-400\">\n                  {{ employee.phone }}\n                </a>\n              </div>\n            </div>\n\n            <!-- Actions -->\n            <div class=\"mt-4 flex space-x-2\">\n              <router-link :to=\"`/app/personnel/${employee.id}`\"\n                           class=\"flex-1 bg-blue-600 hover:bg-blue-700 text-white text-center px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200\">\n                Visualizza Profilo\n              </router-link>\n              <a :href=\"`mailto:${employee.email}`\"\n                 class=\"bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200\">\n                <svg class=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path d=\"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z\"></path>\n                  <path d=\"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z\"></path>\n                </svg>\n              </a>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- List View -->\n      <div v-else class=\"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden\">\n        <div class=\"overflow-x-auto\">\n          <table class=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\n            <thead class=\"bg-gray-50 dark:bg-gray-700\">\n              <tr>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                  Dipendente\n                </th>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                  Posizione\n                </th>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                  Dipartimento\n                </th>\n                <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                  Contatti\n                </th>\n                <th class=\"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                  Azioni\n                </th>\n              </tr>\n            </thead>\n            <tbody class=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\n              <tr v-for=\"employee in filteredEmployees\" :key=\"employee.id\"\n                  class=\"hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200\">\n                <!-- Employee Info -->\n                <td class=\"px-6 py-4 whitespace-nowrap\">\n                  <div class=\"flex items-center\">\n                    <div class=\"flex-shrink-0 w-10 h-10\">\n                      <div class=\"w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center\">\n                        <img v-if=\"employee.profile_image\"\n                             :src=\"employee.profile_image\"\n                             :alt=\"employee.full_name\"\n                             class=\"w-10 h-10 rounded-full object-cover\">\n                        <svg v-else class=\"w-5 h-5 text-gray-600 dark:text-gray-300\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                          <path fill-rule=\"evenodd\" d=\"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\" clip-rule=\"evenodd\"></path>\n                        </svg>\n                      </div>\n                    </div>\n                    <div class=\"ml-4\">\n                      <div class=\"text-sm font-medium text-gray-900 dark:text-white\">\n                        {{ employee.full_name }}\n                      </div>\n                      <div class=\"text-sm text-gray-500 dark:text-gray-400\">\n                        {{ employee.email }}\n                      </div>\n                    </div>\n                  </div>\n                </td>\n\n                <!-- Position -->\n                <td class=\"px-6 py-4 whitespace-nowrap\">\n                  <div class=\"text-sm text-gray-900 dark:text-white\">\n                    {{ employee.position || 'Posizione non specificata' }}\n                  </div>\n                </td>\n\n                <!-- Department -->\n                <td class=\"px-6 py-4 whitespace-nowrap\">\n                  <div class=\"text-sm text-gray-900 dark:text-white\">\n                    {{ employee.department?.name || 'Nessun dipartimento' }}\n                  </div>\n                </td>\n\n                <!-- Contacts -->\n                <td class=\"px-6 py-4 whitespace-nowrap\">\n                  <div class=\"flex space-x-2\">\n                    <a :href=\"`mailto:${employee.email}`\"\n                       class=\"text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300\">\n                      <svg class=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path d=\"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z\"></path>\n                        <path d=\"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z\"></path>\n                      </svg>\n                    </a>\n                    <a v-if=\"employee.phone\"\n                       :href=\"`tel:${employee.phone}`\"\n                       class=\"text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300\">\n                      <svg class=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path d=\"M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z\"></path>\n                      </svg>\n                    </a>\n                  </div>\n                </td>\n\n                <!-- Actions -->\n                <td class=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                  <router-link :to=\"`/app/personnel/${employee.id}`\"\n                               class=\"text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300\">\n                    Visualizza Profilo\n                  </router-link>\n                </td>\n              </tr>\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n\n    <!-- Empty State -->\n    <div v-else class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-12 text-center\">\n      <svg class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\n      </svg>\n      <h3 class=\"mt-2 text-sm font-medium text-gray-900 dark:text-white\">Nessun dipendente trovato</h3>\n      <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\n        {{ hasActiveFilters ? 'Prova a modificare i filtri di ricerca.' : 'Non ci sono dipendenti nella directory.' }}\n      </p>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted } from 'vue'\nimport { usePersonnelStore } from '@/stores/personnel'\n\n// Stores\nconst personnelStore = usePersonnelStore()\n\n// Reactive state\nconst employees = ref([])\nconst departments = ref([])\nconst loading = ref(false)\nconst error = ref(null)\nconst searchQuery = ref('')\nconst selectedDepartment = ref('')\nconst viewMode = ref('grid') // 'grid' or 'list'\n\n// Computed properties\nconst filteredEmployees = computed(() => {\n  let filtered = employees.value\n\n  // Apply search filter\n  if (searchQuery.value.trim()) {\n    const search = searchQuery.value.toLowerCase()\n    filtered = filtered.filter(emp =>\n      emp.full_name.toLowerCase().includes(search) ||\n      emp.email.toLowerCase().includes(search) ||\n      (emp.position && emp.position.toLowerCase().includes(search)) ||\n      (emp.department && emp.department.name.toLowerCase().includes(search))\n    )\n  }\n\n  // Apply department filter\n  if (selectedDepartment.value) {\n    filtered = filtered.filter(emp => emp.department_id == selectedDepartment.value)\n  }\n\n  return filtered\n})\n\nconst hasActiveFilters = computed(() => {\n  return searchQuery.value.trim() !== '' || selectedDepartment.value !== ''\n})\n\n// Utility functions\nconst getDepartmentName = (deptId) => {\n  const dept = departments.value.find(d => d.id == deptId)\n  return dept ? dept.name : ''\n}\n\nlet searchTimeout = null\nconst debouncedSearch = () => {\n  clearTimeout(searchTimeout)\n  searchTimeout = setTimeout(() => {\n    // Search is reactive, no need to do anything here\n  }, 300)\n}\n\n// API functions\nconst fetchEmployees = async () => {\n  loading.value = true\n  error.value = null\n\n  try {\n    const response = await fetch('/api/personnel/users', {\n      credentials: 'include'\n    })\n\n    if (!response.ok) {\n      throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n\n    if (data.success) {\n      employees.value = data.data.users || []\n    } else {\n      throw new Error(data.message || 'Errore nel caricamento dei dipendenti')\n    }\n  } catch (err) {\n    console.error('Error fetching employees:', err)\n    error.value = err.message\n  } finally {\n    loading.value = false\n  }\n}\n\nconst fetchDepartments = async () => {\n  try {\n    const response = await fetch('/api/personnel/departments', {\n      credentials: 'include'\n    })\n\n    if (!response.ok) {\n      throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n\n    if (data.success) {\n      departments.value = data.data.departments || []\n    }\n  } catch (err) {\n    console.error('Error fetching departments:', err)\n    // Non bloccare l'interfaccia per errori sui dipartimenti\n  }\n}\n\n// Event handlers\nconst applyFilters = () => {\n  // Filters are reactive, no need to do anything here\n}\n\nconst clearFilters = () => {\n  searchQuery.value = ''\n  selectedDepartment.value = ''\n}\n\n// Lifecycle\nonMounted(async () => {\n  await Promise.all([\n    fetchEmployees(),\n    fetchDepartments()\n  ])\n})\n</script>"}
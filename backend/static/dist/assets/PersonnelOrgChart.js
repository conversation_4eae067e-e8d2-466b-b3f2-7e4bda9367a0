import{r as f,f as E,b as V,c as s,o as r,j as e,g as c,n as B,t as n,F as $,k as z,h as L,m as k,A,v as S,x as I,a as q,i as O,l as Q}from"./vendor.js";import{_ as j}from"./app.js";const F={class:"department-node"},P={class:"department-header bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm p-4 mb-4 min-w-80"},R={class:"flex items-center justify-between"},G={class:"flex-1"},U={class:"flex items-center"},J={class:"flex items-center"},K={class:"text-lg font-semibold text-gray-900 dark:text-white"},W={key:0,class:"text-sm text-gray-500 dark:text-gray-400"},X={class:"flex items-center mt-1 space-x-4 text-xs text-gray-500 dark:text-gray-400"},Y={key:0},Z={key:0,class:"ml-4 flex items-center"},ee={class:"text-right mr-3"},te={class:"text-sm font-medium text-gray-900 dark:text-white"},re={key:0,class:"mt-4 pt-4 border-t border-gray-200 dark:border-gray-700"},se={class:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3"},ae=["onClick"],le={class:"flex-1 min-w-0"},de={class:"text-sm font-medium text-gray-900 dark:text-white truncate"},ne={class:"text-xs text-gray-500 dark:text-gray-400 truncate"},oe={key:0,class:"ml-2"},ie={key:0,class:"mt-3 text-center"},ue={key:0,class:"ml-8 space-y-4"},me={class:"relative"},M=6,ce={__name:"DepartmentNode",props:{department:{type:Object,required:!0},expanded:{type:Set,required:!0},searchQuery:{type:String,default:""}},emits:["toggle-node","employee-click"],setup(a,{emit:C}){const x=a,b=C,g=f(!1),u=E(()=>x.expanded.has(x.department.id)),l=E(()=>g.value||x.department.employees.length<=M?x.department.employees:x.department.employees.slice(0,M)),h=()=>{b("toggle-node",x.department.id)},o=v=>new Intl.NumberFormat("it-IT").format(v);return(v,d)=>{const H=V("DepartmentNode",!0);return r(),s("div",F,[e("div",P,[e("div",R,[e("div",G,[e("div",U,[a.department.subdepartments.length>0?(r(),s("button",{key:0,onClick:h,class:"mr-2 p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"},[(r(),s("svg",{class:B(["w-4 h-4 text-gray-500 transition-transform",u.value?"transform rotate-90":""]),fill:"currentColor",viewBox:"0 0 20 20"},d[3]||(d[3]=[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]),2))])):c("",!0),e("div",J,[d[4]||(d[4]=e("div",{class:"w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mr-3"},[e("svg",{class:"w-6 h-6 text-blue-600 dark:text-blue-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z","clip-rule":"evenodd"})])],-1)),e("div",null,[e("h3",K,n(a.department.name),1),a.department.description?(r(),s("p",W,n(a.department.description),1)):c("",!0),e("div",X,[e("span",null,n(a.department.employee_count)+" dipendenti",1),a.department.budget>0?(r(),s("span",Y,"Budget: €"+n(o(a.department.budget)),1)):c("",!0)])])])])]),a.department.manager?(r(),s("div",Z,[e("div",ee,[e("p",te,n(a.department.manager.full_name),1),d[5]||(d[5]=e("p",{class:"text-xs text-gray-500 dark:text-gray-400"},"Manager",-1))]),d[6]||(d[6]=e("div",{class:"w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-purple-600 dark:text-purple-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z","clip-rule":"evenodd"})])],-1))])):c("",!0)]),a.department.employees.length>0?(r(),s("div",re,[e("div",se,[(r(!0),s($,null,z(l.value,p=>(r(),s("div",{key:p.id,onClick:w=>v.$emit("employee-click",p),class:"flex items-center p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors"},[d[8]||(d[8]=e("div",{class:"w-8 h-8 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center mr-3"},[e("svg",{class:"w-4 h-4 text-gray-500 dark:text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z","clip-rule":"evenodd"})])],-1)),e("div",le,[e("p",de,n(p.full_name),1),e("p",ne,n(p.position||"Dipendente"),1)]),p.is_manager?(r(),s("div",oe,d[7]||(d[7]=[e("span",{class:"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200"}," Manager ",-1)]))):c("",!0)],8,ae))),128))]),a.department.employees.length>M?(r(),s("div",ie,[e("button",{onClick:d[0]||(d[0]=p=>g.value=!g.value),class:"text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"},n(g.value?"Mostra meno":`Mostra altri ${a.department.employees.length-M}`),1)])):c("",!0)])):c("",!0)]),u.value&&a.department.subdepartments.length>0?(r(),s("div",ue,[e("div",me,[d[9]||(d[9]=e("div",{class:"absolute -left-4 top-0 bottom-0 w-px bg-gray-300 dark:bg-gray-600"},null,-1)),d[10]||(d[10]=e("div",{class:"absolute -left-4 top-6 w-4 h-px bg-gray-300 dark:bg-gray-600"},null,-1)),(r(!0),s($,null,z(a.department.subdepartments,p=>(r(),L(H,{key:p.id,department:p,expanded:a.expanded,"search-query":a.searchQuery,onToggleNode:d[1]||(d[1]=w=>v.$emit("toggle-node",w)),onEmployeeClick:d[2]||(d[2]=w=>v.$emit("employee-click",w))},null,8,["department","expanded","search-query"]))),128))])])):c("",!0)])}}},ge=j(ce,[["__scopeId","data-v-31c5ce5d"]]),xe={class:"department-list"},ve={class:"p-6 border-b border-gray-200 dark:border-gray-700"},pe={class:"flex items-center justify-between"},ye={class:"flex items-center"},he={class:"text-xl font-semibold text-gray-900 dark:text-white"},fe={key:0,class:"text-sm text-gray-500 dark:text-gray-400 mt-1"},be={class:"flex items-center mt-2 space-x-6 text-sm text-gray-500 dark:text-gray-400"},ke={class:"flex items-center"},we={key:0,class:"flex items-center"},_e={key:1,class:"flex items-center"},$e={key:0,class:"flex items-center bg-purple-50 dark:bg-purple-900/20 rounded-lg p-3"},ze={class:"text-sm font-medium text-gray-900 dark:text-white"},Ce={class:"text-xs text-gray-500 dark:text-gray-400"},Me={key:0,class:"p-6"},Be={class:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4"},He=["onClick"],De={class:"flex items-center"},Ee={class:"flex-1 min-w-0"},Ve={class:"text-sm font-medium text-gray-900 dark:text-white truncate"},Le={class:"text-xs text-gray-500 dark:text-gray-400 truncate"},je={class:"text-xs text-gray-500 dark:text-gray-400 truncate"},Ne={class:"mt-3 flex items-center justify-between"},Te={class:"flex items-center space-x-2"},qe={key:0,class:"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200"},Ae={key:0,class:"text-xs text-gray-500 dark:text-gray-400"},Se={key:1,class:"border-t border-gray-200 dark:border-gray-700"},Ie={class:"p-6"},Oe={class:"space-y-4"},Qe={__name:"DepartmentList",props:{department:{type:Object,required:!0},searchQuery:{type:String,default:""}},emits:["employee-click"],setup(a,{emit:C}){const x=u=>new Intl.NumberFormat("it-IT").format(u),b=u=>u?new Date(u).toLocaleDateString("it-IT",{year:"numeric",month:"short"}):"",g=u=>({admin:"Admin",manager:"Manager",employee:"Dipendente"})[u]||u;return(u,l)=>{const h=V("DepartmentList",!0);return r(),s("div",xe,[e("div",ve,[e("div",pe,[e("div",ye,[l[4]||(l[4]=e("div",{class:"w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mr-4"},[e("svg",{class:"w-6 h-6 text-blue-600 dark:text-blue-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z","clip-rule":"evenodd"})])],-1)),e("div",null,[e("h3",he,n(a.department.name),1),a.department.description?(r(),s("p",fe,n(a.department.description),1)):c("",!0),e("div",be,[e("span",ke,[l[1]||(l[1]=e("svg",{class:"w-4 h-4 mr-1",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"})],-1)),k(" "+n(a.department.employee_count)+" dipendenti ",1)]),a.department.budget>0?(r(),s("span",we,[l[2]||(l[2]=e("svg",{class:"w-4 h-4 mr-1",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"}),e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.51-1.31c-.562-.649-1.413-1.076-2.353-1.253V5z","clip-rule":"evenodd"})],-1)),k(" Budget: €"+n(x(a.department.budget)),1)])):c("",!0),a.department.subdepartments.length>0?(r(),s("span",_e,[l[3]||(l[3]=e("svg",{class:"w-4 h-4 mr-1",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z","clip-rule":"evenodd"})],-1)),k(" "+n(a.department.subdepartments.length)+" sotto-dipartimenti ",1)])):c("",!0)])])]),a.department.manager?(r(),s("div",$e,[l[6]||(l[6]=e("div",{class:"w-10 h-10 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mr-3"},[e("svg",{class:"w-5 h-5 text-purple-600 dark:text-purple-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z","clip-rule":"evenodd"})])],-1)),e("div",null,[e("p",ze,n(a.department.manager.full_name),1),l[5]||(l[5]=e("p",{class:"text-xs text-gray-500 dark:text-gray-400"},"Manager del Dipartimento",-1)),e("p",Ce,n(a.department.manager.email),1)])])):c("",!0)])]),a.department.employees.length>0?(r(),s("div",Me,[l[8]||(l[8]=e("h4",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Dipendenti",-1)),e("div",Be,[(r(!0),s($,null,z(a.department.employees,o=>(r(),s("div",{key:o.id,onClick:v=>u.$emit("employee-click",o),class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer transition-colors"},[e("div",De,[l[7]||(l[7]=e("div",{class:"w-10 h-10 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center mr-3"},[e("svg",{class:"w-5 h-5 text-gray-500 dark:text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z","clip-rule":"evenodd"})])],-1)),e("div",Ee,[e("p",Ve,n(o.full_name),1),e("p",Le,n(o.position||"Dipendente"),1),e("p",je,n(o.email),1)])]),e("div",Ne,[e("div",Te,[o.is_manager?(r(),s("span",qe," Manager ")):c("",!0),e("span",{class:B(["inline-flex items-center px-2 py-0.5 rounded text-xs font-medium",o.role==="admin"?"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200":o.role==="manager"?"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200":"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"])},n(g(o.role)),3)]),o.hire_date?(r(),s("div",Ae,n(b(o.hire_date)),1)):c("",!0)])],8,He))),128))])])):c("",!0),a.department.subdepartments.length>0?(r(),s("div",Se,[e("div",Ie,[l[9]||(l[9]=e("h4",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Sotto-dipartimenti",-1)),e("div",Oe,[(r(!0),s($,null,z(a.department.subdepartments,o=>(r(),L(h,{key:o.id,department:o,"search-query":a.searchQuery,onEmployeeClick:l[0]||(l[0]=v=>u.$emit("employee-click",v)),class:"ml-6 border-l-2 border-gray-200 dark:border-gray-700 pl-6"},null,8,["department","search-query"]))),128))])])])):c("",!0)])}}},Fe=j(Qe,[["__scopeId","data-v-d1eb3eb4"]]),Pe={class:"space-y-6"},Re={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},Ge={class:"mt-4 sm:mt-0 flex items-center space-x-3"},Ue={class:"relative"},Je={class:"flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1"},Ke={key:0,class:"grid grid-cols-1 md:grid-cols-3 gap-6"},We={class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"},Xe={class:"flex items-center"},Ye={class:"ml-4"},Ze={class:"text-2xl font-bold text-gray-900 dark:text-white"},et={class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"},tt={class:"flex items-center"},rt={class:"ml-4"},st={class:"text-2xl font-bold text-gray-900 dark:text-white"},at={class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"},lt={class:"flex items-center"},dt={class:"ml-4"},nt={class:"text-2xl font-bold text-gray-900 dark:text-white"},ot={key:1,class:"flex justify-center items-center py-12"},it={key:2,class:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4"},ut={class:"flex"},mt={class:"mt-1 text-sm text-red-700 dark:text-red-300"},ct={key:3,class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700"},gt={key:0,class:"p-6"},xt={class:"orgchart-container overflow-x-auto"},vt={class:"orgchart-tree min-w-max"},pt={key:1,class:"divide-y divide-gray-200 dark:divide-gray-700"},yt={key:4,class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-12"},ht={class:"text-center"},ft={class:"mt-6"},bt={__name:"PersonnelOrgChart",setup(a){const C=Q(),x=f(!1),b=f(null),g=f([]),u=f(null),l=f(""),h=f("tree"),o=f(new Set),v=f(!1),d=E(()=>{if(!l.value.trim())return g.value;const i=l.value.toLowerCase(),t=m=>{const y=m.name.toLowerCase().includes(i)||m.description&&m.description.toLowerCase().includes(i),D=m.employees.filter(_=>_.full_name.toLowerCase().includes(i)||_.email.toLowerCase().includes(i)||_.position&&_.position.toLowerCase().includes(i)),T=m.subdepartments.map(t).filter(_=>_!==null);return y||D.length>0||T.length>0?{...m,employees:D.length>0?D:m.employees,subdepartments:T}:null};return g.value.map(t).filter(m=>m!==null)}),H=async()=>{x.value=!0,b.value=null;try{const i=await fetch("/api/personnel/orgchart",{credentials:"include"});if(!i.ok)throw new Error(`HTTP ${i.status}: ${i.statusText}`);const t=await i.json();if(t.success)g.value=t.data.orgchart||[],u.value=t.data.stats||{},g.value.length>0&&g.value.forEach(m=>{o.value.add(m.id)});else throw new Error(t.message||"Errore nel caricamento dell'organigramma")}catch(i){console.error("Error loading org chart:",i),b.value=i.message}finally{x.value=!1}},p=i=>{o.value.has(i)?o.value.delete(i):o.value.add(i)},w=()=>{if(v.value)o.value.clear(),v.value=!1;else{const i=t=>{t.forEach(m=>{o.value.add(m.id),m.subdepartments&&m.subdepartments.length>0&&i(m.subdepartments)})};i(g.value),v.value=!0}},N=i=>{C.push(`/app/personnel/profile/${i.id}`)};return A(()=>{H()}),(i,t)=>{const m=V("router-link");return r(),s("div",Pe,[e("div",Re,[t[6]||(t[6]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white flex items-center"},[e("svg",{class:"w-8 h-8 mr-3 text-blue-600",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z","clip-rule":"evenodd"})]),k(" Organigramma Aziendale ")]),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"}," Struttura organizzativa e gerarchia aziendale ")],-1)),e("div",Ge,[e("div",Ue,[S(e("input",{"onUpdate:modelValue":t[0]||(t[0]=y=>l.value=y),type:"text",placeholder:"Cerca dipendente...",class:"w-64 pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[I,l.value]]),t[3]||(t[3]=e("svg",{class:"absolute left-3 top-2.5 w-5 h-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1))]),e("div",Je,[e("button",{onClick:t[1]||(t[1]=y=>h.value="tree"),class:B(["px-3 py-1 rounded-md text-sm font-medium transition-colors",h.value==="tree"?"bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm":"text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"])},t[4]||(t[4]=[e("svg",{class:"w-4 h-4 mr-1 inline",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z","clip-rule":"evenodd"})],-1),k(" Albero ")]),2),e("button",{onClick:t[2]||(t[2]=y=>h.value="list"),class:B(["px-3 py-1 rounded-md text-sm font-medium transition-colors",h.value==="list"?"bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm":"text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"])},t[5]||(t[5]=[e("svg",{class:"w-4 h-4 mr-1 inline",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z","clip-rule":"evenodd"})],-1),k(" Lista ")]),2)]),e("button",{onClick:w,class:"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200"},n(v.value?"Comprimi Tutto":"Espandi Tutto"),1)])]),u.value?(r(),s("div",Ke,[e("div",We,[e("div",Xe,[t[8]||(t[8]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"w-8 h-8 text-blue-600",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"})])],-1)),e("div",Ye,[t[7]||(t[7]=e("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Dipendenti Totali",-1)),e("p",Ze,n(u.value.total_employees),1)])])]),e("div",et,[e("div",tt,[t[10]||(t[10]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"w-8 h-8 text-green-600",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z","clip-rule":"evenodd"})])],-1)),e("div",rt,[t[9]||(t[9]=e("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Dipartimenti",-1)),e("p",st,n(u.value.total_departments),1)])])]),e("div",at,[e("div",lt,[t[12]||(t[12]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"w-8 h-8 text-purple-600",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z","clip-rule":"evenodd"})])],-1)),e("div",dt,[t[11]||(t[11]=e("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Manager",-1)),e("p",nt,n(u.value.total_managers),1)])])])])):c("",!0),x.value?(r(),s("div",ot,t[13]||(t[13]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):b.value?(r(),s("div",it,[e("div",ut,[t[15]||(t[15]=e("svg",{class:"w-5 h-5 text-red-400 mr-3 mt-0.5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z","clip-rule":"evenodd"})],-1)),e("div",null,[t[14]||(t[14]=e("h3",{class:"text-sm font-medium text-red-800 dark:text-red-200"},"Errore nel caricamento",-1)),e("p",mt,n(b.value),1)])])])):g.value.length>0?(r(),s("div",ct,[h.value==="tree"?(r(),s("div",gt,[e("div",xt,[e("div",vt,[(r(!0),s($,null,z(d.value,y=>(r(),L(ge,{key:y.id,department:y,expanded:o.value,"search-query":l.value,onToggleNode:p,onEmployeeClick:N},null,8,["department","expanded","search-query"]))),128))])])])):(r(),s("div",pt,[(r(!0),s($,null,z(d.value,y=>(r(),s("div",{key:y.id},[q(Fe,{department:y,"search-query":l.value,onEmployeeClick:N},null,8,["department","search-query"])]))),128))]))])):x.value?c("",!0):(r(),s("div",yt,[e("div",ht,[t[17]||(t[17]=e("svg",{class:"mx-auto h-16 w-16 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})],-1)),t[18]||(t[18]=e("h3",{class:"mt-4 text-lg font-medium text-gray-900 dark:text-white"},"Nessun dipartimento configurato",-1)),t[19]||(t[19]=e("p",{class:"mt-2 text-sm text-gray-500 dark:text-gray-400"}," Inizia creando la struttura organizzativa aziendale ",-1)),e("div",ft,[q(m,{to:"/app/personnel/departments",class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},{default:O(()=>t[16]||(t[16]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),k(" Gestisci Dipartimenti ")])),_:1,__:[16]})])])]))])}}},_t=j(bt,[["__scopeId","data-v-56a5903e"]]);export{_t as default};

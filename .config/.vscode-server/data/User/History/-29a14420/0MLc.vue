<template>
  <!-- <PERSON><PERSON> Backdrop -->
  <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" @click="$emit('close')">
    <!-- Modal Container -->
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white dark:bg-gray-800" @click.stop>
      <!-- <PERSON><PERSON> Header -->
      <div class="flex items-center justify-between pb-4 border-b border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white">
          Crea Nuovo Dipendente
        </h3>
        <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <!-- Modal Body -->
      <div class="mt-6">
        <form @submit.prevent="createUser" class="space-y-6">
          <!-- Basic Information -->
          <div>
            <h4 class="text-md font-medium text-gray-900 dark:text-white mb-4">Informazioni Base</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Nome *
                </label>
                <input v-model="form.first_name"
                       type="text"
                       required
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Cognome *
                </label>
                <input v-model="form.last_name"
                       type="text"
                       required
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Username *
                </label>
                <input v-model="form.username"
                       type="text"
                       required
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Email *
                </label>
                <input v-model="form.email"
                       type="email"
                       required
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Telefono
                </label>
                <input v-model="form.phone"
                       type="tel"
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Posizione
                </label>
                <input v-model="form.position"
                       type="text"
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
              </div>
            </div>
          </div>

          <!-- Role and Department -->
          <div>
            <h4 class="text-md font-medium text-gray-900 dark:text-white mb-4">Ruolo e Dipartimento</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Ruolo
                </label>
                <select v-model="form.role"
                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                  <option value="employee">Dipendente</option>
                  <option value="manager">Manager</option>
                  <option value="admin">Admin</option>
                </select>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Dipartimento
                </label>
                <select v-model="form.department_id"
                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                  <option value="">Seleziona dipartimento</option>
                  <option v-for="dept in departments" :key="dept.id" :value="dept.id">
                    {{ dept.name }}
                  </option>
                </select>
              </div>
            </div>
          </div>

          <!-- Contract Information -->
          <div>
            <h4 class="text-md font-medium text-gray-900 dark:text-white mb-4">Informazioni Contrattuali</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Data Assunzione
                </label>
                <input v-model="form.hire_date"
                       type="date"
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Tipo Contratto
                </label>
                <select v-model="form.employment_type"
                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                  <option value="full_time">Tempo pieno</option>
                  <option value="part_time">Part-time</option>
                  <option value="contractor">Consulente</option>
                  <option value="intern">Stagista</option>
                </select>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Modalità Lavoro
                </label>
                <select v-model="form.work_location"
                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                  <option value="">Seleziona modalità</option>
                  <option value="office">Ufficio</option>
                  <option value="remote">Remoto</option>
                  <option value="hybrid">Ibrido</option>
                </select>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Ore Settimanali
                </label>
                <input v-model.number="form.weekly_hours"
                       type="number"
                       step="0.5"
                       min="0"
                       max="60"
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Fine Periodo Prova
                </label>
                <input v-model="form.probation_end_date"
                       type="date"
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Scadenza Contratto
                </label>
                <input v-model="form.contract_end_date"
                       type="date"
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
              </div>
            </div>
          </div>

          <!-- Salary Information -->
          <div>
            <h4 class="text-md font-medium text-gray-900 dark:text-white mb-4">Informazioni Economiche</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Stipendio Annuo
                </label>
                <input v-model.number="form.salary"
                       type="number"
                       step="100"
                       min="0"
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Valuta
                </label>
                <select v-model="form.salary_currency"
                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                  <option value="EUR">EUR</option>
                  <option value="USD">USD</option>
                  <option value="GBP">GBP</option>
                </select>
              </div>
            </div>
          </div>

          <!-- Emergency Contact -->
          <div>
            <h4 class="text-md font-medium text-gray-900 dark:text-white mb-4">Contatto di Emergenza</h4>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Nome Contatto
                </label>
                <input v-model="form.emergency_contact_name"
                       type="text"
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Telefono Emergenza
                </label>
                <input v-model="form.emergency_contact_phone"
                       type="tel"
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Relazione
                </label>
                <input v-model="form.emergency_contact_relationship"
                       type="text"
                       placeholder="es. Coniuge, Genitore..."
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
              </div>
            </div>
          </div>

          <!-- Password Option -->
          <div>
            <h4 class="text-md font-medium text-gray-900 dark:text-white mb-4">Password</h4>
            <div class="flex items-center space-x-4">
              <label class="flex items-center">
                <input v-model="generatePassword"
                       type="checkbox"
                       class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                  Genera password temporanea automaticamente
                </span>
              </label>
            </div>
            <div v-if="!generatePassword" class="mt-3">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Password Personalizzata
              </label>
              <input v-model="form.password"
                     type="password"
                     class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
            </div>
          </div>

          <!-- Error Message -->
          <div v-if="error" class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
            <div class="flex">
              <svg class="w-5 h-5 text-red-400 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
              </svg>
              <div>
                <h3 class="text-sm font-medium text-red-800 dark:text-red-200">Errore nella creazione</h3>
                <p class="text-sm text-red-700 dark:text-red-300 mt-1">{{ error }}</p>
              </div>
            </div>
          </div>

          <!-- Modal Footer -->
          <div class="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
            <button type="button"
                    @click="$emit('close')"
                    class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
              Annulla
            </button>
            <button type="submit"
                    :disabled="loading"
                    class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50">
              <svg v-if="loading" class="animate-spin -ml-1 mr-3 h-4 w-4 text-white inline" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {{ loading ? 'Creazione...' : 'Crea Dipendente' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

// Emits
const emit = defineEmits(['close', 'user-created'])

// Reactive state
const loading = ref(false)
const error = ref(null)
const departments = ref([])
const generatePassword = ref(true)

// Form data
const form = ref({
  // Basic info
  first_name: '',
  last_name: '',
  username: '',
  email: '',
  phone: '',
  position: '',

  // Role and department
  role: 'employee',
  department_id: '',

  // Contract info
  hire_date: '',
  employment_type: 'full_time',
  work_location: '',
  weekly_hours: 40,
  probation_end_date: '',
  contract_end_date: '',

  // Salary info
  salary: null,
  salary_currency: 'EUR',

  // Emergency contact
  emergency_contact_name: '',
  emergency_contact_phone: '',
  emergency_contact_relationship: '',

  // Password
  password: ''
})

// Methods
const loadDepartments = async () => {
  try {
    const response = await fetch('/api/personnel/departments', {
      credentials: 'include'
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()

    if (data.success) {
      departments.value = data.data.departments || []
    }
  } catch (err) {
    console.error('Error loading departments:', err)
  }
}

const createUser = async () => {
  loading.value = true
  error.value = null

  try {
    // Prepare form data
    const userData = { ...form.value }

    // Remove password if generating automatically
    if (generatePassword.value) {
      delete userData.password
    }

    // Convert empty strings to null for optional fields
    Object.keys(userData).forEach(key => {
      if (userData[key] === '') {
        userData[key] = null
      }
    })

    const response = await fetch('/api/personnel/admin/users', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include',
      body: JSON.stringify(userData)
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()

    if (data.success) {
      emit('user-created', data.data.user)

      // Show success message if password was generated
      if (generatePassword.value && data.data.temporary_password) {
        alert(`Utente creato con successo!\nPassword temporanea: ${data.data.temporary_password}`)
      }
    } else {
      throw new Error(data.message || 'Errore nella creazione utente')
    }
  } catch (err) {
    console.error('Error creating user:', err)
    error.value = err.message
  } finally {
    loading.value = false
  }
}

// Lifecycle
onMounted(() => {
  loadDepartments()

  // Set default hire date to today
  const today = new Date().toISOString().split('T')[0]
  form.value.hire_date = today
})
</script>

<style scoped>
/* Custom styles for modal */
.modal-container {
  max-height: 90vh;
  overflow-y: auto;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .modal-container {
    margin: 1rem;
    width: calc(100% - 2rem);
  }
}
</style>

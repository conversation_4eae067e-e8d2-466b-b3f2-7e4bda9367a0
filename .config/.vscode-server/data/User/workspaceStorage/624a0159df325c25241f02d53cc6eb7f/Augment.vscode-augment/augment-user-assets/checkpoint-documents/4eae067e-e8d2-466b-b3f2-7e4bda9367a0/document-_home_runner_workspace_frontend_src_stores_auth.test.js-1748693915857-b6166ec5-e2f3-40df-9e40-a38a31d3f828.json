{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/stores/auth.test.js"}, "modifiedCode": "import { describe, it, expect, beforeEach, vi } from 'vitest'\nimport { setActive<PERSON><PERSON>, create<PERSON><PERSON> } from 'pinia'\nimport { useAuthStore } from './auth'\nimport { mockApiResponse, createMockUser } from '@/test/utils'\n\ndescribe('Auth Store', () => {\n  beforeEach(() => {\n    setActivePinia(createPinia())\n    fetch.mockClear()\n    localStorage.clear()\n  })\n\n  it('initializes with default state', () => {\n    const store = useAuthStore()\n    \n    expect(store.user).toBeNull()\n    expect(store.isAuthenticated).toBe(false)\n    expect(store.permissions).toEqual([])\n    expect(store.loading).toBe(false)\n  })\n\n  it('logs in user successfully', async () => {\n    const mockUser = createMockUser()\n    fetch.mockResolvedValueOnce(mockApiResponse({\n      user: mockUser,\n      permissions: ['view_personnel', 'manage_users']\n    }))\n\n    const store = useAuthStore()\n    await store.login('testuser', 'password')\n    \n    expect(store.user).toEqual(mockUser)\n    expect(store.isAuthenticated).toBe(true)\n    expect(store.permissions).toEqual(['view_personnel', 'manage_users'])\n    expect(fetch).toHaveBeenCalledWith('/auth/login', expect.any(Object))\n  })\n\n  it('handles login failure', async () => {\n    fetch.mockResolvedValueOnce({\n      ok: false,\n      status: 401,\n      json: async () => ({ success: false, message: 'Invalid credentials' })\n    })\n\n    const store = useAuthStore()\n    \n    await expect(store.login('testuser', 'wrongpassword')).rejects.toThrow('Invalid credentials')\n    expect(store.user).toBeNull()\n    expect(store.isAuthenticated).toBe(false)\n  })\n\n  it('logs out user successfully', async () => {\n    const store = useAuthStore()\n    \n    // Set initial authenticated state\n    store.user = createMockUser()\n    store.permissions = ['view_personnel']\n    \n    fetch.mockResolvedValueOnce(mockApiResponse({}))\n    \n    await store.logout()\n    \n    expect(store.user).toBeNull()\n    expect(store.isAuthenticated).toBe(false)\n    expect(store.permissions).toEqual([])\n    expect(fetch).toHaveBeenCalledWith('/auth/logout', expect.any(Object))\n  })\n\n  it('checks authentication status', async () => {\n    const mockUser = createMockUser()\n    fetch.mockResolvedValueOnce(mockApiResponse({\n      user: mockUser,\n      permissions: ['view_personnel']\n    }))\n\n    const store = useAuthStore()\n    await store.checkAuth()\n    \n    expect(store.user).toEqual(mockUser)\n    expect(store.isAuthenticated).toBe(true)\n    expect(fetch).toHaveBeenCalledWith('/auth/me', expect.any(Object))\n  })\n\n  it('handles unauthenticated check', async () => {\n    fetch.mockResolvedValueOnce({\n      ok: false,\n      status: 401,\n      json: async () => ({ success: false, message: 'Not authenticated' })\n    })\n\n    const store = useAuthStore()\n    await store.checkAuth()\n    \n    expect(store.user).toBeNull()\n    expect(store.isAuthenticated).toBe(false)\n  })\n\n  it('checks user permissions correctly', () => {\n    const store = useAuthStore()\n    store.permissions = ['view_personnel', 'manage_users', 'admin']\n    \n    expect(store.hasPermission('view_personnel')).toBe(true)\n    expect(store.hasPermission('manage_users')).toBe(true)\n    expect(store.hasPermission('delete_users')).toBe(false)\n    expect(store.hasPermission(['view_personnel', 'manage_users'])).toBe(true)\n    expect(store.hasPermission(['view_personnel', 'delete_users'])).toBe(false)\n  })\n\n  it('checks user roles correctly', () => {\n    const store = useAuthStore()\n    store.user = createMockUser({ role: 'admin' })\n    \n    expect(store.hasRole('admin')).toBe(true)\n    expect(store.hasRole('manager')).toBe(false)\n    expect(store.hasRole(['admin', 'manager'])).toBe(true)\n    expect(store.hasRole(['manager', 'employee'])).toBe(false)\n  })\n\n  it('updates user profile', async () => {\n    const store = useAuthStore()\n    store.user = createMockUser()\n    \n    const updatedData = { first_name: 'Updated', last_name: 'Name' }\n    const updatedUser = { ...store.user, ...updatedData }\n    \n    fetch.mockResolvedValueOnce(mockApiResponse({ user: updatedUser }))\n    \n    await store.updateProfile(updatedData)\n    \n    expect(store.user.first_name).toBe('Updated')\n    expect(store.user.last_name).toBe('Name')\n    expect(fetch).toHaveBeenCalledWith('/api/auth/profile', expect.any(Object))\n  })\n\n  it('changes password successfully', async () => {\n    const store = useAuthStore()\n    store.user = createMockUser()\n    \n    fetch.mockResolvedValueOnce(mockApiResponse({}))\n    \n    await store.changePassword('oldpass', 'newpass')\n    \n    expect(fetch).toHaveBeenCalledWith('/api/auth/change-password', {\n      method: 'POST',\n      headers: { 'Content-Type': 'application/json' },\n      credentials: 'include',\n      body: JSON.stringify({\n        current_password: 'oldpass',\n        new_password: 'newpass'\n      })\n    })\n  })\n\n  it('handles loading states correctly', async () => {\n    const store = useAuthStore()\n    \n    // Mock delayed response\n    fetch.mockImplementationOnce(() => \n      new Promise(resolve => \n        setTimeout(() => resolve(mockApiResponse({ user: createMockUser() })), 100)\n      )\n    )\n    \n    const loginPromise = store.login('testuser', 'password')\n    \n    expect(store.loading).toBe(true)\n    \n    await loginPromise\n    \n    expect(store.loading).toBe(false)\n  })\n\n  it('persists authentication state', () => {\n    const store = useAuthStore()\n    const mockUser = createMockUser()\n    \n    store.user = mockUser\n    store.permissions = ['view_personnel']\n    \n    // Simulate page reload by creating new store instance\n    const newStore = useAuthStore()\n    \n    // In real implementation, this would restore from localStorage/sessionStorage\n    expect(newStore.user).toBeNull() // Initially null until restored\n  })\n})\n"}
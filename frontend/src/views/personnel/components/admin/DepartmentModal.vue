<template>
  <!-- <PERSON><PERSON> Backdrop -->
  <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" @click="$emit('close')">
    <!-- Modal Container -->
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-2xl shadow-lg rounded-md bg-white dark:bg-gray-800" @click.stop>
      <!-- <PERSON><PERSON> Header -->
      <div class="flex items-center justify-between pb-4 border-b border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white">
          {{ department ? 'Modifica Dipartimento' : 'Nuovo Dipartimento' }}
        </h3>
        <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <!-- Modal Body -->
      <div class="mt-6">
        <form @submit.prevent="saveDepartment" class="space-y-6">
          <!-- Basic Information -->
          <div>
            <h4 class="text-md font-medium text-gray-900 dark:text-white mb-4">Informazioni Base</h4>
            <div class="grid grid-cols-1 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Nome Dipartimento *
                </label>
                <input v-model="form.name"
                       type="text"
                       required
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Descrizione
                </label>
                <textarea v-model="form.description"
                          rows="3"
                          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"></textarea>
              </div>
            </div>
          </div>

          <!-- Management -->
          <div>
            <h4 class="text-md font-medium text-gray-900 dark:text-white mb-4">Gestione</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Manager
                </label>
                <select v-model="form.manager_id"
                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                  <option value="">Seleziona manager</option>
                  <option v-for="manager in managers" :key="manager.id" :value="manager.id">
                    {{ manager.full_name }}
                  </option>
                </select>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Dipartimento Padre
                </label>
                <select v-model="form.parent_id"
                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                  <option value="">Nessun padre (dipartimento principale)</option>
                  <option v-for="dept in availableParents" :key="dept.id" :value="dept.id">
                    {{ dept.name }}
                  </option>
                </select>
              </div>
            </div>
          </div>

          <!-- Budget -->
          <div>
            <h4 class="text-md font-medium text-gray-900 dark:text-white mb-4">Budget</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Budget Annuale (€)
                </label>
                <input v-model.number="form.budget"
                       type="number"
                       step="100"
                       min="0"
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Codice Dipartimento
                </label>
                <input v-model="form.code"
                       type="text"
                       placeholder="es. IT, HR, SALES"
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
              </div>
            </div>
          </div>

          <!-- Error Message -->
          <div v-if="error" class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
            <div class="flex">
              <svg class="w-5 h-5 text-red-400 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
              </svg>
              <div>
                <h3 class="text-sm font-medium text-red-800 dark:text-red-200">Errore nel salvataggio</h3>
                <p class="text-sm text-red-700 dark:text-red-300 mt-1">{{ error }}</p>
              </div>
            </div>
          </div>

          <!-- Modal Footer -->
          <div class="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
            <button type="button"
                    @click="$emit('close')"
                    class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
              Annulla
            </button>
            <button type="submit"
                    :disabled="loading"
                    class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50">
              <svg v-if="loading" class="animate-spin -ml-1 mr-3 h-4 w-4 text-white inline" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {{ loading ? 'Salvataggio...' : (department ? 'Aggiorna' : 'Crea') }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

// Props
const props = defineProps({
  department: {
    type: Object,
    default: null
  },
  managers: {
    type: Array,
    default: () => []
  },
  departments: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['close', 'saved'])

// Reactive state
const loading = ref(false)
const error = ref(null)

// Form data
const form = ref({
  name: '',
  description: '',
  manager_id: '',
  parent_id: '',
  budget: null,
  code: ''
})

// Computed properties
const availableParents = computed(() => {
  // Exclude current department and its children to prevent circular references
  if (!props.department) {
    return props.departments
  }
  
  return props.departments.filter(dept => 
    dept.id !== props.department.id &&
    dept.parent_id !== props.department.id
  )
})

// Methods
const saveDepartment = async () => {
  loading.value = true
  error.value = null

  try {
    // Prepare form data
    const departmentData = { ...form.value }
    
    // Convert empty strings to null for optional fields
    Object.keys(departmentData).forEach(key => {
      if (departmentData[key] === '') {
        departmentData[key] = null
      }
    })

    const url = props.department 
      ? `/api/personnel/departments/${props.department.id}`
      : '/api/personnel/departments'
    
    const method = props.department ? 'PUT' : 'POST'

    const response = await fetch(url, {
      method,
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include',
      body: JSON.stringify(departmentData)
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()

    if (data.success) {
      emit('saved', data.data.department)
    } else {
      throw new Error(data.message || 'Errore nel salvataggio dipartimento')
    }
  } catch (err) {
    console.error('Error saving department:', err)
    error.value = err.message
  } finally {
    loading.value = false
  }
}

// Lifecycle
onMounted(() => {
  if (props.department) {
    // Populate form with existing department data
    form.value = {
      name: props.department.name || '',
      description: props.department.description || '',
      manager_id: props.department.manager_id || '',
      parent_id: props.department.parent_id || '',
      budget: props.department.budget || null,
      code: props.department.code || ''
    }
  }
})
</script>

<style scoped>
/* Custom styles for modal */
.modal-container {
  max-height: 90vh;
  overflow-y: auto;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .modal-container {
    margin: 1rem;
    width: calc(100% - 2rem);
  }
}
</style>

function Ne(e){return Ne=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ne(e)}function Wt(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Kt(e,t,n){return t&&function(r,a){for(var i=0;i<a.length;i++){var o=a[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(r,o.key,o)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function ts(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ze(e,t){return function(n){if(Array.isArray(n))return n}(e)||function(n,r){var a=n==null?null:typeof Symbol<"u"&&n[Symbol.iterator]||n["@@iterator"];if(a!=null){var i,o,s=[],l=!0,u=!1;try{for(a=a.call(n);!(l=(i=a.next()).done)&&(s.push(i.value),!r||s.length!==r);l=!0);}catch(c){u=!0,o=c}finally{try{l||a.return==null||a.return()}finally{if(u)throw o}}return s}}(e,t)||ti(e,t)||function(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}()}function ns(e){return function(t){if(Array.isArray(t))return _a(t)}(e)||function(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}(e)||ti(e)||function(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}()}function ti(e,t){if(e){if(typeof e=="string")return _a(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return n==="Object"&&e.constructor&&(n=e.constructor.name),n==="Map"||n==="Set"?Array.from(e):n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?_a(e,t):void 0}}function _a(e,t){(t==null||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function ut(e,t){var n=typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=ti(e))||t){n&&(e=n);var r=0,a=function(){};return{s:a,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(l){throw l},f:a}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var i,o=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var l=n.next();return o=l.done,l},e:function(l){s=!0,i=l},f:function(){try{o||n.return==null||n.return()}finally{if(s)throw i}}}}var Re=typeof window>"u"?null:window,Si=Re?Re.navigator:null;Re&&Re.document;var Il=Ne(""),rs=Ne({}),Nl=Ne(function(){}),Ll=typeof HTMLElement>"u"?"undefined":Ne(HTMLElement),fr=function(e){return e&&e.instanceString&&_e(e.instanceString)?e.instanceString():null},ce=function(e){return e!=null&&Ne(e)==Il},_e=function(e){return e!=null&&Ne(e)===Nl},Te=function(e){return!at(e)&&(Array.isArray?Array.isArray(e):e!=null&&e instanceof Array)},me=function(e){return e!=null&&Ne(e)===rs&&!Te(e)&&e.constructor===Object},ae=function(e){return e!=null&&Ne(e)===Ne(1)&&!isNaN(e)},jr=function(e){return Ll==="undefined"?void 0:e!=null&&e instanceof HTMLElement},at=function(e){return pr(e)||as(e)},pr=function(e){return fr(e)==="collection"&&e._private.single},as=function(e){return fr(e)==="collection"&&!e._private.single},ni=function(e){return fr(e)==="core"},is=function(e){return fr(e)==="stylesheet"},Xt=function(e){return e==null||!(e!==""&&!e.match(/^\s+$/))},Ol=function(e){return function(t){return t!=null&&Ne(t)===rs}(e)&&_e(e.then)},or=function(e,t){t||(t=function(){if(arguments.length===1)return arguments[0];if(arguments.length===0)return"undefined";for(var r=[],a=0;a<arguments.length;a++)r.push(arguments[a]);return r.join("$")});var n=function r(){var a,i=arguments,o=t.apply(this,i),s=r.cache;return(a=s[o])||(a=s[o]=e.apply(this,i)),a};return n.cache={},n},ri=or(function(e){return e.replace(/([A-Z])/g,function(t){return"-"+t.toLowerCase()})}),aa=or(function(e){return e.replace(/(-\w)/g,function(t){return t[1].toUpperCase()})}),os=or(function(e,t){return e+t[0].toUpperCase()+t.substring(1)},function(e,t){return e+"$"+t}),Bi=function(e){return Xt(e)?e:e.charAt(0).toUpperCase()+e.substring(1)},Ie="(?:[-+]?(?:(?:\\d+|\\d*\\.\\d+)(?:[Ee][+-]?\\d+)?))",zl="rgb[a]?\\(("+Ie+"[%]?)\\s*,\\s*("+Ie+"[%]?)\\s*,\\s*("+Ie+"[%]?)(?:\\s*,\\s*("+Ie+"))?\\)",Vl="rgb[a]?\\((?:"+Ie+"[%]?)\\s*,\\s*(?:"+Ie+"[%]?)\\s*,\\s*(?:"+Ie+"[%]?)(?:\\s*,\\s*(?:"+Ie+"))?\\)",Fl="hsl[a]?\\(("+Ie+")\\s*,\\s*("+Ie+"[%])\\s*,\\s*("+Ie+"[%])(?:\\s*,\\s*("+Ie+"))?\\)",ql="hsl[a]?\\((?:"+Ie+")\\s*,\\s*(?:"+Ie+"[%])\\s*,\\s*(?:"+Ie+"[%])(?:\\s*,\\s*(?:"+Ie+"))?\\)",ss=function(e,t){return e<t?-1:e>t?1:0},he=Object.assign!=null?Object.assign.bind(Object):function(e){for(var t=arguments,n=1;n<t.length;n++){var r=t[n];if(r!=null)for(var a=Object.keys(r),i=0;i<a.length;i++){var o=a[i];e[o]=r[o]}}return e},ls=function(e){return(Te(e)?e:null)||function(t){return Xl[t.toLowerCase()]}(e)||function(t){if((t.length===4||t.length===7)&&t[0]==="#"){var n,r,a,i=16;return t.length===4?(n=parseInt(t[1]+t[1],i),r=parseInt(t[2]+t[2],i),a=parseInt(t[3]+t[3],i)):(n=parseInt(t[1]+t[2],i),r=parseInt(t[3]+t[4],i),a=parseInt(t[5]+t[6],i)),[n,r,a]}}(e)||function(t){var n,r=new RegExp("^"+zl+"$").exec(t);if(r){n=[];for(var a=[],i=1;i<=3;i++){var o=r[i];if(o[o.length-1]==="%"&&(a[i]=!0),o=parseFloat(o),a[i]&&(o=o/100*255),o<0||o>255)return;n.push(Math.floor(o))}var s=a[1]||a[2]||a[3],l=a[1]&&a[2]&&a[3];if(s&&!l)return;var u=r[4];if(u!==void 0){if((u=parseFloat(u))<0||u>1)return;n.push(u)}}return n}(e)||function(t){var n,r,a,i,o,s,l,u;function c(f,v,m){return m<0&&(m+=1),m>1&&(m-=1),m<1/6?f+6*(v-f)*m:m<.5?v:m<2/3?f+(v-f)*(2/3-m)*6:f}var d=new RegExp("^"+Fl+"$").exec(t);if(d){if((r=parseInt(d[1]))<0?r=(360- -1*r%360)%360:r>360&&(r%=360),r/=360,(a=parseFloat(d[2]))<0||a>100||(a/=100,(i=parseFloat(d[3]))<0||i>100)||(i/=100,(o=d[4])!==void 0&&((o=parseFloat(o))<0||o>1)))return;if(a===0)s=l=u=Math.round(255*i);else{var h=i<.5?i*(1+a):i+a-i*a,p=2*i-h;s=Math.round(255*c(p,h,r+1/3)),l=Math.round(255*c(p,h,r)),u=Math.round(255*c(p,h,r-1/3))}n=[s,l,u,o]}return n}(e)},Xl={transparent:[0,0,0,0],aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],grey:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]},us=function(e){for(var t=e.map,n=e.keys,r=n.length,a=0;a<r;a++){var i=n[a];if(me(i))throw Error("Tried to set map with object key");a<n.length-1?(t[i]==null&&(t[i]={}),t=t[i]):t[i]=e.value}},cs=function(e){for(var t=e.map,n=e.keys,r=n.length,a=0;a<r;a++){var i=n[a];if(me(i))throw Error("Tried to get map with object key");if((t=t[i])==null)return t}return t},an=function(e){var t=typeof e;return e!=null&&(t=="object"||t=="function")},Kn=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},Yl=typeof Kn=="object"&&Kn&&Kn.Object===Object&&Kn,jl=typeof self=="object"&&self&&self.Object===Object&&self,ia=Yl||jl||Function("return this")(),ga=function(){return ia.Date.now()},Wl=/\s/,Kl=function(e){for(var t=e.length;t--&&Wl.test(e.charAt(t)););return t},Hl=/^\s+/,Ul=function(e){return e&&e.slice(0,Kl(e)+1).replace(Hl,"")},_n=ia.Symbol,ds=Object.prototype,Gl=ds.hasOwnProperty,Zl=ds.toString,On=_n?_n.toStringTag:void 0,$l=function(e){var t=Gl.call(e,On),n=e[On];try{e[On]=void 0;var r=!0}catch{}var a=Zl.call(e);return r&&(t?e[On]=n:delete e[On]),a},Ql=Object.prototype.toString,Jl=function(e){return Ql.call(e)},Di=_n?_n.toStringTag:void 0,hs=function(e){return e==null?e===void 0?"[object Undefined]":"[object Null]":Di&&Di in Object(e)?$l(e):Jl(e)},eu=function(e){return e!=null&&typeof e=="object"},gr=function(e){return typeof e=="symbol"||eu(e)&&hs(e)=="[object Symbol]"},tu=/^[-+]0x[0-9a-f]+$/i,nu=/^0b[01]+$/i,ru=/^0o[0-7]+$/i,au=parseInt,_i=function(e){if(typeof e=="number")return e;if(gr(e))return NaN;if(an(e)){var t=typeof e.valueOf=="function"?e.valueOf():e;e=an(t)?t+"":t}if(typeof e!="string")return e===0?e:+e;e=Ul(e);var n=nu.test(e);return n||ru.test(e)?au(e.slice(2),n?2:8):tu.test(e)?NaN:+e},iu=Math.max,ou=Math.min,vr=function(e,t,n){var r,a,i,o,s,l,u=0,c=!1,d=!1,h=!0;if(typeof e!="function")throw new TypeError("Expected a function");function p(y){var b=r,k=a;return r=a=void 0,u=y,o=e.apply(k,b)}function f(y){var b=y-l;return l===void 0||b>=t||b<0||d&&y-u>=i}function v(){var y=ga();if(f(y))return m(y);s=setTimeout(v,function(b){var k=t-(b-l);return d?ou(k,i-(b-u)):k}(y))}function m(y){return s=void 0,h&&r?p(y):(r=a=void 0,o)}function g(){var y=ga(),b=f(y);if(r=arguments,a=this,l=y,b){if(s===void 0)return function(k){return u=k,s=setTimeout(v,t),c?p(k):o}(l);if(d)return clearTimeout(s),s=setTimeout(v,t),p(l)}return s===void 0&&(s=setTimeout(v,t)),o}return t=_i(t)||0,an(n)&&(c=!!n.leading,i=(d="maxWait"in n)?iu(_i(n.maxWait)||0,t):i,h="trailing"in n?!!n.trailing:h),g.cancel=function(){s!==void 0&&clearTimeout(s),u=0,r=l=a=s=void 0},g.flush=function(){return s===void 0?o:m(ga())},g},va=Re?Re.performance:null,fs=va&&va.now?function(){return va.now()}:function(){return Date.now()},su=function(){if(Re){if(Re.requestAnimationFrame)return function(e){Re.requestAnimationFrame(e)};if(Re.mozRequestAnimationFrame)return function(e){Re.mozRequestAnimationFrame(e)};if(Re.webkitRequestAnimationFrame)return function(e){Re.webkitRequestAnimationFrame(e)};if(Re.msRequestAnimationFrame)return function(e){Re.msRequestAnimationFrame(e)}}return function(e){e&&setTimeout(function(){e(fs())},1e3/60)}}(),Wr=function(e){return su(e)},Bt=fs,kn=9261,Hn=5381,ps=function(e){for(var t,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:kn;!(t=e.next()).done;)n=65599*n+t.value|0;return n},sr=function(e){return 65599*(arguments.length>1&&arguments[1]!==void 0?arguments[1]:kn)+e|0},lr=function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Hn;return(t<<5)+t+e|0},_t=function(e){return 2097152*e[0]+e[1]},br=function(e,t){return[sr(e[0],t[0]),lr(e[1],t[1])]},Yt=function(e,t){var n={value:0,done:!1},r=0,a=e.length;return ps({next:function(){return r<a?n.value=e.charCodeAt(r++):n.done=!0,n}},t)},gs=function(){return lu(arguments)},lu=function(e){for(var t,n=0;n<e.length;n++){var r=e[n];t=n===0?Yt(r):Yt(r,t)}return t},Ai=!0,uu=console.warn!=null,cu=console.trace!=null,ai=Number.MAX_SAFE_INTEGER||9007199254740991,vs=function(){return!0},Kr=function(){return!1},Mi=function(){return 0},ii=function(){},De=function(e){throw new Error(e)},ys=function(e){if(e===void 0)return Ai;Ai=!!e},we=function(e){ys()&&(uu?console.warn(e):(console.log(e),cu&&console.trace()))},xt=function(e){return e==null?e:Te(e)?e.slice():me(e)?function(t){return he({},t)}(e):e},ms=function(e,t){for(t=e="";e++<36;t+=51*e&52?(15^e?8^Math.random()*(20^e?16:4):4).toString(16):"-");return t},du={},bs=function(){return du},Ve=function(e){var t=Object.keys(e);return function(n){for(var r={},a=0;a<t.length;a++){var i=t[a],o=n==null?void 0:n[i];r[i]=o===void 0?e[i]:o}return r}},zt=function(e,t,n){for(var r=e.length-1;r>=0;r--)e[r]===t&&e.splice(r,1)},Aa=function(e){e.splice(0,e.length)},ft=function(e,t,n){return n&&(t=os(n,t)),e[t]},At=function(e,t,n,r){n&&(t=os(n,t)),e[t]=r},wt=typeof Map<"u"?Map:function(){function e(){Wt(this,e),this._obj={}}return Kt(e,[{key:"set",value:function(t,n){return this._obj[t]=n,this}},{key:"delete",value:function(t){return this._obj[t]=void 0,this}},{key:"clear",value:function(){this._obj={}}},{key:"has",value:function(t){return this._obj[t]!==void 0}},{key:"get",value:function(t){return this._obj[t]}}]),e}(),hu=function(){function e(t){if(Wt(this,e),this._obj=Object.create(null),this.size=0,t!=null){var n;n=t.instanceString!=null&&t.instanceString()===this.instanceString()?t.toArray():t;for(var r=0;r<n.length;r++)this.add(n[r])}}return Kt(e,[{key:"instanceString",value:function(){return"set"}},{key:"add",value:function(t){var n=this._obj;n[t]!==1&&(n[t]=1,this.size++)}},{key:"delete",value:function(t){var n=this._obj;n[t]===1&&(n[t]=0,this.size--)}},{key:"clear",value:function(){this._obj=Object.create(null)}},{key:"has",value:function(t){return this._obj[t]===1}},{key:"toArray",value:function(){var t=this;return Object.keys(this._obj).filter(function(n){return t.has(n)})}},{key:"forEach",value:function(t,n){return this.toArray().forEach(t,n)}}]),e}(),An=(typeof Set>"u"?"undefined":Ne(Set))!=="undefined"?Set:hu,oa=function(e,t){var n=!(arguments.length>2&&arguments[2]!==void 0)||arguments[2];if(e!==void 0&&t!==void 0&&ni(e)){var r=t.group;if(r==null&&(r=t.data&&t.data.source!=null&&t.data.target!=null?"edges":"nodes"),r==="nodes"||r==="edges"){this.length=1,this[0]=this;var a=this._private={cy:e,single:!0,data:t.data||{},position:t.position||{x:0,y:0},autoWidth:void 0,autoHeight:void 0,autoPadding:void 0,compoundBoundsClean:!1,listeners:[],group:r,style:{},rstyle:{},styleCxts:[],styleKeys:{},removed:!0,selected:!!t.selected,selectable:t.selectable===void 0||!!t.selectable,locked:!!t.locked,grabbed:!1,grabbable:t.grabbable===void 0||!!t.grabbable,pannable:t.pannable===void 0?r==="edges":!!t.pannable,active:!1,classes:new An,animation:{current:[],queue:[]},rscratch:{},scratch:t.scratch||{},edges:[],children:[],parent:t.parent&&t.parent.isNode()?t.parent:null,traversalCache:{},backgrounding:!1,bbCache:null,bbCacheShift:{x:0,y:0},bodyBounds:null,overlayBounds:null,labelBounds:{all:null,source:null,target:null,main:null},arrowBounds:{source:null,target:null,"mid-source":null,"mid-target":null}};if(a.position.x==null&&(a.position.x=0),a.position.y==null&&(a.position.y=0),t.renderedPosition){var i=t.renderedPosition,o=e.pan(),s=e.zoom();a.position={x:(i.x-o.x)/s,y:(i.y-o.y)/s}}var l=[];Te(t.classes)?l=t.classes:ce(t.classes)&&(l=t.classes.split(/\s+/));for(var u=0,c=l.length;u<c;u++){var d=l[u];d&&d!==""&&a.classes.add(d)}this.createEmitter();var h=t.style||t.css;h&&(we("Setting a `style` bypass at element creation should be done only when absolutely necessary.  Try to use the stylesheet instead."),this.style(h)),(n===void 0||n)&&this.restore()}else De("An element must be of type `nodes` or `edges`; you specified `"+r+"`")}else De("An element must have a core reference and parameters set")},Ri=function(e){return e={bfs:e.bfs||!e.dfs,dfs:e.dfs||!e.bfs},function(t,n,r){var a;me(t)&&!at(t)&&(t=(a=t).roots||a.root,n=a.visit,r=a.directed),r=arguments.length!==2||_e(n)?r:n,n=_e(n)?n:function(){};for(var i,o=this._private.cy,s=t=ce(t)?this.filter(t):t,l=[],u=[],c={},d={},h={},p=0,f=this.byGroup(),v=f.nodes,m=f.edges,g=0;g<s.length;g++){var y=s[g],b=y.id();y.isNode()&&(l.unshift(y),e.bfs&&(h[b]=!0,u.push(y)),d[b]=0)}for(var k=function(){var P=e.bfs?l.shift():l.pop(),_=P.id();if(e.dfs){if(h[_])return"continue";h[_]=!0,u.push(P)}var A,B=d[_],O=c[_],N=O!=null?O.source():null,V=O!=null?O.target():null,M=O==null?void 0:P.same(N)?V[0]:N[0];if((A=n(P,O,M,p++,B))===!0)return i=P,"break";if(A===!1)return"break";for(var D=P.connectedEdges().filter(function(U){return(!r||U.source().same(P))&&m.has(U)}),I=0;I<D.length;I++){var z=D[I],q=z.connectedNodes().filter(function(U){return!U.same(P)&&v.has(U)}),j=q.id();q.length===0||h[j]||(q=q[0],l.push(q),e.bfs&&(h[j]=!0,u.push(q)),c[j]=z,d[j]=d[_]+1)}};l.length!==0;){var x=k();if(x!=="continue"&&x==="break")break}for(var w=o.collection(),E=0;E<u.length;E++){var C=u[E],S=c[C.id()];S!=null&&w.push(S),w.push(C)}return{path:o.collection(w),found:o.collection(i)}}},Un={breadthFirstSearch:Ri({bfs:!0}),depthFirstSearch:Ri({dfs:!0})};Un.bfs=Un.breadthFirstSearch,Un.dfs=Un.depthFirstSearch;var fu=function(e,t){return e(t={exports:{}},t.exports),t.exports}(function(e,t){(function(){var n,r,a,i,o,s,l,u,c,d,h,p,f,v,m;a=Math.floor,d=Math.min,r=function(g,y){return g<y?-1:g>y?1:0},c=function(g,y,b,k,x){var w;if(b==null&&(b=0),x==null&&(x=r),b<0)throw new Error("lo must be non-negative");for(k==null&&(k=g.length);b<k;)x(y,g[w=a((b+k)/2)])<0?k=w:b=w+1;return[].splice.apply(g,[b,b-b].concat(y)),y},s=function(g,y,b){return b==null&&(b=r),g.push(y),v(g,0,g.length-1,b)},o=function(g,y){var b,k;return y==null&&(y=r),b=g.pop(),g.length?(k=g[0],g[0]=b,m(g,0,y)):k=b,k},u=function(g,y,b){var k;return b==null&&(b=r),k=g[0],g[0]=y,m(g,0,b),k},l=function(g,y,b){var k;return b==null&&(b=r),g.length&&b(g[0],y)<0&&(y=(k=[g[0],y])[0],g[0]=k[1],m(g,0,b)),y},i=function(g,y){var b,k,x,w,E,C;for(y==null&&(y=r),E=[],k=0,x=(w=(function(){C=[];for(var S=0,P=a(g.length/2);0<=P?S<P:S>P;0<=P?S++:S--)C.push(S);return C}).apply(this).reverse()).length;k<x;k++)b=w[k],E.push(m(g,b,y));return E},f=function(g,y,b){var k;if(b==null&&(b=r),(k=g.indexOf(y))!==-1)return v(g,0,k,b),m(g,k,b)},h=function(g,y,b){var k,x,w,E,C;if(b==null&&(b=r),!(x=g.slice(0,y)).length)return x;for(i(x,b),w=0,E=(C=g.slice(y)).length;w<E;w++)k=C[w],l(x,k,b);return x.sort(b).reverse()},p=function(g,y,b){var k,x,w,E,C,S,P,_,A;if(b==null&&(b=r),10*y<=g.length){if(!(w=g.slice(0,y).sort(b)).length)return w;for(x=w[w.length-1],E=0,S=(P=g.slice(y)).length;E<S;E++)b(k=P[E],x)<0&&(c(w,k,0,null,b),w.pop(),x=w[w.length-1]);return w}for(i(g,b),A=[],C=0,_=d(y,g.length);0<=_?C<_:C>_;0<=_?++C:--C)A.push(o(g,b));return A},v=function(g,y,b,k){var x,w,E;for(k==null&&(k=r),x=g[b];b>y&&k(x,w=g[E=b-1>>1])<0;)g[b]=w,b=E;return g[b]=x},m=function(g,y,b){var k,x,w,E,C;for(b==null&&(b=r),x=g.length,C=y,w=g[y],k=2*y+1;k<x;)(E=k+1)<x&&!(b(g[k],g[E])<0)&&(k=E),g[y]=g[k],k=2*(y=k)+1;return g[y]=w,v(g,C,y,b)},n=function(){function g(y){this.cmp=y??r,this.nodes=[]}return g.push=s,g.pop=o,g.replace=u,g.pushpop=l,g.heapify=i,g.updateItem=f,g.nlargest=h,g.nsmallest=p,g.prototype.push=function(y){return s(this.nodes,y,this.cmp)},g.prototype.pop=function(){return o(this.nodes,this.cmp)},g.prototype.peek=function(){return this.nodes[0]},g.prototype.contains=function(y){return this.nodes.indexOf(y)!==-1},g.prototype.replace=function(y){return u(this.nodes,y,this.cmp)},g.prototype.pushpop=function(y){return l(this.nodes,y,this.cmp)},g.prototype.heapify=function(){return i(this.nodes,this.cmp)},g.prototype.updateItem=function(y){return f(this.nodes,y,this.cmp)},g.prototype.clear=function(){return this.nodes=[]},g.prototype.empty=function(){return this.nodes.length===0},g.prototype.size=function(){return this.nodes.length},g.prototype.clone=function(){var y;return(y=new g).nodes=this.nodes.slice(0),y},g.prototype.toArray=function(){return this.nodes.slice(0)},g.prototype.insert=g.prototype.push,g.prototype.top=g.prototype.peek,g.prototype.front=g.prototype.peek,g.prototype.has=g.prototype.contains,g.prototype.copy=g.prototype.clone,g}(),e.exports=n}).call(Kn)}),yr=fu,pu=Ve({root:null,weight:function(e){return 1},directed:!1}),gu={dijkstra:function(e){if(!me(e)){var t=arguments;e={root:t[0],weight:t[1],directed:t[2]}}var n=pu(e),r=n.root,a=n.weight,i=n.directed,o=this,s=a,l=ce(r)?this.filter(r)[0]:r[0],u={},c={},d={},h=this.byGroup(),p=h.nodes,f=h.edges;f.unmergeBy(function(O){return O.isLoop()});for(var v=function(O){return u[O.id()]},m=function(O,N){u[O.id()]=N,g.updateItem(O)},g=new yr(function(O,N){return v(O)-v(N)}),y=0;y<p.length;y++){var b=p[y];u[b.id()]=b.same(l)?0:1/0,g.push(b)}for(var k=function(O,N){for(var V,M=(i?O.edgesTo(N):O.edgesWith(N)).intersect(f),D=1/0,I=0;I<M.length;I++){var z=M[I],q=s(z);(q<D||!V)&&(D=q,V=z)}return{edge:V,dist:D}};g.size()>0;){var x=g.pop(),w=v(x),E=x.id();if(d[E]=w,w!==1/0)for(var C=x.neighborhood().intersect(p),S=0;S<C.length;S++){var P=C[S],_=P.id(),A=k(x,P),B=w+A.dist;B<v(P)&&(m(P,B),c[_]={node:x,edge:A.edge})}}return{distanceTo:function(O){var N=ce(O)?p.filter(O)[0]:O[0];return d[N.id()]},pathTo:function(O){var N=ce(O)?p.filter(O)[0]:O[0],V=[],M=N,D=M.id();if(N.length>0)for(V.unshift(N);c[D];){var I=c[D];V.unshift(I.edge),V.unshift(I.node),D=(M=I.node).id()}return o.spawn(V)}}}},vu={kruskal:function(e){e=e||function(y){return 1};for(var t=this.byGroup(),n=t.nodes,r=t.edges,a=n.length,i=new Array(a),o=n,s=function(y){for(var b=0;b<i.length;b++)if(i[b].has(y))return b},l=0;l<a;l++)i[l]=this.spawn(n[l]);for(var u=r.sort(function(y,b){return e(y)-e(b)}),c=0;c<u.length;c++){var d=u[c],h=d.source()[0],p=d.target()[0],f=s(h),v=s(p),m=i[f],g=i[v];f!==v&&(o.merge(d),m.merge(g),i.splice(v,1))}return o}},yu=Ve({root:null,goal:null,weight:function(e){return 1},heuristic:function(e){return 0},directed:!1}),mu={aStar:function(e){var t=this.cy(),n=yu(e),r=n.root,a=n.goal,i=n.heuristic,o=n.directed,s=n.weight;r=t.collection(r)[0],a=t.collection(a)[0];var l,u,c=r.id(),d=a.id(),h={},p={},f={},v=new yr(function(D,I){return p[D.id()]-p[I.id()]}),m=new An,g={},y={},b=function(D,I){v.push(D),m.add(I)};b(r,c),h[c]=0,p[c]=i(r);for(var k,x=0;v.size()>0;){if(l=v.pop(),u=l.id(),m.delete(u),x++,u===d){for(var w=[],E=a,C=d,S=y[C];w.unshift(E),S!=null&&w.unshift(S),(E=g[C])!=null;)S=y[C=E.id()];return{found:!0,distance:h[u],path:this.spawn(w),steps:x}}f[u]=!0;for(var P=l._private.edges,_=0;_<P.length;_++){var A=P[_];if(this.hasElementWithId(A.id())&&(!o||A.data("source")===u)){var B=A.source(),O=A.target(),N=B.id()!==u?B:O,V=N.id();if(this.hasElementWithId(V)&&!f[V]){var M=h[u]+s(A);k=V,m.has(k)?M<h[V]&&(h[V]=M,p[V]=M+i(N),g[V]=l,y[V]=A):(h[V]=M,p[V]=M+i(N),b(N,V),g[V]=l,y[V]=A)}}}}return{found:!1,distance:void 0,path:void 0,steps:x}}},bu=Ve({weight:function(e){return 1},directed:!1}),xu={floydWarshall:function(e){for(var t=this.cy(),n=bu(e),r=n.weight,a=n.directed,i=r,o=this.byGroup(),s=o.nodes,l=o.edges,u=s.length,c=u*u,d=function(z){return s.indexOf(z)},h=function(z){return s[z]},p=new Array(c),f=0;f<c;f++){var v=f%u,m=(f-v)/u;p[f]=m===v?0:1/0}for(var g=new Array(c),y=new Array(c),b=0;b<l.length;b++){var k=l[b],x=k.source()[0],w=k.target()[0];if(x!==w){var E=d(x),C=d(w),S=E*u+C,P=i(k);if(p[S]>P&&(p[S]=P,g[S]=C,y[S]=k),!a){var _=C*u+E;!a&&p[_]>P&&(p[_]=P,g[_]=E,y[_]=k)}}}for(var A=0;A<u;A++)for(var B=0;B<u;B++)for(var O=B*u+A,N=0;N<u;N++){var V=B*u+N,M=A*u+N;p[O]+p[M]<p[V]&&(p[V]=p[O]+p[M],g[V]=g[O])}var D=function(z){return d(function(q){return(ce(q)?t.filter(q):q)[0]}(z))},I={distance:function(z,q){var j=D(z),U=D(q);return p[j*u+U]},path:function(z,q){var j=D(z),U=D(q),K=h(j);if(j===U)return K.collection();if(g[j*u+U]==null)return t.collection();var X,H=t.collection(),W=j;for(H.merge(K);j!==U;)W=j,j=g[j*u+U],X=y[W*u+j],H.merge(X),H.merge(h(j));return H}};return I}},wu=Ve({weight:function(e){return 1},directed:!1,root:null}),Eu={bellmanFord:function(e){var t=this,n=wu(e),r=n.weight,a=n.directed,i=n.root,o=r,s=this,l=this.cy(),u=this.byGroup(),c=u.edges,d=u.nodes,h=d.length,p=new wt,f=!1,v=[];i=l.collection(i)[0],c.unmergeBy(function($){return $.isLoop()});for(var m=c.length,g=function($){var ne=p.get($.id());return ne||(ne={},p.set($.id(),ne)),ne},y=function($){return(ce($)?l.$($):$)[0]},b=0;b<h;b++){var k=d[b],x=g(k);k.same(i)?x.dist=0:x.dist=1/0,x.pred=null,x.edge=null}for(var w=!1,E=function($,ne,T,R,L,Y){var F=R.dist+Y;F<L.dist&&!T.same(R.edge)&&(L.dist=F,L.pred=$,L.edge=T,w=!0)},C=1;C<h;C++){w=!1;for(var S=0;S<m;S++){var P=c[S],_=P.source(),A=P.target(),B=o(P),O=g(_),N=g(A);E(_,0,P,O,N,B),a||E(A,0,P,N,O,B)}if(!w)break}if(w)for(var V=[],M=0;M<m;M++){var D=c[M],I=D.source(),z=D.target(),q=o(D),j=g(I).dist,U=g(z).dist;if(j+q<U||!a&&U+q<j){if(f||(we("Graph contains a negative weight cycle for Bellman-Ford"),f=!0),e.findNegativeWeightCycles===!1)break;var K=[];j+q<U&&K.push(I),!a&&U+q<j&&K.push(z);for(var X=K.length,H=0;H<X;H++){var W=K[H],Q=[W];Q.push(g(W).edge);for(var te=g(W).pred;Q.indexOf(te)===-1;)Q.push(te),Q.push(g(te).edge),te=g(te).pred;for(var ie=(Q=Q.slice(Q.indexOf(te)))[0].id(),se=0,le=2;le<Q.length;le+=2)Q[le].id()<ie&&(ie=Q[le].id(),se=le);(Q=Q.slice(se).concat(Q.slice(0,se))).push(Q[0]);var fe=Q.map(function($){return $.id()}).join(",");V.indexOf(fe)===-1&&(v.push(s.spawn(Q)),V.push(fe))}}}return{distanceTo:function($){return g(y($)).dist},pathTo:function($){for(var ne=arguments.length>1&&arguments[1]!==void 0?arguments[1]:i,T=[],R=y($);;){if(R==null)return t.spawn();var L=g(R),Y=L.edge,F=L.pred;if(T.unshift(R[0]),R.same(ne)&&T.length>0)break;Y!=null&&T.unshift(Y),R=F}return s.spawn(T)},hasNegativeWeightCycle:f,negativeWeightCycles:v}}},Tu=Math.sqrt(2),ku=function(e,t,n){n.length===0&&De("Karger-Stein must be run on a connected (sub)graph");for(var r=n[e],a=r[1],i=r[2],o=t[a],s=t[i],l=n,u=l.length-1;u>=0;u--){var c=l[u],d=c[1],h=c[2];(t[d]===o&&t[h]===s||t[d]===s&&t[h]===o)&&l.splice(u,1)}for(var p=0;p<l.length;p++){var f=l[p];f[1]===s?(l[p]=f.slice(),l[p][1]=o):f[2]===s&&(l[p]=f.slice(),l[p][2]=o)}for(var v=0;v<t.length;v++)t[v]===s&&(t[v]=o);return l},ya=function(e,t,n,r){for(;n>r;){var a=Math.floor(Math.random()*t.length);t=ku(a,e,t),n--}return t},Cu={kargerStein:function(){var e=this,t=this.byGroup(),n=t.nodes,r=t.edges;r.unmergeBy(function(V){return V.isLoop()});var a=n.length,i=r.length,o=Math.ceil(Math.pow(Math.log(a)/Math.LN2,2)),s=Math.floor(a/Tu);if(!(a<2)){for(var l=[],u=0;u<i;u++){var c=r[u];l.push([u,n.indexOf(c.source()),n.indexOf(c.target())])}for(var d=1/0,h=[],p=new Array(a),f=new Array(a),v=new Array(a),m=function(V,M){for(var D=0;D<a;D++)M[D]=V[D]},g=0;g<=o;g++){for(var y=0;y<a;y++)f[y]=y;var b=ya(f,l.slice(),a,s),k=b.slice();m(f,v);var x=ya(f,b,s,2),w=ya(v,k,s,2);x.length<=w.length&&x.length<d?(d=x.length,h=x,m(f,p)):w.length<=x.length&&w.length<d&&(d=w.length,h=w,m(v,p))}for(var E=this.spawn(h.map(function(V){return r[V[0]]})),C=this.spawn(),S=this.spawn(),P=p[0],_=0;_<p.length;_++){var A=p[_],B=n[_];A===P?C.merge(B):S.merge(B)}var O=function(V){var M=e.spawn();return V.forEach(function(D){M.merge(D),D.connectedEdges().forEach(function(I){e.contains(I)&&!E.contains(I)&&M.merge(I)})}),M},N=[O(C),O(S)];return{cut:E,components:N,partition1:C,partition2:S}}De("At least 2 nodes are required for Karger-Stein algorithm")}},Hr=function(e,t,n){return{x:e.x*t+n.x,y:e.y*t+n.y}},xs=function(e,t,n){return{x:(e.x-n.x)/t,y:(e.y-n.y)/t}},Cn=function(e){return{x:e[0],y:e[1]}},xr=function(e,t){return Math.atan2(t,e)-Math.PI/2},oi=Math.log2||function(e){return Math.log(e)/Math.log(2)},Ii=function(e){return e>0?1:e<0?-1:0},rn=function(e,t){return Math.sqrt($t(e,t))},$t=function(e,t){var n=t.x-e.x,r=t.y-e.y;return n*n+r*r},Pu=function(e){for(var t=e.length,n=0,r=0;r<t;r++)n+=e[r];for(var a=0;a<t;a++)e[a]=e[a]/n;return e},Oe=function(e,t,n,r){return(1-r)*(1-r)*e+2*(1-r)*r*t+r*r*n},Dn=function(e,t,n,r){return{x:Oe(e.x,t.x,n.x,r),y:Oe(e.y,t.y,n.y,r)}},Gn=function(e,t,n){return Math.max(e,Math.min(n,t))},tt=function(e){if(e==null)return{x1:1/0,y1:1/0,x2:-1/0,y2:-1/0,w:0,h:0};if(e.x1!=null&&e.y1!=null){if(e.x2!=null&&e.y2!=null&&e.x2>=e.x1&&e.y2>=e.y1)return{x1:e.x1,y1:e.y1,x2:e.x2,y2:e.y2,w:e.x2-e.x1,h:e.y2-e.y1};if(e.w!=null&&e.h!=null&&e.w>=0&&e.h>=0)return{x1:e.x1,y1:e.y1,x2:e.x1+e.w,y2:e.y1+e.h,w:e.w,h:e.h}}},ws=function(e,t){e.x1=Math.min(e.x1,t.x1),e.x2=Math.max(e.x2,t.x2),e.w=e.x2-e.x1,e.y1=Math.min(e.y1,t.y1),e.y2=Math.max(e.y2,t.y2),e.h=e.y2-e.y1},Su=function(e,t,n){e.x1=Math.min(e.x1,t),e.x2=Math.max(e.x2,t),e.w=e.x2-e.x1,e.y1=Math.min(e.y1,n),e.y2=Math.max(e.y2,n),e.h=e.y2-e.y1},Nr=function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return e.x1-=t,e.x2+=t,e.y1-=t,e.y2+=t,e.w=e.x2-e.x1,e.h=e.y2-e.y1,e},wr=function(e){var t,n,r,a,i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[0];if(i.length===1)t=n=r=a=i[0];else if(i.length===2)t=r=i[0],a=n=i[1];else if(i.length===4){var o=ze(i,4);t=o[0],n=o[1],r=o[2],a=o[3]}return e.x1-=a,e.x2+=n,e.y1-=t,e.y2+=r,e.w=e.x2-e.x1,e.h=e.y2-e.y1,e},Ni=function(e,t){e.x1=t.x1,e.y1=t.y1,e.x2=t.x2,e.y2=t.y2,e.w=e.x2-e.x1,e.h=e.y2-e.y1},si=function(e,t){return!(e.x1>t.x2)&&!(t.x1>e.x2)&&!(e.x2<t.x1)&&!(t.x2<e.x1)&&!(e.y2<t.y1)&&!(t.y2<e.y1)&&!(e.y1>t.y2)&&!(t.y1>e.y2)},Pn=function(e,t,n){return e.x1<=t&&t<=e.x2&&e.y1<=n&&n<=e.y2},Es=function(e,t){return Pn(e,t.x1,t.y1)&&Pn(e,t.x2,t.y2)},Li=function(e,t,n,r,a,i,o){var s,l,u=arguments.length>7&&arguments[7]!==void 0?arguments[7]:"auto",c=u==="auto"?on(a,i):u,d=a/2,h=i/2,p=(c=Math.min(c,d,h))!==d,f=c!==h;if(p){var v=r-h-o;if((s=Vt(e,t,n,r,n-d+c-o,v,n+d-c+o,v,!1)).length>0)return s}if(f){var m=n+d+o;if((s=Vt(e,t,n,r,m,r-h+c-o,m,r+h-c+o,!1)).length>0)return s}if(p){var g=r+h+o;if((s=Vt(e,t,n,r,n-d+c-o,g,n+d-c+o,g,!1)).length>0)return s}if(f){var y=n-d-o;if((s=Vt(e,t,n,r,y,r-h+c-o,y,r+h-c+o,!1)).length>0)return s}var b=n-d+c,k=r-h+c;if((l=Zn(e,t,n,r,b,k,c+o)).length>0&&l[0]<=b&&l[1]<=k)return[l[0],l[1]];var x=n+d-c,w=r-h+c;if((l=Zn(e,t,n,r,x,w,c+o)).length>0&&l[0]>=x&&l[1]<=w)return[l[0],l[1]];var E=n+d-c,C=r+h-c;if((l=Zn(e,t,n,r,E,C,c+o)).length>0&&l[0]>=E&&l[1]>=C)return[l[0],l[1]];var S=n-d+c,P=r+h-c;return(l=Zn(e,t,n,r,S,P,c+o)).length>0&&l[0]<=S&&l[1]>=P?[l[0],l[1]]:[]},Bu=function(e,t,n,r,a,i,o){var s=o,l=Math.min(n,a),u=Math.max(n,a),c=Math.min(r,i),d=Math.max(r,i);return l-s<=e&&e<=u+s&&c-s<=t&&t<=d+s},Du=function(e,t,n,r,a,i,o,s,l){var u=Math.min(n,o,a)-l,c=Math.max(n,o,a)+l,d=Math.min(r,s,i)-l,h=Math.max(r,s,i)+l;return!(e<u||e>c||t<d||t>h)},_u=function(e,t,n,r,a,i,o,s){var l=[];(function(m,g,y,b,k){var x,w,E,C,S,P,_,A;m===0&&(m=1e-5),E=-27*(b/=m)+(g/=m)*(9*(y/=m)-g*g*2),x=(w=(3*y-g*g)/9)*w*w+(E/=54)*E,k[1]=0,_=g/3,x>0?(S=(S=E+Math.sqrt(x))<0?-Math.pow(-S,1/3):Math.pow(S,1/3),P=(P=E-Math.sqrt(x))<0?-Math.pow(-P,1/3):Math.pow(P,1/3),k[0]=-_+S+P,_+=(S+P)/2,k[4]=k[2]=-_,_=Math.sqrt(3)*(-P+S)/2,k[3]=_,k[5]=-_):(k[5]=k[3]=0,x===0?(A=E<0?-Math.pow(-E,1/3):Math.pow(E,1/3),k[0]=2*A-_,k[4]=k[2]=-(A+_)):(C=(w=-w)*w*w,C=Math.acos(E/Math.sqrt(C)),A=2*Math.sqrt(w),k[0]=-_+A*Math.cos(C/3),k[2]=-_+A*Math.cos((C+2*Math.PI)/3),k[4]=-_+A*Math.cos((C+4*Math.PI)/3)))})(1*n*n-4*n*a+2*n*o+4*a*a-4*a*o+o*o+r*r-4*r*i+2*r*s+4*i*i-4*i*s+s*s,9*n*a-3*n*n-3*n*o-6*a*a+3*a*o+9*r*i-3*r*r-3*r*s-6*i*i+3*i*s,3*n*n-6*n*a+n*o-n*e+2*a*a+2*a*e-o*e+3*r*r-6*r*i+r*s-r*t+2*i*i+2*i*t-s*t,1*n*a-n*n+n*e-a*e+r*i-r*r+r*t-i*t,l);for(var u=[],c=0;c<6;c+=2)Math.abs(l[c+1])<1e-7&&l[c]>=0&&l[c]<=1&&u.push(l[c]);u.push(1),u.push(0);for(var d,h,p,f=-1,v=0;v<u.length;v++)d=Math.pow(1-u[v],2)*n+2*(1-u[v])*u[v]*a+u[v]*u[v]*o,h=Math.pow(1-u[v],2)*r+2*(1-u[v])*u[v]*i+u[v]*u[v]*s,p=Math.pow(d-e,2)+Math.pow(h-t,2),f>=0?p<f&&(f=p):f=p;return f},Au=function(e,t,n,r,a,i){var o=[e-n,t-r],s=[a-n,i-r],l=s[0]*s[0]+s[1]*s[1],u=o[0]*o[0]+o[1]*o[1],c=o[0]*s[0]+o[1]*s[1],d=c*c/l;return c<0?u:d>l?(e-a)*(e-a)+(t-i)*(t-i):u-d},et=function(e,t,n){for(var r,a,i,o,s=0,l=0;l<n.length/2;l++)if(r=n[2*l],a=n[2*l+1],l+1<n.length/2?(i=n[2*(l+1)],o=n[2*(l+1)+1]):(i=n[2*(l+1-n.length/2)],o=n[2*(l+1-n.length/2)+1]),!(r==e&&i==e)){if(!(r>=e&&e>=i||r<=e&&e<=i))continue;(e-r)/(i-r)*(o-a)+a>t&&s++}return s%2!=0},Ct=function(e,t,n,r,a,i,o,s,l){var u,c=new Array(n.length);s[0]!=null?(u=Math.atan(s[1]/s[0]),s[0]<0?u+=Math.PI/2:u=-u-Math.PI/2):u=s;for(var d,h=Math.cos(-u),p=Math.sin(-u),f=0;f<c.length/2;f++)c[2*f]=i/2*(n[2*f]*h-n[2*f+1]*p),c[2*f+1]=o/2*(n[2*f+1]*h+n[2*f]*p),c[2*f]+=r,c[2*f+1]+=a;if(l>0){var v=Gr(c,-l);d=Ur(v)}else d=c;return et(e,t,d)},Ur=function(e){for(var t,n,r,a,i,o,s,l,u=new Array(e.length/2),c=0;c<e.length/4;c++){t=e[4*c],n=e[4*c+1],r=e[4*c+2],a=e[4*c+3],c<e.length/4-1?(i=e[4*(c+1)],o=e[4*(c+1)+1],s=e[4*(c+1)+2],l=e[4*(c+1)+3]):(i=e[0],o=e[1],s=e[2],l=e[3]);var d=Vt(t,n,r,a,i,o,s,l,!0);u[2*c]=d[0],u[2*c+1]=d[1]}return u},Gr=function(e,t){for(var n,r,a,i,o=new Array(2*e.length),s=0;s<e.length/2;s++){n=e[2*s],r=e[2*s+1],s<e.length/2-1?(a=e[2*(s+1)],i=e[2*(s+1)+1]):(a=e[0],i=e[1]);var l=i-r,u=-(a-n),c=Math.sqrt(l*l+u*u),d=l/c,h=u/c;o[4*s]=n+d*t,o[4*s+1]=r+h*t,o[4*s+2]=a+d*t,o[4*s+3]=i+h*t}return o},Ht=function(e,t,n,r,a,i,o){return e-=a,t-=i,(e/=n/2+o)*e+(t/=r/2+o)*t<=1},Zn=function(e,t,n,r,a,i,o){var s=[n-e,r-t],l=[e-a,t-i],u=s[0]*s[0]+s[1]*s[1],c=2*(l[0]*s[0]+l[1]*s[1]),d=c*c-4*u*(l[0]*l[0]+l[1]*l[1]-o*o);if(d<0)return[];var h=(-c+Math.sqrt(d))/(2*u),p=(-c-Math.sqrt(d))/(2*u),f=Math.min(h,p),v=Math.max(h,p),m=[];if(f>=0&&f<=1&&m.push(f),v>=0&&v<=1&&m.push(v),m.length===0)return[];var g=m[0]*s[0]+e,y=m[0]*s[1]+t;return m.length>1?m[0]==m[1]?[g,y]:[g,y,m[1]*s[0]+e,m[1]*s[1]+t]:[g,y]},ma=function(e,t,n){return t<=e&&e<=n||n<=e&&e<=t?e:e<=t&&t<=n||n<=t&&t<=e?t:n},Vt=function(e,t,n,r,a,i,o,s,l){var u=e-a,c=n-e,d=o-a,h=t-i,p=r-t,f=s-i,v=d*h-f*u,m=c*h-p*u,g=f*c-d*p;if(g!==0){var y=v/g,b=m/g,k=-.001;return k<=y&&y<=1.001&&k<=b&&b<=1.001||l?[e+y*c,t+y*p]:[]}return v===0||m===0?ma(e,n,o)===o?[o,s]:ma(e,n,a)===a?[a,i]:ma(a,o,n)===n?[n,r]:[]:[]},ur=function(e,t,n,r,a,i,o,s){var l,u,c,d,h,p,f=[],v=new Array(n.length),m=!0;if(i==null&&(m=!1),m){for(var g=0;g<v.length/2;g++)v[2*g]=n[2*g]*i+r,v[2*g+1]=n[2*g+1]*o+a;if(s>0){var y=Gr(v,-s);u=Ur(y)}else u=v}else u=n;for(var b=0;b<u.length/2;b++)c=u[2*b],d=u[2*b+1],b<u.length/2-1?(h=u[2*(b+1)],p=u[2*(b+1)+1]):(h=u[0],p=u[1]),(l=Vt(e,t,r,a,c,d,h,p)).length!==0&&f.push(l[0],l[1]);return f},Er=function(e,t,n){var r=[e[0]-t[0],e[1]-t[1]],a=Math.sqrt(r[0]*r[0]+r[1]*r[1]),i=(a-n)/a;return i<0&&(i=1e-5),[t[0]+i*r[0],t[1]+i*r[1]]},Qe=function(e,t){var n=Ma(e,t);return n=Ts(n)},Ts=function(e){for(var t,n,r=e.length/2,a=1/0,i=1/0,o=-1/0,s=-1/0,l=0;l<r;l++)t=e[2*l],n=e[2*l+1],a=Math.min(a,t),o=Math.max(o,t),i=Math.min(i,n),s=Math.max(s,n);for(var u=2/(o-a),c=2/(s-i),d=0;d<r;d++)t=e[2*d]=e[2*d]*u,n=e[2*d+1]=e[2*d+1]*c,a=Math.min(a,t),o=Math.max(o,t),i=Math.min(i,n),s=Math.max(s,n);if(i<-1)for(var h=0;h<r;h++)n=e[2*h+1]=e[2*h+1]+(-1-i);return e},Ma=function(e,t){var n=1/e*2*Math.PI,r=e%2==0?Math.PI/2+n/2:Math.PI/2;r+=t;for(var a,i=new Array(2*e),o=0;o<e;o++)a=o*n+r,i[2*o]=Math.cos(a),i[2*o+1]=Math.sin(-a);return i},on=function(e,t){return Math.min(e/4,t/4,8)},ks=function(e,t){return Math.min(e/10,t/10,8)},Ra=function(e,t){return{heightOffset:Math.min(15,.05*t),widthOffset:Math.min(100,.25*e),ctrlPtOffsetPct:.05}},Mu=Ve({dampingFactor:.8,precision:1e-6,iterations:200,weight:function(e){return 1}}),Ru={pageRank:function(e){for(var t=Mu(e),n=t.dampingFactor,r=t.precision,a=t.iterations,i=t.weight,o=this._private.cy,s=this.byGroup(),l=s.nodes,u=s.edges,c=l.length,d=c*c,h=u.length,p=new Array(d),f=new Array(c),v=(1-n)/c,m=0;m<c;m++){for(var g=0;g<c;g++)p[m*c+g]=0;f[m]=0}for(var y=0;y<h;y++){var b=u[y],k=b.data("source"),x=b.data("target");if(k!==x){var w=l.indexOfId(k),E=l.indexOfId(x),C=i(b);p[E*c+w]+=C,f[w]+=C}}for(var S=1/c+v,P=0;P<c;P++)if(f[P]===0)for(var _=0;_<c;_++)p[_*c+P]=S;else for(var A=0;A<c;A++){var B=A*c+P;p[B]=p[B]/f[P]+v}for(var O,N=new Array(c),V=new Array(c),M=0;M<c;M++)N[M]=1;for(var D=0;D<a;D++){for(var I=0;I<c;I++)V[I]=0;for(var z=0;z<c;z++)for(var q=0;q<c;q++){var j=z*c+q;V[z]+=p[j]*N[q]}Pu(V),O=N,N=V,V=O;for(var U=0,K=0;K<c;K++){var X=O[K]-N[K];U+=X*X}if(U<r)break}return{rank:function(H){return H=o.collection(H)[0],N[l.indexOf(H)]}}}},Oi=Ve({root:null,weight:function(e){return 1},directed:!1,alpha:0}),vn={degreeCentralityNormalized:function(e){e=Oi(e);var t=this.cy(),n=this.nodes(),r=n.length;if(e.directed){for(var a={},i={},o=0,s=0,l=0;l<r;l++){var u=n[l],c=u.id();e.root=u;var d=this.degreeCentrality(e);o<d.indegree&&(o=d.indegree),s<d.outdegree&&(s=d.outdegree),a[c]=d.indegree,i[c]=d.outdegree}return{indegree:function(g){return o==0?0:(ce(g)&&(g=t.filter(g)),a[g.id()]/o)},outdegree:function(g){return s===0?0:(ce(g)&&(g=t.filter(g)),i[g.id()]/s)}}}for(var h={},p=0,f=0;f<r;f++){var v=n[f];e.root=v;var m=this.degreeCentrality(e);p<m.degree&&(p=m.degree),h[v.id()]=m.degree}return{degree:function(g){return p===0?0:(ce(g)&&(g=t.filter(g)),h[g.id()]/p)}}},degreeCentrality:function(e){e=Oi(e);var t=this.cy(),n=this,r=e,a=r.root,i=r.weight,o=r.directed,s=r.alpha;if(a=t.collection(a)[0],o){for(var l=a.connectedEdges(),u=l.filter(function(x){return x.target().same(a)&&n.has(x)}),c=l.filter(function(x){return x.source().same(a)&&n.has(x)}),d=u.length,h=c.length,p=0,f=0,v=0;v<u.length;v++)p+=i(u[v]);for(var m=0;m<c.length;m++)f+=i(c[m]);return{indegree:Math.pow(d,1-s)*Math.pow(p,s),outdegree:Math.pow(h,1-s)*Math.pow(f,s)}}for(var g=a.connectedEdges().intersection(n),y=g.length,b=0,k=0;k<g.length;k++)b+=i(g[k]);return{degree:Math.pow(y,1-s)*Math.pow(b,s)}}};vn.dc=vn.degreeCentrality,vn.dcn=vn.degreeCentralityNormalised=vn.degreeCentralityNormalized;var zi=Ve({harmonic:!0,weight:function(){return 1},directed:!1,root:null}),yn={closenessCentralityNormalized:function(e){for(var t=zi(e),n=t.harmonic,r=t.weight,a=t.directed,i=this.cy(),o={},s=0,l=this.nodes(),u=this.floydWarshall({weight:r,directed:a}),c=0;c<l.length;c++){for(var d=0,h=l[c],p=0;p<l.length;p++)if(c!==p){var f=u.distance(h,l[p]);d+=n?1/f:f}n||(d=1/d),s<d&&(s=d),o[h.id()]=d}return{closeness:function(v){return s==0?0:(v=ce(v)?i.filter(v)[0].id():v.id(),o[v]/s)}}},closenessCentrality:function(e){var t=zi(e),n=t.root,r=t.weight,a=t.directed,i=t.harmonic;n=this.filter(n)[0];for(var o=this.dijkstra({root:n,weight:r,directed:a}),s=0,l=this.nodes(),u=0;u<l.length;u++){var c=l[u];if(!c.same(n)){var d=o.distanceTo(c);s+=i?1/d:d}}return i?s:1/s}};yn.cc=yn.closenessCentrality,yn.ccn=yn.closenessCentralityNormalised=yn.closenessCentralityNormalized;var Iu=Ve({weight:null,directed:!1}),Ia={betweennessCentrality:function(e){for(var t=Iu(e),n=t.directed,r=t.weight,a=r!=null,i=this.cy(),o=this.nodes(),s={},l={},u=0,c=function(y,b){l[y]=b,b>u&&(u=b)},d=function(y){return l[y]},h=0;h<o.length;h++){var p=o[h],f=p.id();s[f]=n?p.outgoers().nodes():p.openNeighborhood().nodes(),c(f,0)}for(var v=function(y){for(var b=o[y].id(),k=[],x={},w={},E={},C=new yr(function(K,X){return E[K]-E[X]}),S=0;S<o.length;S++){var P=o[S].id();x[P]=[],w[P]=0,E[P]=1/0}for(w[b]=1,E[b]=0,C.push(b);!C.empty();){var _=C.pop();if(k.push(_),a)for(var A=0;A<s[_].length;A++){var B=s[_][A],O=i.getElementById(_),N=void 0;N=O.edgesTo(B).length>0?O.edgesTo(B)[0]:B.edgesTo(O)[0];var V=r(N);B=B.id(),E[B]>E[_]+V&&(E[B]=E[_]+V,C.nodes.indexOf(B)<0?C.push(B):C.updateItem(B),w[B]=0,x[B]=[]),E[B]==E[_]+V&&(w[B]=w[B]+w[_],x[B].push(_))}else for(var M=0;M<s[_].length;M++){var D=s[_][M].id();E[D]==1/0&&(C.push(D),E[D]=E[_]+1),E[D]==E[_]+1&&(w[D]=w[D]+w[_],x[D].push(_))}}for(var I={},z=0;z<o.length;z++)I[o[z].id()]=0;for(;k.length>0;){for(var q=k.pop(),j=0;j<x[q].length;j++){var U=x[q][j];I[U]=I[U]+w[U]/w[q]*(1+I[q])}q!=o[y].id()&&c(q,d(q)+I[q])}},m=0;m<o.length;m++)v(m);var g={betweenness:function(y){var b=i.collection(y).id();return d(b)},betweennessNormalized:function(y){if(u==0)return 0;var b=i.collection(y).id();return d(b)/u}};return g.betweennessNormalised=g.betweennessNormalized,g}};Ia.bc=Ia.betweennessCentrality;var Nu=Ve({expandFactor:2,inflateFactor:2,multFactor:1,maxIterations:20,attributes:[function(e){return 1}]}),Lu=function(e,t){for(var n=0,r=0;r<t.length;r++)n+=t[r](e);return n},Cs=function(e,t){for(var n,r=0;r<t;r++){n=0;for(var a=0;a<t;a++)n+=e[a*t+r];for(var i=0;i<t;i++)e[i*t+r]=e[i*t+r]/n}},Ou=function(e,t,n){for(var r=new Array(n*n),a=0;a<n;a++){for(var i=0;i<n;i++)r[a*n+i]=0;for(var o=0;o<n;o++)for(var s=0;s<n;s++)r[a*n+s]+=e[a*n+o]*t[o*n+s]}return r},zu=function(e,t,n){for(var r=e.slice(0),a=1;a<n;a++)e=Ou(e,r,t);return e},Vu=function(e,t,n){for(var r=new Array(t*t),a=0;a<t*t;a++)r[a]=Math.pow(e[a],n);return Cs(r,t),r},Fu=function(e,t,n,r){for(var a=0;a<n;a++)if(Math.round(e[a]*Math.pow(10,r))/Math.pow(10,r)!==Math.round(t[a]*Math.pow(10,r))/Math.pow(10,r))return!1;return!0},qu=function(e,t){for(var n=0;n<e.length;n++)if(!t[n]||e[n].id()!==t[n].id())return!1;return!0},Vi=function(e){for(var t=this.nodes(),n=this.edges(),r=this.cy(),a=function(k){return Nu(k)}(e),i={},o=0;o<t.length;o++)i[t[o].id()]=o;for(var s,l=t.length,u=l*l,c=new Array(u),d=0;d<u;d++)c[d]=0;for(var h=0;h<n.length;h++){var p=n[h],f=i[p.source().id()],v=i[p.target().id()],m=Lu(p,a.attributes);c[f*l+v]+=m,c[v*l+f]+=m}(function(k,x,w){for(var E=0;E<x;E++)k[E*x+E]=w})(c,l,a.multFactor),Cs(c,l);for(var g=!0,y=0;g&&y<a.maxIterations;)g=!1,s=zu(c,l,a.expandFactor),c=Vu(s,l,a.inflateFactor),Fu(c,s,u,4)||(g=!0),y++;var b=function(k,x,w,E){for(var C=[],S=0;S<x;S++){for(var P=[],_=0;_<x;_++)Math.round(1e3*k[S*x+_])/1e3>0&&P.push(w[_]);P.length!==0&&C.push(E.collection(P))}return C}(c,l,t,r);return b=function(k){for(var x=0;x<k.length;x++)for(var w=0;w<k.length;w++)x!=w&&qu(k[x],k[w])&&k.splice(w,1);return k}(b),b},Xu={markovClustering:Vi,mcl:Vi},Yu=function(e){return e},Ps=function(e,t){return Math.abs(t-e)},Fi=function(e,t,n){return e+Ps(t,n)},qi=function(e,t,n){return e+Math.pow(n-t,2)},ju=function(e){return Math.sqrt(e)},Wu=function(e,t,n){return Math.max(e,Ps(t,n))},zn=function(e,t,n,r,a){for(var i=arguments.length>5&&arguments[5]!==void 0?arguments[5]:Yu,o=r,s=0;s<e;s++)o=a(o,t(s),n(s));return i(o)},Sn={euclidean:function(e,t,n){return e>=2?zn(e,t,n,0,qi,ju):zn(e,t,n,0,Fi)},squaredEuclidean:function(e,t,n){return zn(e,t,n,0,qi)},manhattan:function(e,t,n){return zn(e,t,n,0,Fi)},max:function(e,t,n){return zn(e,t,n,-1/0,Wu)}};function sa(e,t,n,r,a,i){var o;return o=_e(e)?e:Sn[e]||Sn.euclidean,t===0&&_e(e)?o(a,i):o(t,n,r,a,i)}Sn["squared-euclidean"]=Sn.squaredEuclidean,Sn.squaredeuclidean=Sn.squaredEuclidean;var Ku=Ve({k:2,m:2,sensitivityThreshold:1e-4,distance:"euclidean",maxIterations:10,attributes:[],testMode:!1,testCentroids:null}),Na=function(e){return Ku(e)},Zr=function(e,t,n,r,a){var i=a!=="kMedoids"?function(l){return n[l]}:function(l){return r[l](n)},o=n,s=t;return sa(e,r.length,i,function(l){return r[l](t)},o,s)},ba=function(e,t,n){for(var r=n.length,a=new Array(r),i=new Array(r),o=new Array(t),s=null,l=0;l<r;l++)a[l]=e.min(n[l]).value,i[l]=e.max(n[l]).value;for(var u=0;u<t;u++){s=[];for(var c=0;c<r;c++)s[c]=Math.random()*(i[c]-a[c])+a[c];o[u]=s}return o},Xi=function(e,t,n,r,a){for(var i=1/0,o=0,s=0;s<t.length;s++){var l=Zr(n,e,t[s],r,a);l<i&&(i=l,o=s)}return o},Yi=function(e,t,n){for(var r=[],a=null,i=0;i<t.length;i++)n[(a=t[i]).id()]===e&&r.push(a);return r},Hu=function(e,t,n){return Math.abs(t-e)<=n},Uu=function(e,t,n){for(var r=0;r<e.length;r++)for(var a=0;a<e[r].length;a++)if(Math.abs(e[r][a]-t[r][a])>n)return!1;return!0},Gu=function(e,t,n){for(var r=0;r<n;r++)if(e===t[r])return!0;return!1},ji=function(e,t){var n=new Array(t);if(e.length<50)for(var r=0;r<t;r++){for(var a=e[Math.floor(Math.random()*e.length)];Gu(a,n,r);)a=e[Math.floor(Math.random()*e.length)];n[r]=a}else for(var i=0;i<t;i++)n[i]=e[Math.floor(Math.random()*e.length)];return n},Wi=function(e,t,n){for(var r=0,a=0;a<t.length;a++)r+=Zr("manhattan",t[a],e,n,"kMedoids");return r},Zu=function(e,t,n,r,a){for(var i,o,s=0;s<t.length;s++)for(var l=0;l<e.length;l++)r[s][l]=Math.pow(n[s][l],a.m);for(var u=0;u<e.length;u++)for(var c=0;c<a.attributes.length;c++){i=0,o=0;for(var d=0;d<t.length;d++)i+=r[d][u]*a.attributes[c](t[d]),o+=r[d][u];e[u][c]=i/o}},$u=function(e,t,n,r,a){for(var i=0;i<e.length;i++)t[i]=e[i].slice();for(var o,s,l,u=2/(a.m-1),c=0;c<n.length;c++)for(var d=0;d<r.length;d++){o=0;for(var h=0;h<n.length;h++)s=Zr(a.distance,r[d],n[c],a.attributes,"cmeans"),l=Zr(a.distance,r[d],n[h],a.attributes,"cmeans"),o+=Math.pow(s/l,u);e[d][c]=1/o}},Ki=function(e){var t,n,r,a,i,o=this.cy(),s=this.nodes(),l=Na(e);a=new Array(s.length);for(var u=0;u<s.length;u++)a[u]=new Array(l.k);r=new Array(s.length);for(var c=0;c<s.length;c++)r[c]=new Array(l.k);for(var d=0;d<s.length;d++){for(var h=0,p=0;p<l.k;p++)r[d][p]=Math.random(),h+=r[d][p];for(var f=0;f<l.k;f++)r[d][f]=r[d][f]/h}n=new Array(l.k);for(var v=0;v<l.k;v++)n[v]=new Array(l.attributes.length);i=new Array(s.length);for(var m=0;m<s.length;m++)i[m]=new Array(l.k);for(var g=!0,y=0;g&&y<l.maxIterations;)g=!1,Zu(n,s,r,i,l),$u(r,a,n,s,l),Uu(r,a,l.sensitivityThreshold)||(g=!0),y++;return t=function(b,k,x,w){for(var E,C,S=new Array(x.k),P=0;P<S.length;P++)S[P]=[];for(var _=0;_<k.length;_++){E=-1/0,C=-1;for(var A=0;A<k[0].length;A++)k[_][A]>E&&(E=k[_][A],C=A);S[C].push(b[_])}for(var B=0;B<S.length;B++)S[B]=w.collection(S[B]);return S}(s,r,l,o),{clusters:t,degreeOfMembership:r}},Qu={kMeans:function(e){var t,n=this.cy(),r=this.nodes(),a=null,i=Na(e),o=new Array(i.k),s={};i.testMode?typeof i.testCentroids=="number"?(i.testCentroids,t=ba(r,i.k,i.attributes)):t=Ne(i.testCentroids)==="object"?i.testCentroids:ba(r,i.k,i.attributes):t=ba(r,i.k,i.attributes);for(var l=!0,u=0;l&&u<i.maxIterations;){for(var c=0;c<r.length;c++)s[(a=r[c]).id()]=Xi(a,t,i.distance,i.attributes,"kMeans");l=!1;for(var d=0;d<i.k;d++){var h=Yi(d,r,s);if(h.length!==0){for(var p=i.attributes.length,f=t[d],v=new Array(p),m=new Array(p),g=0;g<p;g++){m[g]=0;for(var y=0;y<h.length;y++)a=h[y],m[g]+=i.attributes[g](a);v[g]=m[g]/h.length,Hu(v[g],f[g],i.sensitivityThreshold)||(l=!0)}t[d]=v,o[d]=n.collection(h)}}u++}return o},kMedoids:function(e){var t,n,r=this.cy(),a=this.nodes(),i=null,o=Na(e),s=new Array(o.k),l={},u=new Array(o.k);o.testMode?typeof o.testCentroids=="number"||(t=Ne(o.testCentroids)==="object"?o.testCentroids:ji(a,o.k)):t=ji(a,o.k);for(var c=!0,d=0;c&&d<o.maxIterations;){for(var h=0;h<a.length;h++)l[(i=a[h]).id()]=Xi(i,t,o.distance,o.attributes,"kMedoids");c=!1;for(var p=0;p<t.length;p++){var f=Yi(p,a,l);if(f.length!==0){u[p]=Wi(t[p],f,o.attributes);for(var v=0;v<f.length;v++)(n=Wi(f[v],f,o.attributes))<u[p]&&(u[p]=n,t[p]=f[v],c=!0);s[p]=r.collection(f)}}d++}return s},fuzzyCMeans:Ki,fcm:Ki},Ju=Ve({distance:"euclidean",linkage:"min",mode:"threshold",threshold:1/0,addDendrogram:!1,dendrogramDepth:0,attributes:[]}),ec={single:"min",complete:"max"},Hi=function(e,t,n,r,a){for(var i,o=0,s=1/0,l=a.attributes,u=function(E,C){return sa(a.distance,l.length,function(S){return l[S](E)},function(S){return l[S](C)},E,C)},c=0;c<e.length;c++){var d=e[c].key,h=n[d][r[d]];h<s&&(o=d,s=h)}if(a.mode==="threshold"&&s>=a.threshold||a.mode==="dendrogram"&&e.length===1)return!1;var p,f=t[o],v=t[r[o]];p=a.mode==="dendrogram"?{left:f,right:v,key:f.key}:{value:f.value.concat(v.value),key:f.key},e[f.index]=p,e.splice(v.index,1),t[f.key]=p;for(var m=0;m<e.length;m++){var g=e[m];f.key===g.key?i=1/0:a.linkage==="min"?(i=n[f.key][g.key],n[f.key][g.key]>n[v.key][g.key]&&(i=n[v.key][g.key])):a.linkage==="max"?(i=n[f.key][g.key],n[f.key][g.key]<n[v.key][g.key]&&(i=n[v.key][g.key])):i=a.linkage==="mean"?(n[f.key][g.key]*f.size+n[v.key][g.key]*v.size)/(f.size+v.size):a.mode==="dendrogram"?u(g.value,f.value):u(g.value[0],f.value[0]),n[f.key][g.key]=n[g.key][f.key]=i}for(var y=0;y<e.length;y++){var b=e[y].key;if(r[b]===f.key||r[b]===v.key){for(var k=b,x=0;x<e.length;x++){var w=e[x].key;n[b][w]<n[b][k]&&(k=w)}r[b]=k}e[y].index=y}return f.key=v.key=f.index=v.index=null,!0},Tr=function e(t,n,r){t&&(t.value?n.push(t.value):(t.left&&e(t.left,n),t.right&&e(t.right,n)))},tc=function e(t,n){if(!t)return"";if(t.left&&t.right){var r=e(t.left,n),a=e(t.right,n),i=n.add({group:"nodes",data:{id:r+","+a}});return n.add({group:"edges",data:{source:r,target:i.id()}}),n.add({group:"edges",data:{source:a,target:i.id()}}),i.id()}return t.value?t.value.id():void 0},nc=function e(t,n,r){if(!t)return[];var a=[],i=[],o=[];return n===0?(t.left&&Tr(t.left,a),t.right&&Tr(t.right,i),o=a.concat(i),[r.collection(o)]):n===1?t.value?[r.collection(t.value)]:(t.left&&Tr(t.left,a),t.right&&Tr(t.right,i),[r.collection(a),r.collection(i)]):t.value?[r.collection(t.value)]:(t.left&&(a=e(t.left,n-1,r)),t.right&&(i=e(t.right,n-1,r)),a.concat(i))},Ui=function(e){for(var t=this.cy(),n=this.nodes(),r=function(g){var y=Ju(g),b=ec[y.linkage];return b!=null&&(y.linkage=b),y}(e),a=r.attributes,i=function(g,y){return sa(r.distance,a.length,function(b){return a[b](g)},function(b){return a[b](y)},g,y)},o=[],s=[],l=[],u=[],c=0;c<n.length;c++){var d={value:r.mode==="dendrogram"?n[c]:[n[c]],key:c,index:c};o[c]=d,u[c]=d,s[c]=[],l[c]=0}for(var h=0;h<o.length;h++)for(var p=0;p<=h;p++){var f=void 0;f=r.mode==="dendrogram"?h===p?1/0:i(o[h].value,o[p].value):h===p?1/0:i(o[h].value[0],o[p].value[0]),s[h][p]=f,s[p][h]=f,f<s[h][l[h]]&&(l[h]=p)}for(var v,m=Hi(o,u,s,l,r);m;)m=Hi(o,u,s,l,r);return r.mode==="dendrogram"?(v=nc(o[0],r.dendrogramDepth,t),r.addDendrogram&&tc(o[0],t)):(v=new Array(o.length),o.forEach(function(g,y){g.key=g.index=null,v[y]=t.collection(g.value)})),v},rc={hierarchicalClustering:Ui,hca:Ui},ac=Ve({distance:"euclidean",preference:"median",damping:.8,maxIterations:1e3,minIterations:100,attributes:[]}),ic=function(e,t,n,r){var a=function(i,o){return r[o](i)};return-sa(e,r.length,function(i){return a(t,i)},function(i){return a(n,i)},t,n)},oc=function(e,t){var n=null;return n=t==="median"?function(r){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:r.length,o=!(arguments.length>4&&arguments[4]!==void 0)||arguments[4],s=!(arguments.length>5&&arguments[5]!==void 0)||arguments[5];arguments.length>3&&arguments[3]!==void 0&&!arguments[3]?(i<r.length&&r.splice(i,r.length-i),a>0&&r.splice(0,a)):r=r.slice(a,i);for(var l=0,u=r.length-1;u>=0;u--){var c=r[u];s?isFinite(c)||(r[u]=-1/0,l++):r.splice(u,1)}o&&r.sort(function(p,f){return p-f});var d=r.length,h=Math.floor(d/2);return d%2!=0?r[h+1+l]:(r[h-1+l]+r[h+l])/2}(e):t==="mean"?function(r){for(var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:r.length,o=0,s=0,l=a;l<i;l++){var u=r[l];isFinite(u)&&(o+=u,s++)}return o/s}(e):t==="min"?function(r){for(var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:r.length,o=1/0,s=a;s<i;s++){var l=r[s];isFinite(l)&&(o=Math.min(l,o))}return o}(e):t==="max"?function(r){for(var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:r.length,o=-1/0,s=a;s<i;s++){var l=r[s];isFinite(l)&&(o=Math.max(l,o))}return o}(e):t,n},Gi=function(e,t,n){for(var r=[],a=0;a<e;a++){for(var i=-1,o=-1/0,s=0;s<n.length;s++){var l=n[s];t[a*e+l]>o&&(i=l,o=t[a*e+l])}i>0&&r.push(i)}for(var u=0;u<n.length;u++)r[n[u]]=n[u];return r},Zi=function(e){for(var t,n,r,a,i,o,s=this.cy(),l=this.nodes(),u=function($){var ne=$.damping,T=$.preference;.5<=ne&&ne<1||De("Damping must range on [0.5, 1).  Got: ".concat(ne));var R=["median","mean","min","max"];return R.some(function(L){return L===T})||ae(T)||De("Preference must be one of [".concat(R.map(function(L){return"'".concat(L,"'")}).join(", "),"] or a number.  Got: ").concat(T)),ac($)}(e),c={},d=0;d<l.length;d++)c[l[d].id()]=d;n=(t=l.length)*t,r=new Array(n);for(var h=0;h<n;h++)r[h]=-1/0;for(var p=0;p<t;p++)for(var f=0;f<t;f++)p!==f&&(r[p*t+f]=ic(u.distance,l[p],l[f],u.attributes));a=oc(r,u.preference);for(var v=0;v<t;v++)r[v*t+v]=a;i=new Array(n);for(var m=0;m<n;m++)i[m]=0;o=new Array(n);for(var g=0;g<n;g++)o[g]=0;for(var y=new Array(t),b=new Array(t),k=new Array(t),x=0;x<t;x++)y[x]=0,b[x]=0,k[x]=0;for(var w,E=new Array(t*u.minIterations),C=0;C<E.length;C++)E[C]=0;for(w=0;w<u.maxIterations;w++){for(var S=0;S<t;S++){for(var P=-1/0,_=-1/0,A=-1,B=0,O=0;O<t;O++)y[O]=i[S*t+O],(B=o[S*t+O]+r[S*t+O])>=P?(_=P,P=B,A=O):B>_&&(_=B);for(var N=0;N<t;N++)i[S*t+N]=(1-u.damping)*(r[S*t+N]-P)+u.damping*y[N];i[S*t+A]=(1-u.damping)*(r[S*t+A]-_)+u.damping*y[A]}for(var V=0;V<t;V++){for(var M=0,D=0;D<t;D++)y[D]=o[D*t+V],b[D]=Math.max(0,i[D*t+V]),M+=b[D];M-=b[V],b[V]=i[V*t+V],M+=b[V];for(var I=0;I<t;I++)o[I*t+V]=(1-u.damping)*Math.min(0,M-b[I])+u.damping*y[I];o[V*t+V]=(1-u.damping)*(M-b[V])+u.damping*y[V]}for(var z=0,q=0;q<t;q++){var j=o[q*t+q]+i[q*t+q]>0?1:0;E[w%u.minIterations*t+q]=j,z+=j}if(z>0&&(w>=u.minIterations-1||w==u.maxIterations-1)){for(var U=0,K=0;K<t;K++){k[K]=0;for(var X=0;X<u.minIterations;X++)k[K]+=E[X*t+K];k[K]!==0&&k[K]!==u.minIterations||U++}if(U===t)break}}for(var H=function($,ne,T){for(var R=[],L=0;L<$;L++)ne[L*$+L]+T[L*$+L]>0&&R.push(L);return R}(t,i,o),W=function($,ne,T){for(var R=Gi($,ne,T),L=0;L<T.length;L++){for(var Y=[],F=0;F<R.length;F++)R[F]===T[L]&&Y.push(F);for(var J=-1,G=-1/0,Z=0;Z<Y.length;Z++){for(var ee=0,oe=0;oe<Y.length;oe++)ee+=ne[Y[oe]*$+Y[Z]];ee>G&&(J=Z,G=ee)}T[L]=Y[J]}return Gi($,ne,T)}(t,r,H),Q={},te=0;te<H.length;te++)Q[H[te]]=[];for(var ie=0;ie<l.length;ie++){var se=W[c[l[ie].id()]];se!=null&&Q[se].push(l[ie])}for(var le=new Array(H.length),fe=0;fe<H.length;fe++)le[fe]=s.collection(Q[H[fe]]);return le},sc={affinityPropagation:Zi,ap:Zi},lc=Ve({root:void 0,directed:!1}),uc={hierholzer:function(e){if(!me(e)){var t=arguments;e={root:t[0],directed:t[1]}}var n,r,a,i=lc(e),o=i.root,s=i.directed,l=this,u=!1;o&&(a=ce(o)?this.filter(o)[0].id():o[0].id());var c={},d={};s?l.forEach(function(g){var y=g.id();if(g.isNode()){var b=g.indegree(!0),k=g.outdegree(!0),x=b-k,w=k-b;x==1?n?u=!0:n=y:w==1?r?u=!0:r=y:(w>1||x>1)&&(u=!0),c[y]=[],g.outgoers().forEach(function(E){E.isEdge()&&c[y].push(E.id())})}else d[y]=[void 0,g.target().id()]}):l.forEach(function(g){var y=g.id();g.isNode()?(g.degree(!0)%2&&(n?r?u=!0:r=y:n=y),c[y]=[],g.connectedEdges().forEach(function(b){return c[y].push(b.id())})):d[y]=[g.source().id(),g.target().id()]});var h={found:!1,trail:void 0};if(u)return h;if(r&&n)if(s){if(a&&r!=a)return h;a=r}else{if(a&&r!=a&&n!=a)return h;a||(a=r)}else a||(a=l[0].id());var p=function(g){for(var y,b,k,x=g,w=[g];c[x].length;)y=c[x].shift(),b=d[y][0],x!=(k=d[y][1])?(c[k]=c[k].filter(function(E){return E!=y}),x=k):s||x==b||(c[b]=c[b].filter(function(E){return E!=y}),x=b),w.unshift(y),w.unshift(x);return w},f=[],v=[];for(v=p(a);v.length!=1;)c[v[0]].length==0?(f.unshift(l.getElementById(v.shift())),f.unshift(l.getElementById(v.shift()))):v=p(v.shift()).concat(v);for(var m in f.unshift(l.getElementById(v.shift())),c)if(c[m].length)return h;return h.found=!0,h.trail=this.spawn(f,!0),h}},kr=function(){var e=this,t={},n=0,r=0,a=[],i=[],o={},s=function u(c,d,h){c===h&&(r+=1),t[d]={id:n,low:n++,cutVertex:!1};var p,f,v,m,g=e.getElementById(d).connectedEdges().intersection(e);g.size()===0?a.push(e.spawn(e.getElementById(d))):g.forEach(function(y){p=y.source().id(),f=y.target().id(),(v=p===d?f:p)!==h&&(m=y.id(),o[m]||(o[m]=!0,i.push({x:d,y:v,edge:y})),v in t?t[d].low=Math.min(t[d].low,t[v].id):(u(c,v,d),t[d].low=Math.min(t[d].low,t[v].low),t[d].id<=t[v].low&&(t[d].cutVertex=!0,function(b,k){for(var x=i.length-1,w=[],E=e.spawn();i[x].x!=b||i[x].y!=k;)w.push(i.pop().edge),x--;w.push(i.pop().edge),w.forEach(function(C){var S=C.connectedNodes().intersection(e);E.merge(C),S.forEach(function(P){var _=P.id(),A=P.connectedEdges().intersection(e);E.merge(P),t[_].cutVertex?E.merge(A.filter(function(B){return B.isLoop()})):E.merge(A)})}),a.push(E)}(d,v))))})};e.forEach(function(u){if(u.isNode()){var c=u.id();c in t||(r=0,s(c,c),t[c].cutVertex=r>1)}});var l=Object.keys(t).filter(function(u){return t[u].cutVertex}).map(function(u){return e.getElementById(u)});return{cut:e.spawn(l),components:a}},Cr=function(){var e=this,t={},n=0,r=[],a=[],i=e.spawn(e),o=function s(l){if(a.push(l),t[l]={index:n,low:n++,explored:!1},e.getElementById(l).connectedEdges().intersection(e).forEach(function(p){var f=p.target().id();f!==l&&(f in t||s(f),t[f].explored||(t[l].low=Math.min(t[l].low,t[f].low)))}),t[l].index===t[l].low){for(var u=e.spawn();;){var c=a.pop();if(u.merge(e.getElementById(c)),t[c].low=t[l].index,t[c].explored=!0,c===l)break}var d=u.edgesWith(u),h=u.merge(d);r.push(h),i=i.difference(h)}};return e.forEach(function(s){if(s.isNode()){var l=s.id();l in t||o(l)}}),{cut:i,components:r}},Ss={};[Un,gu,vu,mu,xu,Eu,Cu,Ru,vn,yn,Ia,Xu,Qu,rc,sc,uc,{hopcroftTarjanBiconnected:kr,htbc:kr,htb:kr,hopcroftTarjanBiconnectedComponents:kr},{tarjanStronglyConnected:Cr,tsc:Cr,tscc:Cr,tarjanStronglyConnectedComponents:Cr}].forEach(function(e){he(Ss,e)});var St=function e(t){if(!(this instanceof e))return new e(t);this.id="Thenable/1.0.7",this.state=0,this.fulfillValue=void 0,this.rejectReason=void 0,this.onFulfilled=[],this.onRejected=[],this.proxy={then:this.then.bind(this)},typeof t=="function"&&t.call(this,this.fulfill.bind(this),this.reject.bind(this))};St.prototype={fulfill:function(e){return $i(this,1,"fulfillValue",e)},reject:function(e){return $i(this,2,"rejectReason",e)},then:function(e,t){var n=this,r=new St;return n.onFulfilled.push(Ji(e,r,"fulfill")),n.onRejected.push(Ji(t,r,"reject")),Bs(n),r.proxy}};var $i=function(e,t,n,r){return e.state===0&&(e.state=t,e[n]=r,Bs(e)),e},Bs=function(e){e.state===1?Qi(e,"onFulfilled",e.fulfillValue):e.state===2&&Qi(e,"onRejected",e.rejectReason)},Qi=function(e,t,n){if(e[t].length!==0){var r=e[t];e[t]=[];var a=function(){for(var i=0;i<r.length;i++)r[i](n)};typeof setImmediate=="function"?setImmediate(a):setTimeout(a,0)}},Ji=function(e,t,n){return function(r){if(typeof e!="function")t[n].call(t,r);else{var a;try{a=e(r)}catch(i){return void t.reject(i)}cc(t,a)}}},cc=function e(t,n){if(t!==n&&t.proxy!==n){var r;if(Ne(n)==="object"&&n!==null||typeof n=="function")try{r=n.then}catch(i){return void t.reject(i)}if(typeof r!="function")t.fulfill(n);else{var a=!1;try{r.call(n,function(i){a||(a=!0,i===n?t.reject(new TypeError("circular thenable chain")):e(t,i))},function(i){a||(a=!0,t.reject(i))})}catch(i){a||t.reject(i)}}}else t.reject(new TypeError("cannot resolve promise with itself"))};St.all=function(e){return new St(function(t,n){for(var r=new Array(e.length),a=0,i=function(s,l){r[s]=l,++a===e.length&&t(r)},o=0;o<e.length;o++)(function(s){var l=e[s];l!=null&&l.then!=null?l.then(function(u){i(s,u)},function(u){n(u)}):i(s,l)})(o)})},St.resolve=function(e){return new St(function(t,n){t(e)})},St.reject=function(e){return new St(function(t,n){n(e)})};var Mn=typeof Promise<"u"?Promise:St,La=function(e,t,n){var r=ni(e),a=!r,i=this._private=he({duration:1e3},t,n);if(i.target=e,i.style=i.style||i.css,i.started=!1,i.playing=!1,i.hooked=!1,i.applying=!1,i.progress=0,i.completes=[],i.frames=[],i.complete&&_e(i.complete)&&i.completes.push(i.complete),a){var o=e.position();i.startPosition=i.startPosition||{x:o.x,y:o.y},i.startStyle=i.startStyle||e.cy().style().getAnimationStartStyle(e,i.style)}if(r){var s=e.pan();i.startPan={x:s.x,y:s.y},i.startZoom=e.zoom()}this.length=1,this[0]=this},Ut=La.prototype;he(Ut,{instanceString:function(){return"animation"},hook:function(){var e=this._private;if(!e.hooked){var t=e.target._private.animation;(e.queue?t.queue:t.current).push(this),at(e.target)&&e.target.cy().addToAnimationPool(e.target),e.hooked=!0}return this},play:function(){var e=this._private;return e.progress===1&&(e.progress=0),e.playing=!0,e.started=!1,e.stopped=!1,this.hook(),this},playing:function(){return this._private.playing},apply:function(){var e=this._private;return e.applying=!0,e.started=!1,e.stopped=!1,this.hook(),this},applying:function(){return this._private.applying},pause:function(){var e=this._private;return e.playing=!1,e.started=!1,this},stop:function(){var e=this._private;return e.playing=!1,e.started=!1,e.stopped=!0,this},rewind:function(){return this.progress(0)},fastforward:function(){return this.progress(1)},time:function(e){var t=this._private;return e===void 0?t.progress*t.duration:this.progress(e/t.duration)},progress:function(e){var t=this._private,n=t.playing;return e===void 0?t.progress:(n&&this.pause(),t.progress=e,t.started=!1,n&&this.play(),this)},completed:function(){return this._private.progress===1},reverse:function(){var e=this._private,t=e.playing;t&&this.pause(),e.progress=1-e.progress,e.started=!1;var n=function(s,l){var u=e[s];u!=null&&(e[s]=e[l],e[l]=u)};if(n("zoom","startZoom"),n("pan","startPan"),n("position","startPosition"),e.style)for(var r=0;r<e.style.length;r++){var a=e.style[r],i=a.name,o=e.startStyle[i];e.startStyle[i]=a,e.style[r]=o}return t&&this.play(),this},promise:function(e){var t,n=this._private;return e==="frame"?t=n.frames:t=n.completes,new Mn(function(r,a){t.push(function(){r()})})}}),Ut.complete=Ut.completed,Ut.run=Ut.play,Ut.running=Ut.playing;var dc={animated:function(){return function(){var e=this,t=e.length!==void 0?e:[e];if(!(this._private.cy||this).styleEnabled())return!1;var n=t[0];return n?n._private.animation.current.length>0:void 0}},clearQueue:function(){return function(){var e=this,t=e.length!==void 0?e:[e];if(!(this._private.cy||this).styleEnabled())return this;for(var n=0;n<t.length;n++)t[n]._private.animation.queue=[];return this}},delay:function(){return function(e,t){return(this._private.cy||this).styleEnabled()?this.animate({delay:e,duration:e,complete:t}):this}},delayAnimation:function(){return function(e,t){return(this._private.cy||this).styleEnabled()?this.animation({delay:e,duration:e,complete:t}):this}},animation:function(){return function(e,t){var n=this,r=n.length!==void 0,a=r?n:[n],i=this._private.cy||this,o=!r,s=!o;if(!i.styleEnabled())return this;var l=i.style();if(e=he({},e,t),Object.keys(e).length===0)return new La(a[0],e);switch(e.duration===void 0&&(e.duration=400),e.duration){case"slow":e.duration=600;break;case"fast":e.duration=200}if(s&&(e.style=l.getPropsList(e.style||e.css),e.css=void 0),s&&e.renderedPosition!=null){var u=e.renderedPosition,c=i.pan(),d=i.zoom();e.position=xs(u,d,c)}if(o&&e.panBy!=null){var h=e.panBy,p=i.pan();e.pan={x:p.x+h.x,y:p.y+h.y}}var f=e.center||e.centre;if(o&&f!=null){var v=i.getCenterPan(f.eles,e.zoom);v!=null&&(e.pan=v)}if(o&&e.fit!=null){var m=e.fit,g=i.getFitViewport(m.eles||m.boundingBox,m.padding);g!=null&&(e.pan=g.pan,e.zoom=g.zoom)}if(o&&me(e.zoom)){var y=i.getZoomedViewport(e.zoom);y!=null?(y.zoomed&&(e.zoom=y.zoom),y.panned&&(e.pan=y.pan)):e.zoom=null}return new La(a[0],e)}},animate:function(){return function(e,t){var n=this,r=n.length!==void 0?n:[n];if(!(this._private.cy||this).styleEnabled())return this;t&&(e=he({},e,t));for(var a=0;a<r.length;a++){var i=r[a],o=i.animated()&&(e.queue===void 0||e.queue);i.animation(e,o?{queue:!0}:void 0).play()}return this}},stop:function(){return function(e,t){var n=this,r=n.length!==void 0?n:[n],a=this._private.cy||this;if(!a.styleEnabled())return this;for(var i=0;i<r.length;i++){for(var o=r[i]._private,s=o.animation.current,l=0;l<s.length;l++){var u=s[l]._private;t&&(u.duration=0)}e&&(o.animation.queue=[]),t||(o.animation.current=[])}return a.notify("draw"),this}}},la=Array.isArray,hc=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,fc=/^\w*$/,pc=function(e,t){if(la(e))return!1;var n=typeof e;return!(n!="number"&&n!="symbol"&&n!="boolean"&&e!=null&&!gr(e))||fc.test(e)||!hc.test(e)||t!=null&&e in Object(t)},eo,gc=function(e){if(!an(e))return!1;var t=hs(e);return t=="[object Function]"||t=="[object GeneratorFunction]"||t=="[object AsyncFunction]"||t=="[object Proxy]"},xa=ia["__core-js_shared__"],to=(eo=/[^.]+$/.exec(xa&&xa.keys&&xa.keys.IE_PROTO||""))?"Symbol(src)_1."+eo:"",vc=function(e){return!!to&&to in e},yc=Function.prototype.toString,mc=function(e){if(e!=null){try{return yc.call(e)}catch{}try{return e+""}catch{}}return""},bc=/^\[object .+?Constructor\]$/,xc=Function.prototype,wc=Object.prototype,Ec=xc.toString,Tc=wc.hasOwnProperty,kc=RegExp("^"+Ec.call(Tc).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),Cc=function(e){return!(!an(e)||vc(e))&&(gc(e)?kc:bc).test(mc(e))},Pc=function(e,t){return e==null?void 0:e[t]},li=function(e,t){var n=Pc(e,t);return Cc(n)?n:void 0},cr=li(Object,"create"),Sc=function(){this.__data__=cr?cr(null):{},this.size=0},Bc=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},Dc=Object.prototype.hasOwnProperty,_c=function(e){var t=this.__data__;if(cr){var n=t[e];return n==="__lodash_hash_undefined__"?void 0:n}return Dc.call(t,e)?t[e]:void 0},Ac=Object.prototype.hasOwnProperty,Mc=function(e){var t=this.__data__;return cr?t[e]!==void 0:Ac.call(t,e)},Rc=function(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=cr&&t===void 0?"__lodash_hash_undefined__":t,this};function mn(e){var t=-1,n=e==null?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}mn.prototype.clear=Sc,mn.prototype.delete=Bc,mn.prototype.get=_c,mn.prototype.has=Mc,mn.prototype.set=Rc;var no=mn,Ic=function(){this.__data__=[],this.size=0},Ds=function(e,t){return e===t||e!=e&&t!=t},ua=function(e,t){for(var n=e.length;n--;)if(Ds(e[n][0],t))return n;return-1},Nc=Array.prototype.splice,Lc=function(e){var t=this.__data__,n=ua(t,e);return!(n<0)&&(n==t.length-1?t.pop():Nc.call(t,n,1),--this.size,!0)},Oc=function(e){var t=this.__data__,n=ua(t,e);return n<0?void 0:t[n][1]},zc=function(e){return ua(this.__data__,e)>-1},Vc=function(e,t){var n=this.__data__,r=ua(n,e);return r<0?(++this.size,n.push([e,t])):n[r][1]=t,this};function bn(e){var t=-1,n=e==null?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}bn.prototype.clear=Ic,bn.prototype.delete=Lc,bn.prototype.get=Oc,bn.prototype.has=zc,bn.prototype.set=Vc;var Fc=bn,qc=li(ia,"Map"),Xc=function(){this.size=0,this.__data__={hash:new no,map:new(qc||Fc),string:new no}},Yc=function(e){var t=typeof e;return t=="string"||t=="number"||t=="symbol"||t=="boolean"?e!=="__proto__":e===null},ca=function(e,t){var n=e.__data__;return Yc(t)?n[typeof t=="string"?"string":"hash"]:n.map},jc=function(e){var t=ca(this,e).delete(e);return this.size-=t?1:0,t},Wc=function(e){return ca(this,e).get(e)},Kc=function(e){return ca(this,e).has(e)},Hc=function(e,t){var n=ca(this,e),r=n.size;return n.set(e,t),this.size+=n.size==r?0:1,this};function xn(e){var t=-1,n=e==null?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}xn.prototype.clear=Xc,xn.prototype.delete=jc,xn.prototype.get=Wc,xn.prototype.has=Kc,xn.prototype.set=Hc;var _s=xn;function ui(e,t){if(typeof e!="function"||t!=null&&typeof t!="function")throw new TypeError("Expected a function");var n=function(){var r=arguments,a=t?t.apply(this,r):r[0],i=n.cache;if(i.has(a))return i.get(a);var o=e.apply(this,r);return n.cache=i.set(a,o)||i,o};return n.cache=new(ui.Cache||_s),n}ui.Cache=_s;var Uc=ui,Gc=function(e){var t=Uc(e,function(r){return n.size===500&&n.clear(),r}),n=t.cache;return t},Zc=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,$c=/\\(\\)?/g,Qc=Gc(function(e){var t=[];return e.charCodeAt(0)===46&&t.push(""),e.replace(Zc,function(n,r,a,i){t.push(a?i.replace($c,"$1"):r||n)}),t}),As=Qc,Ms=function(e,t){for(var n=-1,r=e==null?0:e.length,a=Array(r);++n<r;)a[n]=t(e[n],n,e);return a},ro=_n?_n.prototype:void 0,ao=ro?ro.toString:void 0,Jc=function e(t){if(typeof t=="string")return t;if(la(t))return Ms(t,e)+"";if(gr(t))return ao?ao.call(t):"";var n=t+"";return n=="0"&&1/t==-1/0?"-0":n},Rs=function(e){return e==null?"":Jc(e)},Is=function(e,t){return la(e)?e:pc(e,t)?[e]:As(Rs(e))},ci=function(e){if(typeof e=="string"||gr(e))return e;var t=e+"";return t=="0"&&1/e==-1/0?"-0":t},ed=function(e,t){for(var n=0,r=(t=Is(t,e)).length;e!=null&&n<r;)e=e[ci(t[n++])];return n&&n==r?e:void 0},td=function(e,t,n){var r=e==null?void 0:ed(e,t);return r===void 0?n:r},io=function(){try{var e=li(Object,"defineProperty");return e({},"",{}),e}catch{}}(),nd=function(e,t,n){t=="__proto__"&&io?io(e,t,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[t]=n},rd=Object.prototype.hasOwnProperty,ad=function(e,t,n){var r=e[t];rd.call(e,t)&&Ds(r,n)&&(n!==void 0||t in e)||nd(e,t,n)},id=/^(?:0|[1-9]\d*)$/,od=function(e,t){var n=typeof e;return!!(t=t??9007199254740991)&&(n=="number"||n!="symbol"&&id.test(e))&&e>-1&&e%1==0&&e<t},sd=function(e,t,n,r){if(!an(e))return e;for(var a=-1,i=(t=Is(t,e)).length,o=i-1,s=e;s!=null&&++a<i;){var l=ci(t[a]),u=n;if(l==="__proto__"||l==="constructor"||l==="prototype")return e;if(a!=o){var c=s[l];(u=r?r(c,l,s):void 0)===void 0&&(u=an(c)?c:od(t[a+1])?[]:{})}ad(s,l,u),s=s[l]}return e},ld=function(e,t,n){return e==null?e:sd(e,t,n)},ud=function(e,t){var n=-1,r=e.length;for(t||(t=Array(r));++n<r;)t[n]=e[n];return t},cd=function(e){return la(e)?Ms(e,ci):gr(e)?[e]:ud(As(Rs(e)))},dd={data:function(e){return e=he({},{field:"data",bindingEvent:"data",allowBinding:!1,allowSetting:!1,allowGetting:!1,settingEvent:"data",settingTriggersEvent:!1,triggerFnName:"trigger",immutableKeys:{},updateStyle:!1,beforeGet:function(t){},beforeSet:function(t,n){},onSet:function(t){},canSet:function(t){return!0}},e),function(t,n){var r=e,a=this,i=a.length!==void 0,o=i?a:[a],s=i?a[0]:a;if(ce(t)){var l,u=t.indexOf(".")!==-1&&cd(t);if(r.allowGetting&&n===void 0)return s&&(r.beforeGet(s),l=u&&s._private[r.field][t]===void 0?td(s._private[r.field],u):s._private[r.field][t]),l;if(r.allowSetting&&n!==void 0&&!r.immutableKeys[t]){var c=ts({},t,n);r.beforeSet(a,c);for(var d=0,h=o.length;d<h;d++){var p=o[d];r.canSet(p)&&(u&&s._private[r.field][t]===void 0?ld(p._private[r.field],u,n):p._private[r.field][t]=n)}r.updateStyle&&a.updateStyle(),r.onSet(a),r.settingTriggersEvent&&a[r.triggerFnName](r.settingEvent)}}else if(r.allowSetting&&me(t)){var f,v,m=t,g=Object.keys(m);r.beforeSet(a,m);for(var y=0;y<g.length;y++)if(v=m[f=g[y]],!r.immutableKeys[f])for(var b=0;b<o.length;b++){var k=o[b];r.canSet(k)&&(k._private[r.field][f]=v)}r.updateStyle&&a.updateStyle(),r.onSet(a),r.settingTriggersEvent&&a[r.triggerFnName](r.settingEvent)}else if(r.allowBinding&&_e(t)){var x=t;a.on(r.bindingEvent,x)}else if(r.allowGetting&&t===void 0){var w;return s&&(r.beforeGet(s),w=s._private[r.field]),w}return a}},removeData:function(e){return e=he({},{field:"data",event:"data",triggerFnName:"trigger",triggerEvent:!1,immutableKeys:{}},e),function(t){var n=e,r=this,a=r.length!==void 0?r:[r];if(ce(t)){for(var i=t.split(/\s+/),o=i.length,s=0;s<o;s++){var l=i[s];if(!Xt(l)&&!n.immutableKeys[l])for(var u=0,c=a.length;u<c;u++)a[u]._private[n.field][l]=void 0}n.triggerEvent&&r[n.triggerFnName](n.event)}else if(t===void 0){for(var d=0,h=a.length;d<h;d++)for(var p=a[d]._private[n.field],f=Object.keys(p),v=0;v<f.length;v++){var m=f[v];!n.immutableKeys[m]&&(p[m]=void 0)}n.triggerEvent&&r[n.triggerFnName](n.event)}return r}}},hd={eventAliasesOn:function(e){var t=e;t.addListener=t.listen=t.bind=t.on,t.unlisten=t.unbind=t.off=t.removeListener,t.trigger=t.emit,t.pon=t.promiseOn=function(n,r){var a=this,i=Array.prototype.slice.call(arguments,0);return new Mn(function(o,s){var l=i.concat([function(c){a.off.apply(a,u),o(c)}]),u=l.concat([]);a.on.apply(a,l)})}}},xe={};[dc,dd,hd].forEach(function(e){he(xe,e)});var fd={animate:xe.animate(),animation:xe.animation(),animated:xe.animated(),clearQueue:xe.clearQueue(),delay:xe.delay(),delayAnimation:xe.delayAnimation(),stop:xe.stop()},Lr={classes:function(e){var t=this;if(e===void 0){var n=[];return t[0]._private.classes.forEach(function(h){return n.push(h)}),n}Te(e)||(e=(e||"").match(/\S+/g)||[]);for(var r=[],a=new An(e),i=0;i<t.length;i++){for(var o=t[i],s=o._private,l=s.classes,u=!1,c=0;c<e.length;c++){var d=e[c];if(!l.has(d)){u=!0;break}}u||(u=l.size!==e.length),u&&(s.classes=a,r.push(o))}return r.length>0&&this.spawn(r).updateStyle().emit("class"),t},addClass:function(e){return this.toggleClass(e,!0)},hasClass:function(e){var t=this[0];return t!=null&&t._private.classes.has(e)},toggleClass:function(e,t){Te(e)||(e=e.match(/\S+/g)||[]);for(var n=this,r=t===void 0,a=[],i=0,o=n.length;i<o;i++)for(var s=n[i],l=s._private.classes,u=!1,c=0;c<e.length;c++){var d=e[c],h=l.has(d),p=!1;t||r&&!h?(l.add(d),p=!0):(!t||r&&h)&&(l.delete(d),p=!0),!u&&p&&(a.push(s),u=!0)}return a.length>0&&this.spawn(a).updateStyle().emit("class"),n},removeClass:function(e){return this.toggleClass(e,!1)},flashClass:function(e,t){var n=this;if(t==null)t=250;else if(t===0)return n;return n.addClass(e),setTimeout(function(){n.removeClass(e)},t),n}};Lr.className=Lr.classNames=Lr.classes;var ye={metaChar:"[\\!\\\"\\#\\$\\%\\&\\'\\(\\)\\*\\+\\,\\.\\/\\:\\;\\<\\=\\>\\?\\@\\[\\]\\^\\`\\{\\|\\}\\~]",comparatorOp:"=|\\!=|>|>=|<|<=|\\$=|\\^=|\\*=",boolOp:"\\?|\\!|\\^",string:`"(?:\\\\"|[^"])*"|'(?:\\\\'|[^'])*'`,number:Ie,meta:"degree|indegree|outdegree",separator:"\\s*,\\s*",descendant:"\\s+",child:"\\s+>\\s+",subject:"\\$",group:"node|edge|\\*",directedEdge:"\\s+->\\s+",undirectedEdge:"\\s+<->\\s+"};ye.variable="(?:[\\w-.]|(?:\\\\"+ye.metaChar+"))+",ye.className="(?:[\\w-]|(?:\\\\"+ye.metaChar+"))+",ye.value=ye.string+"|"+ye.number,ye.id=ye.variable,function(){var e,t,n;for(e=ye.comparatorOp.split("|"),n=0;n<e.length;n++)t=e[n],ye.comparatorOp+="|@"+t;for(e=ye.comparatorOp.split("|"),n=0;n<e.length;n++)(t=e[n]).indexOf("!")>=0||t!=="="&&(ye.comparatorOp+="|\\!"+t)}();var di=0,Ns=1,Ls=2,hi=3,fi=4,pi=5,gi=6,vi=7,da=8,yi=9,Oa=10,za=11,Os=12,zs=13,Va=14,mi=15,bi=16,$r=17,Qr=18,Jr=19,Bn=20,Fa=[{selector:":selected",matches:function(e){return e.selected()}},{selector:":unselected",matches:function(e){return!e.selected()}},{selector:":selectable",matches:function(e){return e.selectable()}},{selector:":unselectable",matches:function(e){return!e.selectable()}},{selector:":locked",matches:function(e){return e.locked()}},{selector:":unlocked",matches:function(e){return!e.locked()}},{selector:":visible",matches:function(e){return e.visible()}},{selector:":hidden",matches:function(e){return!e.visible()}},{selector:":transparent",matches:function(e){return e.transparent()}},{selector:":grabbed",matches:function(e){return e.grabbed()}},{selector:":free",matches:function(e){return!e.grabbed()}},{selector:":removed",matches:function(e){return e.removed()}},{selector:":inside",matches:function(e){return!e.removed()}},{selector:":grabbable",matches:function(e){return e.grabbable()}},{selector:":ungrabbable",matches:function(e){return!e.grabbable()}},{selector:":animated",matches:function(e){return e.animated()}},{selector:":unanimated",matches:function(e){return!e.animated()}},{selector:":parent",matches:function(e){return e.isParent()}},{selector:":childless",matches:function(e){return e.isChildless()}},{selector:":child",matches:function(e){return e.isChild()}},{selector:":orphan",matches:function(e){return e.isOrphan()}},{selector:":nonorphan",matches:function(e){return e.isChild()}},{selector:":compound",matches:function(e){return e.isNode()?e.isParent():e.source().isParent()||e.target().isParent()}},{selector:":loop",matches:function(e){return e.isLoop()}},{selector:":simple",matches:function(e){return e.isSimple()}},{selector:":active",matches:function(e){return e.active()}},{selector:":inactive",matches:function(e){return!e.active()}},{selector:":backgrounding",matches:function(e){return e.backgrounding()}},{selector:":nonbackgrounding",matches:function(e){return!e.backgrounding()}}].sort(function(e,t){return function(n,r){return-1*ss(n,r)}(e.selector,t.selector)}),pd=function(){for(var e,t={},n=0;n<Fa.length;n++)t[(e=Fa[n]).selector]=e.matches;return t}(),gd="("+Fa.map(function(e){return e.selector}).join("|")+")",sn=function(e){return e.replace(new RegExp("\\\\("+ye.metaChar+")","g"),function(t,n){return n})},Mt=function(e,t,n){e[e.length-1]=n},qa=[{name:"group",query:!0,regex:"("+ye.group+")",populate:function(e,t,n){var r=ze(n,1)[0];t.checks.push({type:di,value:r==="*"?r:r+"s"})}},{name:"state",query:!0,regex:gd,populate:function(e,t,n){var r=ze(n,1)[0];t.checks.push({type:vi,value:r})}},{name:"id",query:!0,regex:"\\#("+ye.id+")",populate:function(e,t,n){var r=ze(n,1)[0];t.checks.push({type:da,value:sn(r)})}},{name:"className",query:!0,regex:"\\.("+ye.className+")",populate:function(e,t,n){var r=ze(n,1)[0];t.checks.push({type:yi,value:sn(r)})}},{name:"dataExists",query:!0,regex:"\\[\\s*("+ye.variable+")\\s*\\]",populate:function(e,t,n){var r=ze(n,1)[0];t.checks.push({type:fi,field:sn(r)})}},{name:"dataCompare",query:!0,regex:"\\[\\s*("+ye.variable+")\\s*("+ye.comparatorOp+")\\s*("+ye.value+")\\s*\\]",populate:function(e,t,n){var r=ze(n,3),a=r[0],i=r[1],o=r[2];o=new RegExp("^"+ye.string+"$").exec(o)!=null?o.substring(1,o.length-1):parseFloat(o),t.checks.push({type:hi,field:sn(a),operator:i,value:o})}},{name:"dataBool",query:!0,regex:"\\[\\s*("+ye.boolOp+")\\s*("+ye.variable+")\\s*\\]",populate:function(e,t,n){var r=ze(n,2),a=r[0],i=r[1];t.checks.push({type:pi,field:sn(i),operator:a})}},{name:"metaCompare",query:!0,regex:"\\[\\[\\s*("+ye.meta+")\\s*("+ye.comparatorOp+")\\s*("+ye.number+")\\s*\\]\\]",populate:function(e,t,n){var r=ze(n,3),a=r[0],i=r[1],o=r[2];t.checks.push({type:gi,field:sn(a),operator:i,value:parseFloat(o)})}},{name:"nextQuery",separator:!0,regex:ye.separator,populate:function(e,t){var n=e.currentSubject,r=e.edgeCount,a=e.compoundCount,i=e[e.length-1];return n!=null&&(i.subject=n,e.currentSubject=null),i.edgeCount=r,i.compoundCount=a,e.edgeCount=0,e.compoundCount=0,e[e.length++]={checks:[]}}},{name:"directedEdge",separator:!0,regex:ye.directedEdge,populate:function(e,t){if(e.currentSubject==null){var n={checks:[]},r=t,a={checks:[]};return n.checks.push({type:za,source:r,target:a}),Mt(e,0,n),e.edgeCount++,a}var i={checks:[]},o=t,s={checks:[]};return i.checks.push({type:Os,source:o,target:s}),Mt(e,0,i),e.edgeCount++,s}},{name:"undirectedEdge",separator:!0,regex:ye.undirectedEdge,populate:function(e,t){if(e.currentSubject==null){var n={checks:[]},r=t,a={checks:[]};return n.checks.push({type:Oa,nodes:[r,a]}),Mt(e,0,n),e.edgeCount++,a}var i={checks:[]},o=t,s={checks:[]};return i.checks.push({type:Va,node:o,neighbor:s}),Mt(e,0,i),s}},{name:"child",separator:!0,regex:ye.child,populate:function(e,t){if(e.currentSubject==null){var n={checks:[]},r={checks:[]},a=e[e.length-1];return n.checks.push({type:mi,parent:a,child:r}),Mt(e,0,n),e.compoundCount++,r}if(e.currentSubject===t){var i={checks:[]},o=e[e.length-1],s={checks:[]},l={checks:[]},u={checks:[]},c={checks:[]};return i.checks.push({type:Jr,left:o,right:s,subject:l}),l.checks=t.checks,t.checks=[{type:Bn}],c.checks.push({type:Bn}),s.checks.push({type:$r,parent:c,child:u}),Mt(e,0,i),e.currentSubject=l,e.compoundCount++,u}var d={checks:[]},h={checks:[]},p=[{type:$r,parent:d,child:h}];return d.checks=t.checks,t.checks=p,e.compoundCount++,h}},{name:"descendant",separator:!0,regex:ye.descendant,populate:function(e,t){if(e.currentSubject==null){var n={checks:[]},r={checks:[]},a=e[e.length-1];return n.checks.push({type:bi,ancestor:a,descendant:r}),Mt(e,0,n),e.compoundCount++,r}if(e.currentSubject===t){var i={checks:[]},o=e[e.length-1],s={checks:[]},l={checks:[]},u={checks:[]},c={checks:[]};return i.checks.push({type:Jr,left:o,right:s,subject:l}),l.checks=t.checks,t.checks=[{type:Bn}],c.checks.push({type:Bn}),s.checks.push({type:Qr,ancestor:c,descendant:u}),Mt(e,0,i),e.currentSubject=l,e.compoundCount++,u}var d={checks:[]},h={checks:[]},p=[{type:Qr,ancestor:d,descendant:h}];return d.checks=t.checks,t.checks=p,e.compoundCount++,h}},{name:"subject",modifier:!0,regex:ye.subject,populate:function(e,t){if(e.currentSubject!=null&&e.currentSubject!==t)return we("Redefinition of subject in selector `"+e.toString()+"`"),!1;e.currentSubject=t;var n=e[e.length-1].checks[0],r=n==null?null:n.type;r===za?n.type=zs:r===Oa&&(n.type=Va,n.node=n.nodes[1],n.neighbor=n.nodes[0],n.nodes=null)}}];qa.forEach(function(e){return e.regexObj=new RegExp("^"+e.regex)});var vd=function(e){for(var t,n,r,a=0;a<qa.length;a++){var i=qa[a],o=i.name,s=e.match(i.regexObj);if(s!=null){n=s,t=i,r=o;var l=s[0];e=e.substring(l.length);break}}return{expr:t,match:n,name:r,remaining:e}},yd={parse:function(e){var t=this,n=t.inputText=e,r=t[0]={checks:[]};for(t.length=1,n=function(c){var d=c.match(/^\s+/);if(d){var h=d[0];c=c.substring(h.length)}return c}(n);;){var a=vd(n);if(a.expr==null)return we("The selector `"+e+"`is invalid"),!1;var i=a.match.slice(1),o=a.expr.populate(t,r,i);if(o===!1)return!1;if(o!=null&&(r=o),(n=a.remaining).match(/^\s*$/))break}var s=t[t.length-1];t.currentSubject!=null&&(s.subject=t.currentSubject),s.edgeCount=t.edgeCount,s.compoundCount=t.compoundCount;for(var l=0;l<t.length;l++){var u=t[l];if(u.compoundCount>0&&u.edgeCount>0)return we("The selector `"+e+"` is invalid because it uses both a compound selector and an edge selector"),!1;if(u.edgeCount>1)return we("The selector `"+e+"` is invalid because it uses multiple edge selectors"),!1;u.edgeCount===1&&we("The selector `"+e+"` is deprecated.  Edge selectors do not take effect on changes to source and target nodes after an edge is added, for performance reasons.  Use a class or data selector on edges instead, updating the class or data of an edge when your app detects a change in source or target nodes.")}return!0},toString:function(){if(this.toStringCache!=null)return this.toStringCache;for(var e=function(l){return l??""},t=function(l){return ce(l)?'"'+l+'"':e(l)},n=function(l){return" "+l+" "},r=function(l,u){var c=l.type,d=l.value;switch(c){case di:var h=e(d);return h.substring(0,h.length-1);case hi:var p=l.field,f=l.operator;return"["+p+n(e(f))+t(d)+"]";case pi:var v=l.operator,m=l.field;return"["+e(v)+m+"]";case fi:return"["+l.field+"]";case gi:var g=l.operator;return"[["+l.field+n(e(g))+t(d)+"]]";case vi:return d;case da:return"#"+d;case yi:return"."+d;case $r:case mi:return a(l.parent,u)+n(">")+a(l.child,u);case Qr:case bi:return a(l.ancestor,u)+" "+a(l.descendant,u);case Jr:var y=a(l.left,u),b=a(l.subject,u),k=a(l.right,u);return y+(y.length>0?" ":"")+b+k;case Bn:return""}},a=function(l,u){return l.checks.reduce(function(c,d,h){return c+(u===l&&h===0?"$":"")+r(d,u)},"")},i="",o=0;o<this.length;o++){var s=this[o];i+=a(s,s.subject),this.length>1&&o<this.length-1&&(i+=", ")}return this.toStringCache=i,i}},oo=function(e,t,n){var r,a,i,o=ce(e),s=ae(e),l=ce(n),u=!1,c=!1,d=!1;switch(t.indexOf("!")>=0&&(t=t.replace("!",""),c=!0),t.indexOf("@")>=0&&(t=t.replace("@",""),u=!0),(o||l||u)&&(a=o||s?""+e:"",i=""+n),u&&(e=a=a.toLowerCase(),n=i=i.toLowerCase()),t){case"*=":r=a.indexOf(i)>=0;break;case"$=":r=a.indexOf(i,a.length-i.length)>=0;break;case"^=":r=a.indexOf(i)===0;break;case"=":r=e===n;break;case">":d=!0,r=e>n;break;case">=":d=!0,r=e>=n;break;case"<":d=!0,r=e<n;break;case"<=":d=!0,r=e<=n;break;default:r=!1}return!c||e==null&&d||(r=!r),r},wa=function(e,t){return e.data(t)},Ae=[],Be=function(e,t){return e.checks.every(function(n){return Ae[n.type](n,t)})};Ae[di]=function(e,t){var n=e.value;return n==="*"||n===t.group()},Ae[vi]=function(e,t){return function(n,r){return pd[n](r)}(e.value,t)},Ae[da]=function(e,t){var n=e.value;return t.id()===n},Ae[yi]=function(e,t){var n=e.value;return t.hasClass(n)},Ae[gi]=function(e,t){var n=e.field,r=e.operator,a=e.value;return oo(function(i,o){return i[o]()}(t,n),r,a)},Ae[hi]=function(e,t){var n=e.field,r=e.operator,a=e.value;return oo(wa(t,n),r,a)},Ae[pi]=function(e,t){var n=e.field,r=e.operator;return function(a,i){switch(i){case"?":return!!a;case"!":return!a;case"^":return a===void 0}}(wa(t,n),r)},Ae[fi]=function(e,t){var n=e.field;return e.operator,wa(t,n)!==void 0},Ae[Oa]=function(e,t){var n=e.nodes[0],r=e.nodes[1],a=t.source(),i=t.target();return Be(n,a)&&Be(r,i)||Be(r,a)&&Be(n,i)},Ae[Va]=function(e,t){return Be(e.node,t)&&t.neighborhood().some(function(n){return n.isNode()&&Be(e.neighbor,n)})},Ae[za]=function(e,t){return Be(e.source,t.source())&&Be(e.target,t.target())},Ae[Os]=function(e,t){return Be(e.source,t)&&t.outgoers().some(function(n){return n.isNode()&&Be(e.target,n)})},Ae[zs]=function(e,t){return Be(e.target,t)&&t.incomers().some(function(n){return n.isNode()&&Be(e.source,n)})},Ae[mi]=function(e,t){return Be(e.child,t)&&Be(e.parent,t.parent())},Ae[$r]=function(e,t){return Be(e.parent,t)&&t.children().some(function(n){return Be(e.child,n)})},Ae[bi]=function(e,t){return Be(e.descendant,t)&&t.ancestors().some(function(n){return Be(e.ancestor,n)})},Ae[Qr]=function(e,t){return Be(e.ancestor,t)&&t.descendants().some(function(n){return Be(e.descendant,n)})},Ae[Jr]=function(e,t){return Be(e.subject,t)&&Be(e.left,t)&&Be(e.right,t)},Ae[Bn]=function(){return!0},Ae[Ns]=function(e,t){return e.value.has(t)},Ae[Ls]=function(e,t){return(0,e.value)(t)};var md={matches:function(e){for(var t=0;t<this.length;t++){var n=this[t];if(Be(n,e))return!0}return!1},filter:function(e){var t=this;if(t.length===1&&t[0].checks.length===1&&t[0].checks[0].type===da)return e.getElementById(t[0].checks[0].value).collection();var n=function(r){for(var a=0;a<t.length;a++){var i=t[a];if(Be(i,r))return!0}return!1};return t.text()==null&&(n=function(){return!0}),e.filter(n)}},jt=function(e){this.inputText=e,this.currentSubject=null,this.compoundCount=0,this.edgeCount=0,this.length=0,e==null||ce(e)&&e.match(/^\s*$/)||(at(e)?this.addQuery({checks:[{type:Ns,value:e.collection()}]}):_e(e)?this.addQuery({checks:[{type:Ls,value:e}]}):ce(e)?this.parse(e)||(this.invalid=!0):De("A selector must be created from a string; found "))},Rt=jt.prototype;[yd,md].forEach(function(e){return he(Rt,e)}),Rt.text=function(){return this.inputText},Rt.size=function(){return this.length},Rt.eq=function(e){return this[e]},Rt.sameText=function(e){return!this.invalid&&!e.invalid&&this.text()===e.text()},Rt.addQuery=function(e){this[this.length++]=e},Rt.selector=Rt.toString;var Nt={allAre:function(e){var t=new jt(e);return this.every(function(n){return t.matches(n)})},is:function(e){var t=new jt(e);return this.some(function(n){return t.matches(n)})},some:function(e,t){for(var n=0;n<this.length;n++)if(t?e.apply(t,[this[n],n,this]):e(this[n],n,this))return!0;return!1},every:function(e,t){for(var n=0;n<this.length;n++)if(!(t?e.apply(t,[this[n],n,this]):e(this[n],n,this)))return!1;return!0},same:function(e){if(this===e)return!0;e=this.cy().collection(e);var t=this.length;return t===e.length&&(t===1?this[0]===e[0]:this.every(function(n){return e.hasElementWithId(n.id())}))},anySame:function(e){return e=this.cy().collection(e),this.some(function(t){return e.hasElementWithId(t.id())})},allAreNeighbors:function(e){e=this.cy().collection(e);var t=this.neighborhood();return e.every(function(n){return t.hasElementWithId(n.id())})},contains:function(e){e=this.cy().collection(e);var t=this;return e.every(function(n){return t.hasElementWithId(n.id())})}};Nt.allAreNeighbours=Nt.allAreNeighbors,Nt.has=Nt.contains,Nt.equal=Nt.equals=Nt.same;var Pr,Vs,ot=function(e,t){return function(n,r,a,i){var o,s=n,l=this;if(s==null?o="":at(s)&&s.length===1&&(o=s.id()),l.length===1&&o){var u=l[0]._private,c=u.traversalCache=u.traversalCache||{},d=c[t]=c[t]||[],h=Yt(o),p=d[h];return p||(d[h]=e.call(l,n,r,a,i))}return e.call(l,n,r,a,i)}},wn={parent:function(e){var t=[];if(this.length===1){var n=this[0]._private.parent;if(n)return n}for(var r=0;r<this.length;r++){var a=this[r]._private.parent;a&&t.push(a)}return this.spawn(t,!0).filter(e)},parents:function(e){for(var t=[],n=this.parent();n.nonempty();){for(var r=0;r<n.length;r++){var a=n[r];t.push(a)}n=n.parent()}return this.spawn(t,!0).filter(e)},commonAncestors:function(e){for(var t,n=0;n<this.length;n++){var r=this[n].parents();t=(t=t||r).intersect(r)}return t.filter(e)},orphans:function(e){return this.stdFilter(function(t){return t.isOrphan()}).filter(e)},nonorphans:function(e){return this.stdFilter(function(t){return t.isChild()}).filter(e)},children:ot(function(e){for(var t=[],n=0;n<this.length;n++)for(var r=this[n]._private.children,a=0;a<r.length;a++)t.push(r[a]);return this.spawn(t,!0).filter(e)},"children"),siblings:function(e){return this.parent().children().not(this).filter(e)},isParent:function(){var e=this[0];if(e)return e.isNode()&&e._private.children.length!==0},isChildless:function(){var e=this[0];if(e)return e.isNode()&&e._private.children.length===0},isChild:function(){var e=this[0];if(e)return e.isNode()&&e._private.parent!=null},isOrphan:function(){var e=this[0];if(e)return e.isNode()&&e._private.parent==null},descendants:function(e){var t=[];return function n(r){for(var a=0;a<r.length;a++){var i=r[a];t.push(i),i.children().nonempty()&&n(i.children())}}(this.children()),this.spawn(t,!0).filter(e)}};function Ea(e,t,n,r){for(var a=[],i=new An,o=e.cy().hasCompoundNodes(),s=0;s<e.length;s++){var l=e[s];n?a.push(l):o&&r(a,i,l)}for(;a.length>0;){var u=a.shift();t(u),i.add(u.id()),o&&r(a,i,u)}return e}function Fs(e,t,n){if(n.isParent())for(var r=n._private.children,a=0;a<r.length;a++){var i=r[a];t.has(i.id())||e.push(i)}}function qs(e,t,n){if(n.isChild()){var r=n._private.parent;t.has(r.id())||e.push(r)}}function bd(e,t,n){qs(e,t,n),Fs(e,t,n)}wn.forEachDown=function(e){return Ea(this,e,!(arguments.length>1&&arguments[1]!==void 0)||arguments[1],Fs)},wn.forEachUp=function(e){return Ea(this,e,!(arguments.length>1&&arguments[1]!==void 0)||arguments[1],qs)},wn.forEachUpAndDown=function(e){return Ea(this,e,!(arguments.length>1&&arguments[1]!==void 0)||arguments[1],bd)},wn.ancestors=wn.parents,(Pr=Vs={data:xe.data({field:"data",bindingEvent:"data",allowBinding:!0,allowSetting:!0,settingEvent:"data",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0,immutableKeys:{id:!0,source:!0,target:!0,parent:!0},updateStyle:!0}),removeData:xe.removeData({field:"data",event:"data",triggerFnName:"trigger",triggerEvent:!0,immutableKeys:{id:!0,source:!0,target:!0,parent:!0},updateStyle:!0}),scratch:xe.data({field:"scratch",bindingEvent:"scratch",allowBinding:!0,allowSetting:!0,settingEvent:"scratch",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0,updateStyle:!0}),removeScratch:xe.removeData({field:"scratch",event:"scratch",triggerFnName:"trigger",triggerEvent:!0,updateStyle:!0}),rscratch:xe.data({field:"rscratch",allowBinding:!1,allowSetting:!0,settingTriggersEvent:!1,allowGetting:!0}),removeRscratch:xe.removeData({field:"rscratch",triggerEvent:!1}),id:function(){var e=this[0];if(e)return e._private.data.id}}).attr=Pr.data,Pr.removeAttr=Pr.removeData;var ht,Xs,xd=Vs,Or={};function Ta(e){return function(t){var n=this;if(t===void 0&&(t=!0),n.length!==0&&n.isNode()&&!n.removed()){for(var r=0,a=n[0],i=a._private.edges,o=0;o<i.length;o++){var s=i[o];!t&&s.isLoop()||(r+=e(a,s))}return r}}}function ln(e,t){return function(n){for(var r,a=this.nodes(),i=0;i<a.length;i++){var o=a[i][e](n);o===void 0||r!==void 0&&!t(o,r)||(r=o)}return r}}he(Or,{degree:Ta(function(e,t){return t.source().same(t.target())?2:1}),indegree:Ta(function(e,t){return t.target().same(e)?1:0}),outdegree:Ta(function(e,t){return t.source().same(e)?1:0})}),he(Or,{minDegree:ln("degree",function(e,t){return e<t}),maxDegree:ln("degree",function(e,t){return e>t}),minIndegree:ln("indegree",function(e,t){return e<t}),maxIndegree:ln("indegree",function(e,t){return e>t}),minOutdegree:ln("outdegree",function(e,t){return e<t}),maxOutdegree:ln("outdegree",function(e,t){return e>t})}),he(Or,{totalDegree:function(e){for(var t=0,n=this.nodes(),r=0;r<n.length;r++)t+=n[r].degree(e);return t}});var Ys=function(e,t,n){for(var r=0;r<e.length;r++){var a=e[r];if(!a.locked()){var i=a._private.position,o={x:t.x!=null?t.x-i.x:0,y:t.y!=null?t.y-i.y:0};!a.isParent()||o.x===0&&o.y===0||a.children().shift(o,n),a.dirtyBoundingBoxCache()}}},so={field:"position",bindingEvent:"position",allowBinding:!0,allowSetting:!0,settingEvent:"position",settingTriggersEvent:!0,triggerFnName:"emitAndNotify",allowGetting:!0,validKeys:["x","y"],beforeGet:function(e){e.updateCompoundBounds()},beforeSet:function(e,t){Ys(e,t,!1)},onSet:function(e){e.dirtyCompoundBoundsCache()},canSet:function(e){return!e.locked()}};ht=Xs={position:xe.data(so),silentPosition:xe.data(he({},so,{allowBinding:!1,allowSetting:!0,settingTriggersEvent:!1,allowGetting:!1,beforeSet:function(e,t){Ys(e,t,!0)},onSet:function(e){e.dirtyCompoundBoundsCache()}})),positions:function(e,t){if(me(e))t?this.silentPosition(e):this.position(e);else if(_e(e)){var n=e,r=this.cy();r.startBatch();for(var a=0;a<this.length;a++){var i,o=this[a];(i=n(o,a))&&(t?o.silentPosition(i):o.position(i))}r.endBatch()}return this},silentPositions:function(e){return this.positions(e,!0)},shift:function(e,t,n){var r;if(me(e)?(r={x:ae(e.x)?e.x:0,y:ae(e.y)?e.y:0},n=t):ce(e)&&ae(t)&&((r={x:0,y:0})[e]=t),r!=null){var a=this.cy();a.startBatch();for(var i=0;i<this.length;i++){var o=this[i];if(!(a.hasCompoundNodes()&&o.isChild()&&o.ancestors().anySame(this))){var s=o.position(),l={x:s.x+r.x,y:s.y+r.y};n?o.silentPosition(l):o.position(l)}}a.endBatch()}return this},silentShift:function(e,t){return me(e)?this.shift(e,!0):ce(e)&&ae(t)&&this.shift(e,t,!0),this},renderedPosition:function(e,t){var n=this[0],r=this.cy(),a=r.zoom(),i=r.pan(),o=me(e)?e:void 0,s=o!==void 0||t!==void 0&&ce(e);if(n&&n.isNode()){if(!s){var l=n.position();return o=Hr(l,a,i),e===void 0?o:o[e]}for(var u=0;u<this.length;u++){var c=this[u];t!==void 0?c.position(e,(t-i[e])/a):o!==void 0&&c.position(xs(o,a,i))}}else if(!s)return;return this},relativePosition:function(e,t){var n=this[0],r=this.cy(),a=me(e)?e:void 0,i=a!==void 0||t!==void 0&&ce(e),o=r.hasCompoundNodes();if(n&&n.isNode()){if(!i){var s=n.position(),l=o?n.parent():null,u=l&&l.length>0,c=u;u&&(l=l[0]);var d=c?l.position():{x:0,y:0};return a={x:s.x-d.x,y:s.y-d.y},e===void 0?a:a[e]}for(var h=0;h<this.length;h++){var p=this[h],f=o?p.parent():null,v=f&&f.length>0,m=v;v&&(f=f[0]);var g=m?f.position():{x:0,y:0};t!==void 0?p.position(e,t+g[e]):a!==void 0&&p.position({x:a.x+g.x,y:a.y+g.y})}}else if(!i)return;return this}},ht.modelPosition=ht.point=ht.position,ht.modelPositions=ht.points=ht.positions,ht.renderedPoint=ht.renderedPosition,ht.relativePoint=ht.relativePosition;var En,Ft,wd=Xs;En=Ft={},Ft.renderedBoundingBox=function(e){var t=this.boundingBox(e),n=this.cy(),r=n.zoom(),a=n.pan(),i=t.x1*r+a.x,o=t.x2*r+a.x,s=t.y1*r+a.y,l=t.y2*r+a.y;return{x1:i,x2:o,y1:s,y2:l,w:o-i,h:l-s}},Ft.dirtyCompoundBoundsCache=function(){var e=arguments.length>0&&arguments[0]!==void 0&&arguments[0],t=this.cy();return t.styleEnabled()&&t.hasCompoundNodes()?(this.forEachUp(function(n){if(n.isParent()){var r=n._private;r.compoundBoundsClean=!1,r.bbCache=null,e||n.emitAndNotify("bounds")}}),this):this},Ft.updateCompoundBounds=function(){var e=arguments.length>0&&arguments[0]!==void 0&&arguments[0],t=this.cy();if(!t.styleEnabled()||!t.hasCompoundNodes())return this;if(!e&&t.batching())return this;function n(o){if(o.isParent()){var s=o._private,l=o.children(),u=o.pstyle("compound-sizing-wrt-labels").value==="include",c={width:{val:o.pstyle("min-width").pfValue,left:o.pstyle("min-width-bias-left"),right:o.pstyle("min-width-bias-right")},height:{val:o.pstyle("min-height").pfValue,top:o.pstyle("min-height-bias-top"),bottom:o.pstyle("min-height-bias-bottom")}},d=l.boundingBox({includeLabels:u,includeOverlays:!1,useCache:!1}),h=s.position;d.w!==0&&d.h!==0||((d={w:o.pstyle("width").pfValue,h:o.pstyle("height").pfValue}).x1=h.x-d.w/2,d.x2=h.x+d.w/2,d.y1=h.y-d.h/2,d.y2=h.y+d.h/2);var p=c.width.left.value;c.width.left.units==="px"&&c.width.val>0&&(p=100*p/c.width.val);var f=c.width.right.value;c.width.right.units==="px"&&c.width.val>0&&(f=100*f/c.width.val);var v=c.height.top.value;c.height.top.units==="px"&&c.height.val>0&&(v=100*v/c.height.val);var m=c.height.bottom.value;c.height.bottom.units==="px"&&c.height.val>0&&(m=100*m/c.height.val);var g=E(c.width.val-d.w,p,f),y=g.biasDiff,b=g.biasComplementDiff,k=E(c.height.val-d.h,v,m),x=k.biasDiff,w=k.biasComplementDiff;s.autoPadding=function(C,S,P,_){if(P.units!=="%")return P.units==="px"?P.pfValue:0;switch(_){case"width":return C>0?P.pfValue*C:0;case"height":return S>0?P.pfValue*S:0;case"average":return C>0&&S>0?P.pfValue*(C+S)/2:0;case"min":return C>0&&S>0?C>S?P.pfValue*S:P.pfValue*C:0;case"max":return C>0&&S>0?C>S?P.pfValue*C:P.pfValue*S:0;default:return 0}}(d.w,d.h,o.pstyle("padding"),o.pstyle("padding-relative-to").value),s.autoWidth=Math.max(d.w,c.width.val),h.x=(-y+d.x1+d.x2+b)/2,s.autoHeight=Math.max(d.h,c.height.val),h.y=(-x+d.y1+d.y2+w)/2}function E(C,S,P){var _=0,A=0,B=S+P;return C>0&&B>0&&(_=S/B*C,A=P/B*C),{biasDiff:_,biasComplementDiff:A}}}for(var r=0;r<this.length;r++){var a=this[r],i=a._private;i.compoundBoundsClean&&!e||(n(a),t.batching()||(i.compoundBoundsClean=!0))}return this};var lt=function(e){return e===1/0||e===-1/0?0:e},pt=function(e,t,n,r,a){r-t!=0&&a-n!=0&&t!=null&&n!=null&&r!=null&&a!=null&&(e.x1=t<e.x1?t:e.x1,e.x2=r>e.x2?r:e.x2,e.y1=n<e.y1?n:e.y1,e.y2=a>e.y2?a:e.y2,e.w=e.x2-e.x1,e.h=e.y2-e.y1)},Qt=function(e,t){return t==null?e:pt(e,t.x1,t.y1,t.x2,t.y2)},Vn=function(e,t,n){return ft(e,t,n)},Sr=function(e,t,n){if(!t.cy().headless()){var r,a,i=t._private,o=i.rstyle,s=o.arrowWidth/2;if(t.pstyle(n+"-arrow-shape").value!=="none"){n==="source"?(r=o.srcX,a=o.srcY):n==="target"?(r=o.tgtX,a=o.tgtY):(r=o.midX,a=o.midY);var l=i.arrowBounds=i.arrowBounds||{},u=l[n]=l[n]||{};u.x1=r-s,u.y1=a-s,u.x2=r+s,u.y2=a+s,u.w=u.x2-u.x1,u.h=u.y2-u.y1,Nr(u,1),pt(e,u.x1,u.y1,u.x2,u.y2)}}},ka=function(e,t,n){if(!t.cy().headless()){var r;r=n?n+"-":"";var a=t._private,i=a.rstyle;if(t.pstyle(r+"label").strValue){var o,s,l,u,c=t.pstyle("text-halign"),d=t.pstyle("text-valign"),h=Vn(i,"labelWidth",n),p=Vn(i,"labelHeight",n),f=Vn(i,"labelX",n),v=Vn(i,"labelY",n),m=t.pstyle(r+"text-margin-x").pfValue,g=t.pstyle(r+"text-margin-y").pfValue,y=t.isEdge(),b=t.pstyle(r+"text-rotation"),k=t.pstyle("text-outline-width").pfValue,x=t.pstyle("text-border-width").pfValue/2,w=t.pstyle("text-background-padding").pfValue,E=p,C=h,S=C/2,P=E/2;if(y)o=f-S,s=f+S,l=v-P,u=v+P;else{switch(c.value){case"left":o=f-C,s=f;break;case"center":o=f-S,s=f+S;break;case"right":o=f,s=f+C}switch(d.value){case"top":l=v-E,u=v;break;case"center":l=v-P,u=v+P;break;case"bottom":l=v,u=v+E}}var _=m-Math.max(k,x)-w-2,A=m+Math.max(k,x)+w+2,B=g-Math.max(k,x)-w-2,O=g+Math.max(k,x)+w+2;o+=_,s+=A,l+=B,u+=O;var N=n||"main",V=a.labelBounds,M=V[N]=V[N]||{};M.x1=o,M.y1=l,M.x2=s,M.y2=u,M.w=s-o,M.h=u-l,M.leftPad=_,M.rightPad=A,M.topPad=B,M.botPad=O;var D=y&&b.strValue==="autorotate",I=b.pfValue!=null&&b.pfValue!==0;if(D||I){var z=D?Vn(a.rstyle,"labelAngle",n):b.pfValue,q=Math.cos(z),j=Math.sin(z),U=(o+s)/2,K=(l+u)/2;if(!y){switch(c.value){case"left":U=s;break;case"right":U=o}switch(d.value){case"top":K=u;break;case"bottom":K=l}}var X=function(le,fe){return{x:(le-=U)*q-(fe-=K)*j+U,y:le*j+fe*q+K}},H=X(o,l),W=X(o,u),Q=X(s,l),te=X(s,u);o=Math.min(H.x,W.x,Q.x,te.x),s=Math.max(H.x,W.x,Q.x,te.x),l=Math.min(H.y,W.y,Q.y,te.y),u=Math.max(H.y,W.y,Q.y,te.y)}var ie=N+"Rot",se=V[ie]=V[ie]||{};se.x1=o,se.y1=l,se.x2=s,se.y2=u,se.w=s-o,se.h=u-l,pt(e,o,l,s,u),pt(a.labelBounds.all,o,l,s,u)}return e}},Ed=function(e,t){var n,r,a,i,o,s,l,u=e._private.cy,c=u.styleEnabled(),d=u.headless(),h=tt(),p=e._private,f=e.isNode(),v=e.isEdge(),m=p.rstyle,g=f&&c?e.pstyle("bounds-expansion").pfValue:[0],y=function(W){return W.pstyle("display").value!=="none"},b=!c||y(e)&&(!v||y(e.source())&&y(e.target()));if(b){var k=0;c&&t.includeOverlays&&e.pstyle("overlay-opacity").value!==0&&(k=e.pstyle("overlay-padding").value);var x=0;c&&t.includeUnderlays&&e.pstyle("underlay-opacity").value!==0&&(x=e.pstyle("underlay-padding").value);var w=Math.max(k,x),E=0;if(c&&(E=e.pstyle("width").pfValue/2),f&&t.includeNodes){var C=e.position();o=C.x,s=C.y;var S=e.outerWidth()/2,P=e.outerHeight()/2;pt(h,n=o-S,a=s-P,r=o+S,i=s+P),c&&t.includeOutlines&&function(W,Q){if(!Q.cy().headless()){var te,ie,se,le=Q.pstyle("outline-opacity").value,fe=Q.pstyle("outline-width").value;if(le>0&&fe>0){var $=Q.pstyle("outline-offset").value,ne=Q.pstyle("shape").value,T=fe+$,R=(W.w+2*T)/W.w,L=(W.h+2*T)/W.h,Y=0;["diamond","pentagon","round-triangle"].includes(ne)?(R=(W.w+2.4*T)/W.w,Y=-T/3.6):["concave-hexagon","rhomboid","right-rhomboid"].includes(ne)?R=(W.w+2.4*T)/W.w:ne==="star"?(R=(W.w+2.8*T)/W.w,L=(W.h+2.6*T)/W.h,Y=-T/3.8):ne==="triangle"?(R=(W.w+2.8*T)/W.w,L=(W.h+2.4*T)/W.h,Y=-T/1.4):ne==="vee"&&(R=(W.w+4.4*T)/W.w,L=(W.h+3.8*T)/W.h,Y=.5*-T);var F=W.h*L-W.h,J=W.w*R-W.w;if(wr(W,[Math.ceil(F/2),Math.ceil(J/2)]),Y!==0){var G=(ie=0,se=Y,{x1:(te=W).x1+ie,x2:te.x2+ie,y1:te.y1+se,y2:te.y2+se,w:te.w,h:te.h});ws(W,G)}}}}(h,e)}else if(v&&t.includeEdges)if(c&&!d){var _=e.pstyle("curve-style").strValue;if(n=Math.min(m.srcX,m.midX,m.tgtX),r=Math.max(m.srcX,m.midX,m.tgtX),a=Math.min(m.srcY,m.midY,m.tgtY),i=Math.max(m.srcY,m.midY,m.tgtY),pt(h,n-=E,a-=E,r+=E,i+=E),_==="haystack"){var A=m.haystackPts;if(A&&A.length===2){if(n=A[0].x,a=A[0].y,n>(r=A[1].x)){var B=n;n=r,r=B}if(a>(i=A[1].y)){var O=a;a=i,i=O}pt(h,n-E,a-E,r+E,i+E)}}else if(_==="bezier"||_==="unbundled-bezier"||_.endsWith("segments")||_.endsWith("taxi")){var N;switch(_){case"bezier":case"unbundled-bezier":N=m.bezierPts;break;case"segments":case"taxi":case"round-segments":case"round-taxi":N=m.linePts}if(N!=null)for(var V=0;V<N.length;V++){var M=N[V];n=M.x-E,r=M.x+E,a=M.y-E,i=M.y+E,pt(h,n,a,r,i)}}}else{var D=e.source().position(),I=e.target().position();if((n=D.x)>(r=I.x)){var z=n;n=r,r=z}if((a=D.y)>(i=I.y)){var q=a;a=i,i=q}pt(h,n-=E,a-=E,r+=E,i+=E)}if(c&&t.includeEdges&&v&&(Sr(h,e,"mid-source"),Sr(h,e,"mid-target"),Sr(h,e,"source"),Sr(h,e,"target")),c&&e.pstyle("ghost").value==="yes"){var j=e.pstyle("ghost-offset-x").pfValue,U=e.pstyle("ghost-offset-y").pfValue;pt(h,h.x1+j,h.y1+U,h.x2+j,h.y2+U)}var K=p.bodyBounds=p.bodyBounds||{};Ni(K,h),wr(K,g),Nr(K,1),c&&(n=h.x1,r=h.x2,a=h.y1,i=h.y2,pt(h,n-w,a-w,r+w,i+w));var X=p.overlayBounds=p.overlayBounds||{};Ni(X,h),wr(X,g),Nr(X,1);var H=p.labelBounds=p.labelBounds||{};H.all!=null?((l=H.all).x1=1/0,l.y1=1/0,l.x2=-1/0,l.y2=-1/0,l.w=0,l.h=0):H.all=tt(),c&&t.includeLabels&&(t.includeMainLabels&&ka(h,e,null),v&&(t.includeSourceLabels&&ka(h,e,"source"),t.includeTargetLabels&&ka(h,e,"target")))}return h.x1=lt(h.x1),h.y1=lt(h.y1),h.x2=lt(h.x2),h.y2=lt(h.y2),h.w=lt(h.x2-h.x1),h.h=lt(h.y2-h.y1),h.w>0&&h.h>0&&b&&(wr(h,g),Nr(h,1)),h},js=function(e){var t=0,n=function(a){return(a?1:0)<<t++},r=0;return r+=n(e.incudeNodes),r+=n(e.includeEdges),r+=n(e.includeLabels),r+=n(e.includeMainLabels),r+=n(e.includeSourceLabels),r+=n(e.includeTargetLabels),r+=n(e.includeOverlays),r+=n(e.includeOutlines)},Ws=function(e){if(e.isEdge()){var t=e.source().position(),n=e.target().position(),r=function(a){return Math.round(a)};return function(a,i){var o={value:0,done:!1},s=0,l=a.length;return ps({next:function(){return s<l?o.value=a[s++]:o.done=!0,o}},i)}([r(t.x),r(t.y),r(n.x),r(n.y)])}return 0},lo=function(e,t){var n,r=e._private,a=e.isEdge(),i=(t==null?uo:js(t))===uo,o=Ws(e),s=r.bbCachePosKey===o,l=t.useCache&&s,u=function(d){return d._private.bbCache==null||d._private.styleDirty};if(!l||u(e)||a&&(u(e.source())||u(e.target()))?(s||e.recalculateRenderedStyle(l),n=Ed(e,dr),r.bbCache=n,r.bbCachePosKey=o):n=r.bbCache,!i){var c=e.isNode();n=tt(),(t.includeNodes&&c||t.includeEdges&&!c)&&(t.includeOverlays?Qt(n,r.overlayBounds):Qt(n,r.bodyBounds)),t.includeLabels&&(t.includeMainLabels&&(!a||t.includeSourceLabels&&t.includeTargetLabels)?Qt(n,r.labelBounds.all):(t.includeMainLabels&&Qt(n,r.labelBounds.mainRot),t.includeSourceLabels&&Qt(n,r.labelBounds.sourceRot),t.includeTargetLabels&&Qt(n,r.labelBounds.targetRot))),n.w=n.x2-n.x1,n.h=n.y2-n.y1}return n},dr={includeNodes:!0,includeEdges:!0,includeLabels:!0,includeMainLabels:!0,includeSourceLabels:!0,includeTargetLabels:!0,includeOverlays:!0,includeUnderlays:!0,includeOutlines:!0,useCache:!0},uo=js(dr),co=Ve(dr);Ft.boundingBox=function(e){var t;if(this.length!==1||this[0]._private.bbCache==null||this[0]._private.styleDirty||e!==void 0&&e.useCache!==void 0&&e.useCache!==!0){t=tt();var n=co(e=e||dr),r=this;if(r.cy().styleEnabled())for(var a=0;a<r.length;a++){var i=r[a],o=i._private,s=Ws(i),l=o.bbCachePosKey===s,u=n.useCache&&l&&!o.styleDirty;i.recalculateRenderedStyle(u)}this.updateCompoundBounds(!e.useCache);for(var c=0;c<r.length;c++){var d=r[c];Qt(t,lo(d,n))}}else e=e===void 0?dr:co(e),t=lo(this[0],e);return t.x1=lt(t.x1),t.y1=lt(t.y1),t.x2=lt(t.x2),t.y2=lt(t.y2),t.w=lt(t.x2-t.x1),t.h=lt(t.y2-t.y1),t},Ft.dirtyBoundingBoxCache=function(){for(var e=0;e<this.length;e++){var t=this[e]._private;t.bbCache=null,t.bbCachePosKey=null,t.bodyBounds=null,t.overlayBounds=null,t.labelBounds.all=null,t.labelBounds.source=null,t.labelBounds.target=null,t.labelBounds.main=null,t.labelBounds.sourceRot=null,t.labelBounds.targetRot=null,t.labelBounds.mainRot=null,t.arrowBounds.source=null,t.arrowBounds.target=null,t.arrowBounds["mid-source"]=null,t.arrowBounds["mid-target"]=null}return this.emitAndNotify("bounds"),this},Ft.boundingBoxAt=function(e){var t=this.nodes(),n=this.cy(),r=n.hasCompoundNodes(),a=n.collection();if(r&&(a=t.filter(function(s){return s.isParent()}),t=t.not(a)),me(e)){var i=e;e=function(){return i}}n.startBatch(),t.forEach(function(s,l){return s._private.bbAtOldPos=e(s,l)}).silentPositions(e),r&&(a.dirtyCompoundBoundsCache(),a.dirtyBoundingBoxCache(),a.updateCompoundBounds(!0));var o=function(s){return{x1:s.x1,x2:s.x2,w:s.w,y1:s.y1,y2:s.y2,h:s.h}}(this.boundingBox({useCache:!1}));return t.silentPositions(function(s){return s._private.bbAtOldPos}),r&&(a.dirtyCompoundBoundsCache(),a.dirtyBoundingBoxCache(),a.updateCompoundBounds(!0)),n.endBatch(),o},En.boundingbox=En.bb=En.boundingBox,En.renderedBoundingbox=En.renderedBoundingBox;var $n,rr,Td=Ft;$n=rr={};var ho=function(e){e.uppercaseName=Bi(e.name),e.autoName="auto"+e.uppercaseName,e.labelName="label"+e.uppercaseName,e.outerName="outer"+e.uppercaseName,e.uppercaseOuterName=Bi(e.outerName),$n[e.name]=function(){var t=this[0],n=t._private,r=n.cy._private.styleEnabled;if(t){if(r){if(t.isParent())return t.updateCompoundBounds(),n[e.autoName]||0;var a=t.pstyle(e.name);return a.strValue==="label"?(t.recalculateRenderedStyle(),n.rstyle[e.labelName]||0):a.pfValue}return 1}},$n["outer"+e.uppercaseName]=function(){var t=this[0],n=t._private.cy._private.styleEnabled;if(t)return n?t[e.name]()+t.pstyle("border-width").pfValue+2*t.padding():1},$n["rendered"+e.uppercaseName]=function(){var t=this[0];if(t)return t[e.name]()*this.cy().zoom()},$n["rendered"+e.uppercaseOuterName]=function(){var t=this[0];if(t)return t[e.outerName]()*this.cy().zoom()}};ho({name:"width"}),ho({name:"height"}),rr.padding=function(){var e=this[0],t=e._private;return e.isParent()?(e.updateCompoundBounds(),t.autoPadding!==void 0?t.autoPadding:e.pstyle("padding").pfValue):e.pstyle("padding").pfValue},rr.paddedHeight=function(){var e=this[0];return e.height()+2*e.padding()},rr.paddedWidth=function(){var e=this[0];return e.width()+2*e.padding()};var kd=rr,fo={controlPoints:{get:function(e){return e.renderer().getControlPoints(e)},mult:!0},segmentPoints:{get:function(e){return e.renderer().getSegmentPoints(e)},mult:!0},sourceEndpoint:{get:function(e){return e.renderer().getSourceEndpoint(e)}},targetEndpoint:{get:function(e){return e.renderer().getTargetEndpoint(e)}},midpoint:{get:function(e){return e.renderer().getEdgeMidpoint(e)}}},Cd=Object.keys(fo).reduce(function(e,t){var n=fo[t],r=function(a){return"rendered"+a[0].toUpperCase()+a.substr(1)}(t);return e[t]=function(){return function(a,i){if(a.isEdge())return i(a)}(this,n.get)},n.mult?e[r]=function(){return function(a,i){if(a.isEdge()){var o=a.cy(),s=o.pan(),l=o.zoom();return i(a).map(function(u){return Hr(u,l,s)})}}(this,n.get)}:e[r]=function(){return function(a,i){if(a.isEdge()){var o=a.cy();return Hr(i(a),o.zoom(),o.pan())}}(this,n.get)},e},{}),Pd=he({},wd,Td,kd,Cd),Ks=function(e,t){this.recycle(e,t)};function Fn(){return!1}function Br(){return!0}Ks.prototype={instanceString:function(){return"event"},recycle:function(e,t){if(this.isImmediatePropagationStopped=this.isPropagationStopped=this.isDefaultPrevented=Fn,e!=null&&e.preventDefault?(this.type=e.type,this.isDefaultPrevented=e.defaultPrevented?Br:Fn):e!=null&&e.type?t=e:this.type=e,t!=null&&(this.originalEvent=t.originalEvent,this.type=t.type!=null?t.type:this.type,this.cy=t.cy,this.target=t.target,this.position=t.position,this.renderedPosition=t.renderedPosition,this.namespace=t.namespace,this.layout=t.layout),this.cy!=null&&this.position!=null&&this.renderedPosition==null){var n=this.position,r=this.cy.zoom(),a=this.cy.pan();this.renderedPosition={x:n.x*r+a.x,y:n.y*r+a.y}}this.timeStamp=e&&e.timeStamp||Date.now()},preventDefault:function(){this.isDefaultPrevented=Br;var e=this.originalEvent;e&&e.preventDefault&&e.preventDefault()},stopPropagation:function(){this.isPropagationStopped=Br;var e=this.originalEvent;e&&e.stopPropagation&&e.stopPropagation()},stopImmediatePropagation:function(){this.isImmediatePropagationStopped=Br,this.stopPropagation()},isDefaultPrevented:Fn,isPropagationStopped:Fn,isImmediatePropagationStopped:Fn};var Hs=/^([^.]+)(\.(?:[^.]+))?$/,Us={qualifierCompare:function(e,t){return e===t},eventMatches:function(){return!0},addEventFields:function(){},callbackContext:function(e){return e},beforeEmit:function(){},afterEmit:function(){},bubble:function(){return!1},parent:function(){return null},context:null},po=Object.keys(Us),Sd={};function ha(){for(var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:Sd,t=arguments.length>1?arguments[1]:void 0,n=0;n<po.length;n++){var r=po[n];this[r]=e[r]||Us[r]}this.context=t||this.context,this.listeners=[],this.emitting=0}var It=ha.prototype,go=function(e,t,n,r,a,i,o){_e(r)&&(a=r,r=null),o&&(i=i==null?o:he({},i,o));for(var s=Te(n)?n:n.split(/\s+/),l=0;l<s.length;l++){var u=s[l];if(!Xt(u)){var c=u.match(Hs);if(c&&t(e,u,c[1],c[2]?c[2]:null,r,a,i)===!1)break}}},vo=function(e,t){return e.addEventFields(e.context,t),new Ks(t.type,t)},Bd=function(e,t,n){if(fr(n)!=="event")if(me(n))t(e,vo(e,n));else for(var r=Te(n)?n:n.split(/\s+/),a=0;a<r.length;a++){var i=r[a];if(!Xt(i)){var o=i.match(Hs);if(o){var s=o[1],l=o[2]?o[2]:null;t(e,vo(e,{type:s,namespace:l,target:e.context}))}}}else t(e,n)};It.on=It.addListener=function(e,t,n,r,a){return go(this,function(i,o,s,l,u,c,d){_e(c)&&i.listeners.push({event:o,callback:c,type:s,namespace:l,qualifier:u,conf:d})},e,t,n,r,a),this},It.one=function(e,t,n,r){return this.on(e,t,n,r,{one:!0})},It.removeListener=It.off=function(e,t,n,r){var a=this;this.emitting!==0&&(this.listeners=this.listeners.slice());for(var i=this.listeners,o=function(l){var u=i[l];go(a,function(c,d,h,p,f,v){if((u.type===h||e==="*")&&(!p&&u.namespace!==".*"||u.namespace===p)&&(!f||c.qualifierCompare(u.qualifier,f))&&(!v||u.callback===v))return i.splice(l,1),!1},e,t,n,r)},s=i.length-1;s>=0;s--)o(s);return this},It.removeAllListeners=function(){return this.removeListener("*")},It.emit=It.trigger=function(e,t,n){var r=this.listeners,a=r.length;return this.emitting++,Te(t)||(t=[t]),Bd(this,function(i,o){n!=null&&(r=[{event:o.event,type:o.type,namespace:o.namespace,callback:n}],a=r.length);for(var s=function(u){var c=r[u];if(c.type===o.type&&(!c.namespace||c.namespace===o.namespace||c.namespace===".*")&&i.eventMatches(i.context,c,o)){var d=[o];t!=null&&function(f,v){for(var m=0;m<v.length;m++){var g=v[m];f.push(g)}}(d,t),i.beforeEmit(i.context,c,o),c.conf&&c.conf.one&&(i.listeners=i.listeners.filter(function(f){return f!==c}));var h=i.callbackContext(i.context,c,o),p=c.callback.apply(h,d);i.afterEmit(i.context,c,o),p===!1&&(o.stopPropagation(),o.preventDefault())}},l=0;l<a;l++)s(l);i.bubble(i.context)&&!o.isPropagationStopped()&&i.parent(i.context).emit(o,t)},e),this.emitting--,this};var Dd={qualifierCompare:function(e,t){return e==null||t==null?e==null&&t==null:e.sameText(t)},eventMatches:function(e,t,n){var r=t.qualifier;return r==null||e!==n.target&&pr(n.target)&&r.matches(n.target)},addEventFields:function(e,t){t.cy=e.cy(),t.target=e},callbackContext:function(e,t,n){return t.qualifier!=null?n.target:e},beforeEmit:function(e,t){t.conf&&t.conf.once&&t.conf.onceCollection.removeListener(t.event,t.qualifier,t.callback)},bubble:function(){return!0},parent:function(e){return e.isChild()?e.parent():e.cy()}},Dr=function(e){return ce(e)?new jt(e):e},Gs={createEmitter:function(){for(var e=0;e<this.length;e++){var t=this[e],n=t._private;n.emitter||(n.emitter=new ha(Dd,t))}return this},emitter:function(){return this._private.emitter},on:function(e,t,n){for(var r=Dr(t),a=0;a<this.length;a++)this[a].emitter().on(e,r,n);return this},removeListener:function(e,t,n){for(var r=Dr(t),a=0;a<this.length;a++)this[a].emitter().removeListener(e,r,n);return this},removeAllListeners:function(){for(var e=0;e<this.length;e++)this[e].emitter().removeAllListeners();return this},one:function(e,t,n){for(var r=Dr(t),a=0;a<this.length;a++)this[a].emitter().one(e,r,n);return this},once:function(e,t,n){for(var r=Dr(t),a=0;a<this.length;a++)this[a].emitter().on(e,r,n,{once:!0,onceCollection:this})},emit:function(e,t){for(var n=0;n<this.length;n++)this[n].emitter().emit(e,t);return this},emitAndNotify:function(e,t){if(this.length!==0)return this.cy().notify(e,this),this.emit(e,t),this}};xe.eventAliasesOn(Gs);var Zs={nodes:function(e){return this.filter(function(t){return t.isNode()}).filter(e)},edges:function(e){return this.filter(function(t){return t.isEdge()}).filter(e)},byGroup:function(){for(var e=this.spawn(),t=this.spawn(),n=0;n<this.length;n++){var r=this[n];r.isNode()?e.push(r):t.push(r)}return{nodes:e,edges:t}},filter:function(e,t){if(e===void 0)return this;if(ce(e)||at(e))return new jt(e).filter(this);if(_e(e)){for(var n=this.spawn(),r=this,a=0;a<r.length;a++){var i=r[a];(t?e.apply(t,[i,a,r]):e(i,a,r))&&n.push(i)}return n}return this.spawn()},not:function(e){if(e){ce(e)&&(e=this.filter(e));for(var t=this.spawn(),n=0;n<this.length;n++){var r=this[n];e.has(r)||t.push(r)}return t}return this},absoluteComplement:function(){return this.cy().mutableElements().not(this)},intersect:function(e){if(ce(e)){var t=e;return this.filter(t)}for(var n=this.spawn(),r=e,a=this.length<e.length,i=a?this:r,o=a?r:this,s=0;s<i.length;s++){var l=i[s];o.has(l)&&n.push(l)}return n},xor:function(e){var t=this._private.cy;ce(e)&&(e=t.$(e));var n=this.spawn(),r=e,a=function(i,o){for(var s=0;s<i.length;s++){var l=i[s],u=l._private.data.id;o.hasElementWithId(u)||n.push(l)}};return a(this,r),a(r,this),n},diff:function(e){var t=this._private.cy;ce(e)&&(e=t.$(e));var n=this.spawn(),r=this.spawn(),a=this.spawn(),i=e,o=function(s,l,u){for(var c=0;c<s.length;c++){var d=s[c],h=d._private.data.id;l.hasElementWithId(h)?a.merge(d):u.push(d)}};return o(this,i,n),o(i,this,r),{left:n,right:r,both:a}},add:function(e){var t=this._private.cy;if(!e)return this;if(ce(e)){var n=e;e=t.mutableElements().filter(n)}for(var r=this.spawnSelf(),a=0;a<e.length;a++){var i=e[a],o=!this.has(i);o&&r.push(i)}return r},merge:function(e){var t=this._private,n=t.cy;if(!e)return this;if(e&&ce(e)){var r=e;e=n.mutableElements().filter(r)}for(var a=t.map,i=0;i<e.length;i++){var o=e[i],s=o._private.data.id;if(!a.has(s)){var l=this.length++;this[l]=o,a.set(s,{ele:o,index:l})}}return this},unmergeAt:function(e){var t=this[e].id(),n=this._private.map;this[e]=void 0,n.delete(t);var r=e===this.length-1;if(this.length>1&&!r){var a=this.length-1,i=this[a],o=i._private.data.id;this[a]=void 0,this[e]=i,n.set(o,{ele:i,index:e})}return this.length--,this},unmergeOne:function(e){e=e[0];var t=this._private,n=e._private.data.id,r=t.map.get(n);if(!r)return this;var a=r.index;return this.unmergeAt(a),this},unmerge:function(e){var t=this._private.cy;if(!e)return this;if(e&&ce(e)){var n=e;e=t.mutableElements().filter(n)}for(var r=0;r<e.length;r++)this.unmergeOne(e[r]);return this},unmergeBy:function(e){for(var t=this.length-1;t>=0;t--)e(this[t])&&this.unmergeAt(t);return this},map:function(e,t){for(var n=[],r=this,a=0;a<r.length;a++){var i=r[a],o=t?e.apply(t,[i,a,r]):e(i,a,r);n.push(o)}return n},reduce:function(e,t){for(var n=t,r=this,a=0;a<r.length;a++)n=e(n,r[a],a,r);return n},max:function(e,t){for(var n,r=-1/0,a=this,i=0;i<a.length;i++){var o=a[i],s=t?e.apply(t,[o,i,a]):e(o,i,a);s>r&&(r=s,n=o)}return{value:r,ele:n}},min:function(e,t){for(var n,r=1/0,a=this,i=0;i<a.length;i++){var o=a[i],s=t?e.apply(t,[o,i,a]):e(o,i,a);s<r&&(r=s,n=o)}return{value:r,ele:n}}},be=Zs;be.u=be["|"]=be["+"]=be.union=be.or=be.add,be["\\"]=be["!"]=be["-"]=be.difference=be.relativeComplement=be.subtract=be.not,be.n=be["&"]=be["."]=be.and=be.intersection=be.intersect,be["^"]=be["(+)"]=be["(-)"]=be.symmetricDifference=be.symdiff=be.xor,be.fnFilter=be.filterFn=be.stdFilter=be.filter,be.complement=be.abscomp=be.absoluteComplement;var $s=function(e,t){var n=e.cy().hasCompoundNodes();function r(l){var u=l.pstyle("z-compound-depth");return u.value==="auto"?n?l.zDepth():0:u.value==="bottom"?-1:u.value==="top"?ai:0}var a=r(e)-r(t);if(a!==0)return a;function i(l){return l.pstyle("z-index-compare").value==="auto"&&l.isNode()?1:0}var o=i(e)-i(t);if(o!==0)return o;var s=e.pstyle("z-index").value-t.pstyle("z-index").value;return s!==0?s:e.poolIndex()-t.poolIndex()},ea={forEach:function(e,t){if(_e(e))for(var n=this.length,r=0;r<n;r++){var a=this[r];if((t?e.apply(t,[a,r,this]):e(a,r,this))===!1)break}return this},toArray:function(){for(var e=[],t=0;t<this.length;t++)e.push(this[t]);return e},slice:function(e,t){var n=[],r=this.length;t==null&&(t=r),e==null&&(e=0),e<0&&(e=r+e),t<0&&(t=r+t);for(var a=e;a>=0&&a<t&&a<r;a++)n.push(this[a]);return this.spawn(n)},size:function(){return this.length},eq:function(e){return this[e]||this.spawn()},first:function(){return this[0]||this.spawn()},last:function(){return this[this.length-1]||this.spawn()},empty:function(){return this.length===0},nonempty:function(){return!this.empty()},sort:function(e){if(!_e(e))return this;var t=this.toArray().sort(e);return this.spawn(t)},sortByZIndex:function(){return this.sort($s)},zDepth:function(){var e=this[0];if(e){var t=e._private;if(t.group==="nodes"){var n=t.data.parent?e.parents().size():0;return e.isParent()?n:ai-1}var r=t.source,a=t.target,i=r.zDepth(),o=a.zDepth();return Math.max(i,o,0)}}};ea.each=ea.forEach;var Ca;Ca="undefined",(typeof Symbol>"u"?"undefined":Ne(Symbol))!=Ca&&Ne(Symbol.iterator)!=Ca&&(ea[Symbol.iterator]=function(){var e=this,t={value:void 0,done:!1},n=0,r=this.length;return ts({next:function(){return n<r?t.value=e[n++]:(t.value=void 0,t.done=!0),t}},Symbol.iterator,function(){return this})});var _d=Ve({nodeDimensionsIncludeLabels:!1}),zr={layoutDimensions:function(e){var t;if(e=_d(e),this.takesUpSpace())if(e.nodeDimensionsIncludeLabels){var n=this.boundingBox();t={w:n.w,h:n.h}}else t={w:this.outerWidth(),h:this.outerHeight()};else t={w:0,h:0};return t.w!==0&&t.h!==0||(t.w=t.h=1),t},layoutPositions:function(e,t,n){var r=this.nodes().filter(function(g){return!g.isParent()}),a=this.cy(),i=t.eles,o=function(g){return g.id()},s=or(n,o);e.emit({type:"layoutstart",layout:e}),e.animations=[];var l=t.spacingFactor&&t.spacingFactor!==1,u=function(){if(!l)return null;for(var g=tt(),y=0;y<r.length;y++){var b=r[y],k=s(b,y);Su(g,k.x,k.y)}return g}(),c=or(function(g,y){var b=s(g,y);return l&&(b=function(k,x,w){var E=x.x1+x.w/2,C=x.y1+x.h/2;return{x:E+(w.x-E)*k,y:C+(w.y-C)*k}}(Math.abs(t.spacingFactor),u,b)),t.transform!=null&&(b=t.transform(g,b)),b},o);if(t.animate){for(var d=0;d<r.length;d++){var h=r[d],p=c(h,d);if(t.animateFilter==null||t.animateFilter(h,d)){var f=h.animation({position:p,duration:t.animationDuration,easing:t.animationEasing});e.animations.push(f)}else h.position(p)}if(t.fit){var v=a.animation({fit:{boundingBox:i.boundingBoxAt(c),padding:t.padding},duration:t.animationDuration,easing:t.animationEasing});e.animations.push(v)}else if(t.zoom!==void 0&&t.pan!==void 0){var m=a.animation({zoom:t.zoom,pan:t.pan,duration:t.animationDuration,easing:t.animationEasing});e.animations.push(m)}e.animations.forEach(function(g){return g.play()}),e.one("layoutready",t.ready),e.emit({type:"layoutready",layout:e}),Mn.all(e.animations.map(function(g){return g.promise()})).then(function(){e.one("layoutstop",t.stop),e.emit({type:"layoutstop",layout:e})})}else r.positions(c),t.fit&&a.fit(t.eles,t.padding),t.zoom!=null&&a.zoom(t.zoom),t.pan&&a.pan(t.pan),e.one("layoutready",t.ready),e.emit({type:"layoutready",layout:e}),e.one("layoutstop",t.stop),e.emit({type:"layoutstop",layout:e});return this},layout:function(e){return this.cy().makeLayout(he({},e,{eles:this}))}};function Qs(e,t,n){var r,a=n._private,i=a.styleCache=a.styleCache||[];return(r=i[e])!=null?r:r=i[e]=t(n)}function fa(e,t){return e=Yt(e),function(n){return Qs(e,t,n)}}function ta(e,t){e=Yt(e);var n=function(r){return t.call(r)};return function(){var r=this[0];if(r)return Qs(e,n,r)}}zr.createLayout=zr.makeLayout=zr.layout;var Ye={recalculateRenderedStyle:function(e){var t=this.cy(),n=t.renderer(),r=t.styleEnabled();return n&&r&&n.recalculateRenderedStyle(this,e),this},dirtyStyleCache:function(){var e,t=this.cy(),n=function(r){return r._private.styleCache=null};return t.hasCompoundNodes()?((e=this.spawnSelf().merge(this.descendants()).merge(this.parents())).merge(e.connectedEdges()),e.forEach(n)):this.forEach(function(r){n(r),r.connectedEdges().forEach(n)}),this},updateStyle:function(e){var t=this._private.cy;if(!t.styleEnabled())return this;if(t.batching())return t._private.batchStyleEles.merge(this),this;var n=this;e=!(!e&&e!==void 0),t.hasCompoundNodes()&&(n=this.spawnSelf().merge(this.descendants()).merge(this.parents()));var r=n;return e?r.emitAndNotify("style"):r.emit("style"),n.forEach(function(a){return a._private.styleDirty=!0}),this},cleanStyle:function(){var e=this.cy();if(e.styleEnabled())for(var t=0;t<this.length;t++){var n=this[t];n._private.styleDirty&&(n._private.styleDirty=!1,e.style().apply(n))}},parsedStyle:function(e){var t=!(arguments.length>1&&arguments[1]!==void 0)||arguments[1],n=this[0],r=n.cy();if(r.styleEnabled()&&n){n._private.styleDirty&&(n._private.styleDirty=!1,r.style().apply(n));var a=n._private.style[e];return a??(t?r.style().getDefaultProperty(e):null)}},numericStyle:function(e){var t=this[0];if(t.cy().styleEnabled()&&t){var n=t.pstyle(e);return n.pfValue!==void 0?n.pfValue:n.value}},numericStyleUnits:function(e){var t=this[0];if(t.cy().styleEnabled())return t?t.pstyle(e).units:void 0},renderedStyle:function(e){var t=this.cy();if(!t.styleEnabled())return this;var n=this[0];return n?t.style().getRenderedStyle(n,e):void 0},style:function(e,t){var n=this.cy();if(!n.styleEnabled())return this;var r=!1,a=n.style();if(me(e)){var i=e;a.applyBypass(this,i,r),this.emitAndNotify("style")}else if(ce(e)){if(t===void 0){var o=this[0];return o?a.getStylePropertyValue(o,e):void 0}a.applyBypass(this,e,t,r),this.emitAndNotify("style")}else if(e===void 0){var s=this[0];return s?a.getRawStyle(s):void 0}return this},removeStyle:function(e){var t=this.cy();if(!t.styleEnabled())return this;var n=!1,r=t.style(),a=this;if(e===void 0)for(var i=0;i<a.length;i++){var o=a[i];r.removeAllBypasses(o,n)}else{e=e.split(/\s+/);for(var s=0;s<a.length;s++){var l=a[s];r.removeBypasses(l,e,n)}}return this.emitAndNotify("style"),this},show:function(){return this.css("display","element"),this},hide:function(){return this.css("display","none"),this},effectiveOpacity:function(){var e=this.cy();if(!e.styleEnabled())return 1;var t=e.hasCompoundNodes(),n=this[0];if(n){var r=n._private,a=n.pstyle("opacity").value;if(!t)return a;var i=r.data.parent?n.parents():null;if(i)for(var o=0;o<i.length;o++)a*=i[o].pstyle("opacity").value;return a}},transparent:function(){if(!this.cy().styleEnabled())return!1;var e=this[0],t=e.cy().hasCompoundNodes();return e?t?e.effectiveOpacity()===0:e.pstyle("opacity").value===0:void 0},backgrounding:function(){return!!this.cy().styleEnabled()&&!!this[0]._private.backgrounding}};function Pa(e,t){var n=e._private.data.parent?e.parents():null;if(n){for(var r=0;r<n.length;r++)if(!t(n[r]))return!1}return!0}function xi(e){var t=e.ok,n=e.edgeOkViaNode||e.ok,r=e.parentOk||e.ok;return function(){var a=this.cy();if(!a.styleEnabled())return!0;var i=this[0],o=a.hasCompoundNodes();if(i){var s=i._private;if(!t(i))return!1;if(i.isNode())return!o||Pa(i,r);var l=s.source,u=s.target;return n(l)&&(!o||Pa(l,n))&&(l===u||n(u)&&(!o||Pa(u,n)))}}}var Rn=fa("eleTakesUpSpace",function(e){return e.pstyle("display").value==="element"&&e.width()!==0&&(!e.isNode()||e.height()!==0)});Ye.takesUpSpace=ta("takesUpSpace",xi({ok:Rn}));var Ad=fa("eleInteractive",function(e){return e.pstyle("events").value==="yes"&&e.pstyle("visibility").value==="visible"&&Rn(e)}),Md=fa("parentInteractive",function(e){return e.pstyle("visibility").value==="visible"&&Rn(e)});Ye.interactive=ta("interactive",xi({ok:Ad,parentOk:Md,edgeOkViaNode:Rn})),Ye.noninteractive=function(){var e=this[0];if(e)return!e.interactive()};var Rd=fa("eleVisible",function(e){return e.pstyle("visibility").value==="visible"&&e.pstyle("opacity").pfValue!==0&&Rn(e)}),Id=Rn;Ye.visible=ta("visible",xi({ok:Rd,edgeOkViaNode:Id})),Ye.hidden=function(){var e=this[0];if(e)return!e.visible()},Ye.isBundledBezier=ta("isBundledBezier",function(){return!!this.cy().styleEnabled()&&!this.removed()&&this.pstyle("curve-style").value==="bezier"&&this.takesUpSpace()}),Ye.bypass=Ye.css=Ye.style,Ye.renderedCss=Ye.renderedStyle,Ye.removeBypass=Ye.removeCss=Ye.removeStyle,Ye.pstyle=Ye.parsedStyle;var qt={};function yo(e){return function(){var t=arguments,n=[];if(t.length===2){var r=t[0],a=t[1];this.on(e.event,r,a)}else if(t.length===1&&_e(t[0])){var i=t[0];this.on(e.event,i)}else if(t.length===0||t.length===1&&Te(t[0])){for(var o=t.length===1?t[0]:null,s=0;s<this.length;s++){var l=this[s],u=!e.ableField||l._private[e.ableField],c=l._private[e.field]!=e.value;if(e.overrideAble){var d=e.overrideAble(l);if(d!==void 0&&(u=d,!d))return this}u&&(l._private[e.field]=e.value,c&&n.push(l))}var h=this.spawn(n);h.updateStyle(),h.emit(e.event),o&&h.emit(o)}return this}}function un(e){qt[e.field]=function(){var t=this[0];if(t){if(e.overrideField){var n=e.overrideField(t);if(n!==void 0)return n}return t._private[e.field]}},qt[e.on]=yo({event:e.on,field:e.field,ableField:e.ableField,overrideAble:e.overrideAble,value:!0}),qt[e.off]=yo({event:e.off,field:e.field,ableField:e.ableField,overrideAble:e.overrideAble,value:!1})}un({field:"locked",overrideField:function(e){return!!e.cy().autolock()||void 0},on:"lock",off:"unlock"}),un({field:"grabbable",overrideField:function(e){return!e.cy().autoungrabify()&&!e.pannable()&&void 0},on:"grabify",off:"ungrabify"}),un({field:"selected",ableField:"selectable",overrideAble:function(e){return!e.cy().autounselectify()&&void 0},on:"select",off:"unselect"}),un({field:"selectable",overrideField:function(e){return!e.cy().autounselectify()&&void 0},on:"selectify",off:"unselectify"}),qt.deselect=qt.unselect,qt.grabbed=function(){var e=this[0];if(e)return e._private.grabbed},un({field:"active",on:"activate",off:"unactivate"}),un({field:"pannable",on:"panify",off:"unpanify"}),qt.inactive=function(){var e=this[0];if(e)return!e._private.active};var Ke={},mo=function(e){return function(t){for(var n=[],r=0;r<this.length;r++){var a=this[r];if(a.isNode()){for(var i=!1,o=a.connectedEdges(),s=0;s<o.length;s++){var l=o[s],u=l.source(),c=l.target();if(e.noIncomingEdges&&c===a&&u!==a||e.noOutgoingEdges&&u===a&&c!==a){i=!0;break}}i||n.push(a)}}return this.spawn(n,!0).filter(t)}},bo=function(e){return function(t){for(var n=[],r=0;r<this.length;r++){var a=this[r];if(a.isNode())for(var i=a.connectedEdges(),o=0;o<i.length;o++){var s=i[o],l=s.source(),u=s.target();e.outgoing&&l===a?(n.push(s),n.push(u)):e.incoming&&u===a&&(n.push(s),n.push(l))}}return this.spawn(n,!0).filter(t)}},xo=function(e){return function(t){for(var n=this,r=[],a={};;){var i=e.outgoing?n.outgoers():n.incomers();if(i.length===0)break;for(var o=!1,s=0;s<i.length;s++){var l=i[s],u=l.id();a[u]||(a[u]=!0,r.push(l),o=!0)}if(!o)break;n=i}return this.spawn(r,!0).filter(t)}};function wo(e){return function(t){for(var n=[],r=0;r<this.length;r++){var a=this[r]._private[e.attr];a&&n.push(a)}return this.spawn(n,!0).filter(t)}}function Eo(e){return function(t){var n=[],r=this._private.cy,a=e||{};ce(t)&&(t=r.$(t));for(var i=0;i<t.length;i++)for(var o=t[i]._private.edges,s=0;s<o.length;s++){var l=o[s],u=l._private.data,c=this.hasElementWithId(u.source)&&t.hasElementWithId(u.target),d=t.hasElementWithId(u.source)&&this.hasElementWithId(u.target);if(c||d){if((a.thisIsSrc||a.thisIsTgt)&&(a.thisIsSrc&&!c||a.thisIsTgt&&!d))continue;n.push(l)}}return this.spawn(n,!0)}}function To(e){return e=he({},{codirected:!1},e),function(t){for(var n=[],r=this.edges(),a=e,i=0;i<r.length;i++)for(var o=r[i]._private,s=o.source,l=s._private.data.id,u=o.data.target,c=s._private.edges,d=0;d<c.length;d++){var h=c[d],p=h._private.data,f=p.target,v=p.source,m=f===u&&v===l,g=l===f&&u===v;(a.codirected&&m||!a.codirected&&(m||g))&&n.push(h)}return this.spawn(n,!0).filter(t)}}Ke.clearTraversalCache=function(){for(var e=0;e<this.length;e++)this[e]._private.traversalCache=null},he(Ke,{roots:mo({noIncomingEdges:!0}),leaves:mo({noOutgoingEdges:!0}),outgoers:ot(bo({outgoing:!0}),"outgoers"),successors:xo({outgoing:!0}),incomers:ot(bo({incoming:!0}),"incomers"),predecessors:xo({incoming:!0})}),he(Ke,{neighborhood:ot(function(e){for(var t=[],n=this.nodes(),r=0;r<n.length;r++)for(var a=n[r],i=a.connectedEdges(),o=0;o<i.length;o++){var s=i[o],l=s.source(),u=s.target(),c=a===l?u:l;c.length>0&&t.push(c[0]),t.push(s[0])}return this.spawn(t,!0).filter(e)},"neighborhood"),closedNeighborhood:function(e){return this.neighborhood().add(this).filter(e)},openNeighborhood:function(e){return this.neighborhood(e)}}),Ke.neighbourhood=Ke.neighborhood,Ke.closedNeighbourhood=Ke.closedNeighborhood,Ke.openNeighbourhood=Ke.openNeighborhood,he(Ke,{source:ot(function(e){var t,n=this[0];return n&&(t=n._private.source||n.cy().collection()),t&&e?t.filter(e):t},"source"),target:ot(function(e){var t,n=this[0];return n&&(t=n._private.target||n.cy().collection()),t&&e?t.filter(e):t},"target"),sources:wo({attr:"source"}),targets:wo({attr:"target"})}),he(Ke,{edgesWith:ot(Eo(),"edgesWith"),edgesTo:ot(Eo({thisIsSrc:!0}),"edgesTo")}),he(Ke,{connectedEdges:ot(function(e){for(var t=[],n=0;n<this.length;n++){var r=this[n];if(r.isNode())for(var a=r._private.edges,i=0;i<a.length;i++){var o=a[i];t.push(o)}}return this.spawn(t,!0).filter(e)},"connectedEdges"),connectedNodes:ot(function(e){for(var t=[],n=0;n<this.length;n++){var r=this[n];r.isEdge()&&(t.push(r.source()[0]),t.push(r.target()[0]))}return this.spawn(t,!0).filter(e)},"connectedNodes"),parallelEdges:ot(To(),"parallelEdges"),codirectedEdges:ot(To({codirected:!0}),"codirectedEdges")}),he(Ke,{components:function(e){var t=this,n=t.cy(),r=n.collection(),a=e==null?t.nodes():e.nodes(),i=[];e!=null&&a.empty()&&(a=e.sources());var o=function(l,u){r.merge(l),a.unmerge(l),u.merge(l)};if(a.empty())return t.spawn();var s=function(){var l=n.collection();i.push(l);var u=a[0];o(u,l),t.bfs({directed:!1,roots:u,visit:function(c){return o(c,l)}}),l.forEach(function(c){c.connectedEdges().forEach(function(d){t.has(d)&&l.has(d.source())&&l.has(d.target())&&l.merge(d)})})};do s();while(a.length>0);return i},component:function(){var e=this[0];return e.cy().mutableElements().components(e)[0]}}),Ke.componentsOf=Ke.components;var je=function(e,t){var n=arguments.length>2&&arguments[2]!==void 0&&arguments[2],r=arguments.length>3&&arguments[3]!==void 0&&arguments[3];if(e!==void 0){var a=new wt,i=!1;if(t){if(t.length>0&&me(t[0])&&!pr(t[0])){i=!0;for(var o=[],s=new An,l=0,u=t.length;l<u;l++){var c=t[l];c.data==null&&(c.data={});var d=c.data;if(d.id==null)d.id=ms();else if(e.hasElementWithId(d.id)||s.has(d.id))continue;var h=new oa(e,c,!1);o.push(h),s.add(d.id)}t=o}}else t=[];this.length=0;for(var p=0,f=t.length;p<f;p++){var v=t[p][0];if(v!=null){var m=v._private.data.id;n&&a.has(m)||(n&&a.set(m,{index:this.length,ele:v}),this[this.length]=v,this.length++)}}this._private={eles:this,cy:e,get map(){return this.lazyMap==null&&this.rebuildMap(),this.lazyMap},set map(g){this.lazyMap=g},rebuildMap:function(){for(var g=this.lazyMap=new wt,y=this.eles,b=0;b<y.length;b++){var k=y[b];g.set(k.id(),{index:b,ele:k})}}},n&&(this._private.map=a),i&&!r&&this.restore()}else De("A collection must have a reference to the core")},Ce=oa.prototype=je.prototype=Object.create(Array.prototype);Ce.instanceString=function(){return"collection"},Ce.spawn=function(e,t){return new je(this.cy(),e,t)},Ce.spawnSelf=function(){return this.spawn(this)},Ce.cy=function(){return this._private.cy},Ce.renderer=function(){return this._private.cy.renderer()},Ce.element=function(){return this[0]},Ce.collection=function(){return as(this)?this:new je(this._private.cy,[this])},Ce.unique=function(){return new je(this._private.cy,this,!0)},Ce.hasElementWithId=function(e){return e=""+e,this._private.map.has(e)},Ce.getElementById=function(e){e=""+e;var t=this._private.cy,n=this._private.map.get(e);return n?n.ele:new je(t)},Ce.$id=Ce.getElementById,Ce.poolIndex=function(){var e=this._private.cy._private.elements,t=this[0]._private.data.id;return e._private.map.get(t).index},Ce.indexOf=function(e){var t=e[0]._private.data.id;return this._private.map.get(t).index},Ce.indexOfId=function(e){return e=""+e,this._private.map.get(e).index},Ce.json=function(e){var t=this.element(),n=this.cy();if(t==null&&e)return this;if(t!=null){var r=t._private;if(me(e)){if(n.startBatch(),e.data){t.data(e.data);var a=r.data;if(t.isEdge()){var i=!1,o={},s=e.data.source,l=e.data.target;s!=null&&s!=a.source&&(o.source=""+s,i=!0),l!=null&&l!=a.target&&(o.target=""+l,i=!0),i&&(t=t.move(o))}else{var u="parent"in e.data,c=e.data.parent;!u||c==null&&a.parent==null||c==a.parent||(c===void 0&&(c=null),c!=null&&(c=""+c),t=t.move({parent:c}))}}e.position&&t.position(e.position);var d=function(f,v,m){var g=e[f];g!=null&&g!==r[f]&&(g?t[v]():t[m]())};return d("removed","remove","restore"),d("selected","select","unselect"),d("selectable","selectify","unselectify"),d("locked","lock","unlock"),d("grabbable","grabify","ungrabify"),d("pannable","panify","unpanify"),e.classes!=null&&t.classes(e.classes),n.endBatch(),this}if(e===void 0){var h={data:xt(r.data),position:xt(r.position),group:r.group,removed:r.removed,selected:r.selected,selectable:r.selectable,locked:r.locked,grabbable:r.grabbable,pannable:r.pannable,classes:null};h.classes="";var p=0;return r.classes.forEach(function(f){return h.classes+=p++==0?f:" "+f}),h}}},Ce.jsons=function(){for(var e=[],t=0;t<this.length;t++){var n=this[t].json();e.push(n)}return e},Ce.clone=function(){for(var e=this.cy(),t=[],n=0;n<this.length;n++){var r=this[n].json(),a=new oa(e,r,!1);t.push(a)}return new je(e,t)},Ce.copy=Ce.clone,Ce.restore=function(){for(var e,t,n=!(arguments.length>0&&arguments[0]!==void 0)||arguments[0],r=!(arguments.length>1&&arguments[1]!==void 0)||arguments[1],a=this,i=a.cy(),o=i._private,s=[],l=[],u=0,c=a.length;u<c;u++){var d=a[u];r&&!d.removed()||(d.isNode()?s.push(d):l.push(d))}e=s.concat(l);var h=function(){e.splice(t,1),t--};for(t=0;t<e.length;t++){var p=e[t],f=p._private,v=f.data;if(p.clearTraversalCache(),r||f.removed)if(v.id===void 0)v.id=ms();else if(ae(v.id))v.id=""+v.id;else{if(Xt(v.id)||!ce(v.id)){De("Can not create element with invalid string ID `"+v.id+"`"),h();continue}if(i.hasElementWithId(v.id)){De("Can not create second element with ID `"+v.id+"`"),h();continue}}var m=v.id;if(p.isNode()){var g=f.position;g.x==null&&(g.x=0),g.y==null&&(g.y=0)}if(p.isEdge()){for(var y=p,b=["source","target"],k=b.length,x=!1,w=0;w<k;w++){var E=b[w],C=v[E];ae(C)&&(C=v[E]=""+v[E]),C==null||C===""?(De("Can not create edge `"+m+"` with unspecified "+E),x=!0):i.hasElementWithId(C)||(De("Can not create edge `"+m+"` with nonexistant "+E+" `"+C+"`"),x=!0)}if(x){h();continue}var S=i.getElementById(v.source),P=i.getElementById(v.target);S.same(P)?S._private.edges.push(y):(S._private.edges.push(y),P._private.edges.push(y)),y._private.source=S,y._private.target=P}f.map=new wt,f.map.set(m,{ele:p,index:0}),f.removed=!1,r&&i.addToPool(p)}for(var _=0;_<s.length;_++){var A=s[_],B=A._private.data;ae(B.parent)&&(B.parent=""+B.parent);var O=B.parent;if(O!=null||A._private.parent){var N=A._private.parent?i.collection().merge(A._private.parent):i.getElementById(O);if(N.empty())B.parent=void 0;else if(N[0].removed())we("Node added with missing parent, reference to parent removed"),B.parent=void 0,A._private.parent=null;else{for(var V=!1,M=N;!M.empty();){if(A.same(M)){V=!0,B.parent=void 0;break}M=M.parent()}V||(N[0]._private.children.push(A),A._private.parent=N[0],o.hasCompoundNodes=!0)}}}if(e.length>0){for(var D=e.length===a.length?a:new je(i,e),I=0;I<D.length;I++){var z=D[I];z.isNode()||(z.parallelEdges().clearTraversalCache(),z.source().clearTraversalCache(),z.target().clearTraversalCache())}(o.hasCompoundNodes?i.collection().merge(D).merge(D.connectedNodes()).merge(D.parent()):D).dirtyCompoundBoundsCache().dirtyBoundingBoxCache().updateStyle(n),n?D.emitAndNotify("add"):r&&D.emit("add")}return a},Ce.removed=function(){var e=this[0];return e&&e._private.removed},Ce.inside=function(){var e=this[0];return e&&!e._private.removed},Ce.remove=function(){var e=!(arguments.length>0&&arguments[0]!==void 0)||arguments[0],t=!(arguments.length>1&&arguments[1]!==void 0)||arguments[1],n=this,r=[],a={},i=n._private.cy;function o(P){var _=a[P.id()];t&&P.removed()||_||(a[P.id()]=!0,P.isNode()?(r.push(P),function(A){for(var B=A._private.edges,O=0;O<B.length;O++)o(B[O])}(P),function(A){for(var B=A._private.children,O=0;O<B.length;O++)o(B[O])}(P)):r.unshift(P))}for(var s=0,l=n.length;s<l;s++)o(n[s]);function u(P,_){var A=P._private.edges;zt(A,_),P.clearTraversalCache()}function c(P){P.clearTraversalCache()}var d=[];function h(P,_){_=_[0];var A=(P=P[0])._private.children,B=P.id();zt(A,_),_._private.parent=null,d.ids[B]||(d.ids[B]=!0,d.push(P))}d.ids={},n.dirtyCompoundBoundsCache(),t&&i.removeFromPool(r);for(var p=0;p<r.length;p++){var f=r[p];if(f.isEdge()){var v=f.source()[0],m=f.target()[0];u(v,f),u(m,f);for(var g=f.parallelEdges(),y=0;y<g.length;y++){var b=g[y];c(b),b.isBundledBezier()&&b.dirtyBoundingBoxCache()}}else{var k=f.parent();k.length!==0&&h(k,f)}t&&(f._private.removed=!0)}var x=i._private.elements;i._private.hasCompoundNodes=!1;for(var w=0;w<x.length;w++)if(x[w].isParent()){i._private.hasCompoundNodes=!0;break}var E=new je(this.cy(),r);E.size()>0&&(e?E.emitAndNotify("remove"):t&&E.emit("remove"));for(var C=0;C<d.length;C++){var S=d[C];t&&S.removed()||S.updateStyle()}return E},Ce.move=function(e){var t=this._private.cy,n=this,r=!1,a=!1,i=function(h){return h==null?h:""+h};if(e.source!==void 0||e.target!==void 0){var o=i(e.source),s=i(e.target),l=o!=null&&t.hasElementWithId(o),u=s!=null&&t.hasElementWithId(s);(l||u)&&(t.batch(function(){n.remove(r,a),n.emitAndNotify("moveout");for(var h=0;h<n.length;h++){var p=n[h],f=p._private.data;p.isEdge()&&(l&&(f.source=o),u&&(f.target=s))}n.restore(r,a)}),n.emitAndNotify("move"))}else if(e.parent!==void 0){var c=i(e.parent);if(c===null||t.hasElementWithId(c)){var d=c===null?void 0:c;t.batch(function(){var h=n.remove(r,a);h.emitAndNotify("moveout");for(var p=0;p<n.length;p++){var f=n[p],v=f._private.data;f.isNode()&&(v.parent=d)}h.restore(r,a)}),n.emitAndNotify("move")}}return this},[Ss,fd,Lr,Nt,wn,xd,Or,Pd,Gs,Zs,{isNode:function(){return this.group()==="nodes"},isEdge:function(){return this.group()==="edges"},isLoop:function(){return this.isEdge()&&this.source()[0]===this.target()[0]},isSimple:function(){return this.isEdge()&&this.source()[0]!==this.target()[0]},group:function(){var e=this[0];if(e)return e._private.group}},ea,zr,Ye,qt,Ke].forEach(function(e){he(Ce,e)});var Nd={add:function(e){var t,n=this;if(at(e)){var r=e;if(r._private.cy===n)t=r.restore();else{for(var a=[],i=0;i<r.length;i++){var o=r[i];a.push(o.json())}t=new je(n,a)}}else if(Te(e))t=new je(n,e);else if(me(e)&&(Te(e.nodes)||Te(e.edges))){for(var s=e,l=[],u=["nodes","edges"],c=0,d=u.length;c<d;c++){var h=u[c],p=s[h];if(Te(p))for(var f=0,v=p.length;f<v;f++){var m=he({group:h},p[f]);l.push(m)}}t=new je(n,l)}else t=new oa(n,e).collection();return t},remove:function(e){if(!at(e)){if(ce(e)){var t=e;e=this.$(t)}}return e.remove()}};function Ld(e,t,n,r){var a=4,i=1e-7,o=10,s=11,l=1/(s-1),u=typeof Float32Array<"u";if(arguments.length!==4)return!1;for(var c=0;c<4;++c)if(typeof arguments[c]!="number"||isNaN(arguments[c])||!isFinite(arguments[c]))return!1;e=Math.min(e,1),n=Math.min(n,1),e=Math.max(e,0),n=Math.max(n,0);var d=u?new Float32Array(s):new Array(s);function h(w,E){return 1-3*E+3*w}function p(w,E){return 3*E-6*w}function f(w){return 3*w}function v(w,E,C){return((h(E,C)*w+p(E,C))*w+f(E))*w}function m(w,E,C){return 3*h(E,C)*w*w+2*p(E,C)*w+f(E)}function g(w){for(var E=0,C=1,S=s-1;C!==S&&d[C]<=w;++C)E+=l;--C;var P=E+(w-d[C])/(d[C+1]-d[C])*l,_=m(P,e,n);return _>=.001?function(A,B){for(var O=0;O<a;++O){var N=m(B,e,n);if(N===0)return B;B-=(v(B,e,n)-A)/N}return B}(w,P):_===0?P:function(A,B,O){var N,V,M=0;do(N=v(V=B+(O-B)/2,e,n)-A)>0?O=V:B=V;while(Math.abs(N)>i&&++M<o);return V}(w,E,E+l)}var y=!1;function b(){y=!0,e===t&&n===r||function(){for(var w=0;w<s;++w)d[w]=v(w*l,e,n)}()}var k=function(w){return y||b(),e===t&&n===r?w:w===0?0:w===1?1:v(g(w),t,r)};k.getControlPoints=function(){return[{x:e,y:t},{x:n,y:r}]};var x="generateBezier("+[e,t,n,r]+")";return k.toString=function(){return x},k}var Od=function(){function e(r){return-r.tension*r.x-r.friction*r.v}function t(r,a,i){var o={x:r.x+i.dx*a,v:r.v+i.dv*a,tension:r.tension,friction:r.friction};return{dx:o.v,dv:e(o)}}function n(r,a){var i={dx:r.v,dv:e(r)},o=t(r,.5*a,i),s=t(r,.5*a,o),l=t(r,a,s),u=1/6*(i.dx+2*(o.dx+s.dx)+l.dx),c=1/6*(i.dv+2*(o.dv+s.dv)+l.dv);return r.x=r.x+u*a,r.v=r.v+c*a,r}return function r(a,i,o){var s,l,u,c={x:-1,v:0,tension:null,friction:null},d=[0],h=0,p=1e-4;for(a=parseFloat(a)||500,i=parseFloat(i)||20,o=o||null,c.tension=a,c.friction=i,l=(s=o!==null)?(h=r(a,i))/o*.016:.016;u=n(u||c,l),d.push(1+u.x),h+=16,Math.abs(u.x)>p&&Math.abs(u.v)>p;);return s?function(f){return d[f*(d.length-1)|0]}:h}}(),Pe=function(e,t,n,r){var a=Ld(e,t,n,r);return function(i,o,s){return i+(o-i)*a(s)}},Vr={linear:function(e,t,n){return e+(t-e)*n},ease:Pe(.25,.1,.25,1),"ease-in":Pe(.42,0,1,1),"ease-out":Pe(0,0,.58,1),"ease-in-out":Pe(.42,0,.58,1),"ease-in-sine":Pe(.47,0,.745,.715),"ease-out-sine":Pe(.39,.575,.565,1),"ease-in-out-sine":Pe(.445,.05,.55,.95),"ease-in-quad":Pe(.55,.085,.68,.53),"ease-out-quad":Pe(.25,.46,.45,.94),"ease-in-out-quad":Pe(.455,.03,.515,.955),"ease-in-cubic":Pe(.55,.055,.675,.19),"ease-out-cubic":Pe(.215,.61,.355,1),"ease-in-out-cubic":Pe(.645,.045,.355,1),"ease-in-quart":Pe(.895,.03,.685,.22),"ease-out-quart":Pe(.165,.84,.44,1),"ease-in-out-quart":Pe(.77,0,.175,1),"ease-in-quint":Pe(.755,.05,.855,.06),"ease-out-quint":Pe(.23,1,.32,1),"ease-in-out-quint":Pe(.86,0,.07,1),"ease-in-expo":Pe(.95,.05,.795,.035),"ease-out-expo":Pe(.19,1,.22,1),"ease-in-out-expo":Pe(1,0,0,1),"ease-in-circ":Pe(.6,.04,.98,.335),"ease-out-circ":Pe(.075,.82,.165,1),"ease-in-out-circ":Pe(.785,.135,.15,.86),spring:function(e,t,n){if(n===0)return Vr.linear;var r=Od(e,t,n);return function(a,i,o){return a+(i-a)*r(o)}},"cubic-bezier":Pe};function ko(e,t,n,r,a){if(r===1||t===n)return n;var i=a(t,n,r);return e==null||((e.roundValue||e.color)&&(i=Math.round(i)),e.min!==void 0&&(i=Math.max(i,e.min)),e.max!==void 0&&(i=Math.min(i,e.max))),i}function Co(e,t){return e.pfValue!=null||e.value!=null?e.pfValue==null||t!=null&&t.type.units==="%"?e.value:e.pfValue:e}function cn(e,t,n,r,a){var i=a!=null?a.type:null;n<0?n=0:n>1&&(n=1);var o=Co(e,a),s=Co(t,a);if(ae(o)&&ae(s))return ko(i,o,s,n,r);if(Te(o)&&Te(s)){for(var l=[],u=0;u<s.length;u++){var c=o[u],d=s[u];if(c!=null&&d!=null){var h=ko(i,c,d,n,r);l.push(h)}else l.push(d)}return l}}function zd(e,t,n,r){var a=!r,i=e._private,o=t._private,s=o.easing,l=o.startTime,u=(r?e:e.cy()).style();if(!o.easingImpl)if(s==null)o.easingImpl=Vr.linear;else{var c,d,h;ce(s)?c=u.parse("transition-timing-function",s).value:c=s,ce(c)?(d=c,h=[]):(d=c[1],h=c.slice(2).map(function(V){return+V})),h.length>0?(d==="spring"&&h.push(o.duration),o.easingImpl=Vr[d].apply(null,h)):o.easingImpl=Vr[d]}var p,f=o.easingImpl;if(p=o.duration===0?1:(n-l)/o.duration,o.applying&&(p=o.progress),p<0?p=0:p>1&&(p=1),o.delay==null){var v=o.startPosition,m=o.position;if(m&&a&&!e.locked()){var g={};qn(v.x,m.x)&&(g.x=cn(v.x,m.x,p,f)),qn(v.y,m.y)&&(g.y=cn(v.y,m.y,p,f)),e.position(g)}var y=o.startPan,b=o.pan,k=i.pan,x=b!=null&&r;x&&(qn(y.x,b.x)&&(k.x=cn(y.x,b.x,p,f)),qn(y.y,b.y)&&(k.y=cn(y.y,b.y,p,f)),e.emit("pan"));var w=o.startZoom,E=o.zoom,C=E!=null&&r;C&&(qn(w,E)&&(i.zoom=Gn(i.minZoom,cn(w,E,p,f),i.maxZoom)),e.emit("zoom")),(x||C)&&e.emit("viewport");var S=o.style;if(S&&S.length>0&&a){for(var P=0;P<S.length;P++){var _=S[P],A=_.name,B=_,O=o.startStyle[A],N=cn(O,B,p,f,u.properties[O.name]);u.overrideBypass(e,A,N)}e.emit("style")}}return o.progress=p,p}function qn(e,t){return e!=null&&t!=null&&(!(!ae(e)||!ae(t))||!(!e||!t))}function Vd(e,t,n,r){var a=t._private;a.started=!0,a.startTime=n-a.progress*a.duration}function Po(e,t){var n=t._private.aniEles,r=[];function a(u,c){var d=u._private,h=d.animation.current,p=d.animation.queue,f=!1;if(h.length===0){var v=p.shift();v&&h.push(v)}for(var m=function(k){for(var x=k.length-1;x>=0;x--)(0,k[x])();k.splice(0,k.length)},g=h.length-1;g>=0;g--){var y=h[g],b=y._private;b.stopped?(h.splice(g,1),b.hooked=!1,b.playing=!1,b.started=!1,m(b.frames)):(b.playing||b.applying)&&(b.playing&&b.applying&&(b.applying=!1),b.started||Vd(0,y,e),zd(u,y,e,c),b.applying&&(b.applying=!1),m(b.frames),b.step!=null&&b.step(e),y.completed()&&(h.splice(g,1),b.hooked=!1,b.playing=!1,b.started=!1,m(b.completes)),f=!0)}return c||h.length!==0||p.length!==0||r.push(u),f}for(var i=!1,o=0;o<n.length;o++){var s=a(n[o]);i=i||s}var l=a(t,!0);(i||l)&&(n.length>0?t.notify("draw",n):t.notify("draw")),n.unmerge(r),t.emit("step")}var Fd={animate:xe.animate(),animation:xe.animation(),animated:xe.animated(),clearQueue:xe.clearQueue(),delay:xe.delay(),delayAnimation:xe.delayAnimation(),stop:xe.stop(),addToAnimationPool:function(e){this.styleEnabled()&&this._private.aniEles.merge(e)},stopAnimationLoop:function(){this._private.animationsRunning=!1},startAnimationLoop:function(){var e=this;if(e._private.animationsRunning=!0,e.styleEnabled()){var t=e.renderer();t&&t.beforeRender?t.beforeRender(function(n,r){Po(r,e)},t.beforeRenderPriorities.animations):function n(){e._private.animationsRunning&&Wr(function(r){Po(r,e),n()})}()}}},qd={qualifierCompare:function(e,t){return e==null||t==null?e==null&&t==null:e.sameText(t)},eventMatches:function(e,t,n){var r=t.qualifier;return r==null||e!==n.target&&pr(n.target)&&r.matches(n.target)},addEventFields:function(e,t){t.cy=e,t.target=e},callbackContext:function(e,t,n){return t.qualifier!=null?n.target:e}},_r=function(e){return ce(e)?new jt(e):e},Js={createEmitter:function(){var e=this._private;return e.emitter||(e.emitter=new ha(qd,this)),this},emitter:function(){return this._private.emitter},on:function(e,t,n){return this.emitter().on(e,_r(t),n),this},removeListener:function(e,t,n){return this.emitter().removeListener(e,_r(t),n),this},removeAllListeners:function(){return this.emitter().removeAllListeners(),this},one:function(e,t,n){return this.emitter().one(e,_r(t),n),this},once:function(e,t,n){return this.emitter().one(e,_r(t),n),this},emit:function(e,t){return this.emitter().emit(e,t),this},emitAndNotify:function(e,t){return this.emit(e),this.notify(e,t),this}};xe.eventAliasesOn(Js);var Xa={png:function(e){return e=e||{},this._private.renderer.png(e)},jpg:function(e){var t=this._private.renderer;return(e=e||{}).bg=e.bg||"#fff",t.jpg(e)}};Xa.jpeg=Xa.jpg;var Fr={layout:function(e){var t=this;if(e!=null)if(e.name!=null){var n=e.name,r=t.extension("layout",n);if(r!=null){var a;a=ce(e.eles)?t.$(e.eles):e.eles!=null?e.eles:t.$();var i=new r(he({},e,{cy:t,eles:a}));return i}De("No such layout `"+n+"` found.  Did you forget to import it and `cytoscape.use()` it?")}else De("A `name` must be specified to make a layout");else De("Layout options must be specified to make a layout")}};Fr.createLayout=Fr.makeLayout=Fr.layout;var Xd={notify:function(e,t){var n=this._private;if(this.batching()){n.batchNotifications=n.batchNotifications||{};var r=n.batchNotifications[e]=n.batchNotifications[e]||this.collection();t!=null&&r.merge(t)}else if(n.notificationsEnabled){var a=this.renderer();!this.destroyed()&&a&&a.notify(e,t)}},notifications:function(e){var t=this._private;return e===void 0?t.notificationsEnabled:(t.notificationsEnabled=!!e,this)},noNotifications:function(e){this.notifications(!1),e(),this.notifications(!0)},batching:function(){return this._private.batchCount>0},startBatch:function(){var e=this._private;return e.batchCount==null&&(e.batchCount=0),e.batchCount===0&&(e.batchStyleEles=this.collection(),e.batchNotifications={}),e.batchCount++,this},endBatch:function(){var e=this._private;if(e.batchCount===0)return this;if(e.batchCount--,e.batchCount===0){e.batchStyleEles.updateStyle();var t=this.renderer();Object.keys(e.batchNotifications).forEach(function(n){var r=e.batchNotifications[n];r.empty()?t.notify(n):t.notify(n,r)})}return this},batch:function(e){return this.startBatch(),e(),this.endBatch(),this},batchData:function(e){var t=this;return this.batch(function(){for(var n=Object.keys(e),r=0;r<n.length;r++){var a=n[r],i=e[a];t.getElementById(a).data(i)}})}},Yd=Ve({hideEdgesOnViewport:!1,textureOnViewport:!1,motionBlur:!1,motionBlurOpacity:.05,pixelRatio:void 0,desktopTapThreshold:4,touchTapThreshold:8,wheelSensitivity:1,debug:!1,showFps:!1,webgl:!1,webglDebug:!1,webglDebugShowAtlases:!1,webglTexSize:2048,webglTexRows:12,webglBatchSize:2048,webglTexPerBatch:14,webglBgColor:[255,255,255]}),Ya={renderTo:function(e,t,n,r){return this._private.renderer.renderTo(e,t,n,r),this},renderer:function(){return this._private.renderer},forceRender:function(){return this.notify("draw"),this},resize:function(){return this.invalidateSize(),this.emitAndNotify("resize"),this},initRenderer:function(e){var t=this,n=t.extension("renderer",e.name);if(n!=null){e.wheelSensitivity!==void 0&&we("You have set a custom wheel sensitivity.  This will make your app zoom unnaturally when using mainstream mice.  You should change this value from the default only if you can guarantee that all your users will use the same hardware and OS configuration as your current machine.");var r=Yd(e);r.cy=t,t._private.renderer=new n(r),this.notify("init")}else De("Can not initialise: No such renderer `".concat(e.name,"` found. Did you forget to import it and `cytoscape.use()` it?"))},destroyRenderer:function(){var e=this;e.notify("destroy");var t=e.container();if(t)for(t._cyreg=null;t.childNodes.length>0;)t.removeChild(t.childNodes[0]);e._private.renderer=null,e.mutableElements().forEach(function(n){var r=n._private;r.rscratch={},r.rstyle={},r.animation.current=[],r.animation.queue=[]})},onRender:function(e){return this.on("render",e)},offRender:function(e){return this.off("render",e)}};Ya.invalidateDimensions=Ya.resize;var qr={collection:function(e,t){return ce(e)?this.$(e):at(e)?e.collection():Te(e)?(t||(t={}),new je(this,e,t.unique,t.removed)):new je(this)},nodes:function(e){var t=this.$(function(n){return n.isNode()});return e?t.filter(e):t},edges:function(e){var t=this.$(function(n){return n.isEdge()});return e?t.filter(e):t},$:function(e){var t=this._private.elements;return e?t.filter(e):t.spawnSelf()},mutableElements:function(){return this._private.elements}};qr.elements=qr.filter=qr.$;var He={},Xn="t";He.apply=function(e){for(var t=this,n=t._private.cy.collection(),r=0;r<e.length;r++){var a=e[r],i=t.getContextMeta(a);if(!i.empty){var o=t.getContextStyle(i),s=t.applyContextStyle(i,o,a);a._private.appliedInitStyle?t.updateTransitions(a,s.diffProps):a._private.appliedInitStyle=!0,t.updateStyleHints(a)&&n.push(a)}}return n},He.getPropertiesDiff=function(e,t){var n=this,r=n._private.propDiffs=n._private.propDiffs||{},a=e+"-"+t,i=r[a];if(i)return i;for(var o=[],s={},l=0;l<n.length;l++){var u=n[l],c=e[l]===Xn,d=t[l]===Xn,h=c!==d,p=u.mappedProperties.length>0;if(h||d&&p){var f=void 0;h&&p||h?f=u.properties:p&&(f=u.mappedProperties);for(var v=0;v<f.length;v++){for(var m=f[v],g=m.name,y=!1,b=l+1;b<n.length;b++){var k=n[b];if(t[b]===Xn&&(y=k.properties[m.name]!=null))break}s[g]||y||(s[g]=!0,o.push(g))}}}return r[a]=o,o},He.getContextMeta=function(e){for(var t,n=this,r="",a=e._private.styleCxtKey||"",i=0;i<n.length;i++){var o=n[i];r+=o.selector&&o.selector.matches(e)?Xn:"f"}return t=n.getPropertiesDiff(a,r),e._private.styleCxtKey=r,{key:r,diffPropNames:t,empty:t.length===0}},He.getContextStyle=function(e){var t=e.key,n=this._private.contextStyles=this._private.contextStyles||{};if(n[t])return n[t];for(var r={_private:{key:t}},a=0;a<this.length;a++){var i=this[a];if(t[a]===Xn)for(var o=0;o<i.properties.length;o++){var s=i.properties[o];r[s.name]=s}}return n[t]=r,r},He.applyContextStyle=function(e,t,n){for(var r=e.diffPropNames,a={},i=this.types,o=0;o<r.length;o++){var s=r[o],l=t[s],u=n.pstyle(s);if(!l){if(!u)continue;l=u.bypass?{name:s,deleteBypassed:!0}:{name:s,delete:!0}}if(u!==l){if(l.mapped===i.fn&&u!=null&&u.mapping!=null&&u.mapping.value===l.value){var c=u.mapping;if((c.fnValue=l.value(n))===c.prevFnValue)continue}var d=a[s]={prev:u};this.applyParsedProperty(n,l),d.next=n.pstyle(s),d.next&&d.next.bypass&&(d.next=d.next.bypassed)}}return{diffProps:a}},He.updateStyleHints=function(e){var t=e._private,n=this,r=n.propertyGroupNames,a=n.propertyGroupKeys,i=function(te,ie,se){return n.getPropertiesHash(te,ie,se)},o=t.styleKey;if(e.removed())return!1;var s=t.group==="nodes",l=e._private.style;r=Object.keys(l);for(var u=0;u<a.length;u++){var c=a[u];t.styleKeys[c]=[kn,Hn]}for(var d,h=function(te,ie){return t.styleKeys[ie][0]=sr(te,t.styleKeys[ie][0])},p=function(te,ie){return t.styleKeys[ie][1]=lr(te,t.styleKeys[ie][1])},f=function(te,ie){h(te,ie),p(te,ie)},v=function(te,ie){for(var se=0;se<te.length;se++){var le=te.charCodeAt(se);h(le,ie),p(le,ie)}},m=0;m<r.length;m++){var g=r[m],y=l[g];if(y!=null){var b=this.properties[g],k=b.type,x=b.groupKey,w=void 0;b.hashOverride!=null?w=b.hashOverride(e,y):y.pfValue!=null&&(w=y.pfValue);var E=b.enums==null?y.value:null,C=w!=null,S=C||E!=null,P=y.units;k.number&&S&&!k.multiple?(f(-128<(d=C?w:E)&&d<128&&Math.floor(d)!==d?2e9-(1024*d|0):d,x),C||P==null||v(P,x)):v(y.strValue,x)}}for(var _,A,B=[kn,Hn],O=0;O<a.length;O++){var N=a[O],V=t.styleKeys[N];B[0]=sr(V[0],B[0]),B[1]=lr(V[1],B[1])}t.styleKey=(_=B[0],A=B[1],2097152*_+A);var M=t.styleKeys;t.labelDimsKey=_t(M.labelDimensions);var D=i(e,["label"],M.labelDimensions);if(t.labelKey=_t(D),t.labelStyleKey=_t(br(M.commonLabel,D)),!s){var I=i(e,["source-label"],M.labelDimensions);t.sourceLabelKey=_t(I),t.sourceLabelStyleKey=_t(br(M.commonLabel,I));var z=i(e,["target-label"],M.labelDimensions);t.targetLabelKey=_t(z),t.targetLabelStyleKey=_t(br(M.commonLabel,z))}if(s){var q=t.styleKeys,j=q.nodeBody,U=q.nodeBorder,K=q.nodeOutline,X=q.backgroundImage,H=q.compound,W=q.pie,Q=[j,U,K,X,H,W].filter(function(te){return te!=null}).reduce(br,[kn,Hn]);t.nodeKey=_t(Q),t.hasPie=W!=null&&W[0]!==kn&&W[1]!==Hn}return o!==t.styleKey},He.clearStyleHints=function(e){var t=e._private;t.styleCxtKey="",t.styleKeys={},t.styleKey=null,t.labelKey=null,t.labelStyleKey=null,t.sourceLabelKey=null,t.sourceLabelStyleKey=null,t.targetLabelKey=null,t.targetLabelStyleKey=null,t.nodeKey=null,t.hasPie=null},He.applyParsedProperty=function(e,t){var n,r=this,a=t,i=e._private.style,o=r.types,s=r.properties[a.name].type,l=a.bypass,u=i[a.name],c=u&&u.bypass,d=e._private,h="mapping",p=function(z){return z==null?null:z.pfValue!=null?z.pfValue:z.value},f=function(){var z=p(u),q=p(a);r.checkTriggers(e,a.name,z,q)};if(t.name==="curve-style"&&e.isEdge()&&(t.value!=="bezier"&&e.isLoop()||t.value==="haystack"&&(e.source().isParent()||e.target().isParent()))&&(a=t=this.parse(t.name,"bezier",l)),a.delete)return i[a.name]=void 0,f(),!0;if(a.deleteBypassed)return u?!!u.bypass&&(u.bypassed=void 0,f(),!0):(f(),!0);if(a.deleteBypass)return u?!!u.bypass&&(i[a.name]=u.bypassed,f(),!0):(f(),!0);var v=function(){we("Do not assign mappings to elements without corresponding data (i.e. ele `"+e.id()+"` has no mapping for property `"+a.name+"` with data field `"+a.field+"`); try a `["+a.field+"]` selector to limit scope to elements with `"+a.field+"` defined")};switch(a.mapped){case o.mapData:for(var m,g=a.field.split("."),y=d.data,b=0;b<g.length&&y;b++)y=y[g[b]];if(y==null)return v(),!1;if(!ae(y))return we("Do not use continuous mappers without specifying numeric data (i.e. `"+a.field+": "+y+"` for `"+e.id()+"` is non-numeric)"),!1;var k=a.fieldMax-a.fieldMin;if((m=k===0?0:(y-a.fieldMin)/k)<0?m=0:m>1&&(m=1),s.color){var x=a.valueMin[0],w=a.valueMax[0],E=a.valueMin[1],C=a.valueMax[1],S=a.valueMin[2],P=a.valueMax[2],_=a.valueMin[3]==null?1:a.valueMin[3],A=a.valueMax[3]==null?1:a.valueMax[3],B=[Math.round(x+(w-x)*m),Math.round(E+(C-E)*m),Math.round(S+(P-S)*m),Math.round(_+(A-_)*m)];n={bypass:a.bypass,name:a.name,value:B,strValue:"rgb("+B[0]+", "+B[1]+", "+B[2]+")"}}else{if(!s.number)return!1;var O=a.valueMin+(a.valueMax-a.valueMin)*m;n=this.parse(a.name,O,a.bypass,h)}if(!n)return v(),!1;n.mapping=a,a=n;break;case o.data:for(var N=a.field.split("."),V=d.data,M=0;M<N.length&&V;M++)V=V[N[M]];if(V!=null&&(n=this.parse(a.name,V,a.bypass,h)),!n)return v(),!1;n.mapping=a,a=n;break;case o.fn:var D=a.value,I=a.fnValue!=null?a.fnValue:D(e);if(a.prevFnValue=I,I==null)return we("Custom function mappers may not return null (i.e. `"+a.name+"` for ele `"+e.id()+"` is null)"),!1;if(!(n=this.parse(a.name,I,a.bypass,h)))return we("Custom function mappers may not return invalid values for the property type (i.e. `"+a.name+"` for ele `"+e.id()+"` is invalid)"),!1;n.mapping=xt(a),a=n;break;case void 0:break;default:return!1}return l?(a.bypassed=c?u.bypassed:u,i[a.name]=a):c?u.bypassed=a:i[a.name]=a,f(),!0},He.cleanElements=function(e,t){for(var n=0;n<e.length;n++){var r=e[n];if(this.clearStyleHints(r),r.dirtyCompoundBoundsCache(),r.dirtyBoundingBoxCache(),t)for(var a=r._private.style,i=Object.keys(a),o=0;o<i.length;o++){var s=i[o],l=a[s];l!=null&&(l.bypass?l.bypassed=null:a[s]=null)}else r._private.style={}}},He.update=function(){this._private.cy.mutableElements().updateStyle()},He.updateTransitions=function(e,t){var n=this,r=e._private,a=e.pstyle("transition-property").value,i=e.pstyle("transition-duration").pfValue,o=e.pstyle("transition-delay").pfValue;if(a.length>0&&i>0){for(var s={},l=!1,u=0;u<a.length;u++){var c=a[u],d=e.pstyle(c),h=t[c];if(h){var p=h.prev,f=h.next!=null?h.next:d,v=!1,m=void 0,g=1e-6;p&&(ae(p.pfValue)&&ae(f.pfValue)?(v=f.pfValue-p.pfValue,m=p.pfValue+g*v):ae(p.value)&&ae(f.value)?(v=f.value-p.value,m=p.value+g*v):Te(p.value)&&Te(f.value)&&(v=p.value[0]!==f.value[0]||p.value[1]!==f.value[1]||p.value[2]!==f.value[2],m=p.strValue),v&&(s[c]=f.strValue,this.applyBypass(e,c,m),l=!0))}}if(!l)return;r.transitioning=!0,new Mn(function(y){o>0?e.delayAnimation(o).play().promise().then(y):y()}).then(function(){return e.animation({style:s,duration:i,easing:e.pstyle("transition-timing-function").value,queue:!1}).play().promise()}).then(function(){n.removeBypasses(e,a),e.emitAndNotify("style"),r.transitioning=!1})}else r.transitioning&&(this.removeBypasses(e,a),e.emitAndNotify("style"),r.transitioning=!1)},He.checkTrigger=function(e,t,n,r,a,i){var o=this.properties[t],s=a(o);s!=null&&s(n,r)&&i(o)},He.checkZOrderTrigger=function(e,t,n,r){var a=this;this.checkTrigger(e,t,n,r,function(i){return i.triggersZOrder},function(){a._private.cy.notify("zorder",e)})},He.checkBoundsTrigger=function(e,t,n,r){this.checkTrigger(e,t,n,r,function(a){return a.triggersBounds},function(a){e.dirtyCompoundBoundsCache(),e.dirtyBoundingBoxCache(),!a.triggersBoundsOfParallelBeziers||t!=="curve-style"||n!=="bezier"&&r!=="bezier"||e.parallelEdges().forEach(function(i){i.dirtyBoundingBoxCache()}),!a.triggersBoundsOfConnectedEdges||t!=="display"||n!=="none"&&r!=="none"||e.connectedEdges().forEach(function(i){i.dirtyBoundingBoxCache()})})},He.checkTriggers=function(e,t,n,r){e.dirtyStyleCache(),this.checkZOrderTrigger(e,t,n,r),this.checkBoundsTrigger(e,t,n,r)};var jd={applyBypass:function(e,t,n,r){var a=[];if(t==="*"||t==="**"){if(n!==void 0)for(var i=0;i<this.properties.length;i++){var o=this.properties[i].name,s=this.parse(o,n,!0);s&&a.push(s)}}else if(ce(t)){var l=this.parse(t,n,!0);l&&a.push(l)}else{if(!me(t))return!1;var u=t;r=n;for(var c=Object.keys(u),d=0;d<c.length;d++){var h=c[d],p=u[h];if(p===void 0&&(p=u[aa(h)]),p!==void 0){var f=this.parse(h,p,!0);f&&a.push(f)}}}if(a.length===0)return!1;for(var v=!1,m=0;m<e.length;m++){for(var g=e[m],y={},b=void 0,k=0;k<a.length;k++){var x=a[k];if(r){var w=g.pstyle(x.name);b=y[x.name]={prev:w}}v=this.applyParsedProperty(g,xt(x))||v,r&&(b.next=g.pstyle(x.name))}v&&this.updateStyleHints(g),r&&this.updateTransitions(g,y,!0)}return v},overrideBypass:function(e,t,n){t=ri(t);for(var r=0;r<e.length;r++){var a=e[r],i=a._private.style[t],o=this.properties[t].type,s=o.color,l=o.mutiple,u=i?i.pfValue!=null?i.pfValue:i.value:null;i&&i.bypass?(i.value=n,i.pfValue!=null&&(i.pfValue=n),i.strValue=s?"rgb("+n.join(",")+")":l?n.join(" "):""+n,this.updateStyleHints(a)):this.applyBypass(a,t,n),this.checkTriggers(a,t,u,n)}},removeAllBypasses:function(e,t){return this.removeBypasses(e,this.propertyNames,t)},removeBypasses:function(e,t,n){for(var r=0;r<e.length;r++){for(var a=e[r],i={},o=0;o<t.length;o++){var s=t[o],l=this.properties[s],u=a.pstyle(l.name);if(u&&u.bypass){var c=this.parse(s,"",!0),d=i[l.name]={prev:u};this.applyParsedProperty(a,c),d.next=a.pstyle(l.name)}}this.updateStyleHints(a),n&&this.updateTransitions(a,i,!0)}}},Wd={getEmSizeInPixels:function(){var e=this.containerCss("font-size");return e!=null?parseFloat(e):1},containerCss:function(e){var t=this._private.cy,n=t.container(),r=t.window();if(r&&n&&r.getComputedStyle)return r.getComputedStyle(n).getPropertyValue(e)}},ja={getRenderedStyle:function(e,t){return t?this.getStylePropertyValue(e,t,!0):this.getRawStyle(e,!0)},getRawStyle:function(e,t){var n=this;if(e=e[0]){for(var r={},a=0;a<n.properties.length;a++){var i=n.properties[a],o=n.getStylePropertyValue(e,i.name,t);o!=null&&(r[i.name]=o,r[aa(i.name)]=o)}return r}},getIndexedStyle:function(e,t,n,r){var a=e.pstyle(t)[n][r];return a??e.cy().style().getDefaultProperty(t)[n][0]},getStylePropertyValue:function(e,t,n){if(e=e[0]){var r=this.properties[t];r.alias&&(r=r.pointsTo);var a=r.type,i=e.pstyle(r.name);if(i){var o=i.value,s=i.units,l=i.strValue;if(n&&a.number&&o!=null&&ae(o)){var u=e.cy().zoom(),c=function(p){return p*u},d=function(p,f){return c(p)+f},h=Te(o);return(h?s.every(function(p){return p!=null}):s!=null)?h?o.map(function(p,f){return d(p,s[f])}).join(" "):d(o,s):h?o.map(function(p){return ce(p)?p:""+c(p)}).join(" "):""+c(o)}if(l!=null)return l}return null}},getAnimationStartStyle:function(e,t){for(var n={},r=0;r<t.length;r++){var a=t[r].name,i=e.pstyle(a);i!==void 0&&(i=me(i)?this.parse(a,i.strValue):this.parse(a,i)),i&&(n[a]=i)}return n},getPropsList:function(e){var t=[],n=e,r=this.properties;if(n)for(var a=Object.keys(n),i=0;i<a.length;i++){var o=a[i],s=n[o],l=r[o]||r[ri(o)],u=this.parse(l.name,s);u&&t.push(u)}return t},getNonDefaultPropertiesHash:function(e,t,n){var r,a,i,o,s,l,u=n.slice();for(s=0;s<t.length;s++)if(r=t[s],(a=e.pstyle(r,!1))!=null)if(a.pfValue!=null)u[0]=sr(o,u[0]),u[1]=lr(o,u[1]);else for(i=a.strValue,l=0;l<i.length;l++)o=i.charCodeAt(l),u[0]=sr(o,u[0]),u[1]=lr(o,u[1]);return u}};ja.getPropertiesHash=ja.getNonDefaultPropertiesHash;var Kd={appendFromJson:function(e){for(var t=this,n=0;n<e.length;n++){var r=e[n],a=r.selector,i=r.style||r.css,o=Object.keys(i);t.selector(a);for(var s=0;s<o.length;s++){var l=o[s],u=i[l];t.css(l,u)}}return t},fromJson:function(e){var t=this;return t.resetToDefault(),t.appendFromJson(e),t},json:function(){for(var e=[],t=this.defaultLength;t<this.length;t++){for(var n=this[t],r=n.selector,a=n.properties,i={},o=0;o<a.length;o++){var s=a[o];i[s.name]=s.strValue}e.push({selector:r?r.toString():"core",style:i})}return e}},Hd={appendFromString:function(e){var t,n,r,a=this,i=""+e;function o(){i=i.length>t.length?i.substr(t.length):""}function s(){n=n.length>r.length?n.substr(r.length):""}for(i=i.replace(/[/][*](\s|.)+?[*][/]/g,"");!i.match(/^\s*$/);){var l=i.match(/^\s*((?:.|\s)+?)\s*\{((?:.|\s)+?)\}/);if(!l){we("Halting stylesheet parsing: String stylesheet contains more to parse but no selector and block found in: "+i);break}t=l[0];var u=l[1];if(u!=="core"&&new jt(u).invalid){we("Skipping parsing of block: Invalid selector found in string stylesheet: "+u),o();continue}var c=l[2],d=!1;n=c;for(var h=[];!n.match(/^\s*$/);){var p=n.match(/^\s*(.+?)\s*:\s*(.+?)(?:\s*;|\s*$)/);if(!p){we("Skipping parsing of block: Invalid formatting of style property and value definitions found in:"+c),d=!0;break}r=p[0];var f=p[1],v=p[2];this.properties[f]?a.parse(f,v)?(h.push({name:f,val:v}),s()):(we("Skipping property: Invalid property definition in: "+r),s()):(we("Skipping property: Invalid property name in: "+r),s())}if(d){o();break}a.selector(u);for(var m=0;m<h.length;m++){var g=h[m];a.css(g.name,g.val)}o()}return a},fromString:function(e){var t=this;return t.resetToDefault(),t.appendFromString(e),t}},Xe={};(function(){var e=Ie,t=Vl,n=ql,r=function(X){return"^"+X+"\\s*\\(\\s*([\\w\\.]+)\\s*\\)$"},a=function(X){var H=e+"|\\w+|"+t+"|"+n+"|\\#[0-9a-fA-F]{3}|\\#[0-9a-fA-F]{6}";return"^"+X+"\\s*\\(([\\w\\.]+)\\s*\\,\\s*("+e+")\\s*\\,\\s*("+e+")\\s*,\\s*("+H+")\\s*\\,\\s*("+H+")\\)$"},i=[`^url\\s*\\(\\s*['"]?(.+?)['"]?\\s*\\)$`,"^(none)$","^(.+)$"];Xe.types={time:{number:!0,min:0,units:"s|ms",implicitUnits:"ms"},percent:{number:!0,min:0,max:100,units:"%",implicitUnits:"%"},percentages:{number:!0,min:0,max:100,units:"%",implicitUnits:"%",multiple:!0},zeroOneNumber:{number:!0,min:0,max:1,unitless:!0},zeroOneNumbers:{number:!0,min:0,max:1,unitless:!0,multiple:!0},nOneOneNumber:{number:!0,min:-1,max:1,unitless:!0},nonNegativeInt:{number:!0,min:0,integer:!0,unitless:!0},nonNegativeNumber:{number:!0,min:0,unitless:!0},position:{enums:["parent","origin"]},nodeSize:{number:!0,min:0,enums:["label"]},number:{number:!0,unitless:!0},numbers:{number:!0,unitless:!0,multiple:!0},positiveNumber:{number:!0,unitless:!0,min:0,strictMin:!0},size:{number:!0,min:0},bidirectionalSize:{number:!0},bidirectionalSizeMaybePercent:{number:!0,allowPercent:!0},bidirectionalSizes:{number:!0,multiple:!0},sizeMaybePercent:{number:!0,min:0,allowPercent:!0},axisDirection:{enums:["horizontal","leftward","rightward","vertical","upward","downward","auto"]},paddingRelativeTo:{enums:["width","height","average","min","max"]},bgWH:{number:!0,min:0,allowPercent:!0,enums:["auto"],multiple:!0},bgPos:{number:!0,allowPercent:!0,multiple:!0},bgRelativeTo:{enums:["inner","include-padding"],multiple:!0},bgRepeat:{enums:["repeat","repeat-x","repeat-y","no-repeat"],multiple:!0},bgFit:{enums:["none","contain","cover"],multiple:!0},bgCrossOrigin:{enums:["anonymous","use-credentials","null"],multiple:!0},bgClip:{enums:["none","node"],multiple:!0},bgContainment:{enums:["inside","over"],multiple:!0},color:{color:!0},colors:{color:!0,multiple:!0},fill:{enums:["solid","linear-gradient","radial-gradient"]},bool:{enums:["yes","no"]},bools:{enums:["yes","no"],multiple:!0},lineStyle:{enums:["solid","dotted","dashed"]},lineCap:{enums:["butt","round","square"]},linePosition:{enums:["center","inside","outside"]},lineJoin:{enums:["round","bevel","miter"]},borderStyle:{enums:["solid","dotted","dashed","double"]},curveStyle:{enums:["bezier","unbundled-bezier","haystack","segments","straight","straight-triangle","taxi","round-segments","round-taxi"]},radiusType:{enums:["arc-radius","influence-radius"],multiple:!0},fontFamily:{regex:'^([\\w- \\"]+(?:\\s*,\\s*[\\w- \\"]+)*)$'},fontStyle:{enums:["italic","normal","oblique"]},fontWeight:{enums:["normal","bold","bolder","lighter","100","200","300","400","500","600","800","900",100,200,300,400,500,600,700,800,900]},textDecoration:{enums:["none","underline","overline","line-through"]},textTransform:{enums:["none","uppercase","lowercase"]},textWrap:{enums:["none","wrap","ellipsis"]},textOverflowWrap:{enums:["whitespace","anywhere"]},textBackgroundShape:{enums:["rectangle","roundrectangle","round-rectangle"]},nodeShape:{enums:["rectangle","roundrectangle","round-rectangle","cutrectangle","cut-rectangle","bottomroundrectangle","bottom-round-rectangle","barrel","ellipse","triangle","round-triangle","square","pentagon","round-pentagon","hexagon","round-hexagon","concavehexagon","concave-hexagon","heptagon","round-heptagon","octagon","round-octagon","tag","round-tag","star","diamond","round-diamond","vee","rhomboid","right-rhomboid","polygon"]},overlayShape:{enums:["roundrectangle","round-rectangle","ellipse"]},cornerRadius:{number:!0,min:0,units:"px|em",implicitUnits:"px",enums:["auto"]},compoundIncludeLabels:{enums:["include","exclude"]},arrowShape:{enums:["tee","triangle","triangle-tee","circle-triangle","triangle-cross","triangle-backcurve","vee","square","circle","diamond","chevron","none"]},arrowFill:{enums:["filled","hollow"]},arrowWidth:{number:!0,units:"%|px|em",implicitUnits:"px",enums:["match-line"]},display:{enums:["element","none"]},visibility:{enums:["hidden","visible"]},zCompoundDepth:{enums:["bottom","orphan","auto","top"]},zIndexCompare:{enums:["auto","manual"]},valign:{enums:["top","center","bottom"]},halign:{enums:["left","center","right"]},justification:{enums:["left","center","right","auto"]},text:{string:!0},data:{mapping:!0,regex:r("data")},layoutData:{mapping:!0,regex:r("layoutData")},scratch:{mapping:!0,regex:r("scratch")},mapData:{mapping:!0,regex:a("mapData")},mapLayoutData:{mapping:!0,regex:a("mapLayoutData")},mapScratch:{mapping:!0,regex:a("mapScratch")},fn:{mapping:!0,fn:!0},url:{regexes:i,singleRegexMatchValue:!0},urls:{regexes:i,singleRegexMatchValue:!0,multiple:!0},propList:{propList:!0},angle:{number:!0,units:"deg|rad",implicitUnits:"rad"},textRotation:{number:!0,units:"deg|rad",implicitUnits:"rad",enums:["none","autorotate"]},polygonPointList:{number:!0,multiple:!0,evenMultiple:!0,min:-1,max:1,unitless:!0},edgeDistances:{enums:["intersection","node-position","endpoints"]},edgeEndpoint:{number:!0,multiple:!0,units:"%|px|em|deg|rad",implicitUnits:"px",enums:["inside-to-node","outside-to-node","outside-to-node-or-label","outside-to-line","outside-to-line-or-label"],singleEnum:!0,validate:function(X,H){switch(X.length){case 2:return H[0]!=="deg"&&H[0]!=="rad"&&H[1]!=="deg"&&H[1]!=="rad";case 1:return ce(X[0])||H[0]==="deg"||H[0]==="rad";default:return!1}}},easing:{regexes:["^(spring)\\s*\\(\\s*("+e+")\\s*,\\s*("+e+")\\s*\\)$","^(cubic-bezier)\\s*\\(\\s*("+e+")\\s*,\\s*("+e+")\\s*,\\s*("+e+")\\s*,\\s*("+e+")\\s*\\)$"],enums:["linear","ease","ease-in","ease-out","ease-in-out","ease-in-sine","ease-out-sine","ease-in-out-sine","ease-in-quad","ease-out-quad","ease-in-out-quad","ease-in-cubic","ease-out-cubic","ease-in-out-cubic","ease-in-quart","ease-out-quart","ease-in-out-quart","ease-in-quint","ease-out-quint","ease-in-out-quint","ease-in-expo","ease-out-expo","ease-in-out-expo","ease-in-circ","ease-out-circ","ease-in-out-circ"]},gradientDirection:{enums:["to-bottom","to-top","to-left","to-right","to-bottom-right","to-bottom-left","to-top-right","to-top-left","to-right-bottom","to-left-bottom","to-right-top","to-left-top"]},boundsExpansion:{number:!0,multiple:!0,min:0,validate:function(X){var H=X.length;return H===1||H===2||H===4}}};var o={zeroNonZero:function(X,H){return(X==null||H==null)&&X!==H||X==0&&H!=0||X!=0&&H==0},any:function(X,H){return X!=H},emptyNonEmpty:function(X,H){var W=Xt(X),Q=Xt(H);return W&&!Q||!W&&Q}},s=Xe.types,l=[{name:"label",type:s.text,triggersBounds:o.any,triggersZOrder:o.emptyNonEmpty},{name:"text-rotation",type:s.textRotation,triggersBounds:o.any},{name:"text-margin-x",type:s.bidirectionalSize,triggersBounds:o.any},{name:"text-margin-y",type:s.bidirectionalSize,triggersBounds:o.any}],u=[{name:"source-label",type:s.text,triggersBounds:o.any},{name:"source-text-rotation",type:s.textRotation,triggersBounds:o.any},{name:"source-text-margin-x",type:s.bidirectionalSize,triggersBounds:o.any},{name:"source-text-margin-y",type:s.bidirectionalSize,triggersBounds:o.any},{name:"source-text-offset",type:s.size,triggersBounds:o.any}],c=[{name:"target-label",type:s.text,triggersBounds:o.any},{name:"target-text-rotation",type:s.textRotation,triggersBounds:o.any},{name:"target-text-margin-x",type:s.bidirectionalSize,triggersBounds:o.any},{name:"target-text-margin-y",type:s.bidirectionalSize,triggersBounds:o.any},{name:"target-text-offset",type:s.size,triggersBounds:o.any}],d=[{name:"font-family",type:s.fontFamily,triggersBounds:o.any},{name:"font-style",type:s.fontStyle,triggersBounds:o.any},{name:"font-weight",type:s.fontWeight,triggersBounds:o.any},{name:"font-size",type:s.size,triggersBounds:o.any},{name:"text-transform",type:s.textTransform,triggersBounds:o.any},{name:"text-wrap",type:s.textWrap,triggersBounds:o.any},{name:"text-overflow-wrap",type:s.textOverflowWrap,triggersBounds:o.any},{name:"text-max-width",type:s.size,triggersBounds:o.any},{name:"text-outline-width",type:s.size,triggersBounds:o.any},{name:"line-height",type:s.positiveNumber,triggersBounds:o.any}],h=[{name:"text-valign",type:s.valign,triggersBounds:o.any},{name:"text-halign",type:s.halign,triggersBounds:o.any},{name:"color",type:s.color},{name:"text-outline-color",type:s.color},{name:"text-outline-opacity",type:s.zeroOneNumber},{name:"text-background-color",type:s.color},{name:"text-background-opacity",type:s.zeroOneNumber},{name:"text-background-padding",type:s.size,triggersBounds:o.any},{name:"text-border-opacity",type:s.zeroOneNumber},{name:"text-border-color",type:s.color},{name:"text-border-width",type:s.size,triggersBounds:o.any},{name:"text-border-style",type:s.borderStyle,triggersBounds:o.any},{name:"text-background-shape",type:s.textBackgroundShape,triggersBounds:o.any},{name:"text-justification",type:s.justification}],p=[{name:"events",type:s.bool,triggersZOrder:o.any},{name:"text-events",type:s.bool,triggersZOrder:o.any}],f=[{name:"display",type:s.display,triggersZOrder:o.any,triggersBounds:o.any,triggersBoundsOfConnectedEdges:!0},{name:"visibility",type:s.visibility,triggersZOrder:o.any},{name:"opacity",type:s.zeroOneNumber,triggersZOrder:o.zeroNonZero},{name:"text-opacity",type:s.zeroOneNumber},{name:"min-zoomed-font-size",type:s.size},{name:"z-compound-depth",type:s.zCompoundDepth,triggersZOrder:o.any},{name:"z-index-compare",type:s.zIndexCompare,triggersZOrder:o.any},{name:"z-index",type:s.number,triggersZOrder:o.any}],v=[{name:"overlay-padding",type:s.size,triggersBounds:o.any},{name:"overlay-color",type:s.color},{name:"overlay-opacity",type:s.zeroOneNumber,triggersBounds:o.zeroNonZero},{name:"overlay-shape",type:s.overlayShape,triggersBounds:o.any},{name:"overlay-corner-radius",type:s.cornerRadius}],m=[{name:"underlay-padding",type:s.size,triggersBounds:o.any},{name:"underlay-color",type:s.color},{name:"underlay-opacity",type:s.zeroOneNumber,triggersBounds:o.zeroNonZero},{name:"underlay-shape",type:s.overlayShape,triggersBounds:o.any},{name:"underlay-corner-radius",type:s.cornerRadius}],g=[{name:"transition-property",type:s.propList},{name:"transition-duration",type:s.time},{name:"transition-delay",type:s.time},{name:"transition-timing-function",type:s.easing}],y=function(X,H){return H.value==="label"?-X.poolIndex():H.pfValue},b=[{name:"height",type:s.nodeSize,triggersBounds:o.any,hashOverride:y},{name:"width",type:s.nodeSize,triggersBounds:o.any,hashOverride:y},{name:"shape",type:s.nodeShape,triggersBounds:o.any},{name:"shape-polygon-points",type:s.polygonPointList,triggersBounds:o.any},{name:"corner-radius",type:s.cornerRadius},{name:"background-color",type:s.color},{name:"background-fill",type:s.fill},{name:"background-opacity",type:s.zeroOneNumber},{name:"background-blacken",type:s.nOneOneNumber},{name:"background-gradient-stop-colors",type:s.colors},{name:"background-gradient-stop-positions",type:s.percentages},{name:"background-gradient-direction",type:s.gradientDirection},{name:"padding",type:s.sizeMaybePercent,triggersBounds:o.any},{name:"padding-relative-to",type:s.paddingRelativeTo,triggersBounds:o.any},{name:"bounds-expansion",type:s.boundsExpansion,triggersBounds:o.any}],k=[{name:"border-color",type:s.color},{name:"border-opacity",type:s.zeroOneNumber},{name:"border-width",type:s.size,triggersBounds:o.any},{name:"border-style",type:s.borderStyle},{name:"border-cap",type:s.lineCap},{name:"border-join",type:s.lineJoin},{name:"border-dash-pattern",type:s.numbers},{name:"border-dash-offset",type:s.number},{name:"border-position",type:s.linePosition}],x=[{name:"outline-color",type:s.color},{name:"outline-opacity",type:s.zeroOneNumber},{name:"outline-width",type:s.size,triggersBounds:o.any},{name:"outline-style",type:s.borderStyle},{name:"outline-offset",type:s.size,triggersBounds:o.any}],w=[{name:"background-image",type:s.urls},{name:"background-image-crossorigin",type:s.bgCrossOrigin},{name:"background-image-opacity",type:s.zeroOneNumbers},{name:"background-image-containment",type:s.bgContainment},{name:"background-image-smoothing",type:s.bools},{name:"background-position-x",type:s.bgPos},{name:"background-position-y",type:s.bgPos},{name:"background-width-relative-to",type:s.bgRelativeTo},{name:"background-height-relative-to",type:s.bgRelativeTo},{name:"background-repeat",type:s.bgRepeat},{name:"background-fit",type:s.bgFit},{name:"background-clip",type:s.bgClip},{name:"background-width",type:s.bgWH},{name:"background-height",type:s.bgWH},{name:"background-offset-x",type:s.bgPos},{name:"background-offset-y",type:s.bgPos}],E=[{name:"position",type:s.position,triggersBounds:o.any},{name:"compound-sizing-wrt-labels",type:s.compoundIncludeLabels,triggersBounds:o.any},{name:"min-width",type:s.size,triggersBounds:o.any},{name:"min-width-bias-left",type:s.sizeMaybePercent,triggersBounds:o.any},{name:"min-width-bias-right",type:s.sizeMaybePercent,triggersBounds:o.any},{name:"min-height",type:s.size,triggersBounds:o.any},{name:"min-height-bias-top",type:s.sizeMaybePercent,triggersBounds:o.any},{name:"min-height-bias-bottom",type:s.sizeMaybePercent,triggersBounds:o.any}],C=[{name:"line-style",type:s.lineStyle},{name:"line-color",type:s.color},{name:"line-fill",type:s.fill},{name:"line-cap",type:s.lineCap},{name:"line-opacity",type:s.zeroOneNumber},{name:"line-dash-pattern",type:s.numbers},{name:"line-dash-offset",type:s.number},{name:"line-outline-width",type:s.size},{name:"line-outline-color",type:s.color},{name:"line-gradient-stop-colors",type:s.colors},{name:"line-gradient-stop-positions",type:s.percentages},{name:"curve-style",type:s.curveStyle,triggersBounds:o.any,triggersBoundsOfParallelBeziers:!0},{name:"haystack-radius",type:s.zeroOneNumber,triggersBounds:o.any},{name:"source-endpoint",type:s.edgeEndpoint,triggersBounds:o.any},{name:"target-endpoint",type:s.edgeEndpoint,triggersBounds:o.any},{name:"control-point-step-size",type:s.size,triggersBounds:o.any},{name:"control-point-distances",type:s.bidirectionalSizes,triggersBounds:o.any},{name:"control-point-weights",type:s.numbers,triggersBounds:o.any},{name:"segment-distances",type:s.bidirectionalSizes,triggersBounds:o.any},{name:"segment-weights",type:s.numbers,triggersBounds:o.any},{name:"segment-radii",type:s.numbers,triggersBounds:o.any},{name:"radius-type",type:s.radiusType,triggersBounds:o.any},{name:"taxi-turn",type:s.bidirectionalSizeMaybePercent,triggersBounds:o.any},{name:"taxi-turn-min-distance",type:s.size,triggersBounds:o.any},{name:"taxi-direction",type:s.axisDirection,triggersBounds:o.any},{name:"taxi-radius",type:s.number,triggersBounds:o.any},{name:"edge-distances",type:s.edgeDistances,triggersBounds:o.any},{name:"arrow-scale",type:s.positiveNumber,triggersBounds:o.any},{name:"loop-direction",type:s.angle,triggersBounds:o.any},{name:"loop-sweep",type:s.angle,triggersBounds:o.any},{name:"source-distance-from-node",type:s.size,triggersBounds:o.any},{name:"target-distance-from-node",type:s.size,triggersBounds:o.any}],S=[{name:"ghost",type:s.bool,triggersBounds:o.any},{name:"ghost-offset-x",type:s.bidirectionalSize,triggersBounds:o.any},{name:"ghost-offset-y",type:s.bidirectionalSize,triggersBounds:o.any},{name:"ghost-opacity",type:s.zeroOneNumber}],P=[{name:"selection-box-color",type:s.color},{name:"selection-box-opacity",type:s.zeroOneNumber},{name:"selection-box-border-color",type:s.color},{name:"selection-box-border-width",type:s.size},{name:"active-bg-color",type:s.color},{name:"active-bg-opacity",type:s.zeroOneNumber},{name:"active-bg-size",type:s.size},{name:"outside-texture-bg-color",type:s.color},{name:"outside-texture-bg-opacity",type:s.zeroOneNumber}],_=[];Xe.pieBackgroundN=16,_.push({name:"pie-size",type:s.sizeMaybePercent});for(var A=1;A<=Xe.pieBackgroundN;A++)_.push({name:"pie-"+A+"-background-color",type:s.color}),_.push({name:"pie-"+A+"-background-size",type:s.percent}),_.push({name:"pie-"+A+"-background-opacity",type:s.zeroOneNumber});var B=[],O=Xe.arrowPrefixes=["source","mid-source","target","mid-target"];[{name:"arrow-shape",type:s.arrowShape,triggersBounds:o.any},{name:"arrow-color",type:s.color},{name:"arrow-fill",type:s.arrowFill},{name:"arrow-width",type:s.arrowWidth}].forEach(function(X){O.forEach(function(H){var W=H+"-"+X.name,Q=X.type,te=X.triggersBounds;B.push({name:W,type:Q,triggersBounds:te})})},{});var N=Xe.properties=[].concat(p,g,f,v,m,S,h,d,l,u,c,b,k,x,w,_,E,C,B,P),V=Xe.propertyGroups={behavior:p,transition:g,visibility:f,overlay:v,underlay:m,ghost:S,commonLabel:h,labelDimensions:d,mainLabel:l,sourceLabel:u,targetLabel:c,nodeBody:b,nodeBorder:k,nodeOutline:x,backgroundImage:w,pie:_,compound:E,edgeLine:C,edgeArrow:B,core:P},M=Xe.propertyGroupNames={};(Xe.propertyGroupKeys=Object.keys(V)).forEach(function(X){M[X]=V[X].map(function(H){return H.name}),V[X].forEach(function(H){return H.groupKey=X})});var D=Xe.aliases=[{name:"content",pointsTo:"label"},{name:"control-point-distance",pointsTo:"control-point-distances"},{name:"control-point-weight",pointsTo:"control-point-weights"},{name:"segment-distance",pointsTo:"segment-distances"},{name:"segment-weight",pointsTo:"segment-weights"},{name:"segment-radius",pointsTo:"segment-radii"},{name:"edge-text-rotation",pointsTo:"text-rotation"},{name:"padding-left",pointsTo:"padding"},{name:"padding-right",pointsTo:"padding"},{name:"padding-top",pointsTo:"padding"},{name:"padding-bottom",pointsTo:"padding"}];Xe.propertyNames=N.map(function(X){return X.name});for(var I=0;I<N.length;I++){var z=N[I];N[z.name]=z}for(var q=0;q<D.length;q++){var j=D[q],U=N[j.pointsTo],K={name:j.name,alias:!0,pointsTo:U};N.push(K),N[j.name]=K}})(),Xe.getDefaultProperty=function(e){return this.getDefaultProperties()[e]},Xe.getDefaultProperties=function(){var e=this._private;if(e.defaultProperties!=null)return e.defaultProperties;for(var t=he({"selection-box-color":"#ddd","selection-box-opacity":.65,"selection-box-border-color":"#aaa","selection-box-border-width":1,"active-bg-color":"black","active-bg-opacity":.15,"active-bg-size":30,"outside-texture-bg-color":"#000","outside-texture-bg-opacity":.125,events:"yes","text-events":"no","text-valign":"top","text-halign":"center","text-justification":"auto","line-height":1,color:"#000","text-outline-color":"#000","text-outline-width":0,"text-outline-opacity":1,"text-opacity":1,"text-decoration":"none","text-transform":"none","text-wrap":"none","text-overflow-wrap":"whitespace","text-max-width":9999,"text-background-color":"#000","text-background-opacity":0,"text-background-shape":"rectangle","text-background-padding":0,"text-border-opacity":0,"text-border-width":0,"text-border-style":"solid","text-border-color":"#000","font-family":"Helvetica Neue, Helvetica, sans-serif","font-style":"normal","font-weight":"normal","font-size":16,"min-zoomed-font-size":0,"text-rotation":"none","source-text-rotation":"none","target-text-rotation":"none",visibility:"visible",display:"element",opacity:1,"z-compound-depth":"auto","z-index-compare":"auto","z-index":0,label:"","text-margin-x":0,"text-margin-y":0,"source-label":"","source-text-offset":0,"source-text-margin-x":0,"source-text-margin-y":0,"target-label":"","target-text-offset":0,"target-text-margin-x":0,"target-text-margin-y":0,"overlay-opacity":0,"overlay-color":"#000","overlay-padding":10,"overlay-shape":"round-rectangle","overlay-corner-radius":"auto","underlay-opacity":0,"underlay-color":"#000","underlay-padding":10,"underlay-shape":"round-rectangle","underlay-corner-radius":"auto","transition-property":"none","transition-duration":0,"transition-delay":0,"transition-timing-function":"linear","background-blacken":0,"background-color":"#999","background-fill":"solid","background-opacity":1,"background-image":"none","background-image-crossorigin":"anonymous","background-image-opacity":1,"background-image-containment":"inside","background-image-smoothing":"yes","background-position-x":"50%","background-position-y":"50%","background-offset-x":0,"background-offset-y":0,"background-width-relative-to":"include-padding","background-height-relative-to":"include-padding","background-repeat":"no-repeat","background-fit":"none","background-clip":"node","background-width":"auto","background-height":"auto","border-color":"#000","border-opacity":1,"border-width":0,"border-style":"solid","border-dash-pattern":[4,2],"border-dash-offset":0,"border-cap":"butt","border-join":"miter","border-position":"center","outline-color":"#999","outline-opacity":1,"outline-width":0,"outline-offset":0,"outline-style":"solid",height:30,width:30,shape:"ellipse","shape-polygon-points":"-1, -1,   1, -1,   1, 1,   -1, 1","corner-radius":"auto","bounds-expansion":0,"background-gradient-direction":"to-bottom","background-gradient-stop-colors":"#999","background-gradient-stop-positions":"0%",ghost:"no","ghost-offset-y":0,"ghost-offset-x":0,"ghost-opacity":0,padding:0,"padding-relative-to":"width",position:"origin","compound-sizing-wrt-labels":"include","min-width":0,"min-width-bias-left":0,"min-width-bias-right":0,"min-height":0,"min-height-bias-top":0,"min-height-bias-bottom":0},{"pie-size":"100%"},[{name:"pie-{{i}}-background-color",value:"black"},{name:"pie-{{i}}-background-size",value:"0%"},{name:"pie-{{i}}-background-opacity",value:1}].reduce(function(l,u){for(var c=1;c<=Xe.pieBackgroundN;c++){var d=u.name.replace("{{i}}",c),h=u.value;l[d]=h}return l},{}),{"line-style":"solid","line-color":"#999","line-fill":"solid","line-cap":"butt","line-opacity":1,"line-outline-width":0,"line-outline-color":"#000","line-gradient-stop-colors":"#999","line-gradient-stop-positions":"0%","control-point-step-size":40,"control-point-weights":.5,"segment-weights":.5,"segment-distances":20,"segment-radii":15,"radius-type":"arc-radius","taxi-turn":"50%","taxi-radius":15,"taxi-turn-min-distance":10,"taxi-direction":"auto","edge-distances":"intersection","curve-style":"haystack","haystack-radius":0,"arrow-scale":1,"loop-direction":"-45deg","loop-sweep":"-90deg","source-distance-from-node":0,"target-distance-from-node":0,"source-endpoint":"outside-to-node","target-endpoint":"outside-to-node","line-dash-pattern":[6,3],"line-dash-offset":0},[{name:"arrow-shape",value:"none"},{name:"arrow-color",value:"#999"},{name:"arrow-fill",value:"filled"},{name:"arrow-width",value:1}].reduce(function(l,u){return Xe.arrowPrefixes.forEach(function(c){var d=c+"-"+u.name,h=u.value;l[d]=h}),l},{})),n={},r=0;r<this.properties.length;r++){var a=this.properties[r];if(!a.pointsTo){var i=a.name,o=t[i],s=this.parse(i,o);n[i]=s}}return e.defaultProperties=n,e.defaultProperties},Xe.addDefaultStylesheet=function(){this.selector(":parent").css({shape:"rectangle",padding:10,"background-color":"#eee","border-color":"#ccc","border-width":1}).selector("edge").css({width:3}).selector(":loop").css({"curve-style":"bezier"}).selector("edge:compound").css({"curve-style":"bezier","source-endpoint":"outside-to-line","target-endpoint":"outside-to-line"}).selector(":selected").css({"background-color":"#0169D9","line-color":"#0169D9","source-arrow-color":"#0169D9","target-arrow-color":"#0169D9","mid-source-arrow-color":"#0169D9","mid-target-arrow-color":"#0169D9"}).selector(":parent:selected").css({"background-color":"#CCE1F9","border-color":"#aec8e5"}).selector(":active").css({"overlay-color":"black","overlay-padding":10,"overlay-opacity":.25}),this.defaultLength=this.length};var el={parse:function(e,t,n,r){var a=this;if(_e(t))return a.parseImplWarn(e,t,n,r);var i,o=gs(e,""+t,n?"t":"f",r==="mapping"||r===!0||r===!1||r==null?"dontcare":r),s=a.propCache=a.propCache||[];return(i=s[o])||(i=s[o]=a.parseImplWarn(e,t,n,r)),(n||r==="mapping")&&(i=xt(i))&&(i.value=xt(i.value)),i},parseImplWarn:function(e,t,n,r){var a=this.parseImpl(e,t,n,r);return a||t==null||we("The style property `".concat(e,": ").concat(t,"` is invalid")),!a||a.name!=="width"&&a.name!=="height"||t!=="label"||we("The style value of `label` is deprecated for `"+a.name+"`"),a}};el.parseImpl=function(e,t,n,r){var a=this;e=ri(e);var i=a.properties[e],o=t,s=a.types;if(!i||t===void 0)return null;i.alias&&(i=i.pointsTo,e=i.name);var l=ce(t);l&&(t=t.trim());var u,c,d=i.type;if(!d)return null;if(n&&(t===""||t===null))return{name:e,value:t,bypass:!0,deleteBypass:!0};if(_e(t))return{name:e,value:t,strValue:"fn",mapped:s.fn,bypass:n};if(!(!l||r||t.length<7||t[1]!=="a")){if(t.length>=7&&t[0]==="d"&&(u=new RegExp(s.data.regex).exec(t))){if(n)return!1;var h=s.data;return{name:e,value:u,strValue:""+t,mapped:h,field:u[1],bypass:n}}if(t.length>=10&&t[0]==="m"&&(c=new RegExp(s.mapData.regex).exec(t))){if(n||d.multiple)return!1;var p=s.mapData;if(!d.color&&!d.number)return!1;var f=this.parse(e,c[4]);if(!f||f.mapped)return!1;var v=this.parse(e,c[5]);if(!v||v.mapped)return!1;if(f.pfValue===v.pfValue||f.strValue===v.strValue)return we("`"+e+": "+t+"` is not a valid mapper because the output range is zero; converting to `"+e+": "+f.strValue+"`"),this.parse(e,f.strValue);if(d.color){var m=f.value,g=v.value;if(!(m[0]!==g[0]||m[1]!==g[1]||m[2]!==g[2]||m[3]!==g[3]&&(m[3]!=null&&m[3]!==1||g[3]!=null&&g[3]!==1)))return!1}return{name:e,value:c,strValue:""+t,mapped:p,field:c[1],fieldMin:parseFloat(c[2]),fieldMax:parseFloat(c[3]),valueMin:f.value,valueMax:v.value,bypass:n}}}if(d.multiple&&r!=="multiple"){var y;if(y=l?t.split(/\s+/):Te(t)?t:[t],d.evenMultiple&&y.length%2!=0)return null;for(var b=[],k=[],x=[],w="",E=!1,C=0;C<y.length;C++){var S=a.parse(e,y[C],n,"multiple");E=E||ce(S.value),b.push(S.value),x.push(S.pfValue!=null?S.pfValue:S.value),k.push(S.units),w+=(C>0?" ":"")+S.strValue}return d.validate&&!d.validate(b,k)?null:d.singleEnum&&E?b.length===1&&ce(b[0])?{name:e,value:b[0],strValue:b[0],bypass:n}:null:{name:e,value:b,pfValue:x,strValue:w,bypass:n,units:k}}var P,_,A=function(){for(var Q=0;Q<d.enums.length;Q++)if(d.enums[Q]===t)return{name:e,value:t,strValue:""+t,bypass:n};return null};if(d.number){var B,O="px";if(d.units&&(B=d.units),d.implicitUnits&&(O=d.implicitUnits),!d.unitless)if(l){var N="px|em"+(d.allowPercent?"|\\%":"");B&&(N=B);var V=t.match("^("+Ie+")("+N+")?$");V&&(t=V[1],B=V[2]||O)}else B&&!d.implicitUnits||(B=O);if(t=parseFloat(t),isNaN(t)&&d.enums===void 0)return null;if(isNaN(t)&&d.enums!==void 0)return t=o,A();if(d.integer&&(!ae(_=t)||Math.floor(_)!==_)||d.min!==void 0&&(t<d.min||d.strictMin&&t===d.min)||d.max!==void 0&&(t>d.max||d.strictMax&&t===d.max))return null;var M={name:e,value:t,strValue:""+t+(B||""),units:B,bypass:n};return d.unitless||B!=="px"&&B!=="em"?M.pfValue=t:M.pfValue=B!=="px"&&B?this.getEmSizeInPixels()*t:t,B!=="ms"&&B!=="s"||(M.pfValue=B==="ms"?t:1e3*t),B!=="deg"&&B!=="rad"||(M.pfValue=B==="rad"?t:(P=t,Math.PI*P/180)),B==="%"&&(M.pfValue=t/100),M}if(d.propList){var D=[],I=""+t;if(I!=="none"){for(var z=I.split(/\s*,\s*|\s+/),q=0;q<z.length;q++){var j=z[q].trim();a.properties[j]?D.push(j):we("`"+j+"` is not a valid property name")}if(D.length===0)return null}return{name:e,value:D,strValue:D.length===0?"none":D.join(" "),bypass:n}}if(d.color){var U=ls(t);return U?{name:e,value:U,pfValue:U,strValue:"rgb("+U[0]+","+U[1]+","+U[2]+")",bypass:n}:null}if(d.regex||d.regexes){if(d.enums){var K=A();if(K)return K}for(var X=d.regexes?d.regexes:[d.regex],H=0;H<X.length;H++){var W=new RegExp(X[H]).exec(t);if(W)return{name:e,value:d.singleRegexMatchValue?W[1]:W,strValue:""+t,bypass:n}}return null}return d.string?{name:e,value:""+t,strValue:""+t,bypass:n}:d.enums?A():null};var Ge=function e(t){if(!(this instanceof e))return new e(t);ni(t)?(this._private={cy:t,coreStyle:{}},this.length=0,this.resetToDefault()):De("A style must have a core reference")},We=Ge.prototype;We.instanceString=function(){return"style"},We.clear=function(){for(var e=this._private,t=e.cy.elements(),n=0;n<this.length;n++)this[n]=void 0;return this.length=0,e.contextStyles={},e.propDiffs={},this.cleanElements(t,!0),t.forEach(function(r){var a=r[0]._private;a.styleDirty=!0,a.appliedInitStyle=!1}),this},We.resetToDefault=function(){return this.clear(),this.addDefaultStylesheet(),this},We.core=function(e){return this._private.coreStyle[e]||this.getDefaultProperty(e)},We.selector=function(e){var t=e==="core"?null:new jt(e),n=this.length++;return this[n]={selector:t,properties:[],mappedProperties:[],index:n},this},We.css=function(){var e=arguments;if(e.length===1)for(var t=e[0],n=0;n<this.properties.length;n++){var r=this.properties[n],a=t[r.name];a===void 0&&(a=t[aa(r.name)]),a!==void 0&&this.cssRule(r.name,a)}else e.length===2&&this.cssRule(e[0],e[1]);return this},We.style=We.css,We.cssRule=function(e,t){var n=this.parse(e,t);if(n){var r=this.length-1;this[r].properties.push(n),this[r].properties[n.name]=n,n.name.match(/pie-(\d+)-background-size/)&&n.value&&(this._private.hasPie=!0),n.mapped&&this[r].mappedProperties.push(n),!this[r].selector&&(this._private.coreStyle[n.name]=n)}return this},We.append=function(e){return is(e)?e.appendToStyle(this):Te(e)?this.appendFromJson(e):ce(e)&&this.appendFromString(e),this},Ge.fromJson=function(e,t){var n=new Ge(e);return n.fromJson(t),n},Ge.fromString=function(e,t){return new Ge(e).fromString(t)},[He,jd,Wd,ja,Kd,Hd,Xe,el].forEach(function(e){he(We,e)}),Ge.types=We.types,Ge.properties=We.properties,Ge.propertyGroups=We.propertyGroups,Ge.propertyGroupNames=We.propertyGroupNames,Ge.propertyGroupKeys=We.propertyGroupKeys;var Ud={style:function(e){return e&&this.setStyle(e).update(),this._private.style},setStyle:function(e){var t=this._private;return is(e)?t.style=e.generateStyle(this):Te(e)?t.style=Ge.fromJson(this,e):ce(e)?t.style=Ge.fromString(this,e):t.style=Ge(this),t.style},updateStyle:function(){this.mutableElements().updateStyle()}},Jt={autolock:function(e){return e===void 0?this._private.autolock:(this._private.autolock=!!e,this)},autoungrabify:function(e){return e===void 0?this._private.autoungrabify:(this._private.autoungrabify=!!e,this)},autounselectify:function(e){return e===void 0?this._private.autounselectify:(this._private.autounselectify=!!e,this)},selectionType:function(e){var t=this._private;return t.selectionType==null&&(t.selectionType="single"),e===void 0?t.selectionType:(e!=="additive"&&e!=="single"||(t.selectionType=e),this)},panningEnabled:function(e){return e===void 0?this._private.panningEnabled:(this._private.panningEnabled=!!e,this)},userPanningEnabled:function(e){return e===void 0?this._private.userPanningEnabled:(this._private.userPanningEnabled=!!e,this)},zoomingEnabled:function(e){return e===void 0?this._private.zoomingEnabled:(this._private.zoomingEnabled=!!e,this)},userZoomingEnabled:function(e){return e===void 0?this._private.userZoomingEnabled:(this._private.userZoomingEnabled=!!e,this)},boxSelectionEnabled:function(e){return e===void 0?this._private.boxSelectionEnabled:(this._private.boxSelectionEnabled=!!e,this)},pan:function(){var e,t,n,r,a,i=arguments,o=this._private.pan;switch(i.length){case 0:return o;case 1:if(ce(i[0]))return o[e=i[0]];if(me(i[0])){if(!this._private.panningEnabled)return this;r=(n=i[0]).x,a=n.y,ae(r)&&(o.x=r),ae(a)&&(o.y=a),this.emit("pan viewport")}break;case 2:if(!this._private.panningEnabled)return this;t=i[1],(e=i[0])!=="x"&&e!=="y"||!ae(t)||(o[e]=t),this.emit("pan viewport")}return this.notify("viewport"),this},panBy:function(e,t){var n,r,a,i,o,s=arguments,l=this._private.pan;if(!this._private.panningEnabled)return this;switch(s.length){case 1:me(e)&&(i=(a=s[0]).x,o=a.y,ae(i)&&(l.x+=i),ae(o)&&(l.y+=o),this.emit("pan viewport"));break;case 2:r=t,(n=e)!=="x"&&n!=="y"||!ae(r)||(l[n]+=r),this.emit("pan viewport")}return this.notify("viewport"),this},gc:function(){this.notify("gc")},fit:function(e,t){var n=this.getFitViewport(e,t);if(n){var r=this._private;r.zoom=n.zoom,r.pan=n.pan,this.emit("pan zoom viewport"),this.notify("viewport")}return this},getFitViewport:function(e,t){if(ae(e)&&t===void 0&&(t=e,e=void 0),this._private.panningEnabled&&this._private.zoomingEnabled){var n,r;if(ce(e)){var a=e;e=this.$(a)}else if(me(r=e)&&ae(r.x1)&&ae(r.x2)&&ae(r.y1)&&ae(r.y2)){var i=e;(n={x1:i.x1,y1:i.y1,x2:i.x2,y2:i.y2}).w=n.x2-n.x1,n.h=n.y2-n.y1}else at(e)||(e=this.mutableElements());if(!at(e)||!e.empty()){n=n||e.boundingBox();var o,s=this.width(),l=this.height();if(t=ae(t)?t:0,!isNaN(s)&&!isNaN(l)&&s>0&&l>0&&!isNaN(n.w)&&!isNaN(n.h)&&n.w>0&&n.h>0)return{zoom:o=(o=(o=Math.min((s-2*t)/n.w,(l-2*t)/n.h))>this._private.maxZoom?this._private.maxZoom:o)<this._private.minZoom?this._private.minZoom:o,pan:{x:(s-o*(n.x1+n.x2))/2,y:(l-o*(n.y1+n.y2))/2}}}}},zoomRange:function(e,t){var n=this._private;if(t==null){var r=e;e=r.min,t=r.max}return ae(e)&&ae(t)&&e<=t?(n.minZoom=e,n.maxZoom=t):ae(e)&&t===void 0&&e<=n.maxZoom?n.minZoom=e:ae(t)&&e===void 0&&t>=n.minZoom&&(n.maxZoom=t),this},minZoom:function(e){return e===void 0?this._private.minZoom:this.zoomRange({min:e})},maxZoom:function(e){return e===void 0?this._private.maxZoom:this.zoomRange({max:e})},getZoomedViewport:function(e){var t,n,r=this._private,a=r.pan,i=r.zoom,o=!1;if(r.zoomingEnabled||(o=!0),ae(e)?n=e:me(e)&&(n=e.level,e.position!=null?t=Hr(e.position,i,a):e.renderedPosition!=null&&(t=e.renderedPosition),t==null||r.panningEnabled||(o=!0)),n=(n=n>r.maxZoom?r.maxZoom:n)<r.minZoom?r.minZoom:n,o||!ae(n)||n===i||t!=null&&(!ae(t.x)||!ae(t.y)))return null;if(t!=null){var s=a,l=i,u=n;return{zoomed:!0,panned:!0,zoom:u,pan:{x:-u/l*(t.x-s.x)+t.x,y:-u/l*(t.y-s.y)+t.y}}}return{zoomed:!0,panned:!1,zoom:n,pan:a}},zoom:function(e){if(e===void 0)return this._private.zoom;var t=this.getZoomedViewport(e),n=this._private;return t!=null&&t.zoomed?(n.zoom=t.zoom,t.panned&&(n.pan.x=t.pan.x,n.pan.y=t.pan.y),this.emit("zoom"+(t.panned?" pan":"")+" viewport"),this.notify("viewport"),this):this},viewport:function(e){var t=this._private,n=!0,r=!0,a=[],i=!1,o=!1;if(!e)return this;if(ae(e.zoom)||(n=!1),me(e.pan)||(r=!1),!n&&!r)return this;if(n){var s=e.zoom;s<t.minZoom||s>t.maxZoom||!t.zoomingEnabled?i=!0:(t.zoom=s,a.push("zoom"))}if(r&&(!i||!e.cancelOnFailedZoom)&&t.panningEnabled){var l=e.pan;ae(l.x)&&(t.pan.x=l.x,o=!1),ae(l.y)&&(t.pan.y=l.y,o=!1),o||a.push("pan")}return a.length>0&&(a.push("viewport"),this.emit(a.join(" ")),this.notify("viewport")),this},center:function(e){var t=this.getCenterPan(e);return t&&(this._private.pan=t,this.emit("pan viewport"),this.notify("viewport")),this},getCenterPan:function(e,t){if(this._private.panningEnabled){if(ce(e)){var n=e;e=this.mutableElements().filter(n)}else at(e)||(e=this.mutableElements());if(e.length!==0){var r=e.boundingBox(),a=this.width(),i=this.height();return{x:(a-(t=t===void 0?this._private.zoom:t)*(r.x1+r.x2))/2,y:(i-t*(r.y1+r.y2))/2}}}},reset:function(){return this._private.panningEnabled&&this._private.zoomingEnabled?(this.viewport({pan:{x:0,y:0},zoom:1}),this):this},invalidateSize:function(){this._private.sizeCache=null},size:function(){var e,t,n=this._private,r=n.container,a=this;return n.sizeCache=n.sizeCache||(r?(e=a.window().getComputedStyle(r),t=function(i){return parseFloat(e.getPropertyValue(i))},{width:r.clientWidth-t("padding-left")-t("padding-right"),height:r.clientHeight-t("padding-top")-t("padding-bottom")}):{width:1,height:1})},width:function(){return this.size().width},height:function(){return this.size().height},extent:function(){var e=this._private.pan,t=this._private.zoom,n=this.renderedExtent(),r={x1:(n.x1-e.x)/t,x2:(n.x2-e.x)/t,y1:(n.y1-e.y)/t,y2:(n.y2-e.y)/t};return r.w=r.x2-r.x1,r.h=r.y2-r.y1,r},renderedExtent:function(){var e=this.width(),t=this.height();return{x1:0,y1:0,x2:e,y2:t,w:e,h:t}},multiClickDebounceTime:function(e){return e?(this._private.multiClickDebounceTime=e,this):this._private.multiClickDebounceTime}};Jt.centre=Jt.center,Jt.autolockNodes=Jt.autolock,Jt.autoungrabifyNodes=Jt.autoungrabify;var Qn={data:xe.data({field:"data",bindingEvent:"data",allowBinding:!0,allowSetting:!0,settingEvent:"data",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0,updateStyle:!0}),removeData:xe.removeData({field:"data",event:"data",triggerFnName:"trigger",triggerEvent:!0,updateStyle:!0}),scratch:xe.data({field:"scratch",bindingEvent:"scratch",allowBinding:!0,allowSetting:!0,settingEvent:"scratch",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0,updateStyle:!0}),removeScratch:xe.removeData({field:"scratch",event:"scratch",triggerFnName:"trigger",triggerEvent:!0,updateStyle:!0})};Qn.attr=Qn.data,Qn.removeAttr=Qn.removeData;var hr=function(e){var t=this,n=(e=he({},e)).container;n&&!jr(n)&&jr(n[0])&&(n=n[0]);var r=n?n._cyreg:null;(r=r||{})&&r.cy&&(r.cy.destroy(),r={});var a=r.readies=r.readies||[];n&&(n._cyreg=r),r.cy=t;var i=Re!==void 0&&n!==void 0&&!e.headless,o=e;o.layout=he({name:i?"grid":"null"},o.layout),o.renderer=he({name:i?"canvas":"null"},o.renderer);var s=function(c,d,h){return d!==void 0?d:h!==void 0?h:c},l=this._private={container:n,ready:!1,options:o,elements:new je(this),listeners:[],aniEles:new je(this),data:o.data||{},scratch:{},layout:null,renderer:null,destroyed:!1,notificationsEnabled:!0,minZoom:1e-50,maxZoom:1e50,zoomingEnabled:s(!0,o.zoomingEnabled),userZoomingEnabled:s(!0,o.userZoomingEnabled),panningEnabled:s(!0,o.panningEnabled),userPanningEnabled:s(!0,o.userPanningEnabled),boxSelectionEnabled:s(!0,o.boxSelectionEnabled),autolock:s(!1,o.autolock,o.autolockNodes),autoungrabify:s(!1,o.autoungrabify,o.autoungrabifyNodes),autounselectify:s(!1,o.autounselectify),styleEnabled:o.styleEnabled===void 0?i:o.styleEnabled,zoom:ae(o.zoom)?o.zoom:1,pan:{x:me(o.pan)&&ae(o.pan.x)?o.pan.x:0,y:me(o.pan)&&ae(o.pan.y)?o.pan.y:0},animation:{current:[],queue:[]},hasCompoundNodes:!1,multiClickDebounceTime:s(250,o.multiClickDebounceTime)};this.createEmitter(),this.selectionType(o.selectionType),this.zoomRange({min:o.minZoom,max:o.maxZoom}),l.styleEnabled&&t.setStyle([]);var u=he({},o,o.renderer);t.initRenderer(u),function(c,d){if(c.some(Ol))return Mn.all(c).then(d);d(c)}([o.style,o.elements],function(c){var d=c[0],h=c[1];l.styleEnabled&&t.style().append(d),function(p,f,v){t.notifications(!1);var m=t.mutableElements();m.length>0&&m.remove(),p!=null&&(me(p)||Te(p))&&t.add(p),t.one("layoutready",function(y){t.notifications(!0),t.emit(y),t.one("load",f),t.emitAndNotify("load")}).one("layoutstop",function(){t.one("done",v),t.emit("done")});var g=he({},t._private.options.layout);g.eles=t.elements(),t.layout(g).run()}(h,function(){t.startAnimationLoop(),l.ready=!0,_e(o.ready)&&t.on("ready",o.ready);for(var p=0;p<a.length;p++){var f=a[p];t.on("ready",f)}r&&(r.readies=[]),t.emit("ready")},o.done)})},Ar=hr.prototype;he(Ar,{instanceString:function(){return"core"},isReady:function(){return this._private.ready},destroyed:function(){return this._private.destroyed},ready:function(e){return this.isReady()?this.emitter().emit("ready",[],e):this.on("ready",e),this},destroy:function(){var e=this;if(!e.destroyed())return e.stopAnimationLoop(),e.destroyRenderer(),this.emit("destroy"),e._private.destroyed=!0,e},hasElementWithId:function(e){return this._private.elements.hasElementWithId(e)},getElementById:function(e){return this._private.elements.getElementById(e)},hasCompoundNodes:function(){return this._private.hasCompoundNodes},headless:function(){return this._private.renderer.isHeadless()},styleEnabled:function(){return this._private.styleEnabled},addToPool:function(e){return this._private.elements.merge(e),this},removeFromPool:function(e){return this._private.elements.unmerge(e),this},container:function(){return this._private.container||null},window:function(){if(this._private.container==null)return Re;var e=this._private.container.ownerDocument;return e===void 0||e==null?Re:e.defaultView||Re},mount:function(e){if(e!=null){var t=this,n=t._private,r=n.options;return!jr(e)&&jr(e[0])&&(e=e[0]),t.stopAnimationLoop(),t.destroyRenderer(),n.container=e,n.styleEnabled=!0,t.invalidateSize(),t.initRenderer(he({},r,r.renderer,{name:r.renderer.name==="null"?"canvas":r.renderer.name})),t.startAnimationLoop(),t.style(r.style),t.emit("mount"),t}},unmount:function(){var e=this;return e.stopAnimationLoop(),e.destroyRenderer(),e.initRenderer({name:"null"}),e.emit("unmount"),e},options:function(){return xt(this._private.options)},json:function(e){var t=this,n=t._private,r=t.mutableElements();if(me(e)){if(t.startBatch(),e.elements){var a={},i=function(m,g){for(var y=[],b=[],k=0;k<m.length;k++){var x=m[k];if(x.data.id){var w=""+x.data.id,E=t.getElementById(w);a[w]=!0,E.length!==0?b.push({ele:E,json:x}):(g&&(x.group=g),y.push(x))}else we("cy.json() cannot handle elements without an ID attribute")}t.add(y);for(var C=0;C<b.length;C++){var S=b[C],P=S.ele,_=S.json;P.json(_)}};if(Te(e.elements))i(e.elements);else for(var o=["nodes","edges"],s=0;s<o.length;s++){var l=o[s],u=e.elements[l];Te(u)&&i(u,l)}var c=t.collection();r.filter(function(m){return!a[m.id()]}).forEach(function(m){m.isParent()?c.merge(m):m.remove()}),c.forEach(function(m){return m.children().move({parent:null})}),c.forEach(function(m){return function(g){return t.getElementById(g.id())}(m).remove()})}e.style&&t.style(e.style),e.zoom!=null&&e.zoom!==n.zoom&&t.zoom(e.zoom),e.pan&&(e.pan.x===n.pan.x&&e.pan.y===n.pan.y||t.pan(e.pan)),e.data&&t.data(e.data);for(var d=["minZoom","maxZoom","zoomingEnabled","userZoomingEnabled","panningEnabled","userPanningEnabled","boxSelectionEnabled","autolock","autoungrabify","autounselectify","multiClickDebounceTime"],h=0;h<d.length;h++){var p=d[h];e[p]!=null&&t[p](e[p])}return t.endBatch(),this}var f={};e?f.elements=this.elements().map(function(m){return m.json()}):(f.elements={},r.forEach(function(m){var g=m.group();f.elements[g]||(f.elements[g]=[]),f.elements[g].push(m.json())})),this._private.styleEnabled&&(f.style=t.style().json()),f.data=xt(t.data());var v=n.options;return f.zoomingEnabled=n.zoomingEnabled,f.userZoomingEnabled=n.userZoomingEnabled,f.zoom=n.zoom,f.minZoom=n.minZoom,f.maxZoom=n.maxZoom,f.panningEnabled=n.panningEnabled,f.userPanningEnabled=n.userPanningEnabled,f.pan=xt(n.pan),f.boxSelectionEnabled=n.boxSelectionEnabled,f.renderer=xt(v.renderer),f.hideEdgesOnViewport=v.hideEdgesOnViewport,f.textureOnViewport=v.textureOnViewport,f.wheelSensitivity=v.wheelSensitivity,f.motionBlur=v.motionBlur,f.multiClickDebounceTime=v.multiClickDebounceTime,f}}),Ar.$id=Ar.getElementById,[Nd,Fd,Js,Xa,Fr,Xd,Ya,qr,Ud,Jt,Qn].forEach(function(e){he(Ar,e)});var Gd={fit:!0,directed:!1,padding:30,circle:!1,grid:!1,spacingFactor:1.75,boundingBox:void 0,avoidOverlap:!0,nodeDimensionsIncludeLabels:!1,roots:void 0,depthSort:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}},Zd={maximal:!1,acyclic:!1},dn=function(e){return e.scratch("breadthfirst")},So=function(e,t){return e.scratch("breadthfirst",t)};function tl(e){this.options=he({},Gd,Zd,e)}tl.prototype.run=function(){var e,t=this.options,n=t.cy,r=t.eles,a=r.nodes().filter(function($){return $.isChildless()}),i=r,o=t.directed,s=t.acyclic||t.maximal||t.maximalAdjustments>0,l=!!t.boundingBox,u=n.extent(),c=tt(l?t.boundingBox:{x1:u.x1,y1:u.y1,w:u.w,h:u.h});if(at(t.roots))e=t.roots;else if(Te(t.roots)){for(var d=[],h=0;h<t.roots.length;h++){var p=t.roots[h],f=n.getElementById(p);d.push(f)}e=n.collection(d)}else if(ce(t.roots))e=n.$(t.roots);else if(o)e=a.roots();else{var v=r.components();e=n.collection();for(var m=function($){var ne=v[$],T=ne.maxDegree(!1),R=ne.filter(function(L){return L.degree(!1)===T});e=e.add(R)},g=0;g<v.length;g++)m(g)}var y=[],b={},k=function($,ne){y[ne]==null&&(y[ne]=[]);var T=y[ne].length;y[ne].push($),So($,{index:T,depth:ne})};i.bfs({roots:e,directed:t.directed,visit:function($,ne,T,R,L){var Y=$[0],F=Y.id();Y.isChildless()&&k(Y,L),b[F]=!0}});for(var x=[],w=0;w<a.length;w++){var E=a[w];b[E.id()]||x.push(E)}var C=function($){for(var ne=y[$],T=0;T<ne.length;T++){var R=ne[T];R!=null?So(R,{depth:$,index:T}):(ne.splice(T,1),T--)}},S=function($,ne){for(var T=dn($),R=$.incomers().filter(function(ee){return ee.isNode()&&r.has(ee)}),L=-1,Y=$.id(),F=0;F<R.length;F++){var J=R[F],G=dn(J);L=Math.max(L,G.depth)}if(T.depth<=L){if(!t.acyclic&&ne[Y])return null;var Z=L+1;return function(ee,oe){var ue=dn(ee),re=ue.depth,de=ue.index;y[re][de]=null,ee.isChildless()&&k(ee,oe)}($,Z),ne[Y]=Z,!0}return!1};if(o&&s){var P=[],_={},A=function($){return P.push($)};for(a.forEach(function($){return P.push($)});P.length>0;){var B=P.shift(),O=S(B,_);if(O)B.outgoers().filter(function($){return $.isNode()&&r.has($)}).forEach(A);else if(O===null){we("Detected double maximal shift for node `"+B.id()+"`.  Bailing maximal adjustment due to cycle.  Use `options.maximal: true` only on DAGs.");break}}}var N=0;if(t.avoidOverlap)for(var V=0;V<a.length;V++){var M=a[V].layoutDimensions(t),D=M.w,I=M.h;N=Math.max(N,D,I)}var z={},q=function($){if(z[$.id()])return z[$.id()];for(var ne=dn($).depth,T=$.neighborhood(),R=0,L=0,Y=0;Y<T.length;Y++){var F=T[Y];if(!F.isEdge()&&!F.isParent()&&a.has(F)){var J=dn(F);if(J!=null){var G=J.index,Z=J.depth;if(G!=null&&Z!=null){var ee=y[Z].length;Z<ne&&(R+=G/ee,L++)}}}}return R/=L=Math.max(1,L),L===0&&(R=0),z[$.id()]=R,R},j=function($,ne){var T=q($)-q(ne);return T===0?ss($.id(),ne.id()):T};t.depthSort!==void 0&&(j=t.depthSort);for(var U=y.length,K=0;K<U;K++)y[K].sort(j),C(K);for(var X=[],H=0;H<x.length;H++)X.push(x[H]);X.length&&(y.unshift(X),U=y.length,function(){for(var $=0;$<U;$++)C($)}());for(var W=0,Q=0;Q<U;Q++)W=Math.max(y[Q].length,W);var te=c.x1+c.w/2,ie=c.y1+c.h/2,se=a.reduce(function($,ne){return T=ne.boundingBox({includeLabels:t.nodeDimensionsIncludeLabels}),{w:$.w===-1?T.w:($.w+T.w)/2,h:$.h===-1?T.h:($.h+T.h)/2};var T},{w:-1,h:-1}),le=Math.max(U===1?0:l?(c.h-2*t.padding-se.h)/(U-1):(c.h-2*t.padding-se.h)/(U+1),N),fe=y.reduce(function($,ne){return Math.max($,ne.length)},0);return r.nodes().layoutPositions(this,t,function($){var ne=dn($),T=ne.depth,R=ne.index;if(t.circle){var L=Math.min(c.w/2/U,c.h/2/U),Y=(L=Math.max(L,N))*T+L-(U>0&&y[0].length<=3?L/2:0),F=2*Math.PI/y[T].length*R;return T===0&&y[0].length===1&&(Y=1),{x:te+Y*Math.cos(F),y:ie+Y*Math.sin(F)}}var J=y[T].length,G=Math.max(J===1?0:l?(c.w-2*t.padding-se.w)/((t.grid?fe:J)-1):(c.w-2*t.padding-se.w)/((t.grid?fe:J)+1),N);return{x:te+(R+1-(J+1)/2)*G,y:ie+(T+1-(U+1)/2)*le}}),this};var $d={fit:!0,padding:30,boundingBox:void 0,avoidOverlap:!0,nodeDimensionsIncludeLabels:!1,spacingFactor:void 0,radius:void 0,startAngle:1.5*Math.PI,sweep:void 0,clockwise:!0,sort:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}};function nl(e){this.options=he({},$d,e)}nl.prototype.run=function(){var e=this.options,t=e,n=e.cy,r=t.eles,a=t.counterclockwise!==void 0?!t.counterclockwise:t.clockwise,i=r.nodes().not(":parent");t.sort&&(i=i.sort(t.sort));for(var o,s=tt(t.boundingBox?t.boundingBox:{x1:0,y1:0,w:n.width(),h:n.height()}),l=s.x1+s.w/2,u=s.y1+s.h/2,c=(t.sweep===void 0?2*Math.PI-2*Math.PI/i.length:t.sweep)/Math.max(1,i.length-1),d=0,h=0;h<i.length;h++){var p=i[h].layoutDimensions(t),f=p.w,v=p.h;d=Math.max(d,f,v)}if(o=ae(t.radius)?t.radius:i.length<=1?0:Math.min(s.h,s.w)/2-d,i.length>1&&t.avoidOverlap){d*=1.75;var m=Math.cos(c)-Math.cos(0),g=Math.sin(c)-Math.sin(0),y=Math.sqrt(d*d/(m*m+g*g));o=Math.max(y,o)}return r.nodes().layoutPositions(this,t,function(b,k){var x=t.startAngle+k*c*(a?1:-1),w=o*Math.cos(x),E=o*Math.sin(x);return{x:l+w,y:u+E}}),this};var Bo,Qd={fit:!0,padding:30,startAngle:1.5*Math.PI,sweep:void 0,clockwise:!0,equidistant:!1,minNodeSpacing:10,boundingBox:void 0,avoidOverlap:!0,nodeDimensionsIncludeLabels:!1,height:void 0,width:void 0,spacingFactor:void 0,concentric:function(e){return e.degree()},levelWidth:function(e){return e.maxDegree()/4},animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}};function rl(e){this.options=he({},Qd,e)}rl.prototype.run=function(){for(var e=this.options,t=e,n=t.counterclockwise!==void 0?!t.counterclockwise:t.clockwise,r=e.cy,a=t.eles,i=a.nodes().not(":parent"),o=tt(t.boundingBox?t.boundingBox:{x1:0,y1:0,w:r.width(),h:r.height()}),s=o.x1+o.w/2,l=o.y1+o.h/2,u=[],c=0,d=0;d<i.length;d++){var h,p=i[d];h=t.concentric(p),u.push({value:h,node:p}),p._private.scratch.concentric=h}i.updateStyle();for(var f=0;f<i.length;f++){var v=i[f].layoutDimensions(t);c=Math.max(c,v.w,v.h)}u.sort(function(se,le){return le.value-se.value});for(var m=t.levelWidth(i),g=[[]],y=g[0],b=0;b<u.length;b++){var k=u[b];y.length>0&&Math.abs(y[0].value-k.value)>=m&&(y=[],g.push(y)),y.push(k)}var x=c+t.minNodeSpacing;if(!t.avoidOverlap){var w=g.length>0&&g[0].length>1,E=(Math.min(o.w,o.h)/2-x)/(g.length+w?1:0);x=Math.min(x,E)}for(var C=0,S=0;S<g.length;S++){var P=g[S],_=t.sweep===void 0?2*Math.PI-2*Math.PI/P.length:t.sweep,A=P.dTheta=_/Math.max(1,P.length-1);if(P.length>1&&t.avoidOverlap){var B=Math.cos(A)-Math.cos(0),O=Math.sin(A)-Math.sin(0),N=Math.sqrt(x*x/(B*B+O*O));C=Math.max(N,C)}P.r=C,C+=x}if(t.equidistant){for(var V=0,M=0,D=0;D<g.length;D++){var I=g[D].r-M;V=Math.max(V,I)}M=0;for(var z=0;z<g.length;z++){var q=g[z];z===0&&(M=q.r),q.r=M,M+=V}}for(var j={},U=0;U<g.length;U++)for(var K=g[U],X=K.dTheta,H=K.r,W=0;W<K.length;W++){var Q=K[W],te=t.startAngle+(n?1:-1)*X*W,ie={x:s+H*Math.cos(te),y:l+H*Math.sin(te)};j[Q.node.id()]=ie}return a.nodes().layoutPositions(this,t,function(se){var le=se.id();return j[le]}),this};var Jd={ready:function(){},stop:function(){},animate:!0,animationEasing:void 0,animationDuration:void 0,animateFilter:function(e,t){return!0},animationThreshold:250,refresh:20,fit:!0,padding:30,boundingBox:void 0,nodeDimensionsIncludeLabels:!1,randomize:!1,componentSpacing:40,nodeRepulsion:function(e){return 2048},nodeOverlap:4,idealEdgeLength:function(e){return 32},edgeElasticity:function(e){return 32},nestingFactor:1.2,gravity:1,numIter:1e3,initialTemp:1e3,coolingFactor:.99,minTemp:1};function Xr(e){this.options=he({},Jd,e),this.options.layout=this;var t=this.options.eles.nodes(),n=this.options.eles.edges().filter(function(r){var a=r.source().data("id"),i=r.target().data("id"),o=t.some(function(l){return l.data("id")===a}),s=t.some(function(l){return l.data("id")===i});return!o||!s});this.options.eles=this.options.eles.not(n)}Xr.prototype.run=function(){var e=this.options,t=e.cy,n=this;n.stopped=!1,e.animate!==!0&&e.animate!==!1||n.emit({type:"layoutstart",layout:n}),Bo=e.debug===!0;var r=eh(t,n,e);Bo&&(void 0)(r),e.randomize&&rh(r);var a=Bt(),i=function(){ah(r,t,e),e.fit===!0&&t.fit(e.padding)},o=function(c){return!(n.stopped||c>=e.numIter)&&(ih(r,e),r.temperature=r.temperature*e.coolingFactor,!(r.temperature<e.minTemp))},s=function(){if(e.animate===!0||e.animate===!1)i(),n.one("layoutstop",e.stop),n.emit({type:"layoutstop",layout:n});else{var c=e.eles.nodes(),d=al(r,e,c);c.layoutPositions(n,e,d)}},l=0,u=!0;if(e.animate===!0)(function c(){for(var d=0;u&&d<e.refresh;)u=o(l),l++,d++;u?(Bt()-a>=e.animationThreshold&&i(),Wr(c)):(_o(r,e),s())})();else{for(;u;)u=o(l),l++;_o(r,e),s()}return this},Xr.prototype.stop=function(){return this.stopped=!0,this.thread&&this.thread.stop(),this.emit("layoutstop"),this},Xr.prototype.destroy=function(){return this.thread&&this.thread.stop(),this};var eh=function(e,t,n){for(var r=n.eles.edges(),a=n.eles.nodes(),i=tt(n.boundingBox?n.boundingBox:{x1:0,y1:0,w:e.width(),h:e.height()}),o={isCompound:e.hasCompoundNodes(),layoutNodes:[],idToIndex:{},nodeSize:a.size(),graphSet:[],indexToGraph:[],layoutEdges:[],edgeSize:r.size(),temperature:n.initialTemp,clientWidth:i.w,clientHeight:i.h,boundingBox:i},s=n.eles.components(),l={},u=0;u<s.length;u++)for(var c=s[u],d=0;d<c.length;d++)l[c[d].id()]=u;for(u=0;u<o.nodeSize;u++){var h=(g=a[u]).layoutDimensions(n);(M={}).isLocked=g.locked(),M.id=g.data("id"),M.parentId=g.data("parent"),M.cmptId=l[g.id()],M.children=[],M.positionX=g.position("x"),M.positionY=g.position("y"),M.offsetX=0,M.offsetY=0,M.height=h.w,M.width=h.h,M.maxX=M.positionX+M.width/2,M.minX=M.positionX-M.width/2,M.maxY=M.positionY+M.height/2,M.minY=M.positionY-M.height/2,M.padLeft=parseFloat(g.style("padding")),M.padRight=parseFloat(g.style("padding")),M.padTop=parseFloat(g.style("padding")),M.padBottom=parseFloat(g.style("padding")),M.nodeRepulsion=_e(n.nodeRepulsion)?n.nodeRepulsion(g):n.nodeRepulsion,o.layoutNodes.push(M),o.idToIndex[M.id]=u}var p=[],f=0,v=-1,m=[];for(u=0;u<o.nodeSize;u++){var g,y=(g=o.layoutNodes[u]).parentId;y!=null?o.layoutNodes[o.idToIndex[y]].children.push(g.id):(p[++v]=g.id,m.push(g.id))}for(o.graphSet.push(m);f<=v;){var b=p[f++],k=o.idToIndex[b],x=o.layoutNodes[k].children;if(x.length>0)for(o.graphSet.push(x),u=0;u<x.length;u++)p[++v]=x[u]}for(u=0;u<o.graphSet.length;u++){var w=o.graphSet[u];for(d=0;d<w.length;d++){var E=o.idToIndex[w[d]];o.indexToGraph[E]=u}}for(u=0;u<o.edgeSize;u++){var C=r[u],S={};S.id=C.data("id"),S.sourceId=C.data("source"),S.targetId=C.data("target");var P=_e(n.idealEdgeLength)?n.idealEdgeLength(C):n.idealEdgeLength,_=_e(n.edgeElasticity)?n.edgeElasticity(C):n.edgeElasticity,A=o.idToIndex[S.sourceId],B=o.idToIndex[S.targetId];if(o.indexToGraph[A]!=o.indexToGraph[B]){for(var O=th(S.sourceId,S.targetId,o),N=o.graphSet[O],V=0,M=o.layoutNodes[A];N.indexOf(M.id)===-1;)M=o.layoutNodes[o.idToIndex[M.parentId]],V++;for(M=o.layoutNodes[B];N.indexOf(M.id)===-1;)M=o.layoutNodes[o.idToIndex[M.parentId]],V++;P*=V*n.nestingFactor}S.idealLength=P,S.elasticity=_,o.layoutEdges.push(S)}return o},th=function(e,t,n){var r=nh(e,t,0,n);return 2>r.count?0:r.graph},nh=function e(t,n,r,a){var i=a.graphSet[r];if(-1<i.indexOf(t)&&-1<i.indexOf(n))return{count:2,graph:r};for(var o=0,s=0;s<i.length;s++){var l=i[s],u=a.idToIndex[l],c=a.layoutNodes[u].children;if(c.length!==0){var d=e(t,n,a.indexToGraph[a.idToIndex[c[0]]],a);if(d.count!==0){if(d.count!==1)return d;if(++o===2)break}}}return{count:o,graph:r}},rh=function(e,t){for(var n=e.clientWidth,r=e.clientHeight,a=0;a<e.nodeSize;a++){var i=e.layoutNodes[a];i.children.length!==0||i.isLocked||(i.positionX=Math.random()*n,i.positionY=Math.random()*r)}},al=function(e,t,n){var r=e.boundingBox,a={x1:1/0,x2:-1/0,y1:1/0,y2:-1/0};return t.boundingBox&&(n.forEach(function(i){var o=e.layoutNodes[e.idToIndex[i.data("id")]];a.x1=Math.min(a.x1,o.positionX),a.x2=Math.max(a.x2,o.positionX),a.y1=Math.min(a.y1,o.positionY),a.y2=Math.max(a.y2,o.positionY)}),a.w=a.x2-a.x1,a.h=a.y2-a.y1),function(i,o){var s=e.layoutNodes[e.idToIndex[i.data("id")]];if(t.boundingBox){var l=(s.positionX-a.x1)/a.w,u=(s.positionY-a.y1)/a.h;return{x:r.x1+l*r.w,y:r.y1+u*r.h}}return{x:s.positionX,y:s.positionY}}},ah=function(e,t,n){var r=n.layout,a=n.eles.nodes(),i=al(e,n,a);a.positions(i),e.ready!==!0&&(e.ready=!0,r.one("layoutready",n.ready),r.emit({type:"layoutready",layout:this}))},ih=function(e,t,n){oh(e,t),uh(e),ch(e,t),dh(e),hh(e)},oh=function(e,t){for(var n=0;n<e.graphSet.length;n++)for(var r=e.graphSet[n],a=r.length,i=0;i<a;i++)for(var o=e.layoutNodes[e.idToIndex[r[i]]],s=i+1;s<a;s++){var l=e.layoutNodes[e.idToIndex[r[s]]];sh(o,l,e,t)}},Do=function(e){return-e+2*e*Math.random()},sh=function(e,t,n,r){if(e.cmptId===t.cmptId||n.isCompound){var a=t.positionX-e.positionX,i=t.positionY-e.positionY;a===0&&i===0&&(a=Do(1),i=Do(1));var o=lh(e,t,a,i);if(o>0)var s=(u=r.nodeOverlap*o)*a/(v=Math.sqrt(a*a+i*i)),l=u*i/v;else{var u,c=na(e,a,i),d=na(t,-1*a,-1*i),h=d.x-c.x,p=d.y-c.y,f=h*h+p*p,v=Math.sqrt(f);s=(u=(e.nodeRepulsion+t.nodeRepulsion)/f)*h/v,l=u*p/v}e.isLocked||(e.offsetX-=s,e.offsetY-=l),t.isLocked||(t.offsetX+=s,t.offsetY+=l)}},lh=function(e,t,n,r){if(n>0)var a=e.maxX-t.minX;else a=t.maxX-e.minX;if(r>0)var i=e.maxY-t.minY;else i=t.maxY-e.minY;return a>=0&&i>=0?Math.sqrt(a*a+i*i):0},na=function(e,t,n){var r=e.positionX,a=e.positionY,i=e.height||1,o=e.width||1,s=n/t,l=i/o,u={};return t===0&&0<n||t===0&&0>n?(u.x=r,u.y=a+i/2,u):0<t&&-1*l<=s&&s<=l?(u.x=r+o/2,u.y=a+o*n/2/t,u):0>t&&-1*l<=s&&s<=l?(u.x=r-o/2,u.y=a-o*n/2/t,u):0<n&&(s<=-1*l||s>=l)?(u.x=r+i*t/2/n,u.y=a+i/2,u):(0>n&&(s<=-1*l||s>=l)&&(u.x=r-i*t/2/n,u.y=a-i/2),u)},uh=function(e,t){for(var n=0;n<e.edgeSize;n++){var r=e.layoutEdges[n],a=e.idToIndex[r.sourceId],i=e.layoutNodes[a],o=e.idToIndex[r.targetId],s=e.layoutNodes[o],l=s.positionX-i.positionX,u=s.positionY-i.positionY;if(l!==0||u!==0){var c=na(i,l,u),d=na(s,-1*l,-1*u),h=d.x-c.x,p=d.y-c.y,f=Math.sqrt(h*h+p*p),v=Math.pow(r.idealLength-f,2)/r.elasticity;if(f!==0)var m=v*h/f,g=v*p/f;else m=0,g=0;i.isLocked||(i.offsetX+=m,i.offsetY+=g),s.isLocked||(s.offsetX-=m,s.offsetY-=g)}}},ch=function(e,t){if(t.gravity!==0)for(var n=0;n<e.graphSet.length;n++){var r=e.graphSet[n],a=r.length;if(n===0)var i=e.clientHeight/2,o=e.clientWidth/2;else{var s=e.layoutNodes[e.idToIndex[r[0]]],l=e.layoutNodes[e.idToIndex[s.parentId]];i=l.positionX,o=l.positionY}for(var u=0;u<a;u++){var c=e.layoutNodes[e.idToIndex[r[u]]];if(!c.isLocked){var d=i-c.positionX,h=o-c.positionY,p=Math.sqrt(d*d+h*h);if(p>1){var f=t.gravity*d/p,v=t.gravity*h/p;c.offsetX+=f,c.offsetY+=v}}}}},dh=function(e,t){var n=[],r=0,a=-1;for(n.push.apply(n,e.graphSet[0]),a+=e.graphSet[0].length;r<=a;){var i=n[r++],o=e.idToIndex[i],s=e.layoutNodes[o],l=s.children;if(0<l.length&&!s.isLocked){for(var u=s.offsetX,c=s.offsetY,d=0;d<l.length;d++){var h=e.layoutNodes[e.idToIndex[l[d]]];h.offsetX+=u,h.offsetY+=c,n[++a]=l[d]}s.offsetX=0,s.offsetY=0}}},hh=function(e,t){for(var n=0;n<e.nodeSize;n++)0<(a=e.layoutNodes[n]).children.length&&(a.maxX=void 0,a.minX=void 0,a.maxY=void 0,a.minY=void 0);for(n=0;n<e.nodeSize;n++)if(!(0<(a=e.layoutNodes[n]).children.length||a.isLocked)){var r=fh(a.offsetX,a.offsetY,e.temperature);a.positionX+=r.x,a.positionY+=r.y,a.offsetX=0,a.offsetY=0,a.minX=a.positionX-a.width,a.maxX=a.positionX+a.width,a.minY=a.positionY-a.height,a.maxY=a.positionY+a.height,ph(a,e)}for(n=0;n<e.nodeSize;n++){var a;0<(a=e.layoutNodes[n]).children.length&&!a.isLocked&&(a.positionX=(a.maxX+a.minX)/2,a.positionY=(a.maxY+a.minY)/2,a.width=a.maxX-a.minX,a.height=a.maxY-a.minY)}},fh=function(e,t,n){var r=Math.sqrt(e*e+t*t);if(r>n)var a={x:n*e/r,y:n*t/r};else a={x:e,y:t};return a},ph=function e(t,n){var r=t.parentId;if(r!=null){var a=n.layoutNodes[n.idToIndex[r]],i=!1;return(a.maxX==null||t.maxX+a.padRight>a.maxX)&&(a.maxX=t.maxX+a.padRight,i=!0),(a.minX==null||t.minX-a.padLeft<a.minX)&&(a.minX=t.minX-a.padLeft,i=!0),(a.maxY==null||t.maxY+a.padBottom>a.maxY)&&(a.maxY=t.maxY+a.padBottom,i=!0),(a.minY==null||t.minY-a.padTop<a.minY)&&(a.minY=t.minY-a.padTop,i=!0),i?e(a,n):void 0}},_o=function(e,t){for(var n=e.layoutNodes,r=[],a=0;a<n.length;a++){var i=n[a],o=i.cmptId;(r[o]=r[o]||[]).push(i)}var s=0;for(a=0;a<r.length;a++)if(v=r[a]){v.x1=1/0,v.x2=-1/0,v.y1=1/0,v.y2=-1/0;for(var l=0;l<v.length;l++){var u=v[l];v.x1=Math.min(v.x1,u.positionX-u.width/2),v.x2=Math.max(v.x2,u.positionX+u.width/2),v.y1=Math.min(v.y1,u.positionY-u.height/2),v.y2=Math.max(v.y2,u.positionY+u.height/2)}v.w=v.x2-v.x1,v.h=v.y2-v.y1,s+=v.w*v.h}r.sort(function(m,g){return g.w*g.h-m.w*m.h});var c=0,d=0,h=0,p=0,f=Math.sqrt(s)*e.clientWidth/e.clientHeight;for(a=0;a<r.length;a++){var v;if(v=r[a]){for(l=0;l<v.length;l++)(u=v[l]).isLocked||(u.positionX+=c-v.x1,u.positionY+=d-v.y1);c+=v.w+t.componentSpacing,h+=v.w+t.componentSpacing,p=Math.max(p,v.h),h>f&&(d+=p+t.componentSpacing,c=0,h=0,p=0)}}},gh={fit:!0,padding:30,boundingBox:void 0,avoidOverlap:!0,avoidOverlapPadding:10,nodeDimensionsIncludeLabels:!1,spacingFactor:void 0,condense:!1,rows:void 0,cols:void 0,position:function(e){},sort:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}};function il(e){this.options=he({},gh,e)}il.prototype.run=function(){var e=this.options,t=e,n=e.cy,r=t.eles,a=r.nodes().not(":parent");t.sort&&(a=a.sort(t.sort));var i=tt(t.boundingBox?t.boundingBox:{x1:0,y1:0,w:n.width(),h:n.height()});if(i.h===0||i.w===0)r.nodes().layoutPositions(this,t,function(j){return{x:i.x1,y:i.y1}});else{var o=a.size(),s=Math.sqrt(o*i.h/i.w),l=Math.round(s),u=Math.round(i.w/i.h*s),c=function(j){if(j==null)return Math.min(l,u);Math.min(l,u)==l?l=j:u=j},d=function(j){if(j==null)return Math.max(l,u);Math.max(l,u)==l?l=j:u=j},h=t.rows,p=t.cols!=null?t.cols:t.columns;if(h!=null&&p!=null)l=h,u=p;else if(h!=null&&p==null)l=h,u=Math.ceil(o/l);else if(h==null&&p!=null)u=p,l=Math.ceil(o/u);else if(u*l>o){var f=c(),v=d();(f-1)*v>=o?c(f-1):(v-1)*f>=o&&d(v-1)}else for(;u*l<o;){var m=c(),g=d();(g+1)*m>=o?d(g+1):c(m+1)}var y=i.w/u,b=i.h/l;if(t.condense&&(y=0,b=0),t.avoidOverlap)for(var k=0;k<a.length;k++){var x=a[k],w=x._private.position;w.x!=null&&w.y!=null||(w.x=0,w.y=0);var E=x.layoutDimensions(t),C=t.avoidOverlapPadding,S=E.w+C,P=E.h+C;y=Math.max(y,S),b=Math.max(b,P)}for(var _={},A=function(j,U){return!!_["c-"+j+"-"+U]},B=function(j,U){_["c-"+j+"-"+U]=!0},O=0,N=0,V=function(){++N>=u&&(N=0,O++)},M={},D=0;D<a.length;D++){var I=a[D],z=t.position(I);if(z&&(z.row!==void 0||z.col!==void 0)){var q={row:z.row,col:z.col};if(q.col===void 0)for(q.col=0;A(q.row,q.col);)q.col++;else if(q.row===void 0)for(q.row=0;A(q.row,q.col);)q.row++;M[I.id()]=q,B(q.row,q.col)}}a.layoutPositions(this,t,function(j,U){var K,X;if(j.locked()||j.isParent())return!1;var H=M[j.id()];if(H)K=H.col*y+y/2+i.x1,X=H.row*b+b/2+i.y1;else{for(;A(O,N);)V();K=N*y+y/2+i.x1,X=O*b+b/2+i.y1,B(O,N),V()}return{x:K,y:X}})}return this};var vh={ready:function(){},stop:function(){}};function Wa(e){this.options=he({},vh,e)}Wa.prototype.run=function(){var e=this.options,t=e.eles,n=this;return e.cy,n.emit("layoutstart"),t.nodes().positions(function(){return{x:0,y:0}}),n.one("layoutready",e.ready),n.emit("layoutready"),n.one("layoutstop",e.stop),n.emit("layoutstop"),this},Wa.prototype.stop=function(){return this};var yh={positions:void 0,zoom:void 0,pan:void 0,fit:!0,padding:30,spacingFactor:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}};function ol(e){this.options=he({},yh,e)}ol.prototype.run=function(){var e=this.options,t=e.eles.nodes(),n=_e(e.positions);return t.layoutPositions(this,e,function(r,a){var i=function(o){if(e.positions==null)return function(l){return{x:l.x,y:l.y}}(o.position());if(n)return e.positions(o);var s=e.positions[o._private.data.id];return s??null}(r);return!r.locked()&&i!=null&&i}),this};var mh={fit:!0,padding:30,boundingBox:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}};function sl(e){this.options=he({},mh,e)}sl.prototype.run=function(){var e=this.options,t=e.cy,n=e.eles,r=tt(e.boundingBox?e.boundingBox:{x1:0,y1:0,w:t.width(),h:t.height()});return n.nodes().layoutPositions(this,e,function(a,i){return{x:r.x1+Math.round(Math.random()*r.w),y:r.y1+Math.round(Math.random()*r.h)}}),this};var bh=[{name:"breadthfirst",impl:tl},{name:"circle",impl:nl},{name:"concentric",impl:rl},{name:"cose",impl:Xr},{name:"grid",impl:il},{name:"null",impl:Wa},{name:"preset",impl:ol},{name:"random",impl:sl}];function ll(e){this.options=e,this.notifications=0}var Ao=function(){},Mo=function(){throw new Error("A headless instance can not render images")};ll.prototype={recalculateRenderedStyle:Ao,notify:function(){this.notifications++},init:Ao,isHeadless:function(){return!0},png:Mo,jpg:Mo};var xh={arrowShapeWidth:.3,registerArrowShapes:function(){var e=this.arrowShapes={},t=this,n=function(l,u,c,d,h,p,f){var v=h.x-c/2-f,m=h.x+c/2+f,g=h.y-c/2-f,y=h.y+c/2+f;return v<=l&&l<=m&&g<=u&&u<=y},r=function(l,u,c,d,h){var p=l*Math.cos(d)-u*Math.sin(d),f=(l*Math.sin(d)+u*Math.cos(d))*c;return{x:p*c+h.x,y:f+h.y}},a=function(l,u,c,d){for(var h=[],p=0;p<l.length;p+=2){var f=l[p],v=l[p+1];h.push(r(f,v,u,c,d))}return h},i=function(l){for(var u=[],c=0;c<l.length;c++){var d=l[c];u.push(d.x,d.y)}return u},o=function(l){return l.pstyle("width").pfValue*l.pstyle("arrow-scale").pfValue*2},s=function(l,u){ce(u)&&(u=e[u]),e[l]=he({name:l,points:[-.15,-.3,.15,-.3,.15,.3,-.15,.3],collide:function(c,d,h,p,f,v){var m=i(a(this.points,h+2*v,p,f));return et(c,d,m)},roughCollide:n,draw:function(c,d,h,p){var f=a(this.points,d,h,p);t.arrowShapeImpl("polygon")(c,f)},spacing:function(c){return 0},gap:o},u)};s("none",{collide:Kr,roughCollide:Kr,draw:ii,spacing:Mi,gap:Mi}),s("triangle",{points:[-.15,-.3,0,0,.15,-.3]}),s("arrow","triangle"),s("triangle-backcurve",{points:e.triangle.points,controlPoint:[0,-.15],roughCollide:n,draw:function(l,u,c,d,h){var p=a(this.points,u,c,d),f=this.controlPoint,v=r(f[0],f[1],u,c,d);t.arrowShapeImpl(this.name)(l,p,v)},gap:function(l){return .8*o(l)}}),s("triangle-tee",{points:[0,0,.15,-.3,-.15,-.3,0,0],pointsTee:[-.15,-.4,-.15,-.5,.15,-.5,.15,-.4],collide:function(l,u,c,d,h,p,f){var v=i(a(this.points,c+2*f,d,h)),m=i(a(this.pointsTee,c+2*f,d,h));return et(l,u,v)||et(l,u,m)},draw:function(l,u,c,d,h){var p=a(this.points,u,c,d),f=a(this.pointsTee,u,c,d);t.arrowShapeImpl(this.name)(l,p,f)}}),s("circle-triangle",{radius:.15,pointsTr:[0,-.15,.15,-.45,-.15,-.45,0,-.15],collide:function(l,u,c,d,h,p,f){var v=h,m=Math.pow(v.x-l,2)+Math.pow(v.y-u,2)<=Math.pow((c+2*f)*this.radius,2),g=i(a(this.points,c+2*f,d,h));return et(l,u,g)||m},draw:function(l,u,c,d,h){var p=a(this.pointsTr,u,c,d);t.arrowShapeImpl(this.name)(l,p,d.x,d.y,this.radius*u)},spacing:function(l){return t.getArrowWidth(l.pstyle("width").pfValue,l.pstyle("arrow-scale").value)*this.radius}}),s("triangle-cross",{points:[0,0,.15,-.3,-.15,-.3,0,0],baseCrossLinePts:[-.15,-.4,-.15,-.4,.15,-.4,.15,-.4],crossLinePts:function(l,u){var c=this.baseCrossLinePts.slice(),d=u/l;return c[3]=c[3]-d,c[5]=c[5]-d,c},collide:function(l,u,c,d,h,p,f){var v=i(a(this.points,c+2*f,d,h)),m=i(a(this.crossLinePts(c,p),c+2*f,d,h));return et(l,u,v)||et(l,u,m)},draw:function(l,u,c,d,h){var p=a(this.points,u,c,d),f=a(this.crossLinePts(u,h),u,c,d);t.arrowShapeImpl(this.name)(l,p,f)}}),s("vee",{points:[-.15,-.3,0,0,.15,-.3,0,-.15],gap:function(l){return .525*o(l)}}),s("circle",{radius:.15,collide:function(l,u,c,d,h,p,f){var v=h;return Math.pow(v.x-l,2)+Math.pow(v.y-u,2)<=Math.pow((c+2*f)*this.radius,2)},draw:function(l,u,c,d,h){t.arrowShapeImpl(this.name)(l,d.x,d.y,this.radius*u)},spacing:function(l){return t.getArrowWidth(l.pstyle("width").pfValue,l.pstyle("arrow-scale").value)*this.radius}}),s("tee",{points:[-.15,0,-.15,-.1,.15,-.1,.15,0],spacing:function(l){return 1},gap:function(l){return 1}}),s("square",{points:[-.15,0,.15,0,.15,-.3,-.15,-.3]}),s("diamond",{points:[-.15,-.15,0,-.3,.15,-.15,0,0],gap:function(l){return l.pstyle("width").pfValue*l.pstyle("arrow-scale").value}}),s("chevron",{points:[0,0,-.15,-.15,-.1,-.2,0,-.1,.1,-.2,.15,-.15],gap:function(l){return .95*l.pstyle("width").pfValue*l.pstyle("arrow-scale").value}})}},wh={projectIntoViewport:function(e,t){var n=this.cy,r=this.findContainerClientCoords(),a=r[0],i=r[1],o=r[4],s=n.pan(),l=n.zoom();return[((e-a)/o-s.x)/l,((t-i)/o-s.y)/l]},findContainerClientCoords:function(){if(this.containerBB)return this.containerBB;var e=this.container,t=e.getBoundingClientRect(),n=this.cy.window().getComputedStyle(e),r=function(x){return parseFloat(n.getPropertyValue(x))},a=r("padding-left"),i=r("padding-right"),o=r("padding-top"),s=r("padding-bottom"),l=r("border-left-width"),u=r("border-right-width"),c=r("border-top-width"),d=(r("border-bottom-width"),e.clientWidth),h=e.clientHeight,p=a+i,f=o+s,v=l+u,m=t.width/(d+v),g=d-p,y=h-f,b=t.left+a+l,k=t.top+o+c;return this.containerBB=[b,k,g,y,m]},invalidateContainerClientCoordsCache:function(){this.containerBB=null},findNearestElement:function(e,t,n,r){return this.findNearestElements(e,t,n,r)[0]},findNearestElements:function(e,t,n,r){var a,i,o=this,s=this,l=s.getCachedZSortedEles(),u=[],c=s.cy.zoom(),d=s.cy.hasCompoundNodes(),h=(r?24:8)/c,p=(r?8:2)/c,f=(r?8:2)/c,v=1/0;function m(E,C){if(E.isNode()){if(i)return;i=E,u.push(E)}if(E.isEdge()&&(C==null||C<v))if(a){if(a.pstyle("z-compound-depth").value===E.pstyle("z-compound-depth").value&&a.pstyle("z-compound-depth").value===E.pstyle("z-compound-depth").value){for(var S=0;S<u.length;S++)if(u[S].isEdge()){u[S]=E,a=E,v=C??v;break}}}else u.push(E),a=E,v=C??v}function g(E){var C=E.outerWidth()+2*p,S=E.outerHeight()+2*p,P=C/2,_=S/2,A=E.position(),B=E.pstyle("corner-radius").value==="auto"?"auto":E.pstyle("corner-radius").pfValue,O=E._private.rscratch;if(A.x-P<=e&&e<=A.x+P&&A.y-_<=t&&t<=A.y+_&&s.nodeShapes[o.getNodeShape(E)].checkPoint(e,t,0,C,S,A.x,A.y,B,O))return m(E,0),!0}function y(E){var C,S=E._private,P=S.rscratch,_=E.pstyle("width").pfValue,A=E.pstyle("arrow-scale").value,B=_/2+h,O=B*B,N=2*B,V=S.source,M=S.target;if(P.edgeType==="segments"||P.edgeType==="straight"||P.edgeType==="haystack"){for(var D=P.allpts,I=0;I+3<D.length;I+=2)if(Bu(e,t,D[I],D[I+1],D[I+2],D[I+3],N)&&O>(C=Au(e,t,D[I],D[I+1],D[I+2],D[I+3])))return m(E,C),!0}else if(P.edgeType==="bezier"||P.edgeType==="multibezier"||P.edgeType==="self"||P.edgeType==="compound"){for(D=P.allpts,I=0;I+5<P.allpts.length;I+=4)if(Du(e,t,D[I],D[I+1],D[I+2],D[I+3],D[I+4],D[I+5],N)&&O>(C=_u(e,t,D[I],D[I+1],D[I+2],D[I+3],D[I+4],D[I+5])))return m(E,C),!0}V=V||S.source,M=M||S.target;var z=o.getArrowWidth(_,A),q=[{name:"source",x:P.arrowStartX,y:P.arrowStartY,angle:P.srcArrowAngle},{name:"target",x:P.arrowEndX,y:P.arrowEndY,angle:P.tgtArrowAngle},{name:"mid-source",x:P.midX,y:P.midY,angle:P.midsrcArrowAngle},{name:"mid-target",x:P.midX,y:P.midY,angle:P.midtgtArrowAngle}];for(I=0;I<q.length;I++){var j=q[I],U=s.arrowShapes[E.pstyle(j.name+"-arrow-shape").value],K=E.pstyle("width").pfValue;if(U.roughCollide(e,t,z,j.angle,{x:j.x,y:j.y},K,h)&&U.collide(e,t,z,j.angle,{x:j.x,y:j.y},K,h))return m(E),!0}d&&u.length>0&&(g(V),g(M))}function b(E,C,S){return ft(E,C,S)}function k(E,C){var S,P=E._private,_=f;S=C?C+"-":"",E.boundingBox();var A=P.labelBounds[C||"main"],B=E.pstyle(S+"label").value;if(E.pstyle("text-events").strValue==="yes"&&B){var O=b(P.rscratch,"labelX",C),N=b(P.rscratch,"labelY",C),V=b(P.rscratch,"labelAngle",C),M=E.pstyle(S+"text-margin-x").pfValue,D=E.pstyle(S+"text-margin-y").pfValue,I=A.x1-_-M,z=A.x2+_-M,q=A.y1-_-D,j=A.y2+_-D;if(V){var U=Math.cos(V),K=Math.sin(V),X=function(se,le){return{x:(se-=O)*U-(le-=N)*K+O,y:se*K+le*U+N}},H=X(I,q),W=X(I,j),Q=X(z,q),te=X(z,j),ie=[H.x+M,H.y+D,Q.x+M,Q.y+D,te.x+M,te.y+D,W.x+M,W.y+D];if(et(e,t,ie))return m(E),!0}else if(Pn(A,e,t))return m(E),!0}}n&&(l=l.interactive);for(var x=l.length-1;x>=0;x--){var w=l[x];w.isNode()?g(w)||k(w):y(w)||k(w)||k(w,"source")||k(w,"target")}return u},getAllInBox:function(e,t,n,r){for(var a,i,o=this.getCachedZSortedEles().interactive,s=[],l=Math.min(e,n),u=Math.max(e,n),c=Math.min(t,r),d=Math.max(t,r),h=tt({x1:e=l,y1:t=c,x2:n=u,y2:r=d}),p=0;p<o.length;p++){var f=o[p];if(f.isNode()){var v=f,m=v.boundingBox({includeNodes:!0,includeEdges:!1,includeLabels:!1});si(h,m)&&!Es(m,h)&&s.push(v)}else{var g=f,y=g._private,b=y.rscratch;if(b.startX!=null&&b.startY!=null&&!Pn(h,b.startX,b.startY)||b.endX!=null&&b.endY!=null&&!Pn(h,b.endX,b.endY))continue;if(b.edgeType==="bezier"||b.edgeType==="multibezier"||b.edgeType==="self"||b.edgeType==="compound"||b.edgeType==="segments"||b.edgeType==="haystack"){for(var k=y.rstyle.bezierPts||y.rstyle.linePts||y.rstyle.haystackPts,x=!0,w=0;w<k.length;w++)if(a=h,i=k[w],!Pn(a,i.x,i.y)){x=!1;break}x&&s.push(g)}else b.edgeType!=="haystack"&&b.edgeType!=="straight"||s.push(g)}}return s}},Ka={calculateArrowAngles:function(e){var t,n,r,a,i,o,s=e._private.rscratch,l=s.edgeType==="haystack",u=s.edgeType==="bezier",c=s.edgeType==="multibezier",d=s.edgeType==="segments",h=s.edgeType==="compound",p=s.edgeType==="self";if(l?(r=s.haystackPts[0],a=s.haystackPts[1],i=s.haystackPts[2],o=s.haystackPts[3]):(r=s.arrowStartX,a=s.arrowStartY,i=s.arrowEndX,o=s.arrowEndY),v=s.midX,m=s.midY,d)t=r-s.segpts[0],n=a-s.segpts[1];else if(c||h||p||u){var f=s.allpts;t=r-Oe(f[0],f[2],f[4],.1),n=a-Oe(f[1],f[3],f[5],.1)}else t=r-v,n=a-m;s.srcArrowAngle=xr(t,n);var v=s.midX,m=s.midY;if(l&&(v=(r+i)/2,m=(a+o)/2),t=i-r,n=o-a,d)if((f=s.allpts).length/2%2==0){var g=(S=f.length/2)-2;t=f[S]-f[g],n=f[S+1]-f[g+1]}else s.isRound?(t=s.midVector[1],n=-s.midVector[0]):(g=(S=f.length/2-1)-2,t=f[S]-f[g],n=f[S+1]-f[g+1]);else if(c||h||p){var y,b,k,x,f=s.allpts;if(s.ctrlpts.length/2%2==0){var w=(E=(C=f.length/2-1)+2)+2;y=Oe(f[C],f[E],f[w],0),b=Oe(f[C+1],f[E+1],f[w+1],0),k=Oe(f[C],f[E],f[w],1e-4),x=Oe(f[C+1],f[E+1],f[w+1],1e-4)}else{var E,C;w=(E=f.length/2-1)+2,y=Oe(f[C=E-2],f[E],f[w],.4999),b=Oe(f[C+1],f[E+1],f[w+1],.4999),k=Oe(f[C],f[E],f[w],.5),x=Oe(f[C+1],f[E+1],f[w+1],.5)}t=k-y,n=x-b}if(s.midtgtArrowAngle=xr(t,n),s.midDispX=t,s.midDispY=n,t*=-1,n*=-1,d&&(f=s.allpts).length/2%2!=0){if(!s.isRound){var S,P=(S=f.length/2-1)+2;t=-(f[P]-f[S]),n=-(f[P+1]-f[S+1])}}if(s.midsrcArrowAngle=xr(t,n),d)t=i-s.segpts[s.segpts.length-2],n=o-s.segpts[s.segpts.length-1];else if(c||h||p||u){var _=(f=s.allpts).length;t=i-Oe(f[_-6],f[_-4],f[_-2],.9),n=o-Oe(f[_-5],f[_-3],f[_-1],.9)}else t=i-v,n=o-m;s.tgtArrowAngle=xr(t,n)}};Ka.getArrowWidth=Ka.getArrowHeight=function(e,t){var n=this.arrowWidthCache=this.arrowWidthCache||{},r=n[e+", "+t];return r||(r=Math.max(Math.pow(13.37*e,.9),29)*t,n[e+", "+t]=r,r)};var Ha,Ua,Ro,Io,nn,Yr,Tt,Gt,en,kt,Yn,Mr,ul,cl,Ga,Za,No,bt={},st={},Lo=function(e,t,n){n.x=t.x-e.x,n.y=t.y-e.y,n.len=Math.sqrt(n.x*n.x+n.y*n.y),n.nx=n.x/n.len,n.ny=n.y/n.len,n.ang=Math.atan2(n.ny,n.nx)},Eh=function(e,t,n,r,a){var i,o;if(e!==No?Lo(t,e,bt):((o=bt).x=-1*(i=st).x,o.y=-1*i.y,o.nx=-1*i.nx,o.ny=-1*i.ny,o.ang=i.ang>0?-(Math.PI-i.ang):Math.PI+i.ang),Lo(t,n,st),Ro=bt.nx*st.ny-bt.ny*st.nx,Io=bt.nx*st.nx-bt.ny*-st.ny,Tt=Math.asin(Math.max(-1,Math.min(1,Ro))),Math.abs(Tt)<1e-6)return Ha=t.x,Ua=t.y,void(en=Yn=0);nn=1,Yr=!1,Io<0?Tt<0?Tt=Math.PI+Tt:(Tt=Math.PI-Tt,nn=-1,Yr=!0):Tt>0&&(nn=-1,Yr=!0),Yn=t.radius!==void 0?t.radius:r,Gt=Tt/2,Mr=Math.min(bt.len/2,st.len/2),a?(kt=Math.abs(Math.cos(Gt)*Yn/Math.sin(Gt)))>Mr?(kt=Mr,en=Math.abs(kt*Math.sin(Gt)/Math.cos(Gt))):en=Yn:(kt=Math.min(Mr,Yn),en=Math.abs(kt*Math.sin(Gt)/Math.cos(Gt))),Ga=t.x+st.nx*kt,Za=t.y+st.ny*kt,Ha=Ga-st.ny*en*nn,Ua=Za+st.nx*en*nn,ul=t.x+bt.nx*kt,cl=t.y+bt.ny*kt,No=t};function dl(e,t){t.radius===0?e.lineTo(t.cx,t.cy):e.arc(t.cx,t.cy,t.radius,t.startAngle,t.endAngle,t.counterClockwise)}function wi(e,t,n,r){var a=!(arguments.length>4&&arguments[4]!==void 0)||arguments[4];return r===0||t.radius===0?{cx:t.x,cy:t.y,radius:0,startX:t.x,startY:t.y,stopX:t.x,stopY:t.y,startAngle:void 0,endAngle:void 0,counterClockwise:void 0}:(Eh(e,t,n,r,a),{cx:Ha,cy:Ua,radius:en,startX:ul,startY:cl,stopX:Ga,stopY:Za,startAngle:bt.ang+Math.PI/2*nn,endAngle:st.ang-Math.PI/2*nn,counterClockwise:Yr})}var Ue={};function Oo(e){var t=[];if(e!=null){for(var n=0;n<e.length;n+=2){var r=e[n],a=e[n+1];t.push({x:r,y:a})}return t}}Ue.findMidptPtsEtc=function(e,t){var n,r=t.posPts,a=t.intersectionPts,i=t.vectorNormInverse,o=e.pstyle("source-endpoint"),s=e.pstyle("target-endpoint"),l=o.units!=null&&s.units!=null;switch(e.pstyle("edge-distances").value){case"node-position":n=r;break;case"intersection":n=a;break;case"endpoints":if(l){var u=ze(this.manualEndptToPx(e.source()[0],o),2),c=u[0],d=u[1],h=ze(this.manualEndptToPx(e.target()[0],s),2),p=h[0],f=h[1],v={x1:c,y1:d,x2:p,y2:f};i=function(m,g,y,b){var k=b-g,x=y-m,w=Math.sqrt(x*x+k*k);return{x:-k/w,y:x/w}}(c,d,p,f),n=v}else we("Edge ".concat(e.id()," has edge-distances:endpoints specified without manual endpoints specified via source-endpoint and target-endpoint.  Falling back on edge-distances:intersection (default).")),n=a}return{midptPts:n,vectorNormInverse:i}},Ue.findHaystackPoints=function(e){for(var t=0;t<e.length;t++){var n=e[t],r=n._private,a=r.rscratch;if(!a.haystack){var i=2*Math.random()*Math.PI;a.source={x:Math.cos(i),y:Math.sin(i)},i=2*Math.random()*Math.PI,a.target={x:Math.cos(i),y:Math.sin(i)}}var o=r.source,s=r.target,l=o.position(),u=s.position(),c=o.width(),d=s.width(),h=o.height(),p=s.height(),f=n.pstyle("haystack-radius").value/2;a.haystackPts=a.allpts=[a.source.x*c*f+l.x,a.source.y*h*f+l.y,a.target.x*d*f+u.x,a.target.y*p*f+u.y],a.midX=(a.allpts[0]+a.allpts[2])/2,a.midY=(a.allpts[1]+a.allpts[3])/2,a.edgeType="haystack",a.haystack=!0,this.storeEdgeProjections(n),this.calculateArrowAngles(n),this.recalculateEdgeLabelProjections(n),this.calculateLabelAngles(n)}},Ue.findSegmentsPoints=function(e,t){var n=e._private.rscratch,r=e.pstyle("segment-weights"),a=e.pstyle("segment-distances"),i=e.pstyle("segment-radii"),o=e.pstyle("radius-type"),s=Math.min(r.pfValue.length,a.pfValue.length),l=i.pfValue[i.pfValue.length-1],u=o.pfValue[o.pfValue.length-1];n.edgeType="segments",n.segpts=[],n.radii=[],n.isArcRadius=[];for(var c=0;c<s;c++){var d=r.pfValue[c],h=a.pfValue[c],p=1-d,f=d,v=this.findMidptPtsEtc(e,t),m=v.midptPts,g=v.vectorNormInverse,y={x:m.x1*p+m.x2*f,y:m.y1*p+m.y2*f};n.segpts.push(y.x+g.x*h,y.y+g.y*h),n.radii.push(i.pfValue[c]!==void 0?i.pfValue[c]:l),n.isArcRadius.push((o.pfValue[c]!==void 0?o.pfValue[c]:u)==="arc-radius")}},Ue.findLoopPoints=function(e,t,n,r){var a=e._private.rscratch,i=t.dirCounts,o=t.srcPos,s=e.pstyle("control-point-distances"),l=s?s.pfValue[0]:void 0,u=e.pstyle("loop-direction").pfValue,c=e.pstyle("loop-sweep").pfValue,d=e.pstyle("control-point-step-size").pfValue;a.edgeType="self";var h=n,p=d;r&&(h=0,p=l);var f=u-Math.PI/2,v=f-c/2,m=f+c/2,g=u+"_"+c;h=i[g]===void 0?i[g]=0:++i[g],a.ctrlpts=[o.x+1.4*Math.cos(v)*p*(h/3+1),o.y+1.4*Math.sin(v)*p*(h/3+1),o.x+1.4*Math.cos(m)*p*(h/3+1),o.y+1.4*Math.sin(m)*p*(h/3+1)]},Ue.findCompoundLoopPoints=function(e,t,n,r){var a=e._private.rscratch;a.edgeType="compound";var i=t.srcPos,o=t.tgtPos,s=t.srcW,l=t.srcH,u=t.tgtW,c=t.tgtH,d=e.pstyle("control-point-step-size").pfValue,h=e.pstyle("control-point-distances"),p=h?h.pfValue[0]:void 0,f=n,v=d;r&&(f=0,v=p);var m={x:i.x-s/2,y:i.y-l/2},g={x:o.x-u/2,y:o.y-c/2},y={x:Math.min(m.x,g.x),y:Math.min(m.y,g.y)},b=Math.max(.5,Math.log(.01*s)),k=Math.max(.5,Math.log(.01*u));a.ctrlpts=[y.x,y.y-(1+Math.pow(50,1.12)/100)*v*(f/3+1)*b,y.x-(1+Math.pow(50,1.12)/100)*v*(f/3+1)*k,y.y]},Ue.findStraightEdgePoints=function(e){e._private.rscratch.edgeType="straight"},Ue.findBezierPoints=function(e,t,n,r,a){var i=e._private.rscratch,o=e.pstyle("control-point-step-size").pfValue,s=e.pstyle("control-point-distances"),l=e.pstyle("control-point-weights"),u=s&&l?Math.min(s.value.length,l.value.length):1,c=s?s.pfValue[0]:void 0,d=l.value[0],h=r;i.edgeType=h?"multibezier":"bezier",i.ctrlpts=[];for(var p=0;p<u;p++){var f=(.5-t.eles.length/2+n)*o*(a?-1:1),v=void 0,m=Ii(f);h&&(c=s?s.pfValue[p]:o,d=l.value[p]);var g=(v=r?c:c!==void 0?m*c:void 0)!==void 0?v:f,y=1-d,b=d,k=this.findMidptPtsEtc(e,t),x=k.midptPts,w=k.vectorNormInverse,E={x:x.x1*y+x.x2*b,y:x.y1*y+x.y2*b};i.ctrlpts.push(E.x+w.x*g,E.y+w.y*g)}},Ue.findTaxiPoints=function(e,t){var n=e._private.rscratch;n.edgeType="segments";var r="vertical",a="horizontal",i="leftward",o="rightward",s="downward",l="upward",u=t.posPts,c=t.srcW,d=t.srcH,h=t.tgtW,p=t.tgtH,f=e.pstyle("edge-distances").value!=="node-position",v=e.pstyle("taxi-direction").value,m=v,g=e.pstyle("taxi-turn"),y=g.units==="%",b=g.pfValue,k=b<0,x=e.pstyle("taxi-turn-min-distance").pfValue,w=f?(c+h)/2:0,E=f?(d+p)/2:0,C=u.x2-u.x1,S=u.y2-u.y1,P=function(re,de){return re>0?Math.max(re-de,0):Math.min(re+de,0)},_=P(C,w),A=P(S,E),B=!1;m==="auto"?v=Math.abs(_)>Math.abs(A)?a:r:m===l||m===s?(v=r,B=!0):m!==i&&m!==o||(v=a,B=!0);var O,N=v===r,V=N?A:_,M=N?S:C,D=Ii(M),I=!1;B&&(y||k)||!(m===s&&M<0||m===l&&M>0||m===i&&M>0||m===o&&M<0)||(V=(D*=-1)*Math.abs(V),I=!0),y?O=(b<0?1+b:b)*V:O=(b<0?V:0)+b*D;var z=function(re){return Math.abs(re)<x||Math.abs(re)>=Math.abs(V)},q=z(O),j=z(Math.abs(V)-Math.abs(O));if((q||j)&&!I)if(N){var U=Math.abs(M)<=d/2,K=Math.abs(C)<=h/2;if(U){var X=(u.x1+u.x2)/2,H=u.y1,W=u.y2;n.segpts=[X,H,X,W]}else if(K){var Q=(u.y1+u.y2)/2,te=u.x1,ie=u.x2;n.segpts=[te,Q,ie,Q]}else n.segpts=[u.x1,u.y2]}else{var se=Math.abs(M)<=c/2,le=Math.abs(S)<=p/2;if(se){var fe=(u.y1+u.y2)/2,$=u.x1,ne=u.x2;n.segpts=[$,fe,ne,fe]}else if(le){var T=(u.x1+u.x2)/2,R=u.y1,L=u.y2;n.segpts=[T,R,T,L]}else n.segpts=[u.x2,u.y1]}else if(N){var Y=u.y1+O+(f?d/2*D:0),F=u.x1,J=u.x2;n.segpts=[F,Y,J,Y]}else{var G=u.x1+O+(f?c/2*D:0),Z=u.y1,ee=u.y2;n.segpts=[G,Z,G,ee]}if(n.isRound){var oe=e.pstyle("taxi-radius").value,ue=e.pstyle("radius-type").value[0]==="arc-radius";n.radii=new Array(n.segpts.length/2).fill(oe),n.isArcRadius=new Array(n.segpts.length/2).fill(ue)}},Ue.tryToCorrectInvalidPoints=function(e,t){var n=e._private.rscratch;if(n.edgeType==="bezier"){var r=t.srcPos,a=t.tgtPos,i=t.srcW,o=t.srcH,s=t.tgtW,l=t.tgtH,u=t.srcShape,c=t.tgtShape,d=t.srcCornerRadius,h=t.tgtCornerRadius,p=t.srcRs,f=t.tgtRs,v=!ae(n.startX)||!ae(n.startY),m=!ae(n.arrowStartX)||!ae(n.arrowStartY),g=!ae(n.endX)||!ae(n.endY),y=!ae(n.arrowEndX)||!ae(n.arrowEndY),b=3*(this.getArrowWidth(e.pstyle("width").pfValue,e.pstyle("arrow-scale").value)*this.arrowShapeWidth),k=rn({x:n.ctrlpts[0],y:n.ctrlpts[1]},{x:n.startX,y:n.startY}),x=k<b,w=rn({x:n.ctrlpts[0],y:n.ctrlpts[1]},{x:n.endX,y:n.endY}),E=w<b,C=!1;if(v||m||x){C=!0;var S={x:n.ctrlpts[0]-r.x,y:n.ctrlpts[1]-r.y},P=Math.sqrt(S.x*S.x+S.y*S.y),_={x:S.x/P,y:S.y/P},A=Math.max(i,o),B={x:n.ctrlpts[0]+2*_.x*A,y:n.ctrlpts[1]+2*_.y*A},O=u.intersectLine(r.x,r.y,i,o,B.x,B.y,0,d,p);x?(n.ctrlpts[0]=n.ctrlpts[0]+_.x*(b-k),n.ctrlpts[1]=n.ctrlpts[1]+_.y*(b-k)):(n.ctrlpts[0]=O[0]+_.x*b,n.ctrlpts[1]=O[1]+_.y*b)}if(g||y||E){C=!0;var N={x:n.ctrlpts[0]-a.x,y:n.ctrlpts[1]-a.y},V=Math.sqrt(N.x*N.x+N.y*N.y),M={x:N.x/V,y:N.y/V},D=Math.max(i,o),I={x:n.ctrlpts[0]+2*M.x*D,y:n.ctrlpts[1]+2*M.y*D},z=c.intersectLine(a.x,a.y,s,l,I.x,I.y,0,h,f);E?(n.ctrlpts[0]=n.ctrlpts[0]+M.x*(b-w),n.ctrlpts[1]=n.ctrlpts[1]+M.y*(b-w)):(n.ctrlpts[0]=z[0]+M.x*b,n.ctrlpts[1]=z[1]+M.y*b)}C&&this.findEndpoints(e)}},Ue.storeAllpts=function(e){var t=e._private.rscratch;if(t.edgeType==="multibezier"||t.edgeType==="bezier"||t.edgeType==="self"||t.edgeType==="compound"){t.allpts=[],t.allpts.push(t.startX,t.startY);for(var n=0;n+1<t.ctrlpts.length;n+=2)t.allpts.push(t.ctrlpts[n],t.ctrlpts[n+1]),n+3<t.ctrlpts.length&&t.allpts.push((t.ctrlpts[n]+t.ctrlpts[n+2])/2,(t.ctrlpts[n+1]+t.ctrlpts[n+3])/2);var r;t.allpts.push(t.endX,t.endY),t.ctrlpts.length/2%2==0?(r=t.allpts.length/2-1,t.midX=t.allpts[r],t.midY=t.allpts[r+1]):(r=t.allpts.length/2-3,t.midX=Oe(t.allpts[r],t.allpts[r+2],t.allpts[r+4],.5),t.midY=Oe(t.allpts[r+1],t.allpts[r+3],t.allpts[r+5],.5))}else if(t.edgeType==="straight")t.allpts=[t.startX,t.startY,t.endX,t.endY],t.midX=(t.startX+t.endX+t.arrowStartX+t.arrowEndX)/4,t.midY=(t.startY+t.endY+t.arrowStartY+t.arrowEndY)/4;else if(t.edgeType==="segments"){if(t.allpts=[],t.allpts.push(t.startX,t.startY),t.allpts.push.apply(t.allpts,t.segpts),t.allpts.push(t.endX,t.endY),t.isRound){t.roundCorners=[];for(var a=2;a+3<t.allpts.length;a+=2){var i=t.radii[a/2-1],o=t.isArcRadius[a/2-1];t.roundCorners.push(wi({x:t.allpts[a-2],y:t.allpts[a-1]},{x:t.allpts[a],y:t.allpts[a+1],radius:i},{x:t.allpts[a+2],y:t.allpts[a+3]},i,o))}}if(t.segpts.length%4==0){var s=t.segpts.length/2,l=s-2;t.midX=(t.segpts[l]+t.segpts[s])/2,t.midY=(t.segpts[l+1]+t.segpts[s+1])/2}else{var u=t.segpts.length/2-1;if(t.isRound){var c={x:t.segpts[u],y:t.segpts[u+1]},d=t.roundCorners[u/2],h=[c.x-d.cx,c.y-d.cy],p=d.radius/Math.sqrt(Math.pow(h[0],2)+Math.pow(h[1],2));h=h.map(function(f){return f*p}),t.midX=d.cx+h[0],t.midY=d.cy+h[1],t.midVector=h}else t.midX=t.segpts[u],t.midY=t.segpts[u+1]}}},Ue.checkForInvalidEdgeWarning=function(e){var t=e[0]._private.rscratch;t.nodesOverlap||ae(t.startX)&&ae(t.startY)&&ae(t.endX)&&ae(t.endY)?t.loggedErr=!1:t.loggedErr||(t.loggedErr=!0,we("Edge `"+e.id()+"` has invalid endpoints and so it is impossible to draw.  Adjust your edge style (e.g. control points) accordingly or use an alternative edge type.  This is expected behaviour when the source node and the target node overlap."))},Ue.findEdgeControlPoints=function(e){var t=this;if(e&&e.length!==0){for(var n=this,r=n.cy.hasCompoundNodes(),a={map:new wt,get:function(b){var k=this.map.get(b[0]);return k!=null?k.get(b[1]):null},set:function(b,k){var x=this.map.get(b[0]);x==null&&(x=new wt,this.map.set(b[0],x)),x.set(b[1],k)}},i=[],o=[],s=0;s<e.length;s++){var l=e[s],u=l._private,c=l.pstyle("curve-style").value;if(!l.removed()&&l.takesUpSpace())if(c!=="haystack"){var d=c==="unbundled-bezier"||c.endsWith("segments")||c==="straight"||c==="straight-triangle"||c.endsWith("taxi"),h=c==="unbundled-bezier"||c==="bezier",p=u.source,f=u.target,v=[p.poolIndex(),f.poolIndex()].sort(),m=a.get(v);m==null&&(m={eles:[]},a.set(v,m),i.push(v)),m.eles.push(l),d&&(m.hasUnbundled=!0),h&&(m.hasBezier=!0)}else o.push(l)}for(var g=function(b){var k=i[b],x=a.get(k),w=void 0;if(!x.hasUnbundled){var E=x.eles[0].parallelEdges().filter(function(Z){return Z.isBundledBezier()});Aa(x.eles),E.forEach(function(Z){return x.eles.push(Z)}),x.eles.sort(function(Z,ee){return Z.poolIndex()-ee.poolIndex()})}var C=x.eles[0],S=C.source(),P=C.target();if(S.poolIndex()>P.poolIndex()){var _=S;S=P,P=_}var A=x.srcPos=S.position(),B=x.tgtPos=P.position(),O=x.srcW=S.outerWidth(),N=x.srcH=S.outerHeight(),V=x.tgtW=P.outerWidth(),M=x.tgtH=P.outerHeight(),D=x.srcShape=n.nodeShapes[t.getNodeShape(S)],I=x.tgtShape=n.nodeShapes[t.getNodeShape(P)],z=x.srcCornerRadius=S.pstyle("corner-radius").value==="auto"?"auto":S.pstyle("corner-radius").pfValue,q=x.tgtCornerRadius=P.pstyle("corner-radius").value==="auto"?"auto":P.pstyle("corner-radius").pfValue,j=x.tgtRs=P._private.rscratch,U=x.srcRs=S._private.rscratch;x.dirCounts={north:0,west:0,south:0,east:0,northwest:0,southwest:0,northeast:0,southeast:0};for(var K=0;K<x.eles.length;K++){var X=x.eles[K],H=X[0]._private.rscratch,W=X.pstyle("curve-style").value,Q=W==="unbundled-bezier"||W.endsWith("segments")||W.endsWith("taxi"),te=!S.same(X.source());if(!x.calculatedIntersection&&S!==P&&(x.hasBezier||x.hasUnbundled)){x.calculatedIntersection=!0;var ie=D.intersectLine(A.x,A.y,O,N,B.x,B.y,0,z,U),se=x.srcIntn=ie,le=I.intersectLine(B.x,B.y,V,M,A.x,A.y,0,q,j),fe=x.tgtIntn=le,$=x.intersectionPts={x1:ie[0],x2:le[0],y1:ie[1],y2:le[1]},ne=x.posPts={x1:A.x,x2:B.x,y1:A.y,y2:B.y},T=le[1]-ie[1],R=le[0]-ie[0],L=Math.sqrt(R*R+T*T),Y=x.vector={x:R,y:T},F=x.vectorNorm={x:Y.x/L,y:Y.y/L},J={x:-F.y,y:F.x};x.nodesOverlap=!ae(L)||I.checkPoint(ie[0],ie[1],0,V,M,B.x,B.y,q,j)||D.checkPoint(le[0],le[1],0,O,N,A.x,A.y,z,U),x.vectorNormInverse=J,w={nodesOverlap:x.nodesOverlap,dirCounts:x.dirCounts,calculatedIntersection:!0,hasBezier:x.hasBezier,hasUnbundled:x.hasUnbundled,eles:x.eles,srcPos:B,srcRs:j,tgtPos:A,tgtRs:U,srcW:V,srcH:M,tgtW:O,tgtH:N,srcIntn:fe,tgtIntn:se,srcShape:I,tgtShape:D,posPts:{x1:ne.x2,y1:ne.y2,x2:ne.x1,y2:ne.y1},intersectionPts:{x1:$.x2,y1:$.y2,x2:$.x1,y2:$.y1},vector:{x:-Y.x,y:-Y.y},vectorNorm:{x:-F.x,y:-F.y},vectorNormInverse:{x:-J.x,y:-J.y}}}var G=te?w:x;H.nodesOverlap=G.nodesOverlap,H.srcIntn=G.srcIntn,H.tgtIntn=G.tgtIntn,H.isRound=W.startsWith("round"),r&&(S.isParent()||S.isChild()||P.isParent()||P.isChild())&&(S.parents().anySame(P)||P.parents().anySame(S)||S.same(P)&&S.isParent())?t.findCompoundLoopPoints(X,G,K,Q):S===P?t.findLoopPoints(X,G,K,Q):W.endsWith("segments")?t.findSegmentsPoints(X,G):W.endsWith("taxi")?t.findTaxiPoints(X,G):W==="straight"||!Q&&x.eles.length%2==1&&K===Math.floor(x.eles.length/2)?t.findStraightEdgePoints(X):t.findBezierPoints(X,G,K,Q,te),t.findEndpoints(X),t.tryToCorrectInvalidPoints(X,G),t.checkForInvalidEdgeWarning(X),t.storeAllpts(X),t.storeEdgeProjections(X),t.calculateArrowAngles(X),t.recalculateEdgeLabelProjections(X),t.calculateLabelAngles(X)}},y=0;y<i.length;y++)g(y);this.findHaystackPoints(o)}},Ue.getSegmentPoints=function(e){var t=e[0]._private.rscratch;if(this.recalculateRenderedStyle(e),t.edgeType==="segments")return Oo(t.segpts)},Ue.getControlPoints=function(e){var t=e[0]._private.rscratch;this.recalculateRenderedStyle(e);var n=t.edgeType;if(n==="bezier"||n==="multibezier"||n==="self"||n==="compound")return Oo(t.ctrlpts)},Ue.getEdgeMidpoint=function(e){var t=e[0]._private.rscratch;return this.recalculateRenderedStyle(e),{x:t.midX,y:t.midY}};var Th={manualEndptToPx:function(e,t){var n=e.position(),r=e.outerWidth(),a=e.outerHeight(),i=e._private.rscratch;if(t.value.length===2){var o=[t.pfValue[0],t.pfValue[1]];return t.units[0]==="%"&&(o[0]=o[0]*r),t.units[1]==="%"&&(o[1]=o[1]*a),o[0]+=n.x,o[1]+=n.y,o}var s=t.pfValue[0];s=-Math.PI/2+s;var l=2*Math.max(r,a),u=[n.x+Math.cos(s)*l,n.y+Math.sin(s)*l];return this.nodeShapes[this.getNodeShape(e)].intersectLine(n.x,n.y,r,a,u[0],u[1],0,e.pstyle("corner-radius").value==="auto"?"auto":e.pstyle("corner-radius").pfValue,i)},findEndpoints:function(e){var t,n,r,a,i,o=this,s=e.source()[0],l=e.target()[0],u=s.position(),c=l.position(),d=e.pstyle("target-arrow-shape").value,h=e.pstyle("source-arrow-shape").value,p=e.pstyle("target-distance-from-node").pfValue,f=e.pstyle("source-distance-from-node").pfValue,v=s._private.rscratch,m=l._private.rscratch,g=e.pstyle("curve-style").value,y=e._private.rscratch,b=y.edgeType,k=b==="self"||b==="compound",x=b==="bezier"||b==="multibezier"||k,w=b!=="bezier",E=b==="straight"||b==="segments",C=b==="segments",S=x||w||E,P=k||g==="taxi",_=e.pstyle("source-endpoint"),A=P?"outside-to-node":_.value,B=s.pstyle("corner-radius").value==="auto"?"auto":s.pstyle("corner-radius").pfValue,O=e.pstyle("target-endpoint"),N=P?"outside-to-node":O.value,V=l.pstyle("corner-radius").value==="auto"?"auto":l.pstyle("corner-radius").pfValue;if(y.srcManEndpt=_,y.tgtManEndpt=O,x){var M=[y.ctrlpts[0],y.ctrlpts[1]];n=w?[y.ctrlpts[y.ctrlpts.length-2],y.ctrlpts[y.ctrlpts.length-1]]:M,r=M}else if(E){var D=C?y.segpts.slice(0,2):[c.x,c.y];n=C?y.segpts.slice(y.segpts.length-2):[u.x,u.y],r=D}if(N==="inside-to-node")t=[c.x,c.y];else if(O.units)t=this.manualEndptToPx(l,O);else if(N==="outside-to-line")t=y.tgtIntn;else if(N==="outside-to-node"||N==="outside-to-node-or-label"?a=n:N!=="outside-to-line"&&N!=="outside-to-line-or-label"||(a=[u.x,u.y]),t=o.nodeShapes[this.getNodeShape(l)].intersectLine(c.x,c.y,l.outerWidth(),l.outerHeight(),a[0],a[1],0,V,m),N==="outside-to-node-or-label"||N==="outside-to-line-or-label"){var I=l._private.rscratch,z=I.labelWidth,q=I.labelHeight,j=I.labelX,U=I.labelY,K=z/2,X=q/2,H=l.pstyle("text-valign").value;H==="top"?U-=X:H==="bottom"&&(U+=X);var W=l.pstyle("text-halign").value;W==="left"?j-=K:W==="right"&&(j+=K);var Q=ur(a[0],a[1],[j-K,U-X,j+K,U-X,j+K,U+X,j-K,U+X],c.x,c.y);if(Q.length>0){var te=u,ie=$t(te,Cn(t)),se=$t(te,Cn(Q)),le=ie;se<ie&&(t=Q,le=se),Q.length>2&&$t(te,{x:Q[2],y:Q[3]})<le&&(t=[Q[2],Q[3]])}}var fe=Er(t,n,o.arrowShapes[d].spacing(e)+p),$=Er(t,n,o.arrowShapes[d].gap(e)+p);if(y.endX=$[0],y.endY=$[1],y.arrowEndX=fe[0],y.arrowEndY=fe[1],A==="inside-to-node")t=[u.x,u.y];else if(_.units)t=this.manualEndptToPx(s,_);else if(A==="outside-to-line")t=y.srcIntn;else if(A==="outside-to-node"||A==="outside-to-node-or-label"?i=r:A!=="outside-to-line"&&A!=="outside-to-line-or-label"||(i=[c.x,c.y]),t=o.nodeShapes[this.getNodeShape(s)].intersectLine(u.x,u.y,s.outerWidth(),s.outerHeight(),i[0],i[1],0,B,v),A==="outside-to-node-or-label"||A==="outside-to-line-or-label"){var ne=s._private.rscratch,T=ne.labelWidth,R=ne.labelHeight,L=ne.labelX,Y=ne.labelY,F=T/2,J=R/2,G=s.pstyle("text-valign").value;G==="top"?Y-=J:G==="bottom"&&(Y+=J);var Z=s.pstyle("text-halign").value;Z==="left"?L-=F:Z==="right"&&(L+=F);var ee=ur(i[0],i[1],[L-F,Y-J,L+F,Y-J,L+F,Y+J,L-F,Y+J],u.x,u.y);if(ee.length>0){var oe=c,ue=$t(oe,Cn(t)),re=$t(oe,Cn(ee)),de=ue;re<ue&&(t=[ee[0],ee[1]],de=re),ee.length>2&&$t(oe,{x:ee[2],y:ee[3]})<de&&(t=[ee[2],ee[3]])}}var pe=Er(t,r,o.arrowShapes[h].spacing(e)+f),ve=Er(t,r,o.arrowShapes[h].gap(e)+f);y.startX=ve[0],y.startY=ve[1],y.arrowStartX=pe[0],y.arrowStartY=pe[1],S&&(ae(y.startX)&&ae(y.startY)&&ae(y.endX)&&ae(y.endY)?y.badLine=!1:y.badLine=!0)},getSourceEndpoint:function(e){var t=e[0]._private.rscratch;return this.recalculateRenderedStyle(e),t.edgeType==="haystack"?{x:t.haystackPts[0],y:t.haystackPts[1]}:{x:t.arrowStartX,y:t.arrowStartY}},getTargetEndpoint:function(e){var t=e[0]._private.rscratch;return this.recalculateRenderedStyle(e),t.edgeType==="haystack"?{x:t.haystackPts[2],y:t.haystackPts[3]}:{x:t.arrowEndX,y:t.arrowEndY}}},$a={};function kh(e,t,n){for(var r=function(s,l,u,c){return Oe(s,l,u,c)},a=t._private.rstyle.bezierPts,i=0;i<e.bezierProjPcts.length;i++){var o=e.bezierProjPcts[i];a.push({x:r(n[0],n[2],n[4],o),y:r(n[1],n[3],n[5],o)})}}$a.storeEdgeProjections=function(e){var t=e._private,n=t.rscratch,r=n.edgeType;if(t.rstyle.bezierPts=null,t.rstyle.linePts=null,t.rstyle.haystackPts=null,r==="multibezier"||r==="bezier"||r==="self"||r==="compound"){t.rstyle.bezierPts=[];for(var a=0;a+5<n.allpts.length;a+=4)kh(this,e,n.allpts.slice(a,a+6))}else if(r==="segments"){var i=t.rstyle.linePts=[];for(a=0;a+1<n.allpts.length;a+=2)i.push({x:n.allpts[a],y:n.allpts[a+1]})}else if(r==="haystack"){var o=n.haystackPts;t.rstyle.haystackPts=[{x:o[0],y:o[1]},{x:o[2],y:o[3]}]}t.rstyle.arrowWidth=this.getArrowWidth(e.pstyle("width").pfValue,e.pstyle("arrow-scale").value)*this.arrowShapeWidth},$a.recalculateEdgeProjections=function(e){this.findEdgeControlPoints(e)};var Pt={recalculateNodeLabelProjection:function(e){var t=e.pstyle("label").strValue;if(!Xt(t)){var n,r,a=e._private,i=e.width(),o=e.height(),s=e.padding(),l=e.position(),u=e.pstyle("text-halign").strValue,c=e.pstyle("text-valign").strValue,d=a.rscratch,h=a.rstyle;switch(u){case"left":n=l.x-i/2-s;break;case"right":n=l.x+i/2+s;break;default:n=l.x}switch(c){case"top":r=l.y-o/2-s;break;case"bottom":r=l.y+o/2+s;break;default:r=l.y}d.labelX=n,d.labelY=r,h.labelX=n,h.labelY=r,this.calculateLabelAngles(e),this.applyLabelDimensions(e)}}},hl=function(e,t){var n=Math.atan(t/e);return e===0&&n<0&&(n*=-1),n},zo=function(e,t){var n=t.x-e.x,r=t.y-e.y;return hl(n,r)};Pt.recalculateEdgeLabelProjections=function(e){var t,n=e._private,r=n.rscratch,a=this,i={mid:e.pstyle("label").strValue,source:e.pstyle("source-label").strValue,target:e.pstyle("target-label").strValue};if(i.mid||i.source||i.target){t={x:r.midX,y:r.midY};var o=function(c,d,h){At(n.rscratch,c,d,h),At(n.rstyle,c,d,h)};o("labelX",null,t.x),o("labelY",null,t.y);var s=hl(r.midDispX,r.midDispY);o("labelAutoAngle",null,s);var l=function c(){if(c.cache)return c.cache;for(var d=[],h=0;h+5<r.allpts.length;h+=4){var p={x:r.allpts[h],y:r.allpts[h+1]},f={x:r.allpts[h+2],y:r.allpts[h+3]},v={x:r.allpts[h+4],y:r.allpts[h+5]};d.push({p0:p,p1:f,p2:v,startDist:0,length:0,segments:[]})}var m=n.rstyle.bezierPts,g=a.bezierProjPcts.length;function y(E,C,S,P,_){var A=rn(C,S),B=E.segments[E.segments.length-1],O={p0:C,p1:S,t0:P,t1:_,startDist:B?B.startDist+B.length:0,length:A};E.segments.push(O),E.length+=A}for(var b=0;b<d.length;b++){var k=d[b],x=d[b-1];x&&(k.startDist=x.startDist+x.length),y(k,k.p0,m[b*g],0,a.bezierProjPcts[0]);for(var w=0;w<g-1;w++)y(k,m[b*g+w],m[b*g+w+1],a.bezierProjPcts[w],a.bezierProjPcts[w+1]);y(k,m[b*g+g-1],k.p2,a.bezierProjPcts[g-1],1)}return c.cache=d},u=function(c){var d,h=c==="source";if(i[c]){var p=e.pstyle(c+"-text-offset").pfValue;switch(r.edgeType){case"self":case"compound":case"bezier":case"multibezier":for(var f,v=l(),m=0,g=0,y=0;y<v.length;y++){for(var b=v[h?y:v.length-1-y],k=0;k<b.segments.length;k++){var x=b.segments[h?k:b.segments.length-1-k],w=y===v.length-1&&k===b.segments.length-1;if(m=g,(g+=x.length)>=p||w){f={cp:b,segment:x};break}}if(f)break}var E=f.cp,C=f.segment,S=(p-m)/C.length,P=C.t1-C.t0,_=h?C.t0+P*S:C.t1-P*S;_=Gn(0,_,1),t=Dn(E.p0,E.p1,E.p2,_),d=function(z,q,j,U){var K=Gn(0,U-.001,1),X=Gn(0,U+.001,1),H=Dn(z,q,j,K),W=Dn(z,q,j,X);return zo(H,W)}(E.p0,E.p1,E.p2,_);break;case"straight":case"segments":case"haystack":for(var A,B,O,N,V=0,M=r.allpts.length,D=0;D+3<M&&(h?(O={x:r.allpts[D],y:r.allpts[D+1]},N={x:r.allpts[D+2],y:r.allpts[D+3]}):(O={x:r.allpts[M-2-D],y:r.allpts[M-1-D]},N={x:r.allpts[M-4-D],y:r.allpts[M-3-D]}),B=V,!((V+=A=rn(O,N))>=p));D+=2);var I=(p-B)/A;I=Gn(0,I,1),t=function(z,q,j,U){var K=q.x-z.x,X=q.y-z.y,H=rn(z,q),W=K/H,Q=X/H;return j=j??0,U=U??j*H,{x:z.x+W*U,y:z.y+Q*U}}(O,N,I),d=zo(O,N)}o("labelX",c,t.x),o("labelY",c,t.y),o("labelAutoAngle",c,d)}};u("source"),u("target"),this.applyLabelDimensions(e)}},Pt.applyLabelDimensions=function(e){this.applyPrefixedLabelDimensions(e),e.isEdge()&&(this.applyPrefixedLabelDimensions(e,"source"),this.applyPrefixedLabelDimensions(e,"target"))},Pt.applyPrefixedLabelDimensions=function(e,t){var n=e._private,r=this.getLabelText(e,t),a=this.calculateLabelDimensions(e,r),i=e.pstyle("line-height").pfValue,o=e.pstyle("text-wrap").strValue,s=ft(n.rscratch,"labelWrapCachedLines",t)||[],l=o!=="wrap"?1:Math.max(s.length,1),u=a.height/l,c=u*i,d=a.width,h=a.height+(l-1)*(i-1)*u;At(n.rstyle,"labelWidth",t,d),At(n.rscratch,"labelWidth",t,d),At(n.rstyle,"labelHeight",t,h),At(n.rscratch,"labelHeight",t,h),At(n.rscratch,"labelLineHeight",t,c)},Pt.getLabelText=function(e,t){var n=e._private,r=t?t+"-":"",a=e.pstyle(r+"label").strValue,i=e.pstyle("text-transform").value,o=function(O,N){return N?(At(n.rscratch,O,t,N),N):ft(n.rscratch,O,t)};if(!a)return"";i=="none"||(i=="uppercase"?a=a.toUpperCase():i=="lowercase"&&(a=a.toLowerCase()));var s=e.pstyle("text-wrap").value;if(s==="wrap"){var l=o("labelKey");if(l!=null&&o("labelWrapKey")===l)return o("labelWrapCachedText");for(var u=a.split(`
`),c=e.pstyle("text-max-width").pfValue,d=e.pstyle("text-overflow-wrap").value==="anywhere",h=[],p=/[\s\u200b]+|$/g,f=0;f<u.length;f++){var v=u[f],m=this.calculateLabelDimensions(e,v).width;if(d){var g=v.split("").join("​");v=g}if(m>c){var y,b="",k=0,x=ut(v.matchAll(p));try{for(x.s();!(y=x.n()).done;){var w=y.value,E=w[0],C=v.substring(k,w.index);k=w.index+E.length;var S=b.length===0?C:b+C+E;this.calculateLabelDimensions(e,S).width<=c?b+=C+E:(b&&h.push(b),b=C+E)}}catch(O){x.e(O)}finally{x.f()}b.match(/^[\s\u200b]+$/)||h.push(b)}else h.push(v)}o("labelWrapCachedLines",h),a=o("labelWrapCachedText",h.join(`
`)),o("labelWrapKey",l)}else if(s==="ellipsis"){var P=e.pstyle("text-max-width").pfValue,_="",A=!1;if(this.calculateLabelDimensions(e,a).width<P)return a;for(var B=0;B<a.length&&!(this.calculateLabelDimensions(e,_+a[B]+"…").width>P);B++)_+=a[B],B===a.length-1&&(A=!0);return A||(_+="…"),_}return a},Pt.getLabelJustification=function(e){var t=e.pstyle("text-justification").strValue,n=e.pstyle("text-halign").strValue;if(t!=="auto")return t;if(!e.isNode())return"center";switch(n){case"left":return"right";case"right":return"left";default:return"center"}},Pt.calculateLabelDimensions=function(e,t){var n=this,r=n.cy.window().document,a=Yt(t,e._private.labelDimsKey),i=n.labelDimCache||(n.labelDimCache=[]),o=i[a];if(o!=null)return o;var s=e.pstyle("font-style").strValue,l=e.pstyle("font-size").pfValue,u=e.pstyle("font-family").strValue,c=e.pstyle("font-weight").strValue,d=this.labelCalcCanvas,h=this.labelCalcCanvasContext;if(!d){d=this.labelCalcCanvas=r.createElement("canvas"),h=this.labelCalcCanvasContext=d.getContext("2d");var p=d.style;p.position="absolute",p.left="-9999px",p.top="-9999px",p.zIndex="-1",p.visibility="hidden",p.pointerEvents="none"}h.font="".concat(s," ").concat(c," ").concat(l,"px ").concat(u);for(var f=0,v=0,m=t.split(`
`),g=0;g<m.length;g++){var y=m[g],b=h.measureText(y),k=Math.ceil(b.width),x=l;f=Math.max(k,f),v+=x}return f+=0,v+=0,i[a]={width:f,height:v}},Pt.calculateLabelAngle=function(e,t){var n=e._private.rscratch,r=e.isEdge(),a=t?t+"-":"",i=e.pstyle(a+"text-rotation"),o=i.strValue;return o==="none"?0:r&&o==="autorotate"?n.labelAutoAngle:o==="autorotate"?0:i.pfValue},Pt.calculateLabelAngles=function(e){var t=this,n=e.isEdge(),r=e._private.rscratch;r.labelAngle=t.calculateLabelAngle(e),n&&(r.sourceLabelAngle=t.calculateLabelAngle(e,"source"),r.targetLabelAngle=t.calculateLabelAngle(e,"target"))};var fl={},Vo=!1;fl.getNodeShape=function(e){var t=e.pstyle("shape").value;if(t==="cutrectangle"&&(e.width()<28||e.height()<28))return Vo||(we("The `cutrectangle` node shape can not be used at small sizes so `rectangle` is used instead"),Vo=!0),"rectangle";if(e.isParent())return t==="rectangle"||t==="roundrectangle"||t==="round-rectangle"||t==="cutrectangle"||t==="cut-rectangle"||t==="barrel"?t:"rectangle";if(t==="polygon"){var n=e.pstyle("shape-polygon-points").value;return this.nodeShapes.makePolygon(n).name}return t};var Ch={registerCalculationListeners:function(){var e=this.cy,t=e.collection(),n=this,r=function(i){var o=!(arguments.length>1&&arguments[1]!==void 0)||arguments[1];if(t.merge(i),o)for(var s=0;s<i.length;s++){var l=i[s]._private.rstyle;l.clean=!1,l.cleanConnected=!1}};n.binder(e).on("bounds.* dirty.*",function(i){var o=i.target;r(o)}).on("style.* background.*",function(i){var o=i.target;r(o,!1)});var a=function(i){if(i){var o=n.onUpdateEleCalcsFns;t.cleanStyle();for(var s=0;s<t.length;s++){var l=t[s],u=l._private.rstyle;l.isNode()&&!u.cleanConnected&&(r(l.connectedEdges()),u.cleanConnected=!0)}if(o)for(var c=0;c<o.length;c++)(0,o[c])(i,t);n.recalculateRenderedStyle(t),t=e.collection()}};n.flushRenderedStyleQueue=function(){a(!0)},n.beforeRender(a,n.beforeRenderPriorities.eleCalcs)},onUpdateEleCalcs:function(e){(this.onUpdateEleCalcsFns=this.onUpdateEleCalcsFns||[]).push(e)},recalculateRenderedStyle:function(e,t){var n=function(g){return g._private.rstyle.cleanConnected},r=[],a=[];if(!this.destroyed){t===void 0&&(t=!0);for(var i=0;i<e.length;i++){var o=e[i],s=o._private,l=s.rstyle;!o.isEdge()||n(o.source())&&n(o.target())||(l.clean=!1),t&&l.clean||o.removed()||o.pstyle("display").value!=="none"&&(s.group==="nodes"?a.push(o):r.push(o),l.clean=!0)}for(var u=0;u<a.length;u++){var c=a[u],d=c._private.rstyle,h=c.position();this.recalculateNodeLabelProjection(c),d.nodeX=h.x,d.nodeY=h.y,d.nodeW=c.pstyle("width").pfValue,d.nodeH=c.pstyle("height").pfValue}this.recalculateEdgeProjections(r);for(var p=0;p<r.length;p++){var f=r[p]._private,v=f.rstyle,m=f.rscratch;v.srcX=m.arrowStartX,v.srcY=m.arrowStartY,v.tgtX=m.arrowEndX,v.tgtY=m.arrowEndY,v.midX=m.midX,v.midY=m.midY,v.labelAngle=m.labelAngle,v.sourceLabelAngle=m.sourceLabelAngle,v.targetLabelAngle=m.targetLabelAngle}}}},Ph={updateCachedGrabbedEles:function(){var e=this.cachedZSortedEles;if(e){e.drag=[],e.nondrag=[];for(var t=[],n=0;n<e.length;n++){var r=(a=e[n])._private.rscratch;a.grabbed()&&!a.isParent()?t.push(a):r.inDragLayer?e.drag.push(a):e.nondrag.push(a)}for(n=0;n<t.length;n++){var a=t[n];e.drag.push(a)}}},invalidateCachedZSortedEles:function(){this.cachedZSortedEles=null},getCachedZSortedEles:function(e){if(e||!this.cachedZSortedEles){var t=this.cy.mutableElements().toArray();t.sort($s),t.interactive=t.filter(function(n){return n.interactive()}),this.cachedZSortedEles=t,this.updateCachedGrabbedEles()}else t=this.cachedZSortedEles;return t}},pl={};[wh,Ka,Ue,Th,$a,Pt,fl,Ch,Ph].forEach(function(e){he(pl,e)});var Sh={getCachedImage:function(e,t,n){var r=this.imageCache=this.imageCache||{},a=r[e];if(a)return a.image.complete||a.image.addEventListener("load",n),a.image;var i=(a=r[e]=r[e]||{}).image=new Image;i.addEventListener("load",n),i.addEventListener("error",function(){i.error=!0});var o="data:";return e.substring(0,5).toLowerCase()===o||(t=t==="null"?null:t,i.crossOrigin=t),i.src=e,i}},Jn={registerBinding:function(e,t,n,r){var a=Array.prototype.slice.apply(arguments,[1]);if(Array.isArray(e)){for(var i=[],o=0;o<e.length;o++){var s=e[o];if(s!==void 0){var l=this.binder(s);i.push(l.on.apply(l,a))}}return i}return(l=this.binder(e)).on.apply(l,a)}};Jn.binder=function(e){var t,n=this,r=n.cy.window(),a=e===r||e===r.document||e===r.document.body||(t=e,typeof HTMLElement<"u"&&t instanceof HTMLElement);if(n.supportsPassiveEvents==null){var i=!1;try{var o=Object.defineProperty({},"passive",{get:function(){return i=!0,!0}});r.addEventListener("test",null,o)}catch{}n.supportsPassiveEvents=i}var s=function(l,u,c){var d=Array.prototype.slice.call(arguments);return a&&n.supportsPassiveEvents&&(d[2]={capture:c!=null&&c,passive:!1,once:!1}),n.bindings.push({target:e,args:d}),(e.addEventListener||e.on).apply(e,d),this};return{on:s,addEventListener:s,addListener:s,bind:s}},Jn.nodeIsDraggable=function(e){return e&&e.isNode()&&!e.locked()&&e.grabbable()},Jn.nodeIsGrabbable=function(e){return this.nodeIsDraggable(e)&&e.interactive()},Jn.load=function(){var e=this,t=e.cy.window(),n=function(T){return T.selected()},r=function(T,R,L,Y){T==null&&(T=e.cy);for(var F=0;F<R.length;F++){var J=R[F];T.emit({originalEvent:L,type:J,position:Y})}},a=function(T){return T.shiftKey||T.metaKey||T.ctrlKey},i=function(T,R){var L=!0;if(e.cy.hasCompoundNodes()&&T&&T.pannable()){for(var Y=0;R&&Y<R.length;Y++)if((T=R[Y]).isNode()&&T.isParent()&&!T.pannable()){L=!1;break}}else L=!0;return L},o=function(T){T[0]._private.rscratch.inDragLayer=!0},s=function(T){T[0]._private.rscratch.isGrabTarget=!0},l=function(T,R){var L=R.addToList;L.has(T)||!T.grabbable()||T.locked()||(L.merge(T),function(Y){Y[0]._private.grabbed=!0}(T))},u=function(T,R){R=R||{};var L=T.cy().hasCompoundNodes();R.inDragLayer&&(T.forEach(o),T.neighborhood().stdFilter(function(Y){return!L||Y.isEdge()}).forEach(o)),R.addToList&&T.forEach(function(Y){l(Y,R)}),function(Y,F){if(Y.cy().hasCompoundNodes()&&(F.inDragLayer!=null||F.addToList!=null)){var J=Y.descendants();F.inDragLayer&&(J.forEach(o),J.connectedEdges().forEach(o)),F.addToList&&l(J,F)}}(T,R),h(T,{inDragLayer:R.inDragLayer}),e.updateCachedGrabbedEles()},c=u,d=function(T){T&&(e.getCachedZSortedEles().forEach(function(R){(function(L){L[0]._private.grabbed=!1})(R),function(L){L[0]._private.rscratch.inDragLayer=!1}(R),function(L){L[0]._private.rscratch.isGrabTarget=!1}(R)}),e.updateCachedGrabbedEles())},h=function(T,R){if((R.inDragLayer!=null||R.addToList!=null)&&T.cy().hasCompoundNodes()){var L=T.ancestors().orphans();if(!L.same(T)){var Y=L.descendants().spawnSelf().merge(L).unmerge(T).unmerge(T.descendants()),F=Y.connectedEdges();R.inDragLayer&&(F.forEach(o),Y.forEach(o)),R.addToList&&Y.forEach(function(J){l(J,R)})}}},p=function(){document.activeElement!=null&&document.activeElement.blur!=null&&document.activeElement.blur()},f=typeof MutationObserver<"u",v=typeof ResizeObserver<"u";f?(e.removeObserver=new MutationObserver(function(T){for(var R=0;R<T.length;R++){var L=T[R].removedNodes;if(L){for(var Y=0;Y<L.length;Y++)if(L[Y]===e.container){e.destroy();break}}}}),e.container.parentNode&&e.removeObserver.observe(e.container.parentNode,{childList:!0})):e.registerBinding(e.container,"DOMNodeRemoved",function(T){e.destroy()});var m=vr(function(){e.cy.resize()},100);f&&(e.styleObserver=new MutationObserver(m),e.styleObserver.observe(e.container,{attributes:!0})),e.registerBinding(t,"resize",m),v&&(e.resizeObserver=new ResizeObserver(m),e.resizeObserver.observe(e.container));var g=function(){e.invalidateContainerClientCoordsCache()};(function(T,R){for(;T!=null;)R(T),T=T.parentNode})(e.container,function(T){e.registerBinding(T,"transitionend",g),e.registerBinding(T,"animationend",g),e.registerBinding(T,"scroll",g)}),e.registerBinding(e.container,"contextmenu",function(T){T.preventDefault()});var y=function(T){for(var R=e.findContainerClientCoords(),L=R[0],Y=R[1],F=R[2],J=R[3],G=T.touches?T.touches:[T],Z=!1,ee=0;ee<G.length;ee++){var oe=G[ee];if(L<=oe.clientX&&oe.clientX<=L+F&&Y<=oe.clientY&&oe.clientY<=Y+J){Z=!0;break}}if(!Z)return!1;for(var ue=e.container,re=T.target.parentNode,de=!1;re;){if(re===ue){de=!0;break}re=re.parentNode}return!!de};e.registerBinding(e.container,"mousedown",function(T){if(y(T)&&(e.hoverData.which!==1||T.which===1)){T.preventDefault(),p(),e.hoverData.capture=!0,e.hoverData.which=T.which;var R=e.cy,L=[T.clientX,T.clientY],Y=e.projectIntoViewport(L[0],L[1]),F=e.selection,J=e.findNearestElements(Y[0],Y[1],!0,!1),G=J[0],Z=e.dragData.possibleDragElements;if(e.hoverData.mdownPos=Y,e.hoverData.mdownGPos=L,T.which==3){e.hoverData.cxtStarted=!0;var ee={originalEvent:T,type:"cxttapstart",position:{x:Y[0],y:Y[1]}};G?(G.activate(),G.emit(ee),e.hoverData.down=G):R.emit(ee),e.hoverData.downTime=new Date().getTime(),e.hoverData.cxtDragged=!1}else if(T.which==1){if(G&&G.activate(),G!=null&&e.nodeIsGrabbable(G)){var oe=function(re){return{originalEvent:T,type:re,position:{x:Y[0],y:Y[1]}}};if(s(G),G.selected()){Z=e.dragData.possibleDragElements=R.collection();var ue=R.$(function(re){return re.isNode()&&re.selected()&&e.nodeIsGrabbable(re)});u(ue,{addToList:Z}),G.emit(oe("grabon")),ue.forEach(function(re){re.emit(oe("grab"))})}else Z=e.dragData.possibleDragElements=R.collection(),c(G,{addToList:Z}),G.emit(oe("grabon")).emit(oe("grab"));e.redrawHint("eles",!0),e.redrawHint("drag",!0)}e.hoverData.down=G,e.hoverData.downs=J,e.hoverData.downTime=new Date().getTime(),r(G,["mousedown","tapstart","vmousedown"],T,{x:Y[0],y:Y[1]}),G==null?(F[4]=1,e.data.bgActivePosistion={x:Y[0],y:Y[1]},e.redrawHint("select",!0),e.redraw()):G.pannable()&&(F[4]=1),e.hoverData.tapholdCancelled=!1,clearTimeout(e.hoverData.tapholdTimeout),e.hoverData.tapholdTimeout=setTimeout(function(){if(!e.hoverData.tapholdCancelled){var re=e.hoverData.down;re?re.emit({originalEvent:T,type:"taphold",position:{x:Y[0],y:Y[1]}}):R.emit({originalEvent:T,type:"taphold",position:{x:Y[0],y:Y[1]}})}},e.tapholdDuration)}F[0]=F[2]=Y[0],F[1]=F[3]=Y[1]}},!1);var b,k,x,w=function(T){var R=T.getRootNode();if(R&&R.nodeType===11&&R.host!==void 0)return R}(e.container);e.registerBinding([t,w],"mousemove",function(T){if(e.hoverData.capture||y(T)){var R=!1,L=e.cy,Y=L.zoom(),F=[T.clientX,T.clientY],J=e.projectIntoViewport(F[0],F[1]),G=e.hoverData.mdownPos,Z=e.hoverData.mdownGPos,ee=e.selection,oe=null;e.hoverData.draggingEles||e.hoverData.dragging||e.hoverData.selecting||(oe=e.findNearestElement(J[0],J[1],!0,!1));var ue,re=e.hoverData.last,de=e.hoverData.down,pe=[J[0]-ee[2],J[1]-ee[3]],ve=e.dragData.possibleDragElements;if(Z){var Ee=F[0]-Z[0],ke=Ee*Ee,Fe=F[1]-Z[1],Se=ke+Fe*Fe;e.hoverData.isOverThresholdDrag=ue=Se>=e.desktopTapThreshold2}var gt=a(T);ue&&(e.hoverData.tapholdCancelled=!0),R=!0,r(oe,["mousemove","vmousemove","tapdrag"],T,{x:J[0],y:J[1]});var vt=function(){e.data.bgActivePosistion=void 0,e.hoverData.selecting||L.emit({originalEvent:T,type:"boxstart",position:{x:J[0],y:J[1]}}),ee[4]=1,e.hoverData.selecting=!0,e.redrawHint("select",!0),e.redraw()};if(e.hoverData.which===3){if(ue){var Ze={originalEvent:T,type:"cxtdrag",position:{x:J[0],y:J[1]}};de?de.emit(Ze):L.emit(Ze),e.hoverData.cxtDragged=!0,e.hoverData.cxtOver&&oe===e.hoverData.cxtOver||(e.hoverData.cxtOver&&e.hoverData.cxtOver.emit({originalEvent:T,type:"cxtdragout",position:{x:J[0],y:J[1]}}),e.hoverData.cxtOver=oe,oe&&oe.emit({originalEvent:T,type:"cxtdragover",position:{x:J[0],y:J[1]}}))}}else if(e.hoverData.dragging){if(R=!0,L.panningEnabled()&&L.userPanningEnabled()){var Le;if(e.hoverData.justStartedPan){var Dt=e.hoverData.mdownPos;Le={x:(J[0]-Dt[0])*Y,y:(J[1]-Dt[1])*Y},e.hoverData.justStartedPan=!1}else Le={x:pe[0]*Y,y:pe[1]*Y};L.panBy(Le),L.emit("dragpan"),e.hoverData.dragged=!0}J=e.projectIntoViewport(T.clientX,T.clientY)}else if(ee[4]!=1||de!=null&&!de.pannable()){if(de&&de.pannable()&&de.active()&&de.unactivate(),de&&de.grabbed()||oe==re||(re&&r(re,["mouseout","tapdragout"],T,{x:J[0],y:J[1]}),oe&&r(oe,["mouseover","tapdragover"],T,{x:J[0],y:J[1]}),e.hoverData.last=oe),de)if(ue){if(L.boxSelectionEnabled()&&gt)de&&de.grabbed()&&(d(ve),de.emit("freeon"),ve.emit("free"),e.dragData.didDrag&&(de.emit("dragfreeon"),ve.emit("dragfree"))),vt();else if(de&&de.grabbed()&&e.nodeIsDraggable(de)){var nt=!e.dragData.didDrag;nt&&e.redrawHint("eles",!0),e.dragData.didDrag=!0,e.hoverData.draggingEles||u(ve,{inDragLayer:!0});var $e={x:0,y:0};if(ae(pe[0])&&ae(pe[1])&&($e.x+=pe[0],$e.y+=pe[1],nt)){var ct=e.hoverData.dragDelta;ct&&ae(ct[0])&&ae(ct[1])&&($e.x+=ct[0],$e.y+=ct[1])}e.hoverData.draggingEles=!0,ve.silentShift($e).emit("position drag"),e.redrawHint("drag",!0),e.redraw()}}else(function(){var it=e.hoverData.dragDelta=e.hoverData.dragDelta||[];it.length===0?(it.push(pe[0]),it.push(pe[1])):(it[0]+=pe[0],it[1]+=pe[1])})();R=!0}else ue&&(e.hoverData.dragging||!L.boxSelectionEnabled()||!gt&&L.panningEnabled()&&L.userPanningEnabled()?!e.hoverData.selecting&&L.panningEnabled()&&L.userPanningEnabled()&&i(de,e.hoverData.downs)&&(e.hoverData.dragging=!0,e.hoverData.justStartedPan=!0,ee[4]=0,e.data.bgActivePosistion=Cn(G),e.redrawHint("select",!0),e.redraw()):vt(),de&&de.pannable()&&de.active()&&de.unactivate());return ee[2]=J[0],ee[3]=J[1],R?(T.stopPropagation&&T.stopPropagation(),T.preventDefault&&T.preventDefault(),!1):void 0}},!1),e.registerBinding(t,"mouseup",function(T){if((e.hoverData.which!==1||T.which===1||!e.hoverData.capture)&&e.hoverData.capture){e.hoverData.capture=!1;var R=e.cy,L=e.projectIntoViewport(T.clientX,T.clientY),Y=e.selection,F=e.findNearestElement(L[0],L[1],!0,!1),J=e.dragData.possibleDragElements,G=e.hoverData.down,Z=a(T);if(e.data.bgActivePosistion&&(e.redrawHint("select",!0),e.redraw()),e.hoverData.tapholdCancelled=!0,e.data.bgActivePosistion=void 0,G&&G.unactivate(),e.hoverData.which===3){var ee={originalEvent:T,type:"cxttapend",position:{x:L[0],y:L[1]}};if(G?G.emit(ee):R.emit(ee),!e.hoverData.cxtDragged){var oe={originalEvent:T,type:"cxttap",position:{x:L[0],y:L[1]}};G?G.emit(oe):R.emit(oe)}e.hoverData.cxtDragged=!1,e.hoverData.which=null}else if(e.hoverData.which===1){if(r(F,["mouseup","tapend","vmouseup"],T,{x:L[0],y:L[1]}),e.dragData.didDrag||e.hoverData.dragged||e.hoverData.selecting||e.hoverData.isOverThresholdDrag||(r(G,["click","tap","vclick"],T,{x:L[0],y:L[1]}),k=!1,T.timeStamp-x<=R.multiClickDebounceTime()?(b&&clearTimeout(b),k=!0,x=null,r(G,["dblclick","dbltap","vdblclick"],T,{x:L[0],y:L[1]})):(b=setTimeout(function(){k||r(G,["oneclick","onetap","voneclick"],T,{x:L[0],y:L[1]})},R.multiClickDebounceTime()),x=T.timeStamp)),G!=null||e.dragData.didDrag||e.hoverData.selecting||e.hoverData.dragged||a(T)||(R.$(n).unselect(["tapunselect"]),J.length>0&&e.redrawHint("eles",!0),e.dragData.possibleDragElements=J=R.collection()),F!=G||e.dragData.didDrag||e.hoverData.selecting||F!=null&&F._private.selectable&&(e.hoverData.dragging||(R.selectionType()==="additive"||Z?F.selected()?F.unselect(["tapunselect"]):F.select(["tapselect"]):Z||(R.$(n).unmerge(F).unselect(["tapunselect"]),F.select(["tapselect"]))),e.redrawHint("eles",!0)),e.hoverData.selecting){var ue=R.collection(e.getAllInBox(Y[0],Y[1],Y[2],Y[3]));e.redrawHint("select",!0),ue.length>0&&e.redrawHint("eles",!0),R.emit({type:"boxend",originalEvent:T,position:{x:L[0],y:L[1]}});var re=function(pe){return pe.selectable()&&!pe.selected()};R.selectionType()==="additive"||Z||R.$(n).unmerge(ue).unselect(),ue.emit("box").stdFilter(re).select().emit("boxselect"),e.redraw()}if(e.hoverData.dragging&&(e.hoverData.dragging=!1,e.redrawHint("select",!0),e.redrawHint("eles",!0),e.redraw()),!Y[4]){e.redrawHint("drag",!0),e.redrawHint("eles",!0);var de=G&&G.grabbed();d(J),de&&(G.emit("freeon"),J.emit("free"),e.dragData.didDrag&&(G.emit("dragfreeon"),J.emit("dragfree")))}}Y[4]=0,e.hoverData.down=null,e.hoverData.cxtStarted=!1,e.hoverData.draggingEles=!1,e.hoverData.selecting=!1,e.hoverData.isOverThresholdDrag=!1,e.dragData.didDrag=!1,e.hoverData.dragged=!1,e.hoverData.dragDelta=[],e.hoverData.mdownPos=null,e.hoverData.mdownGPos=null,e.hoverData.which=null}},!1);var E,C,S,P,_,A,B,O,N,V,M,D,I,z=function(T){if(!e.scrollingPage){var R=e.cy,L=R.zoom(),Y=R.pan(),F=e.projectIntoViewport(T.clientX,T.clientY),J=[F[0]*L+Y.x,F[1]*L+Y.y];if(e.hoverData.draggingEles||e.hoverData.dragging||e.hoverData.cxtStarted||e.selection[4]!==0)T.preventDefault();else if(R.panningEnabled()&&R.userPanningEnabled()&&R.zoomingEnabled()&&R.userZoomingEnabled()){var G;T.preventDefault(),e.data.wheelZooming=!0,clearTimeout(e.data.wheelTimeout),e.data.wheelTimeout=setTimeout(function(){e.data.wheelZooming=!1,e.redrawHint("eles",!0),e.redraw()},150),G=T.deltaY!=null?T.deltaY/-250:T.wheelDeltaY!=null?T.wheelDeltaY/1e3:T.wheelDelta/1e3,G*=e.wheelSensitivity,T.deltaMode===1&&(G*=33);var Z=R.zoom()*Math.pow(10,G);T.type==="gesturechange"&&(Z=e.gestureStartZoom*T.scale),R.zoom({level:Z,renderedPosition:{x:J[0],y:J[1]}}),R.emit(T.type==="gesturechange"?"pinchzoom":"scrollzoom")}}};e.registerBinding(e.container,"wheel",z,!0),e.registerBinding(t,"scroll",function(T){e.scrollingPage=!0,clearTimeout(e.scrollingPageTimeout),e.scrollingPageTimeout=setTimeout(function(){e.scrollingPage=!1},250)},!0),e.registerBinding(e.container,"gesturestart",function(T){e.gestureStartZoom=e.cy.zoom(),e.hasTouchStarted||T.preventDefault()},!0),e.registerBinding(e.container,"gesturechange",function(T){e.hasTouchStarted||z(T)},!0),e.registerBinding(e.container,"mouseout",function(T){var R=e.projectIntoViewport(T.clientX,T.clientY);e.cy.emit({originalEvent:T,type:"mouseout",position:{x:R[0],y:R[1]}})},!1),e.registerBinding(e.container,"mouseover",function(T){var R=e.projectIntoViewport(T.clientX,T.clientY);e.cy.emit({originalEvent:T,type:"mouseover",position:{x:R[0],y:R[1]}})},!1);var q,j,U,K,X,H,W,Q=function(T,R,L,Y){return Math.sqrt((L-T)*(L-T)+(Y-R)*(Y-R))},te=function(T,R,L,Y){return(L-T)*(L-T)+(Y-R)*(Y-R)};if(e.registerBinding(e.container,"touchstart",q=function(T){if(e.hasTouchStarted=!0,y(T)){p(),e.touchData.capture=!0,e.data.bgActivePosistion=void 0;var R=e.cy,L=e.touchData.now,Y=e.touchData.earlier;if(T.touches[0]){var F=e.projectIntoViewport(T.touches[0].clientX,T.touches[0].clientY);L[0]=F[0],L[1]=F[1]}if(T.touches[1]&&(F=e.projectIntoViewport(T.touches[1].clientX,T.touches[1].clientY),L[2]=F[0],L[3]=F[1]),T.touches[2]&&(F=e.projectIntoViewport(T.touches[2].clientX,T.touches[2].clientY),L[4]=F[0],L[5]=F[1]),T.touches[1]){e.touchData.singleTouchMoved=!0,d(e.dragData.touchDragEles);var J=e.findContainerClientCoords();N=J[0],V=J[1],M=J[2],D=J[3],E=T.touches[0].clientX-N,C=T.touches[0].clientY-V,S=T.touches[1].clientX-N,P=T.touches[1].clientY-V,I=0<=E&&E<=M&&0<=S&&S<=M&&0<=C&&C<=D&&0<=P&&P<=D;var G=R.pan(),Z=R.zoom();if(_=Q(E,C,S,P),A=te(E,C,S,P),O=[((B=[(E+S)/2,(C+P)/2])[0]-G.x)/Z,(B[1]-G.y)/Z],A<4e4&&!T.touches[2]){var ee=e.findNearestElement(L[0],L[1],!0,!0),oe=e.findNearestElement(L[2],L[3],!0,!0);return ee&&ee.isNode()?(ee.activate().emit({originalEvent:T,type:"cxttapstart",position:{x:L[0],y:L[1]}}),e.touchData.start=ee):oe&&oe.isNode()?(oe.activate().emit({originalEvent:T,type:"cxttapstart",position:{x:L[0],y:L[1]}}),e.touchData.start=oe):R.emit({originalEvent:T,type:"cxttapstart",position:{x:L[0],y:L[1]}}),e.touchData.start&&(e.touchData.start._private.grabbed=!1),e.touchData.cxt=!0,e.touchData.cxtDragged=!1,e.data.bgActivePosistion=void 0,void e.redraw()}}if(T.touches[2])R.boxSelectionEnabled()&&T.preventDefault();else if(!T.touches[1]){if(T.touches[0]){var ue=e.findNearestElements(L[0],L[1],!0,!0),re=ue[0];if(re!=null&&(re.activate(),e.touchData.start=re,e.touchData.starts=ue,e.nodeIsGrabbable(re))){var de=e.dragData.touchDragEles=R.collection(),pe=null;e.redrawHint("eles",!0),e.redrawHint("drag",!0),re.selected()?(pe=R.$(function(Se){return Se.selected()&&e.nodeIsGrabbable(Se)}),u(pe,{addToList:de})):c(re,{addToList:de}),s(re);var ve=function(Se){return{originalEvent:T,type:Se,position:{x:L[0],y:L[1]}}};re.emit(ve("grabon")),pe?pe.forEach(function(Se){Se.emit(ve("grab"))}):re.emit(ve("grab"))}r(re,["touchstart","tapstart","vmousedown"],T,{x:L[0],y:L[1]}),re==null&&(e.data.bgActivePosistion={x:F[0],y:F[1]},e.redrawHint("select",!0),e.redraw()),e.touchData.singleTouchMoved=!1,e.touchData.singleTouchStartTime=+new Date,clearTimeout(e.touchData.tapholdTimeout),e.touchData.tapholdTimeout=setTimeout(function(){e.touchData.singleTouchMoved!==!1||e.pinching||e.touchData.selecting||r(e.touchData.start,["taphold"],T,{x:L[0],y:L[1]})},e.tapholdDuration)}}if(T.touches.length>=1){for(var Ee=e.touchData.startPosition=[null,null,null,null,null,null],ke=0;ke<L.length;ke++)Ee[ke]=Y[ke]=L[ke];var Fe=T.touches[0];e.touchData.startGPosition=[Fe.clientX,Fe.clientY]}}},!1),e.registerBinding(t,"touchmove",j=function(T){var R=e.touchData.capture;if(R||y(T)){var L=e.selection,Y=e.cy,F=e.touchData.now,J=e.touchData.earlier,G=Y.zoom();if(T.touches[0]){var Z=e.projectIntoViewport(T.touches[0].clientX,T.touches[0].clientY);F[0]=Z[0],F[1]=Z[1]}T.touches[1]&&(Z=e.projectIntoViewport(T.touches[1].clientX,T.touches[1].clientY),F[2]=Z[0],F[3]=Z[1]),T.touches[2]&&(Z=e.projectIntoViewport(T.touches[2].clientX,T.touches[2].clientY),F[4]=Z[0],F[5]=Z[1]);var ee,oe=e.touchData.startGPosition;if(R&&T.touches[0]&&oe){for(var ue=[],re=0;re<F.length;re++)ue[re]=F[re]-J[re];var de=T.touches[0].clientX-oe[0],pe=de*de,ve=T.touches[0].clientY-oe[1];ee=pe+ve*ve>=e.touchTapThreshold2}if(R&&e.touchData.cxt){T.preventDefault();var Ee=T.touches[0].clientX-N,ke=T.touches[0].clientY-V,Fe=T.touches[1].clientX-N,Se=T.touches[1].clientY-V,gt=te(Ee,ke,Fe,Se);if(gt/A>=2.25||gt>=22500){e.touchData.cxt=!1,e.data.bgActivePosistion=void 0,e.redrawHint("select",!0);var vt={originalEvent:T,type:"cxttapend",position:{x:F[0],y:F[1]}};e.touchData.start?(e.touchData.start.unactivate().emit(vt),e.touchData.start=null):Y.emit(vt)}}if(R&&e.touchData.cxt){vt={originalEvent:T,type:"cxtdrag",position:{x:F[0],y:F[1]}},e.data.bgActivePosistion=void 0,e.redrawHint("select",!0),e.touchData.start?e.touchData.start.emit(vt):Y.emit(vt),e.touchData.start&&(e.touchData.start._private.grabbed=!1),e.touchData.cxtDragged=!0;var Ze=e.findNearestElement(F[0],F[1],!0,!0);e.touchData.cxtOver&&Ze===e.touchData.cxtOver||(e.touchData.cxtOver&&e.touchData.cxtOver.emit({originalEvent:T,type:"cxtdragout",position:{x:F[0],y:F[1]}}),e.touchData.cxtOver=Ze,Ze&&Ze.emit({originalEvent:T,type:"cxtdragover",position:{x:F[0],y:F[1]}}))}else if(R&&T.touches[2]&&Y.boxSelectionEnabled())T.preventDefault(),e.data.bgActivePosistion=void 0,this.lastThreeTouch=+new Date,e.touchData.selecting||Y.emit({originalEvent:T,type:"boxstart",position:{x:F[0],y:F[1]}}),e.touchData.selecting=!0,e.touchData.didSelect=!0,L[4]=1,L&&L.length!==0&&L[0]!==void 0?(L[2]=(F[0]+F[2]+F[4])/3,L[3]=(F[1]+F[3]+F[5])/3):(L[0]=(F[0]+F[2]+F[4])/3,L[1]=(F[1]+F[3]+F[5])/3,L[2]=(F[0]+F[2]+F[4])/3+1,L[3]=(F[1]+F[3]+F[5])/3+1),e.redrawHint("select",!0),e.redraw();else if(R&&T.touches[1]&&!e.touchData.didSelect&&Y.zoomingEnabled()&&Y.panningEnabled()&&Y.userZoomingEnabled()&&Y.userPanningEnabled()){if(T.preventDefault(),e.data.bgActivePosistion=void 0,e.redrawHint("select",!0),Et=e.dragData.touchDragEles){e.redrawHint("drag",!0);for(var Le=0;Le<Et.length;Le++){var Dt=Et[Le]._private;Dt.grabbed=!1,Dt.rscratch.inDragLayer=!1}}var nt=e.touchData.start,$e=(Ee=T.touches[0].clientX-N,ke=T.touches[0].clientY-V,Fe=T.touches[1].clientX-N,Se=T.touches[1].clientY-V,Q(Ee,ke,Fe,Se)),ct=$e/_;if(I){var it=(Ee-E+(Fe-S))/2,In=(ke-C+(Se-P))/2,yt=Y.zoom(),Nn=yt*ct,mr=Y.pan(),ki=O[0]*yt+mr.x,Ci=O[1]*yt+mr.y,Rl={x:-Nn/yt*(ki-mr.x-it)+ki,y:-Nn/yt*(Ci-mr.y-In)+Ci};if(nt&&nt.active()){var Et=e.dragData.touchDragEles;d(Et),e.redrawHint("drag",!0),e.redrawHint("eles",!0),nt.unactivate().emit("freeon"),Et.emit("free"),e.dragData.didDrag&&(nt.emit("dragfreeon"),Et.emit("dragfree"))}Y.viewport({zoom:Nn,pan:Rl,cancelOnFailedZoom:!0}),Y.emit("pinchzoom"),_=$e,E=Ee,C=ke,S=Fe,P=Se,e.pinching=!0}T.touches[0]&&(Z=e.projectIntoViewport(T.touches[0].clientX,T.touches[0].clientY),F[0]=Z[0],F[1]=Z[1]),T.touches[1]&&(Z=e.projectIntoViewport(T.touches[1].clientX,T.touches[1].clientY),F[2]=Z[0],F[3]=Z[1]),T.touches[2]&&(Z=e.projectIntoViewport(T.touches[2].clientX,T.touches[2].clientY),F[4]=Z[0],F[5]=Z[1])}else if(T.touches[0]&&!e.touchData.didSelect){var dt=e.touchData.start,pa=e.touchData.last;if(e.hoverData.draggingEles||e.swipePanning||(Ze=e.findNearestElement(F[0],F[1],!0,!0)),R&&dt!=null&&T.preventDefault(),R&&dt!=null&&e.nodeIsDraggable(dt))if(ee){Et=e.dragData.touchDragEles;var Pi=!e.dragData.didDrag;Pi&&u(Et,{inDragLayer:!0}),e.dragData.didDrag=!0;var Ln={x:0,y:0};ae(ue[0])&&ae(ue[1])&&(Ln.x+=ue[0],Ln.y+=ue[1],Pi&&(e.redrawHint("eles",!0),(mt=e.touchData.dragDelta)&&ae(mt[0])&&ae(mt[1])&&(Ln.x+=mt[0],Ln.y+=mt[1]))),e.hoverData.draggingEles=!0,Et.silentShift(Ln).emit("position drag"),e.redrawHint("drag",!0),e.touchData.startPosition[0]==J[0]&&e.touchData.startPosition[1]==J[1]&&e.redrawHint("eles",!0),e.redraw()}else{var mt;(mt=e.touchData.dragDelta=e.touchData.dragDelta||[]).length===0?(mt.push(ue[0]),mt.push(ue[1])):(mt[0]+=ue[0],mt[1]+=ue[1])}if(r(dt||Ze,["touchmove","tapdrag","vmousemove"],T,{x:F[0],y:F[1]}),dt&&dt.grabbed()||Ze==pa||(pa&&pa.emit({originalEvent:T,type:"tapdragout",position:{x:F[0],y:F[1]}}),Ze&&Ze.emit({originalEvent:T,type:"tapdragover",position:{x:F[0],y:F[1]}})),e.touchData.last=Ze,R)for(Le=0;Le<F.length;Le++)F[Le]&&e.touchData.startPosition[Le]&&ee&&(e.touchData.singleTouchMoved=!0);R&&(dt==null||dt.pannable())&&Y.panningEnabled()&&Y.userPanningEnabled()&&(i(dt,e.touchData.starts)&&(T.preventDefault(),e.data.bgActivePosistion||(e.data.bgActivePosistion=Cn(e.touchData.startPosition)),e.swipePanning?(Y.panBy({x:ue[0]*G,y:ue[1]*G}),Y.emit("dragpan")):ee&&(e.swipePanning=!0,Y.panBy({x:de*G,y:ve*G}),Y.emit("dragpan"),dt&&(dt.unactivate(),e.redrawHint("select",!0),e.touchData.start=null))),Z=e.projectIntoViewport(T.touches[0].clientX,T.touches[0].clientY),F[0]=Z[0],F[1]=Z[1])}for(re=0;re<F.length;re++)J[re]=F[re];R&&T.touches.length>0&&!e.hoverData.draggingEles&&!e.swipePanning&&e.data.bgActivePosistion!=null&&(e.data.bgActivePosistion=void 0,e.redrawHint("select",!0),e.redraw())}},!1),e.registerBinding(t,"touchcancel",U=function(T){var R=e.touchData.start;e.touchData.capture=!1,R&&R.unactivate()}),e.registerBinding(t,"touchend",K=function(T){var R=e.touchData.start;if(e.touchData.capture){T.touches.length===0&&(e.touchData.capture=!1),T.preventDefault();var L=e.selection;e.swipePanning=!1,e.hoverData.draggingEles=!1;var Y,F=e.cy,J=F.zoom(),G=e.touchData.now,Z=e.touchData.earlier;if(T.touches[0]){var ee=e.projectIntoViewport(T.touches[0].clientX,T.touches[0].clientY);G[0]=ee[0],G[1]=ee[1]}if(T.touches[1]&&(ee=e.projectIntoViewport(T.touches[1].clientX,T.touches[1].clientY),G[2]=ee[0],G[3]=ee[1]),T.touches[2]&&(ee=e.projectIntoViewport(T.touches[2].clientX,T.touches[2].clientY),G[4]=ee[0],G[5]=ee[1]),R&&R.unactivate(),e.touchData.cxt){if(Y={originalEvent:T,type:"cxttapend",position:{x:G[0],y:G[1]}},R?R.emit(Y):F.emit(Y),!e.touchData.cxtDragged){var oe={originalEvent:T,type:"cxttap",position:{x:G[0],y:G[1]}};R?R.emit(oe):F.emit(oe)}return e.touchData.start&&(e.touchData.start._private.grabbed=!1),e.touchData.cxt=!1,e.touchData.start=null,void e.redraw()}if(!T.touches[2]&&F.boxSelectionEnabled()&&e.touchData.selecting){e.touchData.selecting=!1;var ue=F.collection(e.getAllInBox(L[0],L[1],L[2],L[3]));L[0]=void 0,L[1]=void 0,L[2]=void 0,L[3]=void 0,L[4]=0,e.redrawHint("select",!0),F.emit({type:"boxend",originalEvent:T,position:{x:G[0],y:G[1]}}),ue.emit("box").stdFilter(function(gt){return gt.selectable()&&!gt.selected()}).select().emit("boxselect"),ue.nonempty()&&e.redrawHint("eles",!0),e.redraw()}if(R!=null&&R.unactivate(),T.touches[2])e.data.bgActivePosistion=void 0,e.redrawHint("select",!0);else if(!T.touches[1]){if(!T.touches[0]){if(!T.touches[0]){e.data.bgActivePosistion=void 0,e.redrawHint("select",!0);var re=e.dragData.touchDragEles;if(R!=null){var de=R._private.grabbed;d(re),e.redrawHint("drag",!0),e.redrawHint("eles",!0),de&&(R.emit("freeon"),re.emit("free"),e.dragData.didDrag&&(R.emit("dragfreeon"),re.emit("dragfree"))),r(R,["touchend","tapend","vmouseup","tapdragout"],T,{x:G[0],y:G[1]}),R.unactivate(),e.touchData.start=null}else{var pe=e.findNearestElement(G[0],G[1],!0,!0);r(pe,["touchend","tapend","vmouseup","tapdragout"],T,{x:G[0],y:G[1]})}var ve=e.touchData.startPosition[0]-G[0],Ee=ve*ve,ke=e.touchData.startPosition[1]-G[1],Fe=(Ee+ke*ke)*J*J;e.touchData.singleTouchMoved||(R||F.$(":selected").unselect(["tapunselect"]),r(R,["tap","vclick"],T,{x:G[0],y:G[1]}),X=!1,T.timeStamp-W<=F.multiClickDebounceTime()?(H&&clearTimeout(H),X=!0,W=null,r(R,["dbltap","vdblclick"],T,{x:G[0],y:G[1]})):(H=setTimeout(function(){X||r(R,["onetap","voneclick"],T,{x:G[0],y:G[1]})},F.multiClickDebounceTime()),W=T.timeStamp)),R!=null&&!e.dragData.didDrag&&R._private.selectable&&Fe<e.touchTapThreshold2&&!e.pinching&&(F.selectionType()==="single"?(F.$(n).unmerge(R).unselect(["tapunselect"]),R.select(["tapselect"])):R.selected()?R.unselect(["tapunselect"]):R.select(["tapselect"]),e.redrawHint("eles",!0)),e.touchData.singleTouchMoved=!0}}}for(var Se=0;Se<G.length;Se++)Z[Se]=G[Se];e.dragData.didDrag=!1,T.touches.length===0&&(e.touchData.dragDelta=[],e.touchData.startPosition=[null,null,null,null,null,null],e.touchData.startGPosition=null,e.touchData.didSelect=!1),T.touches.length<2&&(T.touches.length===1&&(e.touchData.startGPosition=[T.touches[0].clientX,T.touches[0].clientY]),e.pinching=!1,e.redrawHint("eles",!0),e.redraw())}},!1),typeof TouchEvent>"u"){var ie=[],se=function(T){return{clientX:T.clientX,clientY:T.clientY,force:1,identifier:T.pointerId,pageX:T.pageX,pageY:T.pageY,radiusX:T.width/2,radiusY:T.height/2,screenX:T.screenX,screenY:T.screenY,target:T.target}},le=function(T){ie.push(function(R){return{event:R,touch:se(R)}}(T))},fe=function(T){for(var R=0;R<ie.length;R++)if(ie[R].event.pointerId===T.pointerId)return void ie.splice(R,1)},$=function(T){T.touches=ie.map(function(R){return R.touch})},ne=function(T){return T.pointerType==="mouse"||T.pointerType===4};e.registerBinding(e.container,"pointerdown",function(T){ne(T)||(T.preventDefault(),le(T),$(T),q(T))}),e.registerBinding(e.container,"pointerup",function(T){ne(T)||(fe(T),$(T),K(T))}),e.registerBinding(e.container,"pointercancel",function(T){ne(T)||(fe(T),$(T),U())}),e.registerBinding(e.container,"pointermove",function(T){ne(T)||(T.preventDefault(),function(R){var L=ie.filter(function(Y){return Y.event.pointerId===R.pointerId})[0];L.event=R,L.touch=se(R)}(T),$(T),j(T))})}};var Lt={generatePolygon:function(e,t){return this.nodeShapes[e]={renderer:this,name:e,points:t,draw:function(n,r,a,i,o,s){this.renderer.nodeShapeImpl("polygon",n,r,a,i,o,this.points)},intersectLine:function(n,r,a,i,o,s,l,u){return ur(o,s,this.points,n,r,a/2,i/2,l)},checkPoint:function(n,r,a,i,o,s,l,u){return Ct(n,r,this.points,s,l,i,o,[0,-1],a)}}}};Lt.generateEllipse=function(){return this.nodeShapes.ellipse={renderer:this,name:"ellipse",draw:function(e,t,n,r,a,i){this.renderer.nodeShapeImpl(this.name,e,t,n,r,a)},intersectLine:function(e,t,n,r,a,i,o,s){return function(l,u,c,d,h,p){var f=c-l,v=d-u;f/=h,v/=p;var m=Math.sqrt(f*f+v*v),g=m-1;if(g<0)return[];var y=g/m;return[(c-l)*y+l,(d-u)*y+u]}(a,i,e,t,n/2+o,r/2+o)},checkPoint:function(e,t,n,r,a,i,o,s){return Ht(e,t,r,a,i,o,n)}}},Lt.generateRoundPolygon=function(e,t){return this.nodeShapes[e]={renderer:this,name:e,points:t,getOrCreateCorners:function(n,r,a,i,o,s,l){if(s[l]!==void 0&&s[l+"-cx"]===n&&s[l+"-cy"]===r)return s[l];s[l]=new Array(t.length/2),s[l+"-cx"]=n,s[l+"-cy"]=r;var u=a/2,c=i/2;o=o==="auto"?ks(a,i):o;for(var d=new Array(t.length/2),h=0;h<t.length/2;h++)d[h]={x:n+u*t[2*h],y:r+c*t[2*h+1]};var p,f,v,m,g=d.length;for(f=d[g-1],p=0;p<g;p++)v=d[p%g],m=d[(p+1)%g],s[l][p]=wi(f,v,m,o),f=v,v=m;return s[l]},draw:function(n,r,a,i,o,s,l){this.renderer.nodeShapeImpl("round-polygon",n,r,a,i,o,this.points,this.getOrCreateCorners(r,a,i,o,s,l,"drawCorners"))},intersectLine:function(n,r,a,i,o,s,l,u,c){return function(d,h,p,f,v,m,g,y,b){var k,x=[],w=new Array(2*p.length);b.forEach(function(A,B){B===0?(w[w.length-2]=A.startX,w[w.length-1]=A.startY):(w[4*B-2]=A.startX,w[4*B-1]=A.startY),w[4*B]=A.stopX,w[4*B+1]=A.stopY,(k=Zn(d,h,f,v,A.cx,A.cy,A.radius)).length!==0&&x.push(k[0],k[1])});for(var E=0;E<w.length/4;E++)(k=Vt(d,h,f,v,w[4*E],w[4*E+1],w[4*E+2],w[4*E+3],!1)).length!==0&&x.push(k[0],k[1]);if(x.length>2){for(var C=[x[0],x[1]],S=Math.pow(C[0]-d,2)+Math.pow(C[1]-h,2),P=1;P<x.length/2;P++){var _=Math.pow(x[2*P]-d,2)+Math.pow(x[2*P+1]-h,2);_<=S&&(C[0]=x[2*P],C[1]=x[2*P+1],S=_)}return C}return x}(o,s,this.points,n,r,0,0,0,this.getOrCreateCorners(n,r,a,i,u,c,"corners"))},checkPoint:function(n,r,a,i,o,s,l,u,c){return function(d,h,p,f,v,m,g,y){for(var b=new Array(2*p.length),k=0;k<y.length;k++){var x=y[k];if(b[4*k+0]=x.startX,b[4*k+1]=x.startY,b[4*k+2]=x.stopX,b[4*k+3]=x.stopY,Math.pow(x.cx-d,2)+Math.pow(x.cy-h,2)<=Math.pow(x.radius,2))return!0}return et(d,h,b)}(n,r,this.points,0,0,0,0,this.getOrCreateCorners(s,l,i,o,u,c,"corners"))}}},Lt.generateRoundRectangle=function(){return this.nodeShapes["round-rectangle"]=this.nodeShapes.roundrectangle={renderer:this,name:"round-rectangle",points:Qe(4,0),draw:function(e,t,n,r,a,i){this.renderer.nodeShapeImpl(this.name,e,t,n,r,a,this.points,i)},intersectLine:function(e,t,n,r,a,i,o,s){return Li(a,i,e,t,n,r,o,s)},checkPoint:function(e,t,n,r,a,i,o,s){var l=r/2,u=a/2;s=s==="auto"?on(r,a):s;var c=2*(s=Math.min(l,u,s));return!!Ct(e,t,this.points,i,o,r,a-c,[0,-1],n)||!!Ct(e,t,this.points,i,o,r-c,a,[0,-1],n)||!!Ht(e,t,c,c,i-l+s,o-u+s,n)||!!Ht(e,t,c,c,i+l-s,o-u+s,n)||!!Ht(e,t,c,c,i+l-s,o+u-s,n)||!!Ht(e,t,c,c,i-l+s,o+u-s,n)}}},Lt.generateCutRectangle=function(){return this.nodeShapes["cut-rectangle"]=this.nodeShapes.cutrectangle={renderer:this,name:"cut-rectangle",cornerLength:8,points:Qe(4,0),draw:function(e,t,n,r,a,i){this.renderer.nodeShapeImpl(this.name,e,t,n,r,a,null,i)},generateCutTrianglePts:function(e,t,n,r,a){var i=a==="auto"?this.cornerLength:a,o=t/2,s=e/2,l=n-s,u=n+s,c=r-o,d=r+o;return{topLeft:[l,c+i,l+i,c,l+i,c+i],topRight:[u-i,c,u,c+i,u-i,c+i],bottomRight:[u,d-i,u-i,d,u-i,d-i],bottomLeft:[l+i,d,l,d-i,l+i,d-i]}},intersectLine:function(e,t,n,r,a,i,o,s){var l=this.generateCutTrianglePts(n+2*o,r+2*o,e,t,s),u=[].concat.apply([],[l.topLeft.splice(0,4),l.topRight.splice(0,4),l.bottomRight.splice(0,4),l.bottomLeft.splice(0,4)]);return ur(a,i,u,e,t)},checkPoint:function(e,t,n,r,a,i,o,s){var l=s==="auto"?this.cornerLength:s;if(Ct(e,t,this.points,i,o,r,a-2*l,[0,-1],n)||Ct(e,t,this.points,i,o,r-2*l,a,[0,-1],n))return!0;var u=this.generateCutTrianglePts(r,a,i,o);return et(e,t,u.topLeft)||et(e,t,u.topRight)||et(e,t,u.bottomRight)||et(e,t,u.bottomLeft)}}},Lt.generateBarrel=function(){return this.nodeShapes.barrel={renderer:this,name:"barrel",points:Qe(4,0),draw:function(e,t,n,r,a,i){this.renderer.nodeShapeImpl(this.name,e,t,n,r,a)},intersectLine:function(e,t,n,r,a,i,o,s){var l=this.generateBarrelBezierPts(n+2*o,r+2*o,e,t),u=function(d){var h=Dn({x:d[0],y:d[1]},{x:d[2],y:d[3]},{x:d[4],y:d[5]},.15),p=Dn({x:d[0],y:d[1]},{x:d[2],y:d[3]},{x:d[4],y:d[5]},.5),f=Dn({x:d[0],y:d[1]},{x:d[2],y:d[3]},{x:d[4],y:d[5]},.85);return[d[0],d[1],h.x,h.y,p.x,p.y,f.x,f.y,d[4],d[5]]},c=[].concat(u(l.topLeft),u(l.topRight),u(l.bottomRight),u(l.bottomLeft));return ur(a,i,c,e,t)},generateBarrelBezierPts:function(e,t,n,r){var a=t/2,i=e/2,o=n-i,s=n+i,l=r-a,u=r+a,c=Ra(e,t),d=c.heightOffset,h=c.widthOffset,p=c.ctrlPtOffsetPct*e,f={topLeft:[o,l+d,o+p,l,o+h,l],topRight:[s-h,l,s-p,l,s,l+d],bottomRight:[s,u-d,s-p,u,s-h,u],bottomLeft:[o+h,u,o+p,u,o,u-d]};return f.topLeft.isTop=!0,f.topRight.isTop=!0,f.bottomLeft.isBottom=!0,f.bottomRight.isBottom=!0,f},checkPoint:function(e,t,n,r,a,i,o,s){var l=Ra(r,a),u=l.heightOffset,c=l.widthOffset;if(Ct(e,t,this.points,i,o,r,a-2*u,[0,-1],n)||Ct(e,t,this.points,i,o,r-2*c,a,[0,-1],n))return!0;for(var d=this.generateBarrelBezierPts(r,a,i,o),h=function(x,w,E){var C,S,P=E[4],_=E[2],A=E[0],B=E[5],O=E[1],N=Math.min(P,A),V=Math.max(P,A),M=Math.min(B,O),D=Math.max(B,O);if(N<=x&&x<=V&&M<=w&&w<=D){var I=[(C=P)-2*(S=_)+A,2*(S-C),C],z=function(q,j,U,K){var X=j*j-4*q*(U-=K);if(X<0)return[];var H=Math.sqrt(X),W=2*q;return[(-j+H)/W,(-j-H)/W]}(I[0],I[1],I[2],x).filter(function(q){return 0<=q&&q<=1});if(z.length>0)return z[0]}return null},p=Object.keys(d),f=0;f<p.length;f++){var v=d[p[f]],m=h(e,t,v);if(m!=null){var g=v[5],y=v[3],b=v[1],k=Oe(g,y,b,m);if(v.isTop&&k<=t||v.isBottom&&t<=k)return!0}}return!1}}},Lt.generateBottomRoundrectangle=function(){return this.nodeShapes["bottom-round-rectangle"]=this.nodeShapes.bottomroundrectangle={renderer:this,name:"bottom-round-rectangle",points:Qe(4,0),draw:function(e,t,n,r,a,i){this.renderer.nodeShapeImpl(this.name,e,t,n,r,a,this.points,i)},intersectLine:function(e,t,n,r,a,i,o,s){var l=t-(r/2+o),u=Vt(a,i,e,t,e-(n/2+o),l,e+(n/2+o),l,!1);return u.length>0?u:Li(a,i,e,t,n,r,o,s)},checkPoint:function(e,t,n,r,a,i,o,s){var l=2*(s=s==="auto"?on(r,a):s);if(Ct(e,t,this.points,i,o,r,a-l,[0,-1],n)||Ct(e,t,this.points,i,o,r-l,a,[0,-1],n))return!0;var u=r/2+2*n,c=a/2+2*n;return!!et(e,t,[i-u,o-c,i-u,o,i+u,o,i+u,o-c])||!!Ht(e,t,l,l,i+r/2-s,o+a/2-s,n)||!!Ht(e,t,l,l,i-r/2+s,o+a/2-s,n)}}},Lt.registerNodeShapes=function(){var e=this.nodeShapes={},t=this;this.generateEllipse(),this.generatePolygon("triangle",Qe(3,0)),this.generateRoundPolygon("round-triangle",Qe(3,0)),this.generatePolygon("rectangle",Qe(4,0)),e.square=e.rectangle,this.generateRoundRectangle(),this.generateCutRectangle(),this.generateBarrel(),this.generateBottomRoundrectangle();var n=[0,1,1,0,0,-1,-1,0];this.generatePolygon("diamond",n),this.generateRoundPolygon("round-diamond",n),this.generatePolygon("pentagon",Qe(5,0)),this.generateRoundPolygon("round-pentagon",Qe(5,0)),this.generatePolygon("hexagon",Qe(6,0)),this.generateRoundPolygon("round-hexagon",Qe(6,0)),this.generatePolygon("heptagon",Qe(7,0)),this.generateRoundPolygon("round-heptagon",Qe(7,0)),this.generatePolygon("octagon",Qe(8,0)),this.generateRoundPolygon("round-octagon",Qe(8,0));var r=new Array(20),a=Ma(5,0),i=Ma(5,Math.PI/5),o=.5*(3-Math.sqrt(5));o*=1.57;for(var s=0;s<i.length/2;s++)i[2*s]*=o,i[2*s+1]*=o;for(s=0;s<5;s++)r[4*s]=a[2*s],r[4*s+1]=a[2*s+1],r[4*s+2]=i[2*s],r[4*s+3]=i[2*s+1];r=Ts(r),this.generatePolygon("star",r),this.generatePolygon("vee",[-1,-1,0,-.333,1,-1,0,1]),this.generatePolygon("rhomboid",[-1,-1,.333,-1,1,1,-.333,1]),this.generatePolygon("right-rhomboid",[-.333,-1,1,-1,.333,1,-1,1]),this.nodeShapes.concavehexagon=this.generatePolygon("concave-hexagon",[-1,-.95,-.75,0,-1,.95,1,.95,.75,0,1,-.95]);var l=[-1,-1,.25,-1,1,0,.25,1,-1,1];this.generatePolygon("tag",l),this.generateRoundPolygon("round-tag",l),e.makePolygon=function(u){var c,d="polygon-"+u.join("$");return(c=this[d])?c:t.generatePolygon(d,u)}};var gl={timeToRender:function(){return this.redrawTotalTime/this.redrawCount},redraw:function(e){e=e||bs();var t=this;t.averageRedrawTime===void 0&&(t.averageRedrawTime=0),t.lastRedrawTime===void 0&&(t.lastRedrawTime=0),t.lastDrawTime===void 0&&(t.lastDrawTime=0),t.requestedFrame=!0,t.renderOptions=e},beforeRender:function(e,t){if(!this.destroyed){t==null&&De("Priority is not optional for beforeRender");var n=this.beforeRenderCallbacks;n.push({fn:e,priority:t}),n.sort(function(r,a){return a.priority-r.priority})}}},Fo=function(e,t,n){for(var r=e.beforeRenderCallbacks,a=0;a<r.length;a++)r[a].fn(t,n)};gl.startRenderLoop=function(){var e=this,t=e.cy;e.renderLoopStarted||(e.renderLoopStarted=!0,Wr(function n(r){if(!e.destroyed){if(!t.batching())if(e.requestedFrame&&!e.skipFrame){Fo(e,!0,r);var a=Bt();e.render(e.renderOptions);var i=e.lastDrawTime=Bt();e.averageRedrawTime===void 0&&(e.averageRedrawTime=i-a),e.redrawCount===void 0&&(e.redrawCount=0),e.redrawCount++,e.redrawTotalTime===void 0&&(e.redrawTotalTime=0);var o=i-a;e.redrawTotalTime+=o,e.lastRedrawTime=o,e.averageRedrawTime=e.averageRedrawTime/2+o/2,e.requestedFrame=!1}else Fo(e,!1,r);e.skipFrame=!1,Wr(n)}}))};var vl=function(e){this.init(e)},hn=vl.prototype;hn.clientFunctions=["redrawHint","render","renderTo","matchCanvasSize","nodeShapeImpl","arrowShapeImpl"],hn.init=function(e){var t=this;t.options=e,t.cy=e.cy;var n=t.container=e.cy.container(),r=t.cy.window();if(r){var a=r.document,i=a.head,o="__________cytoscape_stylesheet",s="__________cytoscape_container",l=a.getElementById(o)!=null;if(n.className.indexOf(s)<0&&(n.className=(n.className||"")+" "+s),!l){var u=a.createElement("style");u.id=o,u.textContent="."+s+" { position: relative; }",i.insertBefore(u,i.children[0])}r.getComputedStyle(n).getPropertyValue("position")==="static"&&we("A Cytoscape container has style position:static and so can not use UI extensions properly")}t.selection=[void 0,void 0,void 0,void 0,0],t.bezierProjPcts=[.05,.225,.4,.5,.6,.775,.95],t.hoverData={down:null,last:null,downTime:null,triggerMode:null,dragging:!1,initialPan:[null,null],capture:!1},t.dragData={possibleDragElements:[]},t.touchData={start:null,capture:!1,startPosition:[null,null,null,null,null,null],singleTouchStartTime:null,singleTouchMoved:!0,now:[null,null,null,null,null,null],earlier:[null,null,null,null,null,null]},t.redraws=0,t.showFps=e.showFps,t.debug=e.debug,t.webgl=e.webgl,t.hideEdgesOnViewport=e.hideEdgesOnViewport,t.textureOnViewport=e.textureOnViewport,t.wheelSensitivity=e.wheelSensitivity,t.motionBlurEnabled=e.motionBlur,t.forcedPixelRatio=ae(e.pixelRatio)?e.pixelRatio:null,t.motionBlur=e.motionBlur,t.motionBlurOpacity=e.motionBlurOpacity,t.motionBlurTransparency=1-t.motionBlurOpacity,t.motionBlurPxRatio=1,t.mbPxRBlurry=1,t.minMbLowQualFrames=4,t.fullQualityMb=!1,t.clearedForMotionBlur=[],t.desktopTapThreshold=e.desktopTapThreshold,t.desktopTapThreshold2=e.desktopTapThreshold*e.desktopTapThreshold,t.touchTapThreshold=e.touchTapThreshold,t.touchTapThreshold2=e.touchTapThreshold*e.touchTapThreshold,t.tapholdDuration=500,t.bindings=[],t.beforeRenderCallbacks=[],t.beforeRenderPriorities={animations:400,eleCalcs:300,eleTxrDeq:200,lyrTxrDeq:150,lyrTxrSkip:100},t.registerNodeShapes(),t.registerArrowShapes(),t.registerCalculationListeners()},hn.notify=function(e,t){var n=this,r=n.cy;this.destroyed||(e!=="init"?e!=="destroy"?((e==="add"||e==="remove"||e==="move"&&r.hasCompoundNodes()||e==="load"||e==="zorder"||e==="mount")&&n.invalidateCachedZSortedEles(),e==="viewport"&&n.redrawHint("select",!0),e==="gc"&&n.redrawHint("gc",!0),e!=="load"&&e!=="resize"&&e!=="mount"||(n.invalidateContainerClientCoordsCache(),n.matchCanvasSize(n.container)),n.redrawHint("eles",!0),n.redrawHint("drag",!0),this.startRenderLoop(),this.redraw()):n.destroy():n.load())},hn.destroy=function(){var e=this;e.destroyed=!0,e.cy.stopAnimationLoop();for(var t=0;t<e.bindings.length;t++){var n=e.bindings[t],r=n.target;(r.off||r.removeEventListener).apply(r,n.args)}if(e.bindings=[],e.beforeRenderCallbacks=[],e.onUpdateEleCalcsFns=[],e.removeObserver&&e.removeObserver.disconnect(),e.styleObserver&&e.styleObserver.disconnect(),e.resizeObserver&&e.resizeObserver.disconnect(),e.labelCalcDiv)try{document.body.removeChild(e.labelCalcDiv)}catch{}},hn.isHeadless=function(){return!1},[xh,pl,Sh,Jn,Lt,gl].forEach(function(e){he(hn,e)});var Sa=1e3/60,yl=function(e){return function(){var t=this,n=this.renderer;if(!t.dequeueingSetup){t.dequeueingSetup=!0;var r=vr(function(){n.redrawHint("eles",!0),n.redrawHint("drag",!0),n.redraw()},e.deqRedrawThreshold),a=e.priority||ii;n.beforeRender(function(i,o){var s=Bt(),l=n.averageRedrawTime,u=n.lastRedrawTime,c=[],d=n.cy.extent(),h=n.getPixelRatio();for(i||n.flushRenderedStyleQueue();;){var p=Bt(),f=p-s,v=p-o;if(u<Sa){var m=Sa-(i?l:0);if(v>=e.deqFastCost*m)break}else if(i){if(f>=e.deqCost*u||f>=e.deqAvgCost*l)break}else if(v>=e.deqNoDrawCost*Sa)break;var g=e.deq(t,h,d);if(!(g.length>0))break;for(var y=0;y<g.length;y++)c.push(g[y])}c.length>0&&(e.onDeqd(t,c),!i&&e.shouldRedraw(t,c,h,d)&&r())},a(t))}}},Bh=function(){function e(t){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Kr;Wt(this,e),this.idsByKey=new wt,this.keyForId=new wt,this.cachesByLvl=new wt,this.lvls=[],this.getKey=t,this.doesEleInvalidateKey=n}return Kt(e,[{key:"getIdsFor",value:function(t){t==null&&De("Can not get id list for null key");var n=this.idsByKey,r=this.idsByKey.get(t);return r||(r=new An,n.set(t,r)),r}},{key:"addIdForKey",value:function(t,n){t!=null&&this.getIdsFor(t).add(n)}},{key:"deleteIdForKey",value:function(t,n){t!=null&&this.getIdsFor(t).delete(n)}},{key:"getNumberOfIdsForKey",value:function(t){return t==null?0:this.getIdsFor(t).size}},{key:"updateKeyMappingFor",value:function(t){var n=t.id(),r=this.keyForId.get(n),a=this.getKey(t);this.deleteIdForKey(r,n),this.addIdForKey(a,n),this.keyForId.set(n,a)}},{key:"deleteKeyMappingFor",value:function(t){var n=t.id(),r=this.keyForId.get(n);this.deleteIdForKey(r,n),this.keyForId.delete(n)}},{key:"keyHasChangedFor",value:function(t){var n=t.id();return this.keyForId.get(n)!==this.getKey(t)}},{key:"isInvalid",value:function(t){return this.keyHasChangedFor(t)||this.doesEleInvalidateKey(t)}},{key:"getCachesAt",value:function(t){var n=this.cachesByLvl,r=this.lvls,a=n.get(t);return a||(a=new wt,n.set(t,a),r.push(t)),a}},{key:"getCache",value:function(t,n){return this.getCachesAt(n).get(t)}},{key:"get",value:function(t,n){var r=this.getKey(t),a=this.getCache(r,n);return a!=null&&this.updateKeyMappingFor(t),a}},{key:"getForCachedKey",value:function(t,n){var r=this.keyForId.get(t.id());return this.getCache(r,n)}},{key:"hasCache",value:function(t,n){return this.getCachesAt(n).has(t)}},{key:"has",value:function(t,n){var r=this.getKey(t);return this.hasCache(r,n)}},{key:"setCache",value:function(t,n,r){r.key=t,this.getCachesAt(n).set(t,r)}},{key:"set",value:function(t,n,r){var a=this.getKey(t);this.setCache(a,n,r),this.updateKeyMappingFor(t)}},{key:"deleteCache",value:function(t,n){this.getCachesAt(n).delete(t)}},{key:"delete",value:function(t,n){var r=this.getKey(t);this.deleteCache(r,n)}},{key:"invalidateKey",value:function(t){var n=this;this.lvls.forEach(function(r){return n.deleteCache(t,r)})}},{key:"invalidate",value:function(t){var n=t.id(),r=this.keyForId.get(n);this.deleteKeyMappingFor(t);var a=this.doesEleInvalidateKey(t);return a&&this.invalidateKey(r),a||this.getNumberOfIdsForKey(r)===0}}]),e}(),Dh=7.99,fn={dequeue:"dequeue",downscale:"downscale",highQuality:"highQuality"},_h=Ve({getKey:null,doesEleInvalidateKey:Kr,drawElement:null,getBoundingBox:null,getRotationPoint:null,getRotationOffset:null,isVisible:vs,allowEdgeTxrCaching:!0,allowParentTxrCaching:!0}),er=function(e,t){var n=this;n.renderer=e,n.onDequeues=[];var r=_h(t);he(n,r),n.lookup=new Bh(r.getKey,r.doesEleInvalidateKey),n.setupDequeueing()},Me=er.prototype;Me.reasons=fn,Me.getTextureQueue=function(e){var t=this;return t.eleImgCaches=t.eleImgCaches||{},t.eleImgCaches[e]=t.eleImgCaches[e]||[]},Me.getRetiredTextureQueue=function(e){var t=this.eleImgCaches.retired=this.eleImgCaches.retired||{};return t[e]=t[e]||[]},Me.getElementQueue=function(){return this.eleCacheQueue=this.eleCacheQueue||new yr(function(e,t){return t.reqs-e.reqs})},Me.getElementKeyToQueue=function(){return this.eleKeyToCacheQueue=this.eleKeyToCacheQueue||{}},Me.getElement=function(e,t,n,r,a){var i=this,o=this.renderer,s=o.cy.zoom(),l=this.lookup;if(!t||t.w===0||t.h===0||isNaN(t.w)||isNaN(t.h)||!e.visible()||e.removed()||!i.allowEdgeTxrCaching&&e.isEdge()||!i.allowParentTxrCaching&&e.isParent())return null;if(r==null&&(r=Math.ceil(oi(s*n))),r<-4)r=-4;else if(s>=7.99||r>3)return null;var u=Math.pow(2,r),c=t.h*u,d=t.w*u,h=o.eleTextBiggerThanMin(e,u);if(!this.isVisible(e,h))return null;var p,f=l.get(e,r);if(f&&f.invalidated&&(f.invalidated=!1,f.texture.invalidatedWidth-=f.width),f)return f;if(p=c<=25?25:c<=50?50:50*Math.ceil(c/50),c>1024||d>1024)return null;var v=i.getTextureQueue(p),m=v[v.length-2],g=function(){return i.recycleTexture(p,d)||i.addTexture(p,d)};m||(m=v[v.length-1]),m||(m=g()),m.width-m.usedWidth<d&&(m=g());for(var y,b=function(N){return N&&N.scaledLabelShown===h},k=a&&a===fn.dequeue,x=a&&a===fn.highQuality,w=a&&a===fn.downscale,E=r+1;E<=3;E++){var C=l.get(e,E);if(C){y=C;break}}var S=y&&y.level===r+1?y:null,P=function(){m.context.drawImage(S.texture.canvas,S.x,0,S.width,S.height,m.usedWidth,0,d,c)};if(m.context.setTransform(1,0,0,1,0,0),m.context.clearRect(m.usedWidth,0,d,p),b(S))P();else if(b(y)){if(!x)return i.queueElement(e,y.level-1),y;for(var _=y.level;_>r;_--)S=i.getElement(e,t,n,_,fn.downscale);P()}else{var A;if(!k&&!x&&!w)for(var B=r-1;B>=-4;B--){var O=l.get(e,B);if(O){A=O;break}}if(b(A))return i.queueElement(e,r),A;m.context.translate(m.usedWidth,0),m.context.scale(u,u),this.drawElement(m.context,e,t,h,!1),m.context.scale(1/u,1/u),m.context.translate(-m.usedWidth,0)}return f={x:m.usedWidth,texture:m,level:r,scale:u,width:d,height:c,scaledLabelShown:h},m.usedWidth+=Math.ceil(d+8),m.eleCaches.push(f),l.set(e,r,f),i.checkTextureFullness(m),f},Me.invalidateElements=function(e){for(var t=0;t<e.length;t++)this.invalidateElement(e[t])},Me.invalidateElement=function(e){var t=this,n=t.lookup,r=[];if(n.isInvalid(e)){for(var a=-4;a<=3;a++){var i=n.getForCachedKey(e,a);i&&r.push(i)}if(n.invalidate(e))for(var o=0;o<r.length;o++){var s=r[o],l=s.texture;l.invalidatedWidth+=s.width,s.invalidated=!0,t.checkTextureUtility(l)}t.removeFromQueue(e)}},Me.checkTextureUtility=function(e){e.invalidatedWidth>=.2*e.width&&this.retireTexture(e)},Me.checkTextureFullness=function(e){var t=this.getTextureQueue(e.height);e.usedWidth/e.width>.8&&e.fullnessChecks>=10?zt(t,e):e.fullnessChecks++},Me.retireTexture=function(e){var t=e.height,n=this.getTextureQueue(t),r=this.lookup;zt(n,e),e.retired=!0;for(var a=e.eleCaches,i=0;i<a.length;i++){var o=a[i];r.deleteCache(o.key,o.level)}Aa(a),this.getRetiredTextureQueue(t).push(e)},Me.addTexture=function(e,t){var n={};return this.getTextureQueue(e).push(n),n.eleCaches=[],n.height=e,n.width=Math.max(1024,t),n.usedWidth=0,n.invalidatedWidth=0,n.fullnessChecks=0,n.canvas=this.renderer.makeOffscreenCanvas(n.width,n.height),n.context=n.canvas.getContext("2d"),n},Me.recycleTexture=function(e,t){for(var n=this.getTextureQueue(e),r=this.getRetiredTextureQueue(e),a=0;a<r.length;a++){var i=r[a];if(i.width>=t)return i.retired=!1,i.usedWidth=0,i.invalidatedWidth=0,i.fullnessChecks=0,Aa(i.eleCaches),i.context.setTransform(1,0,0,1,0,0),i.context.clearRect(0,0,i.width,i.height),zt(r,i),n.push(i),i}},Me.queueElement=function(e,t){var n=this.getElementQueue(),r=this.getElementKeyToQueue(),a=this.getKey(e),i=r[a];if(i)i.level=Math.max(i.level,t),i.eles.merge(e),i.reqs++,n.updateItem(i);else{var o={eles:e.spawn().merge(e),level:t,reqs:1,key:a};n.push(o),r[a]=o}},Me.dequeue=function(e){for(var t=this,n=t.getElementQueue(),r=t.getElementKeyToQueue(),a=[],i=t.lookup,o=0;o<1&&n.size()>0;o++){var s=n.pop(),l=s.key,u=s.eles[0],c=i.hasCache(u,s.level);if(r[l]=null,!c){a.push(s);var d=t.getBoundingBox(u);t.getElement(u,d,e,s.level,fn.dequeue)}}return a},Me.removeFromQueue=function(e){var t=this.getElementQueue(),n=this.getElementKeyToQueue(),r=this.getKey(e),a=n[r];a!=null&&(a.eles.length===1?(a.reqs=ai,t.updateItem(a),t.pop(),n[r]=null):a.eles.unmerge(e))},Me.onDequeue=function(e){this.onDequeues.push(e)},Me.offDequeue=function(e){zt(this.onDequeues,e)},Me.setupDequeueing=yl({deqRedrawThreshold:100,deqCost:.15,deqAvgCost:.1,deqNoDrawCost:.9,deqFastCost:.9,deq:function(e,t,n){return e.dequeue(t,n)},onDeqd:function(e,t){for(var n=0;n<e.onDequeues.length;n++)(0,e.onDequeues[n])(t)},shouldRedraw:function(e,t,n,r){for(var a=0;a<t.length;a++)for(var i=t[a].eles,o=0;o<i.length;o++){var s=i[o].boundingBox();if(si(s,r))return!0}return!1},priority:function(e){return e.renderer.beforeRenderPriorities.eleTxrDeq}});var ml=function(e){var t=this,n=t.renderer=e,r=n.cy;t.layersByLevel={},t.firstGet=!0,t.lastInvalidationTime=Bt()-500,t.skipping=!1,t.eleTxrDeqs=r.collection(),t.scheduleElementRefinement=vr(function(){t.refineElementTextures(t.eleTxrDeqs),t.eleTxrDeqs.unmerge(t.eleTxrDeqs)},50),n.beforeRender(function(a,i){i-t.lastInvalidationTime<=250?t.skipping=!0:t.skipping=!1},n.beforeRenderPriorities.lyrTxrSkip),t.layersQueue=new yr(function(a,i){return i.reqs-a.reqs}),t.setupDequeueing()},qe=ml.prototype,qo=0,Ah=Math.pow(2,53)-1;qe.makeLayer=function(e,t){var n=Math.pow(2,t),r=Math.ceil(e.w*n),a=Math.ceil(e.h*n),i=this.renderer.makeOffscreenCanvas(r,a),o={id:qo=++qo%Ah,bb:e,level:t,width:r,height:a,canvas:i,context:i.getContext("2d"),eles:[],elesQueue:[],reqs:0},s=o.context,l=-o.bb.x1,u=-o.bb.y1;return s.scale(n,n),s.translate(l,u),o},qe.getLayers=function(e,t,n){var r=this,a=r.renderer.cy.zoom(),i=r.firstGet;if(r.firstGet=!1,n==null){if((n=Math.ceil(oi(a*t)))<-4)n=-4;else if(a>=3.99||n>2)return null}r.validateLayersElesOrdering(n,e);var o,s,l=r.layersByLevel,u=Math.pow(2,n),c=l[n]=l[n]||[];if(r.levelIsComplete(n,e))return c;(function(){var k=function(C){if(r.validateLayersElesOrdering(C,e),r.levelIsComplete(C,e))return s=l[C],!0},x=function(C){if(!s)for(var S=n+C;-4<=S&&S<=2&&!k(S);S+=C);};x(1),x(-1);for(var w=c.length-1;w>=0;w--){var E=c[w];E.invalid&&zt(c,E)}})();var d=function(k){var x=(k=k||{}).after;(function(){if(!o){o=tt();for(var P=0;P<e.length;P++)ws(o,e[P].boundingBox())}})();var w=Math.ceil(o.w*u),E=Math.ceil(o.h*u);if(w>32767||E>32767||w*E>16e6)return null;var C=r.makeLayer(o,n);if(x!=null){var S=c.indexOf(x)+1;c.splice(S,0,C)}else(k.insert===void 0||k.insert)&&c.unshift(C);return C};if(r.skipping&&!i)return null;for(var h=null,p=e.length/1,f=!i,v=0;v<e.length;v++){var m=e[v],g=m._private.rscratch,y=g.imgLayerCaches=g.imgLayerCaches||{},b=y[n];if(b)h=b;else{if((!h||h.eles.length>=p||!Es(h.bb,m.boundingBox()))&&!(h=d({insert:!0,after:h})))return null;s||f?r.queueLayer(h,m):r.drawEleInLayer(h,m,n,t),h.eles.push(m),y[n]=h}}return s||(f?null:c)},qe.getEleLevelForLayerLevel=function(e,t){return e},qe.drawEleInLayer=function(e,t,n,r){var a=this.renderer,i=e.context,o=t.boundingBox();o.w!==0&&o.h!==0&&t.visible()&&(n=this.getEleLevelForLayerLevel(n,r),a.setImgSmoothing(i,!1),a.drawCachedElement(i,t,null,null,n,!0),a.setImgSmoothing(i,!0))},qe.levelIsComplete=function(e,t){var n=this.layersByLevel[e];if(!n||n.length===0)return!1;for(var r=0,a=0;a<n.length;a++){var i=n[a];if(i.reqs>0||i.invalid)return!1;r+=i.eles.length}return r===t.length},qe.validateLayersElesOrdering=function(e,t){var n=this.layersByLevel[e];if(n)for(var r=0;r<n.length;r++){for(var a=n[r],i=-1,o=0;o<t.length;o++)if(a.eles[0]===t[o]){i=o;break}if(i<0)this.invalidateLayer(a);else{var s=i;for(o=0;o<a.eles.length;o++)if(a.eles[o]!==t[s+o]){this.invalidateLayer(a);break}}}},qe.updateElementsInLayers=function(e,t){for(var n=pr(e[0]),r=0;r<e.length;r++)for(var a=n?null:e[r],i=n?e[r]:e[r].ele,o=i._private.rscratch,s=o.imgLayerCaches=o.imgLayerCaches||{},l=-4;l<=2;l++){var u=s[l];u&&(a&&this.getEleLevelForLayerLevel(u.level)!==a.level||t(u,i,a))}},qe.haveLayers=function(){for(var e=!1,t=-4;t<=2;t++){var n=this.layersByLevel[t];if(n&&n.length>0){e=!0;break}}return e},qe.invalidateElements=function(e){var t=this;e.length!==0&&(t.lastInvalidationTime=Bt(),e.length!==0&&t.haveLayers()&&t.updateElementsInLayers(e,function(n,r,a){t.invalidateLayer(n)}))},qe.invalidateLayer=function(e){if(this.lastInvalidationTime=Bt(),!e.invalid){var t=e.level,n=e.eles,r=this.layersByLevel[t];zt(r,e),e.elesQueue=[],e.invalid=!0,e.replacement&&(e.replacement.invalid=!0);for(var a=0;a<n.length;a++){var i=n[a]._private.rscratch.imgLayerCaches;i&&(i[t]=null)}}},qe.refineElementTextures=function(e){var t=this;t.updateElementsInLayers(e,function(n,r,a){var i=n.replacement;if(i||((i=n.replacement=t.makeLayer(n.bb,n.level)).replaces=n,i.eles=n.eles),!i.reqs)for(var o=0;o<i.eles.length;o++)t.queueLayer(i,i.eles[o])})},qe.enqueueElementRefinement=function(e){this.eleTxrDeqs.merge(e),this.scheduleElementRefinement()},qe.queueLayer=function(e,t){var n=this.layersQueue,r=e.elesQueue,a=r.hasId=r.hasId||{};if(!e.replacement){if(t){if(a[t.id()])return;r.push(t),a[t.id()]=!0}e.reqs?(e.reqs++,n.updateItem(e)):(e.reqs=1,n.push(e))}},qe.dequeue=function(e){for(var t=this,n=t.layersQueue,r=[],a=0;a<1&&n.size()!==0;){var i=n.peek();if(i.replacement)n.pop();else if(i.replaces&&i!==i.replaces.replacement)n.pop();else if(i.invalid)n.pop();else{var o=i.elesQueue.shift();o&&(t.drawEleInLayer(i,o,i.level,e),a++),r.length===0&&r.push(!0),i.elesQueue.length===0&&(n.pop(),i.reqs=0,i.replaces&&t.applyLayerReplacement(i),t.requestRedraw())}}return r},qe.applyLayerReplacement=function(e){var t=this.layersByLevel[e.level],n=e.replaces,r=t.indexOf(n);if(!(r<0||n.invalid)){t[r]=e;for(var a=0;a<e.eles.length;a++){var i=e.eles[a]._private,o=i.imgLayerCaches=i.imgLayerCaches||{};o&&(o[e.level]=e)}this.requestRedraw()}},qe.requestRedraw=vr(function(){var e=this.renderer;e.redrawHint("eles",!0),e.redrawHint("drag",!0),e.redraw()},100),qe.setupDequeueing=yl({deqRedrawThreshold:50,deqCost:.15,deqAvgCost:.1,deqNoDrawCost:.9,deqFastCost:.9,deq:function(e,t){return e.dequeue(t)},onDeqd:ii,shouldRedraw:vs,priority:function(e){return e.renderer.beforeRenderPriorities.lyrTxrDeq}});var Xo,bl={};function Mh(e,t){for(var n=0;n<t.length;n++){var r=t[n];e.lineTo(r.x,r.y)}}function Rh(e,t,n){for(var r,a=0;a<t.length;a++){var i=t[a];a===0&&(r=i),e.lineTo(i.x,i.y)}e.quadraticCurveTo(n.x,n.y,r.x,r.y)}function Yo(e,t,n){e.beginPath&&e.beginPath();for(var r=t,a=0;a<r.length;a++){var i=r[a];e.lineTo(i.x,i.y)}var o=n,s=n[0];for(e.moveTo(s.x,s.y),a=1;a<o.length;a++)i=o[a],e.lineTo(i.x,i.y);e.closePath&&e.closePath()}function Ih(e,t,n,r,a){e.beginPath&&e.beginPath(),e.arc(n,r,a,0,2*Math.PI,!1);var i=t,o=i[0];e.moveTo(o.x,o.y);for(var s=0;s<i.length;s++){var l=i[s];e.lineTo(l.x,l.y)}e.closePath&&e.closePath()}function Nh(e,t,n,r){e.arc(t,n,r,0,2*Math.PI,!1)}bl.arrowShapeImpl=function(e){return(Xo||(Xo={polygon:Mh,"triangle-backcurve":Rh,"triangle-tee":Yo,"circle-triangle":Ih,"triangle-cross":Yo,circle:Nh}))[e]};var Tn={drawElement:function(e,t,n,r,a,i){t.isNode()?this.drawNode(e,t,n,r,a,i):this.drawEdge(e,t,n,r,a,i)},drawElementOverlay:function(e,t){t.isNode()?this.drawNodeOverlay(e,t):this.drawEdgeOverlay(e,t)},drawElementUnderlay:function(e,t){t.isNode()?this.drawNodeUnderlay(e,t):this.drawEdgeUnderlay(e,t)},drawCachedElementPortion:function(e,t,n,r,a,i,o,s){var l=this,u=n.getBoundingBox(t);if(u.w!==0&&u.h!==0){var c=n.getElement(t,u,r,a,i);if(c!=null){var d=s(l,t);if(d===0)return;var h,p,f,v,m,g,y=o(l,t),b=u.x1,k=u.y1,x=u.w,w=u.h;if(y!==0){var E=n.getRotationPoint(t);f=E.x,v=E.y,e.translate(f,v),e.rotate(y),(m=l.getImgSmoothing(e))||l.setImgSmoothing(e,!0);var C=n.getRotationOffset(t);h=C.x,p=C.y}else h=b,p=k;d!==1&&(g=e.globalAlpha,e.globalAlpha=g*d),e.drawImage(c.texture.canvas,c.x,0,c.width,c.height,h,p,x,w),d!==1&&(e.globalAlpha=g),y!==0&&(e.rotate(-y),e.translate(-f,-v),m||l.setImgSmoothing(e,!1))}else n.drawElement(e,t)}}},Lh=function(){return 0},Oh=function(e,t){return e.getTextAngle(t,null)},zh=function(e,t){return e.getTextAngle(t,"source")},Vh=function(e,t){return e.getTextAngle(t,"target")},Fh=function(e,t){return t.effectiveOpacity()},Ba=function(e,t){return t.pstyle("text-opacity").pfValue*t.effectiveOpacity()};Tn.drawCachedElement=function(e,t,n,r,a,i){var o=this,s=o.data,l=s.eleTxrCache,u=s.lblTxrCache,c=s.slbTxrCache,d=s.tlbTxrCache,h=t.boundingBox(),p=i===!0?l.reasons.highQuality:null;if(h.w!==0&&h.h!==0&&t.visible()&&(!r||si(h,r))){var f=t.isEdge(),v=t.element()._private.rscratch.badLine;o.drawElementUnderlay(e,t),o.drawCachedElementPortion(e,t,l,n,a,p,Lh,Fh),f&&v||o.drawCachedElementPortion(e,t,u,n,a,p,Oh,Ba),f&&!v&&(o.drawCachedElementPortion(e,t,c,n,a,p,zh,Ba),o.drawCachedElementPortion(e,t,d,n,a,p,Vh,Ba)),o.drawElementOverlay(e,t)}},Tn.drawElements=function(e,t){for(var n=0;n<t.length;n++){var r=t[n];this.drawElement(e,r)}},Tn.drawCachedElements=function(e,t,n,r){for(var a=0;a<t.length;a++){var i=t[a];this.drawCachedElement(e,i,n,r)}},Tn.drawCachedNodes=function(e,t,n,r){for(var a=0;a<t.length;a++){var i=t[a];i.isNode()&&this.drawCachedElement(e,i,n,r)}},Tn.drawLayeredElements=function(e,t,n,r){var a=this.data.lyrTxrCache.getLayers(t,n);if(a)for(var i=0;i<a.length;i++){var o=a[i],s=o.bb;s.w!==0&&s.h!==0&&e.drawImage(o.canvas,s.x1,s.y1,s.w,s.h)}else this.drawCachedElements(e,t,n,r)};var Ot={drawEdge:function(e,t,n){var r=!(arguments.length>3&&arguments[3]!==void 0)||arguments[3],a=!(arguments.length>4&&arguments[4]!==void 0)||arguments[4],i=!(arguments.length>5&&arguments[5]!==void 0)||arguments[5],o=this,s=t._private.rscratch;if((!i||t.visible())&&!s.badLine&&s.allpts!=null&&!isNaN(s.allpts[0])){var l;n&&(l=n,e.translate(-l.x1,-l.y1));var u=i?t.pstyle("opacity").value:1,c=i?t.pstyle("line-opacity").value:1,d=t.pstyle("curve-style").value,h=t.pstyle("line-style").value,p=t.pstyle("width").pfValue,f=t.pstyle("line-cap").value,v=t.pstyle("line-outline-width").value,m=t.pstyle("line-outline-color").value,g=u*c,y=u*c,b=function(){var S=arguments.length>0&&arguments[0]!==void 0?arguments[0]:g;d==="straight-triangle"?(o.eleStrokeStyle(e,t,S),o.drawEdgeTrianglePath(t,e,s.allpts)):(e.lineWidth=p,e.lineCap=f,o.eleStrokeStyle(e,t,S),o.drawEdgePath(t,e,s.allpts,h),e.lineCap="butt")},k=function(){var S=arguments.length>0&&arguments[0]!==void 0?arguments[0]:y;o.drawArrowheads(e,t,S)};if(e.lineJoin="round",t.pstyle("ghost").value==="yes"){var x=t.pstyle("ghost-offset-x").pfValue,w=t.pstyle("ghost-offset-y").pfValue,E=t.pstyle("ghost-opacity").value,C=g*E;e.translate(x,w),b(C),k(C),e.translate(-x,-w)}else(function(){var S=arguments.length>0&&arguments[0]!==void 0?arguments[0]:g;e.lineWidth=p+v,e.lineCap=f,v>0?(o.colorStrokeStyle(e,m[0],m[1],m[2],S),d==="straight-triangle"?o.drawEdgeTrianglePath(t,e,s.allpts):(o.drawEdgePath(t,e,s.allpts,h),e.lineCap="butt")):e.lineCap="butt"})();a&&o.drawEdgeUnderlay(e,t),b(),k(),a&&o.drawEdgeOverlay(e,t),o.drawElementText(e,t,null,r),n&&e.translate(l.x1,l.y1)}}},jo=function(e){if(!["overlay","underlay"].includes(e))throw new Error("Invalid state");return function(t,n){if(n.visible()){var r=n.pstyle("".concat(e,"-opacity")).value;if(r!==0){var a=this,i=a.usePaths(),o=n._private.rscratch,s=2*n.pstyle("".concat(e,"-padding")).pfValue,l=n.pstyle("".concat(e,"-color")).value;t.lineWidth=s,o.edgeType!=="self"||i?t.lineCap="round":t.lineCap="butt",a.colorStrokeStyle(t,l[0],l[1],l[2],r),a.drawEdgePath(n,t,o.allpts,"solid")}}}};Ot.drawEdgeOverlay=jo("overlay"),Ot.drawEdgeUnderlay=jo("underlay"),Ot.drawEdgePath=function(e,t,n,r){var a,i=e._private.rscratch,o=t,s=!1,l=this.usePaths(),u=e.pstyle("line-dash-pattern").pfValue,c=e.pstyle("line-dash-offset").pfValue;if(l){var d=n.join("$");i.pathCacheKey&&i.pathCacheKey===d?(a=t=i.pathCache,s=!0):(a=t=new Path2D,i.pathCacheKey=d,i.pathCache=a)}if(o.setLineDash)switch(r){case"dotted":o.setLineDash([1,1]);break;case"dashed":o.setLineDash(u),o.lineDashOffset=c;break;case"solid":o.setLineDash([])}if(!s&&!i.badLine)switch(t.beginPath&&t.beginPath(),t.moveTo(n[0],n[1]),i.edgeType){case"bezier":case"self":case"compound":case"multibezier":for(var h=2;h+3<n.length;h+=4)t.quadraticCurveTo(n[h],n[h+1],n[h+2],n[h+3]);break;case"straight":case"haystack":for(var p=2;p+1<n.length;p+=2)t.lineTo(n[p],n[p+1]);break;case"segments":if(i.isRound){var f,v=ut(i.roundCorners);try{for(v.s();!(f=v.n()).done;)dl(t,f.value)}catch(g){v.e(g)}finally{v.f()}t.lineTo(n[n.length-2],n[n.length-1])}else for(var m=2;m+1<n.length;m+=2)t.lineTo(n[m],n[m+1])}t=o,l?t.stroke(a):t.stroke(),t.setLineDash&&t.setLineDash([])},Ot.drawEdgeTrianglePath=function(e,t,n){t.fillStyle=t.strokeStyle;for(var r=e.pstyle("width").pfValue,a=0;a+1<n.length;a+=2){var i=[n[a+2]-n[a],n[a+3]-n[a+1]],o=Math.sqrt(i[0]*i[0]+i[1]*i[1]),s=[i[1]/o,-i[0]/o],l=[s[0]*r/2,s[1]*r/2];t.beginPath(),t.moveTo(n[a]-l[0],n[a+1]-l[1]),t.lineTo(n[a]+l[0],n[a+1]+l[1]),t.lineTo(n[a+2],n[a+3]),t.closePath(),t.fill()}},Ot.drawArrowheads=function(e,t,n){var r=t._private.rscratch,a=r.edgeType==="haystack";a||this.drawArrowhead(e,t,"source",r.arrowStartX,r.arrowStartY,r.srcArrowAngle,n),this.drawArrowhead(e,t,"mid-target",r.midX,r.midY,r.midtgtArrowAngle,n),this.drawArrowhead(e,t,"mid-source",r.midX,r.midY,r.midsrcArrowAngle,n),a||this.drawArrowhead(e,t,"target",r.arrowEndX,r.arrowEndY,r.tgtArrowAngle,n)},Ot.drawArrowhead=function(e,t,n,r,a,i,o){if(!(isNaN(r)||r==null||isNaN(a)||a==null||isNaN(i)||i==null)){var s=this,l=t.pstyle(n+"-arrow-shape").value;if(l!=="none"){var u=t.pstyle(n+"-arrow-fill").value==="hollow"?"both":"filled",c=t.pstyle(n+"-arrow-fill").value,d=t.pstyle("width").pfValue,h=t.pstyle(n+"-arrow-width"),p=h.value==="match-line"?d:h.pfValue;h.units==="%"&&(p*=d);var f=t.pstyle("opacity").value;o===void 0&&(o=f);var v=e.globalCompositeOperation;o===1&&c!=="hollow"||(e.globalCompositeOperation="destination-out",s.colorFillStyle(e,255,255,255,1),s.colorStrokeStyle(e,255,255,255,1),s.drawArrowShape(t,e,u,d,l,p,r,a,i),e.globalCompositeOperation=v);var m=t.pstyle(n+"-arrow-color").value;s.colorFillStyle(e,m[0],m[1],m[2],o),s.colorStrokeStyle(e,m[0],m[1],m[2],o),s.drawArrowShape(t,e,c,d,l,p,r,a,i)}}},Ot.drawArrowShape=function(e,t,n,r,a,i,o,s,l){var u,c=this,d=this.usePaths()&&a!=="triangle-cross",h=!1,p=t,f={x:o,y:s},v=e.pstyle("arrow-scale").value,m=this.getArrowWidth(r,v),g=c.arrowShapes[a];if(d){var y=c.arrowPathCache=c.arrowPathCache||[],b=Yt(a),k=y[b];k!=null?(u=t=k,h=!0):(u=t=new Path2D,y[b]=u)}h||(t.beginPath&&t.beginPath(),d?g.draw(t,1,0,{x:0,y:0},1):g.draw(t,m,l,f,r),t.closePath&&t.closePath()),t=p,d&&(t.translate(o,s),t.rotate(l),t.scale(m,m)),n!=="filled"&&n!=="both"||(d?t.fill(u):t.fill()),n!=="hollow"&&n!=="both"||(t.lineWidth=i/(d?m:1),t.lineJoin="miter",d?t.stroke(u):t.stroke()),d&&(t.scale(1/m,1/m),t.rotate(-l),t.translate(-o,-s))};var qh={safeDrawImage:function(e,t,n,r,a,i,o,s,l,u){if(!(a<=0||i<=0||l<=0||u<=0))try{e.drawImage(t,n,r,a,i,o,s,l,u)}catch(c){we(c)}},drawInscribedImage:function(e,t,n,r,a){var i=this,o=n.position(),s=o.x,l=o.y,u=n.cy().style(),c=u.getIndexedStyle.bind(u),d=c(n,"background-fit","value",r),h=c(n,"background-repeat","value",r),p=n.width(),f=n.height(),v=2*n.padding(),m=p+(c(n,"background-width-relative-to","value",r)==="inner"?0:v),g=f+(c(n,"background-height-relative-to","value",r)==="inner"?0:v),y=n._private.rscratch,b=c(n,"background-clip","value",r)==="node",k=c(n,"background-image-opacity","value",r)*a,x=c(n,"background-image-smoothing","value",r),w=n.pstyle("corner-radius").value;w!=="auto"&&(w=n.pstyle("corner-radius").pfValue);var E=t.width||t.cachedW,C=t.height||t.cachedH;E!=null&&C!=null||(document.body.appendChild(t),E=t.cachedW=t.width||t.offsetWidth,C=t.cachedH=t.height||t.offsetHeight,document.body.removeChild(t));var S=E,P=C;if(c(n,"background-width","value",r)!=="auto"&&(S=c(n,"background-width","units",r)==="%"?c(n,"background-width","pfValue",r)*m:c(n,"background-width","pfValue",r)),c(n,"background-height","value",r)!=="auto"&&(P=c(n,"background-height","units",r)==="%"?c(n,"background-height","pfValue",r)*g:c(n,"background-height","pfValue",r)),S!==0&&P!==0){if(d==="contain")S*=_=Math.min(m/S,g/P),P*=_;else if(d==="cover"){var _;S*=_=Math.max(m/S,g/P),P*=_}var A=s-m/2,B=c(n,"background-position-x","units",r),O=c(n,"background-position-x","pfValue",r);A+=B==="%"?(m-S)*O:O;var N=c(n,"background-offset-x","units",r),V=c(n,"background-offset-x","pfValue",r);A+=N==="%"?(m-S)*V:V;var M=l-g/2,D=c(n,"background-position-y","units",r),I=c(n,"background-position-y","pfValue",r);M+=D==="%"?(g-P)*I:I;var z=c(n,"background-offset-y","units",r),q=c(n,"background-offset-y","pfValue",r);M+=z==="%"?(g-P)*q:q,y.pathCache&&(A-=s,M-=l,s=0,l=0);var j=e.globalAlpha;e.globalAlpha=k;var U=i.getImgSmoothing(e),K=!1;if(x==="no"&&U?(i.setImgSmoothing(e,!1),K=!0):x!=="yes"||U||(i.setImgSmoothing(e,!0),K=!0),h==="no-repeat")b&&(e.save(),y.pathCache?e.clip(y.pathCache):(i.nodeShapes[i.getNodeShape(n)].draw(e,s,l,m,g,w,y),e.clip())),i.safeDrawImage(e,t,0,0,E,C,A,M,S,P),b&&e.restore();else{var X=e.createPattern(t,h);e.fillStyle=X,i.nodeShapes[i.getNodeShape(n)].draw(e,s,l,m,g,w,y),e.translate(A,M),e.fill(),e.translate(-A,-M)}e.globalAlpha=j,K&&i.setImgSmoothing(e,U)}}},tn={};function Da(e,t,n,r,a){var i=arguments.length>5&&arguments[5]!==void 0?arguments[5]:5,o=arguments.length>6?arguments[6]:void 0;e.beginPath(),e.moveTo(t+i,n),e.lineTo(t+r-i,n),e.quadraticCurveTo(t+r,n,t+r,n+i),e.lineTo(t+r,n+a-i),e.quadraticCurveTo(t+r,n+a,t+r-i,n+a),e.lineTo(t+i,n+a),e.quadraticCurveTo(t,n+a,t,n+a-i),e.lineTo(t,n+i),e.quadraticCurveTo(t,n,t+i,n),e.closePath(),o?e.stroke():e.fill()}tn.eleTextBiggerThanMin=function(e,t){if(!t){var n=e.cy().zoom(),r=this.getPixelRatio(),a=Math.ceil(oi(n*r));t=Math.pow(2,a)}return!(e.pstyle("font-size").pfValue*t<e.pstyle("min-zoomed-font-size").pfValue)},tn.drawElementText=function(e,t,n,r,a){var i=!(arguments.length>5&&arguments[5]!==void 0)||arguments[5],o=this;if(r==null){if(i&&!o.eleTextBiggerThanMin(t))return}else if(r===!1)return;if(t.isNode()){var s=t.pstyle("label");if(!s||!s.value)return;var l=o.getLabelJustification(t);e.textAlign=l,e.textBaseline="bottom"}else{var u=t.element()._private.rscratch.badLine,c=t.pstyle("label"),d=t.pstyle("source-label"),h=t.pstyle("target-label");if(u||(!c||!c.value)&&(!d||!d.value)&&(!h||!h.value))return;e.textAlign="center",e.textBaseline="bottom"}var p,f=!n;n&&(p=n,e.translate(-p.x1,-p.y1)),a==null?(o.drawText(e,t,null,f,i),t.isEdge()&&(o.drawText(e,t,"source",f,i),o.drawText(e,t,"target",f,i))):o.drawText(e,t,a,f,i),n&&e.translate(p.x1,p.y1)},tn.getFontCache=function(e){var t;this.fontCaches=this.fontCaches||[];for(var n=0;n<this.fontCaches.length;n++)if((t=this.fontCaches[n]).context===e)return t;return t={context:e},this.fontCaches.push(t),t},tn.setupTextStyle=function(e,t){var n=!(arguments.length>2&&arguments[2]!==void 0)||arguments[2],r=t.pstyle("font-style").strValue,a=t.pstyle("font-size").pfValue+"px",i=t.pstyle("font-family").strValue,o=t.pstyle("font-weight").strValue,s=n?t.effectiveOpacity()*t.pstyle("text-opacity").value:1,l=t.pstyle("text-outline-opacity").value*s,u=t.pstyle("color").value,c=t.pstyle("text-outline-color").value;e.font=r+" "+o+" "+a+" "+i,e.lineJoin="round",this.colorFillStyle(e,u[0],u[1],u[2],s),this.colorStrokeStyle(e,c[0],c[1],c[2],l)},tn.getTextAngle=function(e,t){var n,r=e._private.rscratch,a=t?t+"-":"",i=e.pstyle(a+"text-rotation");if(i.strValue==="autorotate"){var o=ft(r,"labelAngle",t);n=e.isEdge()?o:0}else n=i.strValue==="none"?0:i.pfValue;return n},tn.drawText=function(e,t,n){var r=!(arguments.length>3&&arguments[3]!==void 0)||arguments[3],a=!(arguments.length>4&&arguments[4]!==void 0)||arguments[4],i=t._private.rscratch,o=a?t.effectiveOpacity():1;if(!a||o!==0&&t.pstyle("text-opacity").value!==0){n==="main"&&(n=null);var s,l,u=ft(i,"labelX",n),c=ft(i,"labelY",n),d=this.getLabelText(t,n);if(d!=null&&d!==""&&!isNaN(u)&&!isNaN(c)){this.setupTextStyle(e,t,a);var h,p=n?n+"-":"",f=ft(i,"labelWidth",n),v=ft(i,"labelHeight",n),m=t.pstyle(p+"text-margin-x").pfValue,g=t.pstyle(p+"text-margin-y").pfValue,y=t.isEdge(),b=t.pstyle("text-halign").value,k=t.pstyle("text-valign").value;switch(y&&(b="center",k="center"),u+=m,c+=g,(h=r?this.getTextAngle(t,n):0)!==0&&(s=u,l=c,e.translate(s,l),e.rotate(h),u=0,c=0),k){case"top":break;case"center":c+=v/2;break;case"bottom":c+=v}var x=t.pstyle("text-background-opacity").value,w=t.pstyle("text-border-opacity").value,E=t.pstyle("text-border-width").pfValue,C=t.pstyle("text-background-padding").pfValue,S=t.pstyle("text-background-shape").strValue.indexOf("round")===0;if(x>0||E>0&&w>0){var P=u-C;switch(b){case"left":P-=f;break;case"center":P-=f/2}var _=c-v-C,A=f+2*C,B=v+2*C;if(x>0){var O=e.fillStyle,N=t.pstyle("text-background-color").value;e.fillStyle="rgba("+N[0]+","+N[1]+","+N[2]+","+x*o+")",S?Da(e,P,_,A,B,2):e.fillRect(P,_,A,B),e.fillStyle=O}if(E>0&&w>0){var V=e.strokeStyle,M=e.lineWidth,D=t.pstyle("text-border-color").value,I=t.pstyle("text-border-style").value;if(e.strokeStyle="rgba("+D[0]+","+D[1]+","+D[2]+","+w*o+")",e.lineWidth=E,e.setLineDash)switch(I){case"dotted":e.setLineDash([1,1]);break;case"dashed":e.setLineDash([4,2]);break;case"double":e.lineWidth=E/4,e.setLineDash([]);break;case"solid":e.setLineDash([])}if(S?Da(e,P,_,A,B,2,"stroke"):e.strokeRect(P,_,A,B),I==="double"){var z=E/2;S?Da(e,P+z,_+z,A-2*z,B-2*z,2,"stroke"):e.strokeRect(P+z,_+z,A-2*z,B-2*z)}e.setLineDash&&e.setLineDash([]),e.lineWidth=M,e.strokeStyle=V}}var q=2*t.pstyle("text-outline-width").pfValue;if(q>0&&(e.lineWidth=q),t.pstyle("text-wrap").value==="wrap"){var j=ft(i,"labelWrapCachedLines",n),U=ft(i,"labelLineHeight",n),K=f/2,X=this.getLabelJustification(t);switch(X==="auto"||(b==="left"?X==="left"?u+=-f:X==="center"&&(u+=-K):b==="center"?X==="left"?u+=-K:X==="right"&&(u+=K):b==="right"&&(X==="center"?u+=K:X==="right"&&(u+=f))),k){case"top":case"center":case"bottom":c-=(j.length-1)*U}for(var H=0;H<j.length;H++)q>0&&e.strokeText(j[H],u,c),e.fillText(j[H],u,c),c+=U}else q>0&&e.strokeText(d,u,c),e.fillText(d,u,c);h!==0&&(e.rotate(-h),e.translate(-s,-l))}}};var tr={drawNode:function(e,t,n){var r,a,i=!(arguments.length>3&&arguments[3]!==void 0)||arguments[3],o=!(arguments.length>4&&arguments[4]!==void 0)||arguments[4],s=!(arguments.length>5&&arguments[5]!==void 0)||arguments[5],l=this,u=t._private,c=u.rscratch,d=t.position();if(ae(d.x)&&ae(d.y)&&(!s||t.visible())){var h,p,f=s?t.effectiveOpacity():1,v=l.usePaths(),m=!1,g=t.padding();r=t.width()+2*g,a=t.height()+2*g,n&&(p=n,e.translate(-p.x1,-p.y1));for(var y=t.pstyle("background-image").value,b=new Array(y.length),k=new Array(y.length),x=0,w=0;w<y.length;w++){var E=y[w];if(b[w]=E!=null&&E!=="none"){var C=t.cy().style().getIndexedStyle(t,"background-image-crossorigin","value",w);x++,k[w]=l.getCachedImage(E,C,function(){u.backgroundTimestamp=Date.now(),t.emitAndNotify("background")})}}var S=t.pstyle("background-blacken").value,P=t.pstyle("border-width").pfValue,_=t.pstyle("background-opacity").value*f,A=t.pstyle("border-color").value,B=t.pstyle("border-style").value,O=t.pstyle("border-join").value,N=t.pstyle("border-cap").value,V=t.pstyle("border-position").value,M=t.pstyle("border-dash-pattern").pfValue,D=t.pstyle("border-dash-offset").pfValue,I=t.pstyle("border-opacity").value*f,z=t.pstyle("outline-width").pfValue,q=t.pstyle("outline-color").value,j=t.pstyle("outline-style").value,U=t.pstyle("outline-opacity").value*f,K=t.pstyle("outline-offset").value,X=t.pstyle("corner-radius").value;X!=="auto"&&(X=t.pstyle("corner-radius").pfValue);var H=function(){var Z=arguments.length>0&&arguments[0]!==void 0?arguments[0]:_;l.eleFillStyle(e,t,Z)},W=function(){var Z=arguments.length>0&&arguments[0]!==void 0?arguments[0]:I;l.colorStrokeStyle(e,A[0],A[1],A[2],Z)},Q=function(){var Z=arguments.length>0&&arguments[0]!==void 0?arguments[0]:U;l.colorStrokeStyle(e,q[0],q[1],q[2],Z)},te=function(Z,ee,oe,ue){var re,de=l.nodePathCache=l.nodePathCache||[],pe=gs(oe==="polygon"?oe+","+ue.join(","):oe,""+ee,""+Z,""+X),ve=de[pe],Ee=!1;return ve!=null?(re=ve,Ee=!0,c.pathCache=re):(re=new Path2D,de[pe]=c.pathCache=re),{path:re,cacheHit:Ee}},ie=t.pstyle("shape").strValue,se=t.pstyle("shape-polygon-points").pfValue;if(v){e.translate(d.x,d.y);var le=te(r,a,ie,se);h=le.path,m=le.cacheHit}var fe=function(){if(!m){var Z=d;v&&(Z={x:0,y:0}),l.nodeShapes[l.getNodeShape(t)].draw(h||e,Z.x,Z.y,r,a,X,c)}v?e.fill(h):e.fill()},$=function(){for(var Z=arguments.length>0&&arguments[0]!==void 0?arguments[0]:f,ee=!(arguments.length>1&&arguments[1]!==void 0)||arguments[1],oe=u.backgrounding,ue=0,re=0;re<k.length;re++){var de=t.cy().style().getIndexedStyle(t,"background-image-containment","value",re);ee&&de==="over"||!ee&&de==="inside"?ue++:b[re]&&k[re].complete&&!k[re].error&&(ue++,l.drawInscribedImage(e,k[re],t,re,Z))}u.backgrounding=ue!==x,oe!==u.backgrounding&&t.updateStyle(!1)},ne=function(){var Z=arguments.length>0&&arguments[0]!==void 0&&arguments[0],ee=arguments.length>1&&arguments[1]!==void 0?arguments[1]:f;l.hasPie(t)&&(l.drawPie(e,t,ee),Z&&(v||l.nodeShapes[l.getNodeShape(t)].draw(e,d.x,d.y,r,a,X,c)))},T=function(){var Z=(S>0?S:-S)*(arguments.length>0&&arguments[0]!==void 0?arguments[0]:f),ee=S>0?0:255;S!==0&&(l.colorFillStyle(e,ee,ee,ee,Z),v?e.fill(h):e.fill())},R=function(){if(P>0){if(e.lineWidth=P,e.lineCap=N,e.lineJoin=O,e.setLineDash)switch(B){case"dotted":e.setLineDash([1,1]);break;case"dashed":e.setLineDash(M),e.lineDashOffset=D;break;case"solid":case"double":e.setLineDash([])}if(V!=="center"){if(e.save(),e.lineWidth*=2,V==="inside")v?e.clip(h):e.clip();else{var Z=new Path2D;Z.rect(-r/2-P,-a/2-P,r+2*P,a+2*P),Z.addPath(h),e.clip(Z,"evenodd")}v?e.stroke(h):e.stroke(),e.restore()}else v?e.stroke(h):e.stroke();if(B==="double"){e.lineWidth=P/3;var ee=e.globalCompositeOperation;e.globalCompositeOperation="destination-out",v?e.stroke(h):e.stroke(),e.globalCompositeOperation=ee}e.setLineDash&&e.setLineDash([])}},L=function(){if(z>0){if(e.lineWidth=z,e.lineCap="butt",e.setLineDash)switch(j){case"dotted":e.setLineDash([1,1]);break;case"dashed":e.setLineDash([4,2]);break;case"solid":case"double":e.setLineDash([])}var Z=d;v&&(Z={x:0,y:0});var ee=l.getNodeShape(t),oe=P;V==="inside"&&(oe=0),V==="outside"&&(oe*=2);var ue,re=(r+oe+(z+K))/r,de=(a+oe+(z+K))/a,pe=r*re,ve=a*de,Ee=l.nodeShapes[ee].points;if(v&&(ue=te(pe,ve,ee,Ee).path),ee==="ellipse")l.drawEllipsePath(ue||e,Z.x,Z.y,pe,ve);else if(["round-diamond","round-heptagon","round-hexagon","round-octagon","round-pentagon","round-polygon","round-triangle","round-tag"].includes(ee)){var ke=0,Fe=0,Se=0;ee==="round-diamond"?ke=1.4*(oe+K+z):ee==="round-heptagon"?(ke=1.075*(oe+K+z),Se=-(oe/2+K+z)/35):ee==="round-hexagon"?ke=1.12*(oe+K+z):ee==="round-pentagon"?(ke=1.13*(oe+K+z),Se=-(oe/2+K+z)/15):ee==="round-tag"?(ke=1.12*(oe+K+z),Fe=.07*(oe/2+z+K)):ee==="round-triangle"&&(ke=(oe+K+z)*(Math.PI/2),Se=-(oe+K/2+z)/Math.PI),ke!==0&&(pe=r*(re=(r+ke)/r),["round-hexagon","round-tag"].includes(ee)||(ve=a*(de=(a+ke)/a)));for(var gt=pe/2,vt=ve/2,Ze=(X=X==="auto"?ks(pe,ve):X)+(oe+z+K)/2,Le=new Array(Ee.length/2),Dt=new Array(Ee.length/2),nt=0;nt<Ee.length/2;nt++)Le[nt]={x:Z.x+Fe+gt*Ee[2*nt],y:Z.y+Se+vt*Ee[2*nt+1]};var $e,ct,it,In,yt=Le.length;for(ct=Le[yt-1],$e=0;$e<yt;$e++)it=Le[$e%yt],In=Le[($e+1)%yt],Dt[$e]=wi(ct,it,In,Ze),ct=it,it=In;l.drawRoundPolygonPath(ue||e,Z.x+Fe,Z.y+Se,r*re,a*de,Ee,Dt)}else["roundrectangle","round-rectangle"].includes(ee)?(X=X==="auto"?on(pe,ve):X,l.drawRoundRectanglePath(ue||e,Z.x,Z.y,pe,ve,X+(oe+z+K)/2)):["cutrectangle","cut-rectangle"].includes(ee)?(X=X==="auto"?8:X,l.drawCutRectanglePath(ue||e,Z.x,Z.y,pe,ve,null,X+(oe+z+K)/4)):["bottomroundrectangle","bottom-round-rectangle"].includes(ee)?(X=X==="auto"?on(pe,ve):X,l.drawBottomRoundRectanglePath(ue||e,Z.x,Z.y,pe,ve,X+(oe+z+K)/2)):ee==="barrel"?l.drawBarrelPath(ue||e,Z.x,Z.y,pe,ve):ee.startsWith("polygon")||["rhomboid","right-rhomboid","round-tag","tag","vee"].includes(ee)?(Ee=Ur(Gr(Ee,(oe+z+K)/r)),l.drawPolygonPath(ue||e,Z.x,Z.y,r,a,Ee)):(Ee=Ur(Gr(Ee,-((oe+z+K)/r))),l.drawPolygonPath(ue||e,Z.x,Z.y,r,a,Ee));if(v?e.stroke(ue):e.stroke(),j==="double"){e.lineWidth=oe/3;var Nn=e.globalCompositeOperation;e.globalCompositeOperation="destination-out",v?e.stroke(ue):e.stroke(),e.globalCompositeOperation=Nn}e.setLineDash&&e.setLineDash([])}};if(t.pstyle("ghost").value==="yes"){var Y=t.pstyle("ghost-offset-x").pfValue,F=t.pstyle("ghost-offset-y").pfValue,J=t.pstyle("ghost-opacity").value,G=J*f;e.translate(Y,F),Q(),L(),H(J*_),fe(),$(G,!0),W(J*I),R(),ne(S!==0||P!==0),$(G,!1),T(G),e.translate(-Y,-F)}v&&e.translate(-d.x,-d.y),o&&l.drawNodeUnderlay(e,t,d,r,a),v&&e.translate(d.x,d.y),Q(),L(),H(),fe(),$(f,!0),W(),R(),ne(S!==0||P!==0),$(f,!1),T(),v&&e.translate(-d.x,-d.y),l.drawElementText(e,t,null,i),o&&l.drawNodeOverlay(e,t,d,r,a),n&&e.translate(p.x1,p.y1)}}},Wo=function(e){if(!["overlay","underlay"].includes(e))throw new Error("Invalid state");return function(t,n,r,a,i){if(n.visible()){var o=n.pstyle("".concat(e,"-padding")).pfValue,s=n.pstyle("".concat(e,"-opacity")).value,l=n.pstyle("".concat(e,"-color")).value,u=n.pstyle("".concat(e,"-shape")).value,c=n.pstyle("".concat(e,"-corner-radius")).value;if(s>0){if(r=r||n.position(),a==null||i==null){var d=n.padding();a=n.width()+2*d,i=n.height()+2*d}this.colorFillStyle(t,l[0],l[1],l[2],s),this.nodeShapes[u].draw(t,r.x,r.y,a+2*o,i+2*o,c),t.fill()}}}};tr.drawNodeOverlay=Wo("overlay"),tr.drawNodeUnderlay=Wo("underlay"),tr.hasPie=function(e){return(e=e[0])._private.hasPie},tr.drawPie=function(e,t,n,r){t=t[0],r=r||t.position();var a=t.cy().style(),i=t.pstyle("pie-size"),o=r.x,s=r.y,l=t.width(),u=t.height(),c=Math.min(l,u)/2,d=0;this.usePaths()&&(o=0,s=0),i.units==="%"?c*=i.pfValue:i.pfValue!==void 0&&(c=i.pfValue/2);for(var h=1;h<=a.pieBackgroundN;h++){var p=t.pstyle("pie-"+h+"-background-size").value,f=t.pstyle("pie-"+h+"-background-color").value,v=t.pstyle("pie-"+h+"-background-opacity").value*n,m=p/100;m+d>1&&(m=1-d);var g=1.5*Math.PI+2*Math.PI*d,y=g+2*Math.PI*m;p===0||d>=1||d+m>1||(e.beginPath(),e.moveTo(o,s),e.arc(o,s,c,g,y),e.closePath(),this.colorFillStyle(e,f[0],f[1],f[2],v),e.fill(),d+=m)}};var jn,Je={};function Ko(e,t,n){var r=e.createShader(t);if(e.shaderSource(r,n),e.compileShader(r),!e.getShaderParameter(r,e.COMPILE_STATUS))throw new Error(e.getShaderInfoLog(r));return r}function Xh(e,t,n){n===void 0&&(n=t);var r=e.makeOffscreenCanvas(t,n),a=r.context=r.getContext("2d");return r.clear=function(){return a.clearRect(0,0,r.width,r.height)},r.clear(),r}function Ei(e){var t=e.pixelRatio,n=e.cy.zoom(),r=e.cy.pan();return{zoom:n*t,pan:{x:r.x*t,y:r.y*t}}}function Rr(e,t,n){var r=e[0]/255,a=e[1]/255,i=e[2]/255,o=t,s=n||new Array(4);return s[0]=r*o,s[1]=a*o,s[2]=i*o,s[3]=o,s}function Ir(e,t){var n=t||new Array(4);return n[0]=(255&e)/255,n[1]=(e>>8&255)/255,n[2]=(e>>16&255)/255,n[3]=(e>>24&255)/255,n}function Yh(e){return e[0]+(e[1]<<8)+(e[2]<<16)+(e[3]<<24)}function xl(e,t){switch(t){case"float":return[1,e.FLOAT,4];case"vec2":return[2,e.FLOAT,4];case"vec3":return[3,e.FLOAT,4];case"vec4":return[4,e.FLOAT,4];case"int":return[1,e.INT,4];case"ivec2":return[2,e.INT,4]}}function wl(e,t,n){switch(t){case e.FLOAT:return new Float32Array(n);case e.INT:return new Int32Array(n)}}function jh(e,t,n,r,a,i){switch(t){case e.FLOAT:return new Float32Array(n.buffer,i*r,a);case e.INT:return new Int32Array(n.buffer,i*r,a)}}function rt(e,t,n,r){var a=ze(xl(e,n),3),i=a[0],o=a[1],s=a[2],l=wl(e,o,t*i),u=i*s,c=e.createBuffer();e.bindBuffer(e.ARRAY_BUFFER,c),e.bufferData(e.ARRAY_BUFFER,t*u,e.DYNAMIC_DRAW),e.enableVertexAttribArray(r),o===e.FLOAT?e.vertexAttribPointer(r,i,o,!1,u,0):o===e.INT&&e.vertexAttribIPointer(r,i,o,u,0),e.vertexAttribDivisor(r,1),e.bindBuffer(e.ARRAY_BUFFER,null);for(var d=new Array(t),h=0;h<t;h++)d[h]=jh(e,o,l,u,i,h);return c.dataArray=l,c.stride=u,c.size=i,c.getView=function(p){return d[p]},c.setPoint=function(p,f,v){var m=d[p];m[0]=f,m[1]=v},c.bufferSubData=function(p){e.bindBuffer(e.ARRAY_BUFFER,c),p?e.bufferSubData(e.ARRAY_BUFFER,0,l,0,p*i):e.bufferSubData(e.ARRAY_BUFFER,0,l)},c}Je.getPixelRatio=function(){var e=this.data.contexts[0];if(this.forcedPixelRatio!=null)return this.forcedPixelRatio;var t=this.cy.window(),n=e.backingStorePixelRatio||e.webkitBackingStorePixelRatio||e.mozBackingStorePixelRatio||e.msBackingStorePixelRatio||e.oBackingStorePixelRatio||e.backingStorePixelRatio||1;return(t.devicePixelRatio||1)/n},Je.paintCache=function(e){for(var t,n=this.paintCaches=this.paintCaches||[],r=!0,a=0;a<n.length;a++)if((t=n[a]).context===e){r=!1;break}return r&&(t={context:e},n.push(t)),t},Je.createGradientStyleFor=function(e,t,n,r,a){var i,o=this.usePaths(),s=n.pstyle(t+"-gradient-stop-colors").value,l=n.pstyle(t+"-gradient-stop-positions").pfValue;if(r==="radial-gradient")if(n.isEdge()){var u=n.sourceEndpoint(),c=n.targetEndpoint(),d=n.midpoint(),h=rn(u,d),p=rn(c,d);i=e.createRadialGradient(d.x,d.y,0,d.x,d.y,Math.max(h,p))}else{var f=o?{x:0,y:0}:n.position(),v=n.paddedWidth(),m=n.paddedHeight();i=e.createRadialGradient(f.x,f.y,0,f.x,f.y,Math.max(v,m))}else if(n.isEdge()){var g=n.sourceEndpoint(),y=n.targetEndpoint();i=e.createLinearGradient(g.x,g.y,y.x,y.y)}else{var b=o?{x:0,y:0}:n.position(),k=n.paddedWidth()/2,x=n.paddedHeight()/2;switch(n.pstyle("background-gradient-direction").value){case"to-bottom":i=e.createLinearGradient(b.x,b.y-x,b.x,b.y+x);break;case"to-top":i=e.createLinearGradient(b.x,b.y+x,b.x,b.y-x);break;case"to-left":i=e.createLinearGradient(b.x+k,b.y,b.x-k,b.y);break;case"to-right":i=e.createLinearGradient(b.x-k,b.y,b.x+k,b.y);break;case"to-bottom-right":case"to-right-bottom":i=e.createLinearGradient(b.x-k,b.y-x,b.x+k,b.y+x);break;case"to-top-right":case"to-right-top":i=e.createLinearGradient(b.x-k,b.y+x,b.x+k,b.y-x);break;case"to-bottom-left":case"to-left-bottom":i=e.createLinearGradient(b.x+k,b.y-x,b.x-k,b.y+x);break;case"to-top-left":case"to-left-top":i=e.createLinearGradient(b.x+k,b.y+x,b.x-k,b.y-x)}}if(!i)return null;for(var w=l.length===s.length,E=s.length,C=0;C<E;C++)i.addColorStop(w?l[C]:C/(E-1),"rgba("+s[C][0]+","+s[C][1]+","+s[C][2]+","+a+")");return i},Je.gradientFillStyle=function(e,t,n,r){var a=this.createGradientStyleFor(e,"background",t,n,r);if(!a)return null;e.fillStyle=a},Je.colorFillStyle=function(e,t,n,r,a){e.fillStyle="rgba("+t+","+n+","+r+","+a+")"},Je.eleFillStyle=function(e,t,n){var r=t.pstyle("background-fill").value;if(r==="linear-gradient"||r==="radial-gradient")this.gradientFillStyle(e,t,r,n);else{var a=t.pstyle("background-color").value;this.colorFillStyle(e,a[0],a[1],a[2],n)}},Je.gradientStrokeStyle=function(e,t,n,r){var a=this.createGradientStyleFor(e,"line",t,n,r);if(!a)return null;e.strokeStyle=a},Je.colorStrokeStyle=function(e,t,n,r,a){e.strokeStyle="rgba("+t+","+n+","+r+","+a+")"},Je.eleStrokeStyle=function(e,t,n){var r=t.pstyle("line-fill").value;if(r==="linear-gradient"||r==="radial-gradient")this.gradientStrokeStyle(e,t,r,n);else{var a=t.pstyle("line-color").value;this.colorStrokeStyle(e,a[0],a[1],a[2],n)}},Je.matchCanvasSize=function(e){var t=this,n=t.data,r=t.findContainerClientCoords(),a=r[2],i=r[3],o=t.getPixelRatio(),s=t.motionBlurPxRatio;e!==t.data.bufferCanvases[t.MOTIONBLUR_BUFFER_NODE]&&e!==t.data.bufferCanvases[t.MOTIONBLUR_BUFFER_DRAG]||(o=s);var l,u=a*o,c=i*o;if(u!==t.canvasWidth||c!==t.canvasHeight){t.fontCaches=null;var d=n.canvasContainer;d.style.width=a+"px",d.style.height=i+"px";for(var h=0;h<t.CANVAS_LAYERS;h++)(l=n.canvases[h]).width=u,l.height=c,l.style.width=a+"px",l.style.height=i+"px";for(h=0;h<t.BUFFER_COUNT;h++)(l=n.bufferCanvases[h]).width=u,l.height=c,l.style.width=a+"px",l.style.height=i+"px";t.textureMult=1,o<=1&&(l=n.bufferCanvases[t.TEXTURE_BUFFER],t.textureMult=2,l.width=u*t.textureMult,l.height=c*t.textureMult),t.canvasWidth=u,t.canvasHeight=c,t.pixelRatio=o}},Je.renderTo=function(e,t,n,r){this.render({forcedContext:e,forcedZoom:t,forcedPan:n,drawAllLayers:!0,forcedPxRatio:r})},Je.clearCanvas=function(){var e=this,t=e.data;function n(r){r.clearRect(0,0,e.canvasWidth,e.canvasHeight)}n(t.contexts[e.NODE]),n(t.contexts[e.DRAG])},Je.render=function(e){var t=this;e=e||bs();var n=t.cy,r=e.forcedContext,a=e.drawAllLayers,i=e.drawOnlyNodeLayer,o=e.forcedZoom,s=e.forcedPan,l=e.forcedPxRatio===void 0?this.getPixelRatio():e.forcedPxRatio,u=t.data,c=u.canvasNeedsRedraw,d=t.textureOnViewport&&!r&&(t.pinching||t.hoverData.dragging||t.swipePanning||t.data.wheelZooming),h=e.motionBlur!==void 0?e.motionBlur:t.motionBlur,p=t.motionBlurPxRatio,f=n.hasCompoundNodes(),v=t.hoverData.draggingEles,m=!(!t.hoverData.selecting&&!t.touchData.selecting),g=h=h&&!r&&t.motionBlurEnabled&&!m;r||(t.prevPxRatio!==l&&(t.invalidateContainerClientCoordsCache(),t.matchCanvasSize(t.container),t.redrawHint("eles",!0),t.redrawHint("drag",!0)),t.prevPxRatio=l),!r&&t.motionBlurTimeout&&clearTimeout(t.motionBlurTimeout),h&&(t.mbFrames==null&&(t.mbFrames=0),t.mbFrames++,t.mbFrames<3&&(g=!1),t.mbFrames>t.minMbLowQualFrames&&(t.motionBlurPxRatio=t.mbPxRBlurry)),t.clearingMotionBlur&&(t.motionBlurPxRatio=1),t.textureDrawLastFrame&&!d&&(c[t.NODE]=!0,c[t.SELECT_BOX]=!0);var y=n.style(),b=n.zoom(),k=o!==void 0?o:b,x=n.pan(),w={x:x.x,y:x.y},E={zoom:b,pan:{x:x.x,y:x.y}},C=t.prevViewport;C===void 0||E.zoom!==C.zoom||E.pan.x!==C.pan.x||E.pan.y!==C.pan.y||v&&!f||(t.motionBlurPxRatio=1),s&&(w=s),k*=l,w.x*=l,w.y*=l;var S=t.getCachedZSortedEles();function P(W,Q,te,ie,se){var le=W.globalCompositeOperation;W.globalCompositeOperation="destination-out",t.colorFillStyle(W,255,255,255,t.motionBlurTransparency),W.fillRect(Q,te,ie,se),W.globalCompositeOperation=le}function _(W,Q){var te,ie,se,le;t.clearingMotionBlur||W!==u.bufferContexts[t.MOTIONBLUR_BUFFER_NODE]&&W!==u.bufferContexts[t.MOTIONBLUR_BUFFER_DRAG]?(te=w,ie=k,se=t.canvasWidth,le=t.canvasHeight):(te={x:x.x*p,y:x.y*p},ie=b*p,se=t.canvasWidth*p,le=t.canvasHeight*p),W.setTransform(1,0,0,1,0,0),Q==="motionBlur"?P(W,0,0,se,le):r||Q!==void 0&&!Q||W.clearRect(0,0,se,le),a||(W.translate(te.x,te.y),W.scale(ie,ie)),s&&W.translate(s.x,s.y),o&&W.scale(o,o)}if(d||(t.textureDrawLastFrame=!1),d){if(t.textureDrawLastFrame=!0,!t.textureCache){t.textureCache={},t.textureCache.bb=n.mutableElements().boundingBox(),t.textureCache.texture=t.data.bufferCanvases[t.TEXTURE_BUFFER];var A=t.data.bufferContexts[t.TEXTURE_BUFFER];A.setTransform(1,0,0,1,0,0),A.clearRect(0,0,t.canvasWidth*t.textureMult,t.canvasHeight*t.textureMult),t.render({forcedContext:A,drawOnlyNodeLayer:!0,forcedPxRatio:l*t.textureMult}),(E=t.textureCache.viewport={zoom:n.zoom(),pan:n.pan(),width:t.canvasWidth,height:t.canvasHeight}).mpan={x:(0-E.pan.x)/E.zoom,y:(0-E.pan.y)/E.zoom}}c[t.DRAG]=!1,c[t.NODE]=!1;var B=u.contexts[t.NODE],O=t.textureCache.texture;E=t.textureCache.viewport,B.setTransform(1,0,0,1,0,0),h?P(B,0,0,E.width,E.height):B.clearRect(0,0,E.width,E.height);var N=y.core("outside-texture-bg-color").value,V=y.core("outside-texture-bg-opacity").value;t.colorFillStyle(B,N[0],N[1],N[2],V),B.fillRect(0,0,E.width,E.height),b=n.zoom(),_(B,!1),B.clearRect(E.mpan.x,E.mpan.y,E.width/E.zoom/l,E.height/E.zoom/l),B.drawImage(O,E.mpan.x,E.mpan.y,E.width/E.zoom/l,E.height/E.zoom/l)}else t.textureOnViewport&&!r&&(t.textureCache=null);var M=n.extent(),D=t.pinching||t.hoverData.dragging||t.swipePanning||t.data.wheelZooming||t.hoverData.draggingEles||t.cy.animated(),I=t.hideEdgesOnViewport&&D,z=[];if(z[t.NODE]=!c[t.NODE]&&h&&!t.clearedForMotionBlur[t.NODE]||t.clearingMotionBlur,z[t.NODE]&&(t.clearedForMotionBlur[t.NODE]=!0),z[t.DRAG]=!c[t.DRAG]&&h&&!t.clearedForMotionBlur[t.DRAG]||t.clearingMotionBlur,z[t.DRAG]&&(t.clearedForMotionBlur[t.DRAG]=!0),c[t.NODE]||a||i||z[t.NODE]){var q=h&&!z[t.NODE]&&p!==1;_(B=r||(q?t.data.bufferContexts[t.MOTIONBLUR_BUFFER_NODE]:u.contexts[t.NODE]),h&&!q?"motionBlur":void 0),I?t.drawCachedNodes(B,S.nondrag,l,M):t.drawLayeredElements(B,S.nondrag,l,M),t.debug&&t.drawDebugPoints(B,S.nondrag),a||h||(c[t.NODE]=!1)}if(!i&&(c[t.DRAG]||a||z[t.DRAG])&&(q=h&&!z[t.DRAG]&&p!==1,_(B=r||(q?t.data.bufferContexts[t.MOTIONBLUR_BUFFER_DRAG]:u.contexts[t.DRAG]),h&&!q?"motionBlur":void 0),I?t.drawCachedNodes(B,S.drag,l,M):t.drawCachedElements(B,S.drag,l,M),t.debug&&t.drawDebugPoints(B,S.drag),a||h||(c[t.DRAG]=!1)),this.drawSelectionRectangle(e,_),h&&p!==1){var j=u.contexts[t.NODE],U=t.data.bufferCanvases[t.MOTIONBLUR_BUFFER_NODE],K=u.contexts[t.DRAG],X=t.data.bufferCanvases[t.MOTIONBLUR_BUFFER_DRAG],H=function(W,Q,te){W.setTransform(1,0,0,1,0,0),te||!g?W.clearRect(0,0,t.canvasWidth,t.canvasHeight):P(W,0,0,t.canvasWidth,t.canvasHeight);var ie=p;W.drawImage(Q,0,0,t.canvasWidth*ie,t.canvasHeight*ie,0,0,t.canvasWidth,t.canvasHeight)};(c[t.NODE]||z[t.NODE])&&(H(j,U,z[t.NODE]),c[t.NODE]=!1),(c[t.DRAG]||z[t.DRAG])&&(H(K,X,z[t.DRAG]),c[t.DRAG]=!1)}t.prevViewport=E,t.clearingMotionBlur&&(t.clearingMotionBlur=!1,t.motionBlurCleared=!0,t.motionBlur=!0),h&&(t.motionBlurTimeout=setTimeout(function(){t.motionBlurTimeout=null,t.clearedForMotionBlur[t.NODE]=!1,t.clearedForMotionBlur[t.DRAG]=!1,t.motionBlur=!1,t.clearingMotionBlur=!d,t.mbFrames=0,c[t.NODE]=!0,c[t.DRAG]=!0,t.redraw()},100)),r||n.emit("render")},Je.drawSelectionRectangle=function(e,t){var n=this,r=n.cy,a=n.data,i=r.style(),o=e.drawOnlyNodeLayer,s=e.drawAllLayers,l=a.canvasNeedsRedraw,u=e.forcedContext;if(n.showFps||!o&&l[n.SELECT_BOX]&&!s){var c=u||a.contexts[n.SELECT_BOX];if(t(c),n.selection[4]==1&&(n.hoverData.selecting||n.touchData.selecting)){var d=n.cy.zoom(),h=i.core("selection-box-border-width").value/d;c.lineWidth=h,c.fillStyle="rgba("+i.core("selection-box-color").value[0]+","+i.core("selection-box-color").value[1]+","+i.core("selection-box-color").value[2]+","+i.core("selection-box-opacity").value+")",c.fillRect(n.selection[0],n.selection[1],n.selection[2]-n.selection[0],n.selection[3]-n.selection[1]),h>0&&(c.strokeStyle="rgba("+i.core("selection-box-border-color").value[0]+","+i.core("selection-box-border-color").value[1]+","+i.core("selection-box-border-color").value[2]+","+i.core("selection-box-opacity").value+")",c.strokeRect(n.selection[0],n.selection[1],n.selection[2]-n.selection[0],n.selection[3]-n.selection[1]))}if(a.bgActivePosistion&&!n.hoverData.selecting){d=n.cy.zoom();var p=a.bgActivePosistion;c.fillStyle="rgba("+i.core("active-bg-color").value[0]+","+i.core("active-bg-color").value[1]+","+i.core("active-bg-color").value[2]+","+i.core("active-bg-opacity").value+")",c.beginPath(),c.arc(p.x,p.y,i.core("active-bg-size").pfValue/d,0,2*Math.PI),c.fill()}var f=n.lastRedrawTime;if(n.showFps&&f){f=Math.round(f);var v=Math.round(1e3/f),m="1 frame = "+f+" ms = "+v+" fps";if(c.setTransform(1,0,0,1,0,0),c.fillStyle="rgba(255, 0, 0, 0.75)",c.strokeStyle="rgba(255, 0, 0, 0.75)",c.font="30px Arial",!jn){var g=c.measureText(m);jn=g.actualBoundingBoxAscent}c.fillText(m,0,jn),c.strokeRect(0,jn+10,250,20),c.fillRect(0,jn+10,250*Math.min(v/60,1),20)}s||(l[n.SELECT_BOX]=!1)}};var Ho=typeof Float32Array<"u"?Float32Array:Array;function ar(){var e=new Ho(9);return Ho!=Float32Array&&(e[1]=0,e[2]=0,e[3]=0,e[5]=0,e[6]=0,e[7]=0),e[0]=1,e[4]=1,e[8]=1,e}function El(e){return e[0]=1,e[1]=0,e[2]=0,e[3]=0,e[4]=1,e[5]=0,e[6]=0,e[7]=0,e[8]=1,e}function ra(e,t,n){var r=t[0],a=t[1],i=t[2],o=t[3],s=t[4],l=t[5],u=t[6],c=t[7],d=t[8],h=n[0],p=n[1];return e[0]=r,e[1]=a,e[2]=i,e[3]=o,e[4]=s,e[5]=l,e[6]=h*r+p*o+u,e[7]=h*a+p*s+c,e[8]=h*i+p*l+d,e}function Tl(e,t,n){var r=t[0],a=t[1],i=t[2],o=t[3],s=t[4],l=t[5],u=t[6],c=t[7],d=t[8],h=Math.sin(n),p=Math.cos(n);return e[0]=p*r+h*o,e[1]=p*a+h*s,e[2]=p*i+h*l,e[3]=p*o-h*r,e[4]=p*s-h*a,e[5]=p*l-h*i,e[6]=u,e[7]=c,e[8]=d,e}function Ti(e,t,n){var r=n[0],a=n[1];return e[0]=r*t[0],e[1]=r*t[1],e[2]=r*t[2],e[3]=a*t[3],e[4]=a*t[4],e[5]=a*t[5],e[6]=t[6],e[7]=t[7],e[8]=t[8],e}Math.hypot||(Math.hypot=function(){for(var e=0,t=arguments.length;t--;)e+=arguments[t]*arguments[t];return Math.sqrt(e)});var ir={SCREEN:{name:"screen",screen:!0},PICKING:{name:"picking",picking:!0}},Wn=Ve({getKey:null,drawElement:null,getBoundingBox:null,getRotation:null,getRotationPoint:null,getRotationOffset:null,isVisible:null,getPadding:null}),Wh=function(){function e(t,n){Wt(this,e),this.debugID=Math.floor(1e4*Math.random()),this.r=t,this.atlasSize=n.webglTexSize,this.rows=n.webglTexRows,this.enableWrapping=n.enableWrapping,this.texHeight=Math.floor(this.atlasSize/this.rows),this.maxTexWidth=this.atlasSize,this.texture=null,this.canvas=null,this.needsBuffer=!0,this.freePointer={x:0,row:0},this.keyToLocation=new Map,this.canvas=n.createTextureCanvas(t,this.atlasSize,this.atlasSize),this.scratch=n.createTextureCanvas(t,this.atlasSize,this.texHeight,"scratch")}return Kt(e,[{key:"getKeys",value:function(){return new Set(this.keyToLocation.keys())}},{key:"getScale",value:function(t){var n=t.w,r=t.h,a=this.texHeight,i=this.maxTexWidth,o=a/r,s=n*o,l=r*o;return s>i&&(s=n*(o=i/n),l=r*o),{scale:o,texW:s,texH:l}}},{key:"draw",value:function(t,n,r){var a=this,i=this.atlasSize,o=this.rows,s=this.texHeight,l=this.getScale(n),u=l.scale,c=l.texW,d=l.texH,h=[null,null],p=function(m,g){if(r&&g){var y=g.context,b=m.x,k=m.row,x=b,w=s*k;y.save(),y.translate(x,w),y.scale(u,u),r(y,n),y.restore()}},f=function(){p(a.freePointer,a.canvas),h[0]={x:a.freePointer.x,y:a.freePointer.row*s,w:c,h:d},h[1]={x:a.freePointer.x+c,y:a.freePointer.row*s,w:0,h:d},a.freePointer.x+=c,a.freePointer.x==i&&(a.freePointer.x=0,a.freePointer.row++)},v=function(){a.freePointer.x=0,a.freePointer.row++};if(this.freePointer.x+c<=i)f();else{if(this.freePointer.row>=o-1)return!1;this.freePointer.x===i?(v(),f()):this.enableWrapping?function(){var m=a.scratch,g=a.canvas;m.clear(),p({x:0,row:0},m);var y=i-a.freePointer.x,b=c-y,k=s,x=a.freePointer.x,w=a.freePointer.row*s,E=y;g.context.drawImage(m,0,0,E,k,x,w,E,k),h[0]={x,y:w,w:E,h:d};var C=y,S=(a.freePointer.row+1)*s,P=b;g&&g.context.drawImage(m,C,0,P,k,0,S,P,k),h[1]={x:0,y:S,w:P,h:d},a.freePointer.x=b,a.freePointer.row++}():(v(),f())}return this.keyToLocation.set(t,h),this.needsBuffer=!0,h}},{key:"getOffsets",value:function(t){return this.keyToLocation.get(t)}},{key:"isEmpty",value:function(){return this.freePointer.x===0&&this.freePointer.row===0}},{key:"canFit",value:function(t){var n=this.atlasSize,r=this.rows,a=this.getScale(t).texW;return!(this.freePointer.x+a>n)||this.freePointer.row<r-1}},{key:"bufferIfNeeded",value:function(t){this.texture||(this.texture=function(n,r){var a=n.createTexture();return a.buffer=function(i){n.bindTexture(n.TEXTURE_2D,a),n.texParameteri(n.TEXTURE_2D,n.TEXTURE_WRAP_S,n.CLAMP_TO_EDGE),n.texParameteri(n.TEXTURE_2D,n.TEXTURE_WRAP_T,n.CLAMP_TO_EDGE),n.texParameteri(n.TEXTURE_2D,n.TEXTURE_MAG_FILTER,n.LINEAR),n.texParameteri(n.TEXTURE_2D,n.TEXTURE_MIN_FILTER,n.LINEAR_MIPMAP_NEAREST),n.pixelStorei(n.UNPACK_PREMULTIPLY_ALPHA_WEBGL,!0),n.texImage2D(n.TEXTURE_2D,0,n.RGBA,n.RGBA,n.UNSIGNED_BYTE,i),n.generateMipmap(n.TEXTURE_2D),n.bindTexture(n.TEXTURE_2D,null)},a.deleteTexture=function(){n.deleteTexture(a)},a}(t,this.debugID)),this.needsBuffer&&(this.texture.buffer(this.canvas),this.needsBuffer=!1)}},{key:"dispose",value:function(){this.texture&&(this.texture.deleteTexture(),this.texture=null,this.needsBuffer=!0)}}]),e}(),Kh=function(){function e(t,n){Wt(this,e),this.r=t,this.opts=n,this.keyToIds=new Map,this.idToKey=new Map,this.atlases=[],this.styleKeyToAtlas=new Map,this.styleKeyNeedsRedraw=new Set,this.forceGC=!1}return Kt(e,[{key:"getKeys",value:function(){return new Set(this.styleKeyToAtlas.keys())}},{key:"getIdsFor",value:function(t){var n=this.keyToIds.get(t);return n||(n=new Set,this.keyToIds.set(t,n)),n}},{key:"_createAtlas",value:function(){var t=this.r,n=this.opts;return new Wh(t,n)}},{key:"_getScratchCanvas",value:function(){if(!this.scratch){var t=this.r,n=this.opts,r=n.webglTexSize,a=Math.floor(r/n.webglTexRows);this.scratch=n.createTextureCanvas(t,r,a,"scratch")}return this.scratch}},{key:"draw",value:function(t,n,r,a){if(this.styleKeyNeedsRedraw.has(n)){this.styleKeyNeedsRedraw.delete(n),this.deleteKey(t,n);var i=this.styleKeyToAtlas.get(n);i&&(i.forceGC=!0),this.styleKeyToAtlas.delete(n)}var o=this.styleKeyToAtlas.get(n);return o||((o=this.atlases[this.atlases.length-1])&&o.canFit(r)||(o=this._createAtlas(),this.atlases.push(o)),o.draw(n,r,a),this.styleKeyToAtlas.set(n,o),this.getIdsFor(n).add(t),this.idToKey.set(t,n)),o}},{key:"getAtlas",value:function(t){return this.styleKeyToAtlas.get(t)}},{key:"hasAtlas",value:function(t){return this.styleKeyToAtlas.has(t)}},{key:"deleteKey",value:function(t,n){this.idToKey.delete(t),this.getIdsFor(n).delete(t)}},{key:"checkKeyIsInvalid",value:function(t,n){if(!this.idToKey.has(t))return!1;var r=this.idToKey.get(t);return r!=n&&(this.deleteKey(t,r),!0)}},{key:"_getKeysToCollect",value:function(){var t,n=new Set,r=ut(this.styleKeyToAtlas.keys());try{for(r.s();!(t=r.n()).done;){var a=t.value;this.getIdsFor(a).size==0&&n.add(a)}}catch(i){r.e(i)}finally{r.f()}return n}},{key:"gc",value:function(){var t=this,n=this.atlases.some(function(c){return c.forceGC}),r=this._getKeysToCollect();if(r.size!==0||n){var a,i=[],o=new Map,s=null,l=ut(this.atlases);try{var u=function(){var c,d,h=a.value,p=h.getKeys(),f=(d=p,(c=r).intersection?c.intersection(d):new Set(ns(c).filter(function(x){return d.has(x)})));if(f.size===0&&!h.forceGC)return i.push(h),p.forEach(function(x){return o.set(x,h)}),"continue";s||(s=t._createAtlas(),i.push(s));var v,m=ut(p);try{for(m.s();!(v=m.n()).done;){var g=v.value;if(!f.has(g)){var y=ze(h.getOffsets(g),2),b=y[0],k=y[1];s.canFit({w:b.w+k.w,h:b.h})||(s=t._createAtlas(),i.push(s)),t._copyTextureToNewAtlas(g,h,s),o.set(g,s)}}}catch(x){m.e(x)}finally{m.f()}};for(l.s();!(a=l.n()).done;)u()}catch(c){l.e(c)}finally{l.f()}this.atlases=i,this.styleKeyToAtlas=o}else console.log("nothing to garbage collect")}},{key:"_copyTextureToNewAtlas",value:function(t,n,r){var a=ze(n.getOffsets(t),2),i=a[0],o=a[1];if(o.w===0)r.draw(t,i,function(c){c.drawImage(n.canvas,i.x,i.y,i.w,i.h,0,0,i.w,i.h)});else{var s=this._getScratchCanvas();s.clear(),s.context.drawImage(n.canvas,i.x,i.y,i.w,i.h,0,0,i.w,i.h),s.context.drawImage(n.canvas,o.x,o.y,o.w,o.h,i.w,0,o.w,o.h);var l=i.w+o.w,u=i.h;r.draw(t,{w:l,h:u},function(c){c.drawImage(s,0,0,l,u,0,0,l,u)})}}},{key:"getCounts",value:function(){return{keyCount:this.styleKeyToAtlas.size,atlasCount:new Set(this.styleKeyToAtlas.values()).size}}}]),e}(),Hh=function(){function e(t,n){Wt(this,e),this.r=t;var r=n;this.globalOptions=r,this.maxAtlases=r.webglTexPerBatch,this.atlasSize=r.webglTexSize,this.renderTypes=new Map,this.maxAtlasesPerBatch=n.webglTexPerBatch,this.batchAtlases=[],this._cacheScratchCanvas(r)}return Kt(e,[{key:"_cacheScratchCanvas",value:function(t){var n=-1,r=-1,a=null,i=t.createTextureCanvas;t.createTextureCanvas=function(o,s,l,u){return u?(a&&s==n&&l==r||(n=s,r=l,a=i(o,s,l)),a):i(o,s,l)}}},{key:"addRenderType",value:function(t,n){var r=new Kh(this.r,this.globalOptions),a=n;this.renderTypes.set(t,he({type:t,atlasCollection:r},a))}},{key:"getRenderTypes",value:function(){return ns(this.renderTypes.values())}},{key:"getRenderTypeOpts",value:function(t){return this.renderTypes.get(t)}},{key:"invalidate",value:function(t){var n,r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},a=r.forceRedraw,i=a!==void 0&&a,o=r.filterEle,s=o===void 0?function(){return!0}:o,l=r.filterType,u=l===void 0?function(){return!0}:l,c=!1,d=ut(t);try{for(d.s();!(n=d.n()).done;){var h=n.value;if(s(h)){var p,f=h.id(),v=ut(this.getRenderTypes());try{for(v.s();!(p=v.n()).done;){var m=p.value;if(u(m.type)){var g=m.getKey(h);i?(m.atlasCollection.deleteKey(f,g),m.atlasCollection.styleKeyNeedsRedraw.add(g),c=!0):c|=m.atlasCollection.checkKeyIsInvalid(f,g)}}}catch(y){v.e(y)}finally{v.f()}}}}catch(y){d.e(y)}finally{d.f()}return c}},{key:"gc",value:function(){var t,n=ut(this.getRenderTypes());try{for(n.s();!(t=n.n()).done;)t.value.atlasCollection.gc()}catch(r){n.e(r)}finally{n.f()}}},{key:"isRenderable",value:function(t,n){var r=this.getRenderTypeOpts(n);return r&&r.isVisible(t)}},{key:"startBatch",value:function(){this.batchAtlases=[]}},{key:"getAtlasCount",value:function(){return this.batchAtlases.length}},{key:"getAtlases",value:function(){return this.batchAtlases}},{key:"getOrCreateAtlas",value:function(t,n,r){var a=this.renderTypes.get(r),i=a.getKey(t),o=t.id();return a.atlasCollection.draw(o,i,n,function(s){a.drawElement(s,t,n,!0,!0)})}},{key:"getAtlasIndexForBatch",value:function(t){var n=this.batchAtlases.indexOf(t);if(n<0){if(this.batchAtlases.length===this.maxAtlasesPerBatch)return;this.batchAtlases.push(t),n=this.batchAtlases.length-1}return n}},{key:"getIndexArray",value:function(){return Array.from({length:this.maxAtlases},function(t,n){return n})}},{key:"getAtlasInfo",value:function(t,n){var r=this.renderTypes.get(n),a=r.getBoundingBox(t),i=this.getOrCreateAtlas(t,a,n),o=this.getAtlasIndexForBatch(i);if(o!==void 0){var s=r.getKey(t),l=ze(i.getOffsets(s),2),u=l[0];return{atlasID:o,tex:u,tex1:u,tex2:l[1],bb:a,type:n,styleKey:s}}}},{key:"canAddToCurrentBatch",value:function(t,n){if(this.batchAtlases.length===this.maxAtlasesPerBatch){var r=this.renderTypes.get(n),a=r.getKey(t),i=r.atlasCollection.getAtlas(a);return i&&this.batchAtlases.includes(i)}return!0}},{key:"setTransformMatrix",value:function(t,n,r){var a=!(arguments.length>3&&arguments[3]!==void 0)||arguments[3],i=n.bb,o=n.type,s=n.tex1,l=n.tex2,u=this.getRenderTypeOpts(o),c=u.getPadding?u.getPadding(r):0,d=s.w/(s.w+l.w);a||(d=1-d);var h,p,f=this.getAdjustedBB(i,c,a,d);El(t);var v=u.getRotation?u.getRotation(r):0;if(v!==0){var m=u.getRotationPoint(r);ra(t,t,[m.x,m.y]),Tl(t,t,v);var g=u.getRotationOffset(r);h=g.x+f.xOffset,p=g.y}else h=f.x1,p=f.y1;ra(t,t,[h,p]),Ti(t,t,[f.w,f.h])}},{key:"getTransformMatrix",value:function(t,n){var r=!(arguments.length>2&&arguments[2]!==void 0)||arguments[2],a=ar();return this.setTransformMatrix(a,t,n,r),a}},{key:"getAdjustedBB",value:function(t,n,r,a){var i=t.x1,o=t.y1,s=t.w,l=t.h;n&&(i-=n,o-=n,s+=2*n,l+=2*n);var u=0,c=s*a;return r&&a<1?s=c:!r&&a<1&&(i+=u=s-c,s=c),{x1:i,y1:o,w:s,h:l,xOffset:u}}},{key:"getDebugInfo",value:function(){var t,n=[],r=ut(this.renderTypes);try{for(r.s();!(t=r.n()).done;){var a=ze(t.value,2),i=a[0],o=a[1].atlasCollection.getCounts(),s=o.keyCount,l=o.atlasCount;n.push({type:i,keyCount:s,atlasCount:l})}}catch(u){r.e(u)}finally{r.f()}return n}}]),e}(),Uh=function(){function e(t,n,r){Wt(this,e),this.r=t,this.gl=n,this.maxInstances=r.webglBatchSize,this.maxAtlases=r.webglTexPerBatch,this.atlasSize=r.webglTexSize,this.bgColor=r.bgColor,r.enableWrapping=!0,r.createTextureCanvas=Xh,this.atlasManager=new Hh(t,r),this.program=this.createShaderProgram(ir.SCREEN),this.pickingProgram=this.createShaderProgram(ir.PICKING),this.vao=this.createVAO(),this.debugInfo=[]}return Kt(e,[{key:"addTextureRenderType",value:function(t,n){this.atlasManager.addRenderType(t,n)}},{key:"invalidate",value:function(t){var n=(arguments.length>1&&arguments[1]!==void 0?arguments[1]:{}).type,r=this.atlasManager;return n?r.invalidate(t,{filterType:function(a){return a===n},forceRedraw:!0}):r.invalidate(t)}},{key:"gc",value:function(){this.atlasManager.gc()}},{key:"createShaderProgram",value:function(t){var n=this.gl,r=`#version 300 es
      precision highp float;

      uniform mat3 uPanZoomMatrix;
      uniform int  uAtlasSize;
      
      // instanced
      in vec2 aPosition; 

      // what are we rendering?
      in int aVertType;

      // for picking
      in vec4 aIndex;
      
      // For textures
      in int aAtlasId; // which shader unit/atlas to use
      in vec4 aTex1; // x/y/w/h of texture in atlas
      in vec4 aTex2; 

      // for any transforms that are needed
      in vec4 aScaleRotate1;  // vectors use fewer attributes than matrices
      in vec2 aTranslate1;
      in vec4 aScaleRotate2;
      in vec2 aTranslate2;

      // for edges
      in vec4 aPointAPointB;
      in vec4 aPointCPointD;
      in float aLineWidth;
      in vec4 aEdgeColor;

      out vec2 vTexCoord;
      out vec4 vEdgeColor;
      flat out int vAtlasId;
      flat out vec4 vIndex;
      flat out int vVertType;

      void main(void) {
        int vid = gl_VertexID;
        vec2 position = aPosition;

        if(aVertType == `.concat(0,`) {
          float texX;
          float texY;
          float texW;
          float texH;
          mat3  texMatrix;

          int vid = gl_VertexID;
          if(vid <= 5) {
            texX = aTex1.x;
            texY = aTex1.y;
            texW = aTex1.z;
            texH = aTex1.w;
            texMatrix = mat3(
              vec3(aScaleRotate1.xy, 0.0),
              vec3(aScaleRotate2.zw, 0.0),
              vec3(aTranslate1,      1.0)
            );
          } else {
            texX = aTex2.x;
            texY = aTex2.y;
            texW = aTex2.z;
            texH = aTex2.w;
            texMatrix = mat3(
              vec3(aScaleRotate2.xy, 0.0),
              vec3(aScaleRotate2.zw, 0.0),
              vec3(aTranslate2,      1.0)
            );
          }

          if(vid == 1 || vid == 2 || vid == 4 || vid == 7 || vid == 8 || vid == 10) {
            texX += texW;
          }
          if(vid == 2 || vid == 4 || vid == 5 || vid == 8 || vid == 10 || vid == 11) {
            texY += texH;
          }

          float d = float(uAtlasSize);
          vTexCoord = vec2(texX / d, texY / d); // tex coords must be between 0 and 1

          gl_Position = vec4(uPanZoomMatrix * texMatrix * vec3(position, 1.0), 1.0);
        } 
        else if(aVertType == `).concat(1,` && vid < 6) {
          vec2 source = aPointAPointB.xy;
          vec2 target = aPointAPointB.zw;

          // adjust the geometry so that the line is centered on the edge
          position.y = position.y - 0.5;

          vec2 xBasis = target - source;
          vec2 yBasis = normalize(vec2(-xBasis.y, xBasis.x));
          vec2 point = source + xBasis * position.x + yBasis * aLineWidth * position.y;

          gl_Position = vec4(uPanZoomMatrix * vec3(point, 1.0), 1.0);
          vEdgeColor = aEdgeColor;
        } 
        else if(aVertType == `).concat(2,` && vid < 6) {
          vec2 pointA = aPointAPointB.xy;
          vec2 pointB = aPointAPointB.zw;
          vec2 pointC = aPointCPointD.xy;
          vec2 pointD = aPointCPointD.zw;

          // adjust the geometry so that the line is centered on the edge
          position.y = position.y - 0.5;

          vec2 p0 = pointA;
          vec2 p1 = pointB;
          vec2 p2 = pointC;
          vec2 pos = position;
          if(position.x == 1.0) {
            p0 = pointD;
            p1 = pointC;
            p2 = pointB;
            pos = vec2(0.0, -position.y);
          }

          vec2 p01 = p1 - p0;
          vec2 p12 = p2 - p1;
          vec2 p21 = p1 - p2;

          // Find the normal vector.
          vec2 tangent = normalize(normalize(p12) + normalize(p01));
          vec2 normal = vec2(-tangent.y, tangent.x);

          // Find the vector perpendicular to p0 -> p1.
          vec2 p01Norm = normalize(vec2(-p01.y, p01.x));

          // Determine the bend direction.
          float sigma = sign(dot(p01 + p21, normal));
          float width = aLineWidth;

          if(sign(pos.y) == -sigma) {
            // This is an intersecting vertex. Adjust the position so that there's no overlap.
            vec2 point = 0.5 * width * normal * -sigma / dot(normal, p01Norm);
            gl_Position = vec4(uPanZoomMatrix * vec3(p1 + point, 1.0), 1.0);
          } else {
            // This is a non-intersecting vertex. Treat it like a mitre join.
            vec2 point = 0.5 * width * normal * sigma * dot(normal, p01Norm);
            gl_Position = vec4(uPanZoomMatrix * vec3(p1 + point, 1.0), 1.0);
          }

          vEdgeColor = aEdgeColor;
        } 
        else if(aVertType == `).concat(3,` && vid < 3) {
          // massage the first triangle into an edge arrow
          if(vid == 0)
            position = vec2(-0.15, -0.3);
          if(vid == 1)
            position = vec2( 0.0,   0.0);
          if(vid == 2)
            position = vec2( 0.15, -0.3);

          mat3 transform = mat3(
            vec3(aScaleRotate1.xy, 0.0),
            vec3(aScaleRotate1.zw, 0.0),
            vec3(aTranslate1,      1.0)
          );
          gl_Position = vec4(uPanZoomMatrix * transform * vec3(position, 1.0), 1.0);
          vEdgeColor = aEdgeColor;
        } else {
          gl_Position = vec4(2.0, 0.0, 0.0, 1.0); // discard vertex by putting it outside webgl clip space
        }

        vAtlasId = aAtlasId;
        vIndex = aIndex;
        vVertType = aVertType;
      }
    `),a=this.atlasManager.getIndexArray(),i=`#version 300 es
      precision highp float;

      // define texture unit for each node in the batch
      `.concat(a.map(function(l){return"uniform sampler2D uTexture".concat(l,";")}).join(`
	`),`

      uniform vec4 uBGColor;

      in vec2 vTexCoord;
      in vec4 vEdgeColor;
      flat in int vAtlasId;
      flat in vec4 vIndex;
      flat in int vVertType;

      out vec4 outColor;

      void main(void) {
        if(vVertType == `).concat(0,`) {
          `).concat(a.map(function(l){return"if(vAtlasId == ".concat(l,") outColor = texture(uTexture").concat(l,", vTexCoord);")}).join(`
	else `),`
        } else if(vVertType == `).concat(3,`) {
          // blend arrow color with background (using premultiplied alpha)
          outColor.rgb = vEdgeColor.rgb + (uBGColor.rgb * (1.0 - vEdgeColor.a)); 
          outColor.a = 1.0; // make opaque, masks out line under arrow
        } else {
          outColor = vEdgeColor;
        }

        `).concat(t.picking?`if(outColor.a == 0.0) discard;
             else outColor = vIndex;`:"",`
      }
    `),o=function(l,u,c){var d=Ko(l,l.VERTEX_SHADER,u),h=Ko(l,l.FRAGMENT_SHADER,c),p=l.createProgram();if(l.attachShader(p,d),l.attachShader(p,h),l.linkProgram(p),!l.getProgramParameter(p,l.LINK_STATUS))throw new Error("Could not initialize shaders");return p}(n,r,i);o.aPosition=n.getAttribLocation(o,"aPosition"),o.aIndex=n.getAttribLocation(o,"aIndex"),o.aVertType=n.getAttribLocation(o,"aVertType"),o.aAtlasId=n.getAttribLocation(o,"aAtlasId"),o.aTex1=n.getAttribLocation(o,"aTex1"),o.aTex2=n.getAttribLocation(o,"aTex2"),o.aScaleRotate1=n.getAttribLocation(o,"aScaleRotate1"),o.aTranslate1=n.getAttribLocation(o,"aTranslate1"),o.aScaleRotate2=n.getAttribLocation(o,"aScaleRotate2"),o.aTranslate2=n.getAttribLocation(o,"aTranslate2"),o.aPointAPointB=n.getAttribLocation(o,"aPointAPointB"),o.aPointCPointD=n.getAttribLocation(o,"aPointCPointD"),o.aLineWidth=n.getAttribLocation(o,"aLineWidth"),o.aEdgeColor=n.getAttribLocation(o,"aEdgeColor"),o.uPanZoomMatrix=n.getUniformLocation(o,"uPanZoomMatrix"),o.uAtlasSize=n.getUniformLocation(o,"uAtlasSize"),o.uBGColor=n.getUniformLocation(o,"uBGColor"),o.uTextures=[];for(var s=0;s<this.atlasManager.maxAtlases;s++)o.uTextures.push(n.getUniformLocation(o,"uTexture".concat(s)));return o}},{key:"createVAO",value:function(){var t=[0,0,1,0,1,1,0,0,1,1,0,1],n=[].concat(t,t);this.vertexCount=n.length/2;var r=this.maxInstances,a=this.gl,i=this.program,o=a.createVertexArray();return a.bindVertexArray(o),function(s,l,u,c){var d=ze(xl(s,l),2),h=d[0],p=d[1],f=wl(s,p,c),v=s.createBuffer();s.bindBuffer(s.ARRAY_BUFFER,v),s.bufferData(s.ARRAY_BUFFER,f,s.STATIC_DRAW),p===s.FLOAT?s.vertexAttribPointer(u,h,p,!1,0,0):p===s.INT&&s.vertexAttribIPointer(u,h,p,0,0),s.enableVertexAttribArray(u),s.bindBuffer(s.ARRAY_BUFFER,null)}(a,"vec2",i.aPosition,n),this.indexBuffer=rt(a,r,"vec4",i.aIndex),this.vertTypeBuffer=rt(a,r,"int",i.aVertType),this.atlasIdBuffer=rt(a,r,"int",i.aAtlasId),this.tex1Buffer=rt(a,r,"vec4",i.aTex1),this.tex2Buffer=rt(a,r,"vec4",i.aTex2),this.scaleRotate1Buffer=rt(a,r,"vec4",i.aScaleRotate1),this.translate1Buffer=rt(a,r,"vec2",i.aTranslate1),this.scaleRotate2Buffer=rt(a,r,"vec4",i.aScaleRotate2),this.translate2Buffer=rt(a,r,"vec2",i.aTranslate2),this.pointAPointBBuffer=rt(a,r,"vec4",i.aPointAPointB),this.pointCPointDBuffer=rt(a,r,"vec4",i.aPointCPointD),this.lineWidthBuffer=rt(a,r,"float",i.aLineWidth),this.edgeColorBuffer=rt(a,r,"vec4",i.aEdgeColor),a.bindVertexArray(null),o}},{key:"buffers",get:function(){var t=this;return this._buffers||(this._buffers=Object.keys(this).filter(function(n){return n.endsWith("Buffer")}).map(function(n){return t[n]})),this._buffers}},{key:"startFrame",value:function(t,n){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:ir.SCREEN;this.panZoomMatrix=t,this.debugInfo=n,this.renderTarget=r,this.startBatch()}},{key:"startBatch",value:function(){this.instanceCount=0,this.atlasManager.startBatch()}},{key:"endFrame",value:function(){this.endBatch()}},{key:"getTempMatrix",value:function(){return this.tempMatrix=this.tempMatrix||ar()}},{key:"drawTexture",value:function(t,n,r){var a=this.atlasManager;if(a.isRenderable(t,r)){a.canAddToCurrentBatch(t,r)||this.endBatch();var i=this.instanceCount;this.vertTypeBuffer.getView(i)[0]=0,Ir(n,this.indexBuffer.getView(i));var o=a.getAtlasInfo(t,r,o),s=o.atlasID,l=o.tex1,u=o.tex2;this.atlasIdBuffer.getView(i)[0]=s;var c=this.tex1Buffer.getView(i);c[0]=l.x,c[1]=l.y,c[2]=l.w,c[3]=l.h;var d=this.tex2Buffer.getView(i);d[0]=u.x,d[1]=u.y,d[2]=u.w,d[3]=u.h;for(var h=this.getTempMatrix(),p=0,f=[1,2];p<f.length;p++){var v=f[p];a.setTransformMatrix(h,o,t,v===1);var m=this["scaleRotate".concat(v,"Buffer")].getView(i);m[0]=h[0],m[1]=h[1],m[2]=h[3],m[3]=h[4];var g=this["translate".concat(v,"Buffer")].getView(i);g[0]=h[6],g[1]=h[7]}this.instanceCount++,this.instanceCount>=this.maxInstances&&this.endBatch()}}},{key:"drawEdgeArrow",value:function(t,n,r){var a,i,o,s=t._private.rscratch;if(r==="source"?(a=s.arrowStartX,i=s.arrowStartY,o=s.srcArrowAngle):(a=s.arrowEndX,i=s.arrowEndY,o=s.tgtArrowAngle),!(isNaN(a)||a==null||isNaN(i)||i==null||isNaN(o)||o==null)&&t.pstyle(r+"-arrow-shape").value!=="none"){var l=t.pstyle(r+"-arrow-color").value,u=t.pstyle("opacity").value*t.pstyle("line-opacity").value,c=t.pstyle("width").pfValue,d=t.pstyle("arrow-scale").value,h=this.r.getArrowWidth(c,d),p=this.getTempMatrix();El(p),ra(p,p,[a,i]),Ti(p,p,[h,h]),Tl(p,p,o);var f=this.instanceCount;this.vertTypeBuffer.getView(f)[0]=3,Ir(n,this.indexBuffer.getView(f)),Rr(l,u,this.edgeColorBuffer.getView(f));var v=this.scaleRotate1Buffer.getView(f);v[0]=p[0],v[1]=p[1],v[2]=p[3],v[3]=p[4];var m=this.translate1Buffer.getView(f);m[0]=p[6],m[1]=p[7],this.instanceCount++,this.instanceCount>=this.maxInstances&&this.endBatch()}}},{key:"drawEdgeLine",value:function(t,n){var r=t.pstyle("opacity").value,a=t.pstyle("line-opacity").value,i=t.pstyle("width").pfValue,o=t.pstyle("line-color").value,s=r*a,l=this.getEdgePoints(t);if(l.length/2+this.instanceCount>this.maxInstances&&this.endBatch(),l.length==4){var u=this.instanceCount;this.vertTypeBuffer.getView(u)[0]=1,Ir(n,this.indexBuffer.getView(u)),Rr(o,s,this.edgeColorBuffer.getView(u)),this.lineWidthBuffer.getView(u)[0]=i;var c=this.pointAPointBBuffer.getView(u);c[0]=l[0],c[1]=l[1],c[2]=l[2],c[3]=l[3],this.instanceCount++,this.instanceCount>=this.maxInstances&&this.endBatch()}else for(var d=0;d<l.length-2;d+=2){var h=this.instanceCount;this.vertTypeBuffer.getView(h)[0]=2,Ir(n,this.indexBuffer.getView(h)),Rr(o,s,this.edgeColorBuffer.getView(h)),this.lineWidthBuffer.getView(h)[0]=i;var p=l[d-2],f=l[d-1],v=l[d],m=l[d+1],g=l[d+2],y=l[d+3],b=l[d+4],k=l[d+5];d==0&&(p=2*v-g+.001,f=2*m-y+.001),d==l.length-4&&(b=2*g-v+.001,k=2*y-m+.001);var x=this.pointAPointBBuffer.getView(h);x[0]=p,x[1]=f,x[2]=v,x[3]=m;var w=this.pointCPointDBuffer.getView(h);w[0]=g,w[1]=y,w[2]=b,w[3]=k,this.instanceCount++,this.instanceCount>=this.maxInstances&&this.endBatch()}}},{key:"getEdgePoints",value:function(t){var n=t._private.rscratch.allpts;if(n.length==4)return n;var r=this.getNumSegments(t);return this.getCurveSegmentPoints(n,r)}},{key:"getNumSegments",value:function(t){return Math.min(Math.max(15,5),this.maxInstances)}},{key:"getCurveSegmentPoints",value:function(t,n){if(t.length==4)return t;for(var r=Array(2*(n+1)),a=0;a<=n;a++)if(a==0)r[0]=t[0],r[1]=t[1];else if(a==n)r[2*a]=t[t.length-2],r[2*a+1]=t[t.length-1];else{var i=a/n;this.setCurvePoint(t,i,r,2*a)}return r}},{key:"setCurvePoint",value:function(t,n,r,a){if(!(t.length<=2)){for(var i=Array(t.length-2),o=0;o<i.length;o+=2){var s=(1-n)*t[o]+n*t[o+2],l=(1-n)*t[o+1]+n*t[o+3];i[o]=s,i[o+1]=l}return this.setCurvePoint(i,n,r,a)}r[a]=t[0],r[a+1]=t[1]}},{key:"endBatch",value:function(){var t=this.gl,n=this.vao,r=this.vertexCount,a=this.instanceCount;if(a!==0){var i=this.renderTarget.picking?this.pickingProgram:this.program;t.useProgram(i),t.bindVertexArray(n);var o,s=ut(this.buffers);try{for(s.s();!(o=s.n()).done;)o.value.bufferSubData(a)}catch(h){s.e(h)}finally{s.f()}for(var l=this.atlasManager.getAtlases(),u=0;u<l.length;u++)l[u].bufferIfNeeded(t);for(var c=0;c<l.length;c++)t.activeTexture(t.TEXTURE0+c),t.bindTexture(t.TEXTURE_2D,l[c].texture),t.uniform1i(i.uTextures[c],c);t.uniformMatrix3fv(i.uPanZoomMatrix,!1,this.panZoomMatrix),t.uniform1i(i.uAtlasSize,this.atlasManager.atlasSize);var d=Rr(this.bgColor,1);t.uniform4fv(i.uBGColor,d),t.drawArraysInstanced(t.TRIANGLES,0,r,a),t.bindVertexArray(null),t.bindTexture(t.TEXTURE_2D,null),this.debugInfo&&this.debugInfo.push({count:a,atlasCount:l.length}),this.startBatch()}}},{key:"getDebugInfo",value:function(){return this.debugInfo}},{key:"getAtlasDebugInfo",value:function(){return this.atlasManager.getDebugInfo()}}]),e}();function Uo(e,t){return"rgba(".concat(e[0],", ").concat(e[1],", ").concat(e[2],", ").concat(t,")")}var Gh=function(){function e(t){Wt(this,e),this.r=t}return Kt(e,[{key:"getStyleKey",value:function(t,n){var r=this.getStyle(t,n),a=r.shape,i=r.opacity,o=r.color;if(!a)return null;var s=n.width(),l=n.height(),u=Uo(o,i);return Yt("".concat(a,"-").concat(s,"-").concat(l,"-").concat(u))}},{key:"isVisible",value:function(t,n){return n.pstyle("".concat(t,"-opacity")).value>0}},{key:"getStyle",value:function(t,n){return{opacity:n.pstyle("".concat(t,"-opacity")).value,color:n.pstyle("".concat(t,"-color")).value,shape:n.pstyle("".concat(t,"-shape")).value}}},{key:"getPadding",value:function(t,n){return n.pstyle("".concat(t,"-padding")).pfValue}},{key:"draw",value:function(t,n,r,a){if(this.isVisible(t,r)){var i=this.r,o=a.w,s=a.h,l=o/2,u=s/2,c=this.getStyle(t,r),d=c.shape,h=c.color,p=c.opacity;n.save(),n.fillStyle=Uo(h,p),d==="round-rectangle"||d==="roundrectangle"?i.drawRoundRectanglePath(n,l,u,o,s,"auto"):d==="ellipse"&&i.drawEllipsePath(n,l,u,o,s),n.fill(),n.restore()}}}]),e}(),kl={};function Go(e,t){var n=e.canvasWidth,r=e.canvasHeight,a=Ei(e),i=a.pan,o=a.zoom;t.setTransform(1,0,0,1,0,0),t.clearRect(0,0,n,r),t.translate(i.x,i.y),t.scale(o,o)}function Zo(e,t,n){var r,a;e.webglDebug&&(a=[],r=performance.now());var i=e.eleDrawing,o=0;if(n.screen&&e.data.canvasNeedsRedraw[e.SELECT_BOX]&&function(x,w){x.drawSelectionRectangle(w,function(E){return Go(x,E)})}(e,t),e.data.canvasNeedsRedraw[e.NODE]||n.picking){var s=function(x,w){w+=1,x.isNode()?(i.drawTexture(x,w,"node-underlay"),i.drawTexture(x,w,"node-body"),i.drawTexture(x,w,"node-label"),i.drawTexture(x,w,"node-overlay")):(i.drawEdgeLine(x,w),i.drawEdgeArrow(x,w,"source"),i.drawEdgeArrow(x,w,"target"),i.drawTexture(x,w,"edge-label"))},l=e.data.contexts[e.WEBGL];n.screen?(l.clearColor(0,0,0,0),l.enable(l.BLEND),l.blendFunc(l.ONE,l.ONE_MINUS_SRC_ALPHA)):l.disable(l.BLEND),l.clear(l.COLOR_BUFFER_BIT|l.DEPTH_BUFFER_BIT),l.viewport(0,0,l.canvas.width,l.canvas.height);var u=function(x){var w=x.canvasWidth,E=x.canvasHeight,C=Ei(x),S=C.pan,P=C.zoom,_=ar();ra(_,_,[S.x,S.y]),Ti(_,_,[P,P]);var A=ar();(function(ne,T,R){ne[0]=2/T,ne[1]=0,ne[2]=0,ne[3]=0,ne[4]=-2/R,ne[5]=0,ne[6]=-1,ne[7]=1,ne[8]=1})(A,w,E);var B,O,N,V,M,D,I,z,q,j,U,K,X,H,W,Q,te,ie,se,le,fe,$=ar();return B=$,N=_,V=(O=A)[0],M=O[1],D=O[2],I=O[3],z=O[4],q=O[5],j=O[6],U=O[7],K=O[8],X=N[0],H=N[1],W=N[2],Q=N[3],te=N[4],ie=N[5],se=N[6],le=N[7],fe=N[8],B[0]=X*V+H*I+W*j,B[1]=X*M+H*z+W*U,B[2]=X*D+H*q+W*K,B[3]=Q*V+te*I+ie*j,B[4]=Q*M+te*z+ie*U,B[5]=Q*D+te*q+ie*K,B[6]=se*V+le*I+fe*j,B[7]=se*M+le*z+fe*U,B[8]=se*D+le*q+fe*K,$}(e),c=e.getCachedZSortedEles();if(o=c.length,i.startFrame(u,a,n),n.screen){for(var d=0;d<c.nondrag.length;d++)s(c.nondrag[d],d);for(var h=0;h<c.drag.length;h++)s(c.drag[h],-1)}else if(n.picking)for(var p=0;p<c.length;p++)s(c[p],p);i.endFrame(),e.data.gc&&(console.log("Garbage Collect!"),e.data.gc=!1,i.gc()),n.screen&&e.webglDebugShowAtlases&&(function(x){var w=x.data.contexts[x.NODE];w.save(),Go(x,w),w.strokeStyle="rgba(0, 0, 0, 0.3)",w.beginPath(),w.moveTo(-1e3,0),w.lineTo(1e3,0),w.stroke(),w.beginPath(),w.moveTo(0,-1e3),w.lineTo(0,1e3),w.stroke(),w.restore()}(e),function(x){var w=function(C,S,P){for(var _=C.atlasManager.getRenderTypeOpts(S),A=x.data.contexts[x.NODE],B=_.atlasCollection.atlases,O=0;O<B.length;O++){var N=B[O].canvas,V=N.width,M=N.height,D=V*O,I=N.height*P;A.save(),A.scale(.125,.125),A.drawImage(N,D,I),A.strokeStyle="black",A.rect(D,I,V,M),A.stroke(),A.restore()}},E=0;w(x.eleDrawing,"node-body",E++),w(x.eleDrawing,"node-label",E++)}(e)),e.data.canvasNeedsRedraw[e.NODE]=!1,e.data.canvasNeedsRedraw[e.DRAG]=!1}if(e.webglDebug){var f,v=performance.now(),m=0,g=0,y=ut(a);try{for(y.s();!(f=y.n()).done;){var b=f.value;m++,g+=b.count}}catch(x){y.e(x)}finally{y.f()}Math.ceil(v-r);var k="".concat(o," elements, ").concat(g," rectangles, ").concat(m," batches");console.log("WebGL (".concat(n.name,") - ").concat(k))}}kl.initWebgl=function(e,t){var n=this,r=n.data.contexts[n.WEBGL],a=e.cy.container();e.bgColor=function(u){var c=u&&u.style&&u.style.backgroundColor||"white";return ls(c)}(a),e.webglTexSize=Math.min(e.webglTexSize,r.getParameter(r.MAX_TEXTURE_SIZE)),e.webglTexRows=Math.min(e.webglTexRows,54),e.webglBatchSize=Math.min(e.webglBatchSize,16384),e.webglTexPerBatch=Math.min(e.webglTexPerBatch,r.getParameter(r.MAX_TEXTURE_IMAGE_UNITS)),n.webglDebug=e.webglDebug,n.webglDebugShowAtlases=e.webglDebugShowAtlases,console.log("max texture units",r.getParameter(r.MAX_TEXTURE_IMAGE_UNITS)),console.log("max texture size",r.getParameter(r.MAX_TEXTURE_SIZE)),console.log("webgl options",e),n.pickingFrameBuffer=function(u){var c=u.createFramebuffer();u.bindFramebuffer(u.FRAMEBUFFER,c);var d=u.createTexture();return u.bindTexture(u.TEXTURE_2D,d),u.texParameteri(u.TEXTURE_2D,u.TEXTURE_MIN_FILTER,u.LINEAR),u.texParameteri(u.TEXTURE_2D,u.TEXTURE_WRAP_S,u.CLAMP_TO_EDGE),u.texParameteri(u.TEXTURE_2D,u.TEXTURE_WRAP_T,u.CLAMP_TO_EDGE),u.framebufferTexture2D(u.FRAMEBUFFER,u.COLOR_ATTACHMENT0,u.TEXTURE_2D,d,0),u.bindFramebuffer(u.FRAMEBUFFER,null),c.setFramebufferAttachmentSizes=function(h,p){u.bindTexture(u.TEXTURE_2D,d),u.texImage2D(u.TEXTURE_2D,0,u.RGBA,h,p,0,u.RGBA,u.UNSIGNED_BYTE,null)},c}(r),n.pickingFrameBuffer.needsDraw=!0;var i=function(u){return n.getTextAngle(u,null)},o=function(u){var c=u.pstyle("label");return c&&c.value};n.eleDrawing=new Uh(n,r,e);var s=new Gh(n);n.eleDrawing.addTextureRenderType("node-body",Wn({getKey:t.getStyleKey,getBoundingBox:t.getElementBox,drawElement:t.drawElement,isVisible:function(u){return u.visible()}})),n.eleDrawing.addTextureRenderType("node-label",Wn({getKey:t.getLabelKey,getBoundingBox:t.getLabelBox,drawElement:t.drawLabel,getRotation:i,getRotationPoint:t.getLabelRotationPoint,getRotationOffset:t.getLabelRotationOffset,isVisible:o})),n.eleDrawing.addTextureRenderType("node-overlay",Wn({getBoundingBox:t.getElementBox,getKey:function(u){return s.getStyleKey("overlay",u)},drawElement:function(u,c,d){return s.draw("overlay",u,c,d)},isVisible:function(u){return s.isVisible("overlay",u)},getPadding:function(u){return s.getPadding("overlay",u)}})),n.eleDrawing.addTextureRenderType("node-underlay",Wn({getBoundingBox:t.getElementBox,getKey:function(u){return s.getStyleKey("underlay",u)},drawElement:function(u,c,d){return s.draw("underlay",u,c,d)},isVisible:function(u){return s.isVisible("underlay",u)},getPadding:function(u){return s.getPadding("underlay",u)}})),n.eleDrawing.addTextureRenderType("edge-label",Wn({getKey:t.getLabelKey,getBoundingBox:t.getLabelBox,drawElement:t.drawLabel,getRotation:i,getRotationPoint:t.getLabelRotationPoint,getRotationOffset:t.getLabelRotationOffset,isVisible:o}));var l=vr(function(){console.log("garbage collect flag set"),n.data.gc=!0},1e4);n.onUpdateEleCalcs(function(u,c){var d=!1;c&&c.length>0&&(d|=n.eleDrawing.invalidate(c)),d&&l()}),function(u){var c=u.render;u.render=function(f){f=f||{};var v=u.cy;u.webgl&&(v.zoom()>Dh?(function(m){var g=m.data.contexts[m.WEBGL];g.clear(g.COLOR_BUFFER_BIT|g.DEPTH_BUFFER_BIT)}(u),c.call(u,f)):(function(m){var g=function(y){y.save(),y.setTransform(1,0,0,1,0,0),y.clearRect(0,0,m.canvasWidth,m.canvasHeight),y.restore()};g(m.data.contexts[m.NODE]),g(m.data.contexts[m.DRAG])}(u),Zo(u,f,ir.SCREEN)))};var d=u.matchCanvasSize;u.matchCanvasSize=function(f){d.call(u,f),u.pickingFrameBuffer.setFramebufferAttachmentSizes(u.canvasWidth,u.canvasHeight),u.pickingFrameBuffer.needsDraw=!0},u.findNearestElements=function(f,v,m,g){return function(y,b,k){var x,w,E,C=function(A,B,O,N,V){var M,D,I,z,q=Ei(A),j=q.pan,U=q.zoom,K=function(ne,T,R,L,Y){var F=L*R+T.x,J=Y*R+T.y;return[F,J=Math.round(ne.canvasHeight-J)]}(A,j,U,B,O),X=ze(K,2),H=X[0],W=X[1],Q=6;if(M=H-Q/2,D=W-Q/2,z=Q,(I=Q)===0||z===0)return[];var te=A.data.contexts[A.WEBGL];te.bindFramebuffer(te.FRAMEBUFFER,A.pickingFrameBuffer),A.pickingFrameBuffer.needsDraw&&(te.viewport(0,0,te.canvas.width,te.canvas.height),Zo(A,null,ir.PICKING),A.pickingFrameBuffer.needsDraw=!1);var ie=I*z,se=new Uint8Array(4*ie);te.readPixels(M,D,I,z,te.RGBA,te.UNSIGNED_BYTE,se),te.bindFramebuffer(te.FRAMEBUFFER,null);for(var le=new Set,fe=0;fe<ie;fe++){var $=Yh(se.slice(4*fe,4*fe+4))-1;$>=0&&le.add($)}return le}(y,b,k),S=y.getCachedZSortedEles(),P=ut(C);try{for(P.s();!(E=P.n()).done;){var _=S[E.value];if(!x&&_.isNode()&&(x=_),!w&&_.isEdge()&&(w=_),x&&w)break}}catch(A){P.e(A)}finally{P.f()}return[x,w].filter(Boolean)}(u,f,v)};var h=u.invalidateCachedZSortedEles;u.invalidateCachedZSortedEles=function(){h.call(u),u.pickingFrameBuffer.needsDraw=!0};var p=u.notify;u.notify=function(f,v){p.call(u,f,v),f==="viewport"||f==="bounds"?u.pickingFrameBuffer.needsDraw=!0:f==="background"&&u.eleDrawing.invalidate(v,{type:"node-body"})}}(n)};for(var Cl={drawPolygonPath:function(e,t,n,r,a,i){var o=r/2,s=a/2;e.beginPath&&e.beginPath(),e.moveTo(t+o*i[0],n+s*i[1]);for(var l=1;l<i.length/2;l++)e.lineTo(t+o*i[2*l],n+s*i[2*l+1]);e.closePath()},drawRoundPolygonPath:function(e,t,n,r,a,i,o){o.forEach(function(s){return dl(e,s)}),e.closePath()},drawRoundRectanglePath:function(e,t,n,r,a,i){var o=r/2,s=a/2,l=i==="auto"?on(r,a):Math.min(i,s,o);e.beginPath&&e.beginPath(),e.moveTo(t,n-s),e.arcTo(t+o,n-s,t+o,n,l),e.arcTo(t+o,n+s,t,n+s,l),e.arcTo(t-o,n+s,t-o,n,l),e.arcTo(t-o,n-s,t,n-s,l),e.lineTo(t,n-s),e.closePath()},drawBottomRoundRectanglePath:function(e,t,n,r,a,i){var o=r/2,s=a/2,l=i==="auto"?on(r,a):i;e.beginPath&&e.beginPath(),e.moveTo(t,n-s),e.lineTo(t+o,n-s),e.lineTo(t+o,n),e.arcTo(t+o,n+s,t,n+s,l),e.arcTo(t-o,n+s,t-o,n,l),e.lineTo(t-o,n-s),e.lineTo(t,n-s),e.closePath()},drawCutRectanglePath:function(e,t,n,r,a,i,o){var s=r/2,l=a/2,u=o==="auto"?8:o;e.beginPath&&e.beginPath(),e.moveTo(t-s+u,n-l),e.lineTo(t+s-u,n-l),e.lineTo(t+s,n-l+u),e.lineTo(t+s,n+l-u),e.lineTo(t+s-u,n+l),e.lineTo(t-s+u,n+l),e.lineTo(t-s,n+l-u),e.lineTo(t-s,n-l+u),e.closePath()},drawBarrelPath:function(e,t,n,r,a){var i=r/2,o=a/2,s=t-i,l=t+i,u=n-o,c=n+o,d=Ra(r,a),h=d.widthOffset,p=d.heightOffset,f=d.ctrlPtOffsetPct*h;e.beginPath&&e.beginPath(),e.moveTo(s,u+p),e.lineTo(s,c-p),e.quadraticCurveTo(s+f,c,s+h,c),e.lineTo(l-h,c),e.quadraticCurveTo(l-f,c,l,c-p),e.lineTo(l,u+p),e.quadraticCurveTo(l-f,u,l-h,u),e.lineTo(s+h,u),e.quadraticCurveTo(s+f,u,s,u+p),e.closePath()}},$o=Math.sin(0),Qo=Math.cos(0),Qa={},Ja={},Pl=Math.PI/40,pn=0*Math.PI;pn<2*Math.PI;pn+=Pl)Qa[pn]=Math.sin(pn),Ja[pn]=Math.cos(pn);Cl.drawEllipsePath=function(e,t,n,r,a){if(e.beginPath&&e.beginPath(),e.ellipse)e.ellipse(t,n,r/2,a/2,0,0,2*Math.PI);else for(var i,o,s=r/2,l=a/2,u=0*Math.PI;u<2*Math.PI;u+=Pl)i=t-s*Qa[u]*$o+s*Ja[u]*Qo,o=n+l*Ja[u]*$o+l*Qa[u]*Qo,u===0?e.moveTo(i,o):e.lineTo(i,o);e.closePath()};var nr={};function Jo(e){var t=e.indexOf(",");return e.substr(t+1)}function es(e,t,n){var r=function(){return t.toDataURL(n,e.quality)};switch(e.output){case"blob-promise":return new Mn(function(a,i){try{t.toBlob(function(o){o!=null?a(o):i(new Error("`canvas.toBlob()` sent a null value in its callback"))},n,e.quality)}catch(o){i(o)}});case"blob":return function(a,i){for(var o=atob(a),s=new ArrayBuffer(o.length),l=new Uint8Array(s),u=0;u<o.length;u++)l[u]=o.charCodeAt(u);return new Blob([s],{type:i})}(Jo(r()),n);case"base64":return Jo(r());default:return r()}}nr.createBuffer=function(e,t){var n=document.createElement("canvas");return n.width=e,n.height=t,[n,n.getContext("2d")]},nr.bufferCanvasImage=function(e){var t=this.cy,n=t.mutableElements().boundingBox(),r=this.findContainerClientCoords(),a=e.full?Math.ceil(n.w):r[2],i=e.full?Math.ceil(n.h):r[3],o=ae(e.maxWidth)||ae(e.maxHeight),s=this.getPixelRatio(),l=1;if(e.scale!==void 0)a*=e.scale,i*=e.scale,l=e.scale;else if(o){var u=1/0,c=1/0;ae(e.maxWidth)&&(u=l*e.maxWidth/a),ae(e.maxHeight)&&(c=l*e.maxHeight/i),a*=l=Math.min(u,c),i*=l}o||(a*=s,i*=s,l*=s);var d=document.createElement("canvas");d.width=a,d.height=i,d.style.width=a+"px",d.style.height=i+"px";var h=d.getContext("2d");if(a>0&&i>0){h.clearRect(0,0,a,i),h.globalCompositeOperation="source-over";var p=this.getCachedZSortedEles();if(e.full)h.translate(-n.x1*l,-n.y1*l),h.scale(l,l),this.drawElements(h,p),h.scale(1/l,1/l),h.translate(n.x1*l,n.y1*l);else{var f=t.pan(),v={x:f.x*l,y:f.y*l};l*=t.zoom(),h.translate(v.x,v.y),h.scale(l,l),this.drawElements(h,p),h.scale(1/l,1/l),h.translate(-v.x,-v.y)}e.bg&&(h.globalCompositeOperation="destination-over",h.fillStyle=e.bg,h.rect(0,0,a,i),h.fill())}return d},nr.png=function(e){return es(e,this.bufferCanvasImage(e),"image/png")},nr.jpg=function(e){return es(e,this.bufferCanvasImage(e),"image/jpeg")};var Zh={nodeShapeImpl:function(e,t,n,r,a,i,o,s){switch(e){case"ellipse":return this.drawEllipsePath(t,n,r,a,i);case"polygon":return this.drawPolygonPath(t,n,r,a,i,o);case"round-polygon":return this.drawRoundPolygonPath(t,n,r,a,i,o,s);case"roundrectangle":case"round-rectangle":return this.drawRoundRectanglePath(t,n,r,a,i,s);case"cutrectangle":case"cut-rectangle":return this.drawCutRectanglePath(t,n,r,a,i,o,s);case"bottomroundrectangle":case"bottom-round-rectangle":return this.drawBottomRoundRectanglePath(t,n,r,a,i,s);case"barrel":return this.drawBarrelPath(t,n,r,a,i)}}},$h=Sl,ge=Sl.prototype;function Sl(e){var t=this,n=t.cy.window().document;e.webgl&&(ge.CANVAS_LAYERS=t.CANVAS_LAYERS=4,console.log("webgl rendering enabled")),t.data={canvases:new Array(ge.CANVAS_LAYERS),contexts:new Array(ge.CANVAS_LAYERS),canvasNeedsRedraw:new Array(ge.CANVAS_LAYERS),bufferCanvases:new Array(ge.BUFFER_COUNT),bufferContexts:new Array(ge.CANVAS_LAYERS)};var r="-webkit-tap-highlight-color",a="rgba(0,0,0,0)";t.data.canvasContainer=n.createElement("div");var i=t.data.canvasContainer.style;t.data.canvasContainer.style[r]=a,i.position="relative",i.zIndex="0",i.overflow="hidden";var o=e.cy.container();o.appendChild(t.data.canvasContainer),o.style[r]=a;var s={"-webkit-user-select":"none","-moz-user-select":"-moz-none","user-select":"none","-webkit-tap-highlight-color":"rgba(0,0,0,0)","outline-style":"none"};Si&&Si.userAgent.match(/msie|trident|edge/i)&&(s["-ms-touch-action"]="none",s["touch-action"]="none");for(var l=0;l<ge.CANVAS_LAYERS;l++){var u=t.data.canvases[l]=n.createElement("canvas"),c=ge.CANVAS_TYPES[l];t.data.contexts[l]=u.getContext(c),t.data.contexts[l]||De("Could not create canvas of type "+c),Object.keys(s).forEach(function(D){u.style[D]=s[D]}),u.style.position="absolute",u.setAttribute("data-id","layer"+l),u.style.zIndex=String(ge.CANVAS_LAYERS-l),t.data.canvasContainer.appendChild(u),t.data.canvasNeedsRedraw[l]=!1}for(t.data.topCanvas=t.data.canvases[0],t.data.canvases[ge.NODE].setAttribute("data-id","layer"+ge.NODE+"-node"),t.data.canvases[ge.SELECT_BOX].setAttribute("data-id","layer"+ge.SELECT_BOX+"-selectbox"),t.data.canvases[ge.DRAG].setAttribute("data-id","layer"+ge.DRAG+"-drag"),t.data.canvases[ge.WEBGL]&&t.data.canvases[ge.WEBGL].setAttribute("data-id","layer"+ge.WEBGL+"-webgl"),l=0;l<ge.BUFFER_COUNT;l++)t.data.bufferCanvases[l]=n.createElement("canvas"),t.data.bufferContexts[l]=t.data.bufferCanvases[l].getContext("2d"),t.data.bufferCanvases[l].style.position="absolute",t.data.bufferCanvases[l].setAttribute("data-id","buffer"+l),t.data.bufferCanvases[l].style.zIndex=String(-l-1),t.data.bufferCanvases[l].style.visibility="hidden";t.pathsEnabled=!0;var d=tt(),h=function(D){return{x:-D.w/2,y:-D.h/2}},p=function(D){return D[0]._private.nodeKey},f=function(D){return D[0]._private.labelStyleKey},v=function(D,I,z,q,j){return t.drawElement(D,I,z,!1,!1,j)},m=function(D,I,z,q,j){return t.drawElementText(D,I,z,q,"main",j)},g=function(D){return D.boundingBox(),D[0]._private.bodyBounds},y=function(D){return D.boundingBox(),D[0]._private.labelBounds.main||d},b=function(D){return D.boundingBox(),D[0]._private.labelBounds.source||d},k=function(D){return D.boundingBox(),D[0]._private.labelBounds.target||d},x=function(D,I){return I},w=function(D){return{x:((I=g(D)).x1+I.x2)/2,y:(I.y1+I.y2)/2};var I},E=function(D,I,z){var q=D?D+"-":"";return{x:I.x+z.pstyle(q+"text-margin-x").pfValue,y:I.y+z.pstyle(q+"text-margin-y").pfValue}},C=function(D,I,z){var q=D[0]._private.rscratch;return{x:q[I],y:q[z]}},S=function(D){return E("",C(D,"labelX","labelY"),D)},P=function(D){return h(g(D))},_=function(D){var I=y(D),z=h(y(D));if(D.isNode()){switch(D.pstyle("text-halign").value){case"left":z.x=-I.w-(I.leftPad||0);break;case"right":z.x=-(I.rightPad||0)}switch(D.pstyle("text-valign").value){case"top":z.y=-I.h-(I.topPad||0);break;case"bottom":z.y=-(I.botPad||0)}}return z},A=t.data.eleTxrCache=new er(t,{getKey:p,doesEleInvalidateKey:function(D){var I=D[0]._private;return I.oldBackgroundTimestamp!==I.backgroundTimestamp},drawElement:v,getBoundingBox:g,getRotationPoint:w,getRotationOffset:P,allowEdgeTxrCaching:!1,allowParentTxrCaching:!1}),B=t.data.lblTxrCache=new er(t,{getKey:f,drawElement:m,getBoundingBox:y,getRotationPoint:S,getRotationOffset:_,isVisible:x}),O=t.data.slbTxrCache=new er(t,{getKey:function(D){return D[0]._private.sourceLabelStyleKey},drawElement:function(D,I,z,q,j){return t.drawElementText(D,I,z,q,"source",j)},getBoundingBox:b,getRotationPoint:function(D){return E("source",C(D,"sourceLabelX","sourceLabelY"),D)},getRotationOffset:function(D){return h(b(D))},isVisible:x}),N=t.data.tlbTxrCache=new er(t,{getKey:function(D){return D[0]._private.targetLabelStyleKey},drawElement:function(D,I,z,q,j){return t.drawElementText(D,I,z,q,"target",j)},getBoundingBox:k,getRotationPoint:function(D){return E("target",C(D,"targetLabelX","targetLabelY"),D)},getRotationOffset:function(D){return h(k(D))},isVisible:x}),V=t.data.lyrTxrCache=new ml(t);t.onUpdateEleCalcs(function(D,I){A.invalidateElements(I),B.invalidateElements(I),O.invalidateElements(I),N.invalidateElements(I),V.invalidateElements(I);for(var z=0;z<I.length;z++){var q=I[z]._private;q.oldBackgroundTimestamp=q.backgroundTimestamp}});var M=function(D){for(var I=0;I<D.length;I++)V.enqueueElementRefinement(D[I].ele)};A.onDequeue(M),B.onDequeue(M),O.onDequeue(M),N.onDequeue(M),e.webgl&&t.initWebgl(e,{getStyleKey:p,getLabelKey:f,drawElement:v,drawLabel:m,getElementBox:g,getLabelBox:y,getElementRotationPoint:w,getElementRotationOffset:P,getLabelRotationPoint:S,getLabelRotationOffset:_})}ge.CANVAS_LAYERS=3,ge.SELECT_BOX=0,ge.DRAG=1,ge.NODE=2,ge.WEBGL=3,ge.CANVAS_TYPES=["2d","2d","2d","webgl2"],ge.BUFFER_COUNT=3,ge.TEXTURE_BUFFER=0,ge.MOTIONBLUR_BUFFER_NODE=1,ge.MOTIONBLUR_BUFFER_DRAG=2,ge.redrawHint=function(e,t){var n=this;switch(e){case"eles":n.data.canvasNeedsRedraw[ge.NODE]=t;break;case"drag":n.data.canvasNeedsRedraw[ge.DRAG]=t;break;case"select":n.data.canvasNeedsRedraw[ge.SELECT_BOX]=t;break;case"gc":n.data.gc=!0}};var Qh=typeof Path2D<"u";ge.path2dEnabled=function(e){if(e===void 0)return this.pathsEnabled;this.pathsEnabled=!!e},ge.usePaths=function(){return Qh&&this.pathsEnabled},ge.setImgSmoothing=function(e,t){e.imageSmoothingEnabled!=null?e.imageSmoothingEnabled=t:(e.webkitImageSmoothingEnabled=t,e.mozImageSmoothingEnabled=t,e.msImageSmoothingEnabled=t)},ge.getImgSmoothing=function(e){return e.imageSmoothingEnabled!=null?e.imageSmoothingEnabled:e.webkitImageSmoothingEnabled||e.mozImageSmoothingEnabled||e.msImageSmoothingEnabled},ge.makeOffscreenCanvas=function(e,t){var n;return(typeof OffscreenCanvas>"u"?"undefined":Ne(OffscreenCanvas))!=="undefined"?n=new OffscreenCanvas(e,t):((n=this.cy.window().document.createElement("canvas")).width=e,n.height=t),n},[bl,Tn,Ot,qh,tn,tr,Je,kl,Cl,nr,Zh].forEach(function(e){he(ge,e)});var Jh=[{type:"layout",extensions:bh},{type:"renderer",extensions:[{name:"null",impl:ll},{name:"base",impl:vl},{name:"canvas",impl:$h}]}],Bl={},Dl={};function _l(e,t,n){var r=n,a=function(w){we("Can not register `"+t+"` for `"+e+"` since `"+w+"` already exists in the prototype and can not be overridden")};if(e==="core"){if(hr.prototype[t])return a(t);hr.prototype[t]=n}else if(e==="collection"){if(je.prototype[t])return a(t);je.prototype[t]=n}else if(e==="layout"){for(var i=function(w){this.options=w,n.call(this,w),me(this._private)||(this._private={}),this._private.cy=w.cy,this._private.listeners=[],this.createEmitter()},o=i.prototype=Object.create(n.prototype),s=[],l=0;l<s.length;l++){var u=s[l];o[u]=o[u]||function(){return this}}o.start&&!o.run?o.run=function(){return this.start(),this}:!o.start&&o.run&&(o.start=function(){return this.run(),this});var c=n.prototype.stop;o.stop=function(){var w=this.options;if(w&&w.animate){var E=this.animations;if(E)for(var C=0;C<E.length;C++)E[C].stop()}return c?c.call(this):this.emit("layoutstop"),this},o.destroy||(o.destroy=function(){return this}),o.cy=function(){return this._private.cy};var d=function(w){return w._private.cy},h={addEventFields:function(w,E){E.layout=w,E.cy=d(w),E.target=w},bubble:function(){return!0},parent:function(w){return d(w)}};he(o,{createEmitter:function(){return this._private.emitter=new ha(h,this),this},emitter:function(){return this._private.emitter},on:function(w,E){return this.emitter().on(w,E),this},one:function(w,E){return this.emitter().one(w,E),this},once:function(w,E){return this.emitter().one(w,E),this},removeListener:function(w,E){return this.emitter().removeListener(w,E),this},removeAllListeners:function(){return this.emitter().removeAllListeners(),this},emit:function(w,E){return this.emitter().emit(w,E),this}}),xe.eventAliasesOn(o),r=i}else if(e==="renderer"&&t!=="null"&&t!=="base"){var p=Al("renderer","base"),f=p.prototype,v=n,m=n.prototype,g=function(){p.apply(this,arguments),v.apply(this,arguments)},y=g.prototype;for(var b in f){var k=f[b];if(m[b]!=null)return a(b);y[b]=k}for(var x in m)y[x]=m[x];f.clientFunctions.forEach(function(w){y[w]=y[w]||function(){De("Renderer does not implement `renderer."+w+"()` on its prototype")}}),r=g}else if(e==="__proto__"||e==="constructor"||e==="prototype")return De(e+" is an illegal type to be registered, possibly lead to prototype pollutions");return us({map:Bl,keys:[e,t],value:r})}function Al(e,t){return cs({map:Bl,keys:[e,t]})}function ef(e,t,n,r,a){return us({map:Dl,keys:[e,t,n,r],value:a})}function tf(e,t,n,r){return cs({map:Dl,keys:[e,t,n,r]})}var ei=function(){return arguments.length===2?Al.apply(null,arguments):arguments.length===3?_l.apply(null,arguments):arguments.length===4?tf.apply(null,arguments):arguments.length===5?ef.apply(null,arguments):void De("Invalid extension access syntax")};hr.prototype.extension=ei,Jh.forEach(function(e){e.extensions.forEach(function(t){_l(e.type,t.name,t.impl)})});var Ml=function e(){if(!(this instanceof e))return new e;this.length=0},Zt=Ml.prototype;Zt.instanceString=function(){return"stylesheet"},Zt.selector=function(e){return this[this.length++]={selector:e,properties:[]},this},Zt.css=function(e,t){var n=this.length-1;if(ce(e))this[n].properties.push({name:e,value:t});else if(me(e))for(var r=e,a=Object.keys(r),i=0;i<a.length;i++){var o=a[i],s=r[o];if(s!=null){var l=Ge.properties[o]||Ge.properties[aa(o)];if(l!=null){var u=l.name,c=s;this[n].properties.push({name:u,value:c})}}}return this},Zt.style=Zt.css,Zt.generateStyle=function(e){var t=new Ge(e);return this.appendToStyle(t)},Zt.appendToStyle=function(e){for(var t=0;t<this.length;t++){var n=this[t],r=n.selector,a=n.properties;e.selector(r);for(var i=0;i<a.length;i++){var o=a[i];e.css(o.name,o.value)}}return e};var gn=function(e){return e===void 0&&(e={}),me(e)?new hr(e):ce(e)?ei.apply(ei,arguments):void 0};gn.use=function(e){var t=Array.prototype.slice.call(arguments,1);return t.unshift(gn),e.apply(null,t),this},gn.warnings=function(e){return ys(e)},gn.version="3.31.0",gn.stylesheet=gn.Stylesheet=Ml;export{gn as c};

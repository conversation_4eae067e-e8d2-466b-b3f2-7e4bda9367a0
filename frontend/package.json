{"name": "datportal-frontend", "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:watch": "vitest --watch", "lint": "eslint src --ext .vue,.js"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.0", "pinia": "^2.1.0", "axios": "^1.6.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.0", "vite": "^5.0.0", "vitest": "^1.0.0", "@vitest/ui": "^1.0.0", "@vue/test-utils": "^2.4.0", "jsdom": "^23.0.0", "@testing-library/vue": "^8.0.0", "@testing-library/jest-dom": "^6.0.0", "@testing-library/user-event": "^14.0.0", "tailwindcss": "^3.4.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.0.0"}}
{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/testing_strategy.md"}, "modifiedCode": "# 🧪 **STRATEGIA DI TESTING COMPLETA**\n\n## 📋 **OVERVIEW**\n\nStrategia di testing completa per il progetto, allineata con il piano delle task in `/tasks/tasks.json` e coerente con l'architettura Vue.js + Flask.\n\n## 🎯 **OBIETTIVI DI TESTING**\n\n### **Copertura Funzionale:**\n- ✅ **API Backend** - Tutti gli endpoints con casi edge\n- ✅ **Componenti Vue.js** - Rendering, interazioni, stati\n- ✅ **Workflow Completi** - User journey end-to-end\n- ✅ **Integrazione Moduli** - Personnel, Projects, Dashboard\n\n### **Qualità del Codice:**\n- **Coverage Target:** 80% per backend, 70% per frontend\n- **Performance:** Response time < 200ms per API\n- **Security:** Autenticazione, autorizzazione, validazione\n- **Usability:** Accessibilità, responsive design\n\n## 🏗️ **ARCHITETTURA DI TESTING**\n\n### **Backend Testing (Python + pytest)**\n```\nbackend/tests/\n├── unit/                    # Test unitari\n│   ├── test_models.py      # Modelli database\n│   ├── test_utils.py       # Utility functions\n│   └── test_calculations.py # Logica business\n├── api/                     # Test API endpoints\n│   ├── test_auth.py        # Autenticazione\n│   ├── test_personnel.py   # Personnel APIs\n│   ├── test_projects.py    # Projects APIs\n│   └── test_dashboard.py   # Dashboard APIs\n├── integration/             # Test integrazione\n│   ├── test_workflows.py   # User journeys\n│   ├── test_rbac.py        # Permessi\n│   └── test_security.py    # Sicurezza\n└── conftest.py             # Fixtures comuni\n```\n\n### **Frontend Testing (Vue.js + Vitest)**\n```\nfrontend/src/test/\n├── unit/                    # Test unitari\n│   ├── components/         # Componenti Vue\n│   ├── stores/             # Pinia stores\n│   └── utils/              # Utility functions\n├── integration/             # Test integrazione\n│   ├── views/              # Pagine complete\n│   ├── workflows/          # User flows\n│   └── api/                # API integration\n├── e2e/                     # Test end-to-end\n│   ├── personnel/          # Modulo personnel\n│   ├── projects/           # Modulo projects\n│   └── dashboard/          # Dashboard\n└── setup.js               # Configurazione test\n```\n\n## 📊 **COVERAGE TARGETS PER MODULO**\n\n### **Task 7: HR Management (Personnel)**\n- **Backend API:** 90% coverage\n- **Frontend Components:** 85% coverage\n- **Integration Tests:** Workflow completi\n- **Priority:** HIGH (modulo critico)\n\n### **Task 8: Project Management**\n- **Backend API:** 85% coverage\n- **Frontend Components:** 80% coverage\n- **Integration Tests:** CRUD + KPI\n- **Priority:** HIGH (core business)\n\n### **Task 9: Dashboard & Analytics**\n- **Backend API:** 75% coverage\n- **Frontend Components:** 70% coverage\n- **Integration Tests:** Data visualization\n- **Priority:** MEDIUM (reporting)\n\n### **Task 10: Advanced Features**\n- **Backend API:** 70% coverage\n- **Frontend Components:** 65% coverage\n- **Integration Tests:** Feature-specific\n- **Priority:** LOW (nice-to-have)\n\n## 🔧 **CONFIGURAZIONE TESTING**\n\n### **Backend (pytest)**\n```python\n# pytest.ini\n[tool:pytest]\ntestpaths = backend/tests\npython_files = test_*.py\npython_classes = Test*\npython_functions = test_*\naddopts = \n    --cov=backend\n    --cov-report=html\n    --cov-report=term-missing\n    --cov-fail-under=80\n    --strict-markers\n    --disable-warnings\nmarkers =\n    unit: Unit tests\n    integration: Integration tests\n    slow: Slow running tests\n    api: API endpoint tests\n```\n\n### **Frontend (Vitest)**\n```javascript\n// vitest.config.js\nexport default defineConfig({\n  test: {\n    environment: 'jsdom',\n    coverage: {\n      thresholds: {\n        global: {\n          branches: 70,\n          functions: 70,\n          lines: 70,\n          statements: 70\n        }\n      }\n    }\n  }\n})\n```\n\n## 🚀 **COMANDI DI TESTING**\n\n### **Backend Commands**\n```bash\n# Run all tests\ncd backend && python -m pytest\n\n# Run with coverage\ncd backend && python -m pytest --cov=. --cov-report=html\n\n# Run specific module\ncd backend && python -m pytest tests/api/test_personnel.py\n\n# Run integration tests only\ncd backend && python -m pytest -m integration\n\n# Run fast tests only\ncd backend && python -m pytest -m \"not slow\"\n```\n\n### **Frontend Commands**\n```bash\n# Run all tests\ncd frontend && npm run test\n\n# Run with coverage\ncd frontend && npm run test:coverage\n\n# Run in watch mode\ncd frontend && npm run test:watch\n\n# Run specific component\ncd frontend && npm run test PersonnelAdmin.test.js\n\n# Run with UI\ncd frontend && npm run test:ui\n```\n\n## 📋 **TEST CHECKLIST PER FEATURE**\n\n### **Nuova API Endpoint**\n- [ ] Test successful response\n- [ ] Test error cases (400, 401, 403, 404, 500)\n- [ ] Test input validation\n- [ ] Test authentication required\n- [ ] Test authorization (permissions)\n- [ ] Test edge cases (empty data, large data)\n- [ ] Test database constraints\n- [ ] Integration test with frontend\n\n### **Nuovo Componente Vue**\n- [ ] Test rendering with props\n- [ ] Test user interactions (click, input)\n- [ ] Test emitted events\n- [ ] Test computed properties\n- [ ] Test watchers\n- [ ] Test error states\n- [ ] Test loading states\n- [ ] Test responsive behavior\n\n### **Nuovo Workflow**\n- [ ] Test happy path end-to-end\n- [ ] Test error recovery\n- [ ] Test user permissions\n- [ ] Test data persistence\n- [ ] Test cross-module integration\n- [ ] Test performance under load\n\n## 🎯 **PRIORITÀ DI TESTING**\n\n### **Phase 1: Core Functionality (Settimana 1)**\n1. **Authentication & Authorization**\n   - Login/logout workflows\n   - Permission-based access\n   - Session management\n\n2. **Personnel Management**\n   - CRUD operations\n   - Bulk operations\n   - Data integrity\n\n3. **Critical API Endpoints**\n   - User management\n   - Department management\n   - Skills management\n\n### **Phase 2: Business Logic (Settimana 2)**\n1. **Project Management**\n   - Project CRUD\n   - Task management\n   - KPI calculations\n\n2. **Dashboard & Analytics**\n   - Data aggregation\n   - Chart rendering\n   - Real-time updates\n\n3. **Integration Testing**\n   - Cross-module workflows\n   - Data consistency\n   - Performance testing\n\n### **Phase 3: Advanced Features (Settimana 3)**\n1. **Advanced UI Components**\n   - Complex forms\n   - Data tables\n   - File uploads\n\n2. **Edge Cases & Error Handling**\n   - Network failures\n   - Invalid data\n   - Concurrent access\n\n3. **Performance & Security**\n   - Load testing\n   - Security scanning\n   - Accessibility testing\n\n## 📊 **METRICHE DI SUCCESSO**\n\n### **Quantitative Metrics**\n- **Code Coverage:** Backend ≥80%, Frontend ≥70%\n- **Test Execution Time:** <5 minuti per suite completa\n- **API Response Time:** <200ms per endpoint\n- **Bug Detection Rate:** ≥90% bugs trovati in testing\n\n### **Qualitative Metrics**\n- **Test Maintainability:** Test facili da aggiornare\n- **Test Reliability:** <5% flaky tests\n- **Developer Experience:** Setup test <5 minuti\n- **Documentation Quality:** Ogni test ben documentato\n\n## 🔄 **CONTINUOUS INTEGRATION**\n\n### **Pre-commit Hooks**\n```bash\n# Run linting and quick tests\nnpm run lint && npm run test:unit\npython -m pytest tests/unit/ -x\n```\n\n### **CI Pipeline**\n```yaml\n# .github/workflows/test.yml\nname: Test Suite\non: [push, pull_request]\njobs:\n  backend-tests:\n    runs-on: ubuntu-latest\n    steps:\n      - uses: actions/checkout@v2\n      - name: Setup Python\n        uses: actions/setup-python@v2\n      - name: Install dependencies\n        run: pip install -r requirements.txt\n      - name: Run tests\n        run: pytest --cov=backend --cov-report=xml\n      \n  frontend-tests:\n    runs-on: ubuntu-latest\n    steps:\n      - uses: actions/checkout@v2\n      - name: Setup Node\n        uses: actions/setup-node@v2\n      - name: Install dependencies\n        run: npm ci\n      - name: Run tests\n        run: npm run test:coverage\n```\n\n## 📚 **RISORSE E DOCUMENTAZIONE**\n\n### **Testing Libraries**\n- **Backend:** pytest, pytest-cov, factory-boy, freezegun\n- **Frontend:** Vitest, @vue/test-utils, @testing-library/vue\n- **E2E:** Playwright o Cypress (da valutare)\n\n### **Best Practices**\n- **AAA Pattern:** Arrange, Act, Assert\n- **Test Isolation:** Ogni test indipendente\n- **Meaningful Names:** Test names descrivono scenario\n- **Single Responsibility:** Un test, un concetto\n- **Fast Feedback:** Test rapidi per sviluppo\n\n### **Documentation**\n- Test README per ogni modulo\n- Esempi di test per nuovi sviluppatori\n- Troubleshooting guide per test failures\n- Performance benchmarks e targets\n"}
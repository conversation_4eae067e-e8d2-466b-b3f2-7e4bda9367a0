import{r as u,f,A as P,c as a,j as e,I,g as x,m as L,v as E,x as G,H as U,t as i,F as R,k as Q,l as q,b as J,o as l,a as K,i as O}from"./vendor.js";import{u as W}from"./personnel.js";import{a as X}from"./app.js";const Y={class:"department-list"},Z={class:"flex justify-between items-center mb-6"},ee={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-4 mb-6"},te={class:"flex flex-col sm:flex-row gap-4"},re={class:"flex-1"},se={class:"relative"},ae={class:"sm:w-48"},le={class:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6"},oe={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-4"},ie={class:"flex items-center"},ne={class:"ml-4"},de={class:"text-2xl font-semibold text-gray-900 dark:text-white"},ue={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-4"},ce={class:"flex items-center"},ge={class:"ml-4"},xe={class:"text-2xl font-semibold text-gray-900 dark:text-white"},ve={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-4"},me={class:"flex items-center"},pe={class:"ml-4"},he={class:"text-2xl font-semibold text-gray-900 dark:text-white"},fe={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-4"},ye={class:"flex items-center"},ke={class:"ml-4"},we={class:"text-2xl font-semibold text-gray-900 dark:text-white"},be={key:0,class:"flex justify-center items-center h-64"},_e={key:1,class:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6"},ze={class:"flex"},Ce={class:"text-sm text-red-700 dark:text-red-300 mt-1"},Me={key:2,class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},De={class:"overflow-x-auto"},Be={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},Le={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},Ee={class:"px-6 py-4 whitespace-nowrap"},He={class:"flex items-center"},Te={class:"ml-4"},Ve={class:"text-sm font-medium text-gray-900 dark:text-white"},je={key:0,class:"text-sm text-gray-500 dark:text-gray-400"},$e={class:"px-6 py-4 whitespace-nowrap"},Ne={key:0,class:"flex items-center"},Se={class:"ml-3"},Ae={class:"text-sm font-medium text-gray-900 dark:text-white"},Fe={class:"text-sm text-gray-500 dark:text-gray-400"},Pe={key:1,class:"text-sm text-gray-500 dark:text-gray-400"},Ie={class:"px-6 py-4 whitespace-nowrap"},Ge={class:"flex items-center"},Ue={class:"text-sm font-medium text-gray-900 dark:text-white"},Re={class:"px-6 py-4 whitespace-nowrap"},Qe={key:0,class:"text-sm text-gray-900 dark:text-white"},qe={key:1,class:"text-sm text-gray-500 dark:text-gray-400"},Je={class:"px-6 py-4 whitespace-nowrap"},Ke={key:0,class:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"},Oe={key:1,class:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"},We={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},Xe={class:"flex items-center justify-end space-x-2"},Ye=["onClick"],Ze=["onClick"],et={key:3,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-12 text-center"},tt={class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},rt={key:0,class:"mt-6"},it={__name:"DepartmentList",setup(st){q(),W();const{hasPermission:H}=X(),n=u([]),y=u(!1),v=u(null),c=u(""),d=u(""),k=u(!1),T=u(null),m=f(()=>H("manage_users")),C=f(()=>{let r=n.value;if(c.value.trim()){const t=c.value.toLowerCase();r=r.filter(o=>o.name.toLowerCase().includes(t)||o.description&&o.description.toLowerCase().includes(t)||o.manager&&o.manager.full_name.toLowerCase().includes(t))}return d.value&&(d.value==="root"?r=r.filter(t=>!t.parent_id):d.value==="sub"&&(r=r.filter(t=>t.parent_id))),r}),w=f(()=>c.value.trim()!==""||d.value!==""),p=f(()=>{const r=n.value.length,t=n.value.filter(g=>g.manager_id).length,o=n.value.reduce((g,_)=>g+(_.user_count||0),0),s=(g,_=null,h=0)=>{const D=g.filter(z=>z.parent_id===_);if(D.length===0)return h;const B=D.map(z=>s(g,z.id,h+1));return B.length>0?Math.max(h,...B):h},b=n.value.length>0?s(n.value)+1:0;return{totalDepartments:r,managersAssigned:t,totalEmployees:o,hierarchyLevels:b}}),V=r=>new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(r);let M=null;const j=()=>{clearTimeout(M),M=setTimeout(()=>{},300)},$=async()=>{y.value=!0,v.value=null;try{const r=await fetch("/api/personnel/departments",{credentials:"include"});if(!r.ok)throw new Error(`HTTP ${r.status}: ${r.statusText}`);const t=await r.json();if(t.success)n.value=t.data.departments||[];else throw new Error(t.message||"Errore nel caricamento dei dipartimenti")}catch(r){console.error("Error fetching departments:",r),v.value=r.message}finally{y.value=!1}},N=async r=>{if(confirm(`Sei sicuro di voler eliminare il dipartimento "${r.name}"?`))try{const t=await fetch(`/api/personnel/departments/${r.id}`,{method:"DELETE",credentials:"include"});if(!t.ok)throw new Error(`HTTP ${t.status}: ${t.statusText}`);const o=await t.json();if(o.success)n.value=n.value.filter(s=>s.id!==r.id),alert(o.message||"Dipartimento eliminato con successo");else throw new Error(o.message||"Errore nell'eliminazione del dipartimento")}catch(t){console.error("Error deleting department:",t),alert("Errore nell'eliminazione del dipartimento: "+t.message)}},S=r=>{T.value=r,k.value=!0},A=()=>{},F=()=>{c.value="",d.value=""};return P(()=>{$()}),(r,t)=>{const o=J("router-link");return l(),a("div",Y,[e("div",Z,[t[5]||(t[5]=I('<div><div class="flex items-center"><svg class="w-8 h-8 text-blue-600 dark:text-blue-400 mr-3" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z" clip-rule="evenodd"></path></svg><h1 class="text-2xl font-bold text-gray-900 dark:text-white">Gestione Dipartimenti</h1></div><p class="text-gray-600 dark:text-gray-400 mt-1">Gestisci la struttura organizzativa aziendale</p></div>',1)),m.value?(l(),a("button",{key:0,onClick:t[0]||(t[0]=s=>k.value=!0),class:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors duration-200"},t[4]||(t[4]=[e("svg",{class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z","clip-rule":"evenodd"})],-1),L(" Nuovo Dipartimento ")]))):x("",!0)]),e("div",ee,[e("div",te,[e("div",re,[e("div",se,[t[6]||(t[6]=e("svg",{class:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z","clip-rule":"evenodd"})],-1)),E(e("input",{"onUpdate:modelValue":t[1]||(t[1]=s=>c.value=s),onInput:j,type:"text",placeholder:"Cerca dipartimenti...",class:"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,544),[[G,c.value]])])]),e("div",ae,[E(e("select",{"onUpdate:modelValue":t[2]||(t[2]=s=>d.value=s),onChange:A,class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},t[7]||(t[7]=[e("option",{value:""},"Tutti i livelli",-1),e("option",{value:"root"},"Solo dipartimenti principali",-1),e("option",{value:"sub"},"Solo sotto-dipartimenti",-1)]),544),[[U,d.value]])]),w.value?(l(),a("button",{key:0,onClick:F,class:"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"}," Pulisci Filtri ")):x("",!0)])]),e("div",le,[e("div",oe,[e("div",ie,[t[9]||(t[9]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-blue-600 dark:text-blue-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z","clip-rule":"evenodd"})])])],-1)),e("div",ne,[t[8]||(t[8]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400"},"Totale Dipartimenti",-1)),e("p",de,i(p.value.totalDepartments),1)])])]),e("div",ue,[e("div",ce,[t[11]||(t[11]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-green-600 dark:text-green-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z","clip-rule":"evenodd"})])])],-1)),e("div",ge,[t[10]||(t[10]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400"},"Manager Assegnati",-1)),e("p",xe,i(p.value.managersAssigned),1)])])]),e("div",ve,[e("div",me,[t[13]||(t[13]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-purple-600 dark:text-purple-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z","clip-rule":"evenodd"})])])],-1)),e("div",pe,[t[12]||(t[12]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400"},"Totale Dipendenti",-1)),e("p",he,i(p.value.totalEmployees),1)])])]),e("div",fe,[e("div",ye,[t[15]||(t[15]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-yellow-100 dark:bg-yellow-900 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-yellow-600 dark:text-yellow-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z","clip-rule":"evenodd"})])])],-1)),e("div",ke,[t[14]||(t[14]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400"},"Livelli Gerarchia",-1)),e("p",we,i(p.value.hierarchyLevels),1)])])])]),y.value?(l(),a("div",be,t[16]||(t[16]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):v.value?(l(),a("div",_e,[e("div",ze,[t[18]||(t[18]=e("svg",{class:"w-5 h-5 text-red-400 mr-2 mt-0.5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z","clip-rule":"evenodd"})],-1)),e("div",null,[t[17]||(t[17]=e("h3",{class:"text-sm font-medium text-red-800 dark:text-red-200"},"Errore nel caricamento dei dipartimenti",-1)),e("p",Ce,i(v.value),1)])])])):C.value.length>0?(l(),a("div",Me,[e("div",De,[e("table",Be,[t[25]||(t[25]=e("thead",{class:"bg-gray-50 dark:bg-gray-700"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Dipartimento "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Manager "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Dipendenti "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Budget "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Gerarchia "),e("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Azioni ")])],-1)),e("tbody",Le,[(l(!0),a(R,null,Q(C.value,s=>(l(),a("tr",{key:s.id,class:"hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"},[e("td",Ee,[e("div",He,[t[19]||(t[19]=e("div",{class:"flex-shrink-0 w-8 h-8"},[e("div",{class:"w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-blue-600 dark:text-blue-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z","clip-rule":"evenodd"})])])],-1)),e("div",Te,[e("div",Ve,i(s.name),1),s.description?(l(),a("div",je,i(s.description),1)):x("",!0)])])]),e("td",$e,[s.manager?(l(),a("div",Ne,[t[20]||(t[20]=e("div",{class:"flex-shrink-0 w-8 h-8"},[e("div",{class:"w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-gray-600 dark:text-gray-300",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z","clip-rule":"evenodd"})])])],-1)),e("div",Se,[e("div",Ae,i(s.manager.full_name),1),e("div",Fe,i(s.manager.email),1)])])):(l(),a("span",Pe,"Nessun manager"))]),e("td",Ie,[e("div",Ge,[e("span",Ue,i(s.user_count),1),t[21]||(t[21]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400 ml-1"},"dipendenti",-1))])]),e("td",Re,[s.budget?(l(),a("span",Qe,i(V(s.budget)),1)):(l(),a("span",qe,"Non specificato"))]),e("td",Je,[s.parent_id?(l(),a("span",Ke," Sotto-dipartimento ")):(l(),a("span",Oe," Dipartimento principale "))]),e("td",We,[e("div",Xe,[K(o,{to:`/app/personnel/departments/${s.id}`,class:"text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 transition-colors duration-200"},{default:O(()=>t[22]||(t[22]=[e("svg",{class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M10 12a2 2 0 100-4 2 2 0 000 4z"}),e("path",{"fill-rule":"evenodd",d:"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z","clip-rule":"evenodd"})],-1)])),_:2,__:[22]},1032,["to"]),m.value?(l(),a("button",{key:0,onClick:b=>S(s),class:"text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 transition-colors duration-200"},t[23]||(t[23]=[e("svg",{class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"})],-1)]),8,Ye)):x("",!0),m.value&&s.user_count===0?(l(),a("button",{key:1,onClick:b=>N(s),class:"text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 transition-colors duration-200"},t[24]||(t[24]=[e("svg",{class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M9 2a1 1 0 000 2h2a1 1 0 100-2H9z","clip-rule":"evenodd"}),e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414L8.586 12l-2.293 2.293a1 1 0 101.414 1.414L10 13.414l2.293 2.293a1 1 0 001.414-1.414L11.414 12l2.293-2.293z","clip-rule":"evenodd"})],-1)]),8,Ze)):x("",!0)])])]))),128))])])])])):(l(),a("div",et,[t[27]||(t[27]=e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})],-1)),t[28]||(t[28]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun dipartimento trovato",-1)),e("p",tt,i(w.value?"Prova a modificare i filtri di ricerca.":"Inizia creando il primo dipartimento."),1),m.value&&!w.value?(l(),a("div",rt,[e("button",{onClick:t[3]||(t[3]=s=>k.value=!0),class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},t[26]||(t[26]=[e("svg",{class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z","clip-rule":"evenodd"})],-1),L(" Crea Primo Dipartimento ")]))])):x("",!0)]))])}}};export{it as default};

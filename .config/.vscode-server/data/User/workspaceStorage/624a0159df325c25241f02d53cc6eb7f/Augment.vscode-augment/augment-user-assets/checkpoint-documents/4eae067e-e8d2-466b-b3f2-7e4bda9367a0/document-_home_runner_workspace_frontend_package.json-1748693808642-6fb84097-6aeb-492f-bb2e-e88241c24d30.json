{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/package.json"}, "originalCode": "{\n  \"name\": \"datportal-frontend\",\n  \"version\": \"1.0.0\",\n  \"type\": \"module\",\n  \"scripts\": {\n    \"dev\": \"vite\",\n    \"build\": \"vite build\",\n    \"preview\": \"vite preview\",\n    \"test\": \"vitest\",\n    \"test:ui\": \"vitest --ui\",\n    \"test:coverage\": \"vitest --coverage\",\n    \"test:watch\": \"vitest --watch\",\n    \"lint\": \"eslint src --ext .vue,.js\"\n  },\n  \"dependencies\": {\n    \"vue\": \"^3.4.0\",\n    \"vue-router\": \"^4.2.0\",\n    \"pinia\": \"^2.1.0\",\n    \"axios\": \"^1.6.0\"\n  },\n  \"devDependencies\": {\n    \"@vitejs/plugin-vue\": \"^4.5.0\",\n    \"vite\": \"^5.0.0\",\n    \"vitest\": \"^1.0.0\",\n    \"tailwindcss\": \"^3.4.0\",\n    \"autoprefixer\": \"^10.4.0\",\n    \"postcss\": \"^8.4.0\",\n    \"eslint\": \"^8.57.0\",\n    \"eslint-plugin-vue\": \"^9.0.0\"\n  }\n}", "modifiedCode": "{\n  \"name\": \"datportal-frontend\",\n  \"version\": \"1.0.0\",\n  \"type\": \"module\",\n  \"scripts\": {\n    \"dev\": \"vite\",\n    \"build\": \"vite build\",\n    \"preview\": \"vite preview\",\n    \"test\": \"vitest\",\n    \"test:ui\": \"vitest --ui\",\n    \"test:coverage\": \"vitest --coverage\",\n    \"test:watch\": \"vitest --watch\",\n    \"lint\": \"eslint src --ext .vue,.js\"\n  },\n  \"dependencies\": {\n    \"vue\": \"^3.4.0\",\n    \"vue-router\": \"^4.2.0\",\n    \"pinia\": \"^2.1.0\",\n    \"axios\": \"^1.6.0\"\n  },\n  \"devDependencies\": {\n    \"@vitejs/plugin-vue\": \"^4.5.0\",\n    \"vite\": \"^5.0.0\",\n    \"vitest\": \"^1.0.0\",\n    \"@vitest/ui\": \"^1.0.0\",\n    \"@vue/test-utils\": \"^2.4.0\",\n    \"jsdom\": \"^23.0.0\",\n    \"@testing-library/vue\": \"^8.0.0\",\n    \"@testing-library/jest-dom\": \"^6.0.0\",\n    \"@testing-library/user-event\": \"^14.0.0\",\n    \"tailwindcss\": \"^3.4.0\",\n    \"autoprefixer\": \"^10.4.0\",\n    \"postcss\": \"^8.4.0\",\n    \"eslint\": \"^8.57.0\",\n    \"eslint-plugin-vue\": \"^9.0.0\"\n  }\n}"}
import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

// Layout components
import AppLayout from '@/components/layout/AppLayout.vue'
import PublicLayout from '@/components/layout/PublicLayout.vue'

// Public views
import Home from '@/views/public/Home.vue'
import About from '@/views/public/About.vue'
import Contact from '@/views/public/Contact.vue'
import Services from '@/views/public/Services.vue'

// Auth views
import Login from '@/views/auth/Login.vue'
import Register from '@/views/auth/Register.vue'

// Protected views
import Dashboard from '@/views/dashboard/Dashboard.vue'
import Projects from '@/views/projects/Projects.vue'

const routes = [
  // Public routes
  {
    path: '/',
    component: PublicLayout,
    children: [
      { path: '', name: 'home', component: Home },
      { path: 'about', name: 'about', component: About },
      { path: 'contact', name: 'contact', component: Contact },
      { path: 'services', name: 'services', component: Services }
    ]
  },

  // Auth routes
  {
    path: '/auth',
    component: PublicLayout,
    children: [
      { path: 'login', name: 'login', component: Login },
      { path: 'register', name: 'register', component: Register }
    ]
  },

  // Protected routes
  {
    path: '/app',
    component: AppLayout,
    meta: { requiresAuth: true },
    children: [
      { path: '', redirect: '/app/dashboard' },
      {
        path: 'dashboard',
        name: 'dashboard',
        component: Dashboard,
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_dashboard'
        }
      },
      {
        path: 'projects',
        name: 'projects',
        component: Projects,
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_all_projects'
        }
      },
      {
        path: 'projects/create',
        name: 'projects-create',
        component: () => import('@/views/projects/ProjectCreate.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'edit_project'
        }
      },
      {
        path: 'projects/:id',
        name: 'project-view',
        component: () => import('@/views/projects/ProjectView.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_all_projects'
        }
      },
      {
        path: 'projects/:id/edit',
        name: 'project-edit',
        component: () => import('@/views/projects/ProjectEdit.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'edit_project'
        }
      },
      // Personnel routes
      {
        path: 'personnel',
        name: 'personnel',
        component: () => import('@/views/personnel/PersonnelDirectory.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_personnel_data'
        }
      },
      {
        path: 'personnel/orgchart',
        name: 'personnel-orgchart',
        component: () => import('@/views/personnel/PersonnelOrgChart.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_personnel_data'
        }
      },
      {
        path: 'personnel/skills',
        name: 'personnel-skills',
        component: () => import('@/views/personnel/SkillsMatrix.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_personnel_data'
        }
      },
      {
        path: 'personnel/departments',
        name: 'personnel-departments',
        component: () => import('@/views/personnel/DepartmentList.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'manage_users'
        }
      },
      {
        path: 'personnel/departments/create',
        name: 'department-create',
        component: () => import('@/views/personnel/DepartmentCreate.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'manage_users'
        }
      },
      {
        path: 'personnel/departments/:id',
        name: 'department-view',
        component: () => import('@/views/personnel/DepartmentView.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_personnel_data'
        }
      },
      {
        path: 'personnel/departments/:id/edit',
        name: 'department-edit',
        component: () => import('@/views/personnel/DepartmentEdit.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'manage_users'
        }
      },
      {
        path: 'personnel/admin',
        name: 'personnel-admin',
        component: () => import('@/views/personnel/PersonnelAdmin.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'admin_access'
        }
      },
      {
        path: 'personnel/:id',
        name: 'personnel-profile',
        component: () => import('@/views/personnel/PersonnelProfile.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'view_personnel_data'
        }
      },
      {
        path: 'admin',
        redirect: '/app/admin/users'
      },
      {
        path: 'admin/users',
        name: 'admin-users',
        component: () => import('@/views/admin/Admin.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'admin_access'
        }
      },
      {
        path: 'admin/kpi-templates',
        name: 'admin-kpi-templates',
        component: () => import('@/views/admin/KPITemplates.vue'),
        meta: {
          requiresAuth: true,
          requiredPermission: 'admin_access'
        }
      },
      {
        path: 'profile',
        name: 'profile',
        component: () => import('@/views/user/Profile.vue'),
        meta: {
          requiresAuth: true
        }
      },
      {
        path: 'settings',
        name: 'settings',
        component: () => import('@/views/user/Settings.vue'),
        meta: {
          requiresAuth: true
        }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// Navigation guard per autenticazione e autorizzazioni
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  if (to.meta.requiresAuth) {
    // Controlla l'autenticazione se non è già stata verificata
    if (!authStore.sessionChecked) {
      await authStore.initializeAuth()
    }

    if (!authStore.isAuthenticated) {
      next('/auth/login')
      return
    }

    // Controllo permessi se specificato nella route
    if (to.meta.requiredPermission) {
      if (!authStore.hasPermission(to.meta.requiredPermission)) {
        // Redirect a pagina di accesso negato o dashboard
        console.warn(`Accesso negato a ${to.path}: permesso '${to.meta.requiredPermission}' richiesto`)
        next('/app/dashboard')
        return
      }
    }
  }

  next()
})

export default router
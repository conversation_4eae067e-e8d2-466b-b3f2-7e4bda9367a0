{"private": true, "dependencies": {"types-registry": "^0.1.722"}, "devDependencies": {"@types/argparse": "^2.0.17", "@types/balanced-match": "^3.0.2", "@types/boolbase": "^1.0.3", "@types/brace-expansion": "^1.1.2", "@types/braces": "^3.0.5", "@types/camelcase-css": "^2.0.2", "@types/caniuse-lite": "^1.0.5", "@types/chai": "^5.2.2", "@types/check-error": "^1.0.3", "@types/color-convert": "^2.0.4", "@types/color-name": "^2.0.0", "@types/combined-stream": "^1.0.6", "@types/concat-map": "^0.0.3", "@types/cross-spawn": "^6.0.6", "@types/cssesc": "^3.0.2", "@types/deep-eql": "^4.0.2", "@types/didyoumean": "^1.2.3", "@types/dlv": "^1.1.5", "@types/doctrine": "^0.0.9", "@types/electron-to-chromium": "^1.5.0", "@types/eslint-scope": "^8.3.0", "@types/espree": "^10.1.0", "@types/esquery": "^1.5.4", "@types/esrecurse": "^4.3.0", "@types/estraverse": "^5.1.7", "@types/esutils": "^2.0.2", "@types/fast-levenshtein": "^0.0.4", "@types/file-entry-cache": "^5.0.4", "@types/fill-range": "^7.0.3", "@types/flat-cache": "^2.0.2", "@types/follow-redirects": "^1.14.4", "@types/function-bind": "^1.1.10", "@types/get-func-name": "^2.0.4", "@types/get-intrinsic": "^1.2.3", "@types/glob-parent": "^5.1.3", "@types/gopd": "^1.0.3", "@types/imurmurhash": "^0.1.4", "@types/inflight": "^1.0.1", "@types/inherits": "^2.0.0", "@types/is-core-module": "^2.2.2", "@types/is-extglob": "^2.1.0", "@types/is-glob": "^4.0.4", "@types/is-number": "^7.0.5", "@types/isexe": "^2.0.4", "@types/js-yaml": "^4.0.9", "@types/jsdom": "^21.1.7", "@types/json-buffer": "^3.0.2", "@types/json-stable-stringify-without-jsonify": "^1.0.2", "@types/levn": "^0.4.0", "@types/lodash": "^4.17.17", "@types/lodash.merge": "^4.6.9", "@types/merge-stream": "^2.0.0", "@types/merge2": "^1.4.4", "@types/micromatch": "^4.0.9", "@types/mime-db": "^1.43.5", "@types/mime-types": "^3.0.0", "@types/mz": "^2.7.8", "@types/natural-compare": "^1.4.3", "@types/normalize-path": "^3.0.2", "@types/object-assign": "^4.0.33", "@types/object-hash": "^3.0.6", "@types/once": "^1.4.5", "@types/path-is-absolute": "^1.0.2", "@types/path-parse": "^1.0.22", "@types/pathval": "^1.1.2", "@types/picomatch": "^4.0.0", "@types/postcss-import": "^14.0.3", "@types/postcss-js": "^4.0.4", "@types/prelude-ls": "^1.1.34", "@types/proxy-from-env": "^1.0.4", "@types/punycode": "^2.1.4", "@types/react": "^19.1.6", "@types/react-is": "^19.0.0", "@types/resolve": "^1.20.6", "@types/run-parallel": "^1.1.2", "@types/semver": "^7.7.0", "@types/shebang-command": "^1.2.2", "@types/text-table": "^0.2.5", "@types/to-regex-range": "^5.0.3", "@types/type-check": "^0.3.31", "@types/type-detect": "^4.0.3", "@types/util-deprecate": "^1.0.4", "@types/which": "^3.0.4", "@types/xml-name-validator": "^4.0.3"}}
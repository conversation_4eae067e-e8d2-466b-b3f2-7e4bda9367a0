{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/utils/api.test.js"}, "modifiedCode": "import { describe, it, expect, beforeEach, vi } from 'vitest'\nimport { apiRequest, handleApiError } from './api'\n\ndescribe('API Utils', () => {\n  beforeEach(() => {\n    fetch.mockClear()\n  })\n\n  describe('apiRequest', () => {\n    it('makes successful GET request', async () => {\n      const mockData = { id: 1, name: 'Test' }\n      fetch.mockResolvedValueOnce({\n        ok: true,\n        status: 200,\n        json: async () => ({ success: true, data: mockData })\n      })\n\n      const result = await apiRequest('/api/test')\n      \n      expect(fetch).toHaveBeenCalledWith('/api/test', {\n        method: 'GET',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        credentials: 'include'\n      })\n      expect(result.data).toEqual(mockData)\n    })\n\n    it('makes successful POST request with data', async () => {\n      const postData = { name: 'New Item' }\n      const responseData = { id: 1, ...postData }\n      \n      fetch.mockResolvedValueOnce({\n        ok: true,\n        status: 201,\n        json: async () => ({ success: true, data: responseData })\n      })\n\n      const result = await apiRequest('/api/test', {\n        method: 'POST',\n        data: postData\n      })\n      \n      expect(fetch).toHaveBeenCalledWith('/api/test', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        credentials: 'include',\n        body: JSON.stringify(postData)\n      })\n      expect(result.data).toEqual(responseData)\n    })\n\n    it('handles API error responses', async () => {\n      fetch.mockResolvedValueOnce({\n        ok: false,\n        status: 400,\n        json: async () => ({ success: false, message: 'Bad Request' })\n      })\n\n      await expect(apiRequest('/api/test')).rejects.toThrow('Bad Request')\n    })\n\n    it('handles network errors', async () => {\n      fetch.mockRejectedValueOnce(new Error('Network Error'))\n\n      await expect(apiRequest('/api/test')).rejects.toThrow('Network Error')\n    })\n\n    it('includes custom headers', async () => {\n      fetch.mockResolvedValueOnce({\n        ok: true,\n        status: 200,\n        json: async () => ({ success: true, data: {} })\n      })\n\n      await apiRequest('/api/test', {\n        headers: {\n          'X-Custom-Header': 'custom-value'\n        }\n      })\n      \n      expect(fetch).toHaveBeenCalledWith('/api/test', {\n        method: 'GET',\n        headers: {\n          'Content-Type': 'application/json',\n          'X-Custom-Header': 'custom-value'\n        },\n        credentials: 'include'\n      })\n    })\n\n    it('handles FormData requests', async () => {\n      const formData = new FormData()\n      formData.append('file', new Blob(['test']))\n      \n      fetch.mockResolvedValueOnce({\n        ok: true,\n        status: 200,\n        json: async () => ({ success: true, data: {} })\n      })\n\n      await apiRequest('/api/upload', {\n        method: 'POST',\n        data: formData\n      })\n      \n      expect(fetch).toHaveBeenCalledWith('/api/upload', {\n        method: 'POST',\n        credentials: 'include',\n        body: formData\n      })\n    })\n\n    it('handles query parameters', async () => {\n      fetch.mockResolvedValueOnce({\n        ok: true,\n        status: 200,\n        json: async () => ({ success: true, data: {} })\n      })\n\n      await apiRequest('/api/test', {\n        params: {\n          page: 1,\n          limit: 10,\n          search: 'test query'\n        }\n      })\n      \n      expect(fetch).toHaveBeenCalledWith('/api/test?page=1&limit=10&search=test+query', {\n        method: 'GET',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        credentials: 'include'\n      })\n    })\n  })\n\n  describe('handleApiError', () => {\n    it('handles standard API error response', () => {\n      const error = {\n        response: {\n          data: {\n            message: 'Validation failed',\n            errors: ['Field is required']\n          }\n        }\n      }\n\n      const result = handleApiError(error)\n      expect(result.message).toBe('Validation failed')\n      expect(result.errors).toEqual(['Field is required'])\n    })\n\n    it('handles network error', () => {\n      const error = new Error('Network Error')\n      \n      const result = handleApiError(error)\n      expect(result.message).toBe('Network Error')\n      expect(result.isNetworkError).toBe(true)\n    })\n\n    it('handles unknown error format', () => {\n      const error = { someProperty: 'unknown' }\n      \n      const result = handleApiError(error)\n      expect(result.message).toBe('An unexpected error occurred')\n    })\n\n    it('handles HTTP status codes', () => {\n      const error = {\n        response: {\n          status: 401,\n          data: { message: 'Unauthorized' }\n        }\n      }\n\n      const result = handleApiError(error)\n      expect(result.status).toBe(401)\n      expect(result.message).toBe('Unauthorized')\n    })\n\n    it('provides default message for empty response', () => {\n      const error = {\n        response: {\n          status: 500,\n          data: {}\n        }\n      }\n\n      const result = handleApiError(error)\n      expect(result.message).toBe('Server error occurred')\n    })\n  })\n})\n"}
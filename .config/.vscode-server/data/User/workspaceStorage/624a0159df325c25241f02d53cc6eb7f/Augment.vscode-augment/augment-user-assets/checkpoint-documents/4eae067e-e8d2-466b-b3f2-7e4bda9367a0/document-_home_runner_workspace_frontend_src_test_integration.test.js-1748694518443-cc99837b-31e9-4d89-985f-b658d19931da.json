{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/test/integration.test.js"}, "modifiedCode": "import { describe, it, expect, beforeEach, vi } from 'vitest'\n\ndescribe('Task 2 & 7 Integration Tests', () => {\n  beforeEach(() => {\n    // Reset mocks\n    fetch.mockClear()\n    localStorage.clear()\n  })\n\n  describe('Projects Module (Task 2)', () => {\n    it('should have project management components', () => {\n      // Test that project components exist\n      expect(typeof import('../views/projects/ProjectView.vue')).toBe('object')\n      expect(typeof import('../views/projects/ProjectEdit.vue')).toBe('object')\n    })\n\n    it('should handle project API calls', async () => {\n      // Mock successful project API response\n      fetch.mockResolvedValueOnce({\n        ok: true,\n        status: 200,\n        json: async () => ({\n          success: true,\n          data: {\n            project: {\n              id: 1,\n              name: 'Test Project',\n              status: 'active',\n              budget: 10000,\n              total_hours: 100\n            }\n          }\n        })\n      })\n\n      // Simulate API call\n      const response = await fetch('/api/projects/1')\n      const data = await response.json()\n\n      expect(response.ok).toBe(true)\n      expect(data.success).toBe(true)\n      expect(data.data.project.name).toBe('Test Project')\n    })\n\n    it('should calculate KPI data correctly', () => {\n      const project = {\n        budget: 10000,\n        total_hours: 100,\n        invoiced_amount: 8000\n      }\n\n      // Simulate KPI calculation (from ProjectKPI.vue logic)\n      const totalCosts = project.total_hours * 50 // 50€/hour average\n      const marginPercentage = project.budget ? \n        (((project.budget - totalCosts) / project.budget) * 100) : 0\n\n      expect(totalCosts).toBe(5000)\n      expect(marginPercentage).toBe(50) // 50% margin\n    })\n\n    it('should format currency correctly', () => {\n      const formatCurrency = (amount) => {\n        return new Intl.NumberFormat('it-IT', {\n          style: 'currency',\n          currency: 'EUR'\n        }).format(amount || 0)\n      }\n\n      expect(formatCurrency(1000)).toBe('€ 1.000,00')\n      expect(formatCurrency(1234.56)).toBe('€ 1.234,56')\n    })\n\n    it('should format hours correctly', () => {\n      const formatHours = (hours) => {\n        if (!hours || hours === 0) return '0h'\n        return `${parseFloat(hours).toFixed(2)}h`\n      }\n\n      expect(formatHours(0)).toBe('0h')\n      expect(formatHours(8.5)).toBe('8.50h')\n      expect(formatHours(40)).toBe('40.00h')\n    })\n  })\n\n  describe('Personnel Module (Task 7)', () => {\n    it('should have personnel management components', () => {\n      // Test that personnel components exist\n      expect(typeof import('../views/personnel/PersonnelAdmin.vue')).toBe('object')\n      expect(typeof import('../views/personnel/PersonnelProfile.vue')).toBe('object')\n    })\n\n    it('should handle personnel API calls', async () => {\n      // Mock successful personnel API response\n      fetch.mockResolvedValueOnce({\n        ok: true,\n        status: 200,\n        json: async () => ({\n          success: true,\n          data: {\n            users: [\n              {\n                id: 1,\n                username: 'testuser',\n                email: '<EMAIL>',\n                first_name: 'Test',\n                last_name: 'User',\n                role: 'employee'\n              }\n            ],\n            total: 1\n          }\n        })\n      })\n\n      // Simulate API call\n      const response = await fetch('/api/personnel/users')\n      const data = await response.json()\n\n      expect(response.ok).toBe(true)\n      expect(data.success).toBe(true)\n      expect(data.data.users.length).toBe(1)\n      expect(data.data.users[0].email).toBe('<EMAIL>')\n    })\n\n    it('should calculate profile completion correctly', () => {\n      const calculateProfileCompletion = (userData) => {\n        const fields = ['first_name', 'last_name', 'email', 'phone', 'bio']\n        const completedFields = fields.filter(field => userData[field] && userData[field].trim() !== '')\n        return (completedFields.length / fields.length) * 100\n      }\n\n      const user1 = {\n        first_name: 'John',\n        last_name: 'Doe',\n        email: '<EMAIL>',\n        phone: '+1234567890',\n        bio: 'Software developer'\n      }\n\n      const user2 = {\n        first_name: 'Jane',\n        last_name: 'Smith',\n        email: '<EMAIL>'\n      }\n\n      expect(calculateProfileCompletion(user1)).toBe(100)\n      expect(calculateProfileCompletion(user2)).toBe(60)\n    })\n\n    it('should handle bulk operations', async () => {\n      // Mock CSV export\n      fetch.mockResolvedValueOnce({\n        ok: true,\n        status: 200,\n        headers: new Headers({\n          'Content-Type': 'text/csv',\n          'Content-Disposition': 'attachment; filename=\"personnel-export.csv\"'\n        }),\n        text: async () => 'email,first_name,last_name\\<EMAIL>,Test,User'\n      })\n\n      const response = await fetch('/api/personnel/export')\n      const csvData = await response.text()\n\n      expect(response.ok).toBe(true)\n      expect(response.headers.get('Content-Type')).toBe('text/csv')\n      expect(csvData).toContain('email,first_name,last_name')\n      expect(csvData).toContain('<EMAIL>,Test,User')\n    })\n  })\n\n  describe('Cross-Module Integration', () => {\n    it('should integrate personnel data with projects', async () => {\n      // Mock project with personnel data\n      fetch.mockResolvedValueOnce({\n        ok: true,\n        status: 200,\n        json: async () => ({\n          success: true,\n          data: {\n            project: {\n              id: 1,\n              name: 'Test Project',\n              team_members: [\n                {\n                  id: 1,\n                  full_name: 'John Doe',\n                  role: 'developer',\n                  hours_logged: 40\n                }\n              ]\n            }\n          }\n        })\n      })\n\n      const response = await fetch('/api/projects/1')\n      const data = await response.json()\n\n      expect(data.data.project.team_members).toBeDefined()\n      expect(data.data.project.team_members.length).toBe(1)\n      expect(data.data.project.team_members[0].full_name).toBe('John Doe')\n    })\n\n    it('should handle navigation between modules', () => {\n      // Test router navigation logic\n      const routes = [\n        '/app/projects',\n        '/app/projects/1',\n        '/app/projects/1/edit',\n        '/app/personnel',\n        '/app/personnel/admin'\n      ]\n\n      routes.forEach(route => {\n        expect(typeof route).toBe('string')\n        expect(route.startsWith('/app/')).toBe(true)\n      })\n    })\n\n    it('should maintain consistent data formats', () => {\n      // Test data format consistency between modules\n      const projectData = {\n        id: 1,\n        name: 'Test Project',\n        created_at: '2024-01-01T00:00:00Z',\n        updated_at: '2024-01-01T00:00:00Z'\n      }\n\n      const personnelData = {\n        id: 1,\n        username: 'testuser',\n        created_at: '2024-01-01T00:00:00Z',\n        updated_at: '2024-01-01T00:00:00Z'\n      }\n\n      // Both should have consistent timestamp formats\n      expect(projectData.created_at).toMatch(/^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}Z$/)\n      expect(personnelData.created_at).toMatch(/^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}Z$/)\n    })\n\n    it('should handle error states consistently', async () => {\n      // Mock API error\n      fetch.mockRejectedValueOnce(new Error('Network Error'))\n\n      try {\n        await fetch('/api/projects/999')\n      } catch (error) {\n        expect(error.message).toBe('Network Error')\n      }\n\n      // Mock 404 error\n      fetch.mockResolvedValueOnce({\n        ok: false,\n        status: 404,\n        json: async () => ({\n          success: false,\n          message: 'Not found'\n        })\n      })\n\n      const response = await fetch('/api/personnel/users/999')\n      const data = await response.json()\n\n      expect(response.ok).toBe(false)\n      expect(response.status).toBe(404)\n      expect(data.success).toBe(false)\n    })\n  })\n\n  describe('Performance and Optimization', () => {\n    it('should handle large datasets efficiently', () => {\n      // Test pagination logic\n      const mockLargeDataset = Array.from({ length: 1000 }, (_, i) => ({\n        id: i + 1,\n        name: `Item ${i + 1}`\n      }))\n\n      const pageSize = 20\n      const page = 1\n      const startIndex = (page - 1) * pageSize\n      const endIndex = startIndex + pageSize\n      const paginatedData = mockLargeDataset.slice(startIndex, endIndex)\n\n      expect(paginatedData.length).toBe(20)\n      expect(paginatedData[0].id).toBe(1)\n      expect(paginatedData[19].id).toBe(20)\n    })\n\n    it('should debounce search inputs', () => {\n      // Test debounce logic\n      let searchValue = ''\n      let debounceTimer = null\n\n      const debounceSearch = (value, delay = 300) => {\n        clearTimeout(debounceTimer)\n        debounceTimer = setTimeout(() => {\n          searchValue = value\n        }, delay)\n      }\n\n      debounceSearch('test')\n      expect(searchValue).toBe('') // Should not update immediately\n\n      // In real implementation, would test with actual timers\n      // For now, just verify the function exists\n      expect(typeof debounceSearch).toBe('function')\n    })\n  })\n})\n"}
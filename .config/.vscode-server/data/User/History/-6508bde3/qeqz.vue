<template>
  <div>
    <!-- Header -->
    <div class="mb-6">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <div class="flex items-center">
            <svg class="w-8 h-8 text-blue-600 dark:text-blue-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"></path>
            </svg>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Personale</h1>
          </div>
          <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Gest<PERSON>ci il team e le competenze aziendali
          </p>
        </div>
        <div class="mt-4 sm:mt-0 flex flex-wrap gap-3">
          <router-link
            to="/app/personnel/directory"
            class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"
          >
            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z" clip-rule="evenodd"></path>
            </svg>
            Directory
          </router-link>
          <router-link
            to="/app/personnel/orgchart"
            class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"
          >
            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 0l-2 2a1 1 0 101.414 1.414L8 10.414l1.293 1.293a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
            Organigramma
          </router-link>
          <router-link
            to="/app/personnel/skills"
            class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"
          >
            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
            </svg>
            Competenze
          </router-link>
          <router-link
            v-if="hasAdminAccess"
            to="/app/personnel/admin"
            class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700"
          >
            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"></path>
            </svg>
            Amministrazione
          </router-link>
        </div>
      </div>
    </div>

    <!-- Filtri -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg mb-6">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white">Filtri</h3>
      </div>
      <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <!-- Filtro Dipartimento -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Dipartimento
            </label>
            <select
              v-model="filters.department"
              @change="applyFilters"
              class="w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
            >
              <option value="">Tutti i dipartimenti</option>
              <option
                v-for="dept in departments"
                :key="dept.id"
                :value="dept.id"
              >
                {{ dept.name }}
              </option>
            </select>
          </div>

          <!-- Filtro Competenza -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Competenza
            </label>
            <select
              v-model="filters.skill"
              @change="applyFilters"
              class="w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
            >
              <option value="">Tutte le competenze</option>
              <option
                v-for="skill in skills"
                :key="skill.id"
                :value="skill.id"
              >
                {{ skill.name }} ({{ skill.category }})
              </option>
            </select>
          </div>

          <!-- Ricerca -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Ricerca
            </label>
            <input
              v-model="searchQuery"
              @input="debounceSearch"
              type="text"
              placeholder="Nome, email, posizione..."
              class="w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
            >
          </div>
        </div>

        <!-- Pulsante reset filtri -->
        <div v-if="hasActiveFilters" class="mt-4 flex justify-end">
          <button
            @click="clearFilters"
            class="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"
          >
            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" clip-rule="evenodd"></path>
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414L8.586 12l-2.293 2.293a1 1 0 101.414 1.414L10 13.414l2.293 2.293a1 1 0 001.414-1.414L11.414 12l2.293-2.293z" clip-rule="evenodd"></path>
            </svg>
            Pulisci filtri
          </button>
        </div>
      </div>
    </div>

    <!-- Lista Personale -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">
            Team ({{ pagination.total }} dipendenti{{ hasActiveFilters ? ' - filtrati' : '' }})
          </h3>

          <div v-if="hasActiveFilters" class="text-sm text-gray-500 dark:text-gray-400">
            <span
              v-if="searchQuery"
              class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 mr-2"
            >
              <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"></path>
              </svg>
              "{{ searchQuery }}"
            </span>
            <span
              v-if="selectedDepartment"
              class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 mr-2"
            >
              <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z" clip-rule="evenodd"></path>
              </svg>
              {{ selectedDepartment.name }}
            </span>
            <span
              v-if="selectedSkill"
              class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200"
            >
              <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
              </svg>
              {{ selectedSkill.name }}
            </span>
          </div>
        </div>
      </div>

      <!-- Loading State -->
      <div v-if="loading" class="p-6">
        <div class="flex items-center justify-center">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
          <span class="ml-2 text-gray-600 dark:text-gray-400">Caricamento...</span>
        </div>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="p-6">
        <div class="bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                Errore nel caricamento
              </h3>
              <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                {{ error }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Users Grid -->
      <div v-else-if="users.length > 0" class="overflow-hidden">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
          <div
            v-for="user in users"
            :key="user.id"
            class="bg-gray-50 dark:bg-gray-700 rounded-lg p-6 hover:shadow-md transition-shadow duration-200"
          >
            <!-- Avatar e Info Base -->
            <div class="flex items-center mb-4">
              <div class="flex-shrink-0">
                <img
                  v-if="user.profile_image"
                  :src="user.profile_image"
                  :alt="user.full_name"
                  class="h-12 w-12 rounded-full"
                >
                <div
                  v-else
                  class="h-12 w-12 rounded-full bg-primary-500 dark:bg-primary-600 flex items-center justify-center text-white text-lg font-medium"
                >
                  {{ getUserInitials(user) }}
                </div>
              </div>
              <div class="ml-4 flex-1">
                <h4 class="text-lg font-medium text-gray-900 dark:text-white">
                  {{ user.full_name }}
                </h4>
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  {{ user.position || 'Posizione non specificata' }}
                </p>
              </div>
            </div>

            <!-- Dettagli -->
            <div class="space-y-2 mb-4">
              <div class="flex items-center text-sm">
                <span class="text-gray-500 dark:text-gray-400 w-20">Email:</span>
                <span class="text-gray-900 dark:text-white">{{ user.email }}</span>
              </div>
              <div class="flex items-center text-sm">
                <span class="text-gray-500 dark:text-gray-400 w-20">Ruolo:</span>
                <span :class="getRoleClass(user.role)">
                  {{ getRoleLabel(user.role) }}
                </span>
              </div>
              <div v-if="user.department" class="flex items-center text-sm">
                <span class="text-gray-500 dark:text-gray-400 w-20">Dipart.:</span>
                <span class="text-gray-900 dark:text-white">{{ user.department.name }}</span>
              </div>
            </div>

            <!-- Competenze -->
            <div v-if="user.skills && user.skills.length > 0" class="mb-4">
              <p class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Competenze:</p>
              <div class="flex flex-wrap gap-1">
                <span
                  v-for="skill in user.skills.slice(0, 3)"
                  :key="skill.id"
                  class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200"
                >
                  {{ skill.name }}
                </span>
                <span
                  v-if="user.skills.length > 3"
                  class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-600 dark:bg-gray-600 dark:text-gray-300"
                >
                  +{{ user.skills.length - 3 }}
                </span>
              </div>
            </div>

            <!-- Azioni -->
            <div class="flex space-x-2">
              <router-link
                :to="`/app/personnel/${user.id}`"
                class="flex-1 text-center px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"
              >
                👤 Profilo
              </router-link>
              <a
                v-if="user.phone"
                :href="`tel:${user.phone}`"
                class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"
              >
                📞
              </a>
              <a
                :href="`mailto:${user.email}`"
                class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"
              >
                ✉️
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div v-else class="text-center py-12">
        <div class="text-gray-400 dark:text-gray-500 text-6xl mb-4">👥</div>
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Nessun dipendente trovato</h3>
        <p class="text-gray-500 dark:text-gray-400">
          {{ hasActiveFilters ? 'Prova a modificare i filtri di ricerca' : 'Non ci sono dipendenti da visualizzare' }}
        </p>
      </div>

      <!-- Paginazione -->
      <div v-if="pagination.pages > 1" class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <!-- Info risultati -->
          <div class="text-sm text-gray-700 dark:text-gray-300">
            Mostrando <span class="font-medium">{{ (pagination.page - 1) * pagination.per_page + 1 }}</span> -
            <span class="font-medium">{{ Math.min(pagination.page * pagination.per_page, pagination.total) }}</span>
            di <span class="font-medium">{{ pagination.total }}</span> dipendenti
          </div>

          <!-- Controlli paginazione -->
          <nav class="flex items-center space-x-1">
            <!-- Precedente -->
            <button
              v-if="pagination.page > 1"
              @click="changePage(pagination.page - 1)"
              class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              ← Precedente
            </button>
            <span
              v-else
              class="px-3 py-2 border border-gray-200 dark:border-gray-700 rounded-md text-sm font-medium text-gray-400 dark:text-gray-600 bg-gray-50 dark:bg-gray-900 cursor-not-allowed"
            >
              ← Precedente
            </span>

            <!-- Numeri pagina -->
            <template v-for="page in getPageNumbers()" :key="page">
              <button
                v-if="page !== '...'"
                @click="changePage(page)"
                :class="[
                  'px-3 py-2 border rounded-md text-sm font-medium transition-colors',
                  page === pagination.page
                    ? 'border-primary-500 dark:border-primary-400 text-white bg-primary-600 dark:bg-primary-500'
                    : 'border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700'
                ]"
              >
                {{ page }}
              </button>
              <span v-else class="px-2 py-2 text-sm text-gray-500 dark:text-gray-400">…</span>
            </template>

            <!-- Successiva -->
            <button
              v-if="pagination.page < pagination.pages"
              @click="changePage(pagination.page + 1)"
              class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              Successiva →
            </button>
            <span
              v-else
              class="px-3 py-2 border border-gray-200 dark:border-gray-700 rounded-md text-sm font-medium text-gray-400 dark:text-gray-600 bg-gray-50 dark:bg-gray-900 cursor-not-allowed"
            >
              Successiva →
            </span>
          </nav>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { usePersonnelStore } from '@/stores/personnel'
import { usePermissions } from '@/composables/usePermissions'

const route = useRoute()
const router = useRouter()
const personnelStore = usePersonnelStore()
const { hasPermission } = usePermissions()

// Reactive refs
const searchQuery = ref('')
const searchTimeout = ref(null)

// Store state
const {
  users,
  departments,
  skills,
  loading,
  error,
  filters,
  pagination
} = personnelStore

// Computed properties
const hasAdminAccess = computed(() => hasPermission.value('admin_access'))

const hasActiveFilters = computed(() => {
  return searchQuery.value ||
         filters.department ||
         filters.skill ||
         filters.role
})

const selectedDepartment = computed(() => {
  if (!filters.department) return null
  return departments.find(d => d.id === filters.department)
})

const selectedSkill = computed(() => {
  if (!filters.skill) return null
  return skills.find(s => s.id === filters.skill)
})

// Helper functions
const getUserInitials = (user) => {
  const first = user.first_name?.[0] || ''
  const last = user.last_name?.[0] || ''
  return (first + last).toUpperCase()
}

const getRoleLabel = (role) => {
  const labels = {
    'admin': 'Admin',
    'manager': 'Manager',
    'employee': 'Dipendente',
    'human_resources': 'HR',
    'project_manager': 'PM'
  }
  return labels[role] || role
}

const getRoleClass = (role) => {
  const classes = {
    'admin': 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    'manager': 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
    'human_resources': 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
    'project_manager': 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
  }
  return classes[role] || 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
}

const getPageNumbers = () => {
  const pages = []
  const current = pagination.page
  const total = pagination.pages

  // Always show first page
  if (total > 0) pages.push(1)

  // Show pages around current
  for (let i = Math.max(2, current - 1); i <= Math.min(total - 1, current + 1); i++) {
    if (!pages.includes(i)) pages.push(i)
  }

  // Show last page
  if (total > 1 && !pages.includes(total)) {
    if (pages[pages.length - 1] < total - 1) pages.push('...')
    pages.push(total)
  }

  return pages
}

// Methods
const debounceSearch = () => {
  clearTimeout(searchTimeout.value)
  searchTimeout.value = setTimeout(() => {
    applyFilters()
  }, 500)
}

const applyFilters = async () => {
  // Update store filters
  personnelStore.setFilter('search', searchQuery.value)

  // Reset to page 1 when applying filters
  personnelStore.setPagination(1)

  // Fetch with new filters
  await loadUsers()
}

const clearFilters = async () => {
  searchQuery.value = ''
  personnelStore.clearFilters()
  personnelStore.setPagination(1)
  await loadUsers()
}

const changePage = async (page) => {
  personnelStore.setPagination(page)
  await loadUsers()
}

const loadUsers = async () => {
  const params = {
    page: pagination.page,
    per_page: pagination.per_page
  }

  // Add filters
  if (searchQuery.value) params.search = searchQuery.value
  if (filters.department) params.department_id = filters.department
  if (filters.skill) params.skills = filters.skill
  if (filters.role) params.role = filters.role

  await personnelStore.fetchUsers(params)
}

const loadInitialData = async () => {
  // Load departments and skills for filters
  await Promise.all([
    personnelStore.fetchDepartments(),
    personnelStore.fetchSkills()
  ])

  // Parse URL parameters for initial filters
  const urlParams = route.query
  if (urlParams.search) searchQuery.value = urlParams.search
  if (urlParams.department) personnelStore.setFilter('department', parseInt(urlParams.department))
  if (urlParams.skill) personnelStore.setFilter('skill', parseInt(urlParams.skill))
  if (urlParams.page) personnelStore.setPagination(parseInt(urlParams.page))

  // Load users with initial filters
  await loadUsers()
}

// Watchers
watch(() => route.query, (newQuery) => {
  // Update filters when URL changes (for browser back/forward)
  if (newQuery.search !== searchQuery.value) {
    searchQuery.value = newQuery.search || ''
  }
}, { deep: true })

// Lifecycle
onMounted(() => {
  loadInitialData()
})
</script>
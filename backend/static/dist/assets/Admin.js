import{r as i,f as I,A as H,c as n,j as e,g as f,m as F,v as m,x as p,F as G,k as Q,t as o,s as V,H as J,I as K,C as O,o as d,n as U}from"./vendor.js";import{a as W,d as j}from"./app.js";const X={class:"flex flex-col md:flex-row md:items-center md:justify-between mb-8"},Y={class:"mt-4 md:mt-0"},Z={class:"mb-6"},ee={class:"relative rounded-md shadow-sm"},te={class:"bg-white dark:bg-gray-800 shadow overflow-hidden rounded-lg"},re={key:0,class:"p-6 text-center"},ae={key:1},se={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},oe={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},le={class:"px-6 py-4 whitespace-nowrap"},ie={class:"flex items-center"},ne={class:"flex-shrink-0 h-10 w-10"},de={class:"h-10 w-10 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center"},ue={class:"text-primary-600 dark:text-primary-300 font-medium text-lg"},me={class:"ml-4"},ge={class:"text-sm font-medium text-gray-900 dark:text-white"},ce={class:"text-sm text-gray-500 dark:text-gray-400"},pe={class:"px-6 py-4 whitespace-nowrap"},xe={class:"text-sm text-gray-900 dark:text-white"},ye={class:"px-6 py-4 whitespace-nowrap"},ve={class:"px-6 py-4 whitespace-nowrap"},fe={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},be=["onClick"],ke=["onClick"],we=["onClick"],he={key:0,class:"p-6 text-center"},_e={class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},Ue={key:0,class:"fixed inset-0 z-50 overflow-y-auto"},Ce={class:"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"},Me={class:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"},Ae={class:"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4"},ze={class:"text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4"},Ve={class:"space-y-4"},je={class:"grid grid-cols-2 gap-4"},Pe={key:0},Re={class:"flex items-center"},Ee={class:"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse"},Le=["disabled"],Ne={key:1,class:"fixed inset-0 z-50 overflow-y-auto"},Se={class:"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"},Te={class:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"},$e={class:"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4"},Be={class:"text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4"},De={key:2,class:"fixed top-4 right-4 z-50"},qe={class:"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded shadow-md"},Ie={key:3,class:"fixed top-4 right-4 z-50"},He={class:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded shadow-md"},Je={__name:"Admin",setup(Fe){const{isAdmin:P}=W(),g=i([]),w=i(!0),b=i(""),x=i(!1),h=i(!1),k=i(!1),y=i(!1),v=i(null),c=i(""),s=i({username:"",email:"",first_name:"",last_name:"",password:"",role:"employee",is_active:!0}),u=i(""),l=i(""),C=I(()=>{if(!b.value)return g.value;const r=b.value.toLowerCase();return g.value.filter(t=>t.email.toLowerCase().includes(r)||t.username.toLowerCase().includes(r)||t.role.toLowerCase().includes(r)||t.first_name&&t.first_name.toLowerCase().includes(r)||t.last_name&&t.last_name.toLowerCase().includes(r))}),R=async()=>{var r;try{w.value=!0;const t=await j.get("/api/admin/users");t.data&&t.data.data&&t.data.data.users?g.value=t.data.data.users:t.data&&t.data.users?g.value=t.data.users:l.value=((r=t.data)==null?void 0:r.message)||"Errore nel caricamento degli utenti"}catch(t){l.value="Errore nel caricamento degli utenti",console.error("Error loading users:",t)}finally{w.value=!1}},E=r=>r.first_name?r.first_name.charAt(0).toUpperCase():r.username?r.username.charAt(0).toUpperCase():r.email.charAt(0).toUpperCase(),L=r=>r.first_name&&r.last_name?`${r.first_name} ${r.last_name}`:r.username||r.email,N=r=>({admin:"Amministratore",manager:"Manager",employee:"Dipendente",sales:"Commerciale",human_resources:"Risorse Umane"})[r]||r,S=r=>({admin:"bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200",manager:"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200",employee:"bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200",sales:"bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200",human_resources:"bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200"})[r]||"bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200",T=r=>{v.value=r,s.value={...r},y.value=!0,h.value=!0},$=r=>{v.value=r,c.value="",k.value=!0},B=async r=>{r.is_active=!r.is_active,u.value=`Utente ${r.is_active?"attivato":"disattivato"} con successo`,setTimeout(()=>u.value="",3e3)},D=async()=>{x.value=!0;try{if(y.value){const r=g.value.findIndex(t=>t.id===s.value.id);r!==-1&&(g.value[r]={...s.value}),u.value="Utente aggiornato con successo"}else{const r={...s.value,id:Math.max(...g.value.map(t=>t.id))+1};g.value.push(r),u.value="Utente creato con successo"}_(),setTimeout(()=>u.value="",3e3)}catch{l.value="Errore nel salvataggio dell'utente",setTimeout(()=>l.value="",3e3)}finally{x.value=!1}},q=async()=>{try{if(x.value=!0,!c.value||c.value.length<6){l.value="La password deve essere di almeno 6 caratteri",setTimeout(()=>l.value="",3e3);return}const r=await j.post(`/api/admin/users/${v.value.id}/reset-password`,{password:c.value});r.data.success?(u.value=r.data.message,k.value=!1,c.value=""):l.value=r.data.message||"Errore nel reset della password",setTimeout(()=>{u.value="",l.value=""},3e3)}catch{l.value="Errore nel reset della password",setTimeout(()=>l.value="",3e3)}finally{x.value=!1}},_=()=>{h.value=!1,k.value=!1,y.value=!1,v.value=null,s.value={username:"",email:"",first_name:"",last_name:"",password:"",role:"employee",is_active:!0}},M=()=>{k.value=!1,v.value=null,c.value=""};return H(()=>{P.value&&R()}),(r,t)=>{var A;return d(),n("div",null,[e("div",X,[t[11]||(t[11]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Gestione Utenti"),e("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," Gestisci gli utenti, i loro ruoli e autorizzazioni all'interno della piattaforma. ")],-1)),e("div",Y,[e("button",{onClick:t[0]||(t[0]=a=>r.showAddModal=!0),class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},t[10]||(t[10]=[e("svg",{class:"h-4 w-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4v16m8-8H4"})],-1),F(" Aggiungi Utente ")]))])]),e("div",Z,[e("div",ee,[t[12]||(t[12]=e("div",{class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},[e("svg",{class:"h-5 w-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})])],-1)),m(e("input",{"onUpdate:modelValue":t[1]||(t[1]=a=>b.value=a),type:"text",class:"focus:ring-primary-500 focus:border-primary-500 block w-full pl-10 sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md",placeholder:"Cerca utenti per nome, email o ruolo..."},null,512),[[p,b.value]])])]),e("div",te,[w.value?(d(),n("div",re,t[13]||(t[13]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mx-auto"},null,-1),e("p",{class:"mt-2 text-gray-600 dark:text-gray-400"},"Caricamento utenti...",-1)]))):(d(),n("div",ae,[e("table",se,[t[14]||(t[14]=e("thead",{class:"bg-gray-50 dark:bg-gray-700"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Utente "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Email "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Ruolo "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Stato "),e("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Azioni ")])],-1)),e("tbody",oe,[(d(!0),n(G,null,Q(C.value,a=>(d(),n("tr",{key:a.id},[e("td",le,[e("div",ie,[e("div",ne,[e("div",de,[e("span",ue,o(E(a)),1)])]),e("div",me,[e("div",ge,o(L(a)),1),e("div",ce,o(a.username),1)])])]),e("td",pe,[e("div",xe,o(a.email),1)]),e("td",ye,[e("span",{class:U([S(a.role),"px-2 inline-flex text-xs leading-5 font-semibold rounded-full"])},o(N(a.role)),3)]),e("td",ve,[e("span",{class:U([a.is_active?"bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200":"bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200","px-2 inline-flex text-xs leading-5 font-semibold rounded-full"])},o(a.is_active?"Attivo":"Inattivo"),3)]),e("td",fe,[e("button",{onClick:z=>T(a),class:"text-primary-600 dark:text-primary-400 hover:text-primary-900 dark:hover:text-primary-300 mr-3"}," Modifica ",8,be),e("button",{onClick:z=>$(a),class:"text-yellow-600 dark:text-yellow-400 hover:text-yellow-900 dark:hover:text-yellow-300 mr-3"}," Reset Password ",8,ke),e("button",{onClick:z=>B(a),class:U(a.is_active?"text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300":"text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300")},o(a.is_active?"Disattiva":"Attiva"),11,we)])]))),128))])]),C.value.length===0?(d(),n("div",he,[t[15]||(t[15]=e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"})],-1)),t[16]||(t[16]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun utente trovato",-1)),e("p",_e,o(b.value?"Prova con criteri di ricerca diversi.":"Inizia creando il primo utente."),1)])):f("",!0)]))]),h.value?(d(),n("div",Ue,[e("div",Ce,[e("div",{class:"fixed inset-0 transition-opacity",onClick:_},t[17]||(t[17]=[e("div",{class:"absolute inset-0 bg-gray-500 opacity-75"},null,-1)])),e("div",Me,[e("form",{onSubmit:V(D,["prevent"])},[e("div",Ae,[e("h3",ze,o(y.value?"Modifica Utente":"Aggiungi Nuovo Utente"),1),e("div",Ve,[e("div",null,[t[18]||(t[18]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Username",-1)),m(e("input",{"onUpdate:modelValue":t[2]||(t[2]=a=>s.value.username=a),type:"text",required:"",class:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"},null,512),[[p,s.value.username]])]),e("div",null,[t[19]||(t[19]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Email",-1)),m(e("input",{"onUpdate:modelValue":t[3]||(t[3]=a=>s.value.email=a),type:"email",required:"",class:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"},null,512),[[p,s.value.email]])]),e("div",je,[e("div",null,[t[20]||(t[20]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Nome",-1)),m(e("input",{"onUpdate:modelValue":t[4]||(t[4]=a=>s.value.first_name=a),type:"text",class:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"},null,512),[[p,s.value.first_name]])]),e("div",null,[t[21]||(t[21]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Cognome",-1)),m(e("input",{"onUpdate:modelValue":t[5]||(t[5]=a=>s.value.last_name=a),type:"text",class:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"},null,512),[[p,s.value.last_name]])])]),y.value?f("",!0):(d(),n("div",Pe,[t[22]||(t[22]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Password",-1)),m(e("input",{"onUpdate:modelValue":t[6]||(t[6]=a=>s.value.password=a),type:"password",required:"",class:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"},null,512),[[p,s.value.password]])])),e("div",null,[t[24]||(t[24]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Ruolo",-1)),m(e("select",{"onUpdate:modelValue":t[7]||(t[7]=a=>s.value.role=a),class:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"},t[23]||(t[23]=[K('<option value="employee">Dipendente</option><option value="manager">Manager</option><option value="sales">Commerciale</option><option value="human_resources">Risorse Umane</option><option value="admin">Amministratore</option>',5)]),512),[[J,s.value.role]])]),e("div",Re,[m(e("input",{"onUpdate:modelValue":t[8]||(t[8]=a=>s.value.is_active=a),type:"checkbox",class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 rounded"},null,512),[[O,s.value.is_active]]),t[25]||(t[25]=e("label",{class:"ml-2 block text-sm text-gray-900 dark:text-white"}," Utente attivo ",-1))])])]),e("div",Ee,[e("button",{type:"submit",disabled:x.value,class:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50"},o(x.value?"Salvataggio...":y.value?"Aggiorna":"Crea"),9,Le),e("button",{type:"button",onClick:_,class:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"}," Annulla ")])],32)])])])):f("",!0),k.value?(d(),n("div",Ne,[e("div",Se,[e("div",{class:"fixed inset-0 transition-opacity",onClick:M},t[26]||(t[26]=[e("div",{class:"absolute inset-0 bg-gray-500 opacity-75"},null,-1)])),e("div",Te,[e("form",{onSubmit:V(q,["prevent"])},[e("div",$e,[e("h3",Be," Reset Password per "+o((A=v.value)==null?void 0:A.username),1),e("div",null,[t[27]||(t[27]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Nuova Password",-1)),m(e("input",{"onUpdate:modelValue":t[9]||(t[9]=a=>c.value=a),type:"password",required:"",class:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"},null,512),[[p,c.value]])])]),e("div",{class:"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse"},[t[28]||(t[28]=e("button",{type:"submit",class:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-yellow-600 text-base font-medium text-white hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 sm:ml-3 sm:w-auto sm:text-sm"}," Reset Password ",-1)),e("button",{type:"button",onClick:M,class:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"}," Annulla ")])],32)])])])):f("",!0),u.value?(d(),n("div",De,[e("div",qe,o(u.value),1)])):f("",!0),l.value?(d(),n("div",Ie,[e("div",He,o(l.value),1)])):f("",!0)])}}};export{Je as default};

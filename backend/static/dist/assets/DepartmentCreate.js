import{r as l,A as E,c as o,j as t,a as y,i as v,b as V,s as M,g as f,v as u,x as c,t as d,H as k,F as w,k as h,m as _,l as N,o as n}from"./vendor.js";const B={class:"min-h-screen bg-gray-50 dark:bg-gray-900 py-6"},H={class:"max-w-3xl mx-auto px-4 sm:px-6 lg:px-8"},S={class:"mb-8"},j={class:"flex items-center space-x-4"},L={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},P={key:0,class:"mt-1 text-sm text-red-600 dark:text-red-400"},U=["value"],$=["value"],A={key:0,class:"bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md p-4"},F={class:"flex"},O={class:"ml-3"},q={class:"mt-2 text-sm text-red-700 dark:text-red-300"},J={class:"flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700"},R=["disabled"],G={key:0,class:"flex items-center"},I={key:1},W={__name:"DepartmentCreate",setup(K){const z=N(),s=l({name:"",description:"",parent_id:"",manager_id:"",budget:null}),b=l([]),x=l([]),p=l(!1),g=l(null),m=l({}),C=async()=>{try{const r=await fetch("/api/personnel/departments",{credentials:"include"});if(!r.ok)throw new Error(`HTTP ${r.status}: ${r.statusText}`);const e=await r.json();e.success&&(b.value=e.data.departments||[])}catch(r){console.error("Error fetching departments:",r)}},T=async()=>{try{const r=await fetch("/api/personnel/users",{credentials:"include"});if(!r.ok)throw new Error(`HTTP ${r.status}: ${r.statusText}`);const e=await r.json();e.success&&(x.value=e.data.users||[])}catch(r){console.error("Error fetching users:",r)}},D=async()=>{p.value=!0,g.value=null,m.value={};try{const r={name:s.value.name,description:s.value.description,parent_id:s.value.parent_id||null,manager_id:s.value.manager_id||null,budget:s.value.budget||null},e=await fetch("/api/personnel/departments",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(r)});if(!e.ok)throw new Error(`HTTP ${e.status}: ${e.statusText}`);const i=await e.json();if(i.success)z.push("/app/personnel/departments");else throw new Error(i.message||"Errore nella creazione del dipartimento")}catch(r){console.error("Error creating department:",r),g.value=r.message}finally{p.value=!1}};return E(async()=>{await Promise.all([C(),T()])}),(r,e)=>{const i=V("router-link");return n(),o("div",B,[t("div",H,[t("div",S,[t("div",j,[y(i,{to:"/app/personnel/departments",class:"text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"},{default:v(()=>e[5]||(e[5]=[t("svg",{class:"w-6 h-6",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{"fill-rule":"evenodd",d:"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z","clip-rule":"evenodd"})],-1)])),_:1,__:[5]}),e[6]||(e[6]=t("div",null,[t("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Crea Nuovo Dipartimento"),t("p",{class:"text-gray-600 dark:text-gray-400 mt-1"},"Aggiungi un nuovo dipartimento all'organizzazione")],-1))])]),t("div",L,[t("form",{onSubmit:M(D,["prevent"]),class:"p-6 space-y-6"},[t("div",null,[e[7]||(e[7]=t("label",{for:"name",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Nome Dipartimento * ",-1)),u(t("input",{id:"name","onUpdate:modelValue":e[0]||(e[0]=a=>s.value.name=a),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Es. Sviluppo Software"},null,512),[[c,s.value.name]]),m.value.name?(n(),o("p",P,d(m.value.name),1)):f("",!0)]),t("div",null,[e[8]||(e[8]=t("label",{for:"description",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Descrizione ",-1)),u(t("textarea",{id:"description","onUpdate:modelValue":e[1]||(e[1]=a=>s.value.description=a),rows:"3",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"Descrizione del dipartimento e delle sue responsabilità"},null,512),[[c,s.value.description]])]),t("div",null,[e[10]||(e[10]=t("label",{for:"parent_id",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Dipartimento Padre ",-1)),u(t("select",{id:"parent_id","onUpdate:modelValue":e[2]||(e[2]=a=>s.value.parent_id=a),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},[e[9]||(e[9]=t("option",{value:""},"Nessun dipartimento padre (livello radice)",-1)),(n(!0),o(w,null,h(b.value,a=>(n(),o("option",{key:a.id,value:a.id},d(a.name),9,U))),128))],512),[[k,s.value.parent_id]])]),t("div",null,[e[12]||(e[12]=t("label",{for:"manager_id",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Manager ",-1)),u(t("select",{id:"manager_id","onUpdate:modelValue":e[3]||(e[3]=a=>s.value.manager_id=a),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},[e[11]||(e[11]=t("option",{value:""},"Nessun manager assegnato",-1)),(n(!0),o(w,null,h(x.value,a=>(n(),o("option",{key:a.id,value:a.id},d(a.first_name)+" "+d(a.last_name)+" ("+d(a.email)+") ",9,$))),128))],512),[[k,s.value.manager_id]])]),t("div",null,[e[13]||(e[13]=t("label",{for:"budget",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Budget Annuale (€) ",-1)),u(t("input",{id:"budget","onUpdate:modelValue":e[4]||(e[4]=a=>s.value.budget=a),type:"number",min:"0",step:"1000",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"0"},null,512),[[c,s.value.budget,void 0,{number:!0}]])]),g.value?(n(),o("div",A,[t("div",F,[e[15]||(e[15]=t("div",{class:"flex-shrink-0"},[t("svg",{class:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor"},[t("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z","clip-rule":"evenodd"})])],-1)),t("div",O,[e[14]||(e[14]=t("h3",{class:"text-sm font-medium text-red-800 dark:text-red-200"}," Errore nella creazione ",-1)),t("div",q,d(g.value),1)])])])):f("",!0),t("div",J,[y(i,{to:"/app/personnel/departments",class:"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"},{default:v(()=>e[16]||(e[16]=[_(" Annulla ")])),_:1,__:[16]}),t("button",{type:"submit",disabled:p.value,class:"px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-md text-sm font-medium transition-colors duration-200"},[p.value?(n(),o("span",G,e[17]||(e[17]=[t("svg",{class:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",fill:"none",viewBox:"0 0 24 24"},[t("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),t("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),_(" Creazione... ")]))):(n(),o("span",I,"Crea Dipartimento"))],8,R)])],32)])])])}}};export{W as default};

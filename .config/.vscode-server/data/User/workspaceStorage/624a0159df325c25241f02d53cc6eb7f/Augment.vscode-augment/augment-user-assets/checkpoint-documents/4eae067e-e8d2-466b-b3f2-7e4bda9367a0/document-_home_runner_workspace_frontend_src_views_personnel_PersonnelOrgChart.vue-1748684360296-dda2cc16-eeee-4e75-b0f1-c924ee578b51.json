{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/personnel/PersonnelOrgChart.vue"}, "originalCode": "<template>\n  <div class=\"space-y-6\">\n    <!-- Header -->\n    <div class=\"flex flex-col sm:flex-row sm:items-center sm:justify-between\">\n      <div>\n        <h1 class=\"text-2xl font-bold text-gray-900 dark:text-white flex items-center\">\n          <svg class=\"w-8 h-8 mr-3 text-blue-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fill-rule=\"evenodd\" d=\"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z\" clip-rule=\"evenodd\"></path>\n          </svg>\n          Organigramma Aziendale\n        </h1>\n        <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\n          Struttura organizzativa e gerarchia aziendale\n        </p>\n      </div>\n\n      <!-- Controls -->\n      <div class=\"mt-4 sm:mt-0 flex items-center space-x-3\">\n        <!-- Search -->\n        <div class=\"relative\">\n          <input v-model=\"searchQuery\"\n                 type=\"text\"\n                 placeholder=\"Cerca dipendente...\"\n                 class=\"w-64 pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\">\n          <svg class=\"absolute left-3 top-2.5 w-5 h-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"></path>\n          </svg>\n        </div>\n\n        <!-- View Toggle -->\n        <div class=\"flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1\">\n          <button @click=\"viewMode = 'tree'\"\n                  :class=\"[\n                    'px-3 py-1 rounded-md text-sm font-medium transition-colors',\n                    viewMode === 'tree'\n                      ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'\n                      : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'\n                  ]\">\n            <svg class=\"w-4 h-4 mr-1 inline\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fill-rule=\"evenodd\" d=\"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z\" clip-rule=\"evenodd\"></path>\n            </svg>\n            Albero\n          </button>\n          <button @click=\"viewMode = 'list'\"\n                  :class=\"[\n                    'px-3 py-1 rounded-md text-sm font-medium transition-colors',\n                    viewMode === 'list'\n                      ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'\n                      : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'\n                  ]\">\n            <svg class=\"w-4 h-4 mr-1 inline\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fill-rule=\"evenodd\" d=\"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z\" clip-rule=\"evenodd\"></path>\n            </svg>\n            Lista\n          </button>\n        </div>\n\n        <!-- Expand/Collapse All -->\n        <button @click=\"toggleAllNodes\"\n                class=\"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200\">\n          {{ allExpanded ? 'Comprimi Tutto' : 'Espandi Tutto' }}\n        </button>\n      </div>\n    </div>\n\n    <!-- Statistics Cards -->\n    <div v-if=\"stats\" class=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n      <div class=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <svg class=\"w-8 h-8 text-blue-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path d=\"M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z\"></path>\n            </svg>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Dipendenti Totali</p>\n            <p class=\"text-2xl font-bold text-gray-900 dark:text-white\">{{ stats.total_employees }}</p>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <svg class=\"w-8 h-8 text-green-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fill-rule=\"evenodd\" d=\"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z\" clip-rule=\"evenodd\"></path>\n            </svg>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Dipartimenti</p>\n            <p class=\"text-2xl font-bold text-gray-900 dark:text-white\">{{ stats.total_departments }}</p>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <svg class=\"w-8 h-8 text-purple-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fill-rule=\"evenodd\" d=\"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\" clip-rule=\"evenodd\"></path>\n            </svg>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Manager</p>\n            <p class=\"text-2xl font-bold text-gray-900 dark:text-white\">{{ stats.total_managers }}</p>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Loading State -->\n    <div v-if=\"loading\" class=\"flex justify-center items-center py-12\">\n      <div class=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n    </div>\n\n    <!-- Error State -->\n    <div v-else-if=\"error\" class=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4\">\n      <div class=\"flex\">\n        <svg class=\"w-5 h-5 text-red-400 mr-3 mt-0.5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n          <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clip-rule=\"evenodd\"></path>\n        </svg>\n        <div>\n          <h3 class=\"text-sm font-medium text-red-800 dark:text-red-200\">Errore nel caricamento</h3>\n          <p class=\"mt-1 text-sm text-red-700 dark:text-red-300\">{{ error }}</p>\n        </div>\n      </div>\n    </div>\n\n    <!-- Organization Chart Content -->\n    <div v-else-if=\"orgChart.length > 0\" class=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700\">\n      <!-- Tree View -->\n      <div v-if=\"viewMode === 'tree'\" class=\"p-6\">\n        <div class=\"orgchart-container overflow-x-auto\">\n          <div class=\"orgchart-tree min-w-max\">\n            <DepartmentNode\n              v-for=\"department in filteredOrgChart\"\n              :key=\"department.id\"\n              :department=\"department\"\n              :expanded=\"expandedNodes\"\n              :search-query=\"searchQuery\"\n              @toggle-node=\"toggleNode\"\n              @employee-click=\"onEmployeeClick\"\n            />\n          </div>\n        </div>\n      </div>\n\n      <!-- List View -->\n      <div v-else class=\"divide-y divide-gray-200 dark:divide-gray-700\">\n        <div v-for=\"department in filteredOrgChart\" :key=\"department.id\">\n          <DepartmentList\n            :department=\"department\"\n            :search-query=\"searchQuery\"\n            @employee-click=\"onEmployeeClick\"\n          />\n        </div>\n      </div>\n    </div>\n\n    <!-- Empty State -->\n    <div v-else-if=\"!loading\" class=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-12\">\n      <div class=\"text-center\">\n        <svg class=\"mx-auto h-16 w-16 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\"></path>\n        </svg>\n        <h3 class=\"mt-4 text-lg font-medium text-gray-900 dark:text-white\">Nessun dipartimento configurato</h3>\n        <p class=\"mt-2 text-sm text-gray-500 dark:text-gray-400\">\n          Inizia creando la struttura organizzativa aziendale\n        </p>\n        <div class=\"mt-6\">\n          <router-link\n            to=\"/app/personnel/departments\"\n            class=\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n          >\n            <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\"></path>\n            </svg>\n            Gestisci Dipartimenti\n          </router-link>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n", "modifiedCode": "<template>\n  <div class=\"space-y-6\">\n    <!-- Header -->\n    <div class=\"flex flex-col sm:flex-row sm:items-center sm:justify-between\">\n      <div>\n        <h1 class=\"text-2xl font-bold text-gray-900 dark:text-white flex items-center\">\n          <svg class=\"w-8 h-8 mr-3 text-blue-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fill-rule=\"evenodd\" d=\"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z\" clip-rule=\"evenodd\"></path>\n          </svg>\n          Organigramma Aziendale\n        </h1>\n        <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\n          Struttura organizzativa e gerarchia aziendale\n        </p>\n      </div>\n\n      <!-- Controls -->\n      <div class=\"mt-4 sm:mt-0 flex items-center space-x-3\">\n        <!-- Search -->\n        <div class=\"relative\">\n          <input v-model=\"searchQuery\"\n                 type=\"text\"\n                 placeholder=\"Cerca dipendente...\"\n                 class=\"w-64 pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\">\n          <svg class=\"absolute left-3 top-2.5 w-5 h-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"></path>\n          </svg>\n        </div>\n\n        <!-- View Toggle -->\n        <div class=\"flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1\">\n          <button @click=\"viewMode = 'tree'\"\n                  :class=\"[\n                    'px-3 py-1 rounded-md text-sm font-medium transition-colors',\n                    viewMode === 'tree'\n                      ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'\n                      : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'\n                  ]\">\n            <svg class=\"w-4 h-4 mr-1 inline\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fill-rule=\"evenodd\" d=\"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z\" clip-rule=\"evenodd\"></path>\n            </svg>\n            Albero\n          </button>\n          <button @click=\"viewMode = 'list'\"\n                  :class=\"[\n                    'px-3 py-1 rounded-md text-sm font-medium transition-colors',\n                    viewMode === 'list'\n                      ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'\n                      : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'\n                  ]\">\n            <svg class=\"w-4 h-4 mr-1 inline\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fill-rule=\"evenodd\" d=\"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z\" clip-rule=\"evenodd\"></path>\n            </svg>\n            Lista\n          </button>\n        </div>\n\n        <!-- Expand/Collapse All -->\n        <button @click=\"toggleAllNodes\"\n                class=\"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200\">\n          {{ allExpanded ? 'Comprimi Tutto' : 'Espandi Tutto' }}\n        </button>\n      </div>\n    </div>\n\n    <!-- Statistics Cards -->\n    <div v-if=\"stats\" class=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n      <div class=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <svg class=\"w-8 h-8 text-blue-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path d=\"M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z\"></path>\n            </svg>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Dipendenti Totali</p>\n            <p class=\"text-2xl font-bold text-gray-900 dark:text-white\">{{ stats.total_employees }}</p>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <svg class=\"w-8 h-8 text-green-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fill-rule=\"evenodd\" d=\"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z\" clip-rule=\"evenodd\"></path>\n            </svg>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Dipartimenti</p>\n            <p class=\"text-2xl font-bold text-gray-900 dark:text-white\">{{ stats.total_departments }}</p>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <svg class=\"w-8 h-8 text-purple-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fill-rule=\"evenodd\" d=\"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\" clip-rule=\"evenodd\"></path>\n            </svg>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Manager</p>\n            <p class=\"text-2xl font-bold text-gray-900 dark:text-white\">{{ stats.total_managers }}</p>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Loading State -->\n    <div v-if=\"loading\" class=\"flex justify-center items-center py-12\">\n      <div class=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n    </div>\n\n    <!-- Error State -->\n    <div v-else-if=\"error\" class=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4\">\n      <div class=\"flex\">\n        <svg class=\"w-5 h-5 text-red-400 mr-3 mt-0.5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n          <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clip-rule=\"evenodd\"></path>\n        </svg>\n        <div>\n          <h3 class=\"text-sm font-medium text-red-800 dark:text-red-200\">Errore nel caricamento</h3>\n          <p class=\"mt-1 text-sm text-red-700 dark:text-red-300\">{{ error }}</p>\n        </div>\n      </div>\n    </div>\n\n    <!-- Organization Chart Content -->\n    <div v-else-if=\"orgChart.length > 0\" class=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700\">\n      <!-- Tree View -->\n      <div v-if=\"viewMode === 'tree'\" class=\"p-6\">\n        <div class=\"orgchart-container overflow-x-auto\">\n          <div class=\"orgchart-tree min-w-max\">\n            <DepartmentNode\n              v-for=\"department in filteredOrgChart\"\n              :key=\"department.id\"\n              :department=\"department\"\n              :expanded=\"expandedNodes\"\n              :search-query=\"searchQuery\"\n              @toggle-node=\"toggleNode\"\n              @employee-click=\"onEmployeeClick\"\n            />\n          </div>\n        </div>\n      </div>\n\n      <!-- List View -->\n      <div v-else class=\"divide-y divide-gray-200 dark:divide-gray-700\">\n        <div v-for=\"department in filteredOrgChart\" :key=\"department.id\">\n          <DepartmentList\n            :department=\"department\"\n            :search-query=\"searchQuery\"\n            @employee-click=\"onEmployeeClick\"\n          />\n        </div>\n      </div>\n    </div>\n\n    <!-- Empty State -->\n    <div v-else-if=\"!loading\" class=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-12\">\n      <div class=\"text-center\">\n        <svg class=\"mx-auto h-16 w-16 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\"></path>\n        </svg>\n        <h3 class=\"mt-4 text-lg font-medium text-gray-900 dark:text-white\">Nessun dipartimento configurato</h3>\n        <p class=\"mt-2 text-sm text-gray-500 dark:text-gray-400\">\n          Inizia creando la struttura organizzativa aziendale\n        </p>\n        <div class=\"mt-6\">\n          <router-link\n            to=\"/app/personnel/departments\"\n            class=\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n          >\n            <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\"></path>\n            </svg>\n            Gestisci Dipartimenti\n          </router-link>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted } from 'vue'\nimport { useRouter } from 'vue-router'\nimport DepartmentNode from './components/DepartmentNode.vue'\nimport DepartmentList from './components/DepartmentList.vue'\n\n// Router\nconst router = useRouter()\n\n// Reactive state\nconst loading = ref(false)\nconst error = ref(null)\nconst orgChart = ref([])\nconst stats = ref(null)\nconst searchQuery = ref('')\nconst viewMode = ref('tree') // 'tree' or 'list'\nconst expandedNodes = ref(new Set())\nconst allExpanded = ref(false)\n\n// Computed properties\nconst filteredOrgChart = computed(() => {\n  if (!searchQuery.value.trim()) {\n    return orgChart.value\n  }\n\n  const query = searchQuery.value.toLowerCase()\n\n  // Filter departments and employees based on search\n  const filterDepartment = (dept) => {\n    const matchesDept = dept.name.toLowerCase().includes(query) ||\n                       (dept.description && dept.description.toLowerCase().includes(query))\n\n    const matchingEmployees = dept.employees.filter(emp =>\n      emp.full_name.toLowerCase().includes(query) ||\n      emp.email.toLowerCase().includes(query) ||\n      (emp.position && emp.position.toLowerCase().includes(query))\n    )\n\n    const filteredSubdepts = dept.subdepartments\n      .map(filterDepartment)\n      .filter(subdept => subdept !== null)\n\n    // Include department if it matches, has matching employees, or has matching subdepartments\n    if (matchesDept || matchingEmployees.length > 0 || filteredSubdepts.length > 0) {\n      return {\n        ...dept,\n        employees: matchingEmployees.length > 0 ? matchingEmployees : dept.employees,\n        subdepartments: filteredSubdepts\n      }\n    }\n\n    return null\n  }\n\n  return orgChart.value\n    .map(filterDepartment)\n    .filter(dept => dept !== null)\n})\n\n// Methods\nconst loadOrgChart = async () => {\n  loading.value = true\n  error.value = null\n\n  try {\n    const response = await fetch('/api/personnel/orgchart', {\n      credentials: 'include'\n    })\n\n    if (!response.ok) {\n      throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n\n    if (data.success) {\n      orgChart.value = data.data.orgchart || []\n      stats.value = data.data.stats || {}\n\n      // Auto-expand first level by default\n      if (orgChart.value.length > 0) {\n        orgChart.value.forEach(dept => {\n          expandedNodes.value.add(dept.id)\n        })\n      }\n    } else {\n      throw new Error(data.message || 'Errore nel caricamento dell\\'organigramma')\n    }\n  } catch (err) {\n    console.error('Error loading org chart:', err)\n    error.value = err.message\n  } finally {\n    loading.value = false\n  }\n}\n\nconst toggleNode = (nodeId) => {\n  if (expandedNodes.value.has(nodeId)) {\n    expandedNodes.value.delete(nodeId)\n  } else {\n    expandedNodes.value.add(nodeId)\n  }\n}\n\nconst toggleAllNodes = () => {\n  if (allExpanded.value) {\n    // Collapse all\n    expandedNodes.value.clear()\n    allExpanded.value = false\n  } else {\n    // Expand all\n    const expandAll = (departments) => {\n      departments.forEach(dept => {\n        expandedNodes.value.add(dept.id)\n        if (dept.subdepartments && dept.subdepartments.length > 0) {\n          expandAll(dept.subdepartments)\n        }\n      })\n    }\n    expandAll(orgChart.value)\n    allExpanded.value = true\n  }\n}\n\nconst onEmployeeClick = (employee) => {\n  // Navigate to employee profile\n  router.push(`/app/personnel/profile/${employee.id}`)\n}\n\n// Lifecycle\nonMounted(() => {\n  loadOrgChart()\n})\n</script>\n"}
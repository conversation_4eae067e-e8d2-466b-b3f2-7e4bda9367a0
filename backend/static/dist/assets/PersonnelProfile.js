import{r as y,f as $,A as J,u as W,w as X,c as a,g as n,j as e,t as l,m as h,z as K,v as x,x as p,F as m,k as v,H as I,I as j,l as Q,o as s,n as k}from"./vendor.js";import{u as Y}from"./personnel.js";import{a as Z}from"./app.js";const ee={class:"personnel-profile"},te={key:0,class:"flex justify-center items-center h-64"},re={key:1,class:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6"},ae={class:"flex"},se={class:"text-sm text-red-700 dark:text-red-300 mt-1"},oe={key:2,class:"space-y-6"},le={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},ie={class:"bg-gradient-to-r from-blue-500 to-purple-600 px-6 py-8"},ne={class:"flex items-center space-x-6"},de={class:"flex-shrink-0"},ue={class:"w-24 h-24 bg-white rounded-full flex items-center justify-center shadow-lg"},ge=["src","alt"],ce={key:1,class:"w-24 h-24 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center"},xe={class:"flex-1 text-white"},ye={class:"text-3xl font-bold"},pe={class:"text-blue-100 text-lg"},me={class:"flex items-center space-x-4 mt-2"},ve={key:0,class:"inline-flex items-center px-3 py-1 rounded-full text-sm bg-white/20 text-white"},fe={class:"inline-flex items-center px-3 py-1 rounded-full text-sm bg-white/20 text-white"},be={class:"flex-shrink-0"},he={key:0,class:"px-6 py-4 bg-gray-50 dark:bg-gray-700"},ke={class:"flex items-center justify-between mb-2"},we={class:"text-sm text-gray-500 dark:text-gray-400"},_e={class:"w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2"},ze={class:"grid grid-cols-1 lg:grid-cols-3 gap-6"},Ce={class:"lg:col-span-1 space-y-6"},Ve={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Me={class:"flex items-center justify-between mb-4"},je={key:0,class:"space-y-3"},Te={class:"flex items-center"},He={class:"text-gray-900 dark:text-white"},Be={key:0,class:"flex items-center"},Le={class:"text-gray-900 dark:text-white"},Pe={key:1,class:"flex items-center"},Se={class:"text-gray-900 dark:text-white"},De={key:1,class:"space-y-4"},Ee={class:"flex space-x-2"},Ue=["disabled"],Ae={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Ne={key:0,class:"space-y-3"},$e={class:"text-sm font-medium text-gray-900 dark:text-white"},Ie={class:"flex items-center space-x-2"},Fe={class:"flex space-x-1"},Re={key:0,class:"text-xs text-green-600 dark:text-green-400"},Oe={key:0,class:"text-sm text-gray-500 dark:text-gray-400 text-center pt-2"},qe={key:1,class:"text-gray-500 dark:text-gray-400 text-sm"},Ge={key:0,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Je={class:"flex items-center justify-between mb-4"},We={key:0,class:"space-y-3"},Xe={key:0,class:"flex justify-between"},Ke={class:"text-sm text-gray-900 dark:text-white"},Qe={key:1,class:"flex justify-between"},Ye={class:"text-sm text-gray-900 dark:text-white"},Ze={key:2,class:"flex justify-between"},et={class:"text-sm text-gray-900 dark:text-white"},tt={key:3,class:"flex justify-between"},rt={class:"text-sm text-gray-900 dark:text-white"},at={key:4,class:"flex justify-between"},st={class:"text-sm text-gray-900 dark:text-white"},ot={key:1,class:"space-y-4"},lt={class:"border-t border-gray-200 dark:border-gray-600 pt-4 mt-4"},it={class:"space-y-3"},nt={class:"border-t border-gray-200 dark:border-gray-600 pt-4 mt-4"},dt={key:1,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},ut={key:0,class:"text-gray-700 dark:text-gray-300 text-sm leading-relaxed"},gt={class:"lg:col-span-2"},ct={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},xt={class:"border-b border-gray-200 dark:border-gray-700"},yt={class:"-mb-px flex space-x-8 px-6","aria-label":"Tabs"},pt=["onClick"],mt={key:0,class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},vt={key:1,class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},ft={key:2,class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},bt={key:3,class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},ht={key:4,class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},kt={key:5,class:"ml-2 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 py-0.5 px-2 rounded-full text-xs"},wt={class:"p-6"},_t={key:0,class:"space-y-4"},zt={key:0},Ct={class:"flex items-center justify-between"},Vt={class:"font-medium text-gray-900 dark:text-white"},Mt={class:"text-sm text-gray-500 dark:text-gray-400"},jt={key:1,class:"text-center py-8"},Tt={key:1,class:"space-y-4"},Ht={key:0},Bt={class:"flex items-start justify-between"},Lt={class:"flex-1"},Pt={class:"font-medium text-gray-900 dark:text-white"},St={class:"text-sm text-gray-500 dark:text-gray-400 mt-1"},Dt={class:"flex items-center mt-2 space-x-4"},Et={key:0,class:"text-xs text-gray-500 dark:text-gray-400"},Ut={key:1,class:"text-center py-8"},At={key:2,class:"space-y-4"},Nt={key:0},$t={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},It={class:"flex items-center justify-between mb-2"},Ft={class:"font-medium text-gray-900 dark:text-white"},Rt={key:0,class:"text-green-600 dark:text-green-400 text-sm"},Ot={key:0,class:"text-sm text-gray-500 dark:text-gray-400 mb-2"},qt={class:"flex items-center justify-between"},Gt={class:"flex items-center space-x-2"},Jt={class:"flex space-x-1"},Wt={key:0,class:"text-sm text-gray-500 dark:text-gray-400"},Xt={key:1,class:"text-center py-8"},Kt={key:3,class:"space-y-4"},Qt={key:0},Yt={class:"overflow-x-auto"},Zt={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},er={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},tr={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},rr={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},ar={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"},sr={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},or={class:"px-6 py-4 text-sm text-gray-500 dark:text-gray-400"},lr={key:1,class:"text-center py-8"},ir={key:4,class:"space-y-6"},nr={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},dr={class:"flex items-center justify-between mb-4"},ur={key:0,class:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm transition-colors duration-200"},gr={key:0,class:"flex items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"},cr={class:"flex-1"},xr={class:"text-sm font-medium text-gray-900 dark:text-white"},yr={class:"text-xs text-gray-500 dark:text-gray-400"},pr={key:1,class:"text-center py-8"},mr={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},vr={class:"flex items-center justify-between mb-4"},fr={key:0,class:"bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm transition-colors duration-200"},_r={__name:"PersonnelProfile",setup(br){const B=W();Q(),Y();const{hasPermission:T}=Z(),o=y(null),w=y([]),z=y([]),C=y([]),H=y(!1),b=y(null),g=y(!1),V=y(!1),f=y("projects"),d=y({phone:"",bio:"",employee_id:"",job_title:"",employment_type:"",work_location:"",weekly_hours:40,address:"",emergency_contact_name:"",emergency_contact_phone:"",emergency_contact_relationship:""}),_=$(()=>{if(!o.value)return!1;try{return T.value&&typeof T.value=="function"?T.value("edit_personnel_data"):!1}catch(i){return console.warn("Permission check failed:",i),!1}}),F=$(()=>{var i,t;return[{id:"projects",name:"Progetti",count:w.value.length},{id:"tasks",name:"Task",count:z.value.length},{id:"skills",name:"Competenze",count:((t=(i=o.value)==null?void 0:i.skills)==null?void 0:t.length)||0},{id:"timesheet",name:"Timesheet",count:C.value.length},{id:"cv",name:"CV"}]}),M=i=>i?new Date(i).toLocaleDateString("it-IT",{year:"numeric",month:"long",day:"numeric"}):"",R=i=>({full_time:"Tempo Pieno",part_time:"Part Time",contractor:"Consulente",intern:"Stagista"})[i]||i,O=i=>({office:"Ufficio",remote:"Remoto",hybrid:"Ibrido"})[i]||i,L=()=>{var i,t,u,r,c,E,U,A,N;o.value&&(d.value={phone:o.value.phone||"",bio:o.value.bio||"",employee_id:((i=o.value.profile)==null?void 0:i.employee_id)||"",job_title:((t=o.value.profile)==null?void 0:t.job_title)||"",employment_type:((u=o.value.profile)==null?void 0:u.employment_type)||"",work_location:((r=o.value.profile)==null?void 0:r.work_location)||"",weekly_hours:((c=o.value.profile)==null?void 0:c.weekly_hours)||40,address:((E=o.value.profile)==null?void 0:E.address)||"",emergency_contact_name:((U=o.value.profile)==null?void 0:U.emergency_contact_name)||"",emergency_contact_phone:((A=o.value.profile)==null?void 0:A.emergency_contact_phone)||"",emergency_contact_relationship:((N=o.value.profile)==null?void 0:N.emergency_contact_relationship)||""})},q=()=>{g.value=!1,L()},G=async()=>{var i;if(o.value){V.value=!0;try{const u=await(await fetch(`/api/personnel/users/${o.value.id}`,{method:"PUT",headers:{"Content-Type":"application/json","X-CSRFToken":((i=document.querySelector('meta[name="csrf-token"]'))==null?void 0:i.getAttribute("content"))||""},body:JSON.stringify(d.value)})).json();if(u.success)o.value=u.data.user,g.value=!1,console.log("Profilo aggiornato con successo");else throw new Error(u.message||"Errore durante il salvataggio")}catch(t){console.error("Errore durante il salvataggio:",t),b.value=t.message}finally{V.value=!1}}},P=async i=>{H.value=!0,b.value=null;try{const t=await fetch(`/api/personnel/users/${i}`,{credentials:"include"});if(!t.ok)throw new Error(`HTTP ${t.status}: ${t.statusText}`);const u=await t.json();if(u.success)o.value=u.data.user,L();else throw new Error(u.message||"Errore nel caricamento del profilo")}catch(t){console.error("Error fetching user profile:",t),b.value=t.message}finally{H.value=!1}},S=async i=>{try{const t=await fetch(`/api/tasks?assignee_id=${i}&limit=20`,{credentials:"include"});if(!t.ok)throw new Error(`HTTP ${t.status}: ${t.statusText}`);const u=await t.json();u.success&&(z.value=u.data.tasks||[])}catch(t){console.error("Error fetching user tasks:",t)}},D=async i=>{try{const t=new Date,u=new Date;u.setDate(u.getDate()-30);const r=await fetch(`/api/timesheets?user_id=${i}&start_date=${u.toISOString().split("T")[0]}&end_date=${t.toISOString().split("T")[0]}&limit=50`,{credentials:"include"});if(!r.ok)throw new Error(`HTTP ${r.status}: ${r.statusText}`);const c=await r.json();c.success&&(C.value=c.data||[])}catch(t){console.error("Error fetching user timesheets:",t)}};return J(async()=>{const i=B.params.id;if(!i){b.value="ID utente non specificato";return}await P(i),o.value&&(w.value=o.value.projects||[],await S(i),await D(i))}),X(()=>B.params.id,async i=>{i&&(await P(i),o.value&&(w.value=o.value.projects||[],await S(i),await D(i)))}),(i,t)=>{var u;return s(),a("div",ee,[H.value?(s(),a("div",te,t[14]||(t[14]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):b.value?(s(),a("div",re,[e("div",ae,[t[16]||(t[16]=e("svg",{class:"w-5 h-5 text-red-400 mr-2 mt-0.5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z","clip-rule":"evenodd"})],-1)),e("div",null,[t[15]||(t[15]=e("h3",{class:"text-sm font-medium text-red-800 dark:text-red-200"},"Errore nel caricamento del profilo",-1)),e("p",se,l(b.value),1)])])])):o.value?(s(),a("div",oe,[e("div",le,[e("div",ie,[e("div",ne,[e("div",de,[e("div",ue,[o.value.profile_image?(s(),a("img",{key:0,src:o.value.profile_image,alt:o.value.full_name,class:"w-24 h-24 rounded-full object-cover"},null,8,ge)):(s(),a("div",ce,t[17]||(t[17]=[e("svg",{class:"w-12 h-12 text-gray-400 dark:text-gray-300",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z","clip-rule":"evenodd"})],-1)])))])]),e("div",xe,[e("h1",ye,l(o.value.full_name),1),e("p",pe,l(o.value.position||"Posizione non specificata"),1),e("div",me,[o.value.department?(s(),a("span",ve,[t[18]||(t[18]=e("svg",{class:"w-4 h-4 mr-1",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z","clip-rule":"evenodd"})],-1)),h(" "+l(o.value.department.name),1)])):n("",!0),e("span",fe,[t[19]||(t[19]=e("svg",{class:"w-4 h-4 mr-1",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z","clip-rule":"evenodd"})],-1)),h(" "+l(o.value.role),1)])])]),e("div",be,[_.value?(s(),a("button",{key:0,onClick:t[0]||(t[0]=r=>g.value=!g.value),class:"bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-colors duration-200"},[t[20]||(t[20]=e("svg",{class:"w-5 h-5 inline mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"})],-1)),h(" "+l(g.value?"Annulla":"Modifica"),1)])):n("",!0)])])]),o.value.profile&&o.value.profile.profile_completion!==void 0?(s(),a("div",he,[e("div",ke,[t[21]||(t[21]=e("span",{class:"text-sm font-medium text-gray-700 dark:text-gray-300"},"Completamento Profilo",-1)),e("span",we,l(o.value.profile.profile_completion)+"%",1)]),e("div",_e,[e("div",{class:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:K({width:o.value.profile.profile_completion+"%"})},null,4)])])):n("",!0)]),e("div",ze,[e("div",Ce,[e("div",Ve,[e("div",Me,[t[23]||(t[23]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Informazioni di Contatto",-1)),_.value&&!g.value?(s(),a("button",{key:0,onClick:t[1]||(t[1]=r=>g.value=!0),class:"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"},t[22]||(t[22]=[e("svg",{class:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"})],-1)]))):n("",!0)]),g.value?(s(),a("div",De,[e("div",null,[t[27]||(t[27]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Telefono",-1)),x(e("input",{"onUpdate:modelValue":t[2]||(t[2]=r=>d.value.phone=r),type:"tel",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[p,d.value.phone]])]),e("div",null,[t[28]||(t[28]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Bio",-1)),x(e("textarea",{"onUpdate:modelValue":t[3]||(t[3]=r=>d.value.bio=r),rows:"3",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[p,d.value.bio]])]),e("div",Ee,[e("button",{onClick:G,disabled:V.value,class:"flex-1 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-4 py-2 rounded-md transition-colors duration-200"},l(V.value?"Salvataggio...":"Salva"),9,Ue),e("button",{onClick:q,class:"flex-1 bg-gray-300 hover:bg-gray-400 dark:bg-gray-600 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-200 px-4 py-2 rounded-md transition-colors duration-200"}," Annulla ")])])):(s(),a("div",je,[e("div",Te,[t[24]||(t[24]=e("svg",{class:"w-5 h-5 text-gray-400 mr-3",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"}),e("path",{d:"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"})],-1)),e("span",He,l(o.value.email),1)]),o.value.phone?(s(),a("div",Be,[t[25]||(t[25]=e("svg",{class:"w-5 h-5 text-gray-400 mr-3",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"})],-1)),e("span",Le,l(o.value.phone),1)])):n("",!0),o.value.hire_date?(s(),a("div",Pe,[t[26]||(t[26]=e("svg",{class:"w-5 h-5 text-gray-400 mr-3",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z","clip-rule":"evenodd"})],-1)),e("span",Se,"Assunto il "+l(M(o.value.hire_date)),1)])):n("",!0)]))]),e("div",Ae,[t[29]||(t[29]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Competenze Principali",-1)),o.value.skills&&o.value.skills.length>0?(s(),a("div",Ne,[(s(!0),a(m,null,v(o.value.skills.slice(0,5),r=>(s(),a("div",{key:r.id,class:"flex items-center justify-between"},[e("span",$e,l(r.name),1),e("div",Ie,[e("div",Fe,[(s(),a(m,null,v(5,c=>e("div",{key:c,class:k(["w-2 h-2 rounded-full",c<=r.proficiency_level?"bg-blue-500":"bg-gray-300 dark:bg-gray-600"])},null,2)),64))]),r.certified?(s(),a("span",Re,"✓")):n("",!0)])]))),128)),o.value.skills.length>5?(s(),a("div",Oe," +"+l(o.value.skills.length-5)+" altre competenze ",1)):n("",!0)])):(s(),a("div",qe," Nessuna competenza registrata "))]),o.value.profile?(s(),a("div",Ge,[e("div",Je,[t[31]||(t[31]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Informazioni HR",-1)),_.value&&!g.value?(s(),a("button",{key:0,onClick:t[4]||(t[4]=r=>g.value=!0),class:"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"},t[30]||(t[30]=[e("svg",{class:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"})],-1)]))):n("",!0)]),g.value?(s(),a("div",ot,[e("div",null,[t[37]||(t[37]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"ID Dipendente",-1)),x(e("input",{"onUpdate:modelValue":t[5]||(t[5]=r=>d.value.employee_id=r),type:"text",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[p,d.value.employee_id]])]),e("div",null,[t[38]||(t[38]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Titolo Lavoro",-1)),x(e("input",{"onUpdate:modelValue":t[6]||(t[6]=r=>d.value.job_title=r),type:"text",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[p,d.value.job_title]])]),e("div",null,[t[40]||(t[40]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Tipo Contratto",-1)),x(e("select",{"onUpdate:modelValue":t[7]||(t[7]=r=>d.value.employment_type=r),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},t[39]||(t[39]=[j('<option value="">Seleziona tipo</option><option value="full_time">Tempo Pieno</option><option value="part_time">Part Time</option><option value="contractor">Consulente</option><option value="intern">Stagista</option>',5)]),512),[[I,d.value.employment_type]])]),e("div",null,[t[42]||(t[42]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Modalità Lavoro",-1)),x(e("select",{"onUpdate:modelValue":t[8]||(t[8]=r=>d.value.work_location=r),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},t[41]||(t[41]=[e("option",{value:""},"Seleziona modalità",-1),e("option",{value:"office"},"Ufficio",-1),e("option",{value:"remote"},"Remoto",-1),e("option",{value:"hybrid"},"Ibrido",-1)]),512),[[I,d.value.work_location]])]),e("div",null,[t[43]||(t[43]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ore Settimanali",-1)),x(e("input",{"onUpdate:modelValue":t[9]||(t[9]=r=>d.value.weekly_hours=r),type:"number",min:"1",max:"60",step:"0.5",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[p,d.value.weekly_hours]])]),e("div",lt,[t[47]||(t[47]=e("h4",{class:"text-sm font-medium text-gray-900 dark:text-white mb-3"},"Contatto di Emergenza",-1)),e("div",it,[e("div",null,[t[44]||(t[44]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Nome",-1)),x(e("input",{"onUpdate:modelValue":t[10]||(t[10]=r=>d.value.emergency_contact_name=r),type:"text",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[p,d.value.emergency_contact_name]])]),e("div",null,[t[45]||(t[45]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Telefono",-1)),x(e("input",{"onUpdate:modelValue":t[11]||(t[11]=r=>d.value.emergency_contact_phone=r),type:"tel",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[p,d.value.emergency_contact_phone]])]),e("div",null,[t[46]||(t[46]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Relazione",-1)),x(e("input",{"onUpdate:modelValue":t[12]||(t[12]=r=>d.value.emergency_contact_relationship=r),type:"text",placeholder:"es. Coniuge, Genitore, Fratello",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[p,d.value.emergency_contact_relationship]])])])]),e("div",nt,[t[49]||(t[49]=e("h4",{class:"text-sm font-medium text-gray-900 dark:text-white mb-3"},"Indirizzo",-1)),e("div",null,[t[48]||(t[48]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Indirizzo Completo",-1)),x(e("textarea",{"onUpdate:modelValue":t[13]||(t[13]=r=>d.value.address=r),rows:"2",placeholder:"Via, Città, CAP, Provincia",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[p,d.value.address]])])])])):(s(),a("div",We,[o.value.profile.employee_id?(s(),a("div",Xe,[t[32]||(t[32]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"ID Dipendente:",-1)),e("span",Ke,l(o.value.profile.employee_id),1)])):n("",!0),o.value.profile.job_title?(s(),a("div",Qe,[t[33]||(t[33]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Titolo:",-1)),e("span",Ye,l(o.value.profile.job_title),1)])):n("",!0),o.value.profile.employment_type?(s(),a("div",Ze,[t[34]||(t[34]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Tipo Contratto:",-1)),e("span",et,l(R(o.value.profile.employment_type)),1)])):n("",!0),o.value.profile.work_location?(s(),a("div",tt,[t[35]||(t[35]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Modalità Lavoro:",-1)),e("span",rt,l(O(o.value.profile.work_location)),1)])):n("",!0),o.value.profile.weekly_hours?(s(),a("div",at,[t[36]||(t[36]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Ore Settimanali:",-1)),e("span",st,l(o.value.profile.weekly_hours)+"h",1)])):n("",!0)]))])):n("",!0),o.value.bio||g.value?(s(),a("div",dt,[t[50]||(t[50]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Bio",-1)),g.value?n("",!0):(s(),a("p",ut,l(o.value.bio||"Nessuna bio disponibile"),1))])):n("",!0)]),e("div",gt,[e("div",ct,[e("div",xt,[e("nav",yt,[(s(!0),a(m,null,v(F.value,r=>(s(),a("button",{key:r.id,onClick:c=>f.value=r.id,class:k([f.value===r.id?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300","whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center"])},[r.id==="projects"?(s(),a("svg",mt,t[51]||(t[51]=[e("path",{"fill-rule":"evenodd",d:"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z","clip-rule":"evenodd"},null,-1)]))):r.id==="tasks"?(s(),a("svg",vt,t[52]||(t[52]=[e("path",{"fill-rule":"evenodd",d:"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z","clip-rule":"evenodd"},null,-1)]))):r.id==="skills"?(s(),a("svg",ft,t[53]||(t[53]=[e("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"},null,-1)]))):r.id==="timesheet"?(s(),a("svg",bt,t[54]||(t[54]=[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z","clip-rule":"evenodd"},null,-1)]))):r.id==="cv"?(s(),a("svg",ht,t[55]||(t[55]=[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z","clip-rule":"evenodd"},null,-1)]))):n("",!0),h(" "+l(r.name)+" ",1),r.count!==void 0?(s(),a("span",kt,l(r.count),1)):n("",!0)],10,pt))),128))])]),e("div",wt,[f.value==="projects"?(s(),a("div",_t,[w.value.length>0?(s(),a("div",zt,[(s(!0),a(m,null,v(w.value,r=>(s(),a("div",{key:r.id,class:"border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"},[e("div",Ct,[e("div",null,[e("h4",Vt,l(r.name),1),e("p",Mt,l(r.role||"Team Member"),1)]),e("span",{class:k(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",r.status==="active"?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200":r.status==="completed"?"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200":"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"])},l(r.status),3)])]))),128))])):(s(),a("div",jt,t[56]||(t[56]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun progetto",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"L'utente non è assegnato a nessun progetto.",-1)])))])):n("",!0),f.value==="tasks"?(s(),a("div",Tt,[z.value.length>0?(s(),a("div",Ht,[(s(!0),a(m,null,v(z.value,r=>(s(),a("div",{key:r.id,class:"border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"},[e("div",Bt,[e("div",Lt,[e("h4",Pt,l(r.name),1),e("p",St,l(r.project_name),1),e("div",Dt,[e("span",{class:k(["inline-flex items-center px-2 py-1 rounded-full text-xs font-medium",r.priority==="urgent"?"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200":r.priority==="high"?"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200":r.priority==="medium"?"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200":"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"])},l(r.priority),3),r.due_date?(s(),a("span",Et," Scadenza: "+l(M(r.due_date)),1)):n("",!0)])]),e("span",{class:k(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ml-4",r.status==="done"?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200":r.status==="in-progress"?"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200":r.status==="review"?"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200":"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"])},l(r.status),3)])]))),128))])):(s(),a("div",Ut,t[57]||(t[57]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun task",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"L'utente non ha task assegnati.",-1)])))])):n("",!0),f.value==="skills"?(s(),a("div",At,[o.value.skills&&o.value.skills.length>0?(s(),a("div",Nt,[e("div",$t,[(s(!0),a(m,null,v(o.value.skills,r=>(s(),a("div",{key:r.id,class:"border border-gray-200 dark:border-gray-700 rounded-lg p-4"},[e("div",It,[e("h4",Ft,l(r.name),1),r.certified?(s(),a("span",Rt,"✓ Certificato")):n("",!0)]),r.category?(s(),a("p",Ot,l(r.category),1)):n("",!0),e("div",qt,[e("div",Gt,[t[58]||(t[58]=e("span",{class:"text-sm text-gray-600 dark:text-gray-400"},"Livello:",-1)),e("div",Jt,[(s(),a(m,null,v(5,c=>e("div",{key:c,class:k(["w-3 h-3 rounded-full",c<=r.proficiency_level?"bg-blue-500":"bg-gray-300 dark:bg-gray-600"])},null,2)),64))])]),r.years_experience?(s(),a("span",Wt,l(r.years_experience)+" anni ",1)):n("",!0)])]))),128))])])):(s(),a("div",Xt,t[59]||(t[59]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessuna competenza",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"Non sono state registrate competenze per questo utente.",-1)])))])):n("",!0),f.value==="timesheet"?(s(),a("div",Kt,[C.value.length>0?(s(),a("div",Qt,[e("div",Yt,[e("table",Zt,[t[60]||(t[60]=e("thead",{class:"bg-gray-50 dark:bg-gray-700"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"},"Data"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"},"Progetto"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"},"Task"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"},"Ore"),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"},"Descrizione")])],-1)),e("tbody",er,[(s(!0),a(m,null,v(C.value,r=>(s(),a("tr",{key:r.id},[e("td",tr,l(M(r.date)),1),e("td",rr,l(r.project_name),1),e("td",ar,l(r.task_name||"-"),1),e("td",sr,l(r.hours)+"h ",1),e("td",or,l(r.description||"-"),1)]))),128))])])])])):(s(),a("div",lr,t[61]||(t[61]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun timesheet",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"Non sono stati registrati timesheet per questo utente.",-1)])))])):n("",!0),f.value==="cv"?(s(),a("div",ir,[e("div",nr,[e("div",dr,[t[63]||(t[63]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"CV Attuale",-1)),_.value?(s(),a("button",ur,t[62]||(t[62]=[e("svg",{class:"w-4 h-4 inline mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z","clip-rule":"evenodd"})],-1),h(" Carica CV ")]))):n("",!0)]),(u=o.value.profile)!=null&&u.current_cv_path?(s(),a("div",gr,[t[64]||(t[64]=e("svg",{class:"w-8 h-8 text-red-600 mr-3",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z","clip-rule":"evenodd"})],-1)),e("div",cr,[e("p",xr,"CV_"+l(o.value.full_name)+".pdf",1),e("p",yr," Caricato il "+l(M(o.value.profile.cv_last_updated)),1)]),t[65]||(t[65]=j('<div class="flex space-x-2"><button class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"><svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path><path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path></svg></button><button class="text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300"><svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg></button></div>',1))])):(s(),a("div",pr,t[66]||(t[66]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun CV caricato",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"Carica il tuo CV per completare il profilo.",-1)])))]),e("div",mr,[e("div",vr,[t[68]||(t[68]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Documenti",-1)),_.value?(s(),a("button",fr,t[67]||(t[67]=[e("svg",{class:"w-4 h-4 inline mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z","clip-rule":"evenodd"})],-1),h(" Carica Documento ")]))):n("",!0)]),t[69]||(t[69]=j('<div class="text-center py-8"><svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path></svg><h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">Nessun documento</h3><p class="mt-1 text-sm text-gray-500 dark:text-gray-400">I documenti caricati appariranno qui.</p></div>',1))]),t[70]||(t[70]=j('<div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4"><div class="flex"><svg class="w-5 h-5 text-blue-400 mt-0.5 mr-3" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path></svg><div><h3 class="text-sm font-medium text-blue-800 dark:text-blue-200">Suggerimenti per completare il profilo</h3><div class="mt-2 text-sm text-blue-700 dark:text-blue-300"><ul class="list-disc list-inside space-y-1"><li>Carica un CV aggiornato in formato PDF</li><li>Aggiungi certificazioni e documenti di formazione</li><li>Mantieni i documenti sempre aggiornati</li></ul></div></div></div></div>',1))])):n("",!0)])])])])])):n("",!0)])}}};export{_r as default};

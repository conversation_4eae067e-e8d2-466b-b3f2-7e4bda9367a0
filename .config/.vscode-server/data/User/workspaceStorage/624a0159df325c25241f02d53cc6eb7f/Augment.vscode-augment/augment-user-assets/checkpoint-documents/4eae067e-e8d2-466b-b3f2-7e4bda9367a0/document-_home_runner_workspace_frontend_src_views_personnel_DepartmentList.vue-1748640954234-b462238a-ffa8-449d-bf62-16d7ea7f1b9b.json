{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/personnel/DepartmentList.vue"}, "originalCode": "<template>\n  <div class=\"department-list\">\n    <!-- Header -->\n    <div class=\"flex justify-between items-center mb-6\">\n      <div>\n        <div class=\"flex items-center\">\n          <svg class=\"w-8 h-8 text-blue-600 dark:text-blue-400 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fill-rule=\"evenodd\" d=\"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z\" clip-rule=\"evenodd\"></path>\n          </svg>\n          <h1 class=\"text-2xl font-bold text-gray-900 dark:text-white\">Gestione Dipartimenti</h1>\n        </div>\n        <p class=\"text-gray-600 dark:text-gray-400 mt-1\">Gestisci la struttura organizzativa aziendale</p>\n      </div>\n      <button v-if=\"canManageDepartments\"\n              @click=\"showCreateModal = true\"\n              class=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors duration-200\">\n        <svg class=\"w-5 h-5 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n          <path fill-rule=\"evenodd\" d=\"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z\" clip-rule=\"evenodd\"></path>\n        </svg>\n        Nuovo Dipartimento\n      </button>\n    </div>\n\n    <!-- Filters and Search -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-4 mb-6\">\n      <div class=\"flex flex-col sm:flex-row gap-4\">\n        <!-- Search -->\n        <div class=\"flex-1\">\n          <div class=\"relative\">\n            <svg class=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fill-rule=\"evenodd\" d=\"M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z\" clip-rule=\"evenodd\"></path>\n            </svg>\n            <input v-model=\"searchQuery\"\n                   @input=\"debouncedSearch\"\n                   type=\"text\"\n                   placeholder=\"Cerca dipartimenti...\"\n                   class=\"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\">\n          </div>\n        </div>\n\n        <!-- Hierarchy Filter -->\n        <div class=\"sm:w-48\">\n          <select v-model=\"hierarchyFilter\"\n                  @change=\"applyFilters\"\n                  class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\">\n            <option value=\"\">Tutti i livelli</option>\n            <option value=\"root\">Solo dipartimenti principali</option>\n            <option value=\"sub\">Solo sotto-dipartimenti</option>\n          </select>\n        </div>\n\n        <!-- Clear Filters -->\n        <button v-if=\"hasActiveFilters\"\n                @click=\"clearFilters\"\n                class=\"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200\">\n          Pulisci Filtri\n        </button>\n      </div>\n    </div>\n\n    <!-- Statistics Cards -->\n    <div class=\"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6\">\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-4\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-blue-600 dark:text-blue-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fill-rule=\"evenodd\" d=\"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z\" clip-rule=\"evenodd\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-600 dark:text-gray-400\">Totale Dipartimenti</p>\n            <p class=\"text-2xl font-semibold text-gray-900 dark:text-white\">{{ stats.totalDepartments }}</p>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-4\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-green-600 dark:text-green-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fill-rule=\"evenodd\" d=\"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\" clip-rule=\"evenodd\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-600 dark:text-gray-400\">Manager Assegnati</p>\n            <p class=\"text-2xl font-semibold text-gray-900 dark:text-white\">{{ stats.managersAssigned }}</p>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-4\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-purple-600 dark:text-purple-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fill-rule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z\" clip-rule=\"evenodd\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-600 dark:text-gray-400\">Totale Dipendenti</p>\n            <p class=\"text-2xl font-semibold text-gray-900 dark:text-white\">{{ stats.totalEmployees }}</p>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-4\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-yellow-100 dark:bg-yellow-900 rounded-lg flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-yellow-600 dark:text-yellow-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fill-rule=\"evenodd\" d=\"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z\" clip-rule=\"evenodd\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-600 dark:text-gray-400\">Livelli Gerarchia</p>\n            <p class=\"text-2xl font-semibold text-gray-900 dark:text-white\">{{ stats.hierarchyLevels }}</p>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Loading State -->\n    <div v-if=\"loading\" class=\"flex justify-center items-center h-64\">\n      <div class=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n    </div>\n\n    <!-- Error State -->\n    <div v-else-if=\"error\" class=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6\">\n      <div class=\"flex\">\n        <svg class=\"w-5 h-5 text-red-400 mr-2 mt-0.5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n          <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clip-rule=\"evenodd\"></path>\n        </svg>\n        <div>\n          <h3 class=\"text-sm font-medium text-red-800 dark:text-red-200\">Errore nel caricamento dei dipartimenti</h3>\n          <p class=\"text-sm text-red-700 dark:text-red-300 mt-1\">{{ error }}</p>\n        </div>\n      </div>\n    </div>\n\n    <!-- Departments Table -->\n    <div v-else-if=\"filteredDepartments.length > 0\" class=\"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden\">\n      <div class=\"overflow-x-auto\">\n        <table class=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\n          <thead class=\"bg-gray-50 dark:bg-gray-700\">\n            <tr>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Dipartimento\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Manager\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Dipendenti\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Budget\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Gerarchia\n              </th>\n              <th class=\"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Azioni\n              </th>\n            </tr>\n          </thead>\n          <tbody class=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\n            <tr v-for=\"department in filteredDepartments\" :key=\"department.id\"\n                class=\"hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200\">\n              <!-- Department Name -->\n              <td class=\"px-6 py-4 whitespace-nowrap\">\n                <div class=\"flex items-center\">\n                  <div class=\"flex-shrink-0 w-8 h-8\">\n                    <div class=\"w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center\">\n                      <svg class=\"w-4 h-4 text-blue-600 dark:text-blue-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fill-rule=\"evenodd\" d=\"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z\" clip-rule=\"evenodd\"></path>\n                      </svg>\n                    </div>\n                  </div>\n                  <div class=\"ml-4\">\n                    <div class=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      {{ department.name }}\n                    </div>\n                    <div v-if=\"department.description\" class=\"text-sm text-gray-500 dark:text-gray-400\">\n                      {{ department.description }}\n                    </div>\n                  </div>\n                </div>\n              </td>\n\n              <!-- Manager -->\n              <td class=\"px-6 py-4 whitespace-nowrap\">\n                <div v-if=\"department.manager\" class=\"flex items-center\">\n                  <div class=\"flex-shrink-0 w-8 h-8\">\n                    <div class=\"w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center\">\n                      <svg class=\"w-4 h-4 text-gray-600 dark:text-gray-300\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fill-rule=\"evenodd\" d=\"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\" clip-rule=\"evenodd\"></path>\n                      </svg>\n                    </div>\n                  </div>\n                  <div class=\"ml-3\">\n                    <div class=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      {{ department.manager.full_name }}\n                    </div>\n                    <div class=\"text-sm text-gray-500 dark:text-gray-400\">\n                      {{ department.manager.email }}\n                    </div>\n                  </div>\n                </div>\n                <span v-else class=\"text-sm text-gray-500 dark:text-gray-400\">Nessun manager</span>\n              </td>\n\n              <!-- Employee Count -->\n              <td class=\"px-6 py-4 whitespace-nowrap\">\n                <div class=\"flex items-center\">\n                  <span class=\"text-sm font-medium text-gray-900 dark:text-white\">{{ department.user_count }}</span>\n                  <span class=\"text-sm text-gray-500 dark:text-gray-400 ml-1\">dipendenti</span>\n                </div>\n              </td>\n\n              <!-- Budget -->\n              <td class=\"px-6 py-4 whitespace-nowrap\">\n                <span v-if=\"department.budget\" class=\"text-sm text-gray-900 dark:text-white\">\n                  {{ formatCurrency(department.budget) }}\n                </span>\n                <span v-else class=\"text-sm text-gray-500 dark:text-gray-400\">Non specificato</span>\n              </td>\n\n              <!-- Hierarchy -->\n              <td class=\"px-6 py-4 whitespace-nowrap\">\n                <span v-if=\"department.parent_id\" class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200\">\n                  Sotto-dipartimento\n                </span>\n                <span v-else class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200\">\n                  Dipartimento principale\n                </span>\n              </td>\n\n              <!-- Actions -->\n              <td class=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                <div class=\"flex items-center justify-end space-x-2\">\n                  <!-- View Department -->\n                  <router-link :to=\"`/app/personnel/departments/${department.id}`\"\n                               class=\"text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 transition-colors duration-200\">\n                    <svg class=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path d=\"M10 12a2 2 0 100-4 2 2 0 000 4z\"></path>\n                      <path fill-rule=\"evenodd\" d=\"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z\" clip-rule=\"evenodd\"></path>\n                    </svg>\n                  </router-link>\n\n                  <!-- Edit Department -->\n                  <button v-if=\"canManageDepartments\"\n                          @click=\"editDepartment(department)\"\n                          class=\"text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 transition-colors duration-200\">\n                    <svg class=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path d=\"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z\"></path>\n                    </svg>\n                  </button>\n\n                  <!-- Delete Department -->\n                  <button v-if=\"canManageDepartments && department.user_count === 0\"\n                          @click=\"deleteDepartment(department)\"\n                          class=\"text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 transition-colors duration-200\">\n                    <svg class=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fill-rule=\"evenodd\" d=\"M9 2a1 1 0 000 2h2a1 1 0 100-2H9z\" clip-rule=\"evenodd\"></path>\n                      <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414L8.586 12l-2.293 2.293a1 1 0 101.414 1.414L10 13.414l2.293 2.293a1 1 0 001.414-1.414L11.414 12l2.293-2.293z\" clip-rule=\"evenodd\"></path>\n                    </svg>\n                  </button>\n                </div>\n              </td>\n            </tr>\n          </tbody>\n        </table>\n      </div>\n    </div>\n\n    <!-- Empty State -->\n    <div v-else class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-12 text-center\">\n      <svg class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\" />\n      </svg>\n      <h3 class=\"mt-2 text-sm font-medium text-gray-900 dark:text-white\">Nessun dipartimento trovato</h3>\n      <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\n        {{ hasActiveFilters ? 'Prova a modificare i filtri di ricerca.' : 'Inizia creando il primo dipartimento.' }}\n      </p>\n      <div v-if=\"canManageDepartments && !hasActiveFilters\" class=\"mt-6\">\n        <button @click=\"showCreateModal = true\"\n                class=\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\">\n          <svg class=\"w-5 h-5 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fill-rule=\"evenodd\" d=\"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z\" clip-rule=\"evenodd\"></path>\n          </svg>\n          Crea Primo Dipartimento\n        </button>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted, watch } from 'vue'\nimport { useRouter } from 'vue-router'\nimport { usePersonnelStore } from '@/stores/personnel'\nimport { usePermissions } from '@/composables/usePermissions'\n\n// Router\nconst router = useRouter()\n\n// Stores\nconst personnelStore = usePersonnelStore()\nconst { hasPermission } = usePermissions()\n\n// Reactive state\nconst departments = ref([])\nconst loading = ref(false)\nconst error = ref(null)\nconst searchQuery = ref('')\nconst hierarchyFilter = ref('')\nconst showCreateModal = ref(false)\nconst editingDepartment = ref(null)\n\n// Computed properties\nconst canManageDepartments = computed(() => hasPermission('manage_users'))\n\nconst filteredDepartments = computed(() => {\n  let filtered = departments.value\n\n  // Apply search filter\n  if (searchQuery.value.trim()) {\n    const search = searchQuery.value.toLowerCase()\n    filtered = filtered.filter(dept =>\n      dept.name.toLowerCase().includes(search) ||\n      (dept.description && dept.description.toLowerCase().includes(search)) ||\n      (dept.manager && dept.manager.full_name.toLowerCase().includes(search))\n    )\n  }\n\n  // Apply hierarchy filter\n  if (hierarchyFilter.value) {\n    if (hierarchyFilter.value === 'root') {\n      filtered = filtered.filter(dept => !dept.parent_id)\n    } else if (hierarchyFilter.value === 'sub') {\n      filtered = filtered.filter(dept => dept.parent_id)\n    }\n  }\n\n  return filtered\n})\n\nconst hasActiveFilters = computed(() => {\n  return searchQuery.value.trim() !== '' || hierarchyFilter.value !== ''\n})\n\nconst stats = computed(() => {\n  const totalDepartments = departments.value.length\n  const managersAssigned = departments.value.filter(dept => dept.manager_id).length\n  const totalEmployees = departments.value.reduce((sum, dept) => sum + (dept.user_count || 0), 0)\n\n  // Calculate hierarchy levels\n  const getMaxDepth = (depts, parentId = null, depth = 0) => {\n    const children = depts.filter(d => d.parent_id === parentId)\n    if (children.length === 0) return depth\n    const childDepths = children.map(child => getMaxDepth(depts, child.id, depth + 1))\n    return childDepths.length > 0 ? Math.max(depth, ...childDepths) : depth\n  }\n  const hierarchyLevels = departments.value.length > 0 ? getMaxDepth(departments.value) + 1 : 0\n\n  return {\n    totalDepartments,\n    managersAssigned,\n    totalEmployees,\n    hierarchyLevels\n  }\n})\n\n// Utility functions\nconst formatCurrency = (amount) => {\n  return new Intl.NumberFormat('it-IT', {\n    style: 'currency',\n    currency: 'EUR'\n  }).format(amount)\n}\n\nlet searchTimeout = null\nconst debouncedSearch = () => {\n  clearTimeout(searchTimeout)\n  searchTimeout = setTimeout(() => {\n    // Search is reactive, no need to do anything here\n  }, 300)\n}\n\n// API functions\nconst fetchDepartments = async () => {\n  loading.value = true\n  error.value = null\n\n  try {\n    const response = await fetch('/api/personnel/departments', {\n      credentials: 'include'\n    })\n\n    if (!response.ok) {\n      throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n\n    if (data.success) {\n      departments.value = data.data.departments || []\n    } else {\n      throw new Error(data.message || 'Errore nel caricamento dei dipartimenti')\n    }\n  } catch (err) {\n    console.error('Error fetching departments:', err)\n    error.value = err.message\n  } finally {\n    loading.value = false\n  }\n}\n\nconst deleteDepartment = async (department) => {\n  if (!confirm(`Sei sicuro di voler eliminare il dipartimento \"${department.name}\"?`)) {\n    return\n  }\n\n  try {\n    const response = await fetch(`/api/personnel/departments/${department.id}`, {\n      method: 'DELETE',\n      credentials: 'include'\n    })\n\n    if (!response.ok) {\n      throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n\n    if (data.success) {\n      // Remove department from local list\n      departments.value = departments.value.filter(d => d.id !== department.id)\n\n      // Show success message (you might want to use a toast notification here)\n      alert(data.message || 'Dipartimento eliminato con successo')\n    } else {\n      throw new Error(data.message || 'Errore nell\\'eliminazione del dipartimento')\n    }\n  } catch (err) {\n    console.error('Error deleting department:', err)\n    alert('Errore nell\\'eliminazione del dipartimento: ' + err.message)\n  }\n}\n\n// Event handlers\nconst editDepartment = (department) => {\n  editingDepartment.value = department\n  showCreateModal.value = true\n}\n\nconst applyFilters = () => {\n  // Filters are reactive, no need to do anything here\n}\n\nconst clearFilters = () => {\n  searchQuery.value = ''\n  hierarchyFilter.value = ''\n}\n\n// Lifecycle\nonMounted(() => {\n  fetchDepartments()\n})\n</script>", "modifiedCode": "<template>\n  <div class=\"department-list\">\n    <!-- Header -->\n    <div class=\"flex justify-between items-center mb-6\">\n      <div>\n        <div class=\"flex items-center\">\n          <svg class=\"w-8 h-8 text-blue-600 dark:text-blue-400 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fill-rule=\"evenodd\" d=\"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z\" clip-rule=\"evenodd\"></path>\n          </svg>\n          <h1 class=\"text-2xl font-bold text-gray-900 dark:text-white\">Gestione Dipartimenti</h1>\n        </div>\n        <p class=\"text-gray-600 dark:text-gray-400 mt-1\">Gestisci la struttura organizzativa aziendale</p>\n      </div>\n      <button v-if=\"canManageDepartments\"\n              @click=\"showCreateModal = true\"\n              class=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center transition-colors duration-200\">\n        <svg class=\"w-5 h-5 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n          <path fill-rule=\"evenodd\" d=\"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z\" clip-rule=\"evenodd\"></path>\n        </svg>\n        Nuovo Dipartimento\n      </button>\n    </div>\n\n    <!-- Filters and Search -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-4 mb-6\">\n      <div class=\"flex flex-col sm:flex-row gap-4\">\n        <!-- Search -->\n        <div class=\"flex-1\">\n          <div class=\"relative\">\n            <svg class=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fill-rule=\"evenodd\" d=\"M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z\" clip-rule=\"evenodd\"></path>\n            </svg>\n            <input v-model=\"searchQuery\"\n                   @input=\"debouncedSearch\"\n                   type=\"text\"\n                   placeholder=\"Cerca dipartimenti...\"\n                   class=\"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\">\n          </div>\n        </div>\n\n        <!-- Hierarchy Filter -->\n        <div class=\"sm:w-48\">\n          <select v-model=\"hierarchyFilter\"\n                  @change=\"applyFilters\"\n                  class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\">\n            <option value=\"\">Tutti i livelli</option>\n            <option value=\"root\">Solo dipartimenti principali</option>\n            <option value=\"sub\">Solo sotto-dipartimenti</option>\n          </select>\n        </div>\n\n        <!-- Clear Filters -->\n        <button v-if=\"hasActiveFilters\"\n                @click=\"clearFilters\"\n                class=\"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200\">\n          Pulisci Filtri\n        </button>\n      </div>\n    </div>\n\n    <!-- Statistics Cards -->\n    <div class=\"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6\">\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-4\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-blue-600 dark:text-blue-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fill-rule=\"evenodd\" d=\"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z\" clip-rule=\"evenodd\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-600 dark:text-gray-400\">Totale Dipartimenti</p>\n            <p class=\"text-2xl font-semibold text-gray-900 dark:text-white\">{{ stats.totalDepartments }}</p>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-4\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-green-600 dark:text-green-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fill-rule=\"evenodd\" d=\"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\" clip-rule=\"evenodd\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-600 dark:text-gray-400\">Manager Assegnati</p>\n            <p class=\"text-2xl font-semibold text-gray-900 dark:text-white\">{{ stats.managersAssigned }}</p>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-4\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-purple-600 dark:text-purple-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fill-rule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z\" clip-rule=\"evenodd\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-600 dark:text-gray-400\">Totale Dipendenti</p>\n            <p class=\"text-2xl font-semibold text-gray-900 dark:text-white\">{{ stats.totalEmployees }}</p>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-4\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-yellow-100 dark:bg-yellow-900 rounded-lg flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-yellow-600 dark:text-yellow-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fill-rule=\"evenodd\" d=\"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z\" clip-rule=\"evenodd\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-600 dark:text-gray-400\">Livelli Gerarchia</p>\n            <p class=\"text-2xl font-semibold text-gray-900 dark:text-white\">{{ stats.hierarchyLevels }}</p>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Loading State -->\n    <div v-if=\"loading\" class=\"flex justify-center items-center h-64\">\n      <div class=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n    </div>\n\n    <!-- Error State -->\n    <div v-else-if=\"error\" class=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6\">\n      <div class=\"flex\">\n        <svg class=\"w-5 h-5 text-red-400 mr-2 mt-0.5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n          <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clip-rule=\"evenodd\"></path>\n        </svg>\n        <div>\n          <h3 class=\"text-sm font-medium text-red-800 dark:text-red-200\">Errore nel caricamento dei dipartimenti</h3>\n          <p class=\"text-sm text-red-700 dark:text-red-300 mt-1\">{{ error }}</p>\n        </div>\n      </div>\n    </div>\n\n    <!-- Departments Table -->\n    <div v-else-if=\"filteredDepartments.length > 0\" class=\"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden\">\n      <div class=\"overflow-x-auto\">\n        <table class=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\n          <thead class=\"bg-gray-50 dark:bg-gray-700\">\n            <tr>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Dipartimento\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Manager\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Dipendenti\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Budget\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Gerarchia\n              </th>\n              <th class=\"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Azioni\n              </th>\n            </tr>\n          </thead>\n          <tbody class=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\n            <tr v-for=\"department in filteredDepartments\" :key=\"department.id\"\n                class=\"hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200\">\n              <!-- Department Name -->\n              <td class=\"px-6 py-4 whitespace-nowrap\">\n                <div class=\"flex items-center\">\n                  <div class=\"flex-shrink-0 w-8 h-8\">\n                    <div class=\"w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center\">\n                      <svg class=\"w-4 h-4 text-blue-600 dark:text-blue-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fill-rule=\"evenodd\" d=\"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z\" clip-rule=\"evenodd\"></path>\n                      </svg>\n                    </div>\n                  </div>\n                  <div class=\"ml-4\">\n                    <div class=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      {{ department.name }}\n                    </div>\n                    <div v-if=\"department.description\" class=\"text-sm text-gray-500 dark:text-gray-400\">\n                      {{ department.description }}\n                    </div>\n                  </div>\n                </div>\n              </td>\n\n              <!-- Manager -->\n              <td class=\"px-6 py-4 whitespace-nowrap\">\n                <div v-if=\"department.manager\" class=\"flex items-center\">\n                  <div class=\"flex-shrink-0 w-8 h-8\">\n                    <div class=\"w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center\">\n                      <svg class=\"w-4 h-4 text-gray-600 dark:text-gray-300\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fill-rule=\"evenodd\" d=\"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\" clip-rule=\"evenodd\"></path>\n                      </svg>\n                    </div>\n                  </div>\n                  <div class=\"ml-3\">\n                    <div class=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      {{ department.manager.full_name }}\n                    </div>\n                    <div class=\"text-sm text-gray-500 dark:text-gray-400\">\n                      {{ department.manager.email }}\n                    </div>\n                  </div>\n                </div>\n                <span v-else class=\"text-sm text-gray-500 dark:text-gray-400\">Nessun manager</span>\n              </td>\n\n              <!-- Employee Count -->\n              <td class=\"px-6 py-4 whitespace-nowrap\">\n                <div class=\"flex items-center\">\n                  <span class=\"text-sm font-medium text-gray-900 dark:text-white\">{{ department.user_count }}</span>\n                  <span class=\"text-sm text-gray-500 dark:text-gray-400 ml-1\">dipendenti</span>\n                </div>\n              </td>\n\n              <!-- Budget -->\n              <td class=\"px-6 py-4 whitespace-nowrap\">\n                <span v-if=\"department.budget\" class=\"text-sm text-gray-900 dark:text-white\">\n                  {{ formatCurrency(department.budget) }}\n                </span>\n                <span v-else class=\"text-sm text-gray-500 dark:text-gray-400\">Non specificato</span>\n              </td>\n\n              <!-- Hierarchy -->\n              <td class=\"px-6 py-4 whitespace-nowrap\">\n                <span v-if=\"department.parent_id\" class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200\">\n                  Sotto-dipartimento\n                </span>\n                <span v-else class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200\">\n                  Dipartimento principale\n                </span>\n              </td>\n\n              <!-- Actions -->\n              <td class=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                <div class=\"flex items-center justify-end space-x-2\">\n                  <!-- View Department -->\n                  <router-link :to=\"`/app/personnel/departments/${department.id}`\"\n                               class=\"text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 transition-colors duration-200\">\n                    <svg class=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path d=\"M10 12a2 2 0 100-4 2 2 0 000 4z\"></path>\n                      <path fill-rule=\"evenodd\" d=\"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z\" clip-rule=\"evenodd\"></path>\n                    </svg>\n                  </router-link>\n\n                  <!-- Edit Department -->\n                  <button v-if=\"canManageDepartments\"\n                          @click=\"editDepartment(department)\"\n                          class=\"text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 transition-colors duration-200\">\n                    <svg class=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path d=\"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z\"></path>\n                    </svg>\n                  </button>\n\n                  <!-- Delete Department -->\n                  <button v-if=\"canManageDepartments && department.user_count === 0\"\n                          @click=\"deleteDepartment(department)\"\n                          class=\"text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 transition-colors duration-200\">\n                    <svg class=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fill-rule=\"evenodd\" d=\"M9 2a1 1 0 000 2h2a1 1 0 100-2H9z\" clip-rule=\"evenodd\"></path>\n                      <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414L8.586 12l-2.293 2.293a1 1 0 101.414 1.414L10 13.414l2.293 2.293a1 1 0 001.414-1.414L11.414 12l2.293-2.293z\" clip-rule=\"evenodd\"></path>\n                    </svg>\n                  </button>\n                </div>\n              </td>\n            </tr>\n          </tbody>\n        </table>\n      </div>\n    </div>\n\n    <!-- Empty State -->\n    <div v-else class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-12 text-center\">\n      <svg class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\" />\n      </svg>\n      <h3 class=\"mt-2 text-sm font-medium text-gray-900 dark:text-white\">Nessun dipartimento trovato</h3>\n      <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\n        {{ hasActiveFilters ? 'Prova a modificare i filtri di ricerca.' : 'Inizia creando il primo dipartimento.' }}\n      </p>\n      <div v-if=\"canManageDepartments && !hasActiveFilters\" class=\"mt-6\">\n        <button @click=\"showCreateModal = true\"\n                class=\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\">\n          <svg class=\"w-5 h-5 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fill-rule=\"evenodd\" d=\"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z\" clip-rule=\"evenodd\"></path>\n          </svg>\n          Crea Primo Dipartimento\n        </button>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted, watch } from 'vue'\nimport { useRouter } from 'vue-router'\nimport { usePersonnelStore } from '@/stores/personnel'\nimport { usePermissions } from '@/composables/usePermissions'\n\n// Router\nconst router = useRouter()\n\n// Stores\nconst personnelStore = usePersonnelStore()\nconst { hasPermission } = usePermissions()\n\n// Reactive state\nconst departments = ref([])\nconst loading = ref(false)\nconst error = ref(null)\nconst searchQuery = ref('')\nconst hierarchyFilter = ref('')\nconst showCreateModal = ref(false)\nconst editingDepartment = ref(null)\n\n// Computed properties\nconst canManageDepartments = computed(() => hasPermission('manage_users'))\n\nconst filteredDepartments = computed(() => {\n  let filtered = departments.value\n\n  // Apply search filter\n  if (searchQuery.value.trim()) {\n    const search = searchQuery.value.toLowerCase()\n    filtered = filtered.filter(dept =>\n      dept.name.toLowerCase().includes(search) ||\n      (dept.description && dept.description.toLowerCase().includes(search)) ||\n      (dept.manager && dept.manager.full_name.toLowerCase().includes(search))\n    )\n  }\n\n  // Apply hierarchy filter\n  if (hierarchyFilter.value) {\n    if (hierarchyFilter.value === 'root') {\n      filtered = filtered.filter(dept => !dept.parent_id)\n    } else if (hierarchyFilter.value === 'sub') {\n      filtered = filtered.filter(dept => dept.parent_id)\n    }\n  }\n\n  return filtered\n})\n\nconst hasActiveFilters = computed(() => {\n  return searchQuery.value.trim() !== '' || hierarchyFilter.value !== ''\n})\n\nconst stats = computed(() => {\n  const totalDepartments = departments.value.length\n  const managersAssigned = departments.value.filter(dept => dept.manager_id).length\n  const totalEmployees = departments.value.reduce((sum, dept) => sum + (dept.user_count || 0), 0)\n\n  // Calculate hierarchy levels - simplified to avoid JS errors\n  let hierarchyLevels = 0\n  try {\n    const getMaxDepth = (depts, parentId = null, depth = 0) => {\n      const children = depts.filter(d => d.parent_id === parentId)\n      if (children.length === 0) return depth\n\n      let maxChildDepth = depth\n      for (const child of children) {\n        const childDepth = getMaxDepth(depts, child.id, depth + 1)\n        if (childDepth > maxChildDepth) {\n          maxChildDepth = childDepth\n        }\n      }\n      return maxChildDepth\n    }\n\n    hierarchyLevels = departments.value.length > 0 ? getMaxDepth(departments.value) + 1 : 0\n  } catch (e) {\n    console.warn('Error calculating hierarchy levels:', e)\n    hierarchyLevels = 1\n  }\n\n  return {\n    totalDepartments,\n    managersAssigned,\n    totalEmployees,\n    hierarchyLevels\n  }\n})\n\n// Utility functions\nconst formatCurrency = (amount) => {\n  return new Intl.NumberFormat('it-IT', {\n    style: 'currency',\n    currency: 'EUR'\n  }).format(amount)\n}\n\nlet searchTimeout = null\nconst debouncedSearch = () => {\n  clearTimeout(searchTimeout)\n  searchTimeout = setTimeout(() => {\n    // Search is reactive, no need to do anything here\n  }, 300)\n}\n\n// API functions\nconst fetchDepartments = async () => {\n  loading.value = true\n  error.value = null\n\n  try {\n    const response = await fetch('/api/personnel/departments', {\n      credentials: 'include'\n    })\n\n    if (!response.ok) {\n      throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n\n    if (data.success) {\n      departments.value = data.data.departments || []\n    } else {\n      throw new Error(data.message || 'Errore nel caricamento dei dipartimenti')\n    }\n  } catch (err) {\n    console.error('Error fetching departments:', err)\n    error.value = err.message\n  } finally {\n    loading.value = false\n  }\n}\n\nconst deleteDepartment = async (department) => {\n  if (!confirm(`Sei sicuro di voler eliminare il dipartimento \"${department.name}\"?`)) {\n    return\n  }\n\n  try {\n    const response = await fetch(`/api/personnel/departments/${department.id}`, {\n      method: 'DELETE',\n      credentials: 'include'\n    })\n\n    if (!response.ok) {\n      throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n\n    if (data.success) {\n      // Remove department from local list\n      departments.value = departments.value.filter(d => d.id !== department.id)\n\n      // Show success message (you might want to use a toast notification here)\n      alert(data.message || 'Dipartimento eliminato con successo')\n    } else {\n      throw new Error(data.message || 'Errore nell\\'eliminazione del dipartimento')\n    }\n  } catch (err) {\n    console.error('Error deleting department:', err)\n    alert('Errore nell\\'eliminazione del dipartimento: ' + err.message)\n  }\n}\n\n// Event handlers\nconst editDepartment = (department) => {\n  editingDepartment.value = department\n  showCreateModal.value = true\n}\n\nconst applyFilters = () => {\n  // Filters are reactive, no need to do anything here\n}\n\nconst clearFilters = () => {\n  searchQuery.value = ''\n  hierarchyFilter.value = ''\n}\n\n// Lifecycle\nonMounted(() => {\n  fetchDepartments()\n})\n</script>"}
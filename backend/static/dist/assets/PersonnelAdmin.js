import{r as x,f as L,w as oe,A as O,c as l,o as a,j as e,v as k,x as B,H as U,F as D,k as V,t as m,g as w,n as S,a as I,i as K,b as W,m as E,s as G,h as X,y as ae,z as Q,I as le,C as ne}from"./vendor.js";import{_ as F}from"./app.js";const de={class:"space-y-6"},ie={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0"},ue={class:"flex-1 max-w-lg"},me={class:"relative"},ce={class:"flex items-center space-x-3"},ge=["value"],pe=["disabled"],ve={key:0,class:"animate-spin w-4 h-4",fill:"none",viewBox:"0 0 24 24"},xe={key:1,class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},ye={key:0,class:"flex justify-center items-center h-64"},be={key:1,class:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4"},fe={class:"flex"},ke={class:"text-sm text-red-700 dark:text-red-300 mt-1"},he={key:2,class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},we={class:"overflow-x-auto"},_e={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},$e={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},Ce={class:"px-6 py-4 whitespace-nowrap"},Me={class:"flex items-center"},ze={class:"flex-shrink-0 w-10 h-10"},je={class:"w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center"},Be=["src","alt"],Ee={key:1,class:"w-5 h-5 text-gray-600 dark:text-gray-300",fill:"currentColor",viewBox:"0 0 20 20"},Te={class:"ml-4"},He={class:"text-sm font-medium text-gray-900 dark:text-white"},De={class:"text-sm text-gray-500 dark:text-gray-400"},Ve={key:0,class:"text-xs text-gray-400 dark:text-gray-500"},Ue={class:"px-6 py-4 whitespace-nowrap"},Le={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},Se={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"},Ae={class:"space-y-1"},Pe={key:0},Ne={key:1},Ie={class:"px-6 py-4 whitespace-nowrap"},Oe={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},Re={class:"flex items-center justify-end space-x-2"},Ge=["onClick"],Fe=["onClick"],qe=["onClick"],Je={key:0,class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Qe={key:1,class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Ke={key:0,class:"bg-white dark:bg-gray-800 px-4 py-3 border-t border-gray-200 dark:border-gray-700 sm:px-6"},We={class:"flex items-center justify-between"},Xe={class:"flex-1 flex justify-between sm:hidden"},Ye=["disabled"],Ze=["disabled"],et={class:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between"},tt={class:"text-sm text-gray-700 dark:text-gray-300"},rt={class:"font-medium"},st={class:"font-medium"},ot={class:"font-medium"},at={class:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px"},lt=["disabled"],nt=["onClick"],dt=["disabled"],it={key:3,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-12 text-center"},ut={class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},mt={__name:"UsersManagement",emits:["user-created","user-updated","user-deleted"],setup(h,{emit:H}){const p=H,y=x([]),_=x([]),v=x(!1),n=x(null),d=x(""),$=x(""),f=x(""),s=x(1),t=x(20),r=L(()=>{let c=y.value;if(d.value.trim()){const i=d.value.toLowerCase();c=c.filter(z=>z.full_name.toLowerCase().includes(i)||z.email.toLowerCase().includes(i)||z.position&&z.position.toLowerCase().includes(i)||z.department&&z.department.name.toLowerCase().includes(i))}return $.value&&(c=c.filter(i=>i.department_id==$.value)),f.value&&(c=c.filter(i=>i.role===f.value)),c}),b=L(()=>{const c=(s.value-1)*t.value,i=c+t.value;return r.value.slice(c,i)}),T=L(()=>Math.ceil(r.value.length/t.value)),A=L(()=>{const c=[],i=T.value,z=s.value;if(i<=7)for(let g=1;g<=i;g++)c.push(g);else if(z<=4){for(let g=1;g<=5;g++)c.push(g);c.push("..."),c.push(i)}else if(z>=i-3){c.push(1),c.push("...");for(let g=i-4;g<=i;g++)c.push(g)}else{c.push(1),c.push("...");for(let g=z-1;g<=z+1;g++)c.push(g);c.push("..."),c.push(i)}return c.filter(g=>g!=="..."||c.indexOf(g)===c.lastIndexOf(g))}),P=L(()=>d.value.trim()!==""||$.value!==""||f.value!==""),N=async()=>{v.value=!0,n.value=null;try{const c=await fetch("/api/personnel/users",{credentials:"include"});if(!c.ok)throw new Error(`HTTP ${c.status}: ${c.statusText}`);const i=await c.json();if(i.success)y.value=i.data.users||[];else throw new Error(i.message||"Errore nel caricamento utenti")}catch(c){console.error("Error loading users:",c),n.value=c.message}finally{v.value=!1}},q=async()=>{try{const c=await fetch("/api/personnel/departments",{credentials:"include"});if(!c.ok)throw new Error(`HTTP ${c.status}: ${c.statusText}`);const i=await c.json();i.success&&(_.value=i.data.departments||[])}catch(c){console.error("Error loading departments:",c)}},C=c=>{console.log("Edit user:",c)},o=async c=>{if(confirm(`Sei sicuro di voler resettare la password di ${c.full_name}?`))try{const i=await fetch(`/api/personnel/admin/users/${c.id}/reset-password`,{method:"POST",credentials:"include"});if(!i.ok)throw new Error(`HTTP ${i.status}: ${i.statusText}`);const z=await i.json();if(z.success)alert(`Password resettata per ${c.full_name}. Nuova password temporanea: ${z.data.temporary_password}`);else throw new Error(z.message||"Errore nel reset password")}catch(i){console.error("Error resetting password:",i),alert("Errore nel reset della password: "+i.message)}},u=async c=>{const i=c.is_active?"disattivare":"riattivare";if(confirm(`Sei sicuro di voler ${i} ${c.full_name}?`))try{if(c.is_active){const z=await fetch(`/api/personnel/admin/users/${c.id}`,{method:"DELETE",credentials:"include"});if(!z.ok)throw new Error(`HTTP ${z.status}: ${z.statusText}`);(await z.json()).success&&(c.is_active=!1,p("user-deleted",c))}else{const z=await fetch(`/api/personnel/users/${c.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({is_active:!0})});if(!z.ok)throw new Error(`HTTP ${z.status}: ${z.statusText}`);(await z.json()).success&&(c.is_active=!0,p("user-updated",c))}}catch(z){console.error("Error toggling user status:",z),alert("Errore nell'operazione: "+z.message)}},M=()=>{s.value>1&&s.value--},j=()=>{s.value<T.value&&s.value++},J=c=>({admin:"Admin",manager:"Manager",employee:"Dipendente"})[c]||c,re=c=>({full_time:"Tempo pieno",part_time:"Part-time",contractor:"Consulente",intern:"Stagista"})[c]||c,Y=c=>c?new Date(c).toLocaleDateString("it-IT",{year:"numeric",month:"short",day:"numeric"}):"",se=c=>{if(!c)return!1;const i=new Date,g=new Date(c)-i,R=Math.ceil(g/(1e3*60*60*24));return R<=90&&R>=0};return oe([d,$,f],()=>{s.value=1}),O(async()=>{await Promise.all([N(),q()])}),(c,i)=>{const z=W("router-link");return a(),l("div",de,[e("div",ie,[e("div",ue,[e("div",me,[i[3]||(i[3]=e("svg",{class:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1)),k(e("input",{"onUpdate:modelValue":i[0]||(i[0]=g=>d.value=g),type:"text",placeholder:"Cerca dipendenti...",class:"pl-10 pr-4 py-2 w-full border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[B,d.value]])])]),e("div",ce,[k(e("select",{"onUpdate:modelValue":i[1]||(i[1]=g=>$.value=g),class:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},[i[4]||(i[4]=e("option",{value:""},"Tutti i dipartimenti",-1)),(a(!0),l(D,null,V(_.value,g=>(a(),l("option",{key:g.id,value:g.id},m(g.name),9,ge))),128))],512),[[U,$.value]]),k(e("select",{"onUpdate:modelValue":i[2]||(i[2]=g=>f.value=g),class:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},i[5]||(i[5]=[e("option",{value:""},"Tutti i ruoli",-1),e("option",{value:"admin"},"Admin",-1),e("option",{value:"manager"},"Manager",-1),e("option",{value:"employee"},"Dipendente",-1)]),512),[[U,f.value]]),e("button",{onClick:N,disabled:v.value,class:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"},[v.value?(a(),l("svg",ve,i[6]||(i[6]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):(a(),l("svg",xe,i[7]||(i[7]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"},null,-1)])))],8,pe)])]),v.value&&!y.value.length?(a(),l("div",ye,i[8]||(i[8]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):n.value?(a(),l("div",be,[e("div",fe,[i[10]||(i[10]=e("svg",{class:"w-5 h-5 text-red-400 mr-2 mt-0.5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z","clip-rule":"evenodd"})],-1)),e("div",null,[i[9]||(i[9]=e("h3",{class:"text-sm font-medium text-red-800 dark:text-red-200"},"Errore nel caricamento",-1)),e("p",ke,m(n.value),1)])])])):y.value.length>0?(a(),l("div",he,[e("div",we,[e("table",_e,[i[17]||(i[17]=e("thead",{class:"bg-gray-50 dark:bg-gray-700"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Dipendente "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Ruolo "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Dipartimento "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Contratto "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Stato "),e("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Azioni ")])],-1)),e("tbody",$e,[(a(!0),l(D,null,V(b.value,g=>{var R,Z,ee;return a(),l("tr",{key:g.id,class:"hover:bg-gray-50 dark:hover:bg-gray-700"},[e("td",Ce,[e("div",Me,[e("div",ze,[e("div",je,[g.profile_image?(a(),l("img",{key:0,src:g.profile_image,alt:g.full_name,class:"w-10 h-10 rounded-full object-cover"},null,8,Be)):(a(),l("svg",Ee,i[11]||(i[11]=[e("path",{"fill-rule":"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z","clip-rule":"evenodd"},null,-1)])))])]),e("div",Te,[e("div",He,m(g.full_name),1),e("div",De,m(g.email),1),g.position?(a(),l("div",Ve,m(g.position),1)):w("",!0)])])]),e("td",Ue,[e("span",{class:S(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",g.role==="admin"?"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200":g.role==="manager"?"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200":"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"])},m(J(g.role)),3)]),e("td",Le,m(((R=g.department)==null?void 0:R.name)||"Nessun dipartimento"),1),e("td",Se,[e("div",Ae,[g.hire_date?(a(),l("div",Pe," Assunto: "+m(Y(g.hire_date)),1)):w("",!0),(Z=g.profile)!=null&&Z.employment_type?(a(),l("div",Ne,m(re(g.profile.employment_type)),1)):w("",!0),(ee=g.profile)!=null&&ee.contract_end_date?(a(),l("div",{key:2,class:S(se(g.profile.contract_end_date)?"text-red-600 dark:text-red-400 font-medium":"")}," Scade: "+m(Y(g.profile.contract_end_date)),3)):w("",!0)])]),e("td",Ie,[e("span",{class:S(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",g.is_active?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200":"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"])},m(g.is_active?"Attivo":"Disattivato"),3)]),e("td",Oe,[e("div",Re,[I(z,{to:`/app/personnel/${g.id}`,class:"text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300"},{default:K(()=>i[12]||(i[12]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})],-1)])),_:2,__:[12]},1032,["to"]),e("button",{onClick:te=>C(g),class:"text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300"},i[13]||(i[13]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1)]),8,Ge),e("button",{onClick:te=>o(g),class:"text-yellow-600 dark:text-yellow-400 hover:text-yellow-900 dark:hover:text-yellow-300"},i[14]||(i[14]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"})],-1)]),8,Fe),e("button",{onClick:te=>u(g),class:S(g.is_active?"text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300":"text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300")},[g.is_active?(a(),l("svg",Je,i[15]||(i[15]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"},null,-1)]))):(a(),l("svg",Qe,i[16]||(i[16]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"},null,-1)])))],10,qe)])])])}),128))])])]),T.value>1?(a(),l("div",Ke,[e("div",We,[e("div",Xe,[e("button",{onClick:M,disabled:s.value===1,class:"relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"}," Precedente ",8,Ye),e("button",{onClick:j,disabled:s.value===T.value,class:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"}," Successiva ",8,Ze)]),e("div",et,[e("div",null,[e("p",tt,[i[18]||(i[18]=E(" Mostrando ")),e("span",rt,m((s.value-1)*t.value+1),1),i[19]||(i[19]=E(" a ")),e("span",st,m(Math.min(s.value*t.value,r.value.length)),1),i[20]||(i[20]=E(" di ")),e("span",ot,m(r.value.length),1),i[21]||(i[21]=E(" risultati "))])]),e("div",null,[e("nav",at,[e("button",{onClick:M,disabled:s.value===1,class:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"},i[22]||(i[22]=[e("svg",{class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z","clip-rule":"evenodd"})],-1)]),8,lt),(a(!0),l(D,null,V(A.value,g=>(a(),l("button",{key:g,onClick:R=>s.value=g,class:S(["relative inline-flex items-center px-4 py-2 border text-sm font-medium",g===s.value?"z-10 bg-blue-50 dark:bg-blue-900 border-blue-500 text-blue-600 dark:text-blue-200":"bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700"])},m(g),11,nt))),128)),e("button",{onClick:j,disabled:s.value===T.value,class:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"},i[23]||(i[23]=[e("svg",{class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})],-1)]),8,dt)])])])])])):w("",!0)])):(a(),l("div",it,[i[24]||(i[24]=e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})],-1)),i[25]||(i[25]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun dipendente trovato",-1)),e("p",ut,m(P.value?"Prova a modificare i filtri di ricerca.":"Inizia creando il primo dipendente."),1)]))])}}},ct=F(mt,[["__scopeId","data-v-1187e011"]]),gt={class:"flex items-center justify-between pb-4 border-b border-gray-200 dark:border-gray-700"},pt={class:"text-lg font-medium text-gray-900 dark:text-white"},vt={class:"mt-6"},xt={class:"grid grid-cols-1 gap-4"},yt={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},bt=["value"],ft=["value"],kt={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},ht={key:0,class:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4"},wt={class:"flex"},_t={class:"text-sm text-red-700 dark:text-red-300 mt-1"},$t={class:"flex items-center justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700"},Ct=["disabled"],Mt={key:0,class:"animate-spin -ml-1 mr-3 h-4 w-4 text-white inline",fill:"none",viewBox:"0 0 24 24"},zt={__name:"DepartmentModal",props:{department:{type:Object,default:null},managers:{type:Array,default:()=>[]},departments:{type:Array,default:()=>[]}},emits:["close","saved"],setup(h,{emit:H}){const p=h,y=H,_=x(!1),v=x(null),n=x({name:"",description:"",manager_id:"",parent_id:"",budget:null,code:""}),d=L(()=>p.department?p.departments.filter(f=>f.id!==p.department.id&&f.parent_id!==p.department.id):p.departments),$=async()=>{_.value=!0,v.value=null;try{const f={...n.value};Object.keys(f).forEach(T=>{f[T]===""&&(f[T]=null)});const s=p.department?`/api/personnel/departments/${p.department.id}`:"/api/personnel/departments",t=p.department?"PUT":"POST",r=await fetch(s,{method:t,headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(f)});if(!r.ok)throw new Error(`HTTP ${r.status}: ${r.statusText}`);const b=await r.json();if(b.success)y("saved",b.data.department);else throw new Error(b.message||"Errore nel salvataggio dipartimento")}catch(f){console.error("Error saving department:",f),v.value=f.message}finally{_.value=!1}};return O(()=>{p.department&&(n.value={name:p.department.name||"",description:p.department.description||"",manager_id:p.department.manager_id||"",parent_id:p.department.parent_id||"",budget:p.department.budget||null,code:p.department.code||""})}),(f,s)=>(a(),l("div",{class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:s[9]||(s[9]=t=>f.$emit("close"))},[e("div",{class:"relative top-20 mx-auto p-5 border w-11/12 max-w-2xl shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:s[8]||(s[8]=G(()=>{},["stop"]))},[e("div",gt,[e("h3",pt,m(h.department?"Modifica Dipartimento":"Nuovo Dipartimento"),1),e("button",{onClick:s[0]||(s[0]=t=>f.$emit("close")),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},s[10]||(s[10]=[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),e("div",vt,[e("form",{onSubmit:G($,["prevent"]),class:"space-y-6"},[e("div",null,[s[13]||(s[13]=e("h4",{class:"text-md font-medium text-gray-900 dark:text-white mb-4"},"Informazioni Base",-1)),e("div",xt,[e("div",null,[s[11]||(s[11]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Nome Dipartimento * ",-1)),k(e("input",{"onUpdate:modelValue":s[1]||(s[1]=t=>n.value.name=t),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[B,n.value.name]])]),e("div",null,[s[12]||(s[12]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Descrizione ",-1)),k(e("textarea",{"onUpdate:modelValue":s[2]||(s[2]=t=>n.value.description=t),rows:"3",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[B,n.value.description]])])])]),e("div",null,[s[18]||(s[18]=e("h4",{class:"text-md font-medium text-gray-900 dark:text-white mb-4"},"Gestione",-1)),e("div",yt,[e("div",null,[s[15]||(s[15]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Manager ",-1)),k(e("select",{"onUpdate:modelValue":s[3]||(s[3]=t=>n.value.manager_id=t),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},[s[14]||(s[14]=e("option",{value:""},"Seleziona manager",-1)),(a(!0),l(D,null,V(h.managers,t=>(a(),l("option",{key:t.id,value:t.id},m(t.full_name),9,bt))),128))],512),[[U,n.value.manager_id]])]),e("div",null,[s[17]||(s[17]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Dipartimento Padre ",-1)),k(e("select",{"onUpdate:modelValue":s[4]||(s[4]=t=>n.value.parent_id=t),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},[s[16]||(s[16]=e("option",{value:""},"Nessun padre (dipartimento principale)",-1)),(a(!0),l(D,null,V(d.value,t=>(a(),l("option",{key:t.id,value:t.id},m(t.name),9,ft))),128))],512),[[U,n.value.parent_id]])])])]),e("div",null,[s[21]||(s[21]=e("h4",{class:"text-md font-medium text-gray-900 dark:text-white mb-4"},"Budget",-1)),e("div",kt,[e("div",null,[s[19]||(s[19]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Budget Annuale (€) ",-1)),k(e("input",{"onUpdate:modelValue":s[5]||(s[5]=t=>n.value.budget=t),type:"number",step:"100",min:"0",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[B,n.value.budget,void 0,{number:!0}]])]),e("div",null,[s[20]||(s[20]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Codice Dipartimento ",-1)),k(e("input",{"onUpdate:modelValue":s[6]||(s[6]=t=>n.value.code=t),type:"text",placeholder:"es. IT, HR, SALES",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[B,n.value.code]])])])]),v.value?(a(),l("div",ht,[e("div",wt,[s[23]||(s[23]=e("svg",{class:"w-5 h-5 text-red-400 mr-2 mt-0.5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z","clip-rule":"evenodd"})],-1)),e("div",null,[s[22]||(s[22]=e("h3",{class:"text-sm font-medium text-red-800 dark:text-red-200"},"Errore nel salvataggio",-1)),e("p",_t,m(v.value),1)])])])):w("",!0),e("div",$t,[e("button",{type:"button",onClick:s[7]||(s[7]=t=>f.$emit("close")),class:"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"}," Annulla "),e("button",{type:"submit",disabled:_.value,class:"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"},[_.value?(a(),l("svg",Mt,s[24]||(s[24]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):w("",!0),E(" "+m(_.value?"Salvataggio...":h.department?"Aggiorna":"Crea"),1)],8,Ct)])],32)])])]))}},jt=F(zt,[["__scopeId","data-v-2d5db05c"]]),Bt={class:"space-y-6"},Et={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},Tt={class:"mt-4 sm:mt-0 flex items-center space-x-3"},Ht={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},Dt={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},Vt={class:"p-5"},Ut={class:"flex items-center"},Lt={class:"ml-5 w-0 flex-1"},St={class:"text-lg font-medium text-gray-900 dark:text-white"},At={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},Pt={class:"p-5"},Nt={class:"flex items-center"},It={class:"ml-5 w-0 flex-1"},Ot={class:"text-lg font-medium text-gray-900 dark:text-white"},Rt={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},Gt={class:"p-5"},Ft={class:"flex items-center"},qt={class:"ml-5 w-0 flex-1"},Jt={class:"text-lg font-medium text-gray-900 dark:text-white"},Qt={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},Kt={class:"p-5"},Wt={class:"flex items-center"},Xt={class:"ml-5 w-0 flex-1"},Yt={class:"text-lg font-medium text-gray-900 dark:text-white"},Zt={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0"},er={class:"flex-1 max-w-lg"},tr={class:"relative"},rr={class:"flex items-center space-x-3"},sr=["disabled"],or={key:0,class:"animate-spin w-4 h-4",fill:"none",viewBox:"0 0 24 24"},ar={key:1,class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},lr={key:0,class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},nr={class:"overflow-x-auto"},dr={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},ir={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},ur={class:"px-6 py-4 whitespace-nowrap"},mr={class:"text-sm font-medium text-gray-900 dark:text-white"},cr={key:0,class:"text-sm text-gray-500 dark:text-gray-400"},gr={key:1,class:"text-xs text-gray-400 dark:text-gray-500"},pr={class:"px-6 py-4 whitespace-nowrap"},vr={key:0,class:"flex items-center"},xr={class:"ml-3"},yr={class:"text-sm font-medium text-gray-900 dark:text-white"},br={class:"text-sm text-gray-500 dark:text-gray-400"},fr={key:1,class:"text-sm text-gray-500 dark:text-gray-400"},kr={class:"px-6 py-4 whitespace-nowrap"},hr={class:"flex items-center"},wr={class:"text-sm font-medium text-gray-900 dark:text-white"},_r={class:"px-6 py-4 whitespace-nowrap"},$r={class:"text-sm text-gray-900 dark:text-white"},Cr={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},Mr={class:"flex items-center justify-end space-x-2"},zr=["onClick"],jr=["onClick","disabled"],Br={key:1,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-12 text-center"},Er={class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},Tr={class:"mt-6"},Hr={key:2,class:"flex justify-center items-center h-64"},Dr={__name:"DepartmentsManagement",emits:["department-created","department-updated","department-deleted"],setup(h,{emit:H}){const p=H,y=x([]),_=x([]),v=x({}),n=x(!1),d=x(""),$=x(!1),f=x(!1),s=x(null),t=L(()=>{if(!d.value.trim())return y.value;const o=d.value.toLowerCase();return y.value.filter(u=>u.name.toLowerCase().includes(o)||u.description&&u.description.toLowerCase().includes(o)||u.manager&&u.manager.full_name.toLowerCase().includes(o))}),r=async()=>{n.value=!0;try{const o=await fetch("/api/personnel/departments",{credentials:"include"});if(!o.ok)throw new Error(`HTTP ${o.status}: ${o.statusText}`);const u=await o.json();if(u.success)y.value=u.data.departments||[];else throw new Error(u.message||"Errore nel caricamento dipartimenti")}catch(o){console.error("Error loading departments:",o)}finally{n.value=!1}},b=async()=>{try{const o=await fetch("/api/personnel/orgchart",{credentials:"include"});if(!o.ok)throw new Error(`HTTP ${o.status}: ${o.statusText}`);const u=await o.json();if(u.success){v.value=u.data.stats||{};const M=y.value.reduce((j,J)=>j+(J.budget||0),0);v.value.total_budget=M}}catch(o){console.error("Error loading stats:",o)}},T=async()=>{try{const o=await fetch("/api/personnel/users?role=manager,admin",{credentials:"include"});if(!o.ok)throw new Error(`HTTP ${o.status}: ${o.statusText}`);const u=await o.json();u.success&&(_.value=u.data.users||[])}catch(o){console.error("Error loading managers:",o)}},A=o=>{s.value={...o},f.value=!0},P=async o=>{if(o.user_count>0){alert("Impossibile eliminare un dipartimento con dipendenti assegnati");return}if(confirm(`Sei sicuro di voler eliminare il dipartimento "${o.name}"?`))try{const u=await fetch(`/api/personnel/departments/${o.id}`,{method:"DELETE",credentials:"include"});if(!u.ok)throw new Error(`HTTP ${u.status}: ${u.statusText}`);const M=await u.json();if(M.success)await r(),await b(),p("department-deleted",o);else throw new Error(M.message||"Errore nell'eliminazione")}catch(u){console.error("Error deleting department:",u),alert("Errore nell'eliminazione: "+u.message)}},N=()=>{$.value=!1,f.value=!1,s.value=null},q=async o=>{N(),await r(),await b(),s.value?p("department-updated",o):p("department-created",o)},C=o=>o?new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(o):"€0";return O(async()=>{await Promise.all([r(),b(),T()])}),(o,u)=>{const M=W("router-link");return a(),l("div",Bt,[e("div",Et,[u[5]||(u[5]=e("div",null,[e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Gestione Dipartimenti"),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"}," Gestisci la struttura organizzativa aziendale ")],-1)),e("div",Tt,[I(M,{to:"/app/personnel/departments",class:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"},{default:K(()=>u[3]||(u[3]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"})],-1),E(" Vista Avanzata ")])),_:1,__:[3]}),e("button",{onClick:u[0]||(u[0]=j=>$.value=!0),class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"},u[4]||(u[4]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),E(" Nuovo Dipartimento ")]))])]),e("div",Ht,[e("div",Dt,[e("div",Vt,[e("div",Ut,[u[7]||(u[7]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-6 w-6 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})])],-1)),e("div",Lt,[e("dl",null,[u[6]||(u[6]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Dipartimenti Totali ",-1)),e("dd",St,m(v.value.total_departments||0),1)])])])])]),e("div",At,[e("div",Pt,[e("div",Nt,[u[9]||(u[9]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-6 w-6 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})])],-1)),e("div",It,[e("dl",null,[u[8]||(u[8]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Dipendenti Totali ",-1)),e("dd",Ot,m(v.value.total_employees||0),1)])])])])]),e("div",Rt,[e("div",Gt,[e("div",Ft,[u[11]||(u[11]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-6 w-6 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"})])],-1)),e("div",qt,[e("dl",null,[u[10]||(u[10]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Manager Assegnati ",-1)),e("dd",Jt,m(v.value.total_managers||0),1)])])])])]),e("div",Qt,[e("div",Kt,[e("div",Wt,[u[13]||(u[13]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-6 w-6 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",Xt,[e("dl",null,[u[12]||(u[12]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Budget Totale ",-1)),e("dd",Yt,m(C(v.value.total_budget||0)),1)])])])])])]),e("div",Zt,[e("div",er,[e("div",tr,[u[14]||(u[14]=e("svg",{class:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1)),k(e("input",{"onUpdate:modelValue":u[1]||(u[1]=j=>d.value=j),type:"text",placeholder:"Cerca dipartimenti...",class:"pl-10 pr-4 py-2 w-full border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[B,d.value]])])]),e("div",rr,[e("button",{onClick:r,disabled:n.value,class:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"},[n.value?(a(),l("svg",or,u[15]||(u[15]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):(a(),l("svg",ar,u[16]||(u[16]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"},null,-1)])))],8,sr)])]),t.value.length>0?(a(),l("div",lr,[e("div",nr,[e("table",dr,[u[21]||(u[21]=e("thead",{class:"bg-gray-50 dark:bg-gray-700"},[e("tr",null,[e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Dipartimento "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Manager "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Dipendenti "),e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Budget "),e("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Azioni ")])],-1)),e("tbody",ir,[(a(!0),l(D,null,V(t.value,j=>(a(),l("tr",{key:j.id,class:"hover:bg-gray-50 dark:hover:bg-gray-700"},[e("td",ur,[e("div",null,[e("div",mr,m(j.name),1),j.description?(a(),l("div",cr,m(j.description),1)):w("",!0),j.parent?(a(),l("div",gr," Sotto: "+m(j.parent.name),1)):w("",!0)])]),e("td",pr,[j.manager?(a(),l("div",vr,[u[17]||(u[17]=e("div",{class:"flex-shrink-0 w-8 h-8"},[e("div",{class:"w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-gray-600 dark:text-gray-300",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z","clip-rule":"evenodd"})])])],-1)),e("div",xr,[e("div",yr,m(j.manager.full_name),1),e("div",br,m(j.manager.email),1)])])):(a(),l("span",fr,"Nessun manager"))]),e("td",kr,[e("div",hr,[e("span",wr,m(j.user_count||0),1),u[18]||(u[18]=e("span",{class:"ml-1 text-sm text-gray-500 dark:text-gray-400"}," dipendenti ",-1))])]),e("td",_r,[e("span",$r,m(C(j.budget||0)),1)]),e("td",Cr,[e("div",Mr,[e("button",{onClick:J=>A(j),class:"text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300"},u[19]||(u[19]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1)]),8,zr),e("button",{onClick:J=>P(j),disabled:j.user_count>0,class:S(j.user_count>0?"text-gray-400 cursor-not-allowed":"text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300")},u[20]||(u[20]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)]),10,jr)])])]))),128))])])])])):n.value?w("",!0):(a(),l("div",Br,[u[23]||(u[23]=e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})],-1)),u[24]||(u[24]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun dipartimento trovato",-1)),e("p",Er,m(d.value?"Prova a modificare i criteri di ricerca.":"Inizia creando il primo dipartimento."),1),e("div",Tr,[e("button",{onClick:u[2]||(u[2]=j=>$.value=!0),class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"},u[22]||(u[22]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),E(" Crea Primo Dipartimento ")]))])])),n.value?(a(),l("div",Hr,u[25]||(u[25]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):w("",!0),$.value||f.value?(a(),X(jt,{key:3,department:s.value,managers:_.value,departments:y.value,onClose:N,onSaved:q},null,8,["department","managers","departments"])):w("",!0)])}}},Vr={class:"flex items-center justify-between pb-4 border-b border-gray-200 dark:border-gray-700"},Ur={class:"text-lg font-medium text-gray-900 dark:text-white"},Lr={class:"mt-6"},Sr={class:"flex space-x-2"},Ar=["value"],Pr={key:0,class:"mt-2"},Nr={key:0,class:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4"},Ir={class:"flex"},Or={class:"text-sm text-red-700 dark:text-red-300 mt-1"},Rr={class:"flex items-center justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700"},Gr=["disabled"],Fr={key:0,class:"animate-spin -ml-1 mr-3 h-4 w-4 text-white inline",fill:"none",viewBox:"0 0 24 24"},qr={__name:"SkillModal",props:{skill:{type:Object,default:null},categories:{type:Array,default:()=>[]}},emits:["close","saved"],setup(h,{emit:H}){const p=h,y=H,_=x(!1),v=x(null),n=x(!1),d=x(""),$=x({name:"",category:"",description:""}),f=()=>{d.value.trim()&&($.value.category=d.value.trim(),d.value="",n.value=!1)},s=async()=>{_.value=!0,v.value=null;try{const t={...$.value};Object.keys(t).forEach(P=>{t[P]===""&&(t[P]=null)});const r=p.skill?`/api/personnel/skills/${p.skill.id}`:"/api/personnel/skills",b=p.skill?"PUT":"POST",T=await fetch(r,{method:b,headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(t)});if(!T.ok)throw new Error(`HTTP ${T.status}: ${T.statusText}`);const A=await T.json();if(A.success)y("saved",A.data.skill);else throw new Error(A.message||"Errore nel salvataggio competenza")}catch(t){console.error("Error saving skill:",t),v.value=t.message}finally{_.value=!1}};return O(()=>{p.skill&&($.value={name:p.skill.name||"",category:p.skill.category||"",description:p.skill.description||""})}),(t,r)=>(a(),l("div",{class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:r[8]||(r[8]=b=>t.$emit("close"))},[e("div",{class:"relative top-20 mx-auto p-5 border w-11/12 max-w-lg shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:r[7]||(r[7]=G(()=>{},["stop"]))},[e("div",Vr,[e("h3",Ur,m(h.skill?"Modifica Competenza":"Nuova Competenza"),1),e("button",{onClick:r[0]||(r[0]=b=>t.$emit("close")),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},r[9]||(r[9]=[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),e("div",Lr,[e("form",{onSubmit:G(s,["prevent"]),class:"space-y-4"},[e("div",null,[r[10]||(r[10]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Nome Competenza * ",-1)),k(e("input",{"onUpdate:modelValue":r[1]||(r[1]=b=>$.value.name=b),type:"text",required:"",placeholder:"es. JavaScript, Project Management, Design Thinking",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[B,$.value.name]])]),e("div",null,[r[12]||(r[12]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Categoria ",-1)),e("div",Sr,[k(e("select",{"onUpdate:modelValue":r[2]||(r[2]=b=>$.value.category=b),class:"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},[r[11]||(r[11]=e("option",{value:""},"Seleziona categoria esistente",-1)),(a(!0),l(D,null,V(h.categories,b=>(a(),l("option",{key:b,value:b},m(b),9,Ar))),128))],512),[[U,$.value.category]]),e("button",{type:"button",onClick:r[3]||(r[3]=b=>n.value=!n.value),class:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"},m(n.value?"Annulla":"Nuova"),1)]),n.value?(a(),l("div",Pr,[k(e("input",{"onUpdate:modelValue":r[4]||(r[4]=b=>d.value=b),type:"text",placeholder:"Nome nuova categoria",onBlur:f,onKeyup:ae(f,["enter"]),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,544),[[B,d.value]])])):w("",!0)]),e("div",null,[r[13]||(r[13]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Descrizione ",-1)),k(e("textarea",{"onUpdate:modelValue":r[5]||(r[5]=b=>$.value.description=b),rows:"3",placeholder:"Descrizione della competenza e come viene utilizzata...",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[B,$.value.description]])]),v.value?(a(),l("div",Nr,[e("div",Ir,[r[15]||(r[15]=e("svg",{class:"w-5 h-5 text-red-400 mr-2 mt-0.5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z","clip-rule":"evenodd"})],-1)),e("div",null,[r[14]||(r[14]=e("h3",{class:"text-sm font-medium text-red-800 dark:text-red-200"},"Errore nel salvataggio",-1)),e("p",Or,m(v.value),1)])])])):w("",!0),e("div",Rr,[e("button",{type:"button",onClick:r[6]||(r[6]=b=>t.$emit("close")),class:"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"}," Annulla "),e("button",{type:"submit",disabled:_.value,class:"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"},[_.value?(a(),l("svg",Fr,r[16]||(r[16]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):w("",!0),E(" "+m(_.value?"Salvataggio...":h.skill?"Aggiorna":"Crea"),1)],8,Gr)])],32)])])]))}},Jr=F(qr,[["__scopeId","data-v-06d169f0"]]),Qr={class:"space-y-6"},Kr={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},Wr={class:"mt-4 sm:mt-0 flex items-center space-x-3"},Xr={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},Yr={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},Zr={class:"p-5"},es={class:"flex items-center"},ts={class:"ml-5 w-0 flex-1"},rs={class:"text-lg font-medium text-gray-900 dark:text-white"},ss={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},os={class:"p-5"},as={class:"flex items-center"},ls={class:"ml-5 w-0 flex-1"},ns={class:"text-lg font-medium text-gray-900 dark:text-white"},ds={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},is={class:"p-5"},us={class:"flex items-center"},ms={class:"ml-5 w-0 flex-1"},cs={class:"text-lg font-medium text-gray-900 dark:text-white"},gs={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},ps={class:"p-5"},vs={class:"flex items-center"},xs={class:"ml-5 w-0 flex-1"},ys={class:"text-lg font-medium text-gray-900 dark:text-white"},bs={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0"},fs={class:"flex-1 max-w-lg"},ks={class:"relative"},hs={class:"flex items-center space-x-3"},ws=["value"],_s=["disabled"],$s={key:0,class:"animate-spin w-4 h-4",fill:"none",viewBox:"0 0 24 24"},Cs={key:1,class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Ms={key:0,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"},zs={class:"flex items-start justify-between"},js={class:"flex-1"},Bs={class:"text-lg font-medium text-gray-900 dark:text-white"},Es={key:0,class:"text-sm text-blue-600 dark:text-blue-400 mt-1"},Ts={key:1,class:"text-sm text-gray-500 dark:text-gray-400 mt-2"},Hs={class:"mt-4 flex items-center justify-between"},Ds={class:"flex items-center text-sm text-gray-500 dark:text-gray-400"},Vs={class:"flex items-center space-x-2"},Us=["onClick"],Ls=["onClick","disabled"],Ss={key:1,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-12 text-center"},As={class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},Ps={class:"mt-6"},Ns={key:2,class:"flex justify-center items-center h-64"},Is={__name:"SkillsManagement",emits:["skill-created","skill-updated","skill-deleted"],setup(h,{emit:H}){const p=H,y=x([]),_=x([]),v=x(!1),n=x(""),d=x(""),$=x(!1),f=x(!1),s=x(null),t=L(()=>{let C=y.value;if(n.value.trim()){const o=n.value.toLowerCase();C=C.filter(u=>u.name.toLowerCase().includes(o)||u.description&&u.description.toLowerCase().includes(o)||u.category&&u.category.toLowerCase().includes(o))}return d.value&&(C=C.filter(o=>o.category===d.value)),C}),r=L(()=>y.value.reduce((C,o)=>C+(o.user_count||0),0)),b=L(()=>y.value.filter(o=>o.user_count>0).length===0?0:3.2),T=async()=>{v.value=!0;try{const C=await fetch("/api/personnel/skills",{credentials:"include"});if(!C.ok)throw new Error(`HTTP ${C.status}: ${C.statusText}`);const o=await C.json();if(o.success)y.value=o.data.skills||[],_.value=o.data.categories||[];else throw new Error(o.message||"Errore nel caricamento competenze")}catch(C){console.error("Error loading skills:",C)}finally{v.value=!1}},A=C=>{s.value={...C},f.value=!0},P=async C=>{if(C.user_count>0){alert("Impossibile eliminare una competenza assegnata a dipendenti");return}if(confirm(`Sei sicuro di voler eliminare la competenza "${C.name}"?`))try{const o=await fetch(`/api/personnel/skills/${C.id}`,{method:"DELETE",credentials:"include"});if(!o.ok)throw new Error(`HTTP ${o.status}: ${o.statusText}`);const u=await o.json();if(u.success)await T(),p("skill-deleted",C);else throw new Error(u.message||"Errore nell'eliminazione")}catch(o){console.error("Error deleting skill:",o),alert("Errore nell'eliminazione: "+o.message)}},N=()=>{$.value=!1,f.value=!1,s.value=null},q=async C=>{N(),await T(),s.value?p("skill-updated",C):p("skill-created",C)};return O(()=>{T()}),(C,o)=>{const u=W("router-link");return a(),l("div",Qr,[e("div",Kr,[o[6]||(o[6]=e("div",null,[e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Gestione Competenze"),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"}," Gestisci il catalogo delle competenze aziendali ")],-1)),e("div",Wr,[I(u,{to:"/app/personnel/skills",class:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"},{default:K(()=>o[4]||(o[4]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"})],-1),E(" Matrice Completa ")])),_:1,__:[4]}),e("button",{onClick:o[0]||(o[0]=M=>$.value=!0),class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"},o[5]||(o[5]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),E(" Nuova Competenza ")]))])]),e("div",Xr,[e("div",Yr,[e("div",Zr,[e("div",es,[o[8]||(o[8]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-6 w-6 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})])],-1)),e("div",ts,[e("dl",null,[o[7]||(o[7]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Competenze Totali ",-1)),e("dd",rs,m(y.value.length),1)])])])])]),e("div",ss,[e("div",os,[e("div",as,[o[10]||(o[10]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-6 w-6 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"})])],-1)),e("div",ls,[e("dl",null,[o[9]||(o[9]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Categorie ",-1)),e("dd",ns,m(_.value.length),1)])])])])]),e("div",ds,[e("div",is,[e("div",us,[o[12]||(o[12]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-6 w-6 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})])],-1)),e("div",ms,[e("dl",null,[o[11]||(o[11]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Competenze Assegnate ",-1)),e("dd",cs,m(r.value),1)])])])])]),e("div",gs,[e("div",ps,[e("div",vs,[o[14]||(o[14]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-6 w-6 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])],-1)),e("div",xs,[e("dl",null,[o[13]||(o[13]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Livello Medio ",-1)),e("dd",ys,m(b.value.toFixed(1)),1)])])])])])]),e("div",bs,[e("div",fs,[e("div",ks,[o[15]||(o[15]=e("svg",{class:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1)),k(e("input",{"onUpdate:modelValue":o[1]||(o[1]=M=>n.value=M),type:"text",placeholder:"Cerca competenze...",class:"pl-10 pr-4 py-2 w-full border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[B,n.value]])])]),e("div",hs,[k(e("select",{"onUpdate:modelValue":o[2]||(o[2]=M=>d.value=M),class:"px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},[o[16]||(o[16]=e("option",{value:""},"Tutte le categorie",-1)),(a(!0),l(D,null,V(_.value,M=>(a(),l("option",{key:M,value:M},m(M),9,ws))),128))],512),[[U,d.value]]),e("button",{onClick:T,disabled:v.value,class:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"},[v.value?(a(),l("svg",$s,o[17]||(o[17]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):(a(),l("svg",Cs,o[18]||(o[18]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"},null,-1)])))],8,_s)])]),t.value.length>0?(a(),l("div",Ms,[(a(!0),l(D,null,V(t.value,M=>(a(),l("div",{key:M.id,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6 hover:shadow-lg transition-shadow"},[e("div",zs,[e("div",js,[e("h4",Bs,m(M.name),1),M.category?(a(),l("p",Es,m(M.category),1)):w("",!0),M.description?(a(),l("p",Ts,m(M.description),1)):w("",!0),e("div",Hs,[e("div",Ds,[o[19]||(o[19]=e("svg",{class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})],-1)),E(" "+m(M.user_count)+" dipendenti ",1)]),e("div",Vs,[e("button",{onClick:j=>A(M),class:"text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300"},o[20]||(o[20]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1)]),8,Us),e("button",{onClick:j=>P(M),disabled:M.user_count>0,class:S(M.user_count>0?"text-gray-400 cursor-not-allowed":"text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300")},o[21]||(o[21]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)]),10,Ls)])])])])]))),128))])):v.value?w("",!0):(a(),l("div",Ss,[o[23]||(o[23]=e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})],-1)),o[24]||(o[24]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessuna competenza trovata",-1)),e("p",As,m(n.value||d.value?"Prova a modificare i filtri di ricerca.":"Inizia creando la prima competenza."),1),e("div",Ps,[e("button",{onClick:o[3]||(o[3]=M=>$.value=!0),class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"},o[22]||(o[22]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),E(" Crea Prima Competenza ")]))])])),v.value?(a(),l("div",Ns,o[25]||(o[25]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):w("",!0),$.value||f.value?(a(),X(Jr,{key:3,skill:s.value,categories:_.value,onClose:N,onSaved:q},null,8,["skill","categories"])):w("",!0)])}}},Os={class:"space-y-6"},Rs={key:0,class:"grid grid-cols-1 md:grid-cols-3 gap-6"},Gs={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},Fs={class:"p-5"},qs={class:"flex items-center"},Js={class:"ml-5 w-0 flex-1"},Qs={class:"text-lg font-medium text-gray-900 dark:text-white"},Ks={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},Ws={class:"p-5"},Xs={class:"flex items-center"},Ys={class:"ml-5 w-0 flex-1"},Zs={class:"text-lg font-medium text-gray-900 dark:text-white"},eo={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},to={class:"p-5"},ro={class:"flex items-center"},so={class:"ml-5 w-0 flex-1"},oo={class:"text-lg font-medium text-gray-900 dark:text-white"},ao={key:1,class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},lo={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},no={class:"space-y-3"},io={class:"flex items-center"},uo={class:"text-sm text-gray-700 dark:text-gray-300"},mo={class:"flex items-center"},co={class:"text-sm font-medium text-gray-900 dark:text-white mr-2"},go={class:"w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2"},po={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},vo={class:"space-y-3 max-h-64 overflow-y-auto"},xo={class:"flex items-center"},yo={class:"text-sm text-gray-700 dark:text-gray-300 truncate"},bo={class:"flex items-center"},fo={class:"text-sm font-medium text-gray-900 dark:text-white mr-2"},ko={class:"w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2"},ho={key:2,class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},wo={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},_o={class:"space-y-3"},$o={class:"flex items-center"},Co={class:"text-sm text-gray-700 dark:text-gray-300"},Mo={class:"flex items-center"},zo={class:"text-sm font-medium text-gray-900 dark:text-white mr-2"},jo={class:"w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2"},Bo={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Eo={key:0,class:"space-y-3 max-h-64 overflow-y-auto"},To={class:"text-sm text-gray-700 dark:text-gray-300 truncate"},Ho={class:"text-sm font-medium text-gray-900 dark:text-white"},Do={key:1,class:"text-center py-8"},Vo={class:"flex justify-center"},Uo=["disabled"],Lo={key:0,class:"animate-spin -ml-1 mr-3 h-4 w-4 text-white",fill:"none",viewBox:"0 0 24 24"},So={key:1,class:"-ml-1 mr-3 h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Ao={key:3,class:"flex justify-center items-center h-64"},Po={key:4,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-12 text-center"},No={__name:"AnalyticsDashboard",props:{analytics:{type:Object,default:null},loading:{type:Boolean,default:!1}},emits:["refresh"],setup(h,{emit:H}){const p=v=>({admin:"Admin",manager:"Manager",employee:"Dipendente"})[v]||v,y=v=>({full_time:"Tempo pieno",part_time:"Part-time",contractor:"Consulente",intern:"Stagista"})[v]||v,_=v=>v?new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(v):"€0";return(v,n)=>(a(),l("div",Os,[h.analytics?(a(),l("div",Rs,[e("div",Gs,[e("div",Fs,[e("div",qs,[n[2]||(n[2]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-6 w-6 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})])],-1)),e("div",Js,[e("dl",null,[n[1]||(n[1]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Dipendenti Totali ",-1)),e("dd",Qs,m(h.analytics.overview.total_users),1)])])])])]),e("div",Ks,[e("div",Ws,[e("div",Xs,[n[4]||(n[4]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-6 w-6 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})])],-1)),e("div",Ys,[e("dl",null,[n[3]||(n[3]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Dipartimenti ",-1)),e("dd",Zs,m(h.analytics.overview.total_departments),1)])])])])]),e("div",eo,[e("div",to,[e("div",ro,[n[6]||(n[6]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-6 w-6 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"})])],-1)),e("div",so,[e("dl",null,[n[5]||(n[5]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Assunzioni Recenti (90gg) ",-1)),e("dd",oo,m(h.analytics.overview.recent_hires),1)])])])])])])):w("",!0),h.analytics?(a(),l("div",ao,[e("div",lo,[n[7]||(n[7]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Distribuzione per Ruolo",-1)),e("div",no,[(a(!0),l(D,null,V(h.analytics.users_by_role,d=>(a(),l("div",{key:d.role,class:"flex items-center justify-between"},[e("div",io,[e("div",{class:S(["w-3 h-3 rounded-full mr-3",d.role==="admin"?"bg-red-500":d.role==="manager"?"bg-blue-500":"bg-gray-500"])},null,2),e("span",uo,m(p(d.role)),1)]),e("div",mo,[e("span",co,m(d.count),1),e("div",go,[e("div",{class:S(["h-2 rounded-full",d.role==="admin"?"bg-red-500":d.role==="manager"?"bg-blue-500":"bg-gray-500"]),style:Q({width:`${d.count/h.analytics.overview.total_users*100}%`})},null,6)])])]))),128))])]),e("div",po,[n[9]||(n[9]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Distribuzione per Dipartimento",-1)),e("div",vo,[(a(!0),l(D,null,V(h.analytics.users_by_department,d=>(a(),l("div",{key:d.department,class:"flex items-center justify-between"},[e("div",xo,[n[8]||(n[8]=e("div",{class:"w-3 h-3 rounded-full bg-indigo-500 mr-3"},null,-1)),e("span",yo,m(d.department),1)]),e("div",bo,[e("span",fo,m(d.count),1),e("div",ko,[e("div",{class:"h-2 rounded-full bg-indigo-500",style:Q({width:`${d.count/h.analytics.overview.total_users*100}%`})},null,4)])])]))),128))])])])):w("",!0),h.analytics?(a(),l("div",ho,[e("div",wo,[n[11]||(n[11]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Tipi di Contratto",-1)),e("div",_o,[(a(!0),l(D,null,V(h.analytics.employment_types,d=>(a(),l("div",{key:d.type,class:"flex items-center justify-between"},[e("div",$o,[n[10]||(n[10]=e("div",{class:"w-3 h-3 rounded-full bg-green-500 mr-3"},null,-1)),e("span",Co,m(y(d.type)),1)]),e("div",Mo,[e("span",zo,m(d.count),1),e("div",jo,[e("div",{class:"h-2 rounded-full bg-green-500",style:Q({width:`${d.count/h.analytics.overview.total_users*100}%`})},null,4)])])]))),128))])]),e("div",Bo,[n[13]||(n[13]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Stipendio Medio per Dipartimento",-1)),h.analytics.avg_salary_by_department.length>0?(a(),l("div",Eo,[(a(!0),l(D,null,V(h.analytics.avg_salary_by_department,d=>(a(),l("div",{key:d.department,class:"flex items-center justify-between"},[e("span",To,m(d.department),1),e("span",Ho,m(_(d.avg_salary)),1)]))),128))])):(a(),l("div",Do,n[12]||(n[12]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),e("p",{class:"mt-2 text-sm text-gray-500 dark:text-gray-400"},"Nessun dato stipendio disponibile",-1)])))])])):w("",!0),e("div",Vo,[e("button",{onClick:n[0]||(n[0]=d=>v.$emit("refresh")),disabled:h.loading,class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"},[h.loading?(a(),l("svg",Lo,n[14]||(n[14]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):(a(),l("svg",So,n[15]||(n[15]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"},null,-1)]))),n[16]||(n[16]=E(" Aggiorna Dati "))],8,Uo)]),h.loading&&!h.analytics?(a(),l("div",Ao,n[17]||(n[17]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):w("",!0),!h.loading&&!h.analytics?(a(),l("div",Po,n[18]||(n[18]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun dato disponibile",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},' Clicca su "Aggiorna Dati" per caricare le analytics. ',-1)]))):w("",!0)]))}},Io=F(No,[["__scopeId","data-v-ed21a1db"]]),Oo={class:"space-y-6"},Ro={__name:"BulkOperations",emits:["operation-completed"],setup(h,{emit:H}){return(p,y)=>(a(),l("div",Oo,y[0]||(y[0]=[le('<div><h3 class="text-lg font-medium text-gray-900 dark:text-white">Operazioni di Massa</h3><p class="mt-1 text-sm text-gray-500 dark:text-gray-400"> Import/Export dati e operazioni bulk </p></div><div class="grid grid-cols-1 md:grid-cols-2 gap-6"><div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6"><div class="flex items-center mb-4"><svg class="w-6 h-6 text-green-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path></svg><h4 class="text-lg font-medium text-gray-900 dark:text-white">Import Dipendenti</h4></div><p class="text-sm text-gray-500 dark:text-gray-400 mb-4"> Importa dipendenti da file CSV con dati contrattuali completi </p><div class="space-y-3"><button class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"><svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg> Scarica Template CSV </button><button class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700"><svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path></svg> Carica File CSV </button></div></div><div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6"><div class="flex items-center mb-4"><svg class="w-6 h-6 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path></svg><h4 class="text-lg font-medium text-gray-900 dark:text-white">Export Dati</h4></div><p class="text-sm text-gray-500 dark:text-gray-400 mb-4"> Esporta dati del personale in vari formati </p><div class="space-y-3"><button class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"><svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg> Export Completo CSV </button><button class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"><svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg> Export Solo Contatti </button></div></div><div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6"><div class="flex items-center mb-4"><svg class="w-6 h-6 text-purple-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path></svg><h4 class="text-lg font-medium text-gray-900 dark:text-white">Aggiornamenti di Massa</h4></div><p class="text-sm text-gray-500 dark:text-gray-400 mb-4"> Applica modifiche a più dipendenti contemporaneamente </p><div class="space-y-3"><button class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700"><svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path></svg> Assegnazione Dipartimenti </button><button class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"><svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path></svg> Assegnazione Competenze </button></div></div><div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6"><div class="flex items-center mb-4"><svg class="w-6 h-6 text-red-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path></svg><h4 class="text-lg font-medium text-gray-900 dark:text-white">Pulizia Dati</h4></div><p class="text-sm text-gray-500 dark:text-gray-400 mb-4"> Strumenti per la manutenzione e pulizia dei dati </p><div class="space-y-3"><button class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"><svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg> Verifica Integrità Dati </button><button class="w-full inline-flex justify-center items-center px-4 py-2 border border-red-300 dark:border-red-600 shadow-sm text-sm font-medium rounded-md text-red-700 dark:text-red-300 bg-white dark:bg-gray-800 hover:bg-red-50 dark:hover:bg-red-900/20"><svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path></svg> Rimuovi Utenti Inattivi </button></div></div></div><div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4"><div class="flex"><svg class="w-5 h-5 text-yellow-400 mr-3 mt-0.5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg><div><h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">Funzionalità in Sviluppo</h3><p class="text-sm text-yellow-700 dark:text-yellow-300 mt-1"> Le operazioni di massa sono in fase di implementazione. Alcune funzionalità potrebbero non essere ancora disponibili. </p></div></div></div>',3)])))}},Go={class:"flex items-center justify-between pb-4 border-b border-gray-200 dark:border-gray-700"},Fo={class:"mt-6"},qo={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Jo={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Qo=["value"],Ko={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Wo={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Xo={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},Yo={class:"flex items-center space-x-4"},Zo={class:"flex items-center"},ea={key:0,class:"mt-3"},ta={key:0,class:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4"},ra={class:"flex"},sa={class:"text-sm text-red-700 dark:text-red-300 mt-1"},oa={class:"flex items-center justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700"},aa=["disabled"],la={key:0,class:"animate-spin -ml-1 mr-3 h-4 w-4 text-white inline",fill:"none",viewBox:"0 0 24 24"},na={__name:"CreateUserModal",emits:["close","user-created"],setup(h,{emit:H}){const p=H,y=x(!1),_=x(null),v=x([]),n=x(!0),d=x({first_name:"",last_name:"",username:"",email:"",phone:"",position:"",role:"employee",department_id:"",hire_date:"",employment_type:"full_time",work_location:"",weekly_hours:40,probation_end_date:"",contract_end_date:"",salary:null,salary_currency:"EUR",emergency_contact_name:"",emergency_contact_phone:"",emergency_contact_relationship:"",password:""}),$=async()=>{try{const s=await fetch("/api/personnel/departments",{credentials:"include"});if(!s.ok)throw new Error(`HTTP ${s.status}: ${s.statusText}`);const t=await s.json();t.success&&(v.value=t.data.departments||[])}catch(s){console.error("Error loading departments:",s)}},f=async()=>{y.value=!0,_.value=null;try{const s={...d.value};n.value&&delete s.password,Object.keys(s).forEach(b=>{s[b]===""&&(s[b]=null)});const t=await fetch("/api/personnel/admin/users",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(s)});if(!t.ok)throw new Error(`HTTP ${t.status}: ${t.statusText}`);const r=await t.json();if(r.success)p("user-created",r.data.user),n.value&&r.data.temporary_password&&alert(`Utente creato con successo!
Password temporanea: ${r.data.temporary_password}`);else throw new Error(r.message||"Errore nella creazione utente")}catch(s){console.error("Error creating user:",s),_.value=s.message}finally{y.value=!1}};return O(()=>{$();const s=new Date().toISOString().split("T")[0];d.value.hire_date=s}),(s,t)=>(a(),l("div",{class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:t[24]||(t[24]=r=>s.$emit("close"))},[e("div",{class:"relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:t[23]||(t[23]=G(()=>{},["stop"]))},[e("div",Go,[t[26]||(t[26]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Crea Nuovo Dipendente ",-1)),e("button",{onClick:t[0]||(t[0]=r=>s.$emit("close")),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},t[25]||(t[25]=[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),e("div",Fo,[e("form",{onSubmit:G(f,["prevent"]),class:"space-y-6"},[e("div",null,[t[33]||(t[33]=e("h4",{class:"text-md font-medium text-gray-900 dark:text-white mb-4"},"Informazioni Base",-1)),e("div",qo,[e("div",null,[t[27]||(t[27]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Nome * ",-1)),k(e("input",{"onUpdate:modelValue":t[1]||(t[1]=r=>d.value.first_name=r),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[B,d.value.first_name]])]),e("div",null,[t[28]||(t[28]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Cognome * ",-1)),k(e("input",{"onUpdate:modelValue":t[2]||(t[2]=r=>d.value.last_name=r),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[B,d.value.last_name]])]),e("div",null,[t[29]||(t[29]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Username * ",-1)),k(e("input",{"onUpdate:modelValue":t[3]||(t[3]=r=>d.value.username=r),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[B,d.value.username]])]),e("div",null,[t[30]||(t[30]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Email * ",-1)),k(e("input",{"onUpdate:modelValue":t[4]||(t[4]=r=>d.value.email=r),type:"email",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[B,d.value.email]])]),e("div",null,[t[31]||(t[31]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Telefono ",-1)),k(e("input",{"onUpdate:modelValue":t[5]||(t[5]=r=>d.value.phone=r),type:"tel",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[B,d.value.phone]])]),e("div",null,[t[32]||(t[32]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Posizione ",-1)),k(e("input",{"onUpdate:modelValue":t[6]||(t[6]=r=>d.value.position=r),type:"text",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[B,d.value.position]])])])]),e("div",null,[t[38]||(t[38]=e("h4",{class:"text-md font-medium text-gray-900 dark:text-white mb-4"},"Ruolo e Dipartimento",-1)),e("div",Jo,[e("div",null,[t[35]||(t[35]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Ruolo ",-1)),k(e("select",{"onUpdate:modelValue":t[7]||(t[7]=r=>d.value.role=r),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},t[34]||(t[34]=[e("option",{value:"employee"},"Dipendente",-1),e("option",{value:"manager"},"Manager",-1),e("option",{value:"admin"},"Admin",-1)]),512),[[U,d.value.role]])]),e("div",null,[t[37]||(t[37]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Dipartimento ",-1)),k(e("select",{"onUpdate:modelValue":t[8]||(t[8]=r=>d.value.department_id=r),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},[t[36]||(t[36]=e("option",{value:""},"Seleziona dipartimento",-1)),(a(!0),l(D,null,V(v.value,r=>(a(),l("option",{key:r.id,value:r.id},m(r.name),9,Qo))),128))],512),[[U,d.value.department_id]])])])]),e("div",null,[t[47]||(t[47]=e("h4",{class:"text-md font-medium text-gray-900 dark:text-white mb-4"},"Informazioni Contrattuali",-1)),e("div",Ko,[e("div",null,[t[39]||(t[39]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Data Assunzione ",-1)),k(e("input",{"onUpdate:modelValue":t[9]||(t[9]=r=>d.value.hire_date=r),type:"date",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[B,d.value.hire_date]])]),e("div",null,[t[41]||(t[41]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Tipo Contratto ",-1)),k(e("select",{"onUpdate:modelValue":t[10]||(t[10]=r=>d.value.employment_type=r),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},t[40]||(t[40]=[e("option",{value:"full_time"},"Tempo pieno",-1),e("option",{value:"part_time"},"Part-time",-1),e("option",{value:"contractor"},"Consulente",-1),e("option",{value:"intern"},"Stagista",-1)]),512),[[U,d.value.employment_type]])]),e("div",null,[t[43]||(t[43]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Modalità Lavoro ",-1)),k(e("select",{"onUpdate:modelValue":t[11]||(t[11]=r=>d.value.work_location=r),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},t[42]||(t[42]=[e("option",{value:""},"Seleziona modalità",-1),e("option",{value:"office"},"Ufficio",-1),e("option",{value:"remote"},"Remoto",-1),e("option",{value:"hybrid"},"Ibrido",-1)]),512),[[U,d.value.work_location]])]),e("div",null,[t[44]||(t[44]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Ore Settimanali ",-1)),k(e("input",{"onUpdate:modelValue":t[12]||(t[12]=r=>d.value.weekly_hours=r),type:"number",step:"0.5",min:"0",max:"60",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[B,d.value.weekly_hours,void 0,{number:!0}]])]),e("div",null,[t[45]||(t[45]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Fine Periodo Prova ",-1)),k(e("input",{"onUpdate:modelValue":t[13]||(t[13]=r=>d.value.probation_end_date=r),type:"date",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[B,d.value.probation_end_date]])]),e("div",null,[t[46]||(t[46]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Scadenza Contratto ",-1)),k(e("input",{"onUpdate:modelValue":t[14]||(t[14]=r=>d.value.contract_end_date=r),type:"date",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[B,d.value.contract_end_date]])])])]),e("div",null,[t[51]||(t[51]=e("h4",{class:"text-md font-medium text-gray-900 dark:text-white mb-4"},"Informazioni Economiche",-1)),e("div",Wo,[e("div",null,[t[48]||(t[48]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Stipendio Annuo ",-1)),k(e("input",{"onUpdate:modelValue":t[15]||(t[15]=r=>d.value.salary=r),type:"number",step:"100",min:"0",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[B,d.value.salary,void 0,{number:!0}]])]),e("div",null,[t[50]||(t[50]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Valuta ",-1)),k(e("select",{"onUpdate:modelValue":t[16]||(t[16]=r=>d.value.salary_currency=r),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},t[49]||(t[49]=[e("option",{value:"EUR"},"EUR",-1),e("option",{value:"USD"},"USD",-1),e("option",{value:"GBP"},"GBP",-1)]),512),[[U,d.value.salary_currency]])])])]),e("div",null,[t[55]||(t[55]=e("h4",{class:"text-md font-medium text-gray-900 dark:text-white mb-4"},"Contatto di Emergenza",-1)),e("div",Xo,[e("div",null,[t[52]||(t[52]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Nome Contatto ",-1)),k(e("input",{"onUpdate:modelValue":t[17]||(t[17]=r=>d.value.emergency_contact_name=r),type:"text",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[B,d.value.emergency_contact_name]])]),e("div",null,[t[53]||(t[53]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Telefono Emergenza ",-1)),k(e("input",{"onUpdate:modelValue":t[18]||(t[18]=r=>d.value.emergency_contact_phone=r),type:"tel",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[B,d.value.emergency_contact_phone]])]),e("div",null,[t[54]||(t[54]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Relazione ",-1)),k(e("input",{"onUpdate:modelValue":t[19]||(t[19]=r=>d.value.emergency_contact_relationship=r),type:"text",placeholder:"es. Coniuge, Genitore...",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[B,d.value.emergency_contact_relationship]])])])]),e("div",null,[t[58]||(t[58]=e("h4",{class:"text-md font-medium text-gray-900 dark:text-white mb-4"},"Password",-1)),e("div",Yo,[e("label",Zo,[k(e("input",{"onUpdate:modelValue":t[20]||(t[20]=r=>n.value=r),type:"checkbox",class:"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"},null,512),[[ne,n.value]]),t[56]||(t[56]=e("span",{class:"ml-2 text-sm text-gray-700 dark:text-gray-300"}," Genera password temporanea automaticamente ",-1))])]),n.value?w("",!0):(a(),l("div",ea,[t[57]||(t[57]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Password Personalizzata ",-1)),k(e("input",{"onUpdate:modelValue":t[21]||(t[21]=r=>d.value.password=r),type:"password",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"},null,512),[[B,d.value.password]])]))]),_.value?(a(),l("div",ta,[e("div",ra,[t[60]||(t[60]=e("svg",{class:"w-5 h-5 text-red-400 mr-2 mt-0.5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z","clip-rule":"evenodd"})],-1)),e("div",null,[t[59]||(t[59]=e("h3",{class:"text-sm font-medium text-red-800 dark:text-red-200"},"Errore nella creazione",-1)),e("p",sa,m(_.value),1)])])])):w("",!0),e("div",oa,[e("button",{type:"button",onClick:t[22]||(t[22]=r=>s.$emit("close")),class:"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"}," Annulla "),e("button",{type:"submit",disabled:y.value,class:"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"},[y.value?(a(),l("svg",la,t[61]||(t[61]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):w("",!0),E(" "+m(y.value?"Creazione...":"Crea Dipendente"),1)],8,aa)])],32)])])]))}},da=F(na,[["__scopeId","data-v-86e643b3"]]),ia={class:"space-y-6"},ua={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},ma={class:"mt-4 sm:mt-0 flex items-center space-x-3"},ca=["disabled"],ga={key:0,class:"space-y-4"},pa={key:0,class:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4"},va={class:"flex"},xa={class:"flex-1"},ya={class:"mt-2 text-sm text-red-700 dark:text-red-300"},ba={class:"mb-2"},fa={class:"list-disc list-inside space-y-1"},ka={key:0,class:"mt-2 text-xs"},ha={key:1,class:"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4"},wa={class:"flex"},_a={class:"flex-1"},$a={class:"mt-2 text-sm text-yellow-700 dark:text-yellow-300"},Ca={class:"mb-2"},Ma={class:"list-disc list-inside space-y-1"},za={key:0,class:"mt-2 text-xs"},ja={class:"border-b border-gray-200 dark:border-gray-700"},Ba={class:"-mb-px flex space-x-8"},Ea=["onClick"],Ta={key:0,class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},Ha={key:1,class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},Da={key:2,class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},Va={key:3,class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},Ua={key:4,class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},La={class:"mt-6"},Sa={key:0},Aa={key:1},Pa={key:2},Na={key:3},Ia={key:4},Oa={__name:"PersonnelAdmin",setup(h){const H=x(!1),p=x(null),y=x("users"),_=x(!1),v=x([{id:"users",name:"Gestione Utenti"},{id:"departments",name:"Dipartimenti"},{id:"skills",name:"Competenze"},{id:"analytics",name:"Analytics"},{id:"bulk",name:"Operazioni Bulk"}]),n=async()=>{H.value=!0;try{const s=await fetch("/api/personnel/admin/analytics",{credentials:"include"});if(!s.ok)throw new Error(`HTTP ${s.status}: ${s.statusText}`);const t=await s.json();if(t.success)p.value=t.data;else throw new Error(t.message||"Errore nel caricamento analytics")}catch(s){console.error("Error loading analytics:",s)}finally{H.value=!1}},d=async()=>{try{const s=await fetch("/api/personnel/export",{credentials:"include"});if(!s.ok)throw new Error(`HTTP ${s.status}: ${s.statusText}`);const t=await s.blob(),r=window.URL.createObjectURL(t),b=document.createElement("a");b.href=r,b.download=`personnel-export-${new Date().toISOString().split("T")[0]}.csv`,document.body.appendChild(b),b.click(),document.body.removeChild(b),window.URL.revokeObjectURL(r)}catch(s){console.error("Error exporting data:",s)}},$=s=>{_.value=!1,n()},f=s=>s?new Date(s).toLocaleDateString("it-IT",{year:"numeric",month:"short",day:"numeric"}):"";return O(()=>{n()}),(s,t)=>(a(),l("div",ia,[e("div",ua,[t[4]||(t[4]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white flex items-center"},[e("svg",{class:"w-8 h-8 mr-3 text-purple-600",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z","clip-rule":"evenodd"})]),E(" Amministrazione Personnel ")]),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"}," Gestione completa del personale e dati contrattuali ")],-1)),e("div",ma,[e("button",{onClick:t[0]||(t[0]=r=>_.value=!0),class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},t[2]||(t[2]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),E(" Nuovo Dipendente ")])),e("button",{onClick:d,disabled:H.value,class:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"},t[3]||(t[3]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),E(" Esporta Dati ")]),8,ca)])]),p.value&&(p.value.alerts.expiring_contracts.length>0||p.value.alerts.ending_probation.length>0)?(a(),l("div",ga,[p.value.alerts.expiring_contracts.length>0?(a(),l("div",pa,[e("div",va,[t[6]||(t[6]=e("svg",{class:"w-5 h-5 text-red-400 mr-3 mt-0.5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z","clip-rule":"evenodd"})],-1)),e("div",xa,[t[5]||(t[5]=e("h3",{class:"text-sm font-medium text-red-800 dark:text-red-200"},"Contratti in Scadenza",-1)),e("div",ya,[e("p",ba,m(p.value.alerts.expiring_contracts.length)+" contratti scadranno nei prossimi 90 giorni:",1),e("ul",fa,[(a(!0),l(D,null,V(p.value.alerts.expiring_contracts.slice(0,3),r=>(a(),l("li",{key:r.user_id},[e("strong",null,m(r.full_name),1),E(" - "+m(f(r.contract_end_date))+" ("+m(r.days_remaining)+" giorni) ",1)]))),128))]),p.value.alerts.expiring_contracts.length>3?(a(),l("p",ka," +"+m(p.value.alerts.expiring_contracts.length-3)+" altri contratti ",1)):w("",!0)])])])])):w("",!0),p.value.alerts.ending_probation.length>0?(a(),l("div",ha,[e("div",wa,[t[8]||(t[8]=e("svg",{class:"w-5 h-5 text-yellow-400 mr-3 mt-0.5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z","clip-rule":"evenodd"})],-1)),e("div",_a,[t[7]||(t[7]=e("h3",{class:"text-sm font-medium text-yellow-800 dark:text-yellow-200"},"Periodi di Prova in Scadenza",-1)),e("div",$a,[e("p",Ca,m(p.value.alerts.ending_probation.length)+" periodi di prova termineranno nei prossimi 30 giorni:",1),e("ul",Ma,[(a(!0),l(D,null,V(p.value.alerts.ending_probation.slice(0,3),r=>(a(),l("li",{key:r.user_id},[e("strong",null,m(r.full_name),1),E(" - "+m(f(r.probation_end_date))+" ("+m(r.days_remaining)+" giorni) ",1)]))),128))]),p.value.alerts.ending_probation.length>3?(a(),l("p",za," +"+m(p.value.alerts.ending_probation.length-3)+" altri periodi di prova ",1)):w("",!0)])])])])):w("",!0)])):w("",!0),e("div",ja,[e("nav",Ba,[(a(!0),l(D,null,V(v.value,r=>(a(),l("button",{key:r.id,onClick:b=>y.value=r.id,class:S(["py-2 px-1 border-b-2 font-medium text-sm flex items-center",y.value===r.id?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600"])},[r.id==="users"?(a(),l("svg",Ta,t[9]||(t[9]=[e("path",{d:"M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"},null,-1)]))):r.id==="departments"?(a(),l("svg",Ha,t[10]||(t[10]=[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z","clip-rule":"evenodd"},null,-1)]))):r.id==="skills"?(a(),l("svg",Da,t[11]||(t[11]=[e("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"},null,-1)]))):r.id==="analytics"?(a(),l("svg",Va,t[12]||(t[12]=[e("path",{d:"M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"},null,-1)]))):(a(),l("svg",Ua,t[13]||(t[13]=[e("path",{"fill-rule":"evenodd",d:"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z","clip-rule":"evenodd"},null,-1)]))),E(" "+m(r.name),1)],10,Ea))),128))])]),e("div",La,[y.value==="users"?(a(),l("div",Sa,[I(ct,{onUserCreated:n,onUserUpdated:n,onUserDeleted:n})])):y.value==="departments"?(a(),l("div",Aa,[I(Dr,{onDepartmentCreated:n,onDepartmentUpdated:n,onDepartmentDeleted:n})])):y.value==="skills"?(a(),l("div",Pa,[I(Is,{onSkillCreated:n,onSkillUpdated:n,onSkillDeleted:n})])):y.value==="analytics"?(a(),l("div",Na,[I(Io,{analytics:p.value,loading:H.value,onRefresh:n},null,8,["analytics","loading"])])):y.value==="bulk"?(a(),l("div",Ia,[I(Ro,{onOperationCompleted:n})])):w("",!0)]),_.value?(a(),X(da,{key:1,onClose:t[1]||(t[1]=r=>_.value=!1),onUserCreated:$})):w("",!0)]))}},Fa=F(Oa,[["__scopeId","data-v-2701ad16"]]);export{Fa as default};

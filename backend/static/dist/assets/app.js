const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/ProjectCreate.js","assets/vendor.js","assets/ProjectView.js","assets/ProjectView.css","assets/ProjectEdit.js","assets/PersonnelDirectory.js","assets/personnel.js","assets/PersonnelOrgChart.js","assets/SkillsMatrix.js","assets/DepartmentList.js","assets/DepartmentCreate.js","assets/DepartmentView.js","assets/DepartmentEdit.js","assets/PersonnelAdmin.js","assets/PersonnelProfile.js","assets/Admin.js","assets/KPITemplates.js","assets/Profile.js","assets/Settings.js"])))=>i.map(i=>d[i]);
import{r as C,w as ne,c as i,a as $,b as N,o as s,d as Ve,e as ue,f,g as k,n as S,h as q,i as P,t as c,u as me,j as e,F as U,k as F,l as te,m as E,p as O,q as Pe,s as pe,v as W,x as ee,y as re,z as ve,A as Y,T as De,B as Le,C as Te,D as Se,E as de,G as Be,H as ce,I as He,J as qe,K as Re,L as Ne,M as Oe}from"./vendor.js";(function(){const o=document.createElement("link").relList;if(o&&o.supports&&o.supports("modulepreload"))return;for(const a of document.querySelectorAll('link[rel="modulepreload"]'))t(a);new MutationObserver(a=>{for(const m of a)if(m.type==="childList")for(const u of m.addedNodes)u.tagName==="LINK"&&u.rel==="modulepreload"&&t(u)}).observe(document,{childList:!0,subtree:!0});function l(a){const m={};return a.integrity&&(m.integrity=a.integrity),a.referrerPolicy&&(m.referrerPolicy=a.referrerPolicy),a.crossOrigin==="use-credentials"?m.credentials="include":a.crossOrigin==="anonymous"?m.credentials="omit":m.credentials="same-origin",m}function t(a){if(a.ep)return;a.ep=!0;const m=l(a);fetch(a.href,m)}})();const Q=C(!1);let we=!1;const Ae=r=>{r?(document.documentElement.classList.add("dark"),localStorage.setItem("darkMode","true")):(document.documentElement.classList.remove("dark"),localStorage.setItem("darkMode","false"))},Ue=()=>{we||(ne(Q,r=>{Ae(r)}),we=!0)};function ge(){return Ue(),{isDarkMode:Q,toggleDarkMode:()=>{Q.value=!Q.value},setDarkMode:t=>{Q.value=t},initializeDarkMode:()=>{const t=localStorage.getItem("darkMode"),a=document.documentElement.classList.contains("dark");if(t==="true")Q.value=!0;else if(t==="false")Q.value=!1;else{const v=window.matchMedia("(prefers-color-scheme: dark)").matches;Q.value=a||v}Ae(Q.value);const m=window.matchMedia("(prefers-color-scheme: dark)"),u=v=>{const p=localStorage.getItem("darkMode");(!p||p==="null")&&(Q.value=v.matches)};m.addEventListener("change",u)}}}const Fe={id:"app"},Ke={__name:"App",setup(r){const{initializeDarkMode:o}=ge();return o(),(l,t)=>{const a=N("router-view");return s(),i("div",Fe,[$(a)])}}},We="modulepreload",Ge=function(r){return"/"+r},$e={},R=function(o,l,t){let a=Promise.resolve();if(l&&l.length>0){document.getElementsByTagName("link");const u=document.querySelector("meta[property=csp-nonce]"),v=(u==null?void 0:u.nonce)||(u==null?void 0:u.getAttribute("nonce"));a=Promise.allSettled(l.map(p=>{if(p=Ge(p),p in $e)return;$e[p]=!0;const d=p.endsWith(".css"),n=d?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${p}"]${n}`))return;const g=document.createElement("link");if(g.rel=d?"stylesheet":We,d||(g.as="script"),g.crossOrigin="",g.href=p,v&&g.setAttribute("nonce",v),document.head.appendChild(g),d)return new Promise((x,_)=>{g.addEventListener("load",x),g.addEventListener("error",()=>_(new Error(`Unable to preload CSS for ${p}`)))})}))}function m(u){const v=new Event("vite:preloadError",{cancelable:!0});if(v.payload=u,window.dispatchEvent(v),!v.defaultPrevented)throw u}return a.then(u=>{for(const v of u||[])v.status==="rejected"&&m(v.reason);return o().catch(m)})},K=Ve.create({baseURL:"",timeout:1e4,withCredentials:!0,headers:{"Content-Type":"application/json"}});K.interceptors.request.use(r=>{var l,t;const o=(l=document.querySelector('meta[name="csrf-token"]'))==null?void 0:l.getAttribute("content");return o&&["post","put","patch","delete"].includes((t=r.method)==null?void 0:t.toLowerCase())&&(r.headers["X-CSRFToken"]=o),r},r=>Promise.reject(r));K.interceptors.response.use(r=>r,r=>{var o;return((o=r.response)==null?void 0:o.status)===401&&(localStorage.removeItem("user"),console.warn("Sessione scaduta, autenticazione richiesta")),Promise.reject(r)});const Z=ue("auth",()=>{const r=localStorage.getItem("user"),o=C(r?JSON.parse(r):null),l=C(!1),t=C(null),a=C(!1),m=f(()=>!!o.value&&a.value),u={admin:["admin","manage_users","assign_roles","view_all_projects","create_project","edit_project","delete_project","assign_to_project","manage_project_tasks","manage_project_resources","approve_timesheets","view_personnel_data","edit_personnel_data","view_contracts","manage_contracts","view_crm","manage_clients","manage_proposals","view_reports","view_dashboard","submit_timesheet","view_own_timesheets","view_funding","manage_funding","view_products","manage_products","view_performance","manage_performance","view_communications","manage_communications","view_startup","manage_startup"],manager:["view_dashboard","view_all_projects","edit_project","assign_to_project","manage_project_tasks","manage_project_resources","approve_timesheets","view_personnel_data","view_crm","view_reports","submit_timesheet","view_own_timesheets","manage_clients","manage_proposals","view_funding","manage_funding","view_products","manage_products","view_performance","manage_performance","view_communications","manage_communications","view_startup","manage_startup"],employee:["view_dashboard","view_own_timesheets","submit_timesheet"],sales:["view_dashboard","view_crm","manage_clients","manage_proposals","submit_timesheet","view_own_timesheets","view_reports","view_funding","view_products","manage_products"],human_resources:["view_dashboard","manage_users","view_personnel_data","edit_personnel_data","view_contracts","manage_contracts","submit_timesheet","view_own_timesheets","view_reports","view_funding","manage_funding","view_performance","manage_performance","view_communications","manage_communications","view_startup","manage_startup"]},v=z=>!o.value||!o.value.role?!1:o.value.role==="admin"?!0:(u[o.value.role]||[]).includes(z),p=()=>{var z,A;console.log("Current user:",o.value),console.log("User role:",(z=o.value)==null?void 0:z.role),console.log("Has admin permission:",v("admin")),console.log("Available permissions for role:",u[(A=o.value)==null?void 0:A.role])};async function d(z){var A,j;l.value=!0,t.value=null;try{const b=await K.post("/api/auth/login",z);return b.data.success?(o.value=b.data.data.user,localStorage.setItem("user",JSON.stringify(o.value)),a.value=!0,{success:!0}):(t.value=b.data.message||"Errore durante il login",{success:!1,error:t.value})}catch(b){return t.value=((j=(A=b.response)==null?void 0:A.data)==null?void 0:j.message)||"Errore di connessione",{success:!1,error:t.value}}finally{l.value=!1}}async function n(z){var A,j;l.value=!0,t.value=null;try{const b=await K.post("/api/auth/register",z);return b.data.success?{success:!0,message:b.data.message}:(t.value=b.data.message||"Errore durante la registrazione",{success:!1,error:t.value})}catch(b){return t.value=((j=(A=b.response)==null?void 0:A.data)==null?void 0:j.message)||"Errore di connessione",{success:!1,error:t.value}}finally{l.value=!1}}async function g(){try{await K.post("/api/auth/logout")}catch(z){console.warn("Errore durante il logout:",z)}finally{o.value=null,a.value=!1,localStorage.removeItem("user")}}async function x(){if(a.value)return m.value;try{const z=await K.get("/api/auth/me");return z.data.success?(o.value=z.data.data.user,localStorage.setItem("user",JSON.stringify(o.value)),a.value=!0,!0):(await g(),!1)}catch{return await g(),!1}}async function _(){return o.value?await x():(a.value=!0,!1)}return{user:o,loading:l,error:t,sessionChecked:a,isAuthenticated:m,hasPermission:v,debugPermissions:p,login:d,register:n,logout:g,checkAuth:x,initializeAuth:_}}),se=ue("tenant",()=>{const r=C(null),o=C(!1),l=C(null),t=f(()=>{var n;return((n=r.value)==null?void 0:n.company)||{}}),a=f(()=>{var n;return((n=r.value)==null?void 0:n.contact)||{}}),m=f(()=>{var n;return((n=r.value)==null?void 0:n.pages)||{}}),u=f(()=>{var n;return((n=r.value)==null?void 0:n.navigation)||{}}),v=f(()=>{var n;return((n=r.value)==null?void 0:n.footer)||{}});async function p(){try{if(o.value=!0,window.TENANT_CONFIG){r.value=window.TENANT_CONFIG;return}const n=await fetch("/api/config/tenant");r.value=await n.json()}catch(n){l.value="Errore nel caricamento della configurazione",console.error("Errore caricamento tenant config:",n)}finally{o.value=!1}}function d(n,g={}){if(!n||typeof n!="string")return n;let x=n;const _={"company.name":t.value.name||"DatVinci","company.tagline":t.value.tagline||"","company.description":t.value.description||"","company.mission":t.value.mission||"","company.vision":t.value.vision||"","company.founded":t.value.founded||"","company.team_size":t.value.team_size||"","contact.email":a.value.email||"","contact.phone":a.value.phone||"","contact.address":a.value.address||"",current_year:new Date().getFullYear().toString(),...g};for(const[z,A]of Object.entries(_)){const j=new RegExp(`\\{${z}\\}`,"g");x=x.replace(j,A||"")}return x}return{config:r,loading:o,error:l,company:t,contact:a,pages:m,navigation:u,footer:v,loadConfig:p,interpolateText:d}});function Qe(){const r=Z(),o=f(()=>_=>r.hasPermission(_)),l=f(()=>{var _;return((_=r.user)==null?void 0:_.role)||null}),t=f(()=>l.value==="admin"),a=f(()=>l.value==="manager"),m=f(()=>l.value==="employee"),u=f(()=>l.value==="sales"),v=f(()=>l.value==="human_resources"),p=f(()=>o.value("create_project")||o.value("edit_project")||o.value("delete_project")),d=f(()=>o.value("manage_users")||o.value("assign_roles")),n=f(()=>o.value("view_all_projects")),g=f(()=>o.value("view_personnel_data")||o.value("edit_personnel_data")),x=f(()=>o.value("approve_timesheets"));return{hasPermission:o,userRole:l,isAdmin:t,isManager:a,isEmployee:m,isSales:u,isHR:v,canManageProjects:p,canManageUsers:d,canViewAllProjects:n,canManagePersonnel:g,canApproveTimesheets:x}}const Je={key:0,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"},Ye={key:1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"},Xe={key:2,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"},Ze={key:3,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"},et={key:4,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"},tt={key:5,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"},st={key:6,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"},ot={key:7,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"},rt={key:8,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"},at={key:9,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"},nt={key:10,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"},it={key:11,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"},lt={key:12,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"},dt={key:13,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"},ct={key:14,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"},ut={key:15,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"},mt={key:16,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"},pt={key:17,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"},vt={key:18,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"},gt={key:19,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"},ie={__name:"SidebarIcon",props:{icon:{type:String,required:!0},className:{type:String,default:"h-5 w-5"}},setup(r){return(o,l)=>(s(),i("svg",{class:S(r.className),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[r.icon==="dashboard"?(s(),i("path",Je)):r.icon==="projects"?(s(),i("path",Ye)):r.icon==="users"?(s(),i("path",Xe)):r.icon==="clients"?(s(),i("path",Ze)):r.icon==="products"?(s(),i("path",et)):r.icon==="reports"?(s(),i("path",tt)):r.icon==="settings"?(s(),i("path",st)):k("",!0),r.icon==="settings"?(s(),i("path",ot)):r.icon==="user-management"?(s(),i("path",rt)):r.icon==="communications"?(s(),i("path",at)):r.icon==="funding"?(s(),i("path",nt)):r.icon==="reporting"?(s(),i("path",it)):r.icon==="team"?(s(),i("path",lt)):r.icon==="directory"?(s(),i("path",dt)):r.icon==="orgchart"?(s(),i("path",ct)):r.icon==="skills"?(s(),i("path",ut)):r.icon==="departments"?(s(),i("path",mt)):r.icon==="admin"?(s(),i("path",pt)):r.icon==="user-profile"?(s(),i("path",vt)):(s(),i("path",gt))],2))}},ht={key:0,class:"truncate"},ft={key:0,class:"truncate"},X={__name:"SidebarNavItem",props:{item:{type:Object,required:!0},isCollapsed:{type:Boolean,default:!1}},emits:["click"],setup(r){const o=f(()=>["text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-primary-600 dark:hover:text-primary-400"]);return(l,t)=>{const a=N("router-link");return s(),i("div",null,[r.item.path!=="#"?(s(),q(a,{key:0,to:r.item.path,class:S(["group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150",[o.value,{"justify-center":r.isCollapsed}]]),"active-class":"text-primary-600 bg-primary-50 border-r-2 border-primary-600",onClick:t[0]||(t[0]=m=>l.$emit("click"))},{default:P(()=>[$(ie,{icon:r.item.icon,class:S(["flex-shrink-0 h-6 w-6",{"mr-0":r.isCollapsed,"mr-3":!r.isCollapsed}])},null,8,["icon","class"]),r.isCollapsed?k("",!0):(s(),i("span",ht,c(r.item.name),1))]),_:1},8,["to","class"])):(s(),i("div",{key:1,class:S(["group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150 cursor-not-allowed opacity-75",["text-gray-400 hover:text-gray-500",{"justify-center":r.isCollapsed}]])},[$(ie,{icon:r.item.icon,class:S(["flex-shrink-0 h-6 w-6",{"mr-0":r.isCollapsed,"mr-3":!r.isCollapsed}])},null,8,["icon","class"]),r.isCollapsed?k("",!0):(s(),i("span",ft,c(r.item.name),1))],2))])}}},xt={key:0,class:"flex-1 text-left truncate"},yt={key:0,class:"ml-6 space-y-1 mt-1"},_t={class:"truncate"},Ce={__name:"SidebarNavItemCollapsible",props:{item:{type:Object,required:!0},isCollapsed:{type:Boolean,default:!1}},emits:["click"],setup(r){const o=r,l=me(),t=Z(),a=C(!1),m=f(()=>["text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-primary-600 dark:hover:text-primary-400",{"text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900":u.value}]),u=f(()=>o.item.children?o.item.children.some(n=>n.path!=="#"&&l.path.startsWith(n.path)):!1),v=f(()=>o.item.children?o.item.children.filter(n=>{var g;return n.admin?((g=t.user)==null?void 0:g.role)==="admin":!0}):[]);u.value&&(a.value=!0);function p(){o.isCollapsed||(a.value=!a.value)}function d(n){if(n.path==="#")return!1}return(n,g)=>{const x=N("router-link");return s(),i("div",null,[e("button",{onClick:p,class:S(["group w-full flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150",[m.value,{"justify-center":r.isCollapsed}]])},[$(ie,{icon:r.item.icon,class:S(["flex-shrink-0 h-6 w-6",{"mr-0":r.isCollapsed,"mr-3":!r.isCollapsed}])},null,8,["icon","class"]),r.isCollapsed?k("",!0):(s(),i("span",xt,c(r.item.name),1)),r.isCollapsed?k("",!0):(s(),i("svg",{key:1,class:S([{"rotate-90":a.value},"ml-2 h-4 w-4 transition-transform duration-150"]),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},g[0]||(g[0]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"},null,-1)]),2))],2),a.value&&!r.isCollapsed?(s(),i("div",yt,[(s(!0),i(U,null,F(v.value,_=>(s(),q(x,{key:_.name,to:_.path,class:S(["group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150",_.path==="#"?"text-gray-400 dark:text-gray-500 hover:text-gray-500 dark:hover:text-gray-400 cursor-not-allowed opacity-75":"text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-primary-600 dark:hover:text-primary-400"]),"active-class":"text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900",onClick:z=>d(_)},{default:P(()=>[_.icon?(s(),q(ie,{key:0,icon:_.icon,class:"flex-shrink-0 h-4 w-4 mr-2"},null,8,["icon"])):k("",!0),e("span",_t,c(_.name),1)]),_:2},1032,["to","class","onClick"]))),128))])):k("",!0)])}}},kt={class:"mt-5 flex-grow flex flex-col overflow-hidden"},bt={class:"flex-1 px-2 space-y-1"},je={__name:"SidebarNavigation",props:{isCollapsed:{type:Boolean,default:!1}},emits:["item-click"],setup(r){const{hasPermission:o}=Qe(),l=f(()=>o.value("view_dashboard")),t=f(()=>o.value("view_personnel_data")),a=f(()=>o.value("view_all_projects")),m=f(()=>o.value("view_crm")),u=f(()=>o.value("view_products")),v=f(()=>o.value("view_performance")),p=f(()=>o.value("view_communications")),d=f(()=>o.value("view_funding")),n=f(()=>o.value("view_reports")),g=f(()=>o.value("admin_access"));return(x,_)=>(s(),i("div",kt,[e("nav",bt,[l.value?(s(),q(X,{key:0,item:{name:"Dashboard",path:"/app/dashboard",icon:"dashboard"},"is-collapsed":r.isCollapsed,onClick:_[0]||(_[0]=z=>x.$emit("item-click"))},null,8,["is-collapsed"])):k("",!0),t.value?(s(),q(Ce,{key:1,item:{name:"Personale",icon:"users",children:[{name:"Team",path:"/app/personnel",icon:"team"},{name:"Directory",path:"/app/personnel/directory",icon:"directory"},{name:"Organigramma",path:"/app/personnel/orgchart",icon:"orgchart"},{name:"Competenze",path:"/app/personnel/skills",icon:"skills"},{name:"Dipartimenti",path:"/app/personnel/departments",icon:"departments",admin:!0},{name:"Amministrazione",path:"/app/personnel/admin",icon:"admin",admin:!0}]},"is-collapsed":r.isCollapsed,onClick:_[1]||(_[1]=z=>x.$emit("item-click"))},null,8,["is-collapsed"])):k("",!0),a.value?(s(),q(X,{key:2,item:{name:"Progetti",path:"/app/projects",icon:"projects"},"is-collapsed":r.isCollapsed,onClick:_[2]||(_[2]=z=>x.$emit("item-click"))},null,8,["is-collapsed"])):k("",!0),m.value?(s(),q(X,{key:3,item:{name:"CRM",path:"#",icon:"clients"},"is-collapsed":r.isCollapsed,onClick:_[3]||(_[3]=z=>x.$emit("item-click"))},null,8,["is-collapsed"])):k("",!0),u.value?(s(),q(X,{key:4,item:{name:"Prodotti",path:"#",icon:"products"},"is-collapsed":r.isCollapsed,onClick:_[4]||(_[4]=z=>x.$emit("item-click"))},null,8,["is-collapsed"])):k("",!0),v.value?(s(),q(X,{key:5,item:{name:"Performance",path:"#",icon:"reports"},"is-collapsed":r.isCollapsed,onClick:_[5]||(_[5]=z=>x.$emit("item-click"))},null,8,["is-collapsed"])):k("",!0),p.value?(s(),q(X,{key:6,item:{name:"Comunicazione",path:"#",icon:"communications"},"is-collapsed":r.isCollapsed,onClick:_[6]||(_[6]=z=>x.$emit("item-click"))},null,8,["is-collapsed"])):k("",!0),d.value?(s(),q(X,{key:7,item:{name:"Finanziamenti",path:"#",icon:"funding"},"is-collapsed":r.isCollapsed,onClick:_[7]||(_[7]=z=>x.$emit("item-click"))},null,8,["is-collapsed"])):k("",!0),n.value?(s(),q(X,{key:8,item:{name:"Rendicontazione",path:"#",icon:"reporting"},"is-collapsed":r.isCollapsed,onClick:_[8]||(_[8]=z=>x.$emit("item-click"))},null,8,["is-collapsed"])):k("",!0),g.value?(s(),q(Ce,{key:9,item:{name:"Amministrazione",icon:"settings",children:[{name:"Gestione Utenti",path:"/app/admin/users",icon:"user-management"},{name:"Template KPI",path:"/app/admin/kpi-templates",icon:"reports"}]},"is-collapsed":r.isCollapsed,onClick:_[9]||(_[9]=z=>x.$emit("item-click"))},null,8,["is-collapsed"])):k("",!0)])]))}},wt={class:"flex-shrink-0 border-t border-gray-200 p-4"},$t={class:"flex-shrink-0"},Ct={class:"h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center"},jt={class:"text-sm font-medium text-primary-700"},Mt={key:0,class:"ml-3 flex-1 min-w-0"},zt={class:"text-sm font-medium text-gray-900 truncate"},Pt={class:"text-xs text-gray-500 truncate"},St={class:"py-1"},At={key:0,class:"mt-3 text-xs text-gray-400 text-center"},Me={__name:"SidebarFooter",props:{isCollapsed:{type:Boolean,default:!1}},setup(r){const o=te(),l=Z(),t=C(!1),a=f(()=>l.user&&(l.user.name||l.user.username)||"Utente"),m=f(()=>l.user?a.value.charAt(0).toUpperCase():"U"),u=f(()=>l.user?{admin:"Amministratore",manager:"Manager",employee:"Dipendente",client:"Cliente"}[l.user.role]||l.user.role:""),v=f(()=>"1.0.0");async function p(){t.value=!1,await l.logout(),o.push("/auth/login")}return(d,n)=>{const g=N("router-link");return s(),i("div",wt,[e("div",{class:S(["flex items-center",{"justify-center":r.isCollapsed}])},[e("div",$t,[e("div",Ct,[e("span",jt,c(m.value),1)])]),r.isCollapsed?k("",!0):(s(),i("div",Mt,[e("p",zt,c(a.value),1),e("p",Pt,c(u.value),1)])),e("div",{class:S(["relative",{"ml-3":!r.isCollapsed}])},[e("button",{onClick:n[0]||(n[0]=x=>t.value=!t.value),class:"p-1 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"},n[4]||(n[4]=[e("svg",{class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zM13 12a1 1 0 11-2 0 1 1 0 012 0zM20 12a1 1 0 11-2 0 1 1 0 012 0z"})],-1)])),t.value?(s(),i("div",{key:0,onClick:n[3]||(n[3]=x=>t.value=!1),class:"origin-bottom-left fixed bottom-16 left-4 w-48 rounded-md shadow-xl bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 border border-gray-200 dark:border-gray-600",style:{"z-index":"99999"}},[e("div",St,[$(g,{to:"/app/profile",class:"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",onClick:n[1]||(n[1]=x=>t.value=!1)},{default:P(()=>n[5]||(n[5]=[E(" Il tuo profilo ")])),_:1,__:[5]}),$(g,{to:"/app/settings",class:"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",onClick:n[2]||(n[2]=x=>t.value=!1)},{default:P(()=>n[6]||(n[6]=[E(" Impostazioni ")])),_:1,__:[6]}),n[7]||(n[7]=e("hr",{class:"my-1 border-gray-200 dark:border-gray-600"},null,-1)),e("button",{onClick:p,class:"block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"}," Esci ")])])):k("",!0)],2)],2),v.value&&!r.isCollapsed?(s(),i("div",At," v"+c(v.value),1)):k("",!0)])}}},Et={class:"flex"},It={class:"hidden lg:flex lg:flex-shrink-0 lg:fixed lg:inset-y-0 z-10"},Vt={class:"flex flex-col flex-grow pt-5 pb-4 overflow-y-auto bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 shadow-sm"},Dt={class:"flex items-center flex-shrink-0 px-4"},Lt={class:"w-10 h-10 bg-primary-600 rounded flex items-center justify-center mr-3"},Tt={class:"text-white font-bold text-lg"},Bt={class:"text-xl font-semibold text-gray-900 dark:text-white"},Ht={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center"},qt={class:"text-white font-bold text-sm"},Rt={class:"h-5 w-5",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},Nt=["d"],Ot={class:"flex flex-col h-full pt-5 pb-4 overflow-y-auto bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 shadow-sm"},Ut={class:"flex items-center justify-between px-4 mb-4"},Ft={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center mr-3"},Kt={class:"text-white font-bold text-sm"},Wt={class:"text-xl font-semibold text-gray-900 dark:text-white"},Gt={__name:"AppSidebar",props:{isMobileOpen:{type:Boolean,default:!1}},emits:["close","toggle-collapsed"],setup(r,{emit:o}){const l=o,t=se(),a=C(!1),m=f(()=>t.config||{}),u=f(()=>{var n;return((n=m.value.company)==null?void 0:n.name)||"DatPortal"}),v=f(()=>u.value.split(" ").map(g=>g[0]).join("").toUpperCase().slice(0,2));function p(){a.value=!a.value,l("toggle-collapsed",a.value)}function d(){a.value&&(a.value=!1)}return(n,g)=>{const x=N("router-link");return s(),i("div",Et,[e("div",It,[e("div",{class:S(["flex flex-col transition-all duration-300",[a.value?"w-20":"w-64"]])},[e("div",Vt,[e("div",Dt,[e("div",{class:S(["flex items-center",{"justify-center":a.value}])},[$(x,{to:"/app/dashboard",class:S(["flex items-center",{hidden:a.value}])},{default:P(()=>[e("div",Lt,[e("span",Tt,c(v.value),1)]),e("h3",Bt,c(u.value),1)]),_:1},8,["class"]),$(x,{to:"/app/dashboard",class:S(["flex items-center justify-center",{hidden:!a.value}])},{default:P(()=>[e("div",Ht,[e("span",qt,c(v.value),1)])]),_:1},8,["class"])],2),e("button",{onClick:p,class:"ml-auto text-gray-600 dark:text-gray-400 focus:outline-none hover:bg-gray-100 dark:hover:bg-gray-700 p-1 rounded"},[(s(),i("svg",Rt,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:a.value?"M13 5l7 7-7 7M5 5l7 7-7 7":"M11 19l-7-7 7-7m8 14l-7-7 7-7"},null,8,Nt)]))])]),$(je,{"is-collapsed":a.value,onItemClick:d},null,8,["is-collapsed"]),$(Me,{"is-collapsed":a.value},null,8,["is-collapsed"])])],2)]),e("div",{class:S(["fixed inset-y-0 left-0 z-30 w-64 bg-primary-700 transform transition-transform duration-300 ease-in-out lg:hidden",r.isMobileOpen?"translate-x-0":"-translate-x-full"])},[e("div",Ot,[e("div",Ut,[$(x,{to:"/app/dashboard",class:"flex items-center"},{default:P(()=>[e("div",Ft,[e("span",Kt,c(v.value),1)]),e("h3",Wt,c(u.value),1)]),_:1}),e("button",{onClick:g[0]||(g[0]=_=>n.$emit("close")),class:"p-2 rounded-md text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700"},g[2]||(g[2]=[e("svg",{class:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),$(je,{"is-collapsed":!1,onItemClick:g[1]||(g[1]=_=>n.$emit("close"))}),$(Me,{"is-collapsed":!1})])],2)])}}},Qt={class:"flex","aria-label":"Breadcrumb"},Jt={class:"flex items-center space-x-2 text-sm text-gray-500"},Yt={key:0,class:"mr-2"},Xt={class:"flex items-center"},Zt={key:0,class:"h-3 w-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},es=["d"],ts={key:2,class:"font-medium text-gray-900"},ss={__name:"HeaderBreadcrumbs",props:{breadcrumbs:{type:Array,required:!0}},setup(r){return(o,l)=>{const t=N("router-link");return s(),i("nav",Qt,[e("ol",Jt,[(s(!0),i(U,null,F(r.breadcrumbs,(a,m)=>(s(),i("li",{key:m,class:"flex items-center"},[m>0?(s(),i("div",Yt,l[0]||(l[0]=[e("svg",{class:"h-3 w-3 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})],-1)]))):k("",!0),a.to&&m<r.breadcrumbs.length-1?(s(),q(t,{key:1,to:a.to,class:"hover:text-gray-700 transition-colors duration-150"},{default:P(()=>[e("span",Xt,[a.icon?(s(),i("svg",Zt,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:a.icon},null,8,es)])):k("",!0),E(" "+c(a.label),1)])]),_:2},1032,["to"])):(s(),i("span",ts,c(a.label),1))]))),128))])])}}},os={class:"flex items-center space-x-2"},rs={key:0,class:"h-3 w-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},as={key:1,class:"h-3 w-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},ns={__name:"HeaderQuickActions",emits:["quick-create-project"],setup(r){const o=me(),{isDarkMode:l,toggleDarkMode:t}=ge(),a=f(()=>{var m;return((m=o.name)==null?void 0:m.includes("projects"))||o.path.includes("/projects")});return(m,u)=>(s(),i("div",os,[a.value?(s(),i("button",{key:0,onClick:u[0]||(u[0]=v=>m.$emit("quick-create-project")),class:"inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},u[2]||(u[2]=[e("svg",{class:"h-3 w-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),E(" Nuovo Progetto ")]))):k("",!0),e("button",{onClick:u[1]||(u[1]=(...v)=>O(t)&&O(t)(...v)),class:"inline-flex items-center px-3 py-1.5 border border-gray-300 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700",title:"Cambia tema"},[O(l)?(s(),i("svg",as,u[4]||(u[4]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"},null,-1)]))):(s(),i("svg",rs,u[3]||(u[3]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"},null,-1)])))])]))}},is={class:"relative"},ls={class:"relative"},ds={key:0,class:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center"},cs={class:"py-1"},us={key:0,class:"px-4 py-8 text-center text-gray-500 text-sm"},ms={key:1,class:"max-h-64 overflow-y-auto"},ps=["onClick"],vs={class:"flex items-start"},gs={class:"flex-shrink-0"},hs={class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},fs=["d"],xs={class:"ml-3 flex-1"},ys={class:"text-sm font-medium text-gray-900"},_s={class:"text-xs text-gray-500 mt-1"},ks={class:"text-xs text-gray-400 mt-1"},bs={key:0,class:"flex-shrink-0"},ws={key:2,class:"px-4 py-2 border-t border-gray-100"},$s={__name:"HeaderNotifications",setup(r){const o=C(!1),l=C([{id:1,type:"task",title:"Nuovo task assegnato",message:'Ti è stato assegnato un nuovo task nel progetto "Website Redesign"',created_at:new Date().toISOString(),read:!1},{id:2,type:"project",title:"Progetto completato",message:'Il progetto "Mobile App" è stato completato con successo',created_at:new Date(Date.now()-36e5).toISOString(),read:!0}]),t=f(()=>l.value.filter(d=>!d.read).length);function a(d){const n={task:"h-6 w-6 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center",project:"h-6 w-6 rounded-full bg-green-100 text-green-600 flex items-center justify-center",user:"h-6 w-6 rounded-full bg-purple-100 text-purple-600 flex items-center justify-center",system:"h-6 w-6 rounded-full bg-gray-100 text-gray-600 flex items-center justify-center"};return n[d]||n.system}function m(d){const n={task:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2",project:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10",user:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z",system:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"};return n[d]||n.system}function u(d){const n=new Date(d),x=new Date-n;return x<6e4?"Adesso":x<36e5?`${Math.floor(x/6e4)}m fa`:x<864e5?`${Math.floor(x/36e5)}h fa`:n.toLocaleDateString("it-IT")}function v(d){d.read||(d.read=!0),o.value=!1}function p(){l.value.forEach(d=>d.read=!0)}return(d,n)=>(s(),i("div",is,[e("button",{onClick:n[0]||(n[0]=g=>o.value=!o.value),class:"p-2 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},[n[3]||(n[3]=e("span",{class:"sr-only"},"Visualizza notifiche",-1)),e("div",ls,[n[2]||(n[2]=e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 17h5l-5 5v-5zM10 21a2 2 0 01-2-2V7a7 7 0 1114 0v12a2 2 0 01-2 2H10z"})],-1)),t.value>0?(s(),i("span",ds,c(t.value>9?"9+":t.value),1)):k("",!0)])]),o.value?(s(),i("div",{key:0,onClick:n[1]||(n[1]=g=>o.value=!1),class:"origin-top-right absolute right-0 mt-2 w-80 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50"},[e("div",cs,[n[5]||(n[5]=e("div",{class:"px-4 py-2 border-b border-gray-100"},[e("h3",{class:"text-sm font-medium text-gray-900"},"Notifiche")],-1)),l.value.length===0?(s(),i("div",us," Nessuna notifica ")):(s(),i("div",ms,[(s(!0),i(U,null,F(l.value,g=>(s(),i("div",{key:g.id,class:"px-4 py-3 hover:bg-gray-50 cursor-pointer border-b border-gray-50 last:border-b-0",onClick:x=>v(g)},[e("div",vs,[e("div",gs,[e("div",{class:S(a(g.type))},[(s(),i("svg",hs,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:m(g.type)},null,8,fs)]))],2)]),e("div",xs,[e("p",ys,c(g.title),1),e("p",_s,c(g.message),1),e("p",ks,c(u(g.created_at)),1)]),g.read?k("",!0):(s(),i("div",bs,n[4]||(n[4]=[e("div",{class:"h-2 w-2 bg-primary-500 rounded-full"},null,-1)])))])],8,ps))),128))])),l.value.length>0?(s(),i("div",ws,[e("button",{onClick:p,class:"text-xs text-primary-600 hover:text-primary-800"}," Segna tutte come lette ")])):k("",!0)])])):k("",!0)]))}},Cs={class:"relative"},js={class:"flex items-start justify-center min-h-screen pt-16 px-4 pb-20 text-center sm:block sm:p-0"},Ms={class:"inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6"},zs={class:"flex items-center"},Ps={class:"flex-1"},Ss={key:0,class:"mt-4 max-h-64 overflow-y-auto"},As={class:"space-y-1"},Es=["onClick"],Is={class:"flex-shrink-0"},Vs={class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Ds=["d"],Ls={class:"ml-3 flex-1 min-w-0"},Ts={class:"text-sm font-medium text-gray-900 truncate"},Bs={class:"text-xs text-gray-500 truncate"},Hs={class:"ml-2 text-xs text-gray-400"},qs={key:1,class:"mt-4 text-center py-4"},Rs={key:2,class:"mt-4 text-center py-4"},Ns={__name:"HeaderSearch",setup(r){const o=te(),l=C(!1),t=C(""),a=C([]),m=C(-1),u=C(!1),v=C(null),p=[{id:1,type:"project",title:"Website Redesign",description:"Progetto di redesign del sito web aziendale",path:"/app/projects/1"},{id:2,type:"person",title:"Mario Rossi",description:"Senior Developer",path:"/app/personnel/directory/1"},{id:3,type:"document",title:"Specifiche Tecniche",description:"Documento delle specifiche del progetto mobile",path:"/app/documents/1"},{id:4,type:"task",title:"Implementazione API",description:"Task per l'implementazione delle API REST",path:"/app/projects/1/tasks/5"}];ne(l,async j=>{var b;j?(await Pe(),(b=v.value)==null||b.focus()):(t.value="",a.value=[],m.value=-1)});function d(){if(!t.value.trim()){a.value=[];return}u.value=!0,setTimeout(()=>{a.value=p.filter(j=>j.title.toLowerCase().includes(t.value.toLowerCase())||j.description.toLowerCase().includes(t.value.toLowerCase())),m.value=-1,u.value=!1},200)}function n(j){if(a.value.length===0)return;const b=m.value+j;b>=0&&b<a.value.length&&(m.value=b)}function g(){m.value>=0&&a.value[m.value]&&x(a.value[m.value])}function x(j){l.value=!1,o.push(j.path)}function _(j){const b={project:"h-6 w-6 rounded bg-blue-100 text-blue-600 flex items-center justify-center",person:"h-6 w-6 rounded bg-green-100 text-green-600 flex items-center justify-center",document:"h-6 w-6 rounded bg-yellow-100 text-yellow-600 flex items-center justify-center",task:"h-6 w-6 rounded bg-purple-100 text-purple-600 flex items-center justify-center"};return b[j]||b.document}function z(j){const b={project:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10",person:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z",document:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z",task:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"};return b[j]||b.document}function A(j){return{project:"Progetto",person:"Persona",document:"Documento",task:"Task"}[j]||"Elemento"}return(j,b)=>(s(),i("div",Cs,[e("button",{onClick:b[0]||(b[0]=B=>l.value=!l.value),class:"p-2 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},b[7]||(b[7]=[e("span",{class:"sr-only"},"Cerca",-1),e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1)])),l.value?(s(),i("div",{key:0,class:"fixed inset-0 z-50 overflow-y-auto",onClick:b[6]||(b[6]=pe(B=>l.value=!1,["self"]))},[e("div",js,[b[11]||(b[11]=e("div",{class:"fixed inset-0 bg-black bg-opacity-25 transition-opacity"},null,-1)),e("div",Ms,[e("div",null,[e("div",zs,[e("div",Ps,[W(e("input",{ref_key:"searchInput",ref:v,"onUpdate:modelValue":b[1]||(b[1]=B=>t.value=B),onInput:d,onKeydown:[b[2]||(b[2]=re(B=>l.value=!1,["escape"])),re(g,["enter"]),b[3]||(b[3]=re(B=>n(-1),["up"])),b[4]||(b[4]=re(B=>n(1),["down"]))],type:"text",placeholder:"Cerca progetti, persone, documenti...",class:"block w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm"},null,544),[[ee,t.value]])]),e("button",{onClick:b[5]||(b[5]=B=>l.value=!1),class:"ml-3 p-2 text-gray-400 hover:text-gray-600"},b[8]||(b[8]=[e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),a.value.length>0?(s(),i("div",Ss,[e("div",As,[(s(!0),i(U,null,F(a.value,(B,J)=>(s(),i("div",{key:B.id,onClick:L=>x(B),class:S(["flex items-center px-3 py-2 rounded-md cursor-pointer",J===m.value?"bg-primary-50":"hover:bg-gray-50"])},[e("div",Is,[e("div",{class:S(_(B.type))},[(s(),i("svg",Vs,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:z(B.type)},null,8,Ds)]))],2)]),e("div",Ls,[e("p",Ts,c(B.title),1),e("p",Bs,c(B.description),1)]),e("div",Hs,c(A(B.type)),1)],10,Es))),128))])])):t.value&&!u.value?(s(),i("div",qs,b[9]||(b[9]=[e("p",{class:"text-sm text-gray-500"},"Nessun risultato trovato",-1)]))):t.value?k("",!0):(s(),i("div",Rs,b[10]||(b[10]=[e("p",{class:"text-xs text-gray-400"},"Inizia a digitare per cercare...",-1)])))])])])])):k("",!0)]))}},Os={class:"relative"},Us={class:"h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center"},Fs={class:"text-sm font-medium text-primary-700"},Ks={class:"py-1"},Ws={class:"px-4 py-2 border-b border-gray-100 dark:border-gray-700"},Gs={class:"text-sm font-medium text-gray-900 dark:text-white"},Qs={class:"text-xs text-gray-500 dark:text-gray-400"},Js={__name:"HeaderUserMenu",setup(r){const o=te(),l=Z(),t=C(!1),{isDarkMode:a,toggleDarkMode:m}=ge(),u=f(()=>l.user&&(l.user.name||l.user.username)||"Utente"),v=f(()=>{var n;return((n=l.user)==null?void 0:n.email)||""}),p=f(()=>l.user?u.value.charAt(0).toUpperCase():"U");async function d(){t.value=!1,await l.logout(),o.push("/auth/login")}return(n,g)=>{const x=N("router-link");return s(),i("div",Os,[e("button",{onClick:g[0]||(g[0]=_=>t.value=!t.value),class:"flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},[g[5]||(g[5]=e("span",{class:"sr-only"},"Apri menu utente",-1)),e("div",Us,[e("span",Fs,c(p.value),1)])]),t.value?(s(),i("div",{key:0,onClick:g[4]||(g[4]=_=>t.value=!1),class:"origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 z-50"},[e("div",Ks,[e("div",Ws,[e("p",Gs,c(u.value),1),e("p",Qs,c(v.value),1)]),$(x,{to:"/app/profile",class:"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",onClick:g[1]||(g[1]=_=>t.value=!1)},{default:P(()=>g[6]||(g[6]=[E(" Il tuo profilo ")])),_:1,__:[6]}),$(x,{to:"/app/settings",class:"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",onClick:g[2]||(g[2]=_=>t.value=!1)},{default:P(()=>g[7]||(g[7]=[E(" Impostazioni ")])),_:1,__:[7]}),g[8]||(g[8]=e("div",{class:"border-t border-gray-100 dark:border-gray-700 my-1"},null,-1)),e("button",{onClick:g[3]||(g[3]=(..._)=>O(m)&&O(m)(..._)),class:"flex items-center justify-between w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"},[e("span",null,c(O(a)?"Modalità chiara":"Modalità scura"),1),e("i",{class:S([O(a)?"fas fa-sun":"fas fa-moon","text-xs"])},null,2)]),e("button",{onClick:d,class:"block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"}," Esci ")])])):k("",!0)])}}},Ys={class:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700"},Xs={class:"flex justify-between items-center px-4 py-4 sm:px-6 lg:px-8"},Zs={class:"flex items-center space-x-4"},eo={class:"flex flex-col"},to={class:"text-lg font-semibold text-gray-900 dark:text-white"},so={class:"flex items-center space-x-4"},oo={class:"hidden md:flex items-center space-x-2"},ro={__name:"AppHeader",props:{pageTitle:{type:String,required:!0},breadcrumbs:{type:Array,default:()=>[]}},emits:["toggle-mobile-sidebar","quick-create-project"],setup(r){return(o,l)=>(s(),i("header",Ys,[e("div",Xs,[e("div",Zs,[e("button",{onClick:l[0]||(l[0]=t=>o.$emit("toggle-mobile-sidebar")),class:"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:text-gray-500 dark:hover:text-gray-300 dark:hover:bg-gray-700"},l[2]||(l[2]=[e("svg",{class:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h16"})],-1)])),e("div",eo,[e("h2",to,c(r.pageTitle),1),r.breadcrumbs.length>0?(s(),q(ss,{key:0,breadcrumbs:r.breadcrumbs},null,8,["breadcrumbs"])):k("",!0)])]),e("div",so,[e("div",oo,[$(ns,{onQuickCreateProject:l[1]||(l[1]=t=>o.$emit("quick-create-project"))})]),$($s),$(Ns),$(Js)])])]))}},ao={__name:"LoadingSpinner",props:{size:{type:String,default:"md",validator:r=>["sm","md","lg","xl"].includes(r)},message:{type:String,default:""},centered:{type:Boolean,default:!0}},setup(r){const o=r,l=f(()=>{const u={sm:"20px",md:"32px",lg:"48px",xl:"64px"};return`width: ${u[o.size]}; height: ${u[o.size]};`}),t=f(()=>["flex",o.centered?"items-center justify-center":"","space-y-2"]),a=f(()=>["flex items-center justify-center"]),m=f(()=>["text-sm text-gray-600 text-center"]);return(u,v)=>(s(),i("div",{class:S(t.value)},[e("div",{class:S(a.value)},[e("div",{class:"animate-spin rounded-full border-2 border-gray-300 border-t-primary-600",style:ve(l.value)},null,4)],2),r.message?(s(),i("p",{key:0,class:S(m.value)},c(r.message),3)):k("",!0)],2))}},Ee=(r,o)=>{const l=r.__vccOpts||r;for(const[t,a]of o)l[t]=a;return l},no={class:"fixed bottom-0 right-0 z-50 p-6 space-y-4"},io={class:"p-4"},lo={class:"flex items-start"},co={class:"flex-shrink-0"},uo={class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},mo=["d"],po={class:"ml-3 w-0 flex-1 pt-0.5"},vo={class:"text-sm font-medium text-gray-900"},go={class:"mt-1 text-sm text-gray-500"},ho={class:"ml-4 flex-shrink-0 flex"},fo=["onClick"],xo={__name:"NotificationManager",setup(r){const o=C([]);function l(v){const p=Date.now(),d={id:p,type:v.type||"info",title:v.title,message:v.message,duration:v.duration||5e3};o.value.push(d),d.duration>0&&setTimeout(()=>{t(p)},d.duration)}function t(v){const p=o.value.findIndex(d=>d.id===v);p>-1&&o.value.splice(p,1)}function a(v){const p={success:"border-l-4 border-green-400",error:"border-l-4 border-red-400",warning:"border-l-4 border-yellow-400",info:"border-l-4 border-blue-400"};return p[v]||p.info}function m(v){const p={success:"h-8 w-8 rounded-full bg-green-100 text-green-600 flex items-center justify-center",error:"h-8 w-8 rounded-full bg-red-100 text-red-600 flex items-center justify-center",warning:"h-8 w-8 rounded-full bg-yellow-100 text-yellow-600 flex items-center justify-center",info:"h-8 w-8 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center"};return p[v]||p.info}function u(v){const p={success:"M5 13l4 4L19 7",error:"M6 18L18 6M6 6l12 12",warning:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-2.694-.833-3.464 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z",info:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"};return p[v]||p.info}return window.showNotification=l,Y(()=>{}),(v,p)=>(s(),i("div",no,[$(De,{name:"notification",tag:"div",class:"space-y-4"},{default:P(()=>[(s(!0),i(U,null,F(o.value,d=>(s(),i("div",{key:d.id,class:S([a(d.type),"max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden"])},[e("div",io,[e("div",lo,[e("div",co,[e("div",{class:S(m(d.type))},[(s(),i("svg",uo,[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:u(d.type)},null,8,mo)]))],2)]),e("div",po,[e("p",vo,c(d.title),1),e("p",go,c(d.message),1)]),e("div",ho,[e("button",{onClick:n=>t(d.id),class:"bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},p[0]||(p[0]=[e("span",{class:"sr-only"},"Chiudi",-1),e("svg",{class:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),8,fo)])])])],2))),128))]),_:1})]))}},yo=Ee(xo,[["__scopeId","data-v-220f0827"]]),_o={class:"h-screen flex bg-gray-50 dark:bg-gray-900"},ko={class:"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900"},bo={class:"py-6"},wo={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},$o={key:0,class:"mb-6"},Co={key:1,class:"flex items-center justify-center h-64"},jo={__name:"AppLayout",setup(r){const o=me(),l=te(),t=se(),a=C(!1),m=C(!1),u=C(!1);f(()=>t.config||{});const v=f(()=>t.config!==null),p=f(()=>{var j;return(j=o.meta)!=null&&j.title?o.meta.title:{dashboard:"Dashboard",projects:"Progetti","projects-list":"Elenco Progetti","projects-view":"Dettaglio Progetto","projects-create":"Nuovo Progetto",personnel:"Personale","personnel-directory":"Rubrica Aziendale","personnel-orgchart":"Organigramma","personnel-skills":"Competenze"}[o.name]||"DatPortal"}),d=f(()=>{var A;return(A=o.meta)!=null&&A.breadcrumbs?o.meta.breadcrumbs.map(j=>({label:j.label,to:j.to,icon:j.icon})):[]}),n=f(()=>{var A;return((A=o.meta)==null?void 0:A.hasActions)||!1});function g(){a.value=!a.value}function x(){a.value=!1}function _(A){m.value=A}function z(){l.push("/app/projects/create")}return ne(o,()=>{u.value=!0,setTimeout(()=>{u.value=!1},300)}),ne(o,()=>{x()}),Y(()=>{v.value||t.loadConfig()}),(A,j)=>{const b=N("router-view");return s(),i("div",_o,[a.value?(s(),i("div",{key:0,onClick:x,class:"fixed inset-0 z-20 bg-black bg-opacity-50 lg:hidden"})):k("",!0),$(Gt,{"is-mobile-open":a.value,onClose:x,onToggleCollapsed:_},null,8,["is-mobile-open"]),e("div",{class:S(["flex flex-col flex-1 overflow-hidden transition-all duration-300",[m.value?"lg:ml-20":"lg:ml-64"]])},[$(ro,{"page-title":p.value,breadcrumbs:d.value,onToggleMobileSidebar:g,onQuickCreateProject:z},null,8,["page-title","breadcrumbs"]),e("main",ko,[e("div",bo,[e("div",wo,[n.value?(s(),i("div",$o,[Le(A.$slots,"page-actions")])):k("",!0),u.value?(s(),i("div",Co,[$(ao)])):(s(),q(b,{key:2}))])])])],2),$(yo)])}}},Mo={class:"min-h-screen bg-gray-50"},zo={class:"bg-white shadow-sm border-b"},Po={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},So={class:"flex justify-between h-16"},Ao={class:"flex items-center"},Eo={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center"},Io={class:"text-white font-bold text-sm"},Vo={class:"text-xl font-semibold text-gray-900"},Do={class:"hidden md:flex items-center space-x-8"},Lo={class:"flex items-center space-x-4 ml-8 pl-8 border-l border-gray-200"},To={class:"md:hidden flex items-center"},Bo={key:0,class:"md:hidden"},Ho={class:"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t"},qo={class:"bg-gray-800 text-white"},Ro={class:"max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8"},No={class:"grid grid-cols-1 md:grid-cols-4 gap-8"},Oo={class:"col-span-1 md:col-span-2"},Uo={class:"flex items-center space-x-3 mb-4"},Fo={class:"w-8 h-8 bg-primary-600 rounded flex items-center justify-center"},Ko={class:"text-white font-bold text-sm"},Wo={class:"text-xl font-semibold"},Go={class:"text-gray-300 max-w-md"},Qo={class:"space-y-2"},Jo={class:"space-y-2 text-gray-300"},Yo={key:0},Xo={key:1},Zo={key:2},er={class:"mt-8 pt-8 border-t border-gray-700 text-center text-gray-400"},ze={__name:"PublicLayout",setup(r){const o=se(),l=C(!1),t=f(()=>o.config||{}),a=f(()=>{var p;return((p=t.value.company)==null?void 0:p.name)||"DatVinci"}),m=f(()=>a.value.split(" ").map(d=>d[0]).join("").toUpperCase().slice(0,2)),u=f(()=>o.config!==null),v=new Date().getFullYear();return Y(()=>{u.value||o.loadConfig()}),(p,d)=>{var x,_,z,A,j,b;const n=N("router-link"),g=N("router-view");return s(),i("div",Mo,[e("nav",zo,[e("div",Po,[e("div",So,[e("div",Ao,[$(n,{to:"/",class:"flex items-center space-x-3"},{default:P(()=>[e("div",Eo,[e("span",Io,c(m.value),1)]),e("span",Vo,c(a.value),1)]),_:1})]),e("div",Do,[$(n,{to:"/",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:P(()=>d[1]||(d[1]=[E(" Home ")])),_:1,__:[1]}),$(n,{to:"/about",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:P(()=>d[2]||(d[2]=[E(" Chi Siamo ")])),_:1,__:[2]}),$(n,{to:"/services",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:P(()=>d[3]||(d[3]=[E(" Servizi ")])),_:1,__:[3]}),$(n,{to:"/contact",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:P(()=>d[4]||(d[4]=[E(" Contatti ")])),_:1,__:[4]}),e("div",Lo,[$(n,{to:"/auth/login",class:"text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium"},{default:P(()=>d[5]||(d[5]=[E(" Accedi ")])),_:1,__:[5]}),$(n,{to:"/auth/register",class:"bg-primary-600 text-white hover:bg-primary-700 px-4 py-2 rounded-md text-sm font-medium"},{default:P(()=>d[6]||(d[6]=[E(" Registrati ")])),_:1,__:[6]})])]),e("div",To,[e("button",{onClick:d[0]||(d[0]=B=>l.value=!l.value),class:"text-gray-400 hover:text-gray-500"},d[7]||(d[7]=[e("svg",{class:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h16"})],-1)]))])])]),l.value?(s(),i("div",Bo,[e("div",Ho,[$(n,{to:"/",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:P(()=>d[8]||(d[8]=[E(" Home ")])),_:1,__:[8]}),$(n,{to:"/about",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:P(()=>d[9]||(d[9]=[E(" Chi Siamo ")])),_:1,__:[9]}),$(n,{to:"/services",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:P(()=>d[10]||(d[10]=[E(" Servizi ")])),_:1,__:[10]}),$(n,{to:"/contact",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:P(()=>d[11]||(d[11]=[E(" Contatti ")])),_:1,__:[11]}),d[14]||(d[14]=e("hr",{class:"my-2"},null,-1)),$(n,{to:"/auth/login",class:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-600"},{default:P(()=>d[12]||(d[12]=[E(" Accedi ")])),_:1,__:[12]}),$(n,{to:"/auth/register",class:"block px-3 py-2 text-base font-medium bg-primary-600 text-white rounded-md"},{default:P(()=>d[13]||(d[13]=[E(" Registrati ")])),_:1,__:[13]})])])):k("",!0)]),e("main",null,[$(g)]),e("footer",qo,[e("div",Ro,[e("div",No,[e("div",Oo,[e("div",Uo,[e("div",Fo,[e("span",Ko,c(m.value),1)]),e("span",Wo,c(a.value),1)]),e("p",Go,c(O(o).interpolateText((x=t.value.footer)==null?void 0:x.description)||((_=t.value.company)==null?void 0:_.description)||"Innovazione e tecnologia per il futuro digitale della tua azienda."),1)]),e("div",null,[d[19]||(d[19]=e("h3",{class:"text-sm font-semibold uppercase tracking-wider mb-4"},"Link Rapidi",-1)),e("ul",Qo,[e("li",null,[$(n,{to:"/",class:"text-gray-300 hover:text-white"},{default:P(()=>d[15]||(d[15]=[E("Home")])),_:1,__:[15]})]),e("li",null,[$(n,{to:"/about",class:"text-gray-300 hover:text-white"},{default:P(()=>d[16]||(d[16]=[E("Chi Siamo")])),_:1,__:[16]})]),e("li",null,[$(n,{to:"/services",class:"text-gray-300 hover:text-white"},{default:P(()=>d[17]||(d[17]=[E("Servizi")])),_:1,__:[17]})]),e("li",null,[$(n,{to:"/contact",class:"text-gray-300 hover:text-white"},{default:P(()=>d[18]||(d[18]=[E("Contatti")])),_:1,__:[18]})])])]),e("div",null,[d[20]||(d[20]=e("h3",{class:"text-sm font-semibold uppercase tracking-wider mb-4"},"Contatti",-1)),e("ul",Jo,[(z=t.value.contact)!=null&&z.email?(s(),i("li",Yo,c(t.value.contact.email),1)):k("",!0),(A=t.value.contact)!=null&&A.phone?(s(),i("li",Xo,c(t.value.contact.phone),1)):k("",!0),(j=t.value.contact)!=null&&j.address?(s(),i("li",Zo,c(t.value.contact.address),1)):k("",!0)])])]),e("div",er,[e("p",null,c(O(o).interpolateText((b=t.value.footer)==null?void 0:b.copyright)||`© ${O(v)} ${a.value}. Tutti i diritti riservati.`),1)])])])])}}},tr={class:"bg-white"},sr={class:"relative overflow-hidden"},or={class:"max-w-7xl mx-auto"},rr={class:"relative z-10 pb-8 bg-white sm:pb-16 md:pb-20 lg:max-w-2xl lg:w-full lg:pb-28 xl:pb-32"},ar={class:"mt-10 mx-auto max-w-7xl px-4 sm:mt-12 sm:px-6 md:mt-16 lg:mt-20 lg:px-8 xl:mt-28"},nr={class:"sm:text-center lg:text-left"},ir={class:"text-4xl tracking-tight font-extrabold text-gray-900 sm:text-5xl md:text-6xl"},lr={class:"block xl:inline"},dr={class:"mt-3 text-base text-gray-500 sm:mt-5 sm:text-lg sm:max-w-xl sm:mx-auto md:mt-5 md:text-xl lg:mx-0"},cr={class:"mt-5 sm:mt-8 sm:flex sm:justify-center lg:justify-start"},ur={class:"rounded-md shadow"},mr={class:"mt-3 sm:mt-0 sm:ml-3"},pr={class:"py-12 bg-white"},vr={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},gr={class:"lg:text-center"},hr={class:"text-base text-primary-600 font-semibold tracking-wide uppercase"},fr={class:"mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl"},xr={key:0,class:"mt-10"},yr={class:"space-y-10 md:space-y-0 md:grid md:grid-cols-2 md:gap-x-8 md:gap-y-10"},_r={class:"absolute flex items-center justify-center h-12 w-12 rounded-md bg-primary-500 text-white"},kr={class:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},br={key:0,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"},wr={key:1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"},$r={key:2,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"},Cr={key:3,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"},jr={class:"ml-16 text-lg leading-6 font-medium text-gray-900"},Mr={class:"mt-2 ml-16 text-base text-gray-500"},zr={__name:"Home",setup(r){const o=se(),l=f(()=>o.config||{}),t=f(()=>{var m;return((m=l.value.pages)==null?void 0:m.home)||{}}),a=f(()=>l.value.company||{});return Y(()=>{o.config||o.loadConfig()}),(m,u)=>{var p,d,n,g;const v=N("router-link");return s(),i("div",tr,[e("div",sr,[e("div",or,[e("div",rr,[e("main",ar,[e("div",nr,[e("h1",ir,[e("span",lr,c(((p=t.value.hero)==null?void 0:p.title)||"Innovazione per il futuro"),1)]),e("p",dr,c(((d=t.value.hero)==null?void 0:d.subtitle)||O(o).interpolateText(a.value.description)||"Supportiamo le aziende nel loro percorso di crescita attraverso soluzioni tecnologiche all'avanguardia"),1),e("div",cr,[e("div",ur,[$(v,{to:"/services",class:"w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 md:py-4 md:text-lg md:px-10"},{default:P(()=>{var x;return[E(c(((x=t.value.hero)==null?void 0:x.cta_primary)||"Scopri i nostri servizi"),1)]}),_:1})]),e("div",mr,[$(v,{to:"/contact",class:"w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 md:py-4 md:text-lg md:px-10"},{default:P(()=>{var x;return[E(c(((x=t.value.hero)==null?void 0:x.cta_secondary)||"Contattaci"),1)]}),_:1})])])])])])]),u[0]||(u[0]=e("div",{class:"lg:absolute lg:inset-y-0 lg:right-0 lg:w-1/2"},[e("div",{class:"h-56 w-full bg-gradient-to-r from-primary-400 to-primary-600 sm:h-72 md:h-96 lg:w-full lg:h-full flex items-center justify-center"},[e("svg",{class:"h-24 w-24 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})])])],-1))]),e("div",pr,[e("div",vr,[e("div",gr,[e("h2",hr,c(((n=t.value.services_section)==null?void 0:n.title)||"I nostri servizi"),1),e("p",fr,c(((g=t.value.services_section)==null?void 0:g.subtitle)||"Soluzioni innovative per ogni esigenza aziendale"),1)]),a.value.platform_features?(s(),i("div",xr,[e("div",yr,[(s(!0),i(U,null,F(a.value.platform_features,x=>(s(),i("div",{key:x.title,class:"relative"},[e("div",_r,[(s(),i("svg",kr,[x.icon==="briefcase"?(s(),i("path",br)):x.icon==="users"?(s(),i("path",wr)):x.icon==="chart"?(s(),i("path",$r)):(s(),i("path",Cr))]))]),e("p",jr,c(x.title),1),e("p",Mr,c(x.description),1)]))),128))])])):k("",!0)])])])}}},Pr={class:"py-16 bg-white"},Sr={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},Ar={class:"text-center"},Er={class:"text-4xl font-extrabold text-gray-900 sm:text-5xl"},Ir={class:"mt-4 text-xl text-gray-600"},Vr={key:0,class:"mt-16"},Dr={class:"max-w-3xl mx-auto"},Lr={class:"text-3xl font-bold text-gray-900 text-center mb-8"},Tr={class:"text-lg text-gray-700 leading-relaxed"},Br={class:"mt-16 grid grid-cols-1 md:grid-cols-2 gap-12"},Hr={key:0,class:"bg-gray-50 p-8 rounded-lg"},qr={class:"text-2xl font-bold text-gray-900 mb-4"},Rr={class:"text-gray-700"},Nr={key:1,class:"bg-gray-50 p-8 rounded-lg"},Or={class:"text-2xl font-bold text-gray-900 mb-4"},Ur={class:"text-gray-700"},Fr={key:1,class:"mt-16"},Kr={class:"text-center mb-12"},Wr={class:"text-3xl font-bold text-gray-900"},Gr={class:"mt-4 text-xl text-gray-600"},Qr={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"},Jr={class:"text-lg font-semibold text-gray-900"},Yr={key:2,class:"mt-16"},Xr={class:"text-center"},Zr={class:"text-3xl font-bold text-gray-900"},ea={class:"mt-4 text-xl text-gray-600"},ta={class:"mt-8 inline-flex items-center px-6 py-3 bg-primary-50 rounded-lg"},sa={class:"text-primary-900 font-medium"},oa={__name:"About",setup(r){const o=se(),l=f(()=>o.config||{}),t=f(()=>{var m;return((m=l.value.pages)==null?void 0:m.about)||{}}),a=f(()=>l.value.company||{});return Y(()=>{o.config||o.loadConfig()}),(m,u)=>{var v,p;return s(),i("div",Pr,[e("div",Sr,[e("div",Ar,[e("h1",Er,c(((v=t.value.hero)==null?void 0:v.title)||"Chi Siamo"),1),e("p",Ir,c(((p=t.value.hero)==null?void 0:p.subtitle)||"La nostra storia e i nostri valori"),1)]),t.value.story_section?(s(),i("div",Vr,[e("div",Dr,[e("h2",Lr,c(t.value.story_section.title),1),e("p",Tr,c(O(o).interpolateText(t.value.story_section.content)),1)])])):k("",!0),e("div",Br,[t.value.mission_section?(s(),i("div",Hr,[e("h3",qr,c(t.value.mission_section.title),1),e("p",Rr,c(O(o).interpolateText(t.value.mission_section.content)),1)])):k("",!0),t.value.vision_section?(s(),i("div",Nr,[e("h3",Or,c(t.value.vision_section.title),1),e("p",Ur,c(O(o).interpolateText(t.value.vision_section.content)),1)])):k("",!0)]),t.value.expertise_section&&a.value.expertise?(s(),i("div",Fr,[e("div",Kr,[e("h2",Wr,c(t.value.expertise_section.title),1),e("p",Gr,c(t.value.expertise_section.subtitle),1)]),e("div",Qr,[(s(!0),i(U,null,F(a.value.expertise,d=>(s(),i("div",{key:d,class:"bg-white p-6 rounded-lg shadow-sm border border-gray-200 text-center"},[u[0]||(u[0]=e("div",{class:"w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4"},[e("svg",{class:"w-6 h-6 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])],-1)),e("h3",Jr,c(d),1)]))),128))])])):k("",!0),t.value.team_section?(s(),i("div",Yr,[e("div",Xr,[e("h2",Zr,c(t.value.team_section.title),1),e("p",ea,c(t.value.team_section.subtitle),1),e("div",ta,[u[1]||(u[1]=e("svg",{class:"w-5 h-5 text-primary-600 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})],-1)),e("span",sa,c(a.value.team_size),1)])])])):k("",!0)])])}}},ra={class:"py-16 bg-white"},aa={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},na={class:"text-center"},ia={class:"text-4xl font-extrabold text-gray-900 sm:text-5xl"},la={class:"mt-4 text-xl text-gray-600"},da={key:0,class:"mt-8 text-center"},ca={class:"text-lg text-gray-700 max-w-3xl mx-auto"},ua={class:"mt-16 grid grid-cols-1 lg:grid-cols-2 gap-16"},ma={key:0},pa={class:"text-2xl font-bold text-gray-900 mb-8"},va={class:"block text-sm font-medium text-gray-700 mb-2"},ga={class:"block text-sm font-medium text-gray-700 mb-2"},ha={class:"block text-sm font-medium text-gray-700 mb-2"},fa=["disabled"],xa={key:1},ya={class:"text-2xl font-bold text-gray-900 mb-8"},_a={class:"space-y-6"},ka={key:0,class:"flex items-start"},ba={class:"font-medium text-gray-900"},wa={class:"text-gray-600"},$a={key:1,class:"flex items-start"},Ca={class:"font-medium text-gray-900"},ja={class:"text-gray-600"},Ma={key:2,class:"flex items-start"},za={class:"font-medium text-gray-900"},Pa={class:"text-gray-600"},Sa={key:3,class:"flex items-start"},Aa={class:"font-medium text-gray-900"},Ea={class:"text-gray-600"},Ia={__name:"Contact",setup(r){const o=se(),l=f(()=>o.config||{}),t=f(()=>{var d;return((d=l.value.pages)==null?void 0:d.contact)||{}}),a=f(()=>l.value.contact||{}),m=C({name:"",email:"",message:""}),u=C(!1),v=C({text:"",type:""}),p=async()=>{var d,n;if(!m.value.name||!m.value.email||!m.value.message){v.value={text:((d=t.value.form)==null?void 0:d.error_message)||"Tutti i campi sono obbligatori",type:"error"};return}u.value=!0,v.value={text:"",type:""};try{await new Promise(g=>setTimeout(g,1e3)),v.value={text:((n=t.value.form)==null?void 0:n.success_message)||"Messaggio inviato con successo!",type:"success"},m.value={name:"",email:"",message:""}}catch{v.value={text:"Errore durante l'invio. Riprova più tardi.",type:"error"}}finally{u.value=!1}};return Y(()=>{o.config||o.loadConfig()}),(d,n)=>{var g,x;return s(),i("div",ra,[e("div",aa,[e("div",na,[e("h1",ia,c(((g=t.value.hero)==null?void 0:g.title)||"Contattaci"),1),e("p",la,c(((x=t.value.hero)==null?void 0:x.subtitle)||"Siamo qui per aiutarti"),1)]),t.value.intro?(s(),i("div",da,[e("p",ca,c(t.value.intro.content),1)])):k("",!0),e("div",ua,[t.value.form?(s(),i("div",ma,[e("h2",pa,c(t.value.form.title),1),e("form",{onSubmit:pe(p,["prevent"]),class:"space-y-6"},[e("div",null,[e("label",va,c(t.value.form.name_label),1),W(e("input",{"onUpdate:modelValue":n[0]||(n[0]=_=>m.value.name=_),type:"text",required:"",class:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"},null,512),[[ee,m.value.name]])]),e("div",null,[e("label",ga,c(t.value.form.email_label),1),W(e("input",{"onUpdate:modelValue":n[1]||(n[1]=_=>m.value.email=_),type:"email",required:"",class:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"},null,512),[[ee,m.value.email]])]),e("div",null,[e("label",ha,c(t.value.form.message_label),1),W(e("textarea",{"onUpdate:modelValue":n[2]||(n[2]=_=>m.value.message=_),rows:"6",required:"",class:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"},null,512),[[ee,m.value.message]])]),e("button",{type:"submit",disabled:u.value,class:"w-full bg-primary-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-primary-700 disabled:opacity-50"},c(u.value?"Invio in corso...":t.value.form.submit_button),9,fa),v.value.text?(s(),i("div",{key:0,class:S([v.value.type==="success"?"text-green-600":"text-red-600","text-sm mt-2"])},c(v.value.text),3)):k("",!0)],32)])):k("",!0),t.value.info?(s(),i("div",xa,[e("h2",ya,c(t.value.info.title),1),e("div",_a,[a.value.address?(s(),i("div",ka,[n[3]||(n[3]=e("svg",{class:"w-6 h-6 text-primary-600 mt-1 mr-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})],-1)),e("div",null,[e("h3",ba,c(t.value.info.address_label),1),e("p",wa,c(a.value.address),1)])])):k("",!0),a.value.phone?(s(),i("div",$a,[n[4]||(n[4]=e("svg",{class:"w-6 h-6 text-primary-600 mt-1 mr-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"})],-1)),e("div",null,[e("h3",Ca,c(t.value.info.phone_label),1),e("p",ja,c(a.value.phone),1)])])):k("",!0),a.value.email?(s(),i("div",Ma,[n[5]||(n[5]=e("svg",{class:"w-6 h-6 text-primary-600 mt-1 mr-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})],-1)),e("div",null,[e("h3",za,c(t.value.info.email_label),1),e("p",Pa,c(a.value.email),1)])])):k("",!0),a.value.hours?(s(),i("div",Sa,[n[6]||(n[6]=e("svg",{class:"w-6 h-6 text-primary-600 mt-1 mr-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})],-1)),e("div",null,[e("h3",Aa,c(t.value.info.hours_label),1),e("p",Ea,c(a.value.hours),1)])])):k("",!0)])])):k("",!0)])])])}}},Va={class:"py-16 bg-white"},Da={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},La={class:"text-center"},Ta={class:"text-4xl font-extrabold text-gray-900 sm:text-5xl"},Ba={class:"mt-4 text-xl text-gray-600"},Ha={key:0,class:"mt-8 text-center"},qa={class:"text-lg text-gray-700 max-w-3xl mx-auto"},Ra={key:1,class:"mt-16"},Na={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"},Oa={class:"text-xl font-bold text-gray-900 text-center mb-4"},Ua={class:"text-gray-600 text-center"},Fa={key:2,class:"mt-20"},Ka={class:"bg-primary-50 rounded-2xl p-12 text-center"},Wa={class:"text-3xl font-bold text-gray-900 mb-4"},Ga={class:"text-xl text-gray-600 mb-8"},Qa={__name:"Services",setup(r){const o=se(),l=f(()=>o.config||{}),t=f(()=>{var u;return((u=l.value.pages)==null?void 0:u.services)||{}}),a=f(()=>l.value.company||{}),m=u=>({"Sviluppo Software":"Soluzioni software personalizzate per ottimizzare i processi aziendali e migliorare l'efficienza operativa.","Intelligenza Artificiale":"Implementazione di sistemi AI avanzati per automatizzare processi e analizzare dati complessi.","Consulenza IT":"Consulenza strategica per la trasformazione digitale e l'ottimizzazione dell'infrastruttura tecnologica.","Gestione Progetti Innovativi":"Coordinamento e gestione di progetti tecnologici complessi con metodologie agili.","Supporto su Bandi e Finanziamenti":"Assistenza nella ricerca e gestione di bandi pubblici e finanziamenti per l'innovazione."})[u]||"Servizio professionale di alta qualità per supportare la crescita della tua azienda.";return Y(()=>{o.config||o.loadConfig()}),(u,v)=>{var d,n;const p=N("router-link");return s(),i("div",Va,[e("div",Da,[e("div",La,[e("h1",Ta,c(((d=t.value.hero)==null?void 0:d.title)||"I nostri servizi"),1),e("p",Ba,c(((n=t.value.hero)==null?void 0:n.subtitle)||"Soluzioni complete per la tua azienda"),1)]),t.value.intro?(s(),i("div",Ha,[e("p",qa,c(t.value.intro.content),1)])):k("",!0),a.value.expertise?(s(),i("div",Ra,[e("div",Na,[(s(!0),i(U,null,F(a.value.expertise,g=>(s(),i("div",{key:g,class:"bg-white p-8 rounded-lg shadow-lg border border-gray-200 hover:shadow-xl transition-shadow"},[v[0]||(v[0]=e("div",{class:"w-16 h-16 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-6"},[e("svg",{class:"w-8 h-8 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])],-1)),e("h3",Oa,c(g),1),e("p",Ua,c(m(g)),1)]))),128))])])):k("",!0),t.value.cta?(s(),i("div",Fa,[e("div",Ka,[e("h2",Wa,c(t.value.cta.title),1),e("p",Ga,c(t.value.cta.subtitle),1),$(p,{to:"/contact",class:"inline-flex items-center px-8 py-4 bg-primary-600 text-white font-medium rounded-lg hover:bg-primary-700 transition-colors"},{default:P(()=>[E(c(t.value.cta.button)+" ",1),v[1]||(v[1]=e("svg",{class:"ml-2 w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 8l4 4m0 0l-4 4m4-4H3"})],-1))]),_:1,__:[1]})])])):k("",!0)])])}}},Ja={class:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8"},Ya={class:"max-w-md w-full space-y-8"},Xa={class:"mt-2 text-center text-sm text-gray-600"},Za={key:0,class:"rounded-md bg-red-50 p-4"},en={class:"text-sm text-red-700"},tn={class:"rounded-md shadow-sm -space-y-px"},sn={class:"flex items-center justify-between"},on={class:"flex items-center"},rn=["disabled"],an={key:0,class:"absolute left-0 inset-y-0 flex items-center pl-3"},nn={__name:"Login",setup(r){const o=te(),l=Z(),t=C({username:"",password:"",remember:!1}),a=f(()=>l.loading),m=f(()=>l.error);async function u(){(await l.login({username:t.value.username,password:t.value.password,remember:t.value.remember})).success&&o.push("/app/dashboard")}return(v,p)=>{const d=N("router-link");return s(),i("div",Ja,[e("div",Ya,[e("div",null,[p[5]||(p[5]=e("div",{class:"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary-100"},[e("svg",{class:"h-6 w-6 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})])],-1)),p[6]||(p[6]=e("h2",{class:"mt-6 text-center text-3xl font-extrabold text-gray-900"}," Accedi al tuo account ",-1)),e("p",Xa,[p[4]||(p[4]=E(" Oppure ")),$(d,{to:"/auth/register",class:"font-medium text-primary-600 hover:text-primary-500"},{default:P(()=>p[3]||(p[3]=[E(" registrati per un nuovo account ")])),_:1,__:[3]})])]),e("form",{onSubmit:pe(u,["prevent"]),class:"mt-8 space-y-6"},[m.value?(s(),i("div",Za,[e("div",en,c(m.value),1)])):k("",!0),e("div",tn,[e("div",null,[p[7]||(p[7]=e("label",{for:"username",class:"sr-only"},"Username",-1)),W(e("input",{id:"username","onUpdate:modelValue":p[0]||(p[0]=n=>t.value.username=n),name:"username",type:"text",autocomplete:"username",required:"",class:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm",placeholder:"Username"},null,512),[[ee,t.value.username]])]),e("div",null,[p[8]||(p[8]=e("label",{for:"password",class:"sr-only"},"Password",-1)),W(e("input",{id:"password","onUpdate:modelValue":p[1]||(p[1]=n=>t.value.password=n),name:"password",type:"password",autocomplete:"current-password",required:"",class:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm",placeholder:"Password"},null,512),[[ee,t.value.password]])])]),e("div",sn,[e("div",on,[W(e("input",{id:"remember-me","onUpdate:modelValue":p[2]||(p[2]=n=>t.value.remember=n),name:"remember-me",type:"checkbox",class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"},null,512),[[Te,t.value.remember]]),p[9]||(p[9]=e("label",{for:"remember-me",class:"ml-2 block text-sm text-gray-900"}," Ricordami ",-1))]),p[10]||(p[10]=e("div",{class:"text-sm"},[e("a",{href:"#",class:"font-medium text-primary-600 hover:text-primary-500"}," Password dimenticata? ")],-1))]),e("div",null,[e("button",{type:"submit",disabled:a.value,class:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"},[a.value?(s(),i("span",an,p[11]||(p[11]=[e("svg",{class:"h-5 w-5 text-primary-500 animate-spin",fill:"none",viewBox:"0 0 24 24"},[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1)]))):k("",!0),E(" "+c(a.value?"Accesso in corso...":"Accedi"),1)],8,rn)])],32)])])}}},ln={},dn={class:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8"};function cn(r,o){return s(),i("div",dn,o[0]||(o[0]=[e("div",{class:"max-w-md w-full space-y-8"},[e("div",null,[e("h2",{class:"mt-6 text-center text-3xl font-extrabold text-gray-900"}," Registra un nuovo account ")]),e("div",{class:"text-center text-gray-600"}," Registrazione in arrivo... ")],-1)]))}const un=Ee(ln,[["render",cn]]),mn={class:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"},pn={class:"p-5"},vn={class:"flex items-center"},gn={class:"ml-5 w-0 flex-1"},hn={class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"},fn={class:"text-lg font-medium text-gray-900 dark:text-white"},xn={key:0,class:"text-xs text-gray-500 dark:text-gray-400"},yn={key:0,class:"bg-gray-50 dark:bg-gray-700 px-5 py-3"},_n={class:"text-sm"},ae={__name:"StatsCard",props:{title:{type:String,required:!0},value:{type:[Number,String],required:!0},subtitle:{type:String,default:null},icon:{type:String,required:!0},color:{type:String,default:"primary"},link:{type:String,default:null}},setup(r){const o=t=>t==="project"?{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
      </svg>`}:t==="users"?{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
      </svg>`}:t==="clock"?{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>`}:t==="team"?{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
      </svg>`}:{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>`},l=t=>{const a={primary:"bg-primary-500",secondary:"bg-secondary-500",red:"bg-red-500",yellow:"bg-yellow-500",blue:"bg-blue-500",green:"bg-green-500"};return a[t]||a.primary};return(t,a)=>{const m=N("router-link");return s(),i("div",mn,[e("div",pn,[e("div",vn,[e("div",{class:S(["flex-shrink-0 rounded-md p-3",l(r.color)])},[(s(),q(Se(o(r.icon)),{class:"h-6 w-6 text-white"}))],2),e("div",gn,[e("dl",null,[e("dt",hn,c(r.title),1),e("dd",null,[e("div",fn,c(r.value),1),r.subtitle?(s(),i("div",xn,c(r.subtitle),1)):k("",!0)])])])])]),r.link?(s(),i("div",yn,[e("div",_n,[$(m,{to:r.link,class:"font-medium text-primary-600 dark:text-primary-400 hover:text-primary-500 dark:hover:text-primary-300"},{default:P(()=>a[0]||(a[0]=[E(" Vedi tutti ")])),_:1,__:[0]},8,["to"])])])):k("",!0)])}}},kn={class:"py-6"},bn={class:"flex flex-col md:flex-row md:items-center md:justify-between mb-8"},wn={class:"mt-4 md:mt-0 flex space-x-3"},$n={class:"relative"},Cn=["disabled"],jn={class:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"},Mn={class:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8"},zn={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Pn={class:"relative h-64"},Sn={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},An={class:"relative h-64"},En={class:"grid grid-cols-1 lg:grid-cols-3 gap-6"},In={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},Vn={class:"p-6"},Dn={key:0,class:"text-center py-8 text-gray-500"},Ln={key:1,class:"space-y-4"},Tn={class:"flex justify-between items-start"},Bn={class:"flex-1"},Hn={class:"text-sm font-medium text-gray-900 dark:text-white"},qn={class:"text-xs text-gray-500 dark:text-gray-400"},Rn={class:"mt-2 flex justify-between items-center"},Nn={class:"text-xs text-gray-500 dark:text-gray-400"},On={class:"bg-gray-50 dark:bg-gray-700 px-6 py-3"},Un={class:"text-sm"},Fn={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},Kn={class:"p-6"},Wn={key:0,class:"text-center py-8 text-gray-500"},Gn={key:1,class:"space-y-4"},Qn={class:"flex-shrink-0"},Jn={class:"flex-1 min-w-0"},Yn={class:"text-sm font-medium text-gray-900 dark:text-white"},Xn={class:"text-xs text-gray-500 dark:text-gray-400"},Zn={class:"text-xs text-gray-400 dark:text-gray-500"},ei={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},ti={class:"p-6"},si={key:0,class:"text-center py-8 text-gray-500"},oi={key:1,class:"space-y-4"},ri={class:"flex justify-between items-start"},ai={class:"flex-1"},ni={class:"text-sm font-medium text-gray-900 dark:text-white"},ii={class:"text-xs text-gray-500 dark:text-gray-400"},li={class:"text-right"},di={class:"text-sm font-bold text-gray-900 dark:text-white"},ci={class:"text-xs text-gray-500"},ui={class:"mt-2"},mi={class:"w-full bg-gray-200 rounded-full h-2"},pi={class:"text-xs text-gray-500 mt-1"},vi={__name:"Dashboard",setup(r){de.register(...Be),te(),Z();const o=C(!1),l=C("7"),t=C({}),a=C([]),m=C([]),u=C([]),v=C(null),p=C(null);let d=null,n=null;const g=async()=>{try{const y=await fetch("/api/dashboard/stats");if(!y.ok)throw new Error("Failed to fetch stats");const w=await y.json();t.value=w.data}catch(y){console.error("Error fetching dashboard stats:",y),t.value={}}},x=async()=>{try{const y=await fetch(`/api/dashboard/upcoming-tasks?days=${l.value}&limit=5`);if(!y.ok)throw new Error("Failed to fetch upcoming tasks");const w=await y.json();a.value=w.data.tasks}catch(y){console.error("Error fetching upcoming tasks:",y),a.value=[]}},_=async()=>{try{const y=await fetch("/api/dashboard/recent-activities?limit=5");if(!y.ok)throw new Error("Failed to fetch recent activities");const w=await y.json();m.value=w.data.activities}catch(y){console.error("Error fetching recent activities:",y),m.value=[]}},z=async()=>{try{const y=await fetch("/api/dashboard/kpis?limit=3");if(!y.ok)throw new Error("Failed to fetch KPIs");const w=await y.json();u.value=w.data.kpis}catch(y){console.error("Error fetching KPIs:",y),u.value=[]}},A=async()=>{try{const y=await fetch("/api/dashboard/charts/project-status");if(!y.ok)throw new Error("Failed to fetch project chart data");const w=await y.json();b(w.data.chart)}catch(y){console.error("Error fetching project chart:",y)}},j=async()=>{try{const y=await fetch("/api/dashboard/charts/task-status");if(!y.ok)throw new Error("Failed to fetch task chart data");const w=await y.json();B(w.data.chart)}catch(y){console.error("Error fetching task chart:",y)}},b=y=>{if(!v.value)return;const w=v.value.getContext("2d");d&&d.destroy(),d=new de(w,{type:"doughnut",data:{labels:y.labels,datasets:[{data:y.data,backgroundColor:["#3B82F6","#10B981","#F59E0B","#EF4444","#8B5CF6"],borderWidth:2,borderColor:"#ffffff"}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom",labels:{padding:20,usePointStyle:!0}}}}})},B=y=>{if(!p.value)return;const w=p.value.getContext("2d");n&&n.destroy(),n=new de(w,{type:"bar",data:{labels:y.labels,datasets:[{label:"Tasks",data:y.data,backgroundColor:["#60A5FA","#34D399","#FBBF24","#F87171"],borderColor:["#3B82F6","#10B981","#F59E0B","#EF4444"],borderWidth:1}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1}},scales:{y:{beginAtZero:!0,ticks:{stepSize:1}}}}})},J=async()=>{o.value=!0;try{await Promise.all([g(),x(),_(),z(),A(),j()])}finally{o.value=!1}},L=y=>new Date(y).toLocaleDateString("it-IT"),M=y=>{const w=new Date(y),G=Math.floor((new Date-w)/(1e3*60));return G<60?`${G} minuti fa`:G<1440?`${Math.floor(G/60)} ore fa`:`${Math.floor(G/1440)} giorni fa`},h=y=>{const w={high:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",medium:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",low:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"};return w[y]||w.medium},I=y=>{const w={todo:"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300","in-progress":"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",review:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",done:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"};return w[y]||w.todo},H=y=>{const w={task:{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
      </svg>`},timesheet:{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>`},event:{template:`<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
      </svg>`}};return w[y]||w.task},T=y=>{const w={task:"bg-blue-100 text-blue-600",timesheet:"bg-green-100 text-green-600",event:"bg-purple-100 text-purple-600"};return w[y]||w.task},V=y=>y>=90?"bg-green-500":y>=70?"bg-yellow-500":"bg-red-500";return Y(async()=>{await J(),await Pe(),v.value&&p.value&&(await A(),await j())}),(y,w)=>{var G,he,fe,xe,ye,_e,ke,be;const oe=N("router-link");return s(),i("div",kn,[e("div",bn,[w[4]||(w[4]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Dashboard"),e("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," Benvenuto! Ecco una panoramica delle attività della tua azienda. ")],-1)),e("div",wn,[e("div",$n,[W(e("select",{"onUpdate:modelValue":w[0]||(w[0]=D=>l.value=D),onChange:J,class:"bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md py-2 pl-3 pr-10 text-sm font-medium text-gray-700 dark:text-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500"},w[1]||(w[1]=[e("option",{value:"7"},"Ultimi 7 giorni",-1),e("option",{value:"30"},"Ultimo mese",-1),e("option",{value:"90"},"Ultimi 3 mesi",-1)]),544),[[ce,l.value]])]),e("button",{onClick:J,disabled:o.value,type:"button",class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"},[(s(),i("svg",{xmlns:"http://www.w3.org/2000/svg",class:S(["h-4 w-4 mr-2",{"animate-spin":o.value}]),viewBox:"0 0 20 20",fill:"currentColor"},w[2]||(w[2]=[e("path",{"fill-rule":"evenodd",d:"M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z","clip-rule":"evenodd"},null,-1)]),2)),w[3]||(w[3]=E(" Aggiorna "))],8,Cn)])]),e("div",jn,[$(ae,{title:"Progetti Attivi",value:((G=t.value.projects)==null?void 0:G.active)||0,subtitle:`di ${((he=t.value.projects)==null?void 0:he.total)||0} totali`,icon:"project",color:"primary",link:"/app/projects?status=active"},null,8,["value","subtitle"]),$(ae,{title:"Clienti",value:((fe=t.value.team)==null?void 0:fe.clients)||0,icon:"users",color:"secondary",link:"/app/crm/clients"},null,8,["value"]),$(ae,{title:"Task Pendenti",value:((xe=t.value.tasks)==null?void 0:xe.pending)||0,subtitle:`${((ye=t.value.tasks)==null?void 0:ye.overdue)||0} in ritardo`,icon:"clock",color:((_e=t.value.tasks)==null?void 0:_e.overdue)>0?"red":"yellow",link:"/app/tasks?status=pending"},null,8,["value","subtitle","color"]),$(ae,{title:"Team Members",value:((ke=t.value.team)==null?void 0:ke.users)||0,subtitle:`${((be=t.value.team)==null?void 0:be.departments)||0} dipartimenti`,icon:"team",color:"blue",link:"/app/personnel"},null,8,["value","subtitle"])]),e("div",Mn,[e("div",zn,[w[5]||(w[5]=e("div",{class:"flex items-center justify-between mb-4"},[e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Stato Progetti")],-1)),e("div",Pn,[e("canvas",{ref_key:"projectChart",ref:v},null,512)])]),e("div",Sn,[w[6]||(w[6]=e("div",{class:"flex items-center justify-between mb-4"},[e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Stato Attività")],-1)),e("div",An,[e("canvas",{ref_key:"taskChart",ref:p},null,512)])])]),e("div",En,[e("div",In,[e("div",Vn,[w[7]||(w[7]=e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Attività in Scadenza",-1)),a.value.length===0?(s(),i("div",Dn," Nessuna attività in scadenza ")):(s(),i("div",Ln,[(s(!0),i(U,null,F(a.value,D=>(s(),i("div",{key:D.id,class:"border-b border-gray-200 dark:border-gray-700 pb-4 last:border-b-0 last:pb-0"},[e("div",Tn,[e("div",Bn,[e("h3",Hn,c(D.name),1),e("p",qn,c(D.project_name),1)]),e("span",{class:S(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",h(D.priority)])},c(D.priority),3)]),e("div",Rn,[e("span",Nn," Scadenza: "+c(L(D.due_date)),1),e("span",{class:S(["inline-flex items-center px-2 py-1 rounded-full text-xs font-medium",I(D.status)])},c(D.status),3)])]))),128))]))]),e("div",On,[e("div",Un,[$(oe,{to:"/app/tasks",class:"font-medium text-primary-600 dark:text-primary-400 hover:text-primary-500"},{default:P(()=>w[8]||(w[8]=[E(" Vedi tutte le attività ")])),_:1,__:[8]})])])]),e("div",Fn,[e("div",Kn,[w[9]||(w[9]=e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Attività Recenti",-1)),m.value.length===0?(s(),i("div",Wn," Nessuna attività recente ")):(s(),i("div",Gn,[(s(!0),i(U,null,F(m.value,D=>(s(),i("div",{key:`${D.type}-${D.id}`,class:"flex items-start space-x-3"},[e("div",Qn,[e("div",{class:S(["w-8 h-8 rounded-full flex items-center justify-center",T(D.type)])},[(s(),q(Se(H(D.type)),{class:"w-4 h-4"}))],2)]),e("div",Jn,[e("p",Yn,c(D.title),1),e("p",Xn,c(D.description),1),e("p",Zn,c(M(D.timestamp)),1)])]))),128))]))])]),e("div",ei,[e("div",ti,[w[10]||(w[10]=e("h2",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"KPIs Principali",-1)),u.value.length===0?(s(),i("div",si," Nessun KPI configurato ")):(s(),i("div",oi,[(s(!0),i(U,null,F(u.value,D=>(s(),i("div",{key:D.id,class:"border-b border-gray-200 dark:border-gray-700 pb-4 last:border-b-0 last:pb-0"},[e("div",ri,[e("div",ai,[e("h3",ni,c(D.name),1),e("p",ii,c(D.description),1)]),e("div",li,[e("p",di,c(D.current_value)+c(D.unit),1),e("p",ci," Target: "+c(D.target_value)+c(D.unit),1)])]),e("div",ui,[e("div",mi,[e("div",{class:S(["h-2 rounded-full",V(D.performance_percentage)]),style:ve({width:Math.min(D.performance_percentage,100)+"%"})},null,6)]),e("p",pi,c(Math.round(D.performance_percentage))+"% del target",1)])]))),128))]))])])])])}}},gi=ue("projects",()=>{const r=C([]),o=C(null),l=C(!1),t=C(null),a=C(new Map),m=C({page:1,perPage:20,total:0,totalPages:0}),u=C({search:"",status:"",client:"",type:""}),v=f(()=>{let h=r.value;if(u.value.search){const I=u.value.search.toLowerCase();h=h.filter(H=>{var T,V,y;return H.name.toLowerCase().includes(I)||((T=H.description)==null?void 0:T.toLowerCase().includes(I))||((y=(V=H.client)==null?void 0:V.name)==null?void 0:y.toLowerCase().includes(I))})}return u.value.status&&(h=h.filter(I=>I.status===u.value.status)),u.value.client&&(h=h.filter(I=>I.client_id===u.value.client)),u.value.type&&(h=h.filter(I=>I.project_type===u.value.type)),h}),p=f(()=>{const h={};return r.value.forEach(I=>{h[I.status]||(h[I.status]=[]),h[I.status].push(I)}),h}),d=async(h={})=>{var I,H;l.value=!0,t.value=null;try{const T=new URLSearchParams({page:h.page||m.value.page,per_page:h.perPage||m.value.perPage,search:h.search||u.value.search,status:h.status||u.value.status,client:h.client||u.value.client,type:h.type||u.value.type}),V=await K.get(`/api/projects?${T}`);V.data.success&&(r.value=V.data.data.projects,m.value=V.data.data.pagination)}catch(T){t.value=((H=(I=T.response)==null?void 0:I.data)==null?void 0:H.message)||"Errore nel caricamento progetti",console.error("Error fetching projects:",T)}finally{l.value=!1}},n=async(h,I=!1)=>{var H,T;if(!I&&a.value.has(h)){const V=a.value.get(h);return o.value=V,V}l.value=!0,t.value=null;try{const V=await K.get(`/api/projects/${h}`);if(V.data.success){const y=V.data.data.project;return o.value=y,a.value.set(h,y),y}}catch(V){throw t.value=((T=(H=V.response)==null?void 0:H.data)==null?void 0:T.message)||"Errore nel caricamento progetto",console.error("Error fetching project:",V),V}finally{l.value=!1}};return{projects:r,currentProject:o,loading:l,error:t,pagination:m,filters:u,filteredProjects:v,projectsByStatus:p,fetchProjects:d,fetchProject:n,createProject:async h=>{var I,H;l.value=!0,t.value=null;try{const T=await K.post("/api/projects",h);if(T.data.success){const V=T.data.data.project;return r.value.unshift(V),V}}catch(T){throw t.value=((H=(I=T.response)==null?void 0:I.data)==null?void 0:H.message)||"Errore nella creazione progetto",console.error("Error creating project:",T),T}finally{l.value=!1}},updateProject:async(h,I)=>{var H,T,V;l.value=!0,t.value=null;try{const y=await K.put(`/api/projects/${h}`,I);if(y.data.success){const w=y.data.data.project,oe=r.value.findIndex(G=>G.id===h);return oe!==-1&&(r.value[oe]=w),((H=o.value)==null?void 0:H.id)===h&&(o.value=w),a.value.set(h,w),w}}catch(y){throw t.value=((V=(T=y.response)==null?void 0:T.data)==null?void 0:V.message)||"Errore nell'aggiornamento progetto",console.error("Error updating project:",y),y}finally{l.value=!1}},deleteProject:async h=>{var I,H,T;l.value=!0,t.value=null;try{(await K.delete(`/api/projects/${h}`)).data.success&&(r.value=r.value.filter(y=>y.id!==h),((I=o.value)==null?void 0:I.id)===h&&(o.value=null),a.value.delete(h))}catch(V){throw t.value=((T=(H=V.response)==null?void 0:H.data)==null?void 0:T.message)||"Errore nell'eliminazione progetto",console.error("Error deleting project:",V),V}finally{l.value=!1}},setFilters:h=>{u.value={...u.value,...h}},clearFilters:()=>{u.value={search:"",status:"",client:"",type:""}},setCurrentProject:h=>{o.value=h},clearCurrentProject:()=>{o.value=null},clearCache:()=>{a.value.clear()},refreshProject:async h=>await n(h,!0),getCachedProject:h=>a.value.get(h),$reset:()=>{r.value=[],o.value=null,l.value=!1,t.value=null,a.value.clear(),m.value={page:1,perPage:20,total:0,totalPages:0},u.value={search:"",status:"",client:"",type:""}}}}),hi={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6"},fi={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},xi=["value"],yi={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},_i={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},ki={class:"text-lg font-medium text-gray-900 dark:text-white"},bi={key:0,class:"p-6 text-center"},wi={key:1,class:"p-6 text-center"},$i={key:2,class:"divide-y divide-gray-200 dark:divide-gray-700"},Ci=["onClick"],ji={class:"flex items-center justify-between"},Mi={class:"flex-1"},zi={class:"flex items-center"},Pi={class:"text-lg font-medium text-gray-900 dark:text-white"},Si={class:"mt-1 text-sm text-gray-600 dark:text-gray-400"},Ai={class:"mt-2 flex items-center text-sm text-gray-500 dark:text-gray-400"},Ei={key:0},Ii={key:1,class:"mx-2"},Vi={key:2},Di={key:3,class:"mx-2"},Li={key:4},Ti={class:"ml-4 flex items-center space-x-2"},Bi={class:"text-right"},Hi={class:"text-sm font-medium text-gray-900 dark:text-white"},qi={class:"w-20 bg-gray-200 dark:bg-gray-600 rounded-full h-2 mt-1"},Ri={__name:"Projects",setup(r){const o=te(),l=gi(),t=C(!0),a=C(""),m=C({status:"",client:""}),u=f(()=>l.projects),v=C([]),p=f(()=>{let L=u.value;if(m.value.status&&(L=L.filter(M=>M.status===m.value.status)),m.value.client&&(L=L.filter(M=>M.client_id==m.value.client)),a.value){const M=a.value.toLowerCase();L=L.filter(h=>h.name.toLowerCase().includes(M)||h.description&&h.description.toLowerCase().includes(M)||h.client&&h.client.name&&h.client.name.toLowerCase().includes(M))}return L}),d=async()=>{t.value=!0;try{await l.fetchProjects(),v.value=[]}catch(L){console.error("Error loading projects:",L)}finally{t.value=!1}},n=()=>{},g=()=>{},x=()=>{m.value={status:"",client:""},a.value=""},_=()=>{o.push("/app/projects/create")},z=L=>{o.push(`/app/projects/${L}`)},A=L=>({planning:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400",active:"bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",completed:"bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400","on-hold":"bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400"})[L]||"bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400",j=L=>({planning:"Pianificazione",active:"Attivo",completed:"Completato","on-hold":"In Pausa"})[L]||L,b=L=>new Date(L).toLocaleDateString("it-IT"),B=L=>new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(L),J=L=>({planning:10,active:50,completed:100,"on-hold":25})[L.status]||0;return Y(()=>{d()}),(L,M)=>(s(),i("div",null,[e("div",{class:"mb-6"},[e("div",{class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},[M[4]||(M[4]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Progetti"),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"}," Gestisci e monitora tutti i progetti aziendali ")],-1)),e("div",{class:"mt-4 sm:mt-0"},[e("button",{onClick:_,class:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},M[3]||(M[3]=[e("svg",{class:"h-4 w-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4v16m8-8H4"})],-1),E(" Nuovo Progetto ")]))])])]),e("div",hi,[e("div",fi,[e("div",null,[M[6]||(M[6]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Stato",-1)),W(e("select",{"onUpdate:modelValue":M[0]||(M[0]=h=>m.value.status=h),onChange:g,class:"w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"},M[5]||(M[5]=[He('<option value="">Tutti gli stati</option><option value="planning">Pianificazione</option><option value="active">Attivo</option><option value="completed">Completato</option><option value="on-hold">In Pausa</option>',5)]),544),[[ce,m.value.status]])]),e("div",null,[M[8]||(M[8]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Cliente",-1)),W(e("select",{"onUpdate:modelValue":M[1]||(M[1]=h=>m.value.client=h),onChange:g,class:"w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"},[M[7]||(M[7]=e("option",{value:""},"Tutti i clienti",-1)),(s(!0),i(U,null,F(v.value,h=>(s(),i("option",{key:h.id,value:h.id},c(h.name),9,xi))),128))],544),[[ce,m.value.client]])]),e("div",null,[M[9]||(M[9]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Ricerca",-1)),W(e("input",{"onUpdate:modelValue":M[2]||(M[2]=h=>a.value=h),onInput:n,type:"text",placeholder:"Cerca progetti...",class:"w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"},null,544),[[ee,a.value]])]),e("div",{class:"flex items-end"},[e("button",{onClick:x,class:"w-full px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"}," Reset Filtri ")])])]),e("div",yi,[e("div",_i,[e("h3",ki," Progetti ("+c(p.value.length)+") ",1)]),t.value?(s(),i("div",bi,M[10]||(M[10]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500 mx-auto"},null,-1),e("p",{class:"mt-2 text-gray-600 dark:text-gray-400"},"Caricamento progetti...",-1)]))):p.value.length===0?(s(),i("div",wi,M[11]||(M[11]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun progetto",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"Inizia creando il tuo primo progetto.",-1)]))):(s(),i("div",$i,[(s(!0),i(U,null,F(p.value,h=>(s(),i("div",{key:h.id,class:"p-6 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer",onClick:I=>z(h.id)},[e("div",ji,[e("div",Mi,[e("div",zi,[e("h4",Pi,c(h.name),1),e("span",{class:S([A(h.status),"ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},c(j(h.status)),3)]),e("p",Si,c(h.description),1),e("div",Ai,[h.client?(s(),i("span",Ei,"Cliente: "+c(h.client.name),1)):k("",!0),h.client?(s(),i("span",Ii,"•")):k("",!0),h.end_date?(s(),i("span",Vi,"Scadenza: "+c(b(h.end_date)),1)):k("",!0),h.end_date&&h.budget?(s(),i("span",Di,"•")):k("",!0),h.budget?(s(),i("span",Li,"Budget: "+c(B(h.budget)),1)):k("",!0)])]),e("div",Ti,[e("div",Bi,[e("div",Hi,c(J(h))+"% ",1),e("div",qi,[e("div",{class:"bg-primary-600 h-2 rounded-full",style:ve({width:J(h)+"%"})},null,4)])]),M[12]||(M[12]=e("svg",{class:"h-5 w-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1))])])],8,Ci))),128))]))])]))}},Ni=[{path:"/",component:ze,children:[{path:"",name:"home",component:zr},{path:"about",name:"about",component:oa},{path:"contact",name:"contact",component:Ia},{path:"services",name:"services",component:Qa}]},{path:"/auth",component:ze,children:[{path:"login",name:"login",component:nn},{path:"register",name:"register",component:un}]},{path:"/app",component:jo,meta:{requiresAuth:!0},children:[{path:"",redirect:"/app/dashboard"},{path:"dashboard",name:"dashboard",component:vi,meta:{requiresAuth:!0,requiredPermission:"view_dashboard"}},{path:"projects",name:"projects",component:Ri,meta:{requiresAuth:!0,requiredPermission:"view_all_projects"}},{path:"projects/create",name:"projects-create",component:()=>R(()=>import("./ProjectCreate.js"),__vite__mapDeps([0,1])),meta:{requiresAuth:!0,requiredPermission:"edit_project"}},{path:"projects/:id",name:"project-view",component:()=>R(()=>import("./ProjectView.js"),__vite__mapDeps([2,1,3])),meta:{requiresAuth:!0,requiredPermission:"view_all_projects"}},{path:"projects/:id/edit",name:"project-edit",component:()=>R(()=>import("./ProjectEdit.js"),__vite__mapDeps([4,1])),meta:{requiresAuth:!0,requiredPermission:"edit_project"}},{path:"personnel",name:"personnel",component:()=>R(()=>import("./PersonnelDirectory.js"),__vite__mapDeps([5,1,6])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data"}},{path:"personnel/orgchart",name:"personnel-orgchart",component:()=>R(()=>import("./PersonnelOrgChart.js"),__vite__mapDeps([7,1])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data"}},{path:"personnel/skills",name:"personnel-skills",component:()=>R(()=>import("./SkillsMatrix.js"),__vite__mapDeps([8,1])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data"}},{path:"personnel/departments",name:"personnel-departments",component:()=>R(()=>import("./DepartmentList.js"),__vite__mapDeps([9,1,6])),meta:{requiresAuth:!0,requiredPermission:"manage_users"}},{path:"personnel/departments/create",name:"department-create",component:()=>R(()=>import("./DepartmentCreate.js"),__vite__mapDeps([10,1])),meta:{requiresAuth:!0,requiredPermission:"manage_users"}},{path:"personnel/departments/:id",name:"department-view",component:()=>R(()=>import("./DepartmentView.js"),__vite__mapDeps([11,1,6])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data"}},{path:"personnel/departments/:id/edit",name:"department-edit",component:()=>R(()=>import("./DepartmentEdit.js"),__vite__mapDeps([12,1])),meta:{requiresAuth:!0,requiredPermission:"manage_users"}},{path:"personnel/admin",name:"personnel-admin",component:()=>R(()=>import("./PersonnelAdmin.js"),__vite__mapDeps([13,1])),meta:{requiresAuth:!0,requiredPermission:"admin_access"}},{path:"personnel/:id",name:"personnel-profile",component:()=>R(()=>import("./PersonnelProfile.js"),__vite__mapDeps([14,1,6])),meta:{requiresAuth:!0,requiredPermission:"view_personnel_data"}},{path:"admin",redirect:"/app/admin/users"},{path:"admin/users",name:"admin-users",component:()=>R(()=>import("./Admin.js"),__vite__mapDeps([15,1])),meta:{requiresAuth:!0,requiredPermission:"admin_access"}},{path:"admin/kpi-templates",name:"admin-kpi-templates",component:()=>R(()=>import("./KPITemplates.js"),__vite__mapDeps([16,1])),meta:{requiresAuth:!0,requiredPermission:"admin_access"}},{path:"profile",name:"profile",component:()=>R(()=>import("./Profile.js"),__vite__mapDeps([17,1])),meta:{requiresAuth:!0}},{path:"settings",name:"settings",component:()=>R(()=>import("./Settings.js"),__vite__mapDeps([18,1])),meta:{requiresAuth:!0}}]}],Ie=qe({history:Re(),routes:Ni});Ie.beforeEach(async(r,o,l)=>{const t=Z();if(r.meta.requiresAuth){if(t.sessionChecked||await t.initializeAuth(),!t.isAuthenticated){l("/auth/login");return}if(r.meta.requiredPermission&&!t.hasPermission(r.meta.requiredPermission)){console.warn(`Accesso negato a ${r.path}: permesso '${r.meta.requiredPermission}' richiesto`),l("/app/dashboard");return}}l()});const le=Ne(Ke),Oi=Oe();le.use(Oi);le.use(Ie);const Ui=Z();Ui.initializeAuth().then(()=>{console.log("Auth initialized successfully"),le.mount("#app")}).catch(r=>{console.error("Auth initialization failed:",r),le.mount("#app")});export{Ee as _,Qe as a,gi as b,K as c,ge as d,Z as u};

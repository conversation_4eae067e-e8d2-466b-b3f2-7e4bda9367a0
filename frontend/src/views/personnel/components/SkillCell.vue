<template>
  <div @click="$emit('click')" 
       :class="[
         'skill-cell cursor-pointer rounded-lg p-2 transition-all duration-200',
         cellClasses
       ]">
    <!-- Skill Level Stars -->
    <div class="flex justify-center mb-1">
      <div class="flex space-x-0.5">
        <svg v-for="i in 5" 
             :key="i"
             :class="[
               'w-3 h-3',
               i <= userSkill.proficiency_level ? 'text-yellow-400' : 'text-gray-300 dark:text-gray-600'
             ]"
             fill="currentColor" 
             viewBox="0 0 20 20">
          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
        </svg>
      </div>
    </div>
    
    <!-- Level Number -->
    <div class="text-center">
      <span v-if="userSkill.proficiency_level > 0" 
            :class="[
              'text-xs font-medium px-1.5 py-0.5 rounded',
              levelClasses
            ]">
        {{ userSkill.proficiency_level }}
      </span>
      <span v-else class="text-xs text-gray-400 dark:text-gray-600">-</span>
    </div>
    
    <!-- Additional Info (Years/Certification) -->
    <div v-if="userSkill.proficiency_level > 0" class="mt-1 flex justify-center space-x-1">
      <!-- Years Experience -->
      <span v-if="userSkill.years_experience > 0" 
            class="text-xs text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-1 rounded">
        {{ userSkill.years_experience }}y
      </span>
      
      <!-- Certification Badge -->
      <span v-if="userSkill.is_certified" 
            class="text-xs text-green-700 dark:text-green-300 bg-green-100 dark:bg-green-900 px-1 rounded"
            :title="userSkill.certification_name || 'Certificato'">
        ✓
      </span>
      
      <!-- Assessment Type -->
      <span v-if="userSkill.manager_assessed" 
            class="text-xs text-blue-700 dark:text-blue-300 bg-blue-100 dark:bg-blue-900 px-1 rounded"
            title="Valutato dal manager">
        M
      </span>
      <span v-else-if="userSkill.self_assessed" 
            class="text-xs text-purple-700 dark:text-purple-300 bg-purple-100 dark:bg-purple-900 px-1 rounded"
            title="Auto-valutato">
        S
      </span>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

// Props
const props = defineProps({
  userSkill: {
    type: Object,
    required: true
  },
  skill: {
    type: Object,
    required: true
  }
})

// Emits
const emit = defineEmits(['click'])

// Computed properties
const cellClasses = computed(() => {
  const level = props.userSkill.proficiency_level
  
  if (level === 0) {
    return 'bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 border border-gray-200 dark:border-gray-700'
  }
  
  const baseClasses = 'hover:shadow-md transform hover:scale-105'
  
  switch (level) {
    case 1:
      return `${baseClasses} bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 hover:bg-red-100 dark:hover:bg-red-900/30`
    case 2:
      return `${baseClasses} bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 hover:bg-orange-100 dark:hover:bg-orange-900/30`
    case 3:
      return `${baseClasses} bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 hover:bg-yellow-100 dark:hover:bg-yellow-900/30`
    case 4:
      return `${baseClasses} bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 hover:bg-blue-100 dark:hover:bg-blue-900/30`
    case 5:
      return `${baseClasses} bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 hover:bg-green-100 dark:hover:bg-green-900/30`
    default:
      return `${baseClasses} bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700`
  }
})

const levelClasses = computed(() => {
  const level = props.userSkill.proficiency_level
  
  switch (level) {
    case 1:
      return 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200'
    case 2:
      return 'bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200'
    case 3:
      return 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200'
    case 4:
      return 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200'
    case 5:
      return 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200'
    default:
      return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200'
  }
})
</script>

<style scoped>
.skill-cell {
  min-height: 60px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.skill-cell:hover {
  z-index: 10;
  position: relative;
}

/* Tooltip styles */
.skill-cell[title]:hover::after {
  content: attr(title);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 20;
  margin-bottom: 4px;
}

.skill-cell[title]:hover::before {
  content: '';
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 4px solid transparent;
  border-top-color: rgba(0, 0, 0, 0.8);
  z-index: 20;
}
</style>

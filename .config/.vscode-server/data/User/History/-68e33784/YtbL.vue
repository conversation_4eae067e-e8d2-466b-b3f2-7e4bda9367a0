<template>
  <div class="personnel-profile">
    <!-- Loading State -->
    <div v-if="loading" class="flex justify-center items-center h-64">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
      <div class="flex">
        <svg class="w-5 h-5 text-red-400 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
        </svg>
        <div>
          <h3 class="text-sm font-medium text-red-800 dark:text-red-200">Errore nel caricamento del profilo</h3>
          <p class="text-sm text-red-700 dark:text-red-300 mt-1">{{ error }}</p>
        </div>
      </div>
    </div>

    <!-- Profile Content -->
    <div v-else-if="user" class="space-y-6">
      <!-- Header Section -->
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
        <div class="bg-gradient-to-r from-blue-500 to-purple-600 px-6 py-8">
          <div class="flex items-center space-x-6">
            <!-- Avatar -->
            <div class="flex-shrink-0">
              <div class="w-24 h-24 bg-white rounded-full flex items-center justify-center shadow-lg">
                <img v-if="user.profile_image"
                     :src="user.profile_image"
                     :alt="user.full_name"
                     class="w-24 h-24 rounded-full object-cover">
                <div v-else class="w-24 h-24 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                  <svg class="w-12 h-12 text-gray-400 dark:text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                  </svg>
                </div>
              </div>
            </div>

            <!-- User Info -->
            <div class="flex-1 text-white">
              <h1 class="text-3xl font-bold">{{ user.full_name }}</h1>
              <p class="text-blue-100 text-lg">{{ user.position || 'Posizione non specificata' }}</p>
              <div class="flex items-center space-x-4 mt-2">
                <span v-if="user.department" class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-white/20 text-white">
                  <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z" clip-rule="evenodd"></path>
                  </svg>
                  {{ user.department.name }}
                </span>
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-white/20 text-white">
                  <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                  </svg>
                  {{ user.role }}
                </span>
              </div>
            </div>

            <!-- Actions -->
            <div class="flex-shrink-0">
              <button v-if="canEdit"
                      @click="editMode = !editMode"
                      class="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                <svg class="w-5 h-5 inline mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"></path>
                </svg>
                {{ editMode ? 'Annulla' : 'Modifica' }}
              </button>
            </div>
          </div>
        </div>

        <!-- Profile Completion Bar -->
        <div v-if="user.profile && user.profile.profile_completion !== undefined" class="px-6 py-4 bg-gray-50 dark:bg-gray-700">
          <div class="flex items-center justify-between mb-2">
            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Completamento Profilo</span>
            <span class="text-sm text-gray-500 dark:text-gray-400">{{ user.profile.profile_completion }}%</span>
          </div>
          <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
            <div class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                 :style="{ width: user.profile.profile_completion + '%' }"></div>
          </div>
        </div>
      </div>

      <!-- Personal Information Cards - Horizontal Layout -->
      <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 mb-6">
          <!-- Contact Information -->
          <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-medium text-gray-900 dark:text-white">Informazioni di Contatto</h3>
              <button v-if="canEdit && !editMode"
                      @click="editMode = true"
                      class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"></path>
                </svg>
              </button>
            </div>

            <!-- View Mode -->
            <div v-if="!editMode" class="space-y-3">
              <div class="flex items-center">
                <svg class="w-5 h-5 text-gray-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                  <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                </svg>
                <span class="text-gray-900 dark:text-white">{{ user.email }}</span>
              </div>
              <div v-if="user.phone" class="flex items-center">
                <svg class="w-5 h-5 text-gray-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
                </svg>
                <span class="text-gray-900 dark:text-white">{{ user.phone }}</span>
              </div>
              <div v-if="user.hire_date" class="flex items-center">
                <svg class="w-5 h-5 text-gray-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                </svg>
                <span class="text-gray-900 dark:text-white">Assunto il {{ formatDate(user.hire_date) }}</span>
              </div>
            </div>

            <!-- Edit Mode -->
            <div v-else class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Telefono</label>
                <input v-model="editForm.phone"
                       type="tel"
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Bio</label>
                <textarea v-model="editForm.bio"
                          rows="3"
                          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"></textarea>
              </div>
              <div class="flex space-x-2">
                <button @click="saveProfile"
                        :disabled="saving"
                        class="flex-1 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-4 py-2 rounded-md transition-colors duration-200">
                  {{ saving ? 'Salvataggio...' : 'Salva' }}
                </button>
                <button @click="cancelEdit"
                        class="flex-1 bg-gray-300 hover:bg-gray-400 dark:bg-gray-600 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-200 px-4 py-2 rounded-md transition-colors duration-200">
                  Annulla
                </button>
              </div>
            </div>
          </div>

        <!-- Skills Overview -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Competenze Principali</h3>
          <div v-if="user.skills && user.skills.length > 0" class="space-y-3">
            <div v-for="skill in user.skills.slice(0, 4)" :key="skill.id" class="flex items-center justify-between">
              <span class="text-sm font-medium text-gray-900 dark:text-white">{{ skill.name }}</span>
              <div class="flex items-center space-x-2">
                <div class="flex space-x-1">
                  <div v-for="i in 5" :key="i"
                       class="w-2 h-2 rounded-full"
                       :class="i <= skill.proficiency_level ? 'bg-blue-500' : 'bg-gray-300 dark:bg-gray-600'"></div>
                </div>
                <span v-if="skill.certified" class="text-xs text-green-600 dark:text-green-400">✓</span>
              </div>
            </div>
            <div v-if="user.skills.length > 4" class="text-sm text-gray-500 dark:text-gray-400 text-center pt-2">
              +{{ user.skills.length - 4 }} altre competenze
            </div>
          </div>
          <div v-else class="text-gray-500 dark:text-gray-400 text-sm">
            Nessuna competenza registrata
          </div>
        </div>

          <!-- HR Information -->
          <div v-if="user.profile" class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-medium text-gray-900 dark:text-white">Informazioni HR</h3>
              <button v-if="canEdit && !editMode"
                      @click="editMode = true"
                      class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"></path>
                </svg>
              </button>
            </div>

            <!-- View Mode -->
            <div v-if="!editMode" class="space-y-3">
              <div v-if="user.profile.employee_id" class="flex justify-between">
                <span class="text-sm text-gray-500 dark:text-gray-400">ID Dipendente:</span>
                <span class="text-sm text-gray-900 dark:text-white">{{ user.profile.employee_id }}</span>
              </div>
              <div v-if="user.profile.job_title" class="flex justify-between">
                <span class="text-sm text-gray-500 dark:text-gray-400">Titolo:</span>
                <span class="text-sm text-gray-900 dark:text-white">{{ user.profile.job_title }}</span>
              </div>
              <div v-if="user.profile.employment_type" class="flex justify-between">
                <span class="text-sm text-gray-500 dark:text-gray-400">Tipo Contratto:</span>
                <span class="text-sm text-gray-900 dark:text-white">{{ getEmploymentTypeLabel(user.profile.employment_type) }}</span>
              </div>
              <div v-if="user.profile.work_location" class="flex justify-between">
                <span class="text-sm text-gray-500 dark:text-gray-400">Modalità Lavoro:</span>
                <span class="text-sm text-gray-900 dark:text-white">{{ getWorkLocationLabel(user.profile.work_location) }}</span>
              </div>
              <div v-if="user.profile.weekly_hours" class="flex justify-between">
                <span class="text-sm text-gray-500 dark:text-gray-400">Ore Settimanali:</span>
                <span class="text-sm text-gray-900 dark:text-white">{{ user.profile.weekly_hours }}h</span>
              </div>
            </div>

            <!-- Edit Mode HR -->
            <div v-else class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">ID Dipendente</label>
                <input v-model="editForm.employee_id"
                       type="text"
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Titolo Lavoro</label>
                <input v-model="editForm.job_title"
                       type="text"
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Tipo Contratto</label>
                <select v-model="editForm.employment_type"
                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                  <option value="">Seleziona tipo</option>
                  <option value="full_time">Tempo Pieno</option>
                  <option value="part_time">Part Time</option>
                  <option value="contractor">Consulente</option>
                  <option value="intern">Stagista</option>
                </select>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Modalità Lavoro</label>
                <select v-model="editForm.work_location"
                        class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                  <option value="">Seleziona modalità</option>
                  <option value="office">Ufficio</option>
                  <option value="remote">Remoto</option>
                  <option value="hybrid">Ibrido</option>
                </select>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Ore Settimanali</label>
                <input v-model="editForm.weekly_hours"
                       type="number"
                       min="1"
                       max="60"
                       step="0.5"
                       class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
              </div>

              <!-- Emergency Contact Section -->
              <div class="border-t border-gray-200 dark:border-gray-600 pt-4 mt-4">
                <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Contatto di Emergenza</h4>
                <div class="space-y-3">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Nome</label>
                    <input v-model="editForm.emergency_contact_name"
                           type="text"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Telefono</label>
                    <input v-model="editForm.emergency_contact_phone"
                           type="tel"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Relazione</label>
                    <input v-model="editForm.emergency_contact_relationship"
                           type="text"
                           placeholder="es. Coniuge, Genitore, Fratello"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                  </div>
                </div>
              </div>

              <!-- Address Section -->
              <div class="border-t border-gray-200 dark:border-gray-600 pt-4 mt-4">
                <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Indirizzo</h4>
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Indirizzo Completo</label>
                  <textarea v-model="editForm.address"
                            rows="2"
                            placeholder="Via, Città, CAP, Provincia"
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"></textarea>
                </div>
              </div>
            </div>
          </div>

      </div>

      <!-- Bio Section - Full Width -->
      <div v-if="user.bio || editMode" class="bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Bio</h3>
        <p v-if="!editMode" class="text-gray-700 dark:text-gray-300 text-sm leading-relaxed">{{ user.bio || 'Nessuna bio disponibile' }}</p>
      </div>

      <!-- Tabs Content - Full Width -->
      <div class="w-full">
          <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <!-- Tab Navigation -->
            <div class="border-b border-gray-200 dark:border-gray-700">
              <nav class="-mb-px flex space-x-8 px-6" aria-label="Tabs">
                <button v-for="tab in tabs" :key="tab.id"
                        @click="activeTab = tab.id"
                        :class="[
                          activeTab === tab.id
                            ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                            : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300',
                          'whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center'
                        ]">
                  <!-- Tab Icons -->
                  <svg v-if="tab.id === 'projects'" class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z" clip-rule="evenodd"></path>
                  </svg>
                  <svg v-else-if="tab.id === 'tasks'" class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z" clip-rule="evenodd"></path>
                  </svg>
                  <svg v-else-if="tab.id === 'skills'" class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                  </svg>
                  <svg v-else-if="tab.id === 'timesheet'" class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                  </svg>
                  <svg v-else-if="tab.id === 'cv'" class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path>
                  </svg>
                  {{ tab.name }}
                  <span v-if="tab.count !== undefined"
                        class="ml-2 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 py-0.5 px-2 rounded-full text-xs">
                    {{ tab.count }}
                  </span>
                </button>
              </nav>
            </div>

            <!-- Tab Content -->
            <div class="p-6">
              <!-- Projects Tab -->
              <div v-if="activeTab === 'projects'" class="space-y-4">
                <div v-if="userProjects.length > 0">
                  <div v-for="project in userProjects" :key="project.id"
                       class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                    <div class="flex items-center justify-between">
                      <div>
                        <h4 class="font-medium text-gray-900 dark:text-white">{{ project.name }}</h4>
                        <p class="text-sm text-gray-500 dark:text-gray-400">{{ project.role || 'Team Member' }}</p>
                      </div>
                      <span :class="[
                        'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                        project.status === 'active' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                        project.status === 'completed' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                        'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
                      ]">
                        {{ project.status }}
                      </span>
                    </div>
                  </div>
                </div>
                <div v-else class="text-center py-8">
                  <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                  </svg>
                  <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">Nessun progetto</h3>
                  <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">L'utente non è assegnato a nessun progetto.</p>
                </div>
              </div>

              <!-- Tasks Tab -->
              <div v-if="activeTab === 'tasks'" class="space-y-4">
                <div v-if="userTasks.length > 0">
                  <div v-for="task in userTasks" :key="task.id"
                       class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                    <div class="flex items-start justify-between">
                      <div class="flex-1">
                        <h4 class="font-medium text-gray-900 dark:text-white">{{ task.name }}</h4>
                        <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">{{ task.project_name }}</p>
                        <div class="flex items-center mt-2 space-x-4">
                          <span :class="[
                            'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
                            task.priority === 'urgent' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
                            task.priority === 'high' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200' :
                            task.priority === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                            'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
                          ]">
                            {{ task.priority }}
                          </span>
                          <span v-if="task.due_date" class="text-xs text-gray-500 dark:text-gray-400">
                            Scadenza: {{ formatDate(task.due_date) }}
                          </span>
                        </div>
                      </div>
                      <span :class="[
                        'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ml-4',
                        task.status === 'done' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                        task.status === 'in-progress' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                        task.status === 'review' ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200' :
                        'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
                      ]">
                        {{ task.status }}
                      </span>
                    </div>
                  </div>
                </div>
                <div v-else class="text-center py-8">
                  <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                  </svg>
                  <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">Nessun task</h3>
                  <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">L'utente non ha task assegnati.</p>
                </div>
              </div>

              <!-- Skills Tab -->
              <div v-if="activeTab === 'skills'" class="space-y-6">
                <div v-if="user.skills && user.skills.length > 0">
                  <!-- Skills Header with Stats -->
                  <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center space-x-4">
                      <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                        Competenze ({{ user.skills.length }})
                      </h3>
                      <span class="text-sm text-gray-500 dark:text-gray-400">
                        Pagina {{ skillsCurrentPage }} di {{ totalSkillsPages }}
                      </span>
                    </div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">
                      {{ user.skills.filter(s => s.certified).length }} certificate
                    </div>
                  </div>

                  <!-- Skills Grid -->
                  <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
                    <div v-for="skill in paginatedSkills" :key="skill.id"
                         class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow">
                      <div class="flex items-center justify-between mb-2">
                        <h4 class="font-medium text-gray-900 dark:text-white">{{ skill.name }}</h4>
                        <span v-if="skill.certified" class="text-green-600 dark:text-green-400 text-sm font-medium">✓ Certificato</span>
                      </div>
                      <p v-if="skill.category" class="text-sm text-gray-500 dark:text-gray-400 mb-3">{{ skill.category }}</p>
                      <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                          <span class="text-sm text-gray-600 dark:text-gray-400">Livello:</span>
                          <div class="flex space-x-1">
                            <div v-for="i in 5" :key="i"
                                 class="w-3 h-3 rounded-full"
                                 :class="i <= skill.proficiency_level ? 'bg-blue-500' : 'bg-gray-300 dark:bg-gray-600'"></div>
                          </div>
                          <span class="text-xs text-gray-500 dark:text-gray-400">({{ skill.proficiency_level }}/5)</span>
                        </div>
                        <span v-if="skill.years_experience" class="text-sm text-gray-500 dark:text-gray-400">
                          {{ skill.years_experience }}{{ skill.years_experience === 1 ? ' anno' : ' anni' }}
                        </span>
                      </div>
                    </div>
                  </div>

                  <!-- Pagination -->
                  <div v-if="totalSkillsPages > 1" class="flex items-center justify-between border-t border-gray-200 dark:border-gray-700 pt-4">
                    <div class="flex items-center space-x-2">
                      <button @click="skillsCurrentPage = Math.max(1, skillsCurrentPage - 1)"
                              :disabled="skillsCurrentPage === 1"
                              class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700">
                        Precedente
                      </button>
                      <span class="text-sm text-gray-700 dark:text-gray-300">
                        Pagina {{ skillsCurrentPage }} di {{ totalSkillsPages }}
                      </span>
                      <button @click="skillsCurrentPage = Math.min(totalSkillsPages, skillsCurrentPage + 1)"
                              :disabled="skillsCurrentPage === totalSkillsPages"
                              class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700">
                        Successiva
                      </button>
                    </div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">
                      Mostrando {{ ((skillsCurrentPage - 1) * skillsPerPage) + 1 }}-{{ Math.min(skillsCurrentPage * skillsPerPage, user.skills.length) }} di {{ user.skills.length }} competenze
                    </div>
                  </div>
                </div>
                <div v-else class="text-center py-8">
                  <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                  </svg>
                  <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">Nessuna competenza</h3>
                  <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Non sono state registrate competenze per questo utente.</p>
                </div>
              </div>

              <!-- Timesheet Tab -->
              <div v-if="activeTab === 'timesheet'" class="space-y-4">
                <div v-if="userTimesheets.length > 0">
                  <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                      <thead class="bg-gray-50 dark:bg-gray-700">
                        <tr>
                          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Data</th>
                          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Progetto</th>
                          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Task</th>
                          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Ore</th>
                          <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Descrizione</th>
                        </tr>
                      </thead>
                      <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        <tr v-for="timesheet in userTimesheets" :key="timesheet.id">
                          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                            {{ formatDate(timesheet.date) }}
                          </td>
                          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                            {{ timesheet.project_name }}
                          </td>
                          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                            {{ timesheet.task_name || '-' }}
                          </td>
                          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                            {{ timesheet.hours }}h
                          </td>
                          <td class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                            {{ timesheet.description || '-' }}
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
                <div v-else class="text-center py-8">
                  <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">Nessun timesheet</h3>
                  <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Non sono stati registrati timesheet per questo utente.</p>
                </div>
              </div>

              <!-- CV Tab -->
              <div v-if="activeTab === 'cv'" class="space-y-6">
                <!-- Current CV Section -->
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
                  <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">CV Attuale</h3>
                    <button v-if="canEdit"
                            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm transition-colors duration-200">
                      <svg class="w-4 h-4 inline mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                      </svg>
                      Carica CV
                    </button>
                  </div>

                  <div v-if="user.profile?.current_cv_path" class="flex items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <svg class="w-8 h-8 text-red-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path>
                    </svg>
                    <div class="flex-1">
                      <p class="text-sm font-medium text-gray-900 dark:text-white">CV_{{ user.full_name }}.pdf</p>
                      <p class="text-xs text-gray-500 dark:text-gray-400">
                        Caricato il {{ formatDate(user.profile.cv_last_updated) }}
                      </p>
                    </div>
                    <div class="flex space-x-2">
                      <button class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                          <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path>
                        </svg>
                      </button>
                      <button class="text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                      </button>
                    </div>
                  </div>

                  <div v-else class="text-center py-8">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">Nessun CV caricato</h3>
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Carica il tuo CV per completare il profilo.</p>
                  </div>
                </div>

                <!-- Documents Section -->
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
                  <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Documenti</h3>
                    <button v-if="canEdit"
                            class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm transition-colors duration-200">
                      <svg class="w-4 h-4 inline mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                      </svg>
                      Carica Documento
                    </button>
                  </div>

                  <div class="text-center py-8">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">Nessun documento</h3>
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">I documenti caricati appariranno qui.</p>
                  </div>
                </div>

                <!-- Profile Completion Tips -->
                <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                  <div class="flex">
                    <svg class="w-5 h-5 text-blue-400 mt-0.5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                    </svg>
                    <div>
                      <h3 class="text-sm font-medium text-blue-800 dark:text-blue-200">Suggerimenti per completare il profilo</h3>
                      <div class="mt-2 text-sm text-blue-700 dark:text-blue-300">
                        <ul class="list-disc list-inside space-y-1">
                          <li>Carica un CV aggiornato in formato PDF</li>
                          <li>Aggiungi certificazioni e documenti di formazione</li>
                          <li>Mantieni i documenti sempre aggiornati</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { usePersonnelStore } from '@/stores/personnel'
import { usePermissions } from '@/composables/usePermissions'

// Router
const route = useRoute()
const router = useRouter()

// Stores
const personnelStore = usePersonnelStore()
const { hasPermission } = usePermissions()

// Reactive state
const user = ref(null)
const userProjects = ref([])
const userTasks = ref([])
const userTimesheets = ref([])
const loading = ref(false)
const error = ref(null)
const editMode = ref(false)
const saving = ref(false)
const activeTab = ref('projects')

// Skills pagination
const skillsCurrentPage = ref(1)
const skillsPerPage = ref(6)

// Edit form
const editForm = ref({
  phone: '',
  bio: '',
  employee_id: '',
  job_title: '',
  employment_type: '',
  work_location: '',
  weekly_hours: 40,
  address: '',
  emergency_contact_name: '',
  emergency_contact_phone: '',
  emergency_contact_relationship: ''
})

// Computed properties
const canEdit = computed(() => {
  if (!user.value) return false
  try {
    // Fix: hasPermission is a computed that returns a function
    return hasPermission.value && typeof hasPermission.value === 'function' ? hasPermission.value('edit_personnel_data') : false
  } catch (e) {
    console.warn('Permission check failed:', e)
    return false
  }
})

// Skills pagination computed
const paginatedSkills = computed(() => {
  if (!user.value?.skills) return []
  const start = (skillsCurrentPage.value - 1) * skillsPerPage.value
  const end = start + skillsPerPage.value
  return user.value.skills.slice(start, end)
})

const totalSkillsPages = computed(() => {
  if (!user.value?.skills) return 0
  return Math.ceil(user.value.skills.length / skillsPerPage.value)
})

// Tab configuration
const tabs = computed(() => [
  {
    id: 'projects',
    name: 'Progetti',
    count: userProjects.value.length
  },
  {
    id: 'tasks',
    name: 'Task',
    count: userTasks.value.length
  },
  {
    id: 'skills',
    name: 'Competenze',
    count: user.value?.skills?.length || 0
  },
  {
    id: 'timesheet',
    name: 'Timesheet',
    count: userTimesheets.value.length
  },
  {
    id: 'cv',
    name: 'CV'
  }
])

// Utility functions
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('it-IT', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const formatDateTime = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString('it-IT')
}

const getPriorityColor = (priority) => {
  const colors = {
    'low': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
    'medium': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
    'high': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
  }
  return colors[priority] || colors['medium']
}

const getStatusColor = (status) => {
  const colors = {
    'active': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
    'completed': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
    'on_hold': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
    'cancelled': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
  }
  return colors[status] || colors['active']
}

const getEmploymentTypeLabel = (type) => {
  const labels = {
    'full_time': 'Tempo Pieno',
    'part_time': 'Part Time',
    'contractor': 'Consulente',
    'intern': 'Stagista'
  }
  return labels[type] || type
}

const getWorkLocationLabel = (location) => {
  const labels = {
    'office': 'Ufficio',
    'remote': 'Remoto',
    'hybrid': 'Ibrido'
  }
  return labels[location] || location
}

// Tab navigation
const switchTab = (tabId) => {
  activeTab.value = tabId
  // Reset skills pagination when switching to skills tab
  if (tabId === 'skills') {
    skillsCurrentPage.value = 1
  }
}

// Edit methods
const initEditForm = () => {
  if (!user.value) return

  editForm.value = {
    phone: user.value.phone || '',
    bio: user.value.bio || '',
    employee_id: user.value.profile?.employee_id || '',
    job_title: user.value.profile?.job_title || '',
    employment_type: user.value.profile?.employment_type || '',
    work_location: user.value.profile?.work_location || '',
    weekly_hours: user.value.profile?.weekly_hours || 40,
    address: user.value.profile?.address || '',
    emergency_contact_name: user.value.profile?.emergency_contact_name || '',
    emergency_contact_phone: user.value.profile?.emergency_contact_phone || '',
    emergency_contact_relationship: user.value.profile?.emergency_contact_relationship || ''
  }
}

const cancelEdit = () => {
  editMode.value = false
  initEditForm()
}

const saveProfile = async () => {
  if (!user.value) return

  saving.value = true
  try {
    const response = await fetch(`/api/personnel/users/${user.value.id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
      },
      body: JSON.stringify(editForm.value)
    })

    const data = await response.json()

    if (data.success) {
      // Update user data
      user.value = data.data.user
      editMode.value = false

      // Show success message
      console.log('Profilo aggiornato con successo')
    } else {
      throw new Error(data.message || 'Errore durante il salvataggio')
    }
  } catch (err) {
    console.error('Errore durante il salvataggio:', err)
    error.value = err.message
  } finally {
    saving.value = false
  }
}

// API functions
const fetchUserProfile = async (userId) => {
  loading.value = true
  error.value = null

  try {
    const response = await fetch(`/api/personnel/users/${userId}`, {
      credentials: 'include'
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()

    if (data.success) {
      user.value = data.data.user
      // Initialize edit form with user data
      initEditForm()
    } else {
      throw new Error(data.message || 'Errore nel caricamento del profilo')
    }
  } catch (err) {
    console.error('Error fetching user profile:', err)
    error.value = err.message
  } finally {
    loading.value = false
  }
}

const fetchUserTasks = async (userId) => {
  try {
    const response = await fetch(`/api/tasks?assignee_id=${userId}&limit=20`, {
      credentials: 'include'
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()

    if (data.success) {
      userTasks.value = data.data.tasks || []
    }
  } catch (err) {
    console.error('Error fetching user tasks:', err)
    // Non bloccare l'interfaccia per errori sui task
  }
}

const fetchUserTimesheets = async (userId) => {
  try {
    // Fetch timesheets for the last 30 days
    const endDate = new Date()
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - 30)

    const response = await fetch(`/api/timesheets?user_id=${userId}&start_date=${startDate.toISOString().split('T')[0]}&end_date=${endDate.toISOString().split('T')[0]}&limit=50`, {
      credentials: 'include'
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()

    if (data.success) {
      userTimesheets.value = data.data || []
    }
  } catch (err) {
    console.error('Error fetching user timesheets:', err)
    // Non bloccare l'interfaccia per errori sui timesheet
  }
}

// Lifecycle
onMounted(async () => {
  const userId = route.params.id

  if (!userId) {
    error.value = 'ID utente non specificato'
    return
  }

  // Fetch user profile
  await fetchUserProfile(userId)

  // Fetch related data
  if (user.value) {
    userProjects.value = user.value.projects || []
    await fetchUserTasks(userId)
    await fetchUserTimesheets(userId)
  }
})

// Watch for route changes
watch(() => route.params.id, async (newId) => {
  if (newId) {
    await fetchUserProfile(newId)
    if (user.value) {
      userProjects.value = user.value.projects || []
      await fetchUserTasks(newId)
      await fetchUserTimesheets(newId)
    }
  }
})
</script>

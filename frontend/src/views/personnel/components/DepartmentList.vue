<template>
  <div class="department-list">
    <!-- Department Header -->
    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mr-4">
            <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z" clip-rule="evenodd"></path>
            </svg>
          </div>
          
          <div>
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white">{{ department.name }}</h3>
            <p v-if="department.description" class="text-sm text-gray-500 dark:text-gray-400 mt-1">{{ department.description }}</p>
            <div class="flex items-center mt-2 space-x-6 text-sm text-gray-500 dark:text-gray-400">
              <span class="flex items-center">
                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"></path>
                </svg>
                {{ department.employee_count }} dipendenti
              </span>
              <span v-if="department.budget > 0" class="flex items-center">
                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"></path>
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.51-1.31c-.562-.649-1.413-1.076-2.353-1.253V5z" clip-rule="evenodd"></path>
                </svg>
                Budget: €{{ formatCurrency(department.budget) }}
              </span>
              <span v-if="department.subdepartments.length > 0" class="flex items-center">
                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z" clip-rule="evenodd"></path>
                </svg>
                {{ department.subdepartments.length }} sotto-dipartimenti
              </span>
            </div>
          </div>
        </div>
        
        <!-- Manager Info -->
        <div v-if="department.manager" class="flex items-center bg-purple-50 dark:bg-purple-900/20 rounded-lg p-3">
          <div class="w-10 h-10 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mr-3">
            <svg class="w-5 h-5 text-purple-600 dark:text-purple-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
            </svg>
          </div>
          <div>
            <p class="text-sm font-medium text-gray-900 dark:text-white">{{ department.manager.full_name }}</p>
            <p class="text-xs text-gray-500 dark:text-gray-400">Manager del Dipartimento</p>
            <p class="text-xs text-gray-500 dark:text-gray-400">{{ department.manager.email }}</p>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Employees Grid -->
    <div v-if="department.employees.length > 0" class="p-6">
      <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Dipendenti</h4>
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        <div v-for="employee in department.employees" 
             :key="employee.id"
             @click="$emit('employee-click', employee)"
             class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer transition-colors">
          <div class="flex items-center">
            <div class="w-10 h-10 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center mr-3">
              <svg class="w-5 h-5 text-gray-500 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-gray-900 dark:text-white truncate">{{ employee.full_name }}</p>
              <p class="text-xs text-gray-500 dark:text-gray-400 truncate">{{ employee.position || 'Dipendente' }}</p>
              <p class="text-xs text-gray-500 dark:text-gray-400 truncate">{{ employee.email }}</p>
            </div>
          </div>
          
          <div class="mt-3 flex items-center justify-between">
            <div class="flex items-center space-x-2">
              <span v-if="employee.is_manager" 
                    class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                Manager
              </span>
              <span :class="[
                'inline-flex items-center px-2 py-0.5 rounded text-xs font-medium',
                employee.role === 'admin' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
                employee.role === 'manager' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
              ]">
                {{ getRoleLabel(employee.role) }}
              </span>
            </div>
            
            <div v-if="employee.hire_date" class="text-xs text-gray-500 dark:text-gray-400">
              {{ formatDate(employee.hire_date) }}
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Subdepartments -->
    <div v-if="department.subdepartments.length > 0" class="border-t border-gray-200 dark:border-gray-700">
      <div class="p-6">
        <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Sotto-dipartimenti</h4>
        <div class="space-y-4">
          <DepartmentList 
            v-for="subdept in department.subdepartments" 
            :key="subdept.id"
            :department="subdept"
            :search-query="searchQuery"
            @employee-click="$emit('employee-click', $event)"
            class="ml-6 border-l-2 border-gray-200 dark:border-gray-700 pl-6"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// Props
const props = defineProps({
  department: {
    type: Object,
    required: true
  },
  searchQuery: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['employee-click'])

// Methods
const formatCurrency = (amount) => {
  return new Intl.NumberFormat('it-IT').format(amount)
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('it-IT', {
    year: 'numeric',
    month: 'short'
  })
}

const getRoleLabel = (role) => {
  const labels = {
    'admin': 'Admin',
    'manager': 'Manager',
    'employee': 'Dipendente'
  }
  return labels[role] || role
}
</script>

<style scoped>
.department-list {
  background: white;
}

.dark .department-list {
  background: rgb(31 41 55);
}
</style>

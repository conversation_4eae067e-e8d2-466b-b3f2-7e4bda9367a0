import { mount } from '@vue/test-utils'
import { createRouter, createWebHistory } from 'vue-router'
import { createPinia } from 'pinia'
import { vi } from 'vitest'

/**
 * Create a test router with mock routes
 */
export function createTestRouter(routes = []) {
  const defaultRoutes = [
    { path: '/', name: 'home', component: { template: '<div>Home</div>' } },
    { path: '/login', name: 'login', component: { template: '<div>Login</div>' } },
    { path: '/dashboard', name: 'dashboard', component: { template: '<div>Dashboard</div>' } },
    { path: '/personnel', name: 'personnel', component: { template: '<div>Personnel</div>' } },
    { path: '/projects', name: 'projects', component: { template: '<div>Projects</div>' } },
    ...routes
  ]

  return createRouter({
    history: createWebHistory(),
    routes: defaultRoutes
  })
}

/**
 * Create a test pinia store
 */
export function createTestPinia() {
  return createPinia()
}

/**
 * Mount a Vue component with common test setup
 */
export function mountComponent(component, options = {}) {
  const router = options.router || createTestRouter()
  const pinia = options.pinia || createTestPinia()

  const defaultOptions = {
    global: {
      plugins: [router, pinia],
      stubs: {
        RouterLink: true,
        RouterView: true,
        ...options.stubs
      },
      mocks: {
        $t: (key) => key, // Mock i18n
        ...options.mocks
      }
    },
    ...options
  }

  // Remove router and pinia from options to avoid duplication
  delete defaultOptions.router
  delete defaultOptions.pinia

  return mount(component, defaultOptions)
}

/**
 * Mock API responses for testing
 */
export function mockApiResponse(data, success = true, status = 200) {
  return {
    ok: success,
    status,
    json: async () => ({
      success,
      data,
      message: success ? 'Success' : 'Error'
    }),
    text: async () => success ? 'Success' : 'Error',
    headers: new Headers(),
  }
}

/**
 * Mock fetch with specific responses
 */
export function mockFetch(responses = {}) {
  fetch.mockImplementation((url, options) => {
    const method = options?.method || 'GET'
    const key = `${method} ${url}`
    
    if (responses[key]) {
      return Promise.resolve(responses[key])
    }
    
    if (responses[url]) {
      return Promise.resolve(responses[url])
    }
    
    // Default response
    return Promise.resolve(mockApiResponse({}))
  })
}

/**
 * Create mock user data
 */
export function createMockUser(overrides = {}) {
  return {
    id: 1,
    username: 'testuser',
    email: '<EMAIL>',
    first_name: 'Test',
    last_name: 'User',
    role: 'employee',
    is_active: true,
    department_id: 1,
    position: 'Developer',
    ...overrides
  }
}

/**
 * Create mock project data
 */
export function createMockProject(overrides = {}) {
  return {
    id: 1,
    name: 'Test Project',
    description: 'A test project',
    status: 'active',
    start_date: '2024-01-01',
    end_date: '2024-12-31',
    budget: 10000,
    expenses: 2000,
    client_id: 1,
    ...overrides
  }
}

/**
 * Create mock department data
 */
export function createMockDepartment(overrides = {}) {
  return {
    id: 1,
    name: 'Engineering',
    description: 'Software development team',
    manager_id: 1,
    user_count: 5,
    budget: 50000,
    ...overrides
  }
}

/**
 * Create mock skill data
 */
export function createMockSkill(overrides = {}) {
  return {
    id: 1,
    name: 'JavaScript',
    category: 'Programming',
    description: 'JavaScript programming language',
    user_count: 3,
    ...overrides
  }
}

/**
 * Wait for Vue's nextTick and any pending promises
 */
export async function flushPromises() {
  return new Promise(resolve => setTimeout(resolve, 0))
}

/**
 * Simulate user interaction with better timing
 */
export async function userEvent(wrapper, action) {
  await action()
  await wrapper.vm.$nextTick()
  await flushPromises()
}

/**
 * Mock console methods for testing
 */
export function mockConsole() {
  const originalConsole = { ...console }
  
  beforeEach(() => {
    vi.spyOn(console, 'log').mockImplementation(() => {})
    vi.spyOn(console, 'warn').mockImplementation(() => {})
    vi.spyOn(console, 'error').mockImplementation(() => {})
  })
  
  afterEach(() => {
    console.log.mockRestore()
    console.warn.mockRestore()
    console.error.mockRestore()
  })
  
  return originalConsole
}

/**
 * Create a mock store with common state
 */
export function createMockStore(initialState = {}) {
  return {
    auth: {
      user: createMockUser(),
      isAuthenticated: true,
      permissions: ['view_personnel', 'manage_users'],
      ...initialState.auth
    },
    personnel: {
      users: [createMockUser()],
      departments: [createMockDepartment()],
      skills: [createMockSkill()],
      ...initialState.personnel
    },
    projects: {
      projects: [createMockProject()],
      ...initialState.projects
    },
    ...initialState
  }
}

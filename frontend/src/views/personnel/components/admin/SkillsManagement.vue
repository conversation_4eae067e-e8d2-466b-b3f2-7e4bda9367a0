<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
      <div>
        <h3 class="text-lg font-medium text-gray-900 dark:text-white">Gestione Competenze</h3>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Gestisci il catalogo delle competenze aziendali
        </p>
      </div>
      <button class="mt-4 sm:mt-0 inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
        Nuova Competenza
      </button>
    </div>

    <!-- Content -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
      <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">Gestione Competenze</h3>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Funzionalità in fase di implementazione...
        </p>
        <div class="mt-4">
          <router-link to="/app/personnel/skills" 
                       class="text-blue-600 dark:text-blue-400 hover:underline">
            Vai alla matrice competenze esistente →
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// Emits
const emit = defineEmits(['skill-created', 'skill-updated', 'skill-deleted'])

// Placeholder component - will be implemented
</script>

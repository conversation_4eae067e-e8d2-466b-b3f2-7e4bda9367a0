<template>
  <!-- <PERSON><PERSON> Backdrop -->
  <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" @click="$emit('close')">
    <!-- Modal Container -->
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-lg shadow-lg rounded-md bg-white dark:bg-gray-800" @click.stop>
      <!-- <PERSON><PERSON> Header -->
      <div class="flex items-center justify-between pb-4 border-b border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white">
          {{ skill ? 'Modifica Competenza' : 'Nuova Competenza' }}
        </h3>
        <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <!-- Modal Body -->
      <div class="mt-6">
        <form @submit.prevent="saveSkill" class="space-y-4">
          <!-- Skill Name -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Nome Competenza *
            </label>
            <input v-model="form.name"
                   type="text"
                   required
                   placeholder="es. JavaScript, Project Management, Design Thinking"
                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
          </div>

          <!-- Category -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Categoria
            </label>
            <div class="flex space-x-2">
              <select v-model="form.category"
                      class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                <option value="">Seleziona categoria esistente</option>
                <option v-for="category in categories" :key="category" :value="category">
                  {{ category }}
                </option>
              </select>
              <button type="button"
                      @click="showNewCategory = !showNewCategory"
                      class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">
                {{ showNewCategory ? 'Annulla' : 'Nuova' }}
              </button>
            </div>
            
            <div v-if="showNewCategory" class="mt-2">
              <input v-model="newCategory"
                     type="text"
                     placeholder="Nome nuova categoria"
                     @blur="addNewCategory"
                     @keyup.enter="addNewCategory"
                     class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
            </div>
          </div>

          <!-- Description -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Descrizione
            </label>
            <textarea v-model="form.description"
                      rows="3"
                      placeholder="Descrizione della competenza e come viene utilizzata..."
                      class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"></textarea>
          </div>

          <!-- Error Message -->
          <div v-if="error" class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
            <div class="flex">
              <svg class="w-5 h-5 text-red-400 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
              </svg>
              <div>
                <h3 class="text-sm font-medium text-red-800 dark:text-red-200">Errore nel salvataggio</h3>
                <p class="text-sm text-red-700 dark:text-red-300 mt-1">{{ error }}</p>
              </div>
            </div>
          </div>

          <!-- Modal Footer -->
          <div class="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
            <button type="button"
                    @click="$emit('close')"
                    class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
              Annulla
            </button>
            <button type="submit"
                    :disabled="loading"
                    class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50">
              <svg v-if="loading" class="animate-spin -ml-1 mr-3 h-4 w-4 text-white inline" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {{ loading ? 'Salvataggio...' : (skill ? 'Aggiorna' : 'Crea') }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

// Props
const props = defineProps({
  skill: {
    type: Object,
    default: null
  },
  categories: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['close', 'saved'])

// Reactive state
const loading = ref(false)
const error = ref(null)
const showNewCategory = ref(false)
const newCategory = ref('')

// Form data
const form = ref({
  name: '',
  category: '',
  description: ''
})

// Methods
const addNewCategory = () => {
  if (newCategory.value.trim()) {
    form.value.category = newCategory.value.trim()
    newCategory.value = ''
    showNewCategory.value = false
  }
}

const saveSkill = async () => {
  loading.value = true
  error.value = null

  try {
    // Prepare form data
    const skillData = { ...form.value }
    
    // Convert empty strings to null for optional fields
    Object.keys(skillData).forEach(key => {
      if (skillData[key] === '') {
        skillData[key] = null
      }
    })

    const url = props.skill 
      ? `/api/personnel/skills/${props.skill.id}`
      : '/api/personnel/skills'
    
    const method = props.skill ? 'PUT' : 'POST'

    const response = await fetch(url, {
      method,
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include',
      body: JSON.stringify(skillData)
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()

    if (data.success) {
      emit('saved', data.data.skill)
    } else {
      throw new Error(data.message || 'Errore nel salvataggio competenza')
    }
  } catch (err) {
    console.error('Error saving skill:', err)
    error.value = err.message
  } finally {
    loading.value = false
  }
}

// Lifecycle
onMounted(() => {
  if (props.skill) {
    // Populate form with existing skill data
    form.value = {
      name: props.skill.name || '',
      category: props.skill.category || '',
      description: props.skill.description || ''
    }
  }
})
</script>

<style scoped>
/* Custom styles for modal */
.modal-container {
  max-height: 90vh;
  overflow-y: auto;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .modal-container {
    margin: 1rem;
    width: calc(100% - 2rem);
  }
}
</style>

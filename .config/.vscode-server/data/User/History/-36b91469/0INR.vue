<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
          <svg class="w-8 h-8 mr-3 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z" clip-rule="evenodd"></path>
          </svg>
          Organigramma Aziendale
        </h1>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Struttura organizzativa e gerarchia aziendale
        </p>
      </div>

      <!-- Controls -->
      <div class="mt-4 sm:mt-0 flex items-center space-x-3">
        <!-- Search -->
        <div class="relative">
          <input v-model="searchQuery"
                 type="text"
                 placeholder="Cerca dipendente..."
                 class="w-64 pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
          <svg class="absolute left-3 top-2.5 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
          </svg>
        </div>

        <!-- View Toggle -->
        <div class="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
          <button @click="viewMode = 'tree'"
                  :class="[
                    'px-3 py-1 rounded-md text-sm font-medium transition-colors',
                    viewMode === 'tree'
                      ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                      : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                  ]">
            <svg class="w-4 h-4 mr-1 inline" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z" clip-rule="evenodd"></path>
            </svg>
            Albero
          </button>
          <button @click="viewMode = 'list'"
                  :class="[
                    'px-3 py-1 rounded-md text-sm font-medium transition-colors',
                    viewMode === 'list'
                      ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                      : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                  ]">
            <svg class="w-4 h-4 mr-1 inline" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z" clip-rule="evenodd"></path>
            </svg>
            Lista
          </button>
        </div>

        <!-- Expand/Collapse All -->
        <button @click="toggleAllNodes"
                class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200">
          {{ allExpanded ? 'Comprimi Tutto' : 'Espandi Tutto' }}
        </button>
      </div>
    </div>

    <!-- Statistics Cards -->
    <div v-if="stats" class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"></path>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Dipendenti Totali</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ stats.total_employees }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="w-8 h-8 text-green-600" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z" clip-rule="evenodd"></path>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Dipartimenti</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ stats.total_departments }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="w-8 h-8 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Manager</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ stats.total_managers }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
      <div class="flex">
        <svg class="w-5 h-5 text-red-400 mr-3 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
        </svg>
        <div>
          <h3 class="text-sm font-medium text-red-800 dark:text-red-200">Errore nel caricamento</h3>
          <p class="mt-1 text-sm text-red-700 dark:text-red-300">{{ error }}</p>
        </div>
      </div>
    </div>

    <!-- Organization Chart Content -->
    <div v-else-if="orgChart.length > 0" class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
      <!-- Tree View -->
      <div v-if="viewMode === 'tree'" class="p-6">
        <div class="orgchart-container overflow-x-auto">
          <div class="orgchart-tree min-w-max">
            <DepartmentNode
              v-for="department in filteredOrgChart"
              :key="department.id"
              :department="department"
              :expanded="expandedNodes"
              :search-query="searchQuery"
              @toggle-node="toggleNode"
              @employee-click="onEmployeeClick"
            />
          </div>
        </div>
      </div>

      <!-- List View -->
      <div v-else class="divide-y divide-gray-200 dark:divide-gray-700">
        <div v-for="department in filteredOrgChart" :key="department.id">
          <DepartmentList
            :department="department"
            :search-query="searchQuery"
            @employee-click="onEmployeeClick"
          />
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-else-if="!loading" class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-12">
      <div class="text-center">
        <svg class="mx-auto h-16 w-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
        </svg>
        <h3 class="mt-4 text-lg font-medium text-gray-900 dark:text-white">Nessun dipartimento configurato</h3>
        <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
          Inizia creando la struttura organizzativa aziendale
        </p>
        <div class="mt-6">
          <router-link
            to="/app/personnel/departments"
            class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Gestisci Dipartimenti
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import DepartmentNode from './components/DepartmentNode.vue'
import DepartmentList from './components/DepartmentList.vue'

// Router
const router = useRouter()

// Reactive state
const loading = ref(false)
const error = ref(null)
const orgChart = ref([])
const stats = ref(null)
const searchQuery = ref('')
const viewMode = ref('tree') // 'tree' or 'list'
const expandedNodes = ref(new Set())
const allExpanded = ref(false)

// Computed properties
const filteredOrgChart = computed(() => {
  if (!searchQuery.value.trim()) {
    return orgChart.value
  }

  const query = searchQuery.value.toLowerCase()

  // Filter departments and employees based on search
  const filterDepartment = (dept) => {
    const matchesDept = dept.name.toLowerCase().includes(query) ||
                       (dept.description && dept.description.toLowerCase().includes(query))

    const matchingEmployees = dept.employees.filter(emp =>
      emp.full_name.toLowerCase().includes(query) ||
      emp.email.toLowerCase().includes(query) ||
      (emp.position && emp.position.toLowerCase().includes(query))
    )

    const filteredSubdepts = dept.subdepartments
      .map(filterDepartment)
      .filter(subdept => subdept !== null)

    // Include department if it matches, has matching employees, or has matching subdepartments
    if (matchesDept || matchingEmployees.length > 0 || filteredSubdepts.length > 0) {
      return {
        ...dept,
        employees: matchingEmployees.length > 0 ? matchingEmployees : dept.employees,
        subdepartments: filteredSubdepts
      }
    }

    return null
  }

  return orgChart.value
    .map(filterDepartment)
    .filter(dept => dept !== null)
})

// Methods
const loadOrgChart = async () => {
  loading.value = true
  error.value = null

  try {
    const response = await fetch('/api/personnel/orgchart', {
      credentials: 'include'
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()

    if (data.success) {
      orgChart.value = data.data.orgchart || []
      stats.value = data.data.stats || {}

      // Auto-expand first level by default
      if (orgChart.value.length > 0) {
        orgChart.value.forEach(dept => {
          expandedNodes.value.add(dept.id)
        })
      }
    } else {
      throw new Error(data.message || 'Errore nel caricamento dell\'organigramma')
    }
  } catch (err) {
    console.error('Error loading org chart:', err)
    error.value = err.message
  } finally {
    loading.value = false
  }
}

const toggleNode = (nodeId) => {
  if (expandedNodes.value.has(nodeId)) {
    expandedNodes.value.delete(nodeId)
  } else {
    expandedNodes.value.add(nodeId)
  }
}

const toggleAllNodes = () => {
  if (allExpanded.value) {
    // Collapse all
    expandedNodes.value.clear()
    allExpanded.value = false
  } else {
    // Expand all
    const expandAll = (departments) => {
      departments.forEach(dept => {
        expandedNodes.value.add(dept.id)
        if (dept.subdepartments && dept.subdepartments.length > 0) {
          expandAll(dept.subdepartments)
        }
      })
    }
    expandAll(orgChart.value)
    allExpanded.value = true
  }
}

const onEmployeeClick = (employee) => {
  // Navigate to employee profile
  router.push(`/app/personnel/profile/${employee.id}`)
}

// Lifecycle
onMounted(() => {
  loadOrgChart()
})
</script>

<style scoped>
.orgchart-container {
  padding: 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 12px;
  min-height: 400px;
}

.dark .orgchart-container {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
}

.orgchart-tree {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 1rem;
}

/* Connection lines for tree view */
.department-node {
  position: relative;
}

.department-node::before {
  content: '';
  position: absolute;
  left: -20px;
  top: 50%;
  width: 20px;
  height: 1px;
  background: #d1d5db;
  z-index: 1;
}

.dark .department-node::before {
  background: #4b5563;
}

.department-node:first-child::before {
  display: none;
}

/* Hover effects */
.department-header:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.dark .department-header:hover {
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
}

/* Smooth transitions */
.department-header {
  transition: all 0.3s ease;
}

/* Search highlighting */
.highlight {
  background-color: #fef3c7;
  padding: 2px 4px;
  border-radius: 4px;
}

.dark .highlight {
  background-color: #92400e;
  color: #fbbf24;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .orgchart-container {
    padding: 10px;
  }

  .department-header {
    min-width: 280px;
  }
}
</style>

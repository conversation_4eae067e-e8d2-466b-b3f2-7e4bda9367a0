<template>
  <div class="org-chart-diagram">
    <!-- Chart Container -->
    <div ref="chartContainer" class="chart-container bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-800 dark:to-gray-900 rounded-lg p-6 min-h-96">
      <!-- SVG will be inserted here by D3 -->
    </div>
    
    <!-- Chart Controls -->
    <div class="mt-4 flex justify-center space-x-4">
      <button @click="zoomIn" 
              class="px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
      </button>
      
      <button @click="zoomOut" 
              class="px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
        </svg>
      </button>
      
      <button @click="resetZoom" 
              class="px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors">
        Reset
      </button>
      
      <button @click="toggleLayout" 
              class="px-3 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors">
        {{ isVertical ? 'Layout Orizzontale' : 'Layout Verticale' }}
      </button>
    </div>
    
    <!-- Legend -->
    <div class="mt-4 bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
      <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Legenda</h4>
      <div class="flex flex-wrap gap-4 text-xs">
        <div class="flex items-center">
          <div class="w-4 h-4 bg-blue-500 rounded mr-2"></div>
          <span class="text-gray-600 dark:text-gray-400">Dipartimento</span>
        </div>
        <div class="flex items-center">
          <div class="w-4 h-4 bg-purple-500 rounded mr-2"></div>
          <span class="text-gray-600 dark:text-gray-400">Manager</span>
        </div>
        <div class="flex items-center">
          <div class="w-4 h-4 bg-green-500 rounded mr-2"></div>
          <span class="text-gray-600 dark:text-gray-400">Dipendente</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'

// Props
const props = defineProps({
  orgData: {
    type: Array,
    required: true
  },
  searchQuery: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits(['employee-click', 'department-click'])

// Reactive state
const chartContainer = ref(null)
const isVertical = ref(true)
let svg = null
let zoom = null
let simulation = null

// D3.js implementation (simplified version using basic DOM manipulation)
const createChart = () => {
  if (!chartContainer.value || !props.orgData.length) return

  // Clear previous chart
  chartContainer.value.innerHTML = ''

  // Create SVG
  const container = chartContainer.value
  const width = container.clientWidth
  const height = Math.max(600, container.clientHeight)

  svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg')
  svg.setAttribute('width', width)
  svg.setAttribute('height', height)
  svg.setAttribute('viewBox', `0 0 ${width} ${height}`)
  svg.style.background = 'transparent'

  // Create main group for zoom/pan
  const mainGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g')
  svg.appendChild(mainGroup)

  // Flatten org data for visualization
  const nodes = []
  const links = []

  const processNode = (dept, parent = null, level = 0) => {
    // Add department node
    const deptNode = {
      id: `dept-${dept.id}`,
      type: 'department',
      name: dept.name,
      description: dept.description,
      level,
      employees: dept.employees.length,
      budget: dept.budget,
      manager: dept.manager,
      x: 0,
      y: 0
    }
    nodes.push(deptNode)

    // Add link to parent
    if (parent) {
      links.push({
        source: parent.id,
        target: deptNode.id
      })
    }

    // Add employee nodes
    dept.employees.forEach((emp, index) => {
      const empNode = {
        id: `emp-${emp.id}`,
        type: 'employee',
        name: emp.full_name,
        position: emp.position,
        isManager: emp.is_manager,
        level: level + 1,
        x: 0,
        y: 0,
        employee: emp
      }
      nodes.push(empNode)

      // Link employee to department
      links.push({
        source: deptNode.id,
        target: empNode.id
      })
    })

    // Process subdepartments
    dept.subdepartments.forEach(subdept => {
      processNode(subdept, deptNode, level + 1)
    })
  }

  props.orgData.forEach(dept => processNode(dept))

  // Simple tree layout
  layoutNodes(nodes, width, height)

  // Draw links
  links.forEach(link => {
    const sourceNode = nodes.find(n => n.id === link.source)
    const targetNode = nodes.find(n => n.id === link.target)
    
    if (sourceNode && targetNode) {
      const line = document.createElementNS('http://www.w3.org/2000/svg', 'line')
      line.setAttribute('x1', sourceNode.x)
      line.setAttribute('y1', sourceNode.y)
      line.setAttribute('x2', targetNode.x)
      line.setAttribute('y2', targetNode.y)
      line.setAttribute('stroke', '#d1d5db')
      line.setAttribute('stroke-width', '2')
      mainGroup.appendChild(line)
    }
  })

  // Draw nodes
  nodes.forEach(node => {
    const group = document.createElementNS('http://www.w3.org/2000/svg', 'g')
    group.setAttribute('transform', `translate(${node.x}, ${node.y})`)
    group.style.cursor = 'pointer'

    // Node circle
    const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle')
    circle.setAttribute('r', node.type === 'department' ? '25' : '15')
    circle.setAttribute('fill', getNodeColor(node))
    circle.setAttribute('stroke', '#fff')
    circle.setAttribute('stroke-width', '2')
    group.appendChild(circle)

    // Node label
    const text = document.createElementNS('http://www.w3.org/2000/svg', 'text')
    text.setAttribute('text-anchor', 'middle')
    text.setAttribute('dy', node.type === 'department' ? '35' : '25')
    text.setAttribute('font-size', '12')
    text.setAttribute('fill', '#374151')
    text.textContent = truncateText(node.name, 15)
    group.appendChild(text)

    // Add click handler
    group.addEventListener('click', () => {
      if (node.type === 'department') {
        emit('department-click', { id: node.id.replace('dept-', ''), name: node.name })
      } else {
        emit('employee-click', node.employee)
      }
    })

    // Add hover effect
    group.addEventListener('mouseenter', () => {
      circle.setAttribute('stroke-width', '3')
      circle.setAttribute('stroke', '#3b82f6')
    })

    group.addEventListener('mouseleave', () => {
      circle.setAttribute('stroke-width', '2')
      circle.setAttribute('stroke', '#fff')
    })

    mainGroup.appendChild(group)
  })

  container.appendChild(svg)
}

const layoutNodes = (nodes, width, height) => {
  // Simple hierarchical layout
  const levels = {}
  
  // Group nodes by level
  nodes.forEach(node => {
    if (!levels[node.level]) levels[node.level] = []
    levels[node.level].push(node)
  })

  // Position nodes
  Object.keys(levels).forEach(level => {
    const levelNodes = levels[level]
    const levelY = isVertical.value 
      ? (parseInt(level) * (height / Object.keys(levels).length)) + 50
      : height / 2
    
    levelNodes.forEach((node, index) => {
      if (isVertical.value) {
        node.x = (width / (levelNodes.length + 1)) * (index + 1)
        node.y = levelY
      } else {
        node.x = (parseInt(level) * (width / Object.keys(levels).length)) + 50
        node.y = (height / (levelNodes.length + 1)) * (index + 1)
      }
    })
  })
}

const getNodeColor = (node) => {
  if (node.type === 'department') return '#3b82f6'
  if (node.isManager) return '#8b5cf6'
  return '#10b981'
}

const truncateText = (text, maxLength) => {
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text
}

const zoomIn = () => {
  if (svg) {
    const currentScale = getCurrentScale()
    applyZoom(currentScale * 1.2)
  }
}

const zoomOut = () => {
  if (svg) {
    const currentScale = getCurrentScale()
    applyZoom(currentScale * 0.8)
  }
}

const resetZoom = () => {
  if (svg) {
    applyZoom(1)
  }
}

const getCurrentScale = () => {
  const mainGroup = svg.querySelector('g')
  const transform = mainGroup.getAttribute('transform') || ''
  const scaleMatch = transform.match(/scale\(([^)]+)\)/)
  return scaleMatch ? parseFloat(scaleMatch[1]) : 1
}

const applyZoom = (scale) => {
  const mainGroup = svg.querySelector('g')
  mainGroup.setAttribute('transform', `scale(${scale})`)
}

const toggleLayout = () => {
  isVertical.value = !isVertical.value
  nextTick(() => {
    createChart()
  })
}

// Watch for data changes
watch(() => props.orgData, () => {
  nextTick(() => {
    createChart()
  })
}, { deep: true })

// Lifecycle
onMounted(() => {
  nextTick(() => {
    createChart()
  })
  
  // Handle window resize
  window.addEventListener('resize', createChart)
})

onUnmounted(() => {
  window.removeEventListener('resize', createChart)
})
</script>

<style scoped>
.org-chart-diagram {
  width: 100%;
}

.chart-container {
  width: 100%;
  overflow: hidden;
  position: relative;
}

.chart-container svg {
  display: block;
  margin: 0 auto;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .chart-container {
    min-height: 400px;
  }
}
</style>

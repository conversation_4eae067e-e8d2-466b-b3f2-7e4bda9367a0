{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/personnel/DepartmentView.vue"}, "originalCode": "<template>\n  <div class=\"department-view\">\n    <!-- Loading State -->\n    <div v-if=\"loading\" class=\"flex justify-center items-center h-64\">\n      <div class=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n    </div>\n\n    <!-- Error State -->\n    <div v-else-if=\"error\" class=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6\">\n      <div class=\"flex\">\n        <svg class=\"w-5 h-5 text-red-400 mr-2 mt-0.5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n          <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clip-rule=\"evenodd\"></path>\n        </svg>\n        <div>\n          <h3 class=\"text-sm font-medium text-red-800 dark:text-red-200\">Errore nel caricamento del dipartimento</h3>\n          <p class=\"text-sm text-red-700 dark:text-red-300 mt-1\">{{ error }}</p>\n        </div>\n      </div>\n    </div>\n\n    <!-- Department Content -->\n    <div v-else-if=\"department\" class=\"space-y-6\">\n      <!-- Header -->\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden\">\n        <div class=\"bg-gradient-to-r from-blue-500 to-purple-600 px-6 py-8\">\n          <div class=\"flex items-center justify-between\">\n            <div class=\"flex items-center space-x-4\">\n              <!-- Back Button -->\n              <router-link to=\"/app/personnel/departments\"\n                           class=\"text-white hover:text-blue-100 transition-colors duration-200\">\n                <svg class=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fill-rule=\"evenodd\" d=\"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z\" clip-rule=\"evenodd\"></path>\n                </svg>\n              </router-link>\n\n              <!-- Department Info -->\n              <div>\n                <div class=\"flex items-center space-x-3\">\n                  <div class=\"w-12 h-12 bg-white rounded-lg flex items-center justify-center shadow-lg\">\n                    <svg class=\"w-6 h-6 text-blue-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fill-rule=\"evenodd\" d=\"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z\" clip-rule=\"evenodd\"></path>\n                    </svg>\n                  </div>\n                  <div>\n                    <h1 class=\"text-3xl font-bold text-white\">{{ department.name }}</h1>\n                    <p v-if=\"department.description\" class=\"text-blue-100 text-lg\">{{ department.description }}</p>\n                  </div>\n                </div>\n\n                <!-- Department Metadata -->\n                <div class=\"flex items-center space-x-6 mt-4\">\n                  <div v-if=\"department.manager\" class=\"flex items-center text-white\">\n                    <svg class=\"w-5 h-5 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fill-rule=\"evenodd\" d=\"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\" clip-rule=\"evenodd\"></path>\n                    </svg>\n                    <span class=\"text-sm\">Manager: {{ department.manager.full_name }}</span>\n                  </div>\n                  <div class=\"flex items-center text-white\">\n                    <svg class=\"w-5 h-5 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path d=\"M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z\"></path>\n                    </svg>\n                    <span class=\"text-sm\">{{ department.employee_count }} dipendenti</span>\n                  </div>\n                  <div v-if=\"department.budget\" class=\"flex items-center text-white\">\n                    <svg class=\"w-5 h-5 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path d=\"M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z\"></path>\n                      <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z\" clip-rule=\"evenodd\"></path>\n                    </svg>\n                    <span class=\"text-sm\">Budget: {{ formatCurrency(department.budget) }}</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <!-- Actions -->\n            <div v-if=\"canManageDepartments\" class=\"flex space-x-3\">\n              <button @click=\"editMode = !editMode\"\n                      class=\"bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-colors duration-200\">\n                <svg class=\"w-5 h-5 inline mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path d=\"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z\"></path>\n                </svg>\n                {{ editMode ? 'Annulla' : 'Modifica' }}\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n<script setup>\n", "modifiedCode": "<template>\n  <div class=\"department-view\">\n    <!-- Loading State -->\n    <div v-if=\"loading\" class=\"flex justify-center items-center h-64\">\n      <div class=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n    </div>\n\n    <!-- Error State -->\n    <div v-else-if=\"error\" class=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6\">\n      <div class=\"flex\">\n        <svg class=\"w-5 h-5 text-red-400 mr-2 mt-0.5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n          <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clip-rule=\"evenodd\"></path>\n        </svg>\n        <div>\n          <h3 class=\"text-sm font-medium text-red-800 dark:text-red-200\">Errore nel caricamento del dipartimento</h3>\n          <p class=\"text-sm text-red-700 dark:text-red-300 mt-1\">{{ error }}</p>\n        </div>\n      </div>\n    </div>\n\n    <!-- Department Content -->\n    <div v-else-if=\"department\" class=\"space-y-6\">\n      <!-- Header -->\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden\">\n        <div class=\"bg-gradient-to-r from-blue-500 to-purple-600 px-6 py-8\">\n          <div class=\"flex items-center justify-between\">\n            <div class=\"flex items-center space-x-4\">\n              <!-- Back Button -->\n              <router-link to=\"/app/personnel/departments\"\n                           class=\"text-white hover:text-blue-100 transition-colors duration-200\">\n                <svg class=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fill-rule=\"evenodd\" d=\"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z\" clip-rule=\"evenodd\"></path>\n                </svg>\n              </router-link>\n\n              <!-- Department Info -->\n              <div>\n                <div class=\"flex items-center space-x-3\">\n                  <div class=\"w-12 h-12 bg-white rounded-lg flex items-center justify-center shadow-lg\">\n                    <svg class=\"w-6 h-6 text-blue-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fill-rule=\"evenodd\" d=\"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z\" clip-rule=\"evenodd\"></path>\n                    </svg>\n                  </div>\n                  <div>\n                    <h1 class=\"text-3xl font-bold text-white\">{{ department.name }}</h1>\n                    <p v-if=\"department.description\" class=\"text-blue-100 text-lg\">{{ department.description }}</p>\n                  </div>\n                </div>\n\n                <!-- Department Metadata -->\n                <div class=\"flex items-center space-x-6 mt-4\">\n                  <div v-if=\"department.manager\" class=\"flex items-center text-white\">\n                    <svg class=\"w-5 h-5 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fill-rule=\"evenodd\" d=\"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\" clip-rule=\"evenodd\"></path>\n                    </svg>\n                    <span class=\"text-sm\">Manager: {{ department.manager.full_name }}</span>\n                  </div>\n                  <div class=\"flex items-center text-white\">\n                    <svg class=\"w-5 h-5 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path d=\"M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z\"></path>\n                    </svg>\n                    <span class=\"text-sm\">{{ department.employee_count }} dipendenti</span>\n                  </div>\n                  <div v-if=\"department.budget\" class=\"flex items-center text-white\">\n                    <svg class=\"w-5 h-5 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path d=\"M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z\"></path>\n                      <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z\" clip-rule=\"evenodd\"></path>\n                    </svg>\n                    <span class=\"text-sm\">Budget: {{ formatCurrency(department.budget) }}</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <!-- Actions -->\n            <div v-if=\"canManageDepartments\" class=\"flex space-x-3\">\n              <button @click=\"editMode = !editMode\"\n                      class=\"bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-colors duration-200\">\n                <svg class=\"w-5 h-5 inline mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path d=\"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z\"></path>\n                </svg>\n                {{ editMode ? 'Annulla' : 'Modifica' }}\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Tab Navigation -->\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden\">\n        <div class=\"border-b border-gray-200 dark:border-gray-700\">\n          <nav class=\"-mb-px flex space-x-8 px-6\" aria-label=\"Tabs\">\n            <button v-for=\"tab in tabs\" :key=\"tab.id\"\n                    @click=\"activeTab = tab.id\"\n                    :class=\"[\n                      activeTab === tab.id\n                        ? 'border-blue-500 text-blue-600 dark:text-blue-400'\n                        : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300',\n                      'whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center'\n                    ]\">\n              <!-- Tab Icons -->\n              <svg v-if=\"tab.id === 'employees'\" class=\"w-5 h-5 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path d=\"M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z\"></path>\n              </svg>\n              <svg v-else-if=\"tab.id === 'subdepartments'\" class=\"w-5 h-5 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fill-rule=\"evenodd\" d=\"M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 0l-2 2a1 1 0 101.414 1.414L8 10.414l1.293 1.293a1 1 0 001.414 0l4-4z\" clip-rule=\"evenodd\"></path>\n              </svg>\n              {{ tab.name }}\n              <span v-if=\"tab.count !== undefined\"\n                    class=\"ml-2 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 py-0.5 px-2 rounded-full text-xs\">\n                {{ tab.count }}\n              </span>\n            </button>\n          </nav>\n        </div>\n\n        <!-- Tab Content -->\n        <div class=\"p-6\">\n          <!-- Employees Tab -->\n          <div v-if=\"activeTab === 'employees'\">\n            <div v-if=\"department.employees && department.employees.length > 0\" class=\"space-y-4\">\n              <div v-for=\"employee in department.employees\" :key=\"employee.id\"\n                   class=\"border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\">\n                <div class=\"flex items-center justify-between\">\n                  <div class=\"flex items-center space-x-4\">\n                    <div class=\"w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center\">\n                      <svg class=\"w-5 h-5 text-gray-600 dark:text-gray-300\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fill-rule=\"evenodd\" d=\"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\" clip-rule=\"evenodd\"></path>\n                      </svg>\n                    </div>\n                    <div>\n                      <h4 class=\"font-medium text-gray-900 dark:text-white\">{{ employee.full_name }}</h4>\n                      <p class=\"text-sm text-gray-500 dark:text-gray-400\">{{ employee.position || 'Posizione non specificata' }}</p>\n                      <p class=\"text-sm text-gray-500 dark:text-gray-400\">{{ employee.email }}</p>\n                    </div>\n                  </div>\n                  <div class=\"flex items-center space-x-2\">\n                    <span :class=\"[\n                      'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',\n                      employee.is_active ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'\n                    ]\">\n                      {{ employee.is_active ? 'Attivo' : 'Inattivo' }}\n                    </span>\n                    <router-link :to=\"`/app/personnel/profile/${employee.id}`\"\n                                 class=\"text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300\">\n                      <svg class=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path d=\"M10 12a2 2 0 100-4 2 2 0 000 4z\"></path>\n                        <path fill-rule=\"evenodd\" d=\"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z\" clip-rule=\"evenodd\"></path>\n                      </svg>\n                    </router-link>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div v-else class=\"text-center py-8\">\n              <svg class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\n              </svg>\n              <h3 class=\"mt-2 text-sm font-medium text-gray-900 dark:text-white\">Nessun dipendente</h3>\n              <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">Non ci sono dipendenti assegnati a questo dipartimento.</p>\n            </div>\n          </div>\n\n          <!-- Subdepartments Tab -->\n          <div v-if=\"activeTab === 'subdepartments'\">\n            <div v-if=\"department.subdepartments && department.subdepartments.length > 0\" class=\"space-y-4\">\n              <div v-for=\"subdept in department.subdepartments\" :key=\"subdept.id\"\n                   class=\"border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\">\n                <div class=\"flex items-center justify-between\">\n                  <div class=\"flex items-center space-x-4\">\n                    <div class=\"w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center\">\n                      <svg class=\"w-5 h-5 text-blue-600 dark:text-blue-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fill-rule=\"evenodd\" d=\"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z\" clip-rule=\"evenodd\"></path>\n                      </svg>\n                    </div>\n                    <div>\n                      <h4 class=\"font-medium text-gray-900 dark:text-white\">{{ subdept.name }}</h4>\n                      <p class=\"text-sm text-gray-500 dark:text-gray-400\">{{ subdept.employee_count }} dipendenti</p>\n                    </div>\n                  </div>\n                  <router-link :to=\"`/app/personnel/departments/${subdept.id}`\"\n                               class=\"text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300\">\n                    <svg class=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path d=\"M10 12a2 2 0 100-4 2 2 0 000 4z\"></path>\n                      <path fill-rule=\"evenodd\" d=\"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z\" clip-rule=\"evenodd\"></path>\n                    </svg>\n                  </router-link>\n                </div>\n              </div>\n            </div>\n            <div v-else class=\"text-center py-8\">\n              <svg class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\" />\n              </svg>\n              <h3 class=\"mt-2 text-sm font-medium text-gray-900 dark:text-white\">Nessun sotto-dipartimento</h3>\n              <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">Non ci sono sotto-dipartimenti per questo dipartimento.</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\n"}
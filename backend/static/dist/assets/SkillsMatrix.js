import{f as V,c as a,o as l,j as e,g as h,F as k,k as f,n as z,t as s,r as y,A as H,m as B,v as _,H as C,I as M,a as U}from"./vendor.js";import{_ as L}from"./app.js";const N={class:"flex justify-center mb-1"},P={class:"flex space-x-0.5"},T={class:"text-center"},I={key:1,class:"text-xs text-gray-400 dark:text-gray-600"},O={key:0,class:"mt-1 flex justify-center space-x-1"},F={key:0,class:"text-xs text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-1 rounded"},R=["title"],q={key:2,class:"text-xs text-blue-700 dark:text-blue-300 bg-blue-100 dark:bg-blue-900 px-1 rounded",title:"Valutato dal manager"},Q={key:3,class:"text-xs text-purple-700 dark:text-purple-300 bg-purple-100 dark:bg-purple-900 px-1 rounded",title:"Auto-valutato"},G={__name:"SkillCell",props:{userSkill:{type:Object,required:!0},skill:{type:Object,required:!0}},emits:["click"],setup(i,{emit:p}){const b=i,m=V(()=>{const n=b.userSkill.proficiency_level;if(n===0)return"bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 border border-gray-200 dark:border-gray-700";const o="hover:shadow-md transform hover:scale-105";switch(n){case 1:return`${o} bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 hover:bg-red-100 dark:hover:bg-red-900/30`;case 2:return`${o} bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 hover:bg-orange-100 dark:hover:bg-orange-900/30`;case 3:return`${o} bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 hover:bg-yellow-100 dark:hover:bg-yellow-900/30`;case 4:return`${o} bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 hover:bg-blue-100 dark:hover:bg-blue-900/30`;case 5:return`${o} bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 hover:bg-green-100 dark:hover:bg-green-900/30`;default:return`${o} bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700`}}),u=V(()=>{switch(b.userSkill.proficiency_level){case 1:return"bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200";case 2:return"bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200";case 3:return"bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200";case 4:return"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200";case 5:return"bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200";default:return"bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200"}});return(n,o)=>(l(),a("div",{onClick:o[0]||(o[0]=d=>n.$emit("click")),class:z(["skill-cell cursor-pointer rounded-lg p-2 transition-all duration-200",m.value])},[e("div",N,[e("div",P,[(l(),a(k,null,f(5,d=>e("svg",{key:d,class:z(["w-3 h-3",d<=i.userSkill.proficiency_level?"text-yellow-400":"text-gray-300 dark:text-gray-600"]),fill:"currentColor",viewBox:"0 0 20 20"},o[1]||(o[1]=[e("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"},null,-1)]),2)),64))])]),e("div",T,[i.userSkill.proficiency_level>0?(l(),a("span",{key:0,class:z(["text-xs font-medium px-1.5 py-0.5 rounded",u.value])},s(i.userSkill.proficiency_level),3)):(l(),a("span",I,"-"))]),i.userSkill.proficiency_level>0?(l(),a("div",O,[i.userSkill.years_experience>0?(l(),a("span",F,s(i.userSkill.years_experience)+"y ",1)):h("",!0),i.userSkill.is_certified?(l(),a("span",{key:1,class:"text-xs text-green-700 dark:text-green-300 bg-green-100 dark:bg-green-900 px-1 rounded",title:i.userSkill.certification_name||"Certificato"}," ✓ ",8,R)):h("",!0),i.userSkill.manager_assessed?(l(),a("span",q," M ")):i.userSkill.self_assessed?(l(),a("span",Q," S ")):h("",!0)])):h("",!0)],2))}},J=L(G,[["__scopeId","data-v-4c10f004"]]),K={class:"space-y-6"},W={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},X={class:"mt-4 sm:mt-0"},Y=["disabled"],Z={class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"},ee={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},te=["value"],re=["value"],ae={key:0,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"},le={class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"},se={class:"flex items-center"},oe={class:"ml-4"},de={class:"text-2xl font-bold text-gray-900 dark:text-white"},ie={class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"},ne={class:"flex items-center"},ge={class:"ml-4"},ce={class:"text-2xl font-bold text-gray-900 dark:text-white"},ue={class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"},ve={class:"flex items-center"},xe={class:"ml-4"},be={class:"text-2xl font-bold text-gray-900 dark:text-white"},me={class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"},ye={class:"flex items-center"},pe={class:"ml-4"},ke={class:"text-2xl font-bold text-gray-900 dark:text-white"},fe={key:1,class:"flex justify-center items-center py-12"},he={key:2,class:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4"},we={class:"flex"},_e={class:"mt-1 text-sm text-red-700 dark:text-red-300"},Ce={key:3,class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden"},Se={class:"overflow-x-auto"},ze={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},Me={class:"bg-gray-50 dark:bg-gray-700"},$e={class:"flex flex-col items-center"},Ve={class:"mb-1"},Be={class:"text-xs text-gray-400 dark:text-gray-500 normal-case"},Le={class:"mt-1 flex items-center space-x-1"},je={class:"text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-1 rounded"},Ee={class:"text-xs text-gray-400"},Ae={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},De={class:"sticky left-0 z-10 bg-white dark:bg-gray-800 px-6 py-4 whitespace-nowrap border-r border-gray-200 dark:border-gray-600"},He={class:"flex items-center"},Ue={class:"text-sm font-medium text-gray-900 dark:text-white"},Ne={class:"text-sm text-gray-500 dark:text-gray-400"},Pe={class:"text-xs text-gray-400 dark:text-gray-500"},Te={key:4,class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-12"},Ie={__name:"SkillsMatrix",setup(i){const p=y(!1),b=y(null),m=y([]),u=y([]),n=y(null),o=y({departments:[],categories:[]}),d=y({department_id:"",category:"",min_level:"",max_level:""}),w=async()=>{p.value=!0,b.value=null;try{const g=new URLSearchParams;Object.entries(d.value).forEach(([v,c])=>{c&&g.append(v,c)});const t=await fetch(`/api/personnel/skills-matrix?${g}`,{credentials:"include"});if(!t.ok)throw new Error(`HTTP ${t.status}: ${t.statusText}`);const r=await t.json();if(r.success)m.value=r.data.matrix||[],u.value=r.data.skills_summary||[],n.value=r.data.stats||{},o.value=r.data.filters||{departments:[],categories:[]};else throw new Error(r.message||"Errore nel caricamento della matrice competenze")}catch(g){console.error("Error loading skills matrix:",g),b.value=g.message}finally{p.value=!1}},$=(g,t)=>g.skills.find(r=>r.skill_id===t)||{skill_id:t,proficiency_level:0,years_experience:0,is_certified:!1,certification_name:null,self_assessed:!1,manager_assessed:!1},j=(g,t)=>{console.log("Skill cell clicked:",g.full_name,t.name)},E=()=>{if(!m.value.length||!u.value.length)return;const g=["Dipendente","Dipartimento","Posizione",...u.value.map(x=>x.name)],t=m.value.map(x=>{const S=u.value.map(D=>$(x,D.id).proficiency_level||0);return[x.full_name,x.department||"",x.position||"",...S]}),r=[g,...t].map(x=>x.map(S=>`"${S}"`).join(",")).join(`
`),v=new Blob([r],{type:"text/csv;charset=utf-8;"}),c=document.createElement("a"),A=URL.createObjectURL(v);c.setAttribute("href",A),c.setAttribute("download",`skills-matrix-${new Date().toISOString().split("T")[0]}.csv`),c.style.visibility="hidden",document.body.appendChild(c),c.click(),document.body.removeChild(c)};return H(()=>{w()}),(g,t)=>(l(),a("div",K,[e("div",W,[t[5]||(t[5]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white flex items-center"},[e("svg",{class:"w-8 h-8 mr-3 text-green-600",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})]),B(" Matrice Competenze ")]),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"}," Panoramica delle competenze del team con livelli di proficiency ")],-1)),e("div",X,[e("button",{onClick:E,disabled:p.value,class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"},t[4]||(t[4]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),B(" Esporta CSV ")]),8,Y)])]),e("div",Z,[e("div",ee,[e("div",null,[t[7]||(t[7]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Dipartimento",-1)),_(e("select",{"onUpdate:modelValue":t[0]||(t[0]=r=>d.value.department_id=r),onChange:w,class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},[t[6]||(t[6]=e("option",{value:""},"Tutti i dipartimenti",-1)),(l(!0),a(k,null,f(o.value.departments,r=>(l(),a("option",{key:r.id,value:r.id},s(r.name),9,te))),128))],544),[[C,d.value.department_id]])]),e("div",null,[t[9]||(t[9]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Categoria",-1)),_(e("select",{"onUpdate:modelValue":t[1]||(t[1]=r=>d.value.category=r),onChange:w,class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},[t[8]||(t[8]=e("option",{value:""},"Tutte le categorie",-1)),(l(!0),a(k,null,f(o.value.categories,r=>(l(),a("option",{key:r,value:r},s(r),9,re))),128))],544),[[C,d.value.category]])]),e("div",null,[t[11]||(t[11]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Livello Minimo",-1)),_(e("select",{"onUpdate:modelValue":t[2]||(t[2]=r=>d.value.min_level=r),onChange:w,class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},t[10]||(t[10]=[M('<option value="" data-v-d60ac041>Qualsiasi</option><option value="1" data-v-d60ac041>1 - Principiante</option><option value="2" data-v-d60ac041>2 - Base</option><option value="3" data-v-d60ac041>3 - Intermedio</option><option value="4" data-v-d60ac041>4 - Avanzato</option><option value="5" data-v-d60ac041>5 - Esperto</option>',6)]),544),[[C,d.value.min_level]])]),e("div",null,[t[13]||(t[13]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Livello Massimo",-1)),_(e("select",{"onUpdate:modelValue":t[3]||(t[3]=r=>d.value.max_level=r),onChange:w,class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},t[12]||(t[12]=[M('<option value="" data-v-d60ac041>Qualsiasi</option><option value="1" data-v-d60ac041>1 - Principiante</option><option value="2" data-v-d60ac041>2 - Base</option><option value="3" data-v-d60ac041>3 - Intermedio</option><option value="4" data-v-d60ac041>4 - Avanzato</option><option value="5" data-v-d60ac041>5 - Esperto</option>',6)]),544),[[C,d.value.max_level]])])])]),n.value?(l(),a("div",ae,[e("div",le,[e("div",se,[t[15]||(t[15]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"w-8 h-8 text-blue-600",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"})])],-1)),e("div",oe,[t[14]||(t[14]=e("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Dipendenti",-1)),e("p",de,s(n.value.total_users),1)])])]),e("div",ie,[e("div",ne,[t[17]||(t[17]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"w-8 h-8 text-green-600",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})])],-1)),e("div",ge,[t[16]||(t[16]=e("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Competenze",-1)),e("p",ce,s(n.value.total_skills),1)])])]),e("div",ue,[e("div",ve,[t[19]||(t[19]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"w-8 h-8 text-purple-600",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z","clip-rule":"evenodd"})])],-1)),e("div",xe,[t[18]||(t[18]=e("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Media Skills/Utente",-1)),e("p",be,s(n.value.avg_skills_per_user),1)])])]),e("div",me,[e("div",ye,[t[21]||(t[21]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"w-8 h-8 text-orange-600",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z","clip-rule":"evenodd"})])],-1)),e("div",pe,[t[20]||(t[20]=e("p",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Competenze Avanzate",-1)),e("p",ke,s(n.value.skill_coverage.advanced),1)])])])])):h("",!0),p.value?(l(),a("div",fe,t[22]||(t[22]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):b.value?(l(),a("div",he,[e("div",we,[t[24]||(t[24]=e("svg",{class:"w-5 h-5 text-red-400 mr-3 mt-0.5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z","clip-rule":"evenodd"})],-1)),e("div",null,[t[23]||(t[23]=e("h3",{class:"text-sm font-medium text-red-800 dark:text-red-200"},"Errore nel caricamento",-1)),e("p",_e,s(b.value),1)])])])):m.value.length>0?(l(),a("div",Ce,[e("div",Se,[e("table",ze,[e("thead",Me,[e("tr",null,[t[25]||(t[25]=e("th",{scope:"col",class:"sticky left-0 z-10 bg-gray-50 dark:bg-gray-700 px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-r border-gray-200 dark:border-gray-600"}," Dipendente ",-1)),(l(!0),a(k,null,f(u.value,r=>(l(),a("th",{key:r.id,scope:"col",class:"px-3 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider min-w-24"},[e("div",$e,[e("span",Ve,s(r.name),1),e("span",Be,s(r.category),1),e("div",Le,[e("span",je,s(r.total_users),1),e("span",Ee,"avg: "+s(r.avg_level),1)])])]))),128))])]),e("tbody",Ae,[(l(!0),a(k,null,f(m.value,r=>(l(),a("tr",{key:r.user_id,class:"hover:bg-gray-50 dark:hover:bg-gray-700"},[e("td",De,[e("div",He,[t[26]||(t[26]=e("div",{class:"w-10 h-10 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center mr-3"},[e("svg",{class:"w-5 h-5 text-gray-500 dark:text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z","clip-rule":"evenodd"})])],-1)),e("div",null,[e("div",Ue,s(r.full_name),1),e("div",Ne,s(r.position||"Dipendente"),1),e("div",Pe,s(r.department||"N/A"),1)])])]),(l(!0),a(k,null,f(u.value,v=>(l(),a("td",{key:`${r.user_id}-${v.id}`,class:"px-3 py-4 text-center"},[U(J,{"user-skill":$(r,v.id),skill:v,onClick:c=>j(r,v)},null,8,["user-skill","skill","onClick"])]))),128))]))),128))])])])])):p.value?h("",!0):(l(),a("div",Te,t[27]||(t[27]=[M('<div class="text-center" data-v-d60ac041><svg class="mx-auto h-16 w-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-d60ac041><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" data-v-d60ac041></path></svg><h3 class="mt-4 text-lg font-medium text-gray-900 dark:text-white" data-v-d60ac041>Nessuna competenza trovata</h3><p class="mt-2 text-sm text-gray-500 dark:text-gray-400" data-v-d60ac041> Prova a modificare i filtri o aggiungi competenze ai dipendenti </p></div>',1)])))]))}},qe=L(Ie,[["__scopeId","data-v-d60ac041"]]);export{qe as default};

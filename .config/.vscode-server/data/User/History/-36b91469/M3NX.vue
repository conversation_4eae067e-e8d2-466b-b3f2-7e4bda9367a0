<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
          <svg class="w-8 h-8 mr-3 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z" clip-rule="evenodd"></path>
          </svg>
          Organigramma Aziendale
        </h1>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Struttura organizzativa e gerarchia aziendale
        </p>
      </div>

      <!-- Controls -->
      <div class="mt-4 sm:mt-0 flex flex-col sm:flex-row items-stretch sm:items-center space-y-2 sm:space-y-0 sm:space-x-3">
        <!-- Search -->
        <div class="relative">
          <input v-model="searchQuery"
                 @input="updateFilteredStats"
                 type="text"
                 placeholder="Cerca dipendente o dipartimento..."
                 class="w-full sm:w-64 pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
          <svg class="absolute left-3 top-2.5 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
          </svg>
        </div>

        <!-- View Toggle -->
        <div class="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
          <button @click="setViewMode('tree')"
                  :class="[
                    'px-3 py-1 rounded-md text-sm font-medium transition-colors',
                    viewMode === 'tree'
                      ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                      : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                  ]">
            <svg class="w-4 h-4 mr-1 inline" fill="currentColor" viewBox="0 0 20 20">
              <path d="M3 4a1 1 0 000 2h11a1 1 0 100-2H3zM3 8a1 1 0 000 2h6a1 1 0 100-2H3zM14 7a1 1 0 011 1v8a1 1 0 11-2 0V9.414l-1.293 1.293a1 1 0 01-1.414-1.414L12.586 7H14z"></path>
            </svg>
            Albero
          </button>
          <button @click="setViewMode('list')"
                  :class="[
                    'px-3 py-1 rounded-md text-sm font-medium transition-colors',
                    viewMode === 'list'
                      ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                      : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                  ]">
            <svg class="w-4 h-4 mr-1 inline" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z" clip-rule="evenodd"></path>
            </svg>
            Lista
          </button>
          <button @click="setViewMode('chart')"
                  :class="[
                    'px-3 py-1 rounded-md text-sm font-medium transition-colors',
                    viewMode === 'chart'
                      ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                      : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                  ]">
            <svg class="w-4 h-4 mr-1 inline" fill="currentColor" viewBox="0 0 20 20">
              <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"></path>
            </svg>
            Chart
          </button>
        </div>

        <!-- Expand/Collapse All (only for tree view) -->
        <button v-if="viewMode === 'tree'"
                @click="toggleAllNodes"
                :disabled="filteredOrgChart.length === 0"
                class="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white rounded-lg transition-colors duration-200">
          {{ allExpanded ? 'Comprimi Tutto' : 'Espandi Tutto' }}
        </button>

        <!-- Filters Toggle -->
        <button @click="showFilters = !showFilters"
                :class="[
                  'px-4 py-2 rounded-lg transition-colors duration-200 flex items-center',
                  showFilters
                    ? 'bg-green-600 hover:bg-green-700 text-white'
                    : 'bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300'
                ]">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
          </svg>
          Filtri
        </button>
      </div>
    </div>

    <!-- Advanced Filters Panel -->
    <div v-if="showFilters" class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white">Filtri Avanzati</h3>
        <button @click="clearAllFilters"
                class="text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300">
          Cancella tutti
        </button>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <!-- Hierarchy Level Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Livello Gerarchico</label>
          <select v-model="filters.hierarchyLevel"
                  @change="updateFilteredStats"
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            <option value="">Tutti i livelli</option>
            <option value="managers">Solo Manager</option>
            <option value="employees">Solo Dipendenti</option>
            <option value="top_level">Dirigenti</option>
          </select>
        </div>

        <!-- Department Size Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Dimensione Dipartimento</label>
          <select v-model="filters.departmentSize"
                  @change="updateFilteredStats"
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            <option value="">Tutte le dimensioni</option>
            <option value="small">Piccoli (1-5 dipendenti)</option>
            <option value="medium">Medi (6-15 dipendenti)</option>
            <option value="large">Grandi (16+ dipendenti)</option>
          </select>
        </div>

        <!-- Budget Range Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Budget Dipartimento</label>
          <select v-model="filters.budgetRange"
                  @change="updateFilteredStats"
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            <option value="">Tutti i budget</option>
            <option value="low">Basso (< €50k)</option>
            <option value="medium">Medio (€50k - €200k)</option>
            <option value="high">Alto (> €200k)</option>
          </select>
        </div>

        <!-- Has Manager Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Stato Manager</label>
          <select v-model="filters.hasManager"
                  @change="updateFilteredStats"
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            <option value="">Tutti</option>
            <option value="true">Con Manager</option>
            <option value="false">Senza Manager</option>
          </select>
        </div>
      </div>

      <!-- Active Filters Display -->
      <div v-if="hasActiveFilters" class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
        <div class="flex flex-wrap gap-2">
          <span class="text-sm text-gray-500 dark:text-gray-400 mr-2">Filtri attivi:</span>
          <span v-for="filter in activeFiltersDisplay"
                :key="filter.key"
                class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
            {{ filter.label }}
            <button @click="clearFilter(filter.key)"
                    class="ml-1 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200">
              <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
              </svg>
            </button>
          </span>
        </div>
      </div>
    </div>

    <!-- Statistics Cards -->
    <div v-if="filteredStats" class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"></path>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">
              Dipendenti {{ hasActiveFilters ? 'Filtrati' : 'Totali' }}
            </p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ filteredStats.total_employees }}</p>
            <p v-if="hasActiveFilters" class="text-xs text-gray-400">
              di {{ stats.total_employees }} totali
            </p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="w-8 h-8 text-green-600" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z" clip-rule="evenodd"></path>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">
              Dipartimenti {{ hasActiveFilters ? 'Filtrati' : 'Totali' }}
            </p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ filteredStats.total_departments }}</p>
            <p v-if="hasActiveFilters" class="text-xs text-gray-400">
              di {{ stats.total_departments }} totali
            </p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="w-8 h-8 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">
              Manager {{ hasActiveFilters ? 'Filtrati' : 'Totali' }}
            </p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ filteredStats.total_managers }}</p>
            <p v-if="hasActiveFilters" class="text-xs text-gray-400">
              di {{ stats.total_managers }} totali
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
      <div class="flex">
        <svg class="w-5 h-5 text-red-400 mr-3 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
        </svg>
        <div>
          <h3 class="text-sm font-medium text-red-800 dark:text-red-200">Errore nel caricamento</h3>
          <p class="mt-1 text-sm text-red-700 dark:text-red-300">{{ error }}</p>
        </div>
      </div>
    </div>

    <!-- Organization Chart Content -->
    <div v-else-if="orgChart.length > 0" class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
      <!-- Tree View -->
      <div v-if="viewMode === 'tree'" class="p-6">
        <div class="orgchart-container overflow-x-auto">
          <div class="orgchart-tree min-w-max">
            <DepartmentNode
              v-for="department in filteredOrgChart"
              :key="department.id"
              :department="department"
              :expanded="expandedNodes"
              :search-query="searchQuery"
              @toggle-node="toggleNode"
              @employee-click="onEmployeeClick"
            />
          </div>
        </div>
      </div>

      <!-- List View -->
      <div v-else-if="viewMode === 'list'" class="divide-y divide-gray-200 dark:divide-gray-700">
        <div v-for="department in filteredOrgChart" :key="department.id">
          <DepartmentList
            :department="department"
            :search-query="searchQuery"
            @employee-click="onEmployeeClick"
          />
        </div>
      </div>

      <!-- Chart View -->
      <div v-else-if="viewMode === 'chart'" class="p-6">
        <OrgChartDiagram
          :org-data="filteredOrgChart"
          :search-query="searchQuery"
          @employee-click="onEmployeeClick"
          @department-click="onDepartmentClick"
        />
      </div>
    </div>

    <!-- Empty State -->
    <div v-else-if="!loading" class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-12">
      <div class="text-center">
        <svg class="mx-auto h-16 w-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
        </svg>
        <h3 class="mt-4 text-lg font-medium text-gray-900 dark:text-white">Nessun dipartimento configurato</h3>
        <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
          Inizia creando la struttura organizzativa aziendale
        </p>
        <div class="mt-6">
          <router-link
            to="/app/personnel/departments"
            class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Gestisci Dipartimenti
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import DepartmentNode from './components/DepartmentNode.vue'
import DepartmentList from './components/DepartmentList.vue'
import OrgChartDiagram from './components/OrgChartDiagram.vue'

// Router
const router = useRouter()

// Reactive state
const loading = ref(false)
const error = ref(null)
const orgChart = ref([])
const stats = ref(null)
const filteredStats = ref(null)
const searchQuery = ref('')
const viewMode = ref('tree') // 'tree', 'list', or 'chart'
const expandedNodes = ref(new Set())
const allExpanded = ref(false)
const showFilters = ref(false)

// Filters
const filters = ref({
  hierarchyLevel: '',
  departmentSize: '',
  budgetRange: '',
  hasManager: ''
})

// Computed properties
const filteredOrgChart = computed(() => {
  let result = orgChart.value

  // Apply advanced filters first
  if (hasActiveFilters.value) {
    result = result.map(dept => applyFiltersTodepartment(dept)).filter(dept => dept !== null)
  }

  // Apply search filter
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase()
    result = result.map(dept => applySearchToDepart(dept, query)).filter(dept => dept !== null)
  }

  return result
})

const hasActiveFilters = computed(() => {
  return Object.values(filters.value).some(value => value !== '')
})

const activeFiltersDisplay = computed(() => {
  const active = []
  const filterLabels = {
    hierarchyLevel: {
      managers: 'Solo Manager',
      employees: 'Solo Dipendenti',
      top_level: 'Dirigenti'
    },
    departmentSize: {
      small: 'Piccoli Dipartimenti',
      medium: 'Medi Dipartimenti',
      large: 'Grandi Dipartimenti'
    },
    budgetRange: {
      low: 'Budget Basso',
      medium: 'Budget Medio',
      high: 'Budget Alto'
    },
    hasManager: {
      'true': 'Con Manager',
      'false': 'Senza Manager'
    }
  }

  Object.entries(filters.value).forEach(([key, value]) => {
    if (value && filterLabels[key] && filterLabels[key][value]) {
      active.push({
        key,
        label: filterLabels[key][value]
      })
    }
  })

  return active
})

// Methods
const applyFiltersTodepartment = (dept) => {
  // Check department size filter
  if (filters.value.departmentSize) {
    const size = dept.employee_count
    if (filters.value.departmentSize === 'small' && size > 5) return null
    if (filters.value.departmentSize === 'medium' && (size < 6 || size > 15)) return null
    if (filters.value.departmentSize === 'large' && size < 16) return null
  }

  // Check budget filter
  if (filters.value.budgetRange && dept.budget) {
    const budget = dept.budget
    if (filters.value.budgetRange === 'low' && budget >= 50000) return null
    if (filters.value.budgetRange === 'medium' && (budget < 50000 || budget > 200000)) return null
    if (filters.value.budgetRange === 'high' && budget <= 200000) return null
  }

  // Check manager filter
  if (filters.value.hasManager) {
    const hasManager = dept.manager_id !== null
    if (filters.value.hasManager === 'true' && !hasManager) return null
    if (filters.value.hasManager === 'false' && hasManager) return null
  }

  // Filter employees based on hierarchy level
  let filteredEmployees = dept.employees
  if (filters.value.hierarchyLevel) {
    if (filters.value.hierarchyLevel === 'managers') {
      filteredEmployees = dept.employees.filter(emp => emp.is_manager || emp.role === 'manager' || emp.role === 'admin')
    } else if (filters.value.hierarchyLevel === 'employees') {
      filteredEmployees = dept.employees.filter(emp => !emp.is_manager && emp.role === 'employee')
    } else if (filters.value.hierarchyLevel === 'top_level') {
      filteredEmployees = dept.employees.filter(emp => emp.role === 'admin')
    }
  }

  // Recursively filter subdepartments
  const filteredSubdepts = dept.subdepartments
    .map(subdept => applyFiltersTodepartment(subdept))
    .filter(subdept => subdept !== null)

  // Return department if it passes filters or has valid subdepartments
  if (filteredEmployees.length > 0 || filteredSubdepts.length > 0) {
    return {
      ...dept,
      employees: filteredEmployees,
      subdepartments: filteredSubdepts,
      employee_count: filteredEmployees.length
    }
  }

  return null
}

const applySearchToDepart = (dept, query) => {
  const matchesDept = dept.name.toLowerCase().includes(query) ||
                     (dept.description && dept.description.toLowerCase().includes(query))

  const matchingEmployees = dept.employees.filter(emp =>
    emp.full_name.toLowerCase().includes(query) ||
    emp.email.toLowerCase().includes(query) ||
    (emp.position && emp.position.toLowerCase().includes(query))
  )

  const filteredSubdepts = dept.subdepartments
    .map(subdept => applySearchToDepart(subdept, query))
    .filter(subdept => subdept !== null)

  // Include department if it matches, has matching employees, or has matching subdepartments
  if (matchesDept || matchingEmployees.length > 0 || filteredSubdepts.length > 0) {
    return {
      ...dept,
      employees: matchingEmployees.length > 0 ? matchingEmployees : dept.employees,
      subdepartments: filteredSubdepts
    }
  }

  return null
}

const loadOrgChart = async () => {
  loading.value = true
  error.value = null

  try {
    const response = await fetch('/api/personnel/orgchart', {
      credentials: 'include'
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()

    if (data.success) {
      orgChart.value = data.data.orgchart || []
      stats.value = data.data.stats || {}
      updateFilteredStats()

      // Auto-expand first level by default
      if (orgChart.value.length > 0) {
        orgChart.value.forEach(dept => {
          expandedNodes.value.add(dept.id)
        })
      }
    } else {
      throw new Error(data.message || 'Errore nel caricamento dell\'organigramma')
    }
  } catch (err) {
    console.error('Error loading org chart:', err)
    error.value = err.message
  } finally {
    loading.value = false
  }
}

const toggleNode = (nodeId) => {
  if (expandedNodes.value.has(nodeId)) {
    expandedNodes.value.delete(nodeId)
  } else {
    expandedNodes.value.add(nodeId)
  }
}

const toggleAllNodes = () => {
  if (allExpanded.value) {
    // Collapse all
    expandedNodes.value.clear()
    allExpanded.value = false
  } else {
    // Expand all
    const expandAll = (departments) => {
      departments.forEach(dept => {
        expandedNodes.value.add(dept.id)
        if (dept.subdepartments && dept.subdepartments.length > 0) {
          expandAll(dept.subdepartments)
        }
      })
    }
    expandAll(orgChart.value)
    allExpanded.value = true
  }
}

const onEmployeeClick = (employee) => {
  // Navigate to employee profile
  router.push(`/app/personnel/profile/${employee.id}`)
}

// Lifecycle
onMounted(() => {
  loadOrgChart()
})
</script>

<style scoped>
.orgchart-container {
  padding: 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 12px;
  min-height: 400px;
}

.dark .orgchart-container {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
}

.orgchart-tree {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 1rem;
}

/* Connection lines for tree view */
.department-node {
  position: relative;
}

.department-node::before {
  content: '';
  position: absolute;
  left: -20px;
  top: 50%;
  width: 20px;
  height: 1px;
  background: #d1d5db;
  z-index: 1;
}

.dark .department-node::before {
  background: #4b5563;
}

.department-node:first-child::before {
  display: none;
}

/* Hover effects */
.department-header:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.dark .department-header:hover {
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
}

/* Smooth transitions */
.department-header {
  transition: all 0.3s ease;
}

/* Search highlighting */
.highlight {
  background-color: #fef3c7;
  padding: 2px 4px;
  border-radius: 4px;
}

.dark .highlight {
  background-color: #92400e;
  color: #fbbf24;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .orgchart-container {
    padding: 10px;
  }

  .department-header {
    min-width: 280px;
  }
}
</style>

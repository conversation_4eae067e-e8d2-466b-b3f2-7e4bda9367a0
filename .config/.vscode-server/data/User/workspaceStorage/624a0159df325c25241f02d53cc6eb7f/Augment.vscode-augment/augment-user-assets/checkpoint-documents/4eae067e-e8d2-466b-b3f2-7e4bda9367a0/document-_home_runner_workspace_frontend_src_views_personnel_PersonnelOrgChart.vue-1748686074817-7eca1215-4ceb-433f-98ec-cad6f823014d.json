{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/personnel/PersonnelOrgChart.vue"}, "originalCode": "<template>\n  <div class=\"space-y-6\">\n    <!-- Header -->\n    <div class=\"flex flex-col sm:flex-row sm:items-center sm:justify-between\">\n      <div>\n        <h1 class=\"text-2xl font-bold text-gray-900 dark:text-white flex items-center\">\n          <svg class=\"w-8 h-8 mr-3 text-blue-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fill-rule=\"evenodd\" d=\"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z\" clip-rule=\"evenodd\"></path>\n          </svg>\n          Organigramma Aziendale\n        </h1>\n        <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\n          Struttura organizzativa e gerarchia aziendale\n        </p>\n      </div>\n\n      <!-- Controls -->\n      <div class=\"mt-4 sm:mt-0 flex flex-col sm:flex-row items-stretch sm:items-center space-y-2 sm:space-y-0 sm:space-x-3\">\n        <!-- Search -->\n        <div class=\"relative\">\n          <input v-model=\"searchQuery\"\n                 @input=\"updateFilteredStats\"\n                 type=\"text\"\n                 placeholder=\"Cerca dipendente o dipartimento...\"\n                 class=\"w-full sm:w-64 pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\">\n          <svg class=\"absolute left-3 top-2.5 w-5 h-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"></path>\n          </svg>\n        </div>\n\n        <!-- View Toggle -->\n        <div class=\"flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1\">\n          <button @click=\"setViewMode('tree')\"\n                  :class=\"[\n                    'px-3 py-1 rounded-md text-sm font-medium transition-colors',\n                    viewMode === 'tree'\n                      ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'\n                      : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'\n                  ]\">\n            <svg class=\"w-4 h-4 mr-1 inline\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path d=\"M3 4a1 1 0 000 2h11a1 1 0 100-2H3zM3 8a1 1 0 000 2h6a1 1 0 100-2H3zM14 7a1 1 0 011 1v8a1 1 0 11-2 0V9.414l-1.293 1.293a1 1 0 01-1.414-1.414L12.586 7H14z\"></path>\n            </svg>\n            Albero\n          </button>\n          <button @click=\"setViewMode('list')\"\n                  :class=\"[\n                    'px-3 py-1 rounded-md text-sm font-medium transition-colors',\n                    viewMode === 'list'\n                      ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'\n                      : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'\n                  ]\">\n            <svg class=\"w-4 h-4 mr-1 inline\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fill-rule=\"evenodd\" d=\"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z\" clip-rule=\"evenodd\"></path>\n            </svg>\n            Lista\n          </button>\n          <button @click=\"setViewMode('chart')\"\n                  :class=\"[\n                    'px-3 py-1 rounded-md text-sm font-medium transition-colors',\n                    viewMode === 'chart'\n                      ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'\n                      : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'\n                  ]\">\n            <svg class=\"w-4 h-4 mr-1 inline\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path d=\"M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z\"></path>\n            </svg>\n            Chart\n          </button>\n        </div>\n\n        <!-- Expand/Collapse All (only for tree view) -->\n        <button v-if=\"viewMode === 'tree'\"\n                @click=\"toggleAllNodes\"\n                :disabled=\"filteredOrgChart.length === 0\"\n                class=\"px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white rounded-lg transition-colors duration-200\">\n          {{ allExpanded ? 'Comprimi Tutto' : 'Espandi Tutto' }}\n        </button>\n\n        <!-- Filters Toggle -->\n        <button @click=\"showFilters = !showFilters\"\n                :class=\"[\n                  'px-4 py-2 rounded-lg transition-colors duration-200 flex items-center',\n                  showFilters\n                    ? 'bg-green-600 hover:bg-green-700 text-white'\n                    : 'bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300'\n                ]\">\n          <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z\"></path>\n          </svg>\n          Filtri\n        </button>\n      </div>\n    </div>\n\n    <!-- Advanced Filters Panel -->\n    <div v-if=\"showFilters\" class=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\n      <div class=\"flex items-center justify-between mb-4\">\n        <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">Filtri Avanzati</h3>\n        <button @click=\"clearAllFilters\"\n                class=\"text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300\">\n          Cancella tutti\n        </button>\n      </div>\n\n      <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n        <!-- Hierarchy Level Filter -->\n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">Livello Gerarchico</label>\n          <select v-model=\"filters.hierarchyLevel\"\n                  @change=\"updateFilteredStats\"\n                  class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\">\n            <option value=\"\">Tutti i livelli</option>\n            <option value=\"managers\">Solo Manager</option>\n            <option value=\"employees\">Solo Dipendenti</option>\n            <option value=\"top_level\">Dirigenti</option>\n          </select>\n        </div>\n\n        <!-- Department Size Filter -->\n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">Dimensione Dipartimento</label>\n          <select v-model=\"filters.departmentSize\"\n                  @change=\"updateFilteredStats\"\n                  class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\">\n            <option value=\"\">Tutte le dimensioni</option>\n            <option value=\"small\">Piccoli (1-5 dipendenti)</option>\n            <option value=\"medium\">Medi (6-15 dipendenti)</option>\n            <option value=\"large\">Grandi (16+ dipendenti)</option>\n          </select>\n        </div>\n\n        <!-- Budget Range Filter -->\n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">Budget Dipartimento</label>\n          <select v-model=\"filters.budgetRange\"\n                  @change=\"updateFilteredStats\"\n                  class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\">\n            <option value=\"\">Tutti i budget</option>\n            <option value=\"low\">Basso (< €50k)</option>\n            <option value=\"medium\">Medio (€50k - €200k)</option>\n            <option value=\"high\">Alto (> €200k)</option>\n          </select>\n        </div>\n\n        <!-- Has Manager Filter -->\n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">Stato Manager</label>\n          <select v-model=\"filters.hasManager\"\n                  @change=\"updateFilteredStats\"\n                  class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\">\n            <option value=\"\">Tutti</option>\n            <option value=\"true\">Con Manager</option>\n            <option value=\"false\">Senza Manager</option>\n          </select>\n        </div>\n      </div>\n\n      <!-- Active Filters Display -->\n      <div v-if=\"hasActiveFilters\" class=\"mt-4 pt-4 border-t border-gray-200 dark:border-gray-700\">\n        <div class=\"flex flex-wrap gap-2\">\n          <span class=\"text-sm text-gray-500 dark:text-gray-400 mr-2\">Filtri attivi:</span>\n          <span v-for=\"filter in activeFiltersDisplay\"\n                :key=\"filter.key\"\n                class=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200\">\n            {{ filter.label }}\n            <button @click=\"clearFilter(filter.key)\"\n                    class=\"ml-1 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200\">\n              <svg class=\"w-3 h-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fill-rule=\"evenodd\" d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\" clip-rule=\"evenodd\"></path>\n              </svg>\n            </button>\n          </span>\n        </div>\n      </div>\n    </div>\n\n    <!-- Statistics Cards -->\n    <div v-if=\"filteredStats\" class=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n      <div class=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <svg class=\"w-8 h-8 text-blue-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path d=\"M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z\"></path>\n            </svg>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-500 dark:text-gray-400\">\n              Dipendenti {{ hasActiveFilters ? 'Filtrati' : 'Totali' }}\n            </p>\n            <p class=\"text-2xl font-bold text-gray-900 dark:text-white\">{{ filteredStats.total_employees }}</p>\n            <p v-if=\"hasActiveFilters\" class=\"text-xs text-gray-400\">\n              di {{ stats.total_employees }} totali\n            </p>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <svg class=\"w-8 h-8 text-green-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fill-rule=\"evenodd\" d=\"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z\" clip-rule=\"evenodd\"></path>\n            </svg>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-500 dark:text-gray-400\">\n              Dipartimenti {{ hasActiveFilters ? 'Filtrati' : 'Totali' }}\n            </p>\n            <p class=\"text-2xl font-bold text-gray-900 dark:text-white\">{{ filteredStats.total_departments }}</p>\n            <p v-if=\"hasActiveFilters\" class=\"text-xs text-gray-400\">\n              di {{ stats.total_departments }} totali\n            </p>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <svg class=\"w-8 h-8 text-purple-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fill-rule=\"evenodd\" d=\"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\" clip-rule=\"evenodd\"></path>\n            </svg>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-500 dark:text-gray-400\">\n              Manager {{ hasActiveFilters ? 'Filtrati' : 'Totali' }}\n            </p>\n            <p class=\"text-2xl font-bold text-gray-900 dark:text-white\">{{ filteredStats.total_managers }}</p>\n            <p v-if=\"hasActiveFilters\" class=\"text-xs text-gray-400\">\n              di {{ stats.total_managers }} totali\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Loading State -->\n    <div v-if=\"loading\" class=\"flex justify-center items-center py-12\">\n      <div class=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n    </div>\n\n    <!-- Error State -->\n    <div v-else-if=\"error\" class=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4\">\n      <div class=\"flex\">\n        <svg class=\"w-5 h-5 text-red-400 mr-3 mt-0.5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n          <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clip-rule=\"evenodd\"></path>\n        </svg>\n        <div>\n          <h3 class=\"text-sm font-medium text-red-800 dark:text-red-200\">Errore nel caricamento</h3>\n          <p class=\"mt-1 text-sm text-red-700 dark:text-red-300\">{{ error }}</p>\n        </div>\n      </div>\n    </div>\n\n    <!-- Organization Chart Content -->\n    <div v-else-if=\"orgChart.length > 0\" class=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700\">\n      <!-- Tree View -->\n      <div v-if=\"viewMode === 'tree'\" class=\"p-6\">\n        <div class=\"orgchart-container overflow-x-auto\">\n          <div class=\"orgchart-tree min-w-max\">\n            <DepartmentNode\n              v-for=\"department in filteredOrgChart\"\n              :key=\"department.id\"\n              :department=\"department\"\n              :expanded=\"expandedNodes\"\n              :search-query=\"searchQuery\"\n              @toggle-node=\"toggleNode\"\n              @employee-click=\"onEmployeeClick\"\n            />\n          </div>\n        </div>\n      </div>\n\n      <!-- List View -->\n      <div v-else-if=\"viewMode === 'list'\" class=\"divide-y divide-gray-200 dark:divide-gray-700\">\n        <div v-for=\"department in filteredOrgChart\" :key=\"department.id\">\n          <DepartmentList\n            :department=\"department\"\n            :search-query=\"searchQuery\"\n            @employee-click=\"onEmployeeClick\"\n          />\n        </div>\n      </div>\n\n      <!-- Chart View -->\n      <div v-else-if=\"viewMode === 'chart'\" class=\"p-6\">\n        <OrgChartDiagram\n          :org-data=\"filteredOrgChart\"\n          :search-query=\"searchQuery\"\n          @employee-click=\"onEmployeeClick\"\n          @department-click=\"onDepartmentClick\"\n        />\n      </div>\n    </div>\n\n    <!-- Empty State -->\n    <div v-else-if=\"!loading\" class=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-12\">\n      <div class=\"text-center\">\n        <svg class=\"mx-auto h-16 w-16 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\"></path>\n        </svg>\n        <h3 class=\"mt-4 text-lg font-medium text-gray-900 dark:text-white\">Nessun dipartimento configurato</h3>\n        <p class=\"mt-2 text-sm text-gray-500 dark:text-gray-400\">\n          Inizia creando la struttura organizzativa aziendale\n        </p>\n        <div class=\"mt-6\">\n          <router-link\n            to=\"/app/personnel/departments\"\n            class=\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n          >\n            <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\"></path>\n            </svg>\n            Gestisci Dipartimenti\n          </router-link>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted } from 'vue'\nimport { useRouter } from 'vue-router'\nimport DepartmentNode from './components/DepartmentNode.vue'\nimport DepartmentList from './components/DepartmentList.vue'\n\n// Router\nconst router = useRouter()\n\n// Reactive state\nconst loading = ref(false)\nconst error = ref(null)\nconst orgChart = ref([])\nconst stats = ref(null)\nconst searchQuery = ref('')\nconst viewMode = ref('tree') // 'tree' or 'list'\nconst expandedNodes = ref(new Set())\nconst allExpanded = ref(false)\n\n// Computed properties\nconst filteredOrgChart = computed(() => {\n  if (!searchQuery.value.trim()) {\n    return orgChart.value\n  }\n\n  const query = searchQuery.value.toLowerCase()\n\n  // Filter departments and employees based on search\n  const filterDepartment = (dept) => {\n    const matchesDept = dept.name.toLowerCase().includes(query) ||\n                       (dept.description && dept.description.toLowerCase().includes(query))\n\n    const matchingEmployees = dept.employees.filter(emp =>\n      emp.full_name.toLowerCase().includes(query) ||\n      emp.email.toLowerCase().includes(query) ||\n      (emp.position && emp.position.toLowerCase().includes(query))\n    )\n\n    const filteredSubdepts = dept.subdepartments\n      .map(filterDepartment)\n      .filter(subdept => subdept !== null)\n\n    // Include department if it matches, has matching employees, or has matching subdepartments\n    if (matchesDept || matchingEmployees.length > 0 || filteredSubdepts.length > 0) {\n      return {\n        ...dept,\n        employees: matchingEmployees.length > 0 ? matchingEmployees : dept.employees,\n        subdepartments: filteredSubdepts\n      }\n    }\n\n    return null\n  }\n\n  return orgChart.value\n    .map(filterDepartment)\n    .filter(dept => dept !== null)\n})\n\n// Methods\nconst loadOrgChart = async () => {\n  loading.value = true\n  error.value = null\n\n  try {\n    const response = await fetch('/api/personnel/orgchart', {\n      credentials: 'include'\n    })\n\n    if (!response.ok) {\n      throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n\n    if (data.success) {\n      orgChart.value = data.data.orgchart || []\n      stats.value = data.data.stats || {}\n\n      // Auto-expand first level by default\n      if (orgChart.value.length > 0) {\n        orgChart.value.forEach(dept => {\n          expandedNodes.value.add(dept.id)\n        })\n      }\n    } else {\n      throw new Error(data.message || 'Errore nel caricamento dell\\'organigramma')\n    }\n  } catch (err) {\n    console.error('Error loading org chart:', err)\n    error.value = err.message\n  } finally {\n    loading.value = false\n  }\n}\n\nconst toggleNode = (nodeId) => {\n  if (expandedNodes.value.has(nodeId)) {\n    expandedNodes.value.delete(nodeId)\n  } else {\n    expandedNodes.value.add(nodeId)\n  }\n}\n\nconst toggleAllNodes = () => {\n  if (allExpanded.value) {\n    // Collapse all\n    expandedNodes.value.clear()\n    allExpanded.value = false\n  } else {\n    // Expand all\n    const expandAll = (departments) => {\n      departments.forEach(dept => {\n        expandedNodes.value.add(dept.id)\n        if (dept.subdepartments && dept.subdepartments.length > 0) {\n          expandAll(dept.subdepartments)\n        }\n      })\n    }\n    expandAll(orgChart.value)\n    allExpanded.value = true\n  }\n}\n\nconst onEmployeeClick = (employee) => {\n  // Navigate to employee profile\n  router.push(`/app/personnel/profile/${employee.id}`)\n}\n\n// Lifecycle\nonMounted(() => {\n  loadOrgChart()\n})\n</script>\n\n<style scoped>\n.orgchart-container {\n  padding: 20px;\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\n  border-radius: 12px;\n  min-height: 400px;\n}\n\n.dark .orgchart-container {\n  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);\n}\n\n.orgchart-tree {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n  gap: 1rem;\n}\n\n/* Connection lines for tree view */\n.department-node {\n  position: relative;\n}\n\n.department-node::before {\n  content: '';\n  position: absolute;\n  left: -20px;\n  top: 50%;\n  width: 20px;\n  height: 1px;\n  background: #d1d5db;\n  z-index: 1;\n}\n\n.dark .department-node::before {\n  background: #4b5563;\n}\n\n.department-node:first-child::before {\n  display: none;\n}\n\n/* Hover effects */\n.department-header:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\n}\n\n.dark .department-header:hover {\n  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);\n}\n\n/* Smooth transitions */\n.department-header {\n  transition: all 0.3s ease;\n}\n\n/* Search highlighting */\n.highlight {\n  background-color: #fef3c7;\n  padding: 2px 4px;\n  border-radius: 4px;\n}\n\n.dark .highlight {\n  background-color: #92400e;\n  color: #fbbf24;\n}\n\n/* Responsive adjustments */\n@media (max-width: 768px) {\n  .orgchart-container {\n    padding: 10px;\n  }\n\n  .department-header {\n    min-width: 280px;\n  }\n}\n</style>\n", "modifiedCode": "<template>\n  <div class=\"space-y-6\">\n    <!-- Header -->\n    <div class=\"flex flex-col sm:flex-row sm:items-center sm:justify-between\">\n      <div>\n        <h1 class=\"text-2xl font-bold text-gray-900 dark:text-white flex items-center\">\n          <svg class=\"w-8 h-8 mr-3 text-blue-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fill-rule=\"evenodd\" d=\"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z\" clip-rule=\"evenodd\"></path>\n          </svg>\n          Organigramma Aziendale\n        </h1>\n        <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\n          Struttura organizzativa e gerarchia aziendale\n        </p>\n      </div>\n\n      <!-- Controls -->\n      <div class=\"mt-4 sm:mt-0 flex flex-col sm:flex-row items-stretch sm:items-center space-y-2 sm:space-y-0 sm:space-x-3\">\n        <!-- Search -->\n        <div class=\"relative\">\n          <input v-model=\"searchQuery\"\n                 @input=\"updateFilteredStats\"\n                 type=\"text\"\n                 placeholder=\"Cerca dipendente o dipartimento...\"\n                 class=\"w-full sm:w-64 pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\">\n          <svg class=\"absolute left-3 top-2.5 w-5 h-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"></path>\n          </svg>\n        </div>\n\n        <!-- View Toggle -->\n        <div class=\"flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1\">\n          <button @click=\"setViewMode('tree')\"\n                  :class=\"[\n                    'px-3 py-1 rounded-md text-sm font-medium transition-colors',\n                    viewMode === 'tree'\n                      ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'\n                      : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'\n                  ]\">\n            <svg class=\"w-4 h-4 mr-1 inline\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path d=\"M3 4a1 1 0 000 2h11a1 1 0 100-2H3zM3 8a1 1 0 000 2h6a1 1 0 100-2H3zM14 7a1 1 0 011 1v8a1 1 0 11-2 0V9.414l-1.293 1.293a1 1 0 01-1.414-1.414L12.586 7H14z\"></path>\n            </svg>\n            Albero\n          </button>\n          <button @click=\"setViewMode('list')\"\n                  :class=\"[\n                    'px-3 py-1 rounded-md text-sm font-medium transition-colors',\n                    viewMode === 'list'\n                      ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'\n                      : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'\n                  ]\">\n            <svg class=\"w-4 h-4 mr-1 inline\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fill-rule=\"evenodd\" d=\"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z\" clip-rule=\"evenodd\"></path>\n            </svg>\n            Lista\n          </button>\n          <button @click=\"setViewMode('chart')\"\n                  :class=\"[\n                    'px-3 py-1 rounded-md text-sm font-medium transition-colors',\n                    viewMode === 'chart'\n                      ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'\n                      : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'\n                  ]\">\n            <svg class=\"w-4 h-4 mr-1 inline\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path d=\"M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z\"></path>\n            </svg>\n            Chart\n          </button>\n        </div>\n\n        <!-- Expand/Collapse All (only for tree view) -->\n        <button v-if=\"viewMode === 'tree'\"\n                @click=\"toggleAllNodes\"\n                :disabled=\"filteredOrgChart.length === 0\"\n                class=\"px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white rounded-lg transition-colors duration-200\">\n          {{ allExpanded ? 'Comprimi Tutto' : 'Espandi Tutto' }}\n        </button>\n\n        <!-- Filters Toggle -->\n        <button @click=\"showFilters = !showFilters\"\n                :class=\"[\n                  'px-4 py-2 rounded-lg transition-colors duration-200 flex items-center',\n                  showFilters\n                    ? 'bg-green-600 hover:bg-green-700 text-white'\n                    : 'bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300'\n                ]\">\n          <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z\"></path>\n          </svg>\n          Filtri\n        </button>\n      </div>\n    </div>\n\n    <!-- Advanced Filters Panel -->\n    <div v-if=\"showFilters\" class=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\n      <div class=\"flex items-center justify-between mb-4\">\n        <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">Filtri Avanzati</h3>\n        <button @click=\"clearAllFilters\"\n                class=\"text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300\">\n          Cancella tutti\n        </button>\n      </div>\n\n      <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n        <!-- Hierarchy Level Filter -->\n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">Livello Gerarchico</label>\n          <select v-model=\"filters.hierarchyLevel\"\n                  @change=\"updateFilteredStats\"\n                  class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\">\n            <option value=\"\">Tutti i livelli</option>\n            <option value=\"managers\">Solo Manager</option>\n            <option value=\"employees\">Solo Dipendenti</option>\n            <option value=\"top_level\">Dirigenti</option>\n          </select>\n        </div>\n\n        <!-- Department Size Filter -->\n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">Dimensione Dipartimento</label>\n          <select v-model=\"filters.departmentSize\"\n                  @change=\"updateFilteredStats\"\n                  class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\">\n            <option value=\"\">Tutte le dimensioni</option>\n            <option value=\"small\">Piccoli (1-5 dipendenti)</option>\n            <option value=\"medium\">Medi (6-15 dipendenti)</option>\n            <option value=\"large\">Grandi (16+ dipendenti)</option>\n          </select>\n        </div>\n\n        <!-- Budget Range Filter -->\n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">Budget Dipartimento</label>\n          <select v-model=\"filters.budgetRange\"\n                  @change=\"updateFilteredStats\"\n                  class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\">\n            <option value=\"\">Tutti i budget</option>\n            <option value=\"low\">Basso (< €50k)</option>\n            <option value=\"medium\">Medio (€50k - €200k)</option>\n            <option value=\"high\">Alto (> €200k)</option>\n          </select>\n        </div>\n\n        <!-- Has Manager Filter -->\n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">Stato Manager</label>\n          <select v-model=\"filters.hasManager\"\n                  @change=\"updateFilteredStats\"\n                  class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\">\n            <option value=\"\">Tutti</option>\n            <option value=\"true\">Con Manager</option>\n            <option value=\"false\">Senza Manager</option>\n          </select>\n        </div>\n      </div>\n\n      <!-- Active Filters Display -->\n      <div v-if=\"hasActiveFilters\" class=\"mt-4 pt-4 border-t border-gray-200 dark:border-gray-700\">\n        <div class=\"flex flex-wrap gap-2\">\n          <span class=\"text-sm text-gray-500 dark:text-gray-400 mr-2\">Filtri attivi:</span>\n          <span v-for=\"filter in activeFiltersDisplay\"\n                :key=\"filter.key\"\n                class=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200\">\n            {{ filter.label }}\n            <button @click=\"clearFilter(filter.key)\"\n                    class=\"ml-1 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200\">\n              <svg class=\"w-3 h-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fill-rule=\"evenodd\" d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\" clip-rule=\"evenodd\"></path>\n              </svg>\n            </button>\n          </span>\n        </div>\n      </div>\n    </div>\n\n    <!-- Statistics Cards -->\n    <div v-if=\"filteredStats\" class=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n      <div class=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <svg class=\"w-8 h-8 text-blue-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path d=\"M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z\"></path>\n            </svg>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-500 dark:text-gray-400\">\n              Dipendenti {{ hasActiveFilters ? 'Filtrati' : 'Totali' }}\n            </p>\n            <p class=\"text-2xl font-bold text-gray-900 dark:text-white\">{{ filteredStats.total_employees }}</p>\n            <p v-if=\"hasActiveFilters\" class=\"text-xs text-gray-400\">\n              di {{ stats.total_employees }} totali\n            </p>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <svg class=\"w-8 h-8 text-green-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fill-rule=\"evenodd\" d=\"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z\" clip-rule=\"evenodd\"></path>\n            </svg>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-500 dark:text-gray-400\">\n              Dipartimenti {{ hasActiveFilters ? 'Filtrati' : 'Totali' }}\n            </p>\n            <p class=\"text-2xl font-bold text-gray-900 dark:text-white\">{{ filteredStats.total_departments }}</p>\n            <p v-if=\"hasActiveFilters\" class=\"text-xs text-gray-400\">\n              di {{ stats.total_departments }} totali\n            </p>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <svg class=\"w-8 h-8 text-purple-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fill-rule=\"evenodd\" d=\"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\" clip-rule=\"evenodd\"></path>\n            </svg>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-500 dark:text-gray-400\">\n              Manager {{ hasActiveFilters ? 'Filtrati' : 'Totali' }}\n            </p>\n            <p class=\"text-2xl font-bold text-gray-900 dark:text-white\">{{ filteredStats.total_managers }}</p>\n            <p v-if=\"hasActiveFilters\" class=\"text-xs text-gray-400\">\n              di {{ stats.total_managers }} totali\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Loading State -->\n    <div v-if=\"loading\" class=\"flex justify-center items-center py-12\">\n      <div class=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n    </div>\n\n    <!-- Error State -->\n    <div v-else-if=\"error\" class=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4\">\n      <div class=\"flex\">\n        <svg class=\"w-5 h-5 text-red-400 mr-3 mt-0.5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n          <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clip-rule=\"evenodd\"></path>\n        </svg>\n        <div>\n          <h3 class=\"text-sm font-medium text-red-800 dark:text-red-200\">Errore nel caricamento</h3>\n          <p class=\"mt-1 text-sm text-red-700 dark:text-red-300\">{{ error }}</p>\n        </div>\n      </div>\n    </div>\n\n    <!-- Organization Chart Content -->\n    <div v-else-if=\"orgChart.length > 0\" class=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700\">\n      <!-- Tree View -->\n      <div v-if=\"viewMode === 'tree'\" class=\"p-6\">\n        <div class=\"orgchart-container overflow-x-auto\">\n          <div class=\"orgchart-tree min-w-max\">\n            <DepartmentNode\n              v-for=\"department in filteredOrgChart\"\n              :key=\"department.id\"\n              :department=\"department\"\n              :expanded=\"expandedNodes\"\n              :search-query=\"searchQuery\"\n              @toggle-node=\"toggleNode\"\n              @employee-click=\"onEmployeeClick\"\n            />\n          </div>\n        </div>\n      </div>\n\n      <!-- List View -->\n      <div v-else-if=\"viewMode === 'list'\" class=\"divide-y divide-gray-200 dark:divide-gray-700\">\n        <div v-for=\"department in filteredOrgChart\" :key=\"department.id\">\n          <DepartmentList\n            :department=\"department\"\n            :search-query=\"searchQuery\"\n            @employee-click=\"onEmployeeClick\"\n          />\n        </div>\n      </div>\n\n      <!-- Chart View -->\n      <div v-else-if=\"viewMode === 'chart'\" class=\"p-6\">\n        <OrgChartDiagram\n          :org-data=\"filteredOrgChart\"\n          :search-query=\"searchQuery\"\n          @employee-click=\"onEmployeeClick\"\n          @department-click=\"onDepartmentClick\"\n        />\n      </div>\n    </div>\n\n    <!-- Empty State -->\n    <div v-else-if=\"!loading\" class=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-12\">\n      <div class=\"text-center\">\n        <svg class=\"mx-auto h-16 w-16 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\"></path>\n        </svg>\n        <h3 class=\"mt-4 text-lg font-medium text-gray-900 dark:text-white\">Nessun dipartimento configurato</h3>\n        <p class=\"mt-2 text-sm text-gray-500 dark:text-gray-400\">\n          Inizia creando la struttura organizzativa aziendale\n        </p>\n        <div class=\"mt-6\">\n          <router-link\n            to=\"/app/personnel/departments\"\n            class=\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n          >\n            <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\"></path>\n            </svg>\n            Gestisci Dipartimenti\n          </router-link>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted, watch } from 'vue'\nimport { useRouter } from 'vue-router'\nimport DepartmentNode from './components/DepartmentNode.vue'\nimport DepartmentList from './components/DepartmentList.vue'\nimport OrgChartDiagram from './components/OrgChartDiagram.vue'\n\n// Router\nconst router = useRouter()\n\n// Reactive state\nconst loading = ref(false)\nconst error = ref(null)\nconst orgChart = ref([])\nconst stats = ref(null)\nconst filteredStats = ref(null)\nconst searchQuery = ref('')\nconst viewMode = ref('tree') // 'tree', 'list', or 'chart'\nconst expandedNodes = ref(new Set())\nconst allExpanded = ref(false)\nconst showFilters = ref(false)\n\n// Filters\nconst filters = ref({\n  hierarchyLevel: '',\n  departmentSize: '',\n  budgetRange: '',\n  hasManager: ''\n})\n\n// Computed properties\nconst filteredOrgChart = computed(() => {\n  if (!searchQuery.value.trim()) {\n    return orgChart.value\n  }\n\n  const query = searchQuery.value.toLowerCase()\n\n  // Filter departments and employees based on search\n  const filterDepartment = (dept) => {\n    const matchesDept = dept.name.toLowerCase().includes(query) ||\n                       (dept.description && dept.description.toLowerCase().includes(query))\n\n    const matchingEmployees = dept.employees.filter(emp =>\n      emp.full_name.toLowerCase().includes(query) ||\n      emp.email.toLowerCase().includes(query) ||\n      (emp.position && emp.position.toLowerCase().includes(query))\n    )\n\n    const filteredSubdepts = dept.subdepartments\n      .map(filterDepartment)\n      .filter(subdept => subdept !== null)\n\n    // Include department if it matches, has matching employees, or has matching subdepartments\n    if (matchesDept || matchingEmployees.length > 0 || filteredSubdepts.length > 0) {\n      return {\n        ...dept,\n        employees: matchingEmployees.length > 0 ? matchingEmployees : dept.employees,\n        subdepartments: filteredSubdepts\n      }\n    }\n\n    return null\n  }\n\n  return orgChart.value\n    .map(filterDepartment)\n    .filter(dept => dept !== null)\n})\n\n// Methods\nconst loadOrgChart = async () => {\n  loading.value = true\n  error.value = null\n\n  try {\n    const response = await fetch('/api/personnel/orgchart', {\n      credentials: 'include'\n    })\n\n    if (!response.ok) {\n      throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n\n    if (data.success) {\n      orgChart.value = data.data.orgchart || []\n      stats.value = data.data.stats || {}\n\n      // Auto-expand first level by default\n      if (orgChart.value.length > 0) {\n        orgChart.value.forEach(dept => {\n          expandedNodes.value.add(dept.id)\n        })\n      }\n    } else {\n      throw new Error(data.message || 'Errore nel caricamento dell\\'organigramma')\n    }\n  } catch (err) {\n    console.error('Error loading org chart:', err)\n    error.value = err.message\n  } finally {\n    loading.value = false\n  }\n}\n\nconst toggleNode = (nodeId) => {\n  if (expandedNodes.value.has(nodeId)) {\n    expandedNodes.value.delete(nodeId)\n  } else {\n    expandedNodes.value.add(nodeId)\n  }\n}\n\nconst toggleAllNodes = () => {\n  if (allExpanded.value) {\n    // Collapse all\n    expandedNodes.value.clear()\n    allExpanded.value = false\n  } else {\n    // Expand all\n    const expandAll = (departments) => {\n      departments.forEach(dept => {\n        expandedNodes.value.add(dept.id)\n        if (dept.subdepartments && dept.subdepartments.length > 0) {\n          expandAll(dept.subdepartments)\n        }\n      })\n    }\n    expandAll(orgChart.value)\n    allExpanded.value = true\n  }\n}\n\nconst onEmployeeClick = (employee) => {\n  // Navigate to employee profile\n  router.push(`/app/personnel/profile/${employee.id}`)\n}\n\n// Lifecycle\nonMounted(() => {\n  loadOrgChart()\n})\n</script>\n\n<style scoped>\n.orgchart-container {\n  padding: 20px;\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\n  border-radius: 12px;\n  min-height: 400px;\n}\n\n.dark .orgchart-container {\n  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);\n}\n\n.orgchart-tree {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n  gap: 1rem;\n}\n\n/* Connection lines for tree view */\n.department-node {\n  position: relative;\n}\n\n.department-node::before {\n  content: '';\n  position: absolute;\n  left: -20px;\n  top: 50%;\n  width: 20px;\n  height: 1px;\n  background: #d1d5db;\n  z-index: 1;\n}\n\n.dark .department-node::before {\n  background: #4b5563;\n}\n\n.department-node:first-child::before {\n  display: none;\n}\n\n/* Hover effects */\n.department-header:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\n}\n\n.dark .department-header:hover {\n  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);\n}\n\n/* Smooth transitions */\n.department-header {\n  transition: all 0.3s ease;\n}\n\n/* Search highlighting */\n.highlight {\n  background-color: #fef3c7;\n  padding: 2px 4px;\n  border-radius: 4px;\n}\n\n.dark .highlight {\n  background-color: #92400e;\n  color: #fbbf24;\n}\n\n/* Responsive adjustments */\n@media (max-width: 768px) {\n  .orgchart-container {\n    padding: 10px;\n  }\n\n  .department-header {\n    min-width: 280px;\n  }\n}\n</style>\n"}
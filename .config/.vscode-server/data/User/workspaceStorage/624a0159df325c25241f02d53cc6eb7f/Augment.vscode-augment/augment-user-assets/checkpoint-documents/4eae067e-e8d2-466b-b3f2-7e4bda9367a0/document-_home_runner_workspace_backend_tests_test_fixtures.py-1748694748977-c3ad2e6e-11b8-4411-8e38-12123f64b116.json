{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/tests/test_fixtures.py"}, "modifiedCode": "\"\"\"\nTest per verificare che le fixture funzionino correttamente.\n\"\"\"\n\nimport pytest\nfrom models import User, Department, Skill\n\n\nclass TestFixtures:\n    \"\"\"Test per verificare il funzionamento delle fixture.\"\"\"\n\n    def test_app_fixture(self, app):\n        \"\"\"Test che la fixture app funzioni.\"\"\"\n        assert app is not None\n        assert app.config['TESTING'] is True\n\n    def test_client_fixture(self, client):\n        \"\"\"Test che la fixture client funzioni.\"\"\"\n        assert client is not None\n        response = client.get('/')\n        # Non importa il codice di risposta, basta che non ci siano errori\n\n    def test_auth_fixture(self, auth):\n        \"\"\"Test che la fixture auth funzioni.\"\"\"\n        assert auth is not None\n        assert hasattr(auth, 'login')\n        assert hasattr(auth, 'logout')\n\n    def test_auth_login(self, auth):\n        \"\"\"Test che il login tramite fixture auth funzioni.\"\"\"\n        response = auth.login()\n        # Il login dovrebbe funzionare (200 o redirect)\n        assert response.status_code in [200, 302]\n\n    def test_created_user_fixture(self, created_user, app):\n        \"\"\"Test che la fixture created_user funzioni.\"\"\"\n        assert created_user is not None\n        assert isinstance(created_user, int)\n        \n        with app.app_context():\n            user = User.query.get(created_user)\n            assert user is not None\n            assert user.username == 'testuser'\n\n    def test_sample_departments_fixture(self, sample_departments, app):\n        \"\"\"Test che la fixture sample_departments funzioni.\"\"\"\n        assert sample_departments is not None\n        assert len(sample_departments) > 0\n        \n        with app.app_context():\n            dept_count = Department.query.count()\n            assert dept_count >= len(sample_departments)\n\n    def test_sample_skills_fixture(self, sample_skills, app):\n        \"\"\"Test che la fixture sample_skills funzioni.\"\"\"\n        assert sample_skills is not None\n        assert len(sample_skills) > 0\n        \n        with app.app_context():\n            skill_count = Skill.query.count()\n            assert skill_count >= len(sample_skills)\n\n    def test_sample_users_fixture(self, sample_users, app):\n        \"\"\"Test che la fixture sample_users funzioni.\"\"\"\n        assert sample_users is not None\n        assert len(sample_users) > 0\n        \n        with app.app_context():\n            # Verifica che ci sia almeno un admin\n            admin = User.query.filter_by(role='admin').first()\n            assert admin is not None\n            assert admin.username == 'admin'\n\n    def test_database_isolation(self, app, init_database):\n        \"\"\"Test che i test siano isolati a livello di database.\"\"\"\n        with app.app_context():\n            # Crea un utente di test\n            user = User(\n                username='isolation_test',\n                email='<EMAIL>',\n                first_name='Isolation',\n                last_name='Test'\n            )\n            user.set_password('testpass')\n            from extensions import db\n            db.session.add(user)\n            db.session.commit()\n            \n            # Verifica che esista\n            found_user = User.query.filter_by(username='isolation_test').first()\n            assert found_user is not None\n\n    def test_database_isolation_cleanup(self, app):\n        \"\"\"Test che il cleanup del database funzioni tra i test.\"\"\"\n        with app.app_context():\n            # L'utente del test precedente non dovrebbe esistere più\n            # (a meno che non sia stato creato da una fixture)\n            user = User.query.filter_by(username='isolation_test').first()\n            # Questo potrebbe essere None se il cleanup funziona correttamente\n\n\nclass TestAuthenticationFlow:\n    \"\"\"Test per verificare il flusso di autenticazione.\"\"\"\n\n    def test_login_logout_flow(self, auth):\n        \"\"\"Test del flusso completo login/logout.\"\"\"\n        # Login\n        login_response = auth.login()\n        assert login_response.status_code in [200, 302]\n        \n        # Logout\n        logout_response = auth.logout()\n        assert logout_response.status_code in [200, 302]\n\n    def test_login_with_custom_credentials(self, auth, sample_users):\n        \"\"\"Test login con credenziali personalizzate.\"\"\"\n        # Prova login con utente manager\n        response = auth.login(username='manager', password='password')\n        assert response.status_code in [200, 302]\n\n    def test_login_failure(self, auth):\n        \"\"\"Test login con credenziali errate.\"\"\"\n        response = auth.login(username='nonexistent', password='wrongpass')\n        # Dovrebbe fallire ma non crashare\n        assert response.status_code in [200, 302, 401, 403]\n\n\nclass TestDatabaseFixtures:\n    \"\"\"Test per verificare le fixture del database.\"\"\"\n\n    def test_test_client_fixture(self, test_client, app):\n        \"\"\"Test che la fixture test_client funzioni.\"\"\"\n        assert test_client is not None\n        assert isinstance(test_client, int)\n        \n        with app.app_context():\n            from models import Client\n            client = Client.query.get(test_client)\n            assert client is not None\n            assert client.name == 'Test Client'\n\n    def test_test_project_fixture(self, test_project, app):\n        \"\"\"Test che la fixture test_project funzioni.\"\"\"\n        assert test_project is not None\n        assert isinstance(test_project, int)\n        \n        with app.app_context():\n            from models import Project\n            project = Project.query.get(test_project)\n            assert project is not None\n            assert project.name == 'Test Project'\n\n    def test_test_kpi_fixture(self, test_kpi, app):\n        \"\"\"Test che la fixture test_kpi funzioni.\"\"\"\n        assert test_kpi is not None\n        assert isinstance(test_kpi, int)\n        \n        with app.app_context():\n            from models import KPI\n            kpi = KPI.query.get(test_kpi)\n            assert kpi is not None\n            assert kpi.name == 'Test KPI'\n\n\nclass TestComplexFixtures:\n    \"\"\"Test per fixture più complesse con dipendenze.\"\"\"\n\n    def test_sample_user_skills_fixture(self, sample_user_skills, app):\n        \"\"\"Test che la fixture sample_user_skills funzioni.\"\"\"\n        if sample_user_skills:  # Potrebbe essere vuota se non ci sono abbastanza utenti/skill\n            assert len(sample_user_skills) > 0\n            \n            with app.app_context():\n                from models import UserSkill\n                user_skill_count = UserSkill.query.count()\n                assert user_skill_count >= len(sample_user_skills)\n\n    def test_sample_user_profiles_fixture(self, sample_user_profiles, app):\n        \"\"\"Test che la fixture sample_user_profiles funzioni.\"\"\"\n        if sample_user_profiles:  # Potrebbe essere vuota se non ci sono abbastanza utenti\n            assert len(sample_user_profiles) > 0\n            \n            with app.app_context():\n                from models import UserProfile\n                profile_count = UserProfile.query.count()\n                assert profile_count >= len(sample_user_profiles)\n\n    def test_all_fixtures_together(self, app, auth, sample_users, sample_departments, \n                                 sample_skills, test_client, test_project):\n        \"\"\"Test che tutte le fixture funzionino insieme.\"\"\"\n        # Verifica che tutte le fixture siano state create\n        assert app is not None\n        assert auth is not None\n        assert sample_users is not None\n        assert sample_departments is not None\n        assert sample_skills is not None\n        assert test_client is not None\n        assert test_project is not None\n        \n        # Verifica che il login funzioni\n        response = auth.login()\n        assert response.status_code in [200, 302]\n        \n        with app.app_context():\n            # Verifica che i dati siano nel database\n            user_count = User.query.count()\n            dept_count = Department.query.count()\n            skill_count = Skill.query.count()\n            \n            assert user_count > 0\n            assert dept_count > 0\n            assert skill_count > 0\n"}
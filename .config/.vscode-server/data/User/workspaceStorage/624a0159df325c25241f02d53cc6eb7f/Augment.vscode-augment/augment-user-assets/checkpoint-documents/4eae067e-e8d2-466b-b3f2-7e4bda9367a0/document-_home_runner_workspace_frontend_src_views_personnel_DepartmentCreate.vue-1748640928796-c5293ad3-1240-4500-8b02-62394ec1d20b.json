{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/personnel/DepartmentCreate.vue"}, "originalCode": "<template>\n  <div class=\"min-h-screen bg-gray-50 dark:bg-gray-900 py-6\">\n    <div class=\"max-w-3xl mx-auto px-4 sm:px-6 lg:px-8\">\n      <!-- Header -->\n      <div class=\"mb-8\">\n        <div class=\"flex items-center space-x-4\">\n          <router-link to=\"/app/personnel/departments\"\n                       class=\"text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200\">\n            <svg class=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fill-rule=\"evenodd\" d=\"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z\" clip-rule=\"evenodd\"></path>\n            </svg>\n          </router-link>\n          <div>\n            <h1 class=\"text-2xl font-bold text-gray-900 dark:text-white\">Crea Nuovo Dipartimento</h1>\n            <p class=\"text-gray-600 dark:text-gray-400 mt-1\">Aggiungi un nuovo dipartimento all'organizzazione</p>\n          </div>\n        </div>\n      </div>\n\n      <!-- Form -->\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg\">\n        <form @submit.prevent=\"createDepartment\" class=\"p-6 space-y-6\">\n          <!-- Nome Dipartimento -->\n          <div>\n            <label for=\"name\" class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Nome Dipartimento *\n            </label>\n            <input\n              id=\"name\"\n              v-model=\"form.name\"\n              type=\"text\"\n              required\n              class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              placeholder=\"Es. Sviluppo Software\"\n            >\n            <p v-if=\"errors.name\" class=\"mt-1 text-sm text-red-600 dark:text-red-400\">{{ errors.name }}</p>\n          </div>\n\n          <!-- Descrizione -->\n          <div>\n            <label for=\"description\" class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Descrizione\n            </label>\n            <textarea\n              id=\"description\"\n              v-model=\"form.description\"\n              rows=\"3\"\n              class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              placeholder=\"Descrizione del dipartimento e delle sue responsabilità\"\n            ></textarea>\n          </div>\n\n          <!-- Dipartimento Padre -->\n          <div>\n            <label for=\"parent_id\" class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Dipartimento Padre\n            </label>\n            <select\n              id=\"parent_id\"\n              v-model=\"form.parent_id\"\n              class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            >\n              <option value=\"\">Nessun dipartimento padre (livello radice)</option>\n              <option v-for=\"dept in departments\" :key=\"dept.id\" :value=\"dept.id\">\n                {{ dept.name }}\n              </option>\n            </select>\n          </div>\n\n          <!-- Manager -->\n          <div>\n            <label for=\"manager_id\" class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Manager\n            </label>\n            <select\n              id=\"manager_id\"\n              v-model=\"form.manager_id\"\n              class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            >\n              <option value=\"\">Nessun manager assegnato</option>\n              <option v-for=\"user in users\" :key=\"user.id\" :value=\"user.id\">\n                {{ user.first_name }} {{ user.last_name }} ({{ user.email }})\n              </option>\n            </select>\n          </div>\n", "modifiedCode": "<template>\n  <div class=\"min-h-screen bg-gray-50 dark:bg-gray-900 py-6\">\n    <div class=\"max-w-3xl mx-auto px-4 sm:px-6 lg:px-8\">\n      <!-- Header -->\n      <div class=\"mb-8\">\n        <div class=\"flex items-center space-x-4\">\n          <router-link to=\"/app/personnel/departments\"\n                       class=\"text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200\">\n            <svg class=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fill-rule=\"evenodd\" d=\"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z\" clip-rule=\"evenodd\"></path>\n            </svg>\n          </router-link>\n          <div>\n            <h1 class=\"text-2xl font-bold text-gray-900 dark:text-white\">Crea Nuovo Dipartimento</h1>\n            <p class=\"text-gray-600 dark:text-gray-400 mt-1\">Aggiungi un nuovo dipartimento all'organizzazione</p>\n          </div>\n        </div>\n      </div>\n\n      <!-- Form -->\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg\">\n        <form @submit.prevent=\"createDepartment\" class=\"p-6 space-y-6\">\n          <!-- Nome Dipartimento -->\n          <div>\n            <label for=\"name\" class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Nome Dipartimento *\n            </label>\n            <input\n              id=\"name\"\n              v-model=\"form.name\"\n              type=\"text\"\n              required\n              class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              placeholder=\"Es. Sviluppo Software\"\n            >\n            <p v-if=\"errors.name\" class=\"mt-1 text-sm text-red-600 dark:text-red-400\">{{ errors.name }}</p>\n          </div>\n\n          <!-- Descrizione -->\n          <div>\n            <label for=\"description\" class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Descrizione\n            </label>\n            <textarea\n              id=\"description\"\n              v-model=\"form.description\"\n              rows=\"3\"\n              class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              placeholder=\"Descrizione del dipartimento e delle sue responsabilità\"\n            ></textarea>\n          </div>\n\n          <!-- Dipartimento Padre -->\n          <div>\n            <label for=\"parent_id\" class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Dipartimento Padre\n            </label>\n            <select\n              id=\"parent_id\"\n              v-model=\"form.parent_id\"\n              class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            >\n              <option value=\"\">Nessun dipartimento padre (livello radice)</option>\n              <option v-for=\"dept in departments\" :key=\"dept.id\" :value=\"dept.id\">\n                {{ dept.name }}\n              </option>\n            </select>\n          </div>\n\n          <!-- Manager -->\n          <div>\n            <label for=\"manager_id\" class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Manager\n            </label>\n            <select\n              id=\"manager_id\"\n              v-model=\"form.manager_id\"\n              class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            >\n              <option value=\"\">Nessun manager assegnato</option>\n              <option v-for=\"user in users\" :key=\"user.id\" :value=\"user.id\">\n                {{ user.first_name }} {{ user.last_name }} ({{ user.email }})\n              </option>\n            </select>\n          </div>\n\n          <!-- Budget (opzionale) -->\n          <div>\n            <label for=\"budget\" class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Budget Annuale (€)\n            </label>\n            <input\n              id=\"budget\"\n              v-model.number=\"form.budget\"\n              type=\"number\"\n              min=\"0\"\n              step=\"1000\"\n              class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              placeholder=\"0\"\n            >\n          </div>\n\n          <!-- Error Message -->\n          <div v-if=\"error\" class=\"bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md p-4\">\n            <div class=\"flex\">\n              <div class=\"flex-shrink-0\">\n                <svg class=\"h-5 w-5 text-red-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                  <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clip-rule=\"evenodd\" />\n                </svg>\n              </div>\n              <div class=\"ml-3\">\n                <h3 class=\"text-sm font-medium text-red-800 dark:text-red-200\">\n                  Errore nella creazione\n                </h3>\n                <div class=\"mt-2 text-sm text-red-700 dark:text-red-300\">\n                  {{ error }}\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Actions -->\n          <div class=\"flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700\">\n            <router-link\n              to=\"/app/personnel/departments\"\n              class=\"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700\"\n            >\n              Annulla\n            </router-link>\n            <button\n              type=\"submit\"\n              :disabled=\"loading\"\n              class=\"px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-md text-sm font-medium transition-colors duration-200\"\n            >\n              <span v-if=\"loading\" class=\"flex items-center\">\n                <svg class=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\" fill=\"none\" viewBox=\"0 0 24 24\">\n                  <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\"></circle>\n                  <path class=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                </svg>\n                Creazione...\n              </span>\n              <span v-else>Crea Dipartimento</span>\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, onMounted } from 'vue'\nimport { useRouter } from 'vue-router'\n\nconst router = useRouter()\n\n// Reactive state\nconst form = ref({\n  name: '',\n  description: '',\n  parent_id: '',\n  manager_id: '',\n  budget: null\n})\n\nconst departments = ref([])\nconst users = ref([])\nconst loading = ref(false)\nconst error = ref(null)\nconst errors = ref({})\n\n// API functions\nconst fetchDepartments = async () => {\n  try {\n    const response = await fetch('/api/personnel/departments', {\n      credentials: 'include'\n    })\n\n    if (!response.ok) {\n      throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n\n    if (data.success) {\n      departments.value = data.data.departments || []\n    }\n  } catch (err) {\n    console.error('Error fetching departments:', err)\n  }\n}\n\nconst fetchUsers = async () => {\n  try {\n    const response = await fetch('/api/personnel/users', {\n      credentials: 'include'\n    })\n\n    if (!response.ok) {\n      throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n\n    if (data.success) {\n      users.value = data.data.users || []\n    }\n  } catch (err) {\n    console.error('Error fetching users:', err)\n  }\n}\n\nconst createDepartment = async () => {\n  loading.value = true\n  error.value = null\n  errors.value = {}\n\n  try {\n    const payload = {\n      name: form.value.name,\n      description: form.value.description,\n      parent_id: form.value.parent_id || null,\n      manager_id: form.value.manager_id || null,\n      budget: form.value.budget || null\n    }\n\n    const response = await fetch('/api/personnel/departments', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      credentials: 'include',\n      body: JSON.stringify(payload)\n    })\n\n    if (!response.ok) {\n      throw new Error(`HTTP ${response.status}: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n\n    if (data.success) {\n      // Redirect to departments list\n      router.push('/app/personnel/departments')\n    } else {\n      throw new Error(data.message || 'Errore nella creazione del dipartimento')\n    }\n  } catch (err) {\n    console.error('Error creating department:', err)\n    error.value = err.message\n  } finally {\n    loading.value = false\n  }\n}\n\n// Lifecycle\nonMounted(async () => {\n  await Promise.all([\n    fetchDepartments(),\n    fetchUsers()\n  ])\n})\n</script>"}
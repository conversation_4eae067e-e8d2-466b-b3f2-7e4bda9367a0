import{r as c,f as x,A as H,u as V,w as j,c as r,g as i,j as e,t as l,a as g,i as h,b as L,m as w,F as f,k as y,l as N,o as a,n as k}from"./vendor.js";import{u as E}from"./personnel.js";import{a as P}from"./app.js";const T={class:"department-view"},$={key:0,class:"flex justify-center items-center h-64"},A={key:1,class:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6"},D={class:"flex"},S={class:"text-sm text-red-700 dark:text-red-300 mt-1"},I={key:2,class:"space-y-6"},F={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},R={class:"bg-gradient-to-r from-blue-500 to-purple-600 px-6 py-8"},q={class:"flex items-center justify-between"},U={class:"flex items-center space-x-4"},G={class:"flex items-center space-x-3"},J={class:"text-3xl font-bold text-white"},K={key:0,class:"text-blue-100 text-lg"},O={class:"flex items-center space-x-6 mt-4"},Q={key:0,class:"flex items-center text-white"},W={class:"text-sm"},X={class:"flex items-center text-white"},Y={class:"text-sm"},Z={key:1,class:"flex items-center text-white"},ee={class:"text-sm"},te={key:0,class:"flex space-x-3"},se={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},re={class:"border-b border-gray-200 dark:border-gray-700"},ae={class:"-mb-px flex space-x-8 px-6","aria-label":"Tabs"},oe=["onClick"],le={key:0,class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},ne={key:1,class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},de={key:2,class:"ml-2 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 py-0.5 px-2 rounded-full text-xs"},ie={class:"p-6"},ue={key:0},ce={key:0,class:"space-y-4"},ve={class:"flex items-center justify-between"},me={class:"flex items-center space-x-4"},pe={class:"font-medium text-gray-900 dark:text-white"},xe={class:"text-sm text-gray-500 dark:text-gray-400"},ge={class:"text-sm text-gray-500 dark:text-gray-400"},he={class:"flex items-center space-x-2"},fe={key:1,class:"text-center py-8"},ye={key:1},_e={key:0,class:"space-y-4"},be={class:"flex items-center justify-between"},we={class:"flex items-center space-x-4"},ke={class:"font-medium text-gray-900 dark:text-white"},Me={class:"text-sm text-gray-500 dark:text-gray-400"},ze={key:1,class:"text-center py-8"},Le={__name:"DepartmentView",setup(Ce){const _=V();N(),E();const{hasPermission:M}=P(),o=c(null),m=c(!1),u=c(null),p=c(!1),v=c("employees"),z=x(()=>M("manage_users")),C=x(()=>{var n,t,d,s;return[{id:"employees",name:"Dipendenti",count:((t=(n=o.value)==null?void 0:n.employees)==null?void 0:t.length)||0},{id:"subdepartments",name:"Sotto-dipartimenti",count:((s=(d=o.value)==null?void 0:d.subdepartments)==null?void 0:s.length)||0}]});x(()=>o.value?{activeProjects:0,budgetUsagePercentage:o.value.budget?Math.round((o.value.budget_used||0)/o.value.budget*100):0}:{});const B=n=>new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(n),b=async n=>{m.value=!0,u.value=null;try{const t=await fetch(`/api/personnel/departments/${n}`,{credentials:"include"});if(!t.ok)throw new Error(`HTTP ${t.status}: ${t.statusText}`);const d=await t.json();if(d.success)o.value=d.data.department;else throw new Error(d.message||"Errore nel caricamento del dipartimento")}catch(t){console.error("Error fetching department:",t),u.value=t.message}finally{m.value=!1}};return H(async()=>{const n=_.params.id;if(!n){u.value="ID dipartimento non specificato";return}await b(n)}),j(()=>_.params.id,async n=>{n&&await b(n)}),(n,t)=>{const d=L("router-link");return a(),r("div",T,[m.value?(a(),r("div",$,t[1]||(t[1]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):u.value?(a(),r("div",A,[e("div",D,[t[3]||(t[3]=e("svg",{class:"w-5 h-5 text-red-400 mr-2 mt-0.5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z","clip-rule":"evenodd"})],-1)),e("div",null,[t[2]||(t[2]=e("h3",{class:"text-sm font-medium text-red-800 dark:text-red-200"},"Errore nel caricamento del dipartimento",-1)),e("p",S,l(u.value),1)])])])):o.value?(a(),r("div",I,[e("div",F,[e("div",R,[e("div",q,[e("div",U,[g(d,{to:"/app/personnel/departments",class:"text-white hover:text-blue-100 transition-colors duration-200"},{default:h(()=>t[4]||(t[4]=[e("svg",{class:"w-6 h-6",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z","clip-rule":"evenodd"})],-1)])),_:1,__:[4]}),e("div",null,[e("div",G,[t[5]||(t[5]=e("div",{class:"w-12 h-12 bg-white rounded-lg flex items-center justify-center shadow-lg"},[e("svg",{class:"w-6 h-6 text-blue-600",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z","clip-rule":"evenodd"})])],-1)),e("div",null,[e("h1",J,l(o.value.name),1),o.value.description?(a(),r("p",K,l(o.value.description),1)):i("",!0)])]),e("div",O,[o.value.manager?(a(),r("div",Q,[t[6]||(t[6]=e("svg",{class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z","clip-rule":"evenodd"})],-1)),e("span",W,"Manager: "+l(o.value.manager.full_name),1)])):i("",!0),e("div",X,[t[7]||(t[7]=e("svg",{class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"})],-1)),e("span",Y,l(o.value.employee_count)+" dipendenti",1)]),o.value.budget?(a(),r("div",Z,[t[8]||(t[8]=e("svg",{class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"}),e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z","clip-rule":"evenodd"})],-1)),e("span",ee,"Budget: "+l(B(o.value.budget)),1)])):i("",!0)])])]),z.value?(a(),r("div",te,[e("button",{onClick:t[0]||(t[0]=s=>p.value=!p.value),class:"bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-colors duration-200"},[t[9]||(t[9]=e("svg",{class:"w-5 h-5 inline mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"})],-1)),w(" "+l(p.value?"Annulla":"Modifica"),1)])])):i("",!0)])])]),e("div",se,[e("div",re,[e("nav",ae,[(a(!0),r(f,null,y(C.value,s=>(a(),r("button",{key:s.id,onClick:Be=>v.value=s.id,class:k([v.value===s.id?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300","whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center"])},[s.id==="employees"?(a(),r("svg",le,t[10]||(t[10]=[e("path",{d:"M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"},null,-1)]))):s.id==="subdepartments"?(a(),r("svg",ne,t[11]||(t[11]=[e("path",{"fill-rule":"evenodd",d:"M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 0l-2 2a1 1 0 101.414 1.414L8 10.414l1.293 1.293a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"},null,-1)]))):i("",!0),w(" "+l(s.name)+" ",1),s.count!==void 0?(a(),r("span",de,l(s.count),1)):i("",!0)],10,oe))),128))])]),e("div",ie,[v.value==="employees"?(a(),r("div",ue,[o.value.employees&&o.value.employees.length>0?(a(),r("div",ce,[(a(!0),r(f,null,y(o.value.employees,s=>(a(),r("div",{key:s.id,class:"border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"},[e("div",ve,[e("div",me,[t[12]||(t[12]=e("div",{class:"w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-gray-600 dark:text-gray-300",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z","clip-rule":"evenodd"})])],-1)),e("div",null,[e("h4",pe,l(s.full_name),1),e("p",xe,l(s.position||"Posizione non specificata"),1),e("p",ge,l(s.email),1)])]),e("div",he,[e("span",{class:k(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",s.is_active?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200":"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"])},l(s.is_active?"Attivo":"Inattivo"),3),g(d,{to:`/app/personnel/${s.id}`,class:"text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300"},{default:h(()=>t[13]||(t[13]=[e("svg",{class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M10 12a2 2 0 100-4 2 2 0 000 4z"}),e("path",{"fill-rule":"evenodd",d:"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z","clip-rule":"evenodd"})],-1)])),_:2,__:[13]},1032,["to"])])])]))),128))])):(a(),r("div",fe,t[14]||(t[14]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun dipendente",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"Non ci sono dipendenti assegnati a questo dipartimento.",-1)])))])):i("",!0),v.value==="subdepartments"?(a(),r("div",ye,[o.value.subdepartments&&o.value.subdepartments.length>0?(a(),r("div",_e,[(a(!0),r(f,null,y(o.value.subdepartments,s=>(a(),r("div",{key:s.id,class:"border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"},[e("div",be,[e("div",we,[t[15]||(t[15]=e("div",{class:"w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-blue-600 dark:text-blue-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z","clip-rule":"evenodd"})])],-1)),e("div",null,[e("h4",ke,l(s.name),1),e("p",Me,l(s.employee_count)+" dipendenti",1)])]),g(d,{to:`/app/personnel/departments/${s.id}`,class:"text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300"},{default:h(()=>t[16]||(t[16]=[e("svg",{class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M10 12a2 2 0 100-4 2 2 0 000 4z"}),e("path",{"fill-rule":"evenodd",d:"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z","clip-rule":"evenodd"})],-1)])),_:2,__:[16]},1032,["to"])])]))),128))])):(a(),r("div",ze,t[17]||(t[17]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun sotto-dipartimento",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"Non ci sono sotto-dipartimenti per questo dipartimento.",-1)])))])):i("",!0)])])])):i("",!0)])}}};export{Le as default};

var kn=Object.defineProperty;var ot=u=>{throw TypeError(u)};var $n=(u,e,t)=>e in u?kn(u,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):u[e]=t;var _=(u,e,t)=>$n(u,typeof e!="symbol"?e+"":e,t),mn=(u,e,t)=>e.has(u)||ot("Cannot "+t);var it=(u,e,t)=>e.has(u)?ot("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(u):e.set(u,t);var ke=(u,e,t)=>(mn(u,e,"access private method"),t);import{S as B,i as y,s as b,E as ee,e as C,u as F,t as m,h as E,P,a as se,Q as A,aC as Ee,V as O,W as N,X as H,g as Ve,q as J,r as V,F as de,y as Q,z as W,B as U,aB as lt,G as Te,H as Le,n as L,c as k,f as R,D as te,aD as xn,aE as at,a0 as Cn,w as Xe,x as En,a4 as ct,A as An,a6 as wn,ag as Ae,a7 as bn,b as re,ae as vn,ap as Bn,Y as yn,Z as we,j as sn,a2 as Z,aa as pt,a5 as Sn,ab as zn,a9 as Tn,aj as Ln,ai as ht}from"./SpinnerAugment-BRymMBwV.js";import{e as G}from"./BaseButton-rKFNr-KO.js";import"./toggleHighContrast-CwIv4U26.js";import{S as In,a as _n}from"./index-C59i2ECO.js";import{I as Rn}from"./IconButtonAugment-5yqT_m78.js";function dt(...u){return"/"+u.flatMap(e=>e.split("/")).filter(e=>!!e).join("/")}function Dt(u){return u.startsWith("/")||u.startsWith("#")}function je(u){let e,t;const n=u[5].default,r=P(n,u,u[4],null);let s=[{id:u[1]}],o={};for(let i=0;i<s.length;i+=1)o=se(o,s[i]);return{c(){e=A(`h${u[0].depth}`),r&&r.c(),Ee(`h${u[0].depth}`)(e,o)},m(i,l){C(i,e,l),r&&r.m(e,null),t=!0},p(i,l){r&&r.p&&(!t||16&l)&&O(r,n,i,i[4],t?H(n,i[4],l,null):N(i[4]),null),Ee(`h${i[0].depth}`)(e,o=Ve(s,[(!t||2&l)&&{id:i[1]}]))},i(i){t||(F(r,i),t=!0)},o(i){m(r,i),t=!1},d(i){i&&E(e),r&&r.d(i)}}}function jn(u){let e,t,n=`h${u[0].depth}`,r=`h${u[0].depth}`&&je(u);return{c(){r&&r.c(),e=ee()},m(s,o){r&&r.m(s,o),C(s,e,o),t=!0},p(s,[o]){`h${s[0].depth}`?n?b(n,`h${s[0].depth}`)?(r.d(1),r=je(s),n=`h${s[0].depth}`,r.c(),r.m(e.parentNode,e)):r.p(s,o):(r=je(s),n=`h${s[0].depth}`,r.c(),r.m(e.parentNode,e)):n&&(r.d(1),r=null,n=`h${s[0].depth}`)},i(s){t||(F(r,s),t=!0)},o(s){m(r,s),t=!1},d(s){s&&E(e),r&&r.d(s)}}}function Pn(u,e,t){let{$$slots:n={},$$scope:r}=e,{token:s}=e,{options:o}=e,i;return u.$$set=l=>{"token"in l&&t(0,s=l.token),"options"in l&&t(2,o=l.options),"$$scope"in l&&t(4,r=l.$$scope)},u.$$.update=()=>{var l,a;5&u.$$.dirty&&t(1,(l=s.text,a=o.slugger,i=a.slug(l).replace(/--+/g,"-")))},[s,i,o,void 0,r,n]}class On extends B{constructor(e){super(),y(this,e,Pn,jn,b,{token:0,options:2,renderers:3})}get renderers(){return this.$$.ctx[3]}}function Nn(u){let e,t;const n=u[4].default,r=P(n,u,u[3],null);return{c(){e=A("blockquote"),r&&r.c()},m(s,o){C(s,e,o),r&&r.m(e,null),t=!0},p(s,[o]){r&&r.p&&(!t||8&o)&&O(r,n,s,s[3],t?H(n,s[3],o,null):N(s[3]),null)},i(s){t||(F(r,s),t=!0)},o(s){m(r,s),t=!1},d(s){s&&E(e),r&&r.d(s)}}}function Hn(u,e,t){let{$$slots:n={},$$scope:r}=e;return u.$$set=s=>{"$$scope"in s&&t(3,r=s.$$scope)},[void 0,void 0,void 0,r,n]}class qn extends B{constructor(e){super(),y(this,e,Hn,Nn,b,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function ft(u,e,t){const n=u.slice();return n[3]=e[t],n}function gt(u){let e,t,n=G(u[0]),r=[];for(let o=0;o<n.length;o+=1)r[o]=Ft(ft(u,n,o));const s=o=>m(r[o],1,1,()=>{r[o]=null});return{c(){for(let o=0;o<r.length;o+=1)r[o].c();e=ee()},m(o,i){for(let l=0;l<r.length;l+=1)r[l]&&r[l].m(o,i);C(o,e,i),t=!0},p(o,i){if(7&i){let l;for(n=G(o[0]),l=0;l<n.length;l+=1){const a=ft(o,n,l);r[l]?(r[l].p(a,i),F(r[l],1)):(r[l]=Ft(a),r[l].c(),F(r[l],1),r[l].m(e.parentNode,e))}for(J(),l=n.length;l<r.length;l+=1)s(l);V()}},i(o){if(!t){for(let i=0;i<n.length;i+=1)F(r[i]);t=!0}},o(o){r=r.filter(Boolean);for(let i=0;i<r.length;i+=1)m(r[i]);t=!1},d(o){o&&E(e),de(r,o)}}}function Ft(u){let e,t;return e=new un({props:{token:u[3],renderers:u[1],options:u[2]}}),{c(){Q(e.$$.fragment)},m(n,r){W(e,n,r),t=!0},p(n,r){const s={};1&r&&(s.token=n[3]),2&r&&(s.renderers=n[1]),4&r&&(s.options=n[2]),e.$set(s)},i(n){t||(F(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){U(e,n)}}}function Zn(u){let e,t,n=u[0]&&gt(u);return{c(){n&&n.c(),e=ee()},m(r,s){n&&n.m(r,s),C(r,e,s),t=!0},p(r,[s]){r[0]?n?(n.p(r,s),1&s&&F(n,1)):(n=gt(r),n.c(),F(n,1),n.m(e.parentNode,e)):n&&(J(),m(n,1,1,()=>{n=null}),V())},i(r){t||(F(n),t=!0)},o(r){m(n),t=!1},d(r){r&&E(e),n&&n.d(r)}}}function Mn(u,e,t){let{tokens:n}=e,{renderers:r}=e,{options:s}=e;return u.$$set=o=>{"tokens"in o&&t(0,n=o.tokens),"renderers"in o&&t(1,r=o.renderers),"options"in o&&t(2,s=o.options)},[n,r,s]}class Ie extends B{constructor(e){super(),y(this,e,Mn,Zn,b,{tokens:0,renderers:1,options:2})}}function kt(u){let e,t,n;var r=u[1][u[0].type];function s(o,i){return{props:{token:o[0],options:o[2],renderers:o[1],$$slots:{default:[Un]},$$scope:{ctx:o}}}}return r&&(e=lt(r,s(u))),{c(){e&&Q(e.$$.fragment),t=ee()},m(o,i){e&&W(e,o,i),C(o,t,i),n=!0},p(o,i){if(3&i&&r!==(r=o[1][o[0].type])){if(e){J();const l=e;m(l.$$.fragment,1,0,()=>{U(l,1)}),V()}r?(e=lt(r,s(o)),Q(e.$$.fragment),F(e.$$.fragment,1),W(e,t.parentNode,t)):e=null}else if(r){const l={};1&i&&(l.token=o[0]),4&i&&(l.options=o[2]),2&i&&(l.renderers=o[1]),15&i&&(l.$$scope={dirty:i,ctx:o}),e.$set(l)}},i(o){n||(e&&F(e.$$.fragment,o),n=!0)},o(o){e&&m(e.$$.fragment,o),n=!1},d(o){o&&E(t),e&&U(e,o)}}}function Qn(u){let e,t=u[0].raw+"";return{c(){e=Te(t)},m(n,r){C(n,e,r)},p(n,r){1&r&&t!==(t=n[0].raw+"")&&Le(e,t)},i:L,o:L,d(n){n&&E(e)}}}function Wn(u){let e,t;return e=new Ie({props:{tokens:u[0].tokens,renderers:u[1],options:u[2]}}),{c(){Q(e.$$.fragment)},m(n,r){W(e,n,r),t=!0},p(n,r){const s={};1&r&&(s.tokens=n[0].tokens),2&r&&(s.renderers=n[1]),4&r&&(s.options=n[2]),e.$set(s)},i(n){t||(F(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){U(e,n)}}}function Un(u){let e,t,n,r;const s=[Wn,Qn],o=[];function i(l,a){return"tokens"in l[0]&&l[0].tokens?0:1}return e=i(u),t=o[e]=s[e](u),{c(){t.c(),n=ee()},m(l,a){o[e].m(l,a),C(l,n,a),r=!0},p(l,a){let c=e;e=i(l),e===c?o[e].p(l,a):(J(),m(o[c],1,1,()=>{o[c]=null}),V(),t=o[e],t?t.p(l,a):(t=o[e]=s[e](l),t.c()),F(t,1),t.m(n.parentNode,n))},i(l){r||(F(t),r=!0)},o(l){m(t),r=!1},d(l){l&&E(n),o[e].d(l)}}}function Jn(u){let e,t,n=u[1][u[0].type]&&kt(u);return{c(){n&&n.c(),e=ee()},m(r,s){n&&n.m(r,s),C(r,e,s),t=!0},p(r,[s]){r[1][r[0].type]?n?(n.p(r,s),3&s&&F(n,1)):(n=kt(r),n.c(),F(n,1),n.m(e.parentNode,e)):n&&(J(),m(n,1,1,()=>{n=null}),V())},i(r){t||(F(n),t=!0)},o(r){m(n),t=!1},d(r){r&&E(e),n&&n.d(r)}}}function Vn(u,e,t){let{token:n}=e,{renderers:r}=e,{options:s}=e;return u.$$set=o=>{"token"in o&&t(0,n=o.token),"renderers"in o&&t(1,r=o.renderers),"options"in o&&t(2,s=o.options)},[n,r,s]}class un extends B{constructor(e){super(),y(this,e,Vn,Jn,b,{token:0,renderers:1,options:2})}}function $t(u,e,t){const n=u.slice();return n[4]=e[t],n}function mt(u){let e,t;return e=new un({props:{token:{...u[4]},options:u[1],renderers:u[2]}}),{c(){Q(e.$$.fragment)},m(n,r){W(e,n,r),t=!0},p(n,r){const s={};1&r&&(s.token={...n[4]}),2&r&&(s.options=n[1]),4&r&&(s.renderers=n[2]),e.$set(s)},i(n){t||(F(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){U(e,n)}}}function Pe(u){let e,t,n,r=G(u[0].items),s=[];for(let a=0;a<r.length;a+=1)s[a]=mt($t(u,r,a));const o=a=>m(s[a],1,1,()=>{s[a]=null});let i=[{start:t=u[0].start||1}],l={};for(let a=0;a<i.length;a+=1)l=se(l,i[a]);return{c(){e=A(u[3]);for(let a=0;a<s.length;a+=1)s[a].c();Ee(u[3])(e,l)},m(a,c){C(a,e,c);for(let p=0;p<s.length;p+=1)s[p]&&s[p].m(e,null);n=!0},p(a,c){if(7&c){let p;for(r=G(a[0].items),p=0;p<r.length;p+=1){const g=$t(a,r,p);s[p]?(s[p].p(g,c),F(s[p],1)):(s[p]=mt(g),s[p].c(),F(s[p],1),s[p].m(e,null))}for(J(),p=r.length;p<s.length;p+=1)o(p);V()}Ee(a[3])(e,l=Ve(i,[(!n||1&c&&t!==(t=a[0].start||1))&&{start:t}]))},i(a){if(!n){for(let c=0;c<r.length;c+=1)F(s[c]);n=!0}},o(a){s=s.filter(Boolean);for(let c=0;c<s.length;c+=1)m(s[c]);n=!1},d(a){a&&E(e),de(s,a)}}}function Xn(u){let e,t=u[3],n=u[3]&&Pe(u);return{c(){n&&n.c(),e=ee()},m(r,s){n&&n.m(r,s),C(r,e,s)},p(r,[s]){r[3]?t?b(t,r[3])?(n.d(1),n=Pe(r),t=r[3],n.c(),n.m(e.parentNode,e)):n.p(r,s):(n=Pe(r),t=r[3],n.c(),n.m(e.parentNode,e)):t&&(n.d(1),n=null,t=r[3])},i:L,o(r){m(n,r)},d(r){r&&E(e),n&&n.d(r)}}}function Gn(u,e,t){let n,{token:r}=e,{options:s}=e,{renderers:o}=e;return u.$$set=i=>{"token"in i&&t(0,r=i.token),"options"in i&&t(1,s=i.options),"renderers"in i&&t(2,o=i.renderers)},u.$$.update=()=>{1&u.$$.dirty&&t(3,n=r.ordered?"ol":"ul")},[r,s,o,n]}class Yn extends B{constructor(e){super(),y(this,e,Gn,Xn,b,{token:0,options:1,renderers:2})}}function Kn(u){let e,t;const n=u[4].default,r=P(n,u,u[3],null);return{c(){e=A("li"),r&&r.c()},m(s,o){C(s,e,o),r&&r.m(e,null),t=!0},p(s,[o]){r&&r.p&&(!t||8&o)&&O(r,n,s,s[3],t?H(n,s[3],o,null):N(s[3]),null)},i(s){t||(F(r,s),t=!0)},o(s){m(r,s),t=!1},d(s){s&&E(e),r&&r.d(s)}}}function er(u,e,t){let{$$slots:n={},$$scope:r}=e;return u.$$set=s=>{"$$scope"in s&&t(3,r=s.$$scope)},[void 0,void 0,void 0,r,n]}class tr extends B{constructor(e){super(),y(this,e,er,Kn,b,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function nr(u){let e;return{c(){e=A("br")},m(t,n){C(t,e,n)},p:L,i:L,o:L,d(t){t&&E(e)}}}function rr(u,e,t){return[void 0,void 0,void 0]}class sr extends B{constructor(e){super(),y(this,e,rr,nr,b,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function ur(u){let e,t,n,r,s=u[0].text+"";return{c(){e=A("pre"),t=A("code"),n=Te(s),k(t,"class",r=`lang-${u[0].lang}`)},m(o,i){C(o,e,i),R(e,t),R(t,n)},p(o,[i]){1&i&&s!==(s=o[0].text+"")&&Le(n,s),1&i&&r!==(r=`lang-${o[0].lang}`)&&k(t,"class",r)},i:L,o:L,d(o){o&&E(e)}}}function or(u,e,t){let{token:n}=e;return u.$$set=r=>{"token"in r&&t(0,n=r.token)},[n,void 0,void 0]}class ir extends B{constructor(e){super(),y(this,e,or,ur,b,{token:0,options:1,renderers:2})}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function lr(u){let e,t,n=u[0].raw.slice(1,u[0].raw.length-1)+"";return{c(){e=A("code"),t=Te(n)},m(r,s){C(r,e,s),R(e,t)},p(r,[s]){1&s&&n!==(n=r[0].raw.slice(1,r[0].raw.length-1)+"")&&Le(t,n)},i:L,o:L,d(r){r&&E(e)}}}function ar(u,e,t){let{token:n}=e;return u.$$set=r=>{"token"in r&&t(0,n=r.token)},[n,void 0,void 0]}class cr extends B{constructor(e){super(),y(this,e,ar,lr,b,{token:0,options:1,renderers:2})}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function xt(u,e,t){const n=u.slice();return n[3]=e[t],n}function Ct(u,e,t){const n=u.slice();return n[6]=e[t],n}function Et(u,e,t){const n=u.slice();return n[9]=e[t],n}function At(u){let e,t,n,r;return t=new Ie({props:{tokens:u[9].tokens,options:u[1],renderers:u[2]}}),{c(){e=A("th"),Q(t.$$.fragment),n=te(),k(e,"scope","col")},m(s,o){C(s,e,o),W(t,e,null),R(e,n),r=!0},p(s,o){const i={};1&o&&(i.tokens=s[9].tokens),2&o&&(i.options=s[1]),4&o&&(i.renderers=s[2]),t.$set(i)},i(s){r||(F(t.$$.fragment,s),r=!0)},o(s){m(t.$$.fragment,s),r=!1},d(s){s&&E(e),U(t)}}}function wt(u){let e,t,n;return t=new Ie({props:{tokens:u[6].tokens,options:u[1],renderers:u[2]}}),{c(){e=A("td"),Q(t.$$.fragment)},m(r,s){C(r,e,s),W(t,e,null),n=!0},p(r,s){const o={};1&s&&(o.tokens=r[6].tokens),2&s&&(o.options=r[1]),4&s&&(o.renderers=r[2]),t.$set(o)},i(r){n||(F(t.$$.fragment,r),n=!0)},o(r){m(t.$$.fragment,r),n=!1},d(r){r&&E(e),U(t)}}}function bt(u){let e,t,n,r=G(u[3]),s=[];for(let i=0;i<r.length;i+=1)s[i]=wt(Ct(u,r,i));const o=i=>m(s[i],1,1,()=>{s[i]=null});return{c(){e=A("tr");for(let i=0;i<s.length;i+=1)s[i].c();t=te()},m(i,l){C(i,e,l);for(let a=0;a<s.length;a+=1)s[a]&&s[a].m(e,null);R(e,t),n=!0},p(i,l){if(7&l){let a;for(r=G(i[3]),a=0;a<r.length;a+=1){const c=Ct(i,r,a);s[a]?(s[a].p(c,l),F(s[a],1)):(s[a]=wt(c),s[a].c(),F(s[a],1),s[a].m(e,t))}for(J(),a=r.length;a<s.length;a+=1)o(a);V()}},i(i){if(!n){for(let l=0;l<r.length;l+=1)F(s[l]);n=!0}},o(i){s=s.filter(Boolean);for(let l=0;l<s.length;l+=1)m(s[l]);n=!1},d(i){i&&E(e),de(s,i)}}}function pr(u){let e,t,n,r,s,o,i=G(u[0].header),l=[];for(let h=0;h<i.length;h+=1)l[h]=At(Et(u,i,h));const a=h=>m(l[h],1,1,()=>{l[h]=null});let c=G(u[0].rows),p=[];for(let h=0;h<c.length;h+=1)p[h]=bt(xt(u,c,h));const g=h=>m(p[h],1,1,()=>{p[h]=null});return{c(){e=A("table"),t=A("thead"),n=A("tr");for(let h=0;h<l.length;h+=1)l[h].c();r=te(),s=A("tbody");for(let h=0;h<p.length;h+=1)p[h].c()},m(h,f){C(h,e,f),R(e,t),R(t,n);for(let d=0;d<l.length;d+=1)l[d]&&l[d].m(n,null);R(e,r),R(e,s);for(let d=0;d<p.length;d+=1)p[d]&&p[d].m(s,null);o=!0},p(h,[f]){if(7&f){let d;for(i=G(h[0].header),d=0;d<i.length;d+=1){const D=Et(h,i,d);l[d]?(l[d].p(D,f),F(l[d],1)):(l[d]=At(D),l[d].c(),F(l[d],1),l[d].m(n,null))}for(J(),d=i.length;d<l.length;d+=1)a(d);V()}if(7&f){let d;for(c=G(h[0].rows),d=0;d<c.length;d+=1){const D=xt(h,c,d);p[d]?(p[d].p(D,f),F(p[d],1)):(p[d]=bt(D),p[d].c(),F(p[d],1),p[d].m(s,null))}for(J(),d=c.length;d<p.length;d+=1)g(d);V()}},i(h){if(!o){for(let f=0;f<i.length;f+=1)F(l[f]);for(let f=0;f<c.length;f+=1)F(p[f]);o=!0}},o(h){l=l.filter(Boolean);for(let f=0;f<l.length;f+=1)m(l[f]);p=p.filter(Boolean);for(let f=0;f<p.length;f+=1)m(p[f]);o=!1},d(h){h&&E(e),de(l,h),de(p,h)}}}function hr(u,e,t){let{token:n}=e,{options:r}=e,{renderers:s}=e;return u.$$set=o=>{"token"in o&&t(0,n=o.token),"options"in o&&t(1,r=o.options),"renderers"in o&&t(2,s=o.renderers)},[n,r,s]}class dr extends B{constructor(e){super(),y(this,e,hr,pr,b,{token:0,options:1,renderers:2})}}function Dr(u){let e,t,n=u[0].text+"";return{c(){e=new xn(!1),t=ee(),e.a=t},m(r,s){e.m(n,r,s),C(r,t,s)},p(r,[s]){1&s&&n!==(n=r[0].text+"")&&e.p(n)},i:L,o:L,d(r){r&&(E(t),e.d())}}}function fr(u,e,t){let{token:n}=e;return u.$$set=r=>{"token"in r&&t(0,n=r.token)},[n,void 0,void 0]}class gr extends B{constructor(e){super(),y(this,e,fr,Dr,b,{token:0,options:1,renderers:2})}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Fr(u){let e,t;const n=u[4].default,r=P(n,u,u[3],null);return{c(){e=A("p"),r&&r.c()},m(s,o){C(s,e,o),r&&r.m(e,null),t=!0},p(s,[o]){r&&r.p&&(!t||8&o)&&O(r,n,s,s[3],t?H(n,s[3],o,null):N(s[3]),null)},i(s){t||(F(r,s),t=!0)},o(s){m(r,s),t=!1},d(s){s&&E(e),r&&r.d(s)}}}function kr(u,e,t){let{$$slots:n={},$$scope:r}=e;return u.$$set=s=>{"$$scope"in s&&t(3,r=s.$$scope)},[void 0,void 0,void 0,r,n]}let $r=class extends B{constructor(u){super(),y(this,u,kr,Fr,b,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}};function mr(u){let e,t,n,r;const s=u[4].default,o=P(s,u,u[3],null);return{c(){e=A("a"),o&&o.c(),k(e,"href",t=Dt(u[0].href)?dt(u[1].baseUrl,u[0].href):u[0].href),k(e,"title",n=u[0].title)},m(i,l){C(i,e,l),o&&o.m(e,null),r=!0},p(i,[l]){o&&o.p&&(!r||8&l)&&O(o,s,i,i[3],r?H(s,i[3],l,null):N(i[3]),null),(!r||3&l&&t!==(t=Dt(i[0].href)?dt(i[1].baseUrl,i[0].href):i[0].href))&&k(e,"href",t),(!r||1&l&&n!==(n=i[0].title))&&k(e,"title",n)},i(i){r||(F(o,i),r=!0)},o(i){m(o,i),r=!1},d(i){i&&E(e),o&&o.d(i)}}}function xr(u,e,t){let{$$slots:n={},$$scope:r}=e,{token:s}=e,{options:o}=e;return u.$$set=i=>{"token"in i&&t(0,s=i.token),"options"in i&&t(1,o=i.options),"$$scope"in i&&t(3,r=i.$$scope)},[s,o,void 0,r,n]}class Cr extends B{constructor(e){super(),y(this,e,xr,mr,b,{token:0,options:1,renderers:2})}get renderers(){return this.$$.ctx[2]}}function Er(u){let e;const t=u[4].default,n=P(t,u,u[3],null);return{c(){n&&n.c()},m(r,s){n&&n.m(r,s),e=!0},p(r,[s]){n&&n.p&&(!e||8&s)&&O(n,t,r,r[3],e?H(t,r[3],s,null):N(r[3]),null)},i(r){e||(F(n,r),e=!0)},o(r){m(n,r),e=!1},d(r){n&&n.d(r)}}}function Ar(u,e,t){let{$$slots:n={},$$scope:r}=e;return u.$$set=s=>{"$$scope"in s&&t(3,r=s.$$scope)},[void 0,void 0,void 0,r,n]}class wr extends B{constructor(e){super(),y(this,e,Ar,Er,b,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function br(u){let e,t;const n=u[4].default,r=P(n,u,u[3],null);return{c(){e=A("dfn"),r&&r.c()},m(s,o){C(s,e,o),r&&r.m(e,null),t=!0},p(s,[o]){r&&r.p&&(!t||8&o)&&O(r,n,s,s[3],t?H(n,s[3],o,null):N(s[3]),null)},i(s){t||(F(r,s),t=!0)},o(s){m(r,s),t=!1},d(s){s&&E(e),r&&r.d(s)}}}function vr(u,e,t){let{$$slots:n={},$$scope:r}=e;return u.$$set=s=>{"$$scope"in s&&t(3,r=s.$$scope)},[void 0,void 0,void 0,r,n]}class Br extends B{constructor(e){super(),y(this,e,vr,br,b,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function yr(u){let e,t;const n=u[4].default,r=P(n,u,u[3],null);return{c(){e=A("del"),r&&r.c()},m(s,o){C(s,e,o),r&&r.m(e,null),t=!0},p(s,[o]){r&&r.p&&(!t||8&o)&&O(r,n,s,s[3],t?H(n,s[3],o,null):N(s[3]),null)},i(s){t||(F(r,s),t=!0)},o(s){m(r,s),t=!1},d(s){s&&E(e),r&&r.d(s)}}}function Sr(u,e,t){let{$$slots:n={},$$scope:r}=e;return u.$$set=s=>{"$$scope"in s&&t(3,r=s.$$scope)},[void 0,void 0,void 0,r,n]}class zr extends B{constructor(e){super(),y(this,e,Sr,yr,b,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Tr(u){let e,t;const n=u[4].default,r=P(n,u,u[3],null);return{c(){e=A("em"),r&&r.c()},m(s,o){C(s,e,o),r&&r.m(e,null),t=!0},p(s,[o]){r&&r.p&&(!t||8&o)&&O(r,n,s,s[3],t?H(n,s[3],o,null):N(s[3]),null)},i(s){t||(F(r,s),t=!0)},o(s){m(r,s),t=!1},d(s){s&&E(e),r&&r.d(s)}}}function Lr(u,e,t){let{$$slots:n={},$$scope:r}=e;return u.$$set=s=>{"$$scope"in s&&t(3,r=s.$$scope)},[void 0,void 0,void 0,r,n]}class Ir extends B{constructor(e){super(),y(this,e,Lr,Tr,b,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function _r(u){let e;return{c(){e=A("hr")},m(t,n){C(t,e,n)},p:L,i:L,o:L,d(t){t&&E(e)}}}function Rr(u,e,t){return[void 0,void 0,void 0]}class jr extends B{constructor(e){super(),y(this,e,Rr,_r,b,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Pr(u){let e,t;const n=u[4].default,r=P(n,u,u[3],null);return{c(){e=A("strong"),r&&r.c()},m(s,o){C(s,e,o),r&&r.m(e,null),t=!0},p(s,[o]){r&&r.p&&(!t||8&o)&&O(r,n,s,s[3],t?H(n,s[3],o,null):N(s[3]),null)},i(s){t||(F(r,s),t=!0)},o(s){m(r,s),t=!1},d(s){s&&E(e),r&&r.d(s)}}}function Or(u,e,t){let{$$slots:n={},$$scope:r}=e;return u.$$set=s=>{"$$scope"in s&&t(3,r=s.$$scope)},[void 0,void 0,void 0,r,n]}class Nr extends B{constructor(e){super(),y(this,e,Or,Pr,b,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Hr(u){let e,t,n,r;return{c(){e=A("img"),at(e.src,t=u[0].href)||k(e,"src",t),k(e,"title",n=u[0].title),k(e,"alt",r=u[0].text),k(e,"class","markdown-image svelte-z38cge")},m(s,o){C(s,e,o)},p(s,[o]){1&o&&!at(e.src,t=s[0].href)&&k(e,"src",t),1&o&&n!==(n=s[0].title)&&k(e,"title",n),1&o&&r!==(r=s[0].text)&&k(e,"alt",r)},i:L,o:L,d(s){s&&E(e)}}}function qr(u,e,t){let{token:n}=e;return u.$$set=r=>{"token"in r&&t(0,n=r.token)},[n,void 0,void 0]}class Zr extends B{constructor(e){super(),y(this,e,qr,Hr,b,{token:0,options:1,renderers:2})}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Mr(u){let e;const t=u[4].default,n=P(t,u,u[3],null);return{c(){n&&n.c()},m(r,s){n&&n.m(r,s),e=!0},p(r,[s]){n&&n.p&&(!e||8&s)&&O(n,t,r,r[3],e?H(t,r[3],s,null):N(r[3]),null)},i(r){e||(F(n,r),e=!0)},o(r){m(n,r),e=!1},d(r){n&&n.d(r)}}}function Qr(u,e,t){let{$$slots:n={},$$scope:r}=e;return u.$$set=s=>{"$$scope"in s&&t(3,r=s.$$scope)},[void 0,void 0,void 0,r,n]}class vt extends B{constructor(e){super(),y(this,e,Qr,Mr,b,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Wr(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}let oe={async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null};function Bt(u){oe=u}const on=/[&<>"']/,Ur=new RegExp(on.source,"g"),ln=/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,Jr=new RegExp(ln.source,"g"),Vr={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},yt=u=>Vr[u];function M(u,e){if(e){if(on.test(u))return u.replace(Ur,yt)}else if(ln.test(u))return u.replace(Jr,yt);return u}const Xr=/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/gi;function Gr(u){return u.replace(Xr,(e,t)=>(t=t.toLowerCase())==="colon"?":":t.charAt(0)==="#"?t.charAt(1)==="x"?String.fromCharCode(parseInt(t.substring(2),16)):String.fromCharCode(+t.substring(1)):"")}const Yr=/(^|[^\[])\^/g;function z(u,e){let t=typeof u=="string"?u:u.source;e=e||"";const n={replace:(r,s)=>{let o=typeof s=="string"?s:s.source;return o=o.replace(Yr,"$1"),t=t.replace(r,o),n},getRegex:()=>new RegExp(t,e)};return n}function St(u){try{u=encodeURI(u).replace(/%25/g,"%")}catch{return null}return u}const pe={exec:()=>null};function zt(u,e){const t=u.replace(/\|/g,(r,s,o)=>{let i=!1,l=s;for(;--l>=0&&o[l]==="\\";)i=!i;return i?"|":" |"}).split(/ \|/);let n=0;if(t[0].trim()||t.shift(),t.length>0&&!t[t.length-1].trim()&&t.pop(),e)if(t.length>e)t.splice(e);else for(;t.length<e;)t.push("");for(;n<t.length;n++)t[n]=t[n].trim().replace(/\\\|/g,"|");return t}function $e(u,e,t){const n=u.length;if(n===0)return"";let r=0;for(;r<n;){const s=u.charAt(n-r-1);if(s!==e||t){if(s===e||!t)break;r++}else r++}return u.slice(0,n-r)}function Tt(u,e,t,n){const r=e.href,s=e.title?M(e.title):null,o=u[1].replace(/\\([\[\]])/g,"$1");if(u[0].charAt(0)!=="!"){n.state.inLink=!0;const i={type:"link",raw:t,href:r,title:s,text:o,tokens:n.inlineTokens(o)};return n.state.inLink=!1,i}return{type:"image",raw:t,href:r,title:s,text:M(o)}}class be{constructor(e){_(this,"options");_(this,"rules");_(this,"lexer");this.options=e||oe}space(e){const t=this.rules.block.newline.exec(e);if(t&&t[0].length>0)return{type:"space",raw:t[0]}}code(e){const t=this.rules.block.code.exec(e);if(t){const n=t[0].replace(/^ {1,4}/gm,"");return{type:"code",raw:t[0],codeBlockStyle:"indented",text:this.options.pedantic?n:$e(n,`
`)}}}fences(e){const t=this.rules.block.fences.exec(e);if(t){const n=t[0],r=function(s,o){const i=s.match(/^(\s+)(?:```)/);if(i===null)return o;const l=i[1];return o.split(`
`).map(a=>{const c=a.match(/^\s+/);if(c===null)return a;const[p]=c;return p.length>=l.length?a.slice(l.length):a}).join(`
`)}(n,t[3]||"");return{type:"code",raw:n,lang:t[2]?t[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):t[2],text:r}}}heading(e){const t=this.rules.block.heading.exec(e);if(t){let n=t[2].trim();if(/#$/.test(n)){const r=$e(n,"#");this.options.pedantic?n=r.trim():r&&!/ $/.test(r)||(n=r.trim())}return{type:"heading",raw:t[0],depth:t[1].length,text:n,tokens:this.lexer.inline(n)}}}hr(e){const t=this.rules.block.hr.exec(e);if(t)return{type:"hr",raw:t[0]}}blockquote(e){const t=this.rules.block.blockquote.exec(e);if(t){const n=$e(t[0].replace(/^ *>[ \t]?/gm,""),`
`),r=this.lexer.state.top;this.lexer.state.top=!0;const s=this.lexer.blockTokens(n);return this.lexer.state.top=r,{type:"blockquote",raw:t[0],tokens:s,text:n}}}list(e){let t=this.rules.block.list.exec(e);if(t){let n=t[1].trim();const r=n.length>1,s={type:"list",raw:"",ordered:r,start:r?+n.slice(0,-1):"",loose:!1,items:[]};n=r?`\\d{1,9}\\${n.slice(-1)}`:`\\${n}`,this.options.pedantic&&(n=r?n:"[*+-]");const o=new RegExp(`^( {0,3}${n})((?:[	 ][^\\n]*)?(?:\\n|$))`);let i="",l="",a=!1;for(;e;){let c=!1;if(!(t=o.exec(e))||this.rules.block.hr.test(e))break;i=t[0],e=e.substring(i.length);let p=t[2].split(`
`,1)[0].replace(/^\t+/,I=>" ".repeat(3*I.length)),g=e.split(`
`,1)[0],h=0;this.options.pedantic?(h=2,l=p.trimStart()):(h=t[2].search(/[^ ]/),h=h>4?1:h,l=p.slice(h),h+=t[1].length);let f=!1;if(!p&&/^ *$/.test(g)&&(i+=g+`
`,e=e.substring(g.length+1),c=!0),!c){const I=new RegExp(`^ {0,${Math.min(3,h-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),j=new RegExp(`^ {0,${Math.min(3,h-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),w=new RegExp(`^ {0,${Math.min(3,h-1)}}(?:\`\`\`|~~~)`),v=new RegExp(`^ {0,${Math.min(3,h-1)}}#`);for(;e;){const $=e.split(`
`,1)[0];if(g=$,this.options.pedantic&&(g=g.replace(/^ {1,4}(?=( {4})*[^ ])/g,"  ")),w.test(g)||v.test(g)||I.test(g)||j.test(e))break;if(g.search(/[^ ]/)>=h||!g.trim())l+=`
`+g.slice(h);else{if(f||p.search(/[^ ]/)>=4||w.test(p)||v.test(p)||j.test(p))break;l+=`
`+g}f||g.trim()||(f=!0),i+=$+`
`,e=e.substring($.length+1),p=g.slice(h)}}s.loose||(a?s.loose=!0:/\n *\n *$/.test(i)&&(a=!0));let d,D=null;this.options.gfm&&(D=/^\[[ xX]\] /.exec(l),D&&(d=D[0]!=="[ ] ",l=l.replace(/^\[[ xX]\] +/,""))),s.items.push({type:"list_item",raw:i,task:!!D,checked:d,loose:!1,text:l,tokens:[]}),s.raw+=i}s.items[s.items.length-1].raw=i.trimEnd(),s.items[s.items.length-1].text=l.trimEnd(),s.raw=s.raw.trimEnd();for(let c=0;c<s.items.length;c++)if(this.lexer.state.top=!1,s.items[c].tokens=this.lexer.blockTokens(s.items[c].text,[]),!s.loose){const p=s.items[c].tokens.filter(h=>h.type==="space"),g=p.length>0&&p.some(h=>/\n.*\n/.test(h.raw));s.loose=g}if(s.loose)for(let c=0;c<s.items.length;c++)s.items[c].loose=!0;return s}}html(e){const t=this.rules.block.html.exec(e);if(t)return{type:"html",block:!0,raw:t[0],pre:t[1]==="pre"||t[1]==="script"||t[1]==="style",text:t[0]}}def(e){const t=this.rules.block.def.exec(e);if(t){const n=t[1].toLowerCase().replace(/\s+/g," "),r=t[2]?t[2].replace(/^<(.*)>$/,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",s=t[3]?t[3].substring(1,t[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):t[3];return{type:"def",tag:n,raw:t[0],href:r,title:s}}}table(e){const t=this.rules.block.table.exec(e);if(!t||!/[:|]/.test(t[2]))return;const n=zt(t[1]),r=t[2].replace(/^\||\| *$/g,"").split("|"),s=t[3]&&t[3].trim()?t[3].replace(/\n[ \t]*$/,"").split(`
`):[],o={type:"table",raw:t[0],header:[],align:[],rows:[]};if(n.length===r.length){for(const i of r)/^ *-+: *$/.test(i)?o.align.push("right"):/^ *:-+: *$/.test(i)?o.align.push("center"):/^ *:-+ *$/.test(i)?o.align.push("left"):o.align.push(null);for(const i of n)o.header.push({text:i,tokens:this.lexer.inline(i)});for(const i of s)o.rows.push(zt(i,o.header.length).map(l=>({text:l,tokens:this.lexer.inline(l)})));return o}}lheading(e){const t=this.rules.block.lheading.exec(e);if(t)return{type:"heading",raw:t[0],depth:t[2].charAt(0)==="="?1:2,text:t[1],tokens:this.lexer.inline(t[1])}}paragraph(e){const t=this.rules.block.paragraph.exec(e);if(t){const n=t[1].charAt(t[1].length-1)===`
`?t[1].slice(0,-1):t[1];return{type:"paragraph",raw:t[0],text:n,tokens:this.lexer.inline(n)}}}text(e){const t=this.rules.block.text.exec(e);if(t)return{type:"text",raw:t[0],text:t[0],tokens:this.lexer.inline(t[0])}}escape(e){const t=this.rules.inline.escape.exec(e);if(t)return{type:"escape",raw:t[0],text:M(t[1])}}tag(e){const t=this.rules.inline.tag.exec(e);if(t)return!this.lexer.state.inLink&&/^<a /i.test(t[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&/^<\/a>/i.test(t[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&/^<(pre|code|kbd|script)(\s|>)/i.test(t[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(t[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:t[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:t[0]}}link(e){const t=this.rules.inline.link.exec(e);if(t){const n=t[2].trim();if(!this.options.pedantic&&/^</.test(n)){if(!/>$/.test(n))return;const o=$e(n.slice(0,-1),"\\");if((n.length-o.length)%2==0)return}else{const o=function(i,l){if(i.indexOf(l[1])===-1)return-1;let a=0;for(let c=0;c<i.length;c++)if(i[c]==="\\")c++;else if(i[c]===l[0])a++;else if(i[c]===l[1]&&(a--,a<0))return c;return-1}(t[2],"()");if(o>-1){const i=(t[0].indexOf("!")===0?5:4)+t[1].length+o;t[2]=t[2].substring(0,o),t[0]=t[0].substring(0,i).trim(),t[3]=""}}let r=t[2],s="";if(this.options.pedantic){const o=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(r);o&&(r=o[1],s=o[3])}else s=t[3]?t[3].slice(1,-1):"";return r=r.trim(),/^</.test(r)&&(r=this.options.pedantic&&!/>$/.test(n)?r.slice(1):r.slice(1,-1)),Tt(t,{href:r&&r.replace(this.rules.inline.anyPunctuation,"$1"),title:s&&s.replace(this.rules.inline.anyPunctuation,"$1")},t[0],this.lexer)}}reflink(e,t){let n;if((n=this.rules.inline.reflink.exec(e))||(n=this.rules.inline.nolink.exec(e))){const r=t[(n[2]||n[1]).replace(/\s+/g," ").toLowerCase()];if(!r){const s=n[0].charAt(0);return{type:"text",raw:s,text:s}}return Tt(n,r,n[0],this.lexer)}}emStrong(e,t,n=""){let r=this.rules.inline.emStrongLDelim.exec(e);if(r&&!(r[3]&&n.match(/[\p{L}\p{N}]/u))&&(!(r[1]||r[2])||!n||this.rules.inline.punctuation.exec(n))){const s=[...r[0]].length-1;let o,i,l=s,a=0;const c=r[0][0]==="*"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(c.lastIndex=0,t=t.slice(-1*e.length+s);(r=c.exec(t))!=null;){if(o=r[1]||r[2]||r[3]||r[4]||r[5]||r[6],!o)continue;if(i=[...o].length,r[3]||r[4]){l+=i;continue}if((r[5]||r[6])&&s%3&&!((s+i)%3)){a+=i;continue}if(l-=i,l>0)continue;i=Math.min(i,i+l+a);const p=[...r[0]][0].length,g=e.slice(0,s+r.index+p+i);if(Math.min(s,i)%2){const f=g.slice(1,-1);return{type:"em",raw:g,text:f,tokens:this.lexer.inlineTokens(f)}}const h=g.slice(2,-2);return{type:"strong",raw:g,text:h,tokens:this.lexer.inlineTokens(h)}}}}codespan(e){const t=this.rules.inline.code.exec(e);if(t){let n=t[2].replace(/\n/g," ");const r=/[^ ]/.test(n),s=/^ /.test(n)&&/ $/.test(n);return r&&s&&(n=n.substring(1,n.length-1)),n=M(n,!0),{type:"codespan",raw:t[0],text:n}}}br(e){const t=this.rules.inline.br.exec(e);if(t)return{type:"br",raw:t[0]}}del(e){const t=this.rules.inline.del.exec(e);if(t)return{type:"del",raw:t[0],text:t[2],tokens:this.lexer.inlineTokens(t[2])}}autolink(e){const t=this.rules.inline.autolink.exec(e);if(t){let n,r;return t[2]==="@"?(n=M(t[1]),r="mailto:"+n):(n=M(t[1]),r=n),{type:"link",raw:t[0],text:n,href:r,tokens:[{type:"text",raw:n,text:n}]}}}url(e){var n;let t;if(t=this.rules.inline.url.exec(e)){let r,s;if(t[2]==="@")r=M(t[0]),s="mailto:"+r;else{let o;do o=t[0],t[0]=((n=this.rules.inline._backpedal.exec(t[0]))==null?void 0:n[0])??"";while(o!==t[0]);r=M(t[0]),s=t[1]==="www."?"http://"+t[0]:t[0]}return{type:"link",raw:t[0],text:r,href:s,tokens:[{type:"text",raw:r,text:r}]}}}inlineText(e){const t=this.rules.inline.text.exec(e);if(t){let n;return n=this.lexer.state.inRawBlock?t[0]:M(t[0]),{type:"text",raw:t[0],text:n}}}}const De=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,an=/(?:[*+-]|\d{1,9}[.)])/,cn=z(/^(?!bull )((?:.|\n(?!\s*?\n|bull ))+?)\n {0,3}(=+|-+) *(?:\n+|$)/).replace(/bull/g,an).getRegex(),Ge=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,Ye=/(?!\s*\])(?:\\.|[^\[\]\\])+/,Kr=z(/^ {0,3}\[(label)\]: *(?:\n *)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n *)?| *\n *)(title))? *(?:\n+|$)/).replace("label",Ye).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),es=z(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,an).getRegex(),_e="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|section|source|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",Ke=/<!--(?!-?>)[\s\S]*?(?:-->|$)/,ts=z("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n *)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$))","i").replace("comment",Ke).replace("tag",_e).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),Lt=z(Ge).replace("hr",De).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",_e).getRegex(),et={blockquote:z(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",Lt).getRegex(),code:/^( {4}[^\n]+(?:\n(?: *(?:\n|$))*)?)+/,def:Kr,fences:/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,heading:/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,hr:De,html:ts,lheading:cn,list:es,newline:/^(?: *(?:\n|$))+/,paragraph:Lt,table:pe,text:/^[^\n]+/},It=z("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",De).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code"," {4}[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",_e).getRegex(),ns={...et,table:It,paragraph:z(Ge).replace("hr",De).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",It).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",_e).getRegex()},rs={...et,html:z(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",Ke).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:pe,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:z(Ge).replace("hr",De).replace("heading",` *#{1,6} *[^
]`).replace("lheading",cn).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},pn=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,hn=/^( {2,}|\\)\n(?!\s*$)/,fe="\\p{P}$+<=>`^|~",ss=z(/^((?![*_])[\spunctuation])/,"u").replace(/punctuation/g,fe).getRegex(),us=z(/^(?:\*+(?:((?!\*)[punct])|[^\s*]))|^_+(?:((?!_)[punct])|([^\s_]))/,"u").replace(/punct/g,fe).getRegex(),os=z("^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)[punct](\\*+)(?=[\\s]|$)|[^punct\\s](\\*+)(?!\\*)(?=[punct\\s]|$)|(?!\\*)[punct\\s](\\*+)(?=[^punct\\s])|[\\s](\\*+)(?!\\*)(?=[punct])|(?!\\*)[punct](\\*+)(?!\\*)(?=[punct])|[^punct\\s](\\*+)(?=[^punct\\s])","gu").replace(/punct/g,fe).getRegex(),is=z("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)[punct](_+)(?=[\\s]|$)|[^punct\\s](_+)(?!_)(?=[punct\\s]|$)|(?!_)[punct\\s](_+)(?=[^punct\\s])|[\\s](_+)(?!_)(?=[punct])|(?!_)[punct](_+)(?!_)(?=[punct])","gu").replace(/punct/g,fe).getRegex(),ls=z(/\\([punct])/,"gu").replace(/punct/g,fe).getRegex(),as=z(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),cs=z(Ke).replace("(?:-->|$)","-->").getRegex(),ps=z("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",cs).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),ve=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,hs=z(/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/).replace("label",ve).replace("href",/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),_t=z(/^!?\[(label)\]\[(ref)\]/).replace("label",ve).replace("ref",Ye).getRegex(),Rt=z(/^!?\[(ref)\](?:\[\])?/).replace("ref",Ye).getRegex(),tt={_backpedal:pe,anyPunctuation:ls,autolink:as,blockSkip:/\[[^[\]]*?\]\([^\(\)]*?\)|`[^`]*?`|<[^<>]*?>/g,br:hn,code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,del:pe,emStrongLDelim:us,emStrongRDelimAst:os,emStrongRDelimUnd:is,escape:pn,link:hs,nolink:Rt,punctuation:ss,reflink:_t,reflinkSearch:z("reflink|nolink(?!\\()","g").replace("reflink",_t).replace("nolink",Rt).getRegex(),tag:ps,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,url:pe},ds={...tt,link:z(/^!?\[(label)\]\((.*?)\)/).replace("label",ve).getRegex(),reflink:z(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",ve).getRegex()},qe={...tt,escape:z(pn).replace("])","~|])").getRegex(),url:z(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])([\s\S]*?[^\s~])\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},Ds={...qe,br:z(hn).replace("{2,}","*").getRegex(),text:z(qe.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},me={normal:et,gfm:ns,pedantic:rs},le={normal:tt,gfm:qe,breaks:Ds,pedantic:ds};class X{constructor(e){_(this,"tokens");_(this,"options");_(this,"state");_(this,"tokenizer");_(this,"inlineQueue");this.tokens=[],this.tokens.links=Object.create(null),this.options=e||oe,this.options.tokenizer=this.options.tokenizer||new be,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const t={block:me.normal,inline:le.normal};this.options.pedantic?(t.block=me.pedantic,t.inline=le.pedantic):this.options.gfm&&(t.block=me.gfm,this.options.breaks?t.inline=le.breaks:t.inline=le.gfm),this.tokenizer.rules=t}static get rules(){return{block:me,inline:le}}static lex(e,t){return new X(t).lex(e)}static lexInline(e,t){return new X(t).inlineTokens(e)}lex(e){e=e.replace(/\r\n|\r/g,`
`),this.blockTokens(e,this.tokens);for(let t=0;t<this.inlineQueue.length;t++){const n=this.inlineQueue[t];this.inlineTokens(n.src,n.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(e,t=[]){let n,r,s,o;for(e=this.options.pedantic?e.replace(/\t/g,"    ").replace(/^ +$/gm,""):e.replace(/^( *)(\t+)/gm,(i,l,a)=>l+"    ".repeat(a.length));e;)if(!(this.options.extensions&&this.options.extensions.block&&this.options.extensions.block.some(i=>!!(n=i.call({lexer:this},e,t))&&(e=e.substring(n.raw.length),t.push(n),!0))))if(n=this.tokenizer.space(e))e=e.substring(n.raw.length),n.raw.length===1&&t.length>0?t[t.length-1].raw+=`
`:t.push(n);else if(n=this.tokenizer.code(e))e=e.substring(n.raw.length),r=t[t.length-1],!r||r.type!=="paragraph"&&r.type!=="text"?t.push(n):(r.raw+=`
`+n.raw,r.text+=`
`+n.text,this.inlineQueue[this.inlineQueue.length-1].src=r.text);else if(n=this.tokenizer.fences(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.heading(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.hr(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.blockquote(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.list(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.html(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.def(e))e=e.substring(n.raw.length),r=t[t.length-1],!r||r.type!=="paragraph"&&r.type!=="text"?this.tokens.links[n.tag]||(this.tokens.links[n.tag]={href:n.href,title:n.title}):(r.raw+=`
`+n.raw,r.text+=`
`+n.raw,this.inlineQueue[this.inlineQueue.length-1].src=r.text);else if(n=this.tokenizer.table(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.lheading(e))e=e.substring(n.raw.length),t.push(n);else{if(s=e,this.options.extensions&&this.options.extensions.startBlock){let i=1/0;const l=e.slice(1);let a;this.options.extensions.startBlock.forEach(c=>{a=c.call({lexer:this},l),typeof a=="number"&&a>=0&&(i=Math.min(i,a))}),i<1/0&&i>=0&&(s=e.substring(0,i+1))}if(this.state.top&&(n=this.tokenizer.paragraph(s)))r=t[t.length-1],o&&r.type==="paragraph"?(r.raw+=`
`+n.raw,r.text+=`
`+n.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=r.text):t.push(n),o=s.length!==e.length,e=e.substring(n.raw.length);else if(n=this.tokenizer.text(e))e=e.substring(n.raw.length),r=t[t.length-1],r&&r.type==="text"?(r.raw+=`
`+n.raw,r.text+=`
`+n.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=r.text):t.push(n);else if(e){const i="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(i);break}throw new Error(i)}}return this.state.top=!0,t}inline(e,t=[]){return this.inlineQueue.push({src:e,tokens:t}),t}inlineTokens(e,t=[]){let n,r,s,o,i,l,a=e;if(this.tokens.links){const c=Object.keys(this.tokens.links);if(c.length>0)for(;(o=this.tokenizer.rules.inline.reflinkSearch.exec(a))!=null;)c.includes(o[0].slice(o[0].lastIndexOf("[")+1,-1))&&(a=a.slice(0,o.index)+"["+"a".repeat(o[0].length-2)+"]"+a.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(o=this.tokenizer.rules.inline.blockSkip.exec(a))!=null;)a=a.slice(0,o.index)+"["+"a".repeat(o[0].length-2)+"]"+a.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;(o=this.tokenizer.rules.inline.anyPunctuation.exec(a))!=null;)a=a.slice(0,o.index)+"++"+a.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;e;)if(i||(l=""),i=!1,!(this.options.extensions&&this.options.extensions.inline&&this.options.extensions.inline.some(c=>!!(n=c.call({lexer:this},e,t))&&(e=e.substring(n.raw.length),t.push(n),!0))))if(n=this.tokenizer.escape(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.tag(e))e=e.substring(n.raw.length),r=t[t.length-1],r&&n.type==="text"&&r.type==="text"?(r.raw+=n.raw,r.text+=n.text):t.push(n);else if(n=this.tokenizer.link(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.reflink(e,this.tokens.links))e=e.substring(n.raw.length),r=t[t.length-1],r&&n.type==="text"&&r.type==="text"?(r.raw+=n.raw,r.text+=n.text):t.push(n);else if(n=this.tokenizer.emStrong(e,a,l))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.codespan(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.br(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.del(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.autolink(e))e=e.substring(n.raw.length),t.push(n);else if(this.state.inLink||!(n=this.tokenizer.url(e))){if(s=e,this.options.extensions&&this.options.extensions.startInline){let c=1/0;const p=e.slice(1);let g;this.options.extensions.startInline.forEach(h=>{g=h.call({lexer:this},p),typeof g=="number"&&g>=0&&(c=Math.min(c,g))}),c<1/0&&c>=0&&(s=e.substring(0,c+1))}if(n=this.tokenizer.inlineText(s))e=e.substring(n.raw.length),n.raw.slice(-1)!=="_"&&(l=n.raw.slice(-1)),i=!0,r=t[t.length-1],r&&r.type==="text"?(r.raw+=n.raw,r.text+=n.text):t.push(n);else if(e){const c="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(c);break}throw new Error(c)}}else e=e.substring(n.raw.length),t.push(n);return t}}class Be{constructor(e){_(this,"options");this.options=e||oe}code(e,t,n){var s;const r=(s=(t||"").match(/^\S*/))==null?void 0:s[0];return e=e.replace(/\n$/,"")+`
`,r?'<pre><code class="language-'+M(r)+'">'+(n?e:M(e,!0))+`</code></pre>
`:"<pre><code>"+(n?e:M(e,!0))+`</code></pre>
`}blockquote(e){return`<blockquote>
${e}</blockquote>
`}html(e,t){return e}heading(e,t,n){return`<h${t}>${e}</h${t}>
`}hr(){return`<hr>
`}list(e,t,n){const r=t?"ol":"ul";return"<"+r+(t&&n!==1?' start="'+n+'"':"")+`>
`+e+"</"+r+`>
`}listitem(e,t,n){return`<li>${e}</li>
`}checkbox(e){return"<input "+(e?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph(e){return`<p>${e}</p>
`}table(e,t){return t&&(t=`<tbody>${t}</tbody>`),`<table>
<thead>
`+e+`</thead>
`+t+`</table>
`}tablerow(e){return`<tr>
${e}</tr>
`}tablecell(e,t){const n=t.header?"th":"td";return(t.align?`<${n} align="${t.align}">`:`<${n}>`)+e+`</${n}>
`}strong(e){return`<strong>${e}</strong>`}em(e){return`<em>${e}</em>`}codespan(e){return`<code>${e}</code>`}br(){return"<br>"}del(e){return`<del>${e}</del>`}link(e,t,n){const r=St(e);if(r===null)return n;let s='<a href="'+(e=r)+'"';return t&&(s+=' title="'+t+'"'),s+=">"+n+"</a>",s}image(e,t,n){const r=St(e);if(r===null)return n;let s=`<img src="${e=r}" alt="${n}"`;return t&&(s+=` title="${t}"`),s+=">",s}text(e){return e}}class nt{strong(e){return e}em(e){return e}codespan(e){return e}del(e){return e}html(e){return e}text(e){return e}link(e,t,n){return""+n}image(e,t,n){return""+n}br(){return""}}class Y{constructor(e){_(this,"options");_(this,"renderer");_(this,"textRenderer");this.options=e||oe,this.options.renderer=this.options.renderer||new Be,this.renderer=this.options.renderer,this.renderer.options=this.options,this.textRenderer=new nt}static parse(e,t){return new Y(t).parse(e)}static parseInline(e,t){return new Y(t).parseInline(e)}parse(e,t=!0){let n="";for(let r=0;r<e.length;r++){const s=e[r];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[s.type]){const o=s,i=this.options.extensions.renderers[o.type].call({parser:this},o);if(i!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(o.type)){n+=i||"";continue}}switch(s.type){case"space":continue;case"hr":n+=this.renderer.hr();continue;case"heading":{const o=s;n+=this.renderer.heading(this.parseInline(o.tokens),o.depth,Gr(this.parseInline(o.tokens,this.textRenderer)));continue}case"code":{const o=s;n+=this.renderer.code(o.text,o.lang,!!o.escaped);continue}case"table":{const o=s;let i="",l="";for(let c=0;c<o.header.length;c++)l+=this.renderer.tablecell(this.parseInline(o.header[c].tokens),{header:!0,align:o.align[c]});i+=this.renderer.tablerow(l);let a="";for(let c=0;c<o.rows.length;c++){const p=o.rows[c];l="";for(let g=0;g<p.length;g++)l+=this.renderer.tablecell(this.parseInline(p[g].tokens),{header:!1,align:o.align[g]});a+=this.renderer.tablerow(l)}n+=this.renderer.table(i,a);continue}case"blockquote":{const o=s,i=this.parse(o.tokens);n+=this.renderer.blockquote(i);continue}case"list":{const o=s,i=o.ordered,l=o.start,a=o.loose;let c="";for(let p=0;p<o.items.length;p++){const g=o.items[p],h=g.checked,f=g.task;let d="";if(g.task){const D=this.renderer.checkbox(!!h);a?g.tokens.length>0&&g.tokens[0].type==="paragraph"?(g.tokens[0].text=D+" "+g.tokens[0].text,g.tokens[0].tokens&&g.tokens[0].tokens.length>0&&g.tokens[0].tokens[0].type==="text"&&(g.tokens[0].tokens[0].text=D+" "+g.tokens[0].tokens[0].text)):g.tokens.unshift({type:"text",text:D+" "}):d+=D+" "}d+=this.parse(g.tokens,a),c+=this.renderer.listitem(d,f,!!h)}n+=this.renderer.list(c,i,l);continue}case"html":{const o=s;n+=this.renderer.html(o.text,o.block);continue}case"paragraph":{const o=s;n+=this.renderer.paragraph(this.parseInline(o.tokens));continue}case"text":{let o=s,i=o.tokens?this.parseInline(o.tokens):o.text;for(;r+1<e.length&&e[r+1].type==="text";)o=e[++r],i+=`
`+(o.tokens?this.parseInline(o.tokens):o.text);n+=t?this.renderer.paragraph(i):i;continue}default:{const o='Token with "'+s.type+'" type was not found.';if(this.options.silent)return console.error(o),"";throw new Error(o)}}}return n}parseInline(e,t){t=t||this.renderer;let n="";for(let r=0;r<e.length;r++){const s=e[r];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[s.type]){const o=this.options.extensions.renderers[s.type].call({parser:this},s);if(o!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(s.type)){n+=o||"";continue}}switch(s.type){case"escape":{const o=s;n+=t.text(o.text);break}case"html":{const o=s;n+=t.html(o.text);break}case"link":{const o=s;n+=t.link(o.href,o.title,this.parseInline(o.tokens,t));break}case"image":{const o=s;n+=t.image(o.href,o.title,o.text);break}case"strong":{const o=s;n+=t.strong(this.parseInline(o.tokens,t));break}case"em":{const o=s;n+=t.em(this.parseInline(o.tokens,t));break}case"codespan":{const o=s;n+=t.codespan(o.text);break}case"br":n+=t.br();break;case"del":{const o=s;n+=t.del(this.parseInline(o.tokens,t));break}case"text":{const o=s;n+=t.text(o.text);break}default:{const o='Token with "'+s.type+'" type was not found.';if(this.options.silent)return console.error(o),"";throw new Error(o)}}}return n}}class he{constructor(e){_(this,"options");this.options=e||oe}preprocess(e){return e}postprocess(e){return e}processAllTokens(e){return e}}_(he,"passThroughHooks",new Set(["preprocess","postprocess","processAllTokens"]));var ue,Ze,dn,rn;const ne=new(rn=class{constructor(...u){it(this,ue);_(this,"defaults",{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null});_(this,"options",this.setOptions);_(this,"parse",ke(this,ue,Ze).call(this,X.lex,Y.parse));_(this,"parseInline",ke(this,ue,Ze).call(this,X.lexInline,Y.parseInline));_(this,"Parser",Y);_(this,"Renderer",Be);_(this,"TextRenderer",nt);_(this,"Lexer",X);_(this,"Tokenizer",be);_(this,"Hooks",he);this.use(...u)}walkTokens(u,e){var n,r;let t=[];for(const s of u)switch(t=t.concat(e.call(this,s)),s.type){case"table":{const o=s;for(const i of o.header)t=t.concat(this.walkTokens(i.tokens,e));for(const i of o.rows)for(const l of i)t=t.concat(this.walkTokens(l.tokens,e));break}case"list":{const o=s;t=t.concat(this.walkTokens(o.items,e));break}default:{const o=s;(r=(n=this.defaults.extensions)==null?void 0:n.childTokens)!=null&&r[o.type]?this.defaults.extensions.childTokens[o.type].forEach(i=>{const l=o[i].flat(1/0);t=t.concat(this.walkTokens(l,e))}):o.tokens&&(t=t.concat(this.walkTokens(o.tokens,e)))}}return t}use(...u){const e=this.defaults.extensions||{renderers:{},childTokens:{}};return u.forEach(t=>{const n={...t};if(n.async=this.defaults.async||n.async||!1,t.extensions&&(t.extensions.forEach(r=>{if(!r.name)throw new Error("extension name required");if("renderer"in r){const s=e.renderers[r.name];e.renderers[r.name]=s?function(...o){let i=r.renderer.apply(this,o);return i===!1&&(i=s.apply(this,o)),i}:r.renderer}if("tokenizer"in r){if(!r.level||r.level!=="block"&&r.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");const s=e[r.level];s?s.unshift(r.tokenizer):e[r.level]=[r.tokenizer],r.start&&(r.level==="block"?e.startBlock?e.startBlock.push(r.start):e.startBlock=[r.start]:r.level==="inline"&&(e.startInline?e.startInline.push(r.start):e.startInline=[r.start]))}"childTokens"in r&&r.childTokens&&(e.childTokens[r.name]=r.childTokens)}),n.extensions=e),t.renderer){const r=this.defaults.renderer||new Be(this.defaults);for(const s in t.renderer){if(!(s in r))throw new Error(`renderer '${s}' does not exist`);if(s==="options")continue;const o=s,i=t.renderer[o],l=r[o];r[o]=(...a)=>{let c=i.apply(r,a);return c===!1&&(c=l.apply(r,a)),c||""}}n.renderer=r}if(t.tokenizer){const r=this.defaults.tokenizer||new be(this.defaults);for(const s in t.tokenizer){if(!(s in r))throw new Error(`tokenizer '${s}' does not exist`);if(["options","rules","lexer"].includes(s))continue;const o=s,i=t.tokenizer[o],l=r[o];r[o]=(...a)=>{let c=i.apply(r,a);return c===!1&&(c=l.apply(r,a)),c}}n.tokenizer=r}if(t.hooks){const r=this.defaults.hooks||new he;for(const s in t.hooks){if(!(s in r))throw new Error(`hook '${s}' does not exist`);if(s==="options")continue;const o=s,i=t.hooks[o],l=r[o];he.passThroughHooks.has(s)?r[o]=a=>{if(this.defaults.async)return Promise.resolve(i.call(r,a)).then(p=>l.call(r,p));const c=i.call(r,a);return l.call(r,c)}:r[o]=(...a)=>{let c=i.apply(r,a);return c===!1&&(c=l.apply(r,a)),c}}n.hooks=r}if(t.walkTokens){const r=this.defaults.walkTokens,s=t.walkTokens;n.walkTokens=function(o){let i=[];return i.push(s.call(this,o)),r&&(i=i.concat(r.call(this,o))),i}}this.defaults={...this.defaults,...n}}),this}setOptions(u){return this.defaults={...this.defaults,...u},this}lexer(u,e){return X.lex(u,e??this.defaults)}parser(u,e){return Y.parse(u,e??this.defaults)}},ue=new WeakSet,Ze=function(u,e){return(t,n)=>{const r={...n},s={...this.defaults,...r};this.defaults.async===!0&&r.async===!1&&(s.silent||console.warn("marked(): The async option was set to true by an extension. The async: false option sent to parse will be ignored."),s.async=!0);const o=ke(this,ue,dn).call(this,!!s.silent,!!s.async);if(t==null)return o(new Error("marked(): input parameter is undefined or null"));if(typeof t!="string")return o(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(t)+", string expected"));if(s.hooks&&(s.hooks.options=s),s.async)return Promise.resolve(s.hooks?s.hooks.preprocess(t):t).then(i=>u(i,s)).then(i=>s.hooks?s.hooks.processAllTokens(i):i).then(i=>s.walkTokens?Promise.all(this.walkTokens(i,s.walkTokens)).then(()=>i):i).then(i=>e(i,s)).then(i=>s.hooks?s.hooks.postprocess(i):i).catch(o);try{s.hooks&&(t=s.hooks.preprocess(t));let i=u(t,s);s.hooks&&(i=s.hooks.processAllTokens(i)),s.walkTokens&&this.walkTokens(i,s.walkTokens);let l=e(i,s);return s.hooks&&(l=s.hooks.postprocess(l)),l}catch(i){return o(i)}}},dn=function(u,e){return t=>{if(t.message+=`
Please report this to https://github.com/markedjs/marked.`,u){const n="<p>An error occurred:</p><pre>"+M(t.message+"",!0)+"</pre>";return e?Promise.resolve(n):n}if(e)return Promise.reject(t);throw t}},rn);function S(u,e){return ne.parse(u,e)}S.options=S.setOptions=function(u){return ne.setOptions(u),S.defaults=ne.defaults,Bt(S.defaults),S},S.getDefaults=Wr,S.defaults=oe,S.use=function(...u){return ne.use(...u),S.defaults=ne.defaults,Bt(S.defaults),S},S.walkTokens=function(u,e){return ne.walkTokens(u,e)},S.parseInline=ne.parseInline,S.Parser=Y,S.parser=Y.parse,S.Renderer=Be,S.TextRenderer=nt,S.Lexer=X,S.lexer=X.lex,S.Tokenizer=be,S.Hooks=he,S.parse=S,S.options,S.setOptions,S.use,S.walkTokens,S.parseInline,Y.parse,X.lex;const fs=/[\0-\x1F!-,\.\/:-@\[-\^`\{-\xA9\xAB-\xB4\xB6-\xB9\xBB-\xBF\xD7\xF7\u02C2-\u02C5\u02D2-\u02DF\u02E5-\u02EB\u02ED\u02EF-\u02FF\u0375\u0378\u0379\u037E\u0380-\u0385\u0387\u038B\u038D\u03A2\u03F6\u0482\u0530\u0557\u0558\u055A-\u055F\u0589-\u0590\u05BE\u05C0\u05C3\u05C6\u05C8-\u05CF\u05EB-\u05EE\u05F3-\u060F\u061B-\u061F\u066A-\u066D\u06D4\u06DD\u06DE\u06E9\u06FD\u06FE\u0700-\u070F\u074B\u074C\u07B2-\u07BF\u07F6-\u07F9\u07FB\u07FC\u07FE\u07FF\u082E-\u083F\u085C-\u085F\u086B-\u089F\u08B5\u08C8-\u08D2\u08E2\u0964\u0965\u0970\u0984\u098D\u098E\u0991\u0992\u09A9\u09B1\u09B3-\u09B5\u09BA\u09BB\u09C5\u09C6\u09C9\u09CA\u09CF-\u09D6\u09D8-\u09DB\u09DE\u09E4\u09E5\u09F2-\u09FB\u09FD\u09FF\u0A00\u0A04\u0A0B-\u0A0E\u0A11\u0A12\u0A29\u0A31\u0A34\u0A37\u0A3A\u0A3B\u0A3D\u0A43-\u0A46\u0A49\u0A4A\u0A4E-\u0A50\u0A52-\u0A58\u0A5D\u0A5F-\u0A65\u0A76-\u0A80\u0A84\u0A8E\u0A92\u0AA9\u0AB1\u0AB4\u0ABA\u0ABB\u0AC6\u0ACA\u0ACE\u0ACF\u0AD1-\u0ADF\u0AE4\u0AE5\u0AF0-\u0AF8\u0B00\u0B04\u0B0D\u0B0E\u0B11\u0B12\u0B29\u0B31\u0B34\u0B3A\u0B3B\u0B45\u0B46\u0B49\u0B4A\u0B4E-\u0B54\u0B58-\u0B5B\u0B5E\u0B64\u0B65\u0B70\u0B72-\u0B81\u0B84\u0B8B-\u0B8D\u0B91\u0B96-\u0B98\u0B9B\u0B9D\u0BA0-\u0BA2\u0BA5-\u0BA7\u0BAB-\u0BAD\u0BBA-\u0BBD\u0BC3-\u0BC5\u0BC9\u0BCE\u0BCF\u0BD1-\u0BD6\u0BD8-\u0BE5\u0BF0-\u0BFF\u0C0D\u0C11\u0C29\u0C3A-\u0C3C\u0C45\u0C49\u0C4E-\u0C54\u0C57\u0C5B-\u0C5F\u0C64\u0C65\u0C70-\u0C7F\u0C84\u0C8D\u0C91\u0CA9\u0CB4\u0CBA\u0CBB\u0CC5\u0CC9\u0CCE-\u0CD4\u0CD7-\u0CDD\u0CDF\u0CE4\u0CE5\u0CF0\u0CF3-\u0CFF\u0D0D\u0D11\u0D45\u0D49\u0D4F-\u0D53\u0D58-\u0D5E\u0D64\u0D65\u0D70-\u0D79\u0D80\u0D84\u0D97-\u0D99\u0DB2\u0DBC\u0DBE\u0DBF\u0DC7-\u0DC9\u0DCB-\u0DCE\u0DD5\u0DD7\u0DE0-\u0DE5\u0DF0\u0DF1\u0DF4-\u0E00\u0E3B-\u0E3F\u0E4F\u0E5A-\u0E80\u0E83\u0E85\u0E8B\u0EA4\u0EA6\u0EBE\u0EBF\u0EC5\u0EC7\u0ECE\u0ECF\u0EDA\u0EDB\u0EE0-\u0EFF\u0F01-\u0F17\u0F1A-\u0F1F\u0F2A-\u0F34\u0F36\u0F38\u0F3A-\u0F3D\u0F48\u0F6D-\u0F70\u0F85\u0F98\u0FBD-\u0FC5\u0FC7-\u0FFF\u104A-\u104F\u109E\u109F\u10C6\u10C8-\u10CC\u10CE\u10CF\u10FB\u1249\u124E\u124F\u1257\u1259\u125E\u125F\u1289\u128E\u128F\u12B1\u12B6\u12B7\u12BF\u12C1\u12C6\u12C7\u12D7\u1311\u1316\u1317\u135B\u135C\u1360-\u137F\u1390-\u139F\u13F6\u13F7\u13FE-\u1400\u166D\u166E\u1680\u169B-\u169F\u16EB-\u16ED\u16F9-\u16FF\u170D\u1715-\u171F\u1735-\u173F\u1754-\u175F\u176D\u1771\u1774-\u177F\u17D4-\u17D6\u17D8-\u17DB\u17DE\u17DF\u17EA-\u180A\u180E\u180F\u181A-\u181F\u1879-\u187F\u18AB-\u18AF\u18F6-\u18FF\u191F\u192C-\u192F\u193C-\u1945\u196E\u196F\u1975-\u197F\u19AC-\u19AF\u19CA-\u19CF\u19DA-\u19FF\u1A1C-\u1A1F\u1A5F\u1A7D\u1A7E\u1A8A-\u1A8F\u1A9A-\u1AA6\u1AA8-\u1AAF\u1AC1-\u1AFF\u1B4C-\u1B4F\u1B5A-\u1B6A\u1B74-\u1B7F\u1BF4-\u1BFF\u1C38-\u1C3F\u1C4A-\u1C4C\u1C7E\u1C7F\u1C89-\u1C8F\u1CBB\u1CBC\u1CC0-\u1CCF\u1CD3\u1CFB-\u1CFF\u1DFA\u1F16\u1F17\u1F1E\u1F1F\u1F46\u1F47\u1F4E\u1F4F\u1F58\u1F5A\u1F5C\u1F5E\u1F7E\u1F7F\u1FB5\u1FBD\u1FBF-\u1FC1\u1FC5\u1FCD-\u1FCF\u1FD4\u1FD5\u1FDC-\u1FDF\u1FED-\u1FF1\u1FF5\u1FFD-\u203E\u2041-\u2053\u2055-\u2070\u2072-\u207E\u2080-\u208F\u209D-\u20CF\u20F1-\u2101\u2103-\u2106\u2108\u2109\u2114\u2116-\u2118\u211E-\u2123\u2125\u2127\u2129\u212E\u213A\u213B\u2140-\u2144\u214A-\u214D\u214F-\u215F\u2189-\u24B5\u24EA-\u2BFF\u2C2F\u2C5F\u2CE5-\u2CEA\u2CF4-\u2CFF\u2D26\u2D28-\u2D2C\u2D2E\u2D2F\u2D68-\u2D6E\u2D70-\u2D7E\u2D97-\u2D9F\u2DA7\u2DAF\u2DB7\u2DBF\u2DC7\u2DCF\u2DD7\u2DDF\u2E00-\u2E2E\u2E30-\u3004\u3008-\u3020\u3030\u3036\u3037\u303D-\u3040\u3097\u3098\u309B\u309C\u30A0\u30FB\u3100-\u3104\u3130\u318F-\u319F\u31C0-\u31EF\u3200-\u33FF\u4DC0-\u4DFF\u9FFD-\u9FFF\uA48D-\uA4CF\uA4FE\uA4FF\uA60D-\uA60F\uA62C-\uA63F\uA673\uA67E\uA6F2-\uA716\uA720\uA721\uA789\uA78A\uA7C0\uA7C1\uA7CB-\uA7F4\uA828-\uA82B\uA82D-\uA83F\uA874-\uA87F\uA8C6-\uA8CF\uA8DA-\uA8DF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA954-\uA95F\uA97D-\uA97F\uA9C1-\uA9CE\uA9DA-\uA9DF\uA9FF\uAA37-\uAA3F\uAA4E\uAA4F\uAA5A-\uAA5F\uAA77-\uAA79\uAAC3-\uAADA\uAADE\uAADF\uAAF0\uAAF1\uAAF7-\uAB00\uAB07\uAB08\uAB0F\uAB10\uAB17-\uAB1F\uAB27\uAB2F\uAB5B\uAB6A-\uAB6F\uABEB\uABEE\uABEF\uABFA-\uABFF\uD7A4-\uD7AF\uD7C7-\uD7CA\uD7FC-\uD7FF\uE000-\uF8FF\uFA6E\uFA6F\uFADA-\uFAFF\uFB07-\uFB12\uFB18-\uFB1C\uFB29\uFB37\uFB3D\uFB3F\uFB42\uFB45\uFBB2-\uFBD2\uFD3E-\uFD4F\uFD90\uFD91\uFDC8-\uFDEF\uFDFC-\uFDFF\uFE10-\uFE1F\uFE30-\uFE32\uFE35-\uFE4C\uFE50-\uFE6F\uFE75\uFEFD-\uFF0F\uFF1A-\uFF20\uFF3B-\uFF3E\uFF40\uFF5B-\uFF65\uFFBF-\uFFC1\uFFC8\uFFC9\uFFD0\uFFD1\uFFD8\uFFD9\uFFDD-\uFFFF]|\uD800[\uDC0C\uDC27\uDC3B\uDC3E\uDC4E\uDC4F\uDC5E-\uDC7F\uDCFB-\uDD3F\uDD75-\uDDFC\uDDFE-\uDE7F\uDE9D-\uDE9F\uDED1-\uDEDF\uDEE1-\uDEFF\uDF20-\uDF2C\uDF4B-\uDF4F\uDF7B-\uDF7F\uDF9E\uDF9F\uDFC4-\uDFC7\uDFD0\uDFD6-\uDFFF]|\uD801[\uDC9E\uDC9F\uDCAA-\uDCAF\uDCD4-\uDCD7\uDCFC-\uDCFF\uDD28-\uDD2F\uDD64-\uDDFF\uDF37-\uDF3F\uDF56-\uDF5F\uDF68-\uDFFF]|\uD802[\uDC06\uDC07\uDC09\uDC36\uDC39-\uDC3B\uDC3D\uDC3E\uDC56-\uDC5F\uDC77-\uDC7F\uDC9F-\uDCDF\uDCF3\uDCF6-\uDCFF\uDD16-\uDD1F\uDD3A-\uDD7F\uDDB8-\uDDBD\uDDC0-\uDDFF\uDE04\uDE07-\uDE0B\uDE14\uDE18\uDE36\uDE37\uDE3B-\uDE3E\uDE40-\uDE5F\uDE7D-\uDE7F\uDE9D-\uDEBF\uDEC8\uDEE7-\uDEFF\uDF36-\uDF3F\uDF56-\uDF5F\uDF73-\uDF7F\uDF92-\uDFFF]|\uD803[\uDC49-\uDC7F\uDCB3-\uDCBF\uDCF3-\uDCFF\uDD28-\uDD2F\uDD3A-\uDE7F\uDEAA\uDEAD-\uDEAF\uDEB2-\uDEFF\uDF1D-\uDF26\uDF28-\uDF2F\uDF51-\uDFAF\uDFC5-\uDFDF\uDFF7-\uDFFF]|\uD804[\uDC47-\uDC65\uDC70-\uDC7E\uDCBB-\uDCCF\uDCE9-\uDCEF\uDCFA-\uDCFF\uDD35\uDD40-\uDD43\uDD48-\uDD4F\uDD74\uDD75\uDD77-\uDD7F\uDDC5-\uDDC8\uDDCD\uDDDB\uDDDD-\uDDFF\uDE12\uDE38-\uDE3D\uDE3F-\uDE7F\uDE87\uDE89\uDE8E\uDE9E\uDEA9-\uDEAF\uDEEB-\uDEEF\uDEFA-\uDEFF\uDF04\uDF0D\uDF0E\uDF11\uDF12\uDF29\uDF31\uDF34\uDF3A\uDF45\uDF46\uDF49\uDF4A\uDF4E\uDF4F\uDF51-\uDF56\uDF58-\uDF5C\uDF64\uDF65\uDF6D-\uDF6F\uDF75-\uDFFF]|\uD805[\uDC4B-\uDC4F\uDC5A-\uDC5D\uDC62-\uDC7F\uDCC6\uDCC8-\uDCCF\uDCDA-\uDD7F\uDDB6\uDDB7\uDDC1-\uDDD7\uDDDE-\uDDFF\uDE41-\uDE43\uDE45-\uDE4F\uDE5A-\uDE7F\uDEB9-\uDEBF\uDECA-\uDEFF\uDF1B\uDF1C\uDF2C-\uDF2F\uDF3A-\uDFFF]|\uD806[\uDC3B-\uDC9F\uDCEA-\uDCFE\uDD07\uDD08\uDD0A\uDD0B\uDD14\uDD17\uDD36\uDD39\uDD3A\uDD44-\uDD4F\uDD5A-\uDD9F\uDDA8\uDDA9\uDDD8\uDDD9\uDDE2\uDDE5-\uDDFF\uDE3F-\uDE46\uDE48-\uDE4F\uDE9A-\uDE9C\uDE9E-\uDEBF\uDEF9-\uDFFF]|\uD807[\uDC09\uDC37\uDC41-\uDC4F\uDC5A-\uDC71\uDC90\uDC91\uDCA8\uDCB7-\uDCFF\uDD07\uDD0A\uDD37-\uDD39\uDD3B\uDD3E\uDD48-\uDD4F\uDD5A-\uDD5F\uDD66\uDD69\uDD8F\uDD92\uDD99-\uDD9F\uDDAA-\uDEDF\uDEF7-\uDFAF\uDFB1-\uDFFF]|\uD808[\uDF9A-\uDFFF]|\uD809[\uDC6F-\uDC7F\uDD44-\uDFFF]|[\uD80A\uD80B\uD80E-\uD810\uD812-\uD819\uD824-\uD82B\uD82D\uD82E\uD830-\uD833\uD837\uD839\uD83D\uD83F\uD87B-\uD87D\uD87F\uD885-\uDB3F\uDB41-\uDBFF][\uDC00-\uDFFF]|\uD80D[\uDC2F-\uDFFF]|\uD811[\uDE47-\uDFFF]|\uD81A[\uDE39-\uDE3F\uDE5F\uDE6A-\uDECF\uDEEE\uDEEF\uDEF5-\uDEFF\uDF37-\uDF3F\uDF44-\uDF4F\uDF5A-\uDF62\uDF78-\uDF7C\uDF90-\uDFFF]|\uD81B[\uDC00-\uDE3F\uDE80-\uDEFF\uDF4B-\uDF4E\uDF88-\uDF8E\uDFA0-\uDFDF\uDFE2\uDFE5-\uDFEF\uDFF2-\uDFFF]|\uD821[\uDFF8-\uDFFF]|\uD823[\uDCD6-\uDCFF\uDD09-\uDFFF]|\uD82C[\uDD1F-\uDD4F\uDD53-\uDD63\uDD68-\uDD6F\uDEFC-\uDFFF]|\uD82F[\uDC6B-\uDC6F\uDC7D-\uDC7F\uDC89-\uDC8F\uDC9A-\uDC9C\uDC9F-\uDFFF]|\uD834[\uDC00-\uDD64\uDD6A-\uDD6C\uDD73-\uDD7A\uDD83\uDD84\uDD8C-\uDDA9\uDDAE-\uDE41\uDE45-\uDFFF]|\uD835[\uDC55\uDC9D\uDCA0\uDCA1\uDCA3\uDCA4\uDCA7\uDCA8\uDCAD\uDCBA\uDCBC\uDCC4\uDD06\uDD0B\uDD0C\uDD15\uDD1D\uDD3A\uDD3F\uDD45\uDD47-\uDD49\uDD51\uDEA6\uDEA7\uDEC1\uDEDB\uDEFB\uDF15\uDF35\uDF4F\uDF6F\uDF89\uDFA9\uDFC3\uDFCC\uDFCD]|\uD836[\uDC00-\uDDFF\uDE37-\uDE3A\uDE6D-\uDE74\uDE76-\uDE83\uDE85-\uDE9A\uDEA0\uDEB0-\uDFFF]|\uD838[\uDC07\uDC19\uDC1A\uDC22\uDC25\uDC2B-\uDCFF\uDD2D-\uDD2F\uDD3E\uDD3F\uDD4A-\uDD4D\uDD4F-\uDEBF\uDEFA-\uDFFF]|\uD83A[\uDCC5-\uDCCF\uDCD7-\uDCFF\uDD4C-\uDD4F\uDD5A-\uDFFF]|\uD83B[\uDC00-\uDDFF\uDE04\uDE20\uDE23\uDE25\uDE26\uDE28\uDE33\uDE38\uDE3A\uDE3C-\uDE41\uDE43-\uDE46\uDE48\uDE4A\uDE4C\uDE50\uDE53\uDE55\uDE56\uDE58\uDE5A\uDE5C\uDE5E\uDE60\uDE63\uDE65\uDE66\uDE6B\uDE73\uDE78\uDE7D\uDE7F\uDE8A\uDE9C-\uDEA0\uDEA4\uDEAA\uDEBC-\uDFFF]|\uD83C[\uDC00-\uDD2F\uDD4A-\uDD4F\uDD6A-\uDD6F\uDD8A-\uDFFF]|\uD83E[\uDC00-\uDFEF\uDFFA-\uDFFF]|\uD869[\uDEDE-\uDEFF]|\uD86D[\uDF35-\uDF3F]|\uD86E[\uDC1E\uDC1F]|\uD873[\uDEA2-\uDEAF]|\uD87A[\uDFE1-\uDFFF]|\uD87E[\uDE1E-\uDFFF]|\uD884[\uDF4B-\uDFFF]|\uDB40[\uDC00-\uDCFF\uDDF0-\uDFFF]/g,gs=Object.hasOwnProperty;class Fs{constructor(){this.occurrences,this.reset()}slug(e,t){const n=this;let r=function(o,i){return typeof o!="string"?"":(i||(o=o.toLowerCase()),o.replace(fs,"").replace(/ /g,"-"))}(e,t===!0);const s=r;for(;gs.call(n.occurrences,r);)n.occurrences[s]++,r=s+"-"+n.occurrences[s];return n.occurrences[r]=0,r}reset(){this.occurrences=Object.create(null)}}function ks(u){let e,t;return e=new Ie({props:{tokens:u[0],renderers:u[1],options:u[2]}}),{c(){Q(e.$$.fragment)},m(n,r){W(e,n,r),t=!0},p(n,[r]){const s={};1&r&&(s.tokens=n[0]),2&r&&(s.renderers=n[1]),4&r&&(s.options=n[2]),e.$set(s)},i(n){t||(F(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){U(e,n)}}}function $s(u,e,t){(function(){const a=console.warn;console.warn=c=>{c.includes("unknown prop")||c.includes("unexpected slot")||a(c)},Cn(()=>{console.warn=a})})();let n,r,s,{source:o}=e,{options:i={}}=e,{renderers:l={}}=e;return u.$$set=a=>{"source"in a&&t(3,o=a.source),"options"in a&&t(4,i=a.options),"renderers"in a&&t(5,l=a.renderers)},u.$$.update=()=>{var a;56&u.$$.dirty&&(t(0,(a=o,n=new X().lex(a))),t(1,r={heading:On,blockquote:qn,list:Yn,list_item:tr,br:sr,code:ir,codespan:cr,table:dr,html:gr,paragraph:$r,link:Cr,text:wr,def:Br,del:zr,em:Ir,hr:jr,strong:Nr,image:Zr,space:vt,escape:vt,...l}),t(2,s={baseUrl:"/",slugger:new Fs,...i}))},[n,r,s,o,i,l]}class ms extends B{constructor(e){super(),y(this,e,$s,ks,b,{source:3,options:4,renderers:5})}}const xs=u=>({}),jt=u=>({}),Cs=u=>({}),Pt=u=>({}),Es=u=>({}),Ot=u=>({});function As(u){let e,t,n,r,s,o,i,l,a,c,p,g;const h=u[12].topBarLeft,f=P(h,u,u[11],Ot),d=u[12].topBarRight,D=P(d,u,u[11],Pt);function I($){u[15]($)}let j={options:{lineNumbers:"off",wrappingIndent:"same",padding:u[5],wordWrap:u[2]?"off":"on",contextmenu:!1,wordBasedSuggestions:"off",renderLineHighlight:"none",occurrencesHighlight:"off",selectionHighlight:!1,codeLens:!1,links:!1,hover:{enabled:!1},hideCursorInOverviewRuler:!0,renderWhitespace:"none",renderFinalNewline:"on"},text:u[3].text,lang:u[4]||u[3].lang,height:u[6]};u[0]!==void 0&&(j.editorInstance=u[0]),o=new In({props:j}),Xe.push(()=>En(o,"editorInstance",I));const w=u[12].actionsBar,v=P(w,u,u[11],jt);return{c(){e=A("div"),t=A("div"),n=A("div"),f&&f.c(),r=te(),D&&D.c(),s=te(),Q(o.$$.fragment),l=te(),a=A("div"),v&&v.c(),k(n,"class","c-codeblock__top-bar-left svelte-1jljgam"),k(t,"class","c-codeblock__top-bar-anchor monaco-component svelte-1jljgam"),k(a,"class","c-codeblock__actions-bar-anchor svelte-1jljgam"),k(e,"class","c-codeblock svelte-1jljgam"),k(e,"role","button"),k(e,"tabindex","0")},m($,T){C($,e,T),R(e,t),R(t,n),f&&f.m(n,null),R(t,r),D&&D.m(t,null),R(e,s),W(o,e,null),R(e,l),R(e,a),v&&v.m(a,null),u[16](e),c=!0,p||(g=[ct(window,"focus",u[14]),ct(e,"mouseenter",u[13])],p=!0)},p($,[T]){f&&f.p&&(!c||2048&T)&&O(f,h,$,$[11],c?H(h,$[11],T,Es):N($[11]),Ot),D&&D.p&&(!c||2048&T)&&O(D,d,$,$[11],c?H(d,$[11],T,Cs):N($[11]),Pt);const x={};36&T&&(x.options={lineNumbers:"off",wrappingIndent:"same",padding:$[5],wordWrap:$[2]?"off":"on",contextmenu:!1,wordBasedSuggestions:"off",renderLineHighlight:"none",occurrencesHighlight:"off",selectionHighlight:!1,codeLens:!1,links:!1,hover:{enabled:!1},hideCursorInOverviewRuler:!0,renderWhitespace:"none",renderFinalNewline:"on"}),8&T&&(x.text=$[3].text),24&T&&(x.lang=$[4]||$[3].lang),64&T&&(x.height=$[6]),!i&&1&T&&(i=!0,x.editorInstance=$[0],An(()=>i=!1)),o.$set(x),v&&v.p&&(!c||2048&T)&&O(v,w,$,$[11],c?H(w,$[11],T,xs):N($[11]),jt)},i($){c||(F(f,$),F(D,$),F(o.$$.fragment,$),F(v,$),c=!0)},o($){m(f,$),m(D,$),m(o.$$.fragment,$),m(v,$),c=!1},d($){$&&E(e),f&&f.d($),D&&D.d($),U(o),v&&v.d($),u[16](null),p=!1,wn(g)}}}function ws(u,e,t){let n,{$$slots:r={},$$scope:s}=e,{scroll:o=!1}=e,{token:i}=e,{language:l}=e,{padding:a={top:0,bottom:0}}=e,{editorInstance:c}=e,{element:p}=e,{height:g}=e;const h=_n.getContext().monaco;Ae(u,h,D=>t(17,n=D));const f=()=>{if(!c)return;const D=c.getSelections();if(!(D!=null&&D.length))return;const I=c.getModel();if(D.map(j=>(I==null?void 0:I.getValueLengthInRange(j))||0).reduce((j,w)=>j+w,0)!==0)return D.sort(n==null?void 0:n.Range.compareRangesUsingStarts).map(j=>(I==null?void 0:I.getValueInRange(j))||"").join(`
`)},d=()=>{if(c)return c.getValue()||""};return u.$$set=D=>{"scroll"in D&&t(2,o=D.scroll),"token"in D&&t(3,i=D.token),"language"in D&&t(4,l=D.language),"padding"in D&&t(5,a=D.padding),"editorInstance"in D&&t(0,c=D.editorInstance),"element"in D&&t(1,p=D.element),"height"in D&&t(6,g=D.height),"$$scope"in D&&t(11,s=D.$$scope)},u.$$.update=()=>{var D;32&u.$$.dirty&&(D=a,c==null||c.updateOptions({padding:D})),65&u.$$.dirty&&(c==null||c.updateOptions({scrollbar:{vertical:g!==void 0?"auto":"hidden"}}))},[c,p,o,i,l,a,g,h,()=>c&&(f()||d())||"",f,d,s,r,function(D){bn.call(this,u,D)},()=>c==null?void 0:c.layout(),function(D){c=D,t(0,c)},function(D){Xe[D?"unshift":"push"](()=>{p=D,t(1,p)})}]}class Nt extends B{constructor(e){super(),y(this,e,ws,As,b,{scroll:2,token:3,language:4,padding:5,editorInstance:0,element:1,height:6,getSelectionOrContents:8,getSelections:9,getContents:10})}get getSelectionOrContents(){return this.$$.ctx[8]}get getSelections(){return this.$$.ctx[9]}get getContents(){return this.$$.ctx[10]}}const bs=u=>({codespanContents:2&u}),Ht=u=>({codespanContents:u[1]});function vs(u){let e,t,n;const r=u[4].default,s=P(r,u,u[3],Ht),o=s||function(i){let l;return{c(){l=Te(i[1])},m(a,c){C(a,l,c)},p(a,c){2&c&&Le(l,a[1])},d(a){a&&E(l)}}}(u);return{c(){e=A("span"),t=A("code"),o&&o.c(),k(t,"class","markdown-codespan svelte-1ufogiu")},m(i,l){C(i,e,l),R(e,t),o&&o.m(t,null),u[5](e),n=!0},p(i,[l]){s?s.p&&(!n||10&l)&&O(s,r,i,i[3],n?H(r,i[3],l,bs):N(i[3]),Ht):o&&o.p&&(!n||2&l)&&o.p(i,n?l:-1)},i(i){n||(F(o,i),n=!0)},o(i){m(o,i),n=!1},d(i){i&&E(e),o&&o.d(i),u[5](null)}}}function Bs(u,e,t){let n,{$$slots:r={},$$scope:s}=e,{token:o}=e,{element:i}=e;return u.$$set=l=>{"token"in l&&t(2,o=l.token),"element"in l&&t(0,i=l.element),"$$scope"in l&&t(3,s=l.$$scope)},u.$$.update=()=>{4&u.$$.dirty&&t(1,n=o.raw.slice(1,o.raw.length-1))},[i,n,o,s,r,function(l){Xe[l?"unshift":"push"](()=>{i=l,t(0,i)})}]}class qt extends B{constructor(e){super(),y(this,e,Bs,vs,b,{token:2,element:0})}}function ys(u){let e,t;const n=u[1].default,r=P(n,u,u[0],null);return{c(){e=A("p"),r&&r.c(),k(e,"class","augment-markdown-paragraph svelte-1edcdk9")},m(s,o){C(s,e,o),r&&r.m(e,null),t=!0},p(s,[o]){r&&r.p&&(!t||1&o)&&O(r,n,s,s[0],t?H(n,s[0],o,null):N(s[0]),null)},i(s){t||(F(r,s),t=!0)},o(s){m(r,s),t=!1},d(s){s&&E(e),r&&r.d(s)}}}function Ss(u,e,t){let{$$slots:n={},$$scope:r}=e;return u.$$set=s=>{"$$scope"in s&&t(0,r=s.$$scope)},[r,n]}class Zt extends B{constructor(e){super(),y(this,e,Ss,ys,b,{})}}function zs(u){let e,t,n;return t=new ms({props:{source:u[0],renderers:{codespan:qt,code:Nt,paragraph:Zt,...u[1]}}}),{c(){e=A("div"),Q(t.$$.fragment),k(e,"class","c-markdown svelte-n6ddeo")},m(r,s){C(r,e,s),W(t,e,null),n=!0},p(r,[s]){const o={};1&s&&(o.source=r[0]),2&s&&(o.renderers={codespan:qt,code:Nt,paragraph:Zt,...r[1]}),t.$set(o)},i(r){n||(F(t.$$.fragment,r),n=!0)},o(r){m(t.$$.fragment,r),n=!1},d(r){r&&E(e),U(t)}}}function Ts(u,e,t){let{markdown:n}=e,{renderers:r={}}=e;return u.$$set=s=>{"markdown"in s&&t(0,n=s.markdown),"renderers"in s&&t(1,r=s.renderers)},[n,r]}class ou extends B{constructor(e){super(),y(this,e,Ts,zs,b,{markdown:0,renderers:1})}}function Ls(u){let e,t;return{c(){e=re("svg"),t=re("path"),k(t,"fill-rule","evenodd"),k(t,"clip-rule","evenodd"),k(t,"d","M4.93179 5.43179C4.75605 5.60753 4.75605 5.89245 4.93179 6.06819C5.10753 6.24392 5.39245 6.24392 5.56819 6.06819L7.49999 4.13638L9.43179 6.06819C9.60753 6.24392 9.89245 6.24392 10.0682 6.06819C10.2439 5.89245 10.2439 5.60753 10.0682 5.43179L7.81819 3.18179C7.73379 3.0974 7.61933 3.04999 7.49999 3.04999C7.38064 3.04999 7.26618 3.0974 7.18179 3.18179L4.93179 5.43179ZM10.0682 9.56819C10.2439 9.39245 10.2439 9.10753 10.0682 8.93179C9.89245 8.75606 9.60753 8.75606 9.43179 8.93179L7.49999 10.8636L5.56819 8.93179C5.39245 8.75606 5.10753 8.75606 4.93179 8.93179C4.75605 9.10753 4.75605 9.39245 4.93179 9.56819L7.18179 11.8182C7.35753 11.9939 7.64245 11.9939 7.81819 11.8182L10.0682 9.56819Z"),k(t,"fill","currentColor"),k(e,"width","15"),k(e,"height","15"),k(e,"viewBox","0 0 15 15"),k(e,"fill","none"),k(e,"xmlns","http://www.w3.org/2000/svg")},m(n,r){C(n,e,r),R(e,t)},p:L,i:L,o:L,d(n){n&&E(e)}}}class Is extends B{constructor(e){super(),y(this,e,null,Ls,b,{})}}function _s(u){let e,t,n;return{c(){e=re("svg"),t=re("path"),n=re("path"),k(t,"fill-rule","evenodd"),k(t,"clip-rule","evenodd"),k(t,"d","M5.26045 11.9272C5.07304 12.1146 5.07304 12.4185 5.26045 12.606C5.44792 12.7934 5.75183 12.7934 5.93929 12.606L7.99988 10.5454L10.0605 12.606C10.2479 12.7934 10.5518 12.7934 10.7393 12.606C10.9267 12.4185 10.9267 12.1146 10.7393 11.9272L8.33929 9.52716C8.15184 9.33975 7.84792 9.33975 7.66046 9.52716L5.26045 11.9272Z"),k(t,"fill","currentColor"),k(n,"d","M10.7393 3.39387C10.9267 3.58132 10.9267 3.88524 10.7393 4.07269L8.33929 6.47269C8.24928 6.56271 8.12718 6.61328 7.99988 6.61328C7.87258 6.61328 7.75049 6.56271 7.66046 6.47269L5.26045 4.0727C5.07304 3.88524 5.07304 3.58132 5.26045 3.39387C5.44792 3.20642 5.75183 3.20642 5.93929 3.39387L7.99988 5.45447L10.0605 3.39387C10.2479 3.20642 10.5518 3.20642 10.7393 3.39387Z"),k(n,"fill","currentColor"),k(e,"width","16"),k(e,"height","16"),k(e,"viewBox","0 0 16 16"),k(e,"fill","none"),k(e,"xmlns","http://www.w3.org/2000/svg")},m(r,s){C(r,e,s),R(e,t),R(e,n)},p:L,i:L,o:L,d(r){r&&E(e)}}}class Rs extends B{constructor(e){super(),y(this,e,null,_s,b,{})}}const Dn=Symbol("collapsible");function js(){return vn(Dn)}function Ps(u){let e,t;return e=new Rs({}),{c(){Q(e.$$.fragment)},m(n,r){W(e,n,r),t=!0},i(n){t||(F(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){U(e,n)}}}function Os(u){let e,t;return e=new Is({}),{c(){Q(e.$$.fragment)},m(n,r){W(e,n,r),t=!0},i(n){t||(F(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){U(e,n)}}}function Ns(u){let e,t,n,r;const s=[Os,Ps],o=[];function i(l,a){return l[0]?0:1}return e=i(u),t=o[e]=s[e](u),{c(){t.c(),n=ee()},m(l,a){o[e].m(l,a),C(l,n,a),r=!0},p(l,a){let c=e;e=i(l),e!==c&&(J(),m(o[c],1,1,()=>{o[c]=null}),V(),t=o[e],t||(t=o[e]=s[e](l),t.c()),F(t,1),t.m(n.parentNode,n))},i(l){r||(F(t),r=!0)},o(l){m(t),r=!1},d(l){l&&E(n),o[e].d(l)}}}function Hs(u){let e,t;const n=[{variant:"ghost-block"},{color:"neutral"},{size:1},u[3]];let r={$$slots:{default:[Ns]},$$scope:{ctx:u}};for(let s=0;s<n.length;s+=1)r=se(r,n[s]);return e=new Rn({props:r}),e.$on("click",u[2]),{c(){Q(e.$$.fragment)},m(s,o){W(e,s,o),t=!0},p(s,[o]){const i=8&o?Ve(n,[n[0],n[1],n[2],yn(s[3])]):{};33&o&&(i.$$scope={dirty:o,ctx:s}),e.$set(i)},i(s){t||(F(e.$$.fragment,s),t=!0)},o(s){m(e.$$.fragment,s),t=!1},d(s){U(e,s)}}}function qs(u,e,t){const n=[];let r,s=we(e,n);const{collapsed:o,setCollapsed:i}=js();return Ae(u,o,l=>t(0,r=l)),u.$$set=l=>{e=se(se({},e),sn(l)),t(3,s=we(e,n))},[r,o,function(){i(!r)},s]}class iu extends B{constructor(e){super(),y(this,e,qs,Hs,b,{})}}function Zs(u,e){const{onStuck:t,onUnstuck:n,offset:r=0}=e,s=document.createElement("div");s.style.position="absolute",s.style.top=r?`${r}px`:"0",s.style.height="1px",s.style.width="100%",s.style.pointerEvents="none",s.style.opacity="0",s.style.zIndex="-1";const o=u.parentNode;if(!o)return{update:()=>{},destroy:()=>{}};window.getComputedStyle(o).position==="static"&&(o.style.position="relative"),o.insertBefore(s,u);const i=new IntersectionObserver(([l])=>{l.isIntersecting?n==null||n():t==null||t()},{threshold:0,rootMargin:"-1px 0px 0px 0px"});return i.observe(s),{update(l){e.onStuck=l.onStuck,e.onUnstuck=l.onUnstuck,l.offset!==void 0&&l.offset!==r&&(s.style.top=`${l.offset}px`)},destroy(){i.disconnect(),s.remove()}}}const Ms=u=>({}),Mt=u=>({}),Qs=u=>({}),Qt=u=>({});function Wt(u){let e,t,n,r;const s=u[14].default,o=P(s,u,u[13],null);let i=u[9].footer&&Ut(u);return{c(){e=A("div"),o&&o.c(),t=te(),i&&i.c(),n=ee(),k(e,"class","c-collapsible__body svelte-11wdaij")},m(l,a){C(l,e,a),o&&o.m(e,null),C(l,t,a),i&&i.m(l,a),C(l,n,a),r=!0},p(l,a){o&&o.p&&(!r||8192&a)&&O(o,s,l,l[13],r?H(s,l[13],a,null):N(l[13]),null),l[9].footer?i?(i.p(l,a),512&a&&F(i,1)):(i=Ut(l),i.c(),F(i,1),i.m(n.parentNode,n)):i&&(J(),m(i,1,1,()=>{i=null}),V())},i(l){r||(F(o,l),F(i),r=!0)},o(l){m(o,l),m(i),r=!1},d(l){l&&(E(e),E(t),E(n)),o&&o.d(l),i&&i.d(l)}}}function Ut(u){let e,t;const n=u[14].footer,r=P(n,u,u[13],Mt);return{c(){e=A("footer"),r&&r.c(),k(e,"class","c-collapsible__footer svelte-11wdaij")},m(s,o){C(s,e,o),r&&r.m(e,null),t=!0},p(s,o){r&&r.p&&(!t||8192&o)&&O(r,n,s,s[13],t?H(n,s[13],o,Ms):N(s[13]),Mt)},i(s){t||(F(r,s),t=!0)},o(s){m(r,s),t=!1},d(s){s&&E(e),r&&r.d(s)}}}function Ws(u){let e,t,n,r,s,o,i,l,a,c,p;const g=u[14].header,h=P(g,u,u[13],Qt);let f=u[4]&&u[5]&&Wt(u);return{c(){e=A("div"),t=A("header"),n=A("div"),h&&h.c(),s=te(),o=A("div"),i=A("div"),f&&f.c(),k(n,"class","c-collapsible__header-inner svelte-11wdaij"),Z(n,"is-collapsed",u[3]),Z(n,"is-header-stuck",u[0]),Z(n,"has-header-padding",u[2]>0),k(t,"class","c-collapsible__header svelte-11wdaij"),Z(t,"is-sticky",u[1]),k(i,"class","c-collapsible__content-inner svelte-11wdaij"),k(o,"class","c-collapsible__content svelte-11wdaij"),Z(o,"is-collapsed",u[3]),k(e,"class",l="c-collapsible "+u[6]+" svelte-11wdaij"),Z(e,"is-collapsed",u[3]),Z(e,"is-expandable",u[4]),pt(e,"--sticky-header-top",`${u[2]}px`)},m(d,D){C(d,e,D),R(e,t),R(t,n),h&&h.m(n,null),R(e,s),R(e,o),R(o,i),f&&f.m(i,null),a=!0,c||(p=Sn(r=Zs.call(null,t,{offset:-u[2],onStuck:u[15],onUnstuck:u[16]})),c=!0)},p(d,[D]){h&&h.p&&(!a||8192&D)&&O(h,g,d,d[13],a?H(g,d[13],D,Qs):N(d[13]),Qt),(!a||8&D)&&Z(n,"is-collapsed",d[3]),(!a||1&D)&&Z(n,"is-header-stuck",d[0]),(!a||4&D)&&Z(n,"has-header-padding",d[2]>0),r&&zn(r.update)&&5&D&&r.update.call(null,{offset:-d[2],onStuck:d[15],onUnstuck:d[16]}),(!a||2&D)&&Z(t,"is-sticky",d[1]),d[4]&&d[5]?f?(f.p(d,D),48&D&&F(f,1)):(f=Wt(d),f.c(),F(f,1),f.m(i,null)):f&&(J(),m(f,1,1,()=>{f=null}),V()),(!a||8&D)&&Z(o,"is-collapsed",d[3]),(!a||64&D&&l!==(l="c-collapsible "+d[6]+" svelte-11wdaij"))&&k(e,"class",l),(!a||72&D)&&Z(e,"is-collapsed",d[3]),(!a||80&D)&&Z(e,"is-expandable",d[4]),4&D&&pt(e,"--sticky-header-top",`${d[2]}px`)},i(d){a||(F(h,d),F(f),a=!0)},o(d){m(h,d),m(f),a=!1},d(d){d&&E(e),h&&h.d(d),f&&f.d(),c=!1,p()}}}function Us(u,e,t){let n;const r=["collapsed","stickyHeader","expandable","isHeaderStuck","stickyHeaderTop","toggle"];let s,o,i=we(e,r),{$$slots:l={},$$scope:a}=e;const c=Tn(l);let{collapsed:p=!1}=e,{stickyHeader:g=!1}=e,{expandable:h=!0}=e,{isHeaderStuck:f=!1}=e,{stickyHeaderTop:d=-.5}=e;const D=ht(p);Ae(u,D,x=>t(3,s=x));const I=Ln(D,x=>x),j=ht(h);Ae(u,j,x=>t(4,o=x));let w,v=!1;function $(x){h?D.set(x):D.set(!0)}const T=function(){$(!s)};return Bn(Dn,{collapsed:I,setCollapsed:$,toggle:T,expandable:j}),u.$$set=x=>{e=se(se({},e),sn(x)),t(22,i=we(e,r)),"collapsed"in x&&t(10,p=x.collapsed),"stickyHeader"in x&&t(1,g=x.stickyHeader),"expandable"in x&&t(11,h=x.expandable),"isHeaderStuck"in x&&t(0,f=x.isHeaderStuck),"stickyHeaderTop"in x&&t(2,d=x.stickyHeaderTop),"$$scope"in x&&t(13,a=x.$$scope)},u.$$.update=()=>{16&u.$$.dirty&&t(11,h=o),2048&u.$$.dirty&&j.set(h),8&u.$$.dirty&&t(10,p=s),1024&u.$$.dirty&&D.set(p),2048&u.$$.dirty&&(h||D.set(!0)),1024&u.$$.dirty&&(p?(clearTimeout(w),w=setTimeout(()=>{t(5,v=!1)},200)):(clearTimeout(w),t(5,v=!0))),t(6,{class:n}=i,n)},[f,g,d,s,o,v,n,D,j,c,p,h,T,a,l,()=>{t(0,f=!0)},()=>{t(0,f=!1)}]}class lu extends B{constructor(e){super(),y(this,e,Us,Ws,b,{collapsed:10,stickyHeader:1,expandable:11,isHeaderStuck:0,stickyHeaderTop:2,toggle:12})}get toggle(){return this.$$.ctx[12]}}function Js(u){let e,t;return{c(){e=re("svg"),t=re("path"),k(t,"fill-rule","evenodd"),k(t,"clip-rule","evenodd"),k(t,"d","M4.04896 3.25233C4.20904 3.1558 4.40798 3.15014 4.57329 3.23739L14.1733 8.30406C14.3482 8.39638 14.4577 8.57794 14.4577 8.77573C14.4577 8.97352 14.3482 9.15508 14.1733 9.2474L4.57329 14.3141C4.40798 14.4013 4.20904 14.3957 4.04896 14.2991C3.88888 14.2026 3.79102 14.0293 3.79102 13.8424V3.70906C3.79102 3.52214 3.88888 3.34885 4.04896 3.25233Z"),k(t,"fill","currentColor"),k(e,"width","16"),k(e,"height","16"),k(e,"viewBox","0 1 16 16"),k(e,"fill","none"),k(e,"xmlns","http://www.w3.org/2000/svg")},m(n,r){C(n,e,r),R(e,t)},p:L,i:L,o:L,d(n){n&&E(e)}}}class au extends B{constructor(e){super(),y(this,e,null,Js,b,{})}}function K(){}function Jt(u,e,t,n,r){for(var s,o=[];e;)o.push(e),s=e.previousComponent,delete e.previousComponent,e=s;o.reverse();for(var i=0,l=o.length,a=0,c=0;i<l;i++){var p=o[i];if(p.removed)p.value=u.join(n.slice(c,c+p.count)),c+=p.count;else{if(!p.added&&r){var g=t.slice(a,a+p.count);g=g.map(function(h,f){var d=n[c+f];return d.length>h.length?d:h}),p.value=u.join(g)}else p.value=u.join(t.slice(a,a+p.count));a+=p.count,p.added||(c+=p.count)}}return o}function Vt(u,e){var t;for(t=0;t<u.length&&t<e.length;t++)if(u[t]!=e[t])return u.slice(0,t);return u.slice(0,t)}function Xt(u,e){var t;if(!u||!e||u[u.length-1]!=e[e.length-1])return"";for(t=0;t<u.length&&t<e.length;t++)if(u[u.length-(t+1)]!=e[e.length-(t+1)])return u.slice(-t);return u.slice(-t)}function Me(u,e,t){if(u.slice(0,e.length)!=e)throw Error("string ".concat(JSON.stringify(u)," doesn't start with prefix ").concat(JSON.stringify(e),"; this is a bug"));return t+u.slice(e.length)}function Qe(u,e,t){if(!e)return u+t;if(u.slice(-e.length)!=e)throw Error("string ".concat(JSON.stringify(u)," doesn't end with suffix ").concat(JSON.stringify(e),"; this is a bug"));return u.slice(0,-e.length)+t}function ae(u,e){return Me(u,e,"")}function xe(u,e){return Qe(u,e,"")}function Gt(u,e){return e.slice(0,function(t,n){var r=0;t.length>n.length&&(r=t.length-n.length);var s=n.length;t.length<n.length&&(s=t.length);var o=Array(s),i=0;o[0]=0;for(var l=1;l<s;l++){for(n[l]==n[i]?o[l]=o[i]:o[l]=i;i>0&&n[l]!=n[i];)i=o[i];n[l]==n[i]&&i++}i=0;for(var a=r;a<t.length;a++){for(;i>0&&t[a]!=n[i];)i=o[i];t[a]==n[i]&&i++}return i}(u,e))}K.prototype={diff:function(u,e){var t,n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},r=n.callback;typeof n=="function"&&(r=n,n={});var s=this;function o(w){return w=s.postProcess(w,n),r?(setTimeout(function(){r(w)},0),!0):w}u=this.castInput(u,n),e=this.castInput(e,n),u=this.removeEmpty(this.tokenize(u,n));var i=(e=this.removeEmpty(this.tokenize(e,n))).length,l=u.length,a=1,c=i+l;n.maxEditLength!=null&&(c=Math.min(c,n.maxEditLength));var p=(t=n.timeout)!==null&&t!==void 0?t:1/0,g=Date.now()+p,h=[{oldPos:-1,lastComponent:void 0}],f=this.extractCommon(h[0],e,u,0,n);if(h[0].oldPos+1>=l&&f+1>=i)return o(Jt(s,h[0].lastComponent,e,u,s.useLongestToken));var d=-1/0,D=1/0;function I(){for(var w=Math.max(d,-a);w<=Math.min(D,a);w+=2){var v=void 0,$=h[w-1],T=h[w+1];$&&(h[w-1]=void 0);var x=!1;if(T){var q=T.oldPos-w;x=T&&0<=q&&q<i}var ie=$&&$.oldPos+1<l;if(x||ie){if(v=!ie||x&&$.oldPos<T.oldPos?s.addToPath(T,!0,!1,0,n):s.addToPath($,!1,!0,1,n),f=s.extractCommon(v,e,u,w,n),v.oldPos+1>=l&&f+1>=i)return o(Jt(s,v.lastComponent,e,u,s.useLongestToken));h[w]=v,v.oldPos+1>=l&&(D=Math.min(D,w-1)),f+1>=i&&(d=Math.max(d,w+1))}else h[w]=void 0}a++}if(r)(function w(){setTimeout(function(){if(a>c||Date.now()>g)return r();I()||w()},0)})();else for(;a<=c&&Date.now()<=g;){var j=I();if(j)return j}},addToPath:function(u,e,t,n,r){var s=u.lastComponent;return s&&!r.oneChangePerToken&&s.added===e&&s.removed===t?{oldPos:u.oldPos+n,lastComponent:{count:s.count+1,added:e,removed:t,previousComponent:s.previousComponent}}:{oldPos:u.oldPos+n,lastComponent:{count:1,added:e,removed:t,previousComponent:s}}},extractCommon:function(u,e,t,n,r){for(var s=e.length,o=t.length,i=u.oldPos,l=i-n,a=0;l+1<s&&i+1<o&&this.equals(t[i+1],e[l+1],r);)l++,i++,a++,r.oneChangePerToken&&(u.lastComponent={count:1,previousComponent:u.lastComponent,added:!1,removed:!1});return a&&!r.oneChangePerToken&&(u.lastComponent={count:a,previousComponent:u.lastComponent,added:!1,removed:!1}),u.oldPos=i,l},equals:function(u,e,t){return t.comparator?t.comparator(u,e):u===e||t.ignoreCase&&u.toLowerCase()===e.toLowerCase()},removeEmpty:function(u){for(var e=[],t=0;t<u.length;t++)u[t]&&e.push(u[t]);return e},castInput:function(u){return u},tokenize:function(u){return Array.from(u)},join:function(u){return u.join("")},postProcess:function(u){return u}};var ye="a-zA-Z0-9_\\u{C0}-\\u{FF}\\u{D8}-\\u{F6}\\u{F8}-\\u{2C6}\\u{2C8}-\\u{2D7}\\u{2DE}-\\u{2FF}\\u{1E00}-\\u{1EFF}",Vs=new RegExp("[".concat(ye,"]+|\\s+|[^").concat(ye,"]"),"ug"),Ce=new K;function Yt(u,e,t,n){if(e&&t){var r=e.value.match(/^\s*/)[0],s=e.value.match(/\s*$/)[0],o=t.value.match(/^\s*/)[0],i=t.value.match(/\s*$/)[0];if(u){var l=Vt(r,o);u.value=Qe(u.value,o,l),e.value=ae(e.value,l),t.value=ae(t.value,l)}if(n){var a=Xt(s,i);n.value=Me(n.value,i,a),e.value=xe(e.value,a),t.value=xe(t.value,a)}}else if(t)u&&(t.value=t.value.replace(/^\s*/,"")),n&&(n.value=n.value.replace(/^\s*/,""));else if(u&&n){var c=n.value.match(/^\s*/)[0],p=e.value.match(/^\s*/)[0],g=e.value.match(/\s*$/)[0],h=Vt(c,p);e.value=ae(e.value,h);var f=Xt(ae(c,h),g);e.value=xe(e.value,f),n.value=Me(n.value,c,f),u.value=Qe(u.value,c,c.slice(0,c.length-f.length))}else if(n){var d=n.value.match(/^\s*/)[0],D=Gt(e.value.match(/\s*$/)[0],d);e.value=xe(e.value,D)}else if(u){var I=Gt(u.value.match(/\s*$/)[0],e.value.match(/^\s*/)[0]);e.value=ae(e.value,I)}}Ce.equals=function(u,e,t){return t.ignoreCase&&(u=u.toLowerCase(),e=e.toLowerCase()),u.trim()===e.trim()},Ce.tokenize=function(u){var e,t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(t.intlSegmenter){if(t.intlSegmenter.resolvedOptions().granularity!="word")throw new Error('The segmenter passed must have a granularity of "word"');e=Array.from(t.intlSegmenter.segment(u),function(s){return s.segment})}else e=u.match(Vs)||[];var n=[],r=null;return e.forEach(function(s){/\s/.test(s)?r==null?n.push(s):n.push(n.pop()+s):/\s/.test(r)?n[n.length-1]==r?n.push(n.pop()+s):n.push(r+s):n.push(s),r=s}),n},Ce.join=function(u){return u.map(function(e,t){return t==0?e:e.replace(/^\s+/,"")}).join("")},Ce.postProcess=function(u,e){if(!u||e.oneChangePerToken)return u;var t=null,n=null,r=null;return u.forEach(function(s){s.added?n=s:s.removed?r=s:((n||r)&&Yt(t,r,n,s),t=s,n=null,r=null)}),(n||r)&&Yt(t,r,n,null),u},new K().tokenize=function(u){var e=new RegExp("(\\r?\\n)|[".concat(ye,"]+|[^\\S\\n\\r]+|[^").concat(ye,"]"),"ug");return u.match(e)||[]};var Se=new K;function Kt(u,e,t){return Se.diff(u,e,t)}function en(u,e){var t=Object.keys(u);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(u);e&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(u,r).enumerable})),t.push.apply(t,n)}return t}function ze(u){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?arguments[e]:{};e%2?en(Object(t),!0).forEach(function(n){Gs(u,n,t[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(u,Object.getOwnPropertyDescriptors(t)):en(Object(t)).forEach(function(n){Object.defineProperty(u,n,Object.getOwnPropertyDescriptor(t,n))})}return u}function Xs(u){var e=function(t,n){if(typeof t!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var s=r.call(t,n||"default");if(typeof s!="object")return s;throw new TypeError("@@toPrimitive must return a primitive value.")}return(n==="string"?String:Number)(t)}(u,"string");return typeof e=="symbol"?e:e+""}function We(u){return We=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},We(u)}function Gs(u,e,t){return(e=Xs(e))in u?Object.defineProperty(u,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):u[e]=t,u}function Oe(u){return function(e){if(Array.isArray(e))return Ne(e)}(u)||function(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}(u)||function(e,t){if(e){if(typeof e=="string")return Ne(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if(n==="Object"&&e.constructor&&(n=e.constructor.name),n==="Map"||n==="Set")return Array.from(e);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Ne(e,t)}}(u)||function(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}()}function Ne(u,e){(e==null||e>u.length)&&(e=u.length);for(var t=0,n=new Array(e);t<e;t++)n[t]=u[t];return n}Se.tokenize=function(u,e){e.stripTrailingCr&&(u=u.replace(/\r\n/g,`
`));var t=[],n=u.split(/(\n|\r\n)/);n[n.length-1]||n.pop();for(var r=0;r<n.length;r++){var s=n[r];r%2&&!e.newlineIsToken?t[t.length-1]+=s:t.push(s)}return t},Se.equals=function(u,e,t){return t.ignoreWhitespace?(t.newlineIsToken&&u.includes(`
`)||(u=u.trim()),t.newlineIsToken&&e.includes(`
`)||(e=e.trim())):t.ignoreNewlineAtEof&&!t.newlineIsToken&&(u.endsWith(`
`)&&(u=u.slice(0,-1)),e.endsWith(`
`)&&(e=e.slice(0,-1))),K.prototype.equals.call(this,u,e,t)},new K().tokenize=function(u){return u.split(/(\S.+?[.!?])(?=\s+|$)/)},new K().tokenize=function(u){return u.split(/([{}:;,]|\s+)/)};var ce=new K;function Ue(u,e,t,n,r){var s,o;for(e=e||[],t=t||[],n&&(u=n(r,u)),s=0;s<e.length;s+=1)if(e[s]===u)return t[s];if(Object.prototype.toString.call(u)==="[object Array]"){for(e.push(u),o=new Array(u.length),t.push(o),s=0;s<u.length;s+=1)o[s]=Ue(u[s],e,t,n,r);return e.pop(),t.pop(),o}if(u&&u.toJSON&&(u=u.toJSON()),We(u)==="object"&&u!==null){e.push(u),o={},t.push(o);var i,l=[];for(i in u)Object.prototype.hasOwnProperty.call(u,i)&&l.push(i);for(l.sort(),s=0;s<l.length;s+=1)o[i=l[s]]=Ue(u[i],e,t,n,i);e.pop(),t.pop()}else o=u;return o}ce.useLongestToken=!0,ce.tokenize=Se.tokenize,ce.castInput=function(u,e){var t=e.undefinedReplacement,n=e.stringifyReplacer,r=n===void 0?function(s,o){return o===void 0?t:o}:n;return typeof u=="string"?u:JSON.stringify(Ue(u,null,null,r),r,"  ")},ce.equals=function(u,e,t){return K.prototype.equals.call(ce,u.replace(/,([\r\n])/g,"$1"),e.replace(/,([\r\n])/g,"$1"),t)};var He=new K;function cu(u){var e=u.split(/\n/),t=[],n=0;function r(){var i={};for(t.push(i);n<e.length;){var l=e[n];if(/^(\-\-\-|\+\+\+|@@)\s/.test(l))break;var a=/^(?:Index:|diff(?: -r \w+)+)\s+(.+?)\s*$/.exec(l);a&&(i.index=a[1]),n++}for(s(i),s(i),i.hunks=[];n<e.length;){var c=e[n];if(/^(Index:\s|diff\s|\-\-\-\s|\+\+\+\s|===================================================================)/.test(c))break;if(/^@@/.test(c))i.hunks.push(o());else{if(c)throw new Error("Unknown line "+(n+1)+" "+JSON.stringify(c));n++}}}function s(i){var l=/^(---|\+\+\+)\s+(.*)\r?$/.exec(e[n]);if(l){var a=l[1]==="---"?"old":"new",c=l[2].split("	",2),p=c[0].replace(/\\\\/g,"\\");/^".*"$/.test(p)&&(p=p.substr(1,p.length-2)),i[a+"FileName"]=p,i[a+"Header"]=(c[1]||"").trim(),n++}}function o(){var i=n,l=e[n++].split(/@@ -(\d+)(?:,(\d+))? \+(\d+)(?:,(\d+))? @@/),a={oldStart:+l[1],oldLines:l[2]===void 0?1:+l[2],newStart:+l[3],newLines:l[4]===void 0?1:+l[4],lines:[]};a.oldLines===0&&(a.oldStart+=1),a.newLines===0&&(a.newStart+=1);for(var c=0,p=0;n<e.length&&(p<a.oldLines||c<a.newLines||(g=e[n])!==null&&g!==void 0&&g.startsWith("\\"));n++){var g,h=e[n].length==0&&n!=e.length-1?" ":e[n][0];if(h!=="+"&&h!=="-"&&h!==" "&&h!=="\\")throw new Error("Hunk at line ".concat(i+1," contained invalid line ").concat(e[n]));a.lines.push(e[n]),h==="+"?c++:h==="-"?p++:h===" "&&(c++,p++)}if(c||a.newLines!==1||(a.newLines=0),p||a.oldLines!==1||(a.oldLines=0),c!==a.newLines)throw new Error("Added line count did not match for hunk at line "+(i+1));if(p!==a.oldLines)throw new Error("Removed line count did not match for hunk at line "+(i+1));return a}for(;n<e.length;)r();return t}function tn(u,e,t,n,r,s,o){if(o||(o={}),typeof o=="function"&&(o={callback:o}),o.context===void 0&&(o.context=4),o.newlineIsToken)throw new Error("newlineIsToken may not be used with patch-generation functions, only with diffing functions");if(!o.callback)return l(Kt(t,n,o));var i=o.callback;function l(a){if(a){a.push({value:"",lines:[]});for(var c=[],p=0,g=0,h=[],f=1,d=1,D=function(){var x=a[I],q=x.lines||function(ge){var gn=ge.endsWith(`
`),Fe=ge.split(`
`).map(function(Fn){return Fn+`
`});return gn?Fe.pop():Fe.push(Fe.pop().slice(0,-1)),Fe}(x.value);if(x.lines=q,x.added||x.removed){var ie;if(!p){var rt=a[I-1];p=f,g=d,rt&&(h=o.context>0?T(rt.lines.slice(-o.context)):[],p-=h.length,g-=h.length)}(ie=h).push.apply(ie,Oe(q.map(function(ge){return(x.added?"+":"-")+ge}))),x.added?d+=q.length:f+=q.length}else{if(p)if(q.length<=2*o.context&&I<a.length-2){var st;(st=h).push.apply(st,Oe(T(q)))}else{var ut,Re=Math.min(q.length,o.context);(ut=h).push.apply(ut,Oe(T(q.slice(0,Re))));var fn={oldStart:p,oldLines:f-p+Re,newStart:g,newLines:d-g+Re,lines:h};c.push(fn),p=0,g=0,h=[]}f+=q.length,d+=q.length}},I=0;I<a.length;I++)D();for(var j=0,w=c;j<w.length;j++)for(var v=w[j],$=0;$<v.lines.length;$++)v.lines[$].endsWith(`
`)?v.lines[$]=v.lines[$].slice(0,-1):(v.lines.splice($+1,0,"\\ No newline at end of file"),$++);return{oldFileName:u,newFileName:e,oldHeader:r,newHeader:s,hunks:c}}function T(x){return x.map(function(q){return" "+q})}}Kt(t,n,ze(ze({},o),{},{callback:function(a){var c=l(a);i(c)}}))}function Je(u){if(Array.isArray(u))return u.map(Je).join(`
`);var e=[];u.oldFileName==u.newFileName&&e.push("Index: "+u.oldFileName),e.push("==================================================================="),e.push("--- "+u.oldFileName+(u.oldHeader===void 0?"":"	"+u.oldHeader)),e.push("+++ "+u.newFileName+(u.newHeader===void 0?"":"	"+u.newHeader));for(var t=0;t<u.hunks.length;t++){var n=u.hunks[t];n.oldLines===0&&(n.oldStart-=1),n.newLines===0&&(n.newStart-=1),e.push("@@ -"+n.oldStart+","+n.oldLines+" +"+n.newStart+","+n.newLines+" @@"),e.push.apply(e,n.lines)}return e.join(`
`)+`
`}function Ys(u,e,t,n,r,s,o){var i;if(typeof o=="function"&&(o={callback:o}),(i=o)===null||i===void 0||!i.callback){var l=tn(u,e,t,n,r,s,o);return l?Je(l):void 0}var a=o.callback;tn(u,e,t,n,r,s,ze(ze({},o),{},{callback:function(c){c?a(Je(c)):a()}}))}function nn(u){let e=0;for(let t=0;t<u.length;t++)e=(e<<5)-e+u.charCodeAt(t),e|=0;return Math.abs(e).toString(36)}function Ks(u,e,t,n,r={}){const{context:s=3,generateId:o=!0}=r,i=Ys(u,e,t,n,"","",{context:s}),l=e||u;let a;return o?a=`${nn(l)}-${nn(t+n)}`:a=Math.random().toString(36).substring(2,15),{id:a,path:l,diff:i,originalCode:t,modifiedCode:n}}function pu(u,e={}){return u.map(t=>Ks(t.oldPath,t.newPath,t.oldContent,t.newContent,e))}function hu(u){const e=u.split(`
`);return{additions:e.filter(t=>t.startsWith("+")).length,deletions:e.filter(t=>t.startsWith("-")).length}}function du(u){return!u.originalCode||u.originalCode.trim()===""}function Du(u){return!u.modifiedCode||u.modifiedCode.trim()===""}He.tokenize=function(u){return u.slice()},He.join=He.removeEmpty=function(u){return u};export{Nt as C,ou as M,au as P,qt as a,iu as b,lu as c,pu as d,hu as e,Du as f,Ks as g,Ys as h,du as i,Rs as j,js as k,cu as p};

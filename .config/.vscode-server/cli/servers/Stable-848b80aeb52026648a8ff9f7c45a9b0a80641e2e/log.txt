*
* Visual Studio Code Server
*
* By using the software, you agree to
* the Visual Studio Code Server License Terms (https://aka.ms/vscode-server-license) and
* the Microsoft Privacy Statement (https://privacy.microsoft.com/en-US/privacystatement).
*
Server bound to /tmp/code-************************************
Extension host agent listening on /tmp/code-************************************

[08:02:19] 




[08:02:20] Extension host agent started.
[08:02:20] [<unknown>][cd9088a7][ManagementConnection] New connection established.
[08:02:20] [<unknown>][1f00bb31][ExtensionHostConnection] New connection established.
[08:02:20] [<unknown>][1f00bb31][ExtensionHostConnection] <454> Launched Extension Host Process.
[08:02:21] ComputeTargetPlatform: linux-x64
[08:02:24] ComputeTargetPlatform: linux-x64
[08:02:25] Getting Manifest... augment.vscode-augment
[08:02:25] Installing extension: augment.vscode-augment {
  productVersion: { version: '1.100.2', date: '2025-05-14T21:47:40.416Z' },
  pinned: false,
  operation: 3,
  isApplicationScoped: false,
  donotVerifySignature: false,
  context: { clientTargetPlatform: 'darwin-arm64' },
  profileLocation: _r {
    scheme: 'file',
    authority: '',
    path: '/home/<USER>/.vscode-server/extensions/extensions.json',
    query: '',
    fragment: '',
    _formatted: 'file:///home/<USER>/.vscode-server/extensions/extensions.json',
    _fsPath: '/home/<USER>/.vscode-server/extensions/extensions.json'
  }
}
[08:02:27] Extension signature verification result for augment.vscode-augment: Success. Internal Code: 0. Executed: true. Duration: 1580ms.
[08:02:28] Extracted extension to file:///home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.467.1: augment.vscode-augment
[08:02:28] Renamed to /home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.467.1
[08:02:28] Marked extension as removed augment.vscode-augment-0.464.1
[08:02:28] Extension installed successfully: augment.vscode-augment file:///home/<USER>/.vscode-server/extensions/extensions.json
New EH opened, aborting shutdown
[08:07:20] New EH opened, aborting shutdown
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:3000
stack trace: Error: connect ECONNREFUSED 127.0.0.1:3000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)
[10:32:00] Error: connect ECONNREFUSED 127.0.0.1:3000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 3000
}

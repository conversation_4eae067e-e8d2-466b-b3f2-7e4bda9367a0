{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/personnel/components/OrgChartDiagram.vue"}, "modifiedCode": "<template>\n  <div class=\"org-chart-diagram\">\n    <!-- Chart Container -->\n    <div ref=\"chartContainer\" class=\"chart-container bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-800 dark:to-gray-900 rounded-lg p-6 min-h-96\">\n      <!-- SVG will be inserted here by D3 -->\n    </div>\n    \n    <!-- Chart Controls -->\n    <div class=\"mt-4 flex justify-center space-x-4\">\n      <button @click=\"zoomIn\" \n              class=\"px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors\">\n        <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\"></path>\n        </svg>\n      </button>\n      \n      <button @click=\"zoomOut\" \n              class=\"px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors\">\n        <svg class=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M20 12H4\"></path>\n        </svg>\n      </button>\n      \n      <button @click=\"resetZoom\" \n              class=\"px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors\">\n        Reset\n      </button>\n      \n      <button @click=\"toggleLayout\" \n              class=\"px-3 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors\">\n        {{ isVertical ? 'Layout Orizzontale' : 'Layout Verticale' }}\n      </button>\n    </div>\n    \n    <!-- Legend -->\n    <div class=\"mt-4 bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700\">\n      <h4 class=\"text-sm font-medium text-gray-900 dark:text-white mb-2\">Legenda</h4>\n      <div class=\"flex flex-wrap gap-4 text-xs\">\n        <div class=\"flex items-center\">\n          <div class=\"w-4 h-4 bg-blue-500 rounded mr-2\"></div>\n          <span class=\"text-gray-600 dark:text-gray-400\">Dipartimento</span>\n        </div>\n        <div class=\"flex items-center\">\n          <div class=\"w-4 h-4 bg-purple-500 rounded mr-2\"></div>\n          <span class=\"text-gray-600 dark:text-gray-400\">Manager</span>\n        </div>\n        <div class=\"flex items-center\">\n          <div class=\"w-4 h-4 bg-green-500 rounded mr-2\"></div>\n          <span class=\"text-gray-600 dark:text-gray-400\">Dipendente</span>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'\n\n// Props\nconst props = defineProps({\n  orgData: {\n    type: Array,\n    required: true\n  },\n  searchQuery: {\n    type: String,\n    default: ''\n  }\n})\n\n// Emits\nconst emit = defineEmits(['employee-click', 'department-click'])\n\n// Reactive state\nconst chartContainer = ref(null)\nconst isVertical = ref(true)\nlet svg = null\nlet zoom = null\nlet simulation = null\n\n// D3.js implementation (simplified version using basic DOM manipulation)\nconst createChart = () => {\n  if (!chartContainer.value || !props.orgData.length) return\n\n  // Clear previous chart\n  chartContainer.value.innerHTML = ''\n\n  // Create SVG\n  const container = chartContainer.value\n  const width = container.clientWidth\n  const height = Math.max(600, container.clientHeight)\n\n  svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg')\n  svg.setAttribute('width', width)\n  svg.setAttribute('height', height)\n  svg.setAttribute('viewBox', `0 0 ${width} ${height}`)\n  svg.style.background = 'transparent'\n\n  // Create main group for zoom/pan\n  const mainGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g')\n  svg.appendChild(mainGroup)\n\n  // Flatten org data for visualization\n  const nodes = []\n  const links = []\n\n  const processNode = (dept, parent = null, level = 0) => {\n    // Add department node\n    const deptNode = {\n      id: `dept-${dept.id}`,\n      type: 'department',\n      name: dept.name,\n      description: dept.description,\n      level,\n      employees: dept.employees.length,\n      budget: dept.budget,\n      manager: dept.manager,\n      x: 0,\n      y: 0\n    }\n    nodes.push(deptNode)\n\n    // Add link to parent\n    if (parent) {\n      links.push({\n        source: parent.id,\n        target: deptNode.id\n      })\n    }\n\n    // Add employee nodes\n    dept.employees.forEach((emp, index) => {\n      const empNode = {\n        id: `emp-${emp.id}`,\n        type: 'employee',\n        name: emp.full_name,\n        position: emp.position,\n        isManager: emp.is_manager,\n        level: level + 1,\n        x: 0,\n        y: 0,\n        employee: emp\n      }\n      nodes.push(empNode)\n\n      // Link employee to department\n      links.push({\n        source: deptNode.id,\n        target: empNode.id\n      })\n    })\n\n    // Process subdepartments\n    dept.subdepartments.forEach(subdept => {\n      processNode(subdept, deptNode, level + 1)\n    })\n  }\n\n  props.orgData.forEach(dept => processNode(dept))\n\n  // Simple tree layout\n  layoutNodes(nodes, width, height)\n\n  // Draw links\n  links.forEach(link => {\n    const sourceNode = nodes.find(n => n.id === link.source)\n    const targetNode = nodes.find(n => n.id === link.target)\n    \n    if (sourceNode && targetNode) {\n      const line = document.createElementNS('http://www.w3.org/2000/svg', 'line')\n      line.setAttribute('x1', sourceNode.x)\n      line.setAttribute('y1', sourceNode.y)\n      line.setAttribute('x2', targetNode.x)\n      line.setAttribute('y2', targetNode.y)\n      line.setAttribute('stroke', '#d1d5db')\n      line.setAttribute('stroke-width', '2')\n      mainGroup.appendChild(line)\n    }\n  })\n\n  // Draw nodes\n  nodes.forEach(node => {\n    const group = document.createElementNS('http://www.w3.org/2000/svg', 'g')\n    group.setAttribute('transform', `translate(${node.x}, ${node.y})`)\n    group.style.cursor = 'pointer'\n\n    // Node circle\n    const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle')\n    circle.setAttribute('r', node.type === 'department' ? '25' : '15')\n    circle.setAttribute('fill', getNodeColor(node))\n    circle.setAttribute('stroke', '#fff')\n    circle.setAttribute('stroke-width', '2')\n    group.appendChild(circle)\n\n    // Node label\n    const text = document.createElementNS('http://www.w3.org/2000/svg', 'text')\n    text.setAttribute('text-anchor', 'middle')\n    text.setAttribute('dy', node.type === 'department' ? '35' : '25')\n    text.setAttribute('font-size', '12')\n    text.setAttribute('fill', '#374151')\n    text.textContent = truncateText(node.name, 15)\n    group.appendChild(text)\n\n    // Add click handler\n    group.addEventListener('click', () => {\n      if (node.type === 'department') {\n        emit('department-click', { id: node.id.replace('dept-', ''), name: node.name })\n      } else {\n        emit('employee-click', node.employee)\n      }\n    })\n\n    // Add hover effect\n    group.addEventListener('mouseenter', () => {\n      circle.setAttribute('stroke-width', '3')\n      circle.setAttribute('stroke', '#3b82f6')\n    })\n\n    group.addEventListener('mouseleave', () => {\n      circle.setAttribute('stroke-width', '2')\n      circle.setAttribute('stroke', '#fff')\n    })\n\n    mainGroup.appendChild(group)\n  })\n\n  container.appendChild(svg)\n}\n\nconst layoutNodes = (nodes, width, height) => {\n  // Simple hierarchical layout\n  const levels = {}\n  \n  // Group nodes by level\n  nodes.forEach(node => {\n    if (!levels[node.level]) levels[node.level] = []\n    levels[node.level].push(node)\n  })\n\n  // Position nodes\n  Object.keys(levels).forEach(level => {\n    const levelNodes = levels[level]\n    const levelY = isVertical.value \n      ? (parseInt(level) * (height / Object.keys(levels).length)) + 50\n      : height / 2\n    \n    levelNodes.forEach((node, index) => {\n      if (isVertical.value) {\n        node.x = (width / (levelNodes.length + 1)) * (index + 1)\n        node.y = levelY\n      } else {\n        node.x = (parseInt(level) * (width / Object.keys(levels).length)) + 50\n        node.y = (height / (levelNodes.length + 1)) * (index + 1)\n      }\n    })\n  })\n}\n\nconst getNodeColor = (node) => {\n  if (node.type === 'department') return '#3b82f6'\n  if (node.isManager) return '#8b5cf6'\n  return '#10b981'\n}\n\nconst truncateText = (text, maxLength) => {\n  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text\n}\n\nconst zoomIn = () => {\n  if (svg) {\n    const currentScale = getCurrentScale()\n    applyZoom(currentScale * 1.2)\n  }\n}\n\nconst zoomOut = () => {\n  if (svg) {\n    const currentScale = getCurrentScale()\n    applyZoom(currentScale * 0.8)\n  }\n}\n\nconst resetZoom = () => {\n  if (svg) {\n    applyZoom(1)\n  }\n}\n\nconst getCurrentScale = () => {\n  const mainGroup = svg.querySelector('g')\n  const transform = mainGroup.getAttribute('transform') || ''\n  const scaleMatch = transform.match(/scale\\(([^)]+)\\)/)\n  return scaleMatch ? parseFloat(scaleMatch[1]) : 1\n}\n\nconst applyZoom = (scale) => {\n  const mainGroup = svg.querySelector('g')\n  mainGroup.setAttribute('transform', `scale(${scale})`)\n}\n\nconst toggleLayout = () => {\n  isVertical.value = !isVertical.value\n  nextTick(() => {\n    createChart()\n  })\n}\n\n// Watch for data changes\nwatch(() => props.orgData, () => {\n  nextTick(() => {\n    createChart()\n  })\n}, { deep: true })\n\n// Lifecycle\nonMounted(() => {\n  nextTick(() => {\n    createChart()\n  })\n  \n  // Handle window resize\n  window.addEventListener('resize', createChart)\n})\n\nonUnmounted(() => {\n  window.removeEventListener('resize', createChart)\n})\n</script>\n\n<style scoped>\n.org-chart-diagram {\n  width: 100%;\n}\n\n.chart-container {\n  width: 100%;\n  overflow: hidden;\n  position: relative;\n}\n\n.chart-container svg {\n  display: block;\n  margin: 0 auto;\n}\n\n/* Responsive adjustments */\n@media (max-width: 768px) {\n  .chart-container {\n    min-height: 400px;\n  }\n}\n</style>\n"}
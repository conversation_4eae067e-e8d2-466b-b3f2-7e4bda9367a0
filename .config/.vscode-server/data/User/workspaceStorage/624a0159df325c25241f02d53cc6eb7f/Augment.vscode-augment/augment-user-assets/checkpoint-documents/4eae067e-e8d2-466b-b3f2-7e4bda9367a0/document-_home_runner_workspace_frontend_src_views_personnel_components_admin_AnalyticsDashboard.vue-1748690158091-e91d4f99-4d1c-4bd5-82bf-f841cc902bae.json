{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/personnel/components/admin/AnalyticsDashboard.vue"}, "modifiedCode": "<template>\n  <div class=\"space-y-6\">\n    <!-- Overview Cards -->\n    <div v-if=\"analytics\" class=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n      <!-- Total Users -->\n      <div class=\"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg\">\n        <div class=\"p-5\">\n          <div class=\"flex items-center\">\n            <div class=\"flex-shrink-0\">\n              <svg class=\"h-6 w-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"></path>\n              </svg>\n            </div>\n            <div class=\"ml-5 w-0 flex-1\">\n              <dl>\n                <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                  Dipendenti Totali\n                </dt>\n                <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                  {{ analytics.overview.total_users }}\n                </dd>\n              </dl>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Total Departments -->\n      <div class=\"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg\">\n        <div class=\"p-5\">\n          <div class=\"flex items-center\">\n            <div class=\"flex-shrink-0\">\n              <svg class=\"h-6 w-6 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\"></path>\n              </svg>\n            </div>\n            <div class=\"ml-5 w-0 flex-1\">\n              <dl>\n                <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                  Dipartimenti\n                </dt>\n                <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                  {{ analytics.overview.total_departments }}\n                </dd>\n              </dl>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Recent Hires -->\n      <div class=\"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg\">\n        <div class=\"p-5\">\n          <div class=\"flex items-center\">\n            <div class=\"flex-shrink-0\">\n              <svg class=\"h-6 w-6 text-purple-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z\"></path>\n              </svg>\n            </div>\n            <div class=\"ml-5 w-0 flex-1\">\n              <dl>\n                <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                  Assunzioni Recenti (90gg)\n                </dt>\n                <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                  {{ analytics.overview.recent_hires }}\n                </dd>\n              </dl>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Charts Row -->\n    <div v-if=\"analytics\" class=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n      <!-- Users by Role Chart -->\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <h3 class=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">Distribuzione per Ruolo</h3>\n        <div class=\"space-y-3\">\n          <div v-for=\"role in analytics.users_by_role\" :key=\"role.role\" class=\"flex items-center justify-between\">\n            <div class=\"flex items-center\">\n              <div :class=\"[\n                'w-3 h-3 rounded-full mr-3',\n                role.role === 'admin' ? 'bg-red-500' :\n                role.role === 'manager' ? 'bg-blue-500' :\n                'bg-gray-500'\n              ]\"></div>\n              <span class=\"text-sm text-gray-700 dark:text-gray-300\">{{ getRoleLabel(role.role) }}</span>\n            </div>\n            <div class=\"flex items-center\">\n              <span class=\"text-sm font-medium text-gray-900 dark:text-white mr-2\">{{ role.count }}</span>\n              <div class=\"w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                <div :class=\"[\n                  'h-2 rounded-full',\n                  role.role === 'admin' ? 'bg-red-500' :\n                  role.role === 'manager' ? 'bg-blue-500' :\n                  'bg-gray-500'\n                ]\" :style=\"{ width: `${(role.count / analytics.overview.total_users) * 100}%` }\"></div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Users by Department Chart -->\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <h3 class=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">Distribuzione per Dipartimento</h3>\n        <div class=\"space-y-3 max-h-64 overflow-y-auto\">\n          <div v-for=\"dept in analytics.users_by_department\" :key=\"dept.department\" class=\"flex items-center justify-between\">\n            <div class=\"flex items-center\">\n              <div class=\"w-3 h-3 rounded-full bg-indigo-500 mr-3\"></div>\n              <span class=\"text-sm text-gray-700 dark:text-gray-300 truncate\">{{ dept.department }}</span>\n            </div>\n            <div class=\"flex items-center\">\n              <span class=\"text-sm font-medium text-gray-900 dark:text-white mr-2\">{{ dept.count }}</span>\n              <div class=\"w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                <div class=\"h-2 rounded-full bg-indigo-500\" :style=\"{ width: `${(dept.count / analytics.overview.total_users) * 100}%` }\"></div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Employment Types and Salary Analysis -->\n    <div v-if=\"analytics\" class=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n      <!-- Employment Types -->\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <h3 class=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">Tipi di Contratto</h3>\n        <div class=\"space-y-3\">\n          <div v-for=\"type in analytics.employment_types\" :key=\"type.type\" class=\"flex items-center justify-between\">\n            <div class=\"flex items-center\">\n              <div class=\"w-3 h-3 rounded-full bg-green-500 mr-3\"></div>\n              <span class=\"text-sm text-gray-700 dark:text-gray-300\">{{ getEmploymentTypeLabel(type.type) }}</span>\n            </div>\n            <div class=\"flex items-center\">\n              <span class=\"text-sm font-medium text-gray-900 dark:text-white mr-2\">{{ type.count }}</span>\n              <div class=\"w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                <div class=\"h-2 rounded-full bg-green-500\" :style=\"{ width: `${(type.count / analytics.overview.total_users) * 100}%` }\"></div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Average Salary by Department -->\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <h3 class=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">Stipendio Medio per Dipartimento</h3>\n        <div v-if=\"analytics.avg_salary_by_department.length > 0\" class=\"space-y-3 max-h-64 overflow-y-auto\">\n          <div v-for=\"dept in analytics.avg_salary_by_department\" :key=\"dept.department\" class=\"flex items-center justify-between\">\n            <span class=\"text-sm text-gray-700 dark:text-gray-300 truncate\">{{ dept.department }}</span>\n            <span class=\"text-sm font-medium text-gray-900 dark:text-white\">\n              {{ formatCurrency(dept.avg_salary) }}\n            </span>\n          </div>\n        </div>\n        <div v-else class=\"text-center py-8\">\n          <svg class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path>\n          </svg>\n          <p class=\"mt-2 text-sm text-gray-500 dark:text-gray-400\">Nessun dato stipendio disponibile</p>\n        </div>\n      </div>\n    </div>\n\n    <!-- Refresh Button -->\n    <div class=\"flex justify-center\">\n      <button @click=\"$emit('refresh')\"\n              :disabled=\"loading\"\n              class=\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50\">\n        <svg v-if=\"loading\" class=\"animate-spin -ml-1 mr-3 h-4 w-4 text-white\" fill=\"none\" viewBox=\"0 0 24 24\">\n          <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\"></circle>\n          <path class=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n        </svg>\n        <svg v-else class=\"-ml-1 mr-3 h-4 w-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"></path>\n        </svg>\n        Aggiorna Dati\n      </button>\n    </div>\n\n    <!-- Loading State -->\n    <div v-if=\"loading && !analytics\" class=\"flex justify-center items-center h-64\">\n      <div class=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n    </div>\n\n    <!-- Empty State -->\n    <div v-if=\"!loading && !analytics\" class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-12 text-center\">\n      <svg class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"></path>\n      </svg>\n      <h3 class=\"mt-2 text-sm font-medium text-gray-900 dark:text-white\">Nessun dato disponibile</h3>\n      <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\n        Clicca su \"Aggiorna Dati\" per caricare le analytics.\n      </p>\n    </div>\n  </div>\n</template>\n\n<script setup>\n// Props\nconst props = defineProps({\n  analytics: {\n    type: Object,\n    default: null\n  },\n  loading: {\n    type: Boolean,\n    default: false\n  }\n})\n\n// Emits\nconst emit = defineEmits(['refresh'])\n\n// Methods\nconst getRoleLabel = (role) => {\n  const labels = {\n    'admin': 'Admin',\n    'manager': 'Manager',\n    'employee': 'Dipendente'\n  }\n  return labels[role] || role\n}\n\nconst getEmploymentTypeLabel = (type) => {\n  const labels = {\n    'full_time': 'Tempo pieno',\n    'part_time': 'Part-time',\n    'contractor': 'Consulente',\n    'intern': 'Stagista'\n  }\n  return labels[type] || type\n}\n\nconst formatCurrency = (amount) => {\n  if (!amount) return '€0'\n  return new Intl.NumberFormat('it-IT', {\n    style: 'currency',\n    currency: 'EUR'\n  }).format(amount)\n}\n</script>\n\n<style scoped>\n/* Custom styles for analytics dashboard */\n.chart-container {\n  position: relative;\n  height: 300px;\n}\n\n/* Responsive adjustments */\n@media (max-width: 768px) {\n  .chart-container {\n    height: 200px;\n  }\n}\n</style>\n"}
"""
API endpoints for personnel management.
Provides REST API for users, departments, skills, and organization chart.
"""

from flask import Blueprint, request, jsonify
from flask_login import current_user
from sqlalchemy import or_, and_, func
from sqlalchemy.orm import joinedload

from extensions import db
from models import User, Department, Skill, UserSkill, UserProfile
from utils.api_utils import (
    api_response, get_pagination_params, api_permission_required,
    handle_api_error, api_login_required
)
from utils.permissions import (
    PERMISSION_VIEW_PERSONNEL_DATA, PERMISSION_EDIT_PERSONNEL_DATA, PERMISSION_MANAGE_USERS
)

# Create blueprint
api_personnel = Blueprint('api_personnel', __name__, url_prefix='/personnel')

@api_personnel.route('/users', methods=['GET'])
@api_login_required
@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)
def get_users():
    """
    Get list of users with filtering, pagination, and search.

    Query Parameters:
    - page: Page number (default: 1)
    - per_page: Items per page (default: 20)
    - search: Search in name, username, email
    - department_id: Filter by department
    - role: Filter by role
    - is_active: Filter by active status
    - skills: Filter by skills (comma-separated skill IDs)
    """
    try:
        # Get pagination parameters
        page, per_page = get_pagination_params()

        # Build base query
        query = User.query.options(
            joinedload(User.department_obj),
            joinedload(User.detailed_skills).joinedload(UserSkill.skill),
            joinedload(User.profile)
        )

        # Apply filters
        search = request.args.get('search', '').strip()
        if search:
            search_filter = or_(
                User.first_name.ilike(f'%{search}%'),
                User.last_name.ilike(f'%{search}%'),
                User.username.ilike(f'%{search}%'),
                User.email.ilike(f'%{search}%')
            )
            query = query.filter(search_filter)

        # Department filter
        department_id = request.args.get('department_id', type=int)
        if department_id:
            query = query.filter(User.department_id == department_id)

        # Role filter
        role = request.args.get('role')
        if role:
            query = query.filter(User.role == role)

        # Active status filter
        is_active = request.args.get('is_active')
        if is_active is not None:
            is_active_bool = is_active.lower() in ['true', '1', 'yes']
            query = query.filter(User.is_active == is_active_bool)

        # Skills filter
        skills = request.args.get('skills')
        if skills:
            skill_ids = [int(id.strip()) for id in skills.split(',') if id.strip().isdigit()]
            if skill_ids:
                query = query.join(UserSkill).filter(UserSkill.skill_id.in_(skill_ids))

        # Order by
        order_by = request.args.get('order_by', 'last_name')
        order_dir = request.args.get('order_dir', 'asc')

        if hasattr(User, order_by):
            order_column = getattr(User, order_by)
            if order_dir.lower() == 'desc':
                order_column = order_column.desc()
            query = query.order_by(order_column)
        else:
            query = query.order_by(User.last_name.asc())

        # Execute pagination
        pagination = query.paginate(
            page=page, per_page=per_page, error_out=False
        )

        # Serialize users
        users_data = []
        for user in pagination.items:
            user_data = {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'full_name': user.full_name,
                'role': user.role,
                'department_id': user.department_id,
                'department_name': user.department_obj.name if user.department_obj else None,
                'position': user.position,
                'hire_date': user.hire_date.isoformat() if user.hire_date else None,
                'phone': user.phone,
                'profile_image': user.profile_image,
                'is_active': user.is_active,
                'last_login': user.last_login.isoformat() if user.last_login else None,
                'skills': [
                    {
                        'id': us.skill.id,
                        'name': us.skill.name,
                        'category': us.skill.category,
                        'proficiency_level': us.proficiency_level,
                        'years_experience': us.years_experience
                    }
                    for us in user.detailed_skills
                ] if user.detailed_skills else [],
                'profile_completion': user.profile.profile_completion if user.profile else 0.0
            }
            users_data.append(user_data)

        return api_response(
            data={
                'users': users_data,
                'pagination': {
                    'page': pagination.page,
                    'pages': pagination.pages,
                    'per_page': pagination.per_page,
                    'total': pagination.total,
                    'has_next': pagination.has_next,
                    'has_prev': pagination.has_prev
                }
            },
            message=f"Retrieved {len(users_data)} users"
        )

    except Exception as e:
        return handle_api_error(e)

@api_personnel.route('/users/<int:user_id>', methods=['GET'])
@api_login_required
@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)
def get_user(user_id):
    """
    Get detailed information about a specific user.
    """
    try:
        user = User.query.options(
            joinedload(User.department_obj),
            joinedload(User.detailed_skills).joinedload(UserSkill.skill),
            joinedload(User.profile),
            joinedload(User.projects)
        ).get_or_404(user_id)

        # Serialize user with full details
        user_data = {
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'full_name': user.full_name,
            'role': user.role,
            'department_id': user.department_id,
            'department': {
                'id': user.department_obj.id,
                'name': user.department_obj.name,
                'description': user.department_obj.description
            } if user.department_obj else None,
            'position': user.position,
            'hire_date': user.hire_date.isoformat() if user.hire_date else None,
            'phone': user.phone,
            'profile_image': user.profile_image,
            'bio': user.bio,
            'is_active': user.is_active,
            'dark_mode': user.dark_mode,
            'created_at': user.created_at.isoformat() if user.created_at else None,
            'last_login': user.last_login.isoformat() if user.last_login else None,
            'skills': [
                {
                    'id': us.skill.id,
                    'name': us.skill.name,
                    'category': us.skill.category,
                    'description': us.skill.description,
                    'proficiency_level': us.proficiency_level,
                    'years_experience': us.years_experience,
                    'certified': us.is_certified,
                    'last_used': us.certification_date.isoformat() if us.certification_date else None
                }
                for us in user.detailed_skills
            ] if user.detailed_skills else [],
            'projects': [
                {
                    'id': project.id,
                    'name': project.name,
                    'status': project.status,
                    'role': 'team_member'  # Could be enhanced with actual role from project_team table
                }
                for project in user.projects
            ] if user.projects else [],
            'profile': {
                'emergency_contact_name': user.profile.emergency_contact_name,
                'emergency_contact_phone': user.profile.emergency_contact_phone,
                'address': user.profile.address,
                'profile_completion': user.profile.profile_completion,
                'notes': user.profile.notes if current_user.role in ['admin', 'human_resources'] else None
            } if user.profile else None
        }

        return api_response(
            data={'user': user_data},
            message=f"Retrieved user {user.full_name}"
        )

    except Exception as e:
        return handle_api_error(e)

@api_personnel.route('/departments', methods=['GET'])
@api_login_required
def get_departments():
    """
    Get list of departments with organization chart data.
    """
    try:
        departments = Department.query.options(
            joinedload(Department.manager)
        ).all()

        departments_data = []
        for dept in departments:
            dept_data = {
                'id': dept.id,
                'name': dept.name,
                'description': dept.description,
                'manager_id': dept.manager_id,
                'manager': {
                    'id': dept.manager.id,
                    'full_name': dept.manager.full_name,
                    'email': dept.manager.email
                } if dept.manager else None,
                'user_count': dept.employees.count() if dept.employees else 0,
                'users': [
                    {
                        'id': user.id,
                        'full_name': user.full_name,
                        'position': user.position,
                        'email': user.email,
                        'is_active': user.is_active
                    }
                    for user in dept.employees
                ] if dept.employees else [],
                'created_at': dept.created_at.isoformat() if dept.created_at else None
            }
            departments_data.append(dept_data)

        return api_response(
            data={'departments': departments_data},
            message=f"Retrieved {len(departments_data)} departments"
        )

    except Exception as e:
        return handle_api_error(e)

@api_personnel.route('/departments', methods=['POST'])
@api_login_required
@api_permission_required(PERMISSION_MANAGE_USERS)
def create_department():
    """
    Create a new department.
    """
    try:
        data = request.get_json()

        # Validate required fields
        if not data.get('name'):
            return api_response(
                success=False,
                message='Nome dipartimento richiesto',
                status_code=400
            )

        # Check if department name already exists
        existing_dept = Department.query.filter_by(name=data['name']).first()
        if existing_dept:
            return api_response(
                success=False,
                message='Un dipartimento con questo nome esiste già',
                status_code=400
            )

        # Create new department
        department = Department(
            name=data['name'],
            description=data.get('description', ''),
            manager_id=data.get('manager_id'),
            parent_id=data.get('parent_id'),
            budget=data.get('budget', 0.0)
        )

        db.session.add(department)
        db.session.commit()

        # Return created department data
        dept_data = {
            'id': department.id,
            'name': department.name,
            'description': department.description,
            'manager_id': department.manager_id,
            'parent_id': department.parent_id,
            'budget': department.budget,
            'user_count': 0,
            'created_at': department.created_at.isoformat()
        }

        return api_response(
            data={'department': dept_data},
            message=f"Dipartimento '{department.name}' creato con successo"
        )

    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)

@api_personnel.route('/departments/<int:dept_id>', methods=['GET'])
@api_login_required
@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)
def get_department(dept_id):
    """
    Get detailed information about a specific department.
    """
    try:
        department = Department.query.options(
            joinedload(Department.manager),
            joinedload(Department.employees),
            joinedload(Department.subdepartments)
        ).get_or_404(dept_id)

        dept_data = {
            'id': department.id,
            'name': department.name,
            'description': department.description,
            'manager_id': department.manager_id,
            'parent_id': department.parent_id,
            'budget': department.budget,
            'manager': {
                'id': department.manager.id,
                'full_name': department.manager.full_name,
                'email': department.manager.email,
                'position': department.manager.position
            } if department.manager else None,
            'parent': {
                'id': department.parent.id,
                'name': department.parent.name
            } if department.parent else None,
            'employees': [
                {
                    'id': emp.id,
                    'full_name': emp.full_name,
                    'email': emp.email,
                    'position': emp.position,
                    'is_active': emp.is_active,
                    'hire_date': emp.hire_date.isoformat() if emp.hire_date else None
                }
                for emp in department.employees if emp.is_active
            ],
            'subdepartments': [
                {
                    'id': sub.id,
                    'name': sub.name,
                    'employee_count': getattr(sub, 'employee_count', 0)
                }
                for sub in department.subdepartments if getattr(sub, 'is_active', True)
            ],
            'employee_count': len([emp for emp in department.employees if emp.is_active]),
            'created_at': department.created_at.isoformat(),
            'updated_at': department.updated_at.isoformat() if department.updated_at else None
        }

        return api_response(
            data={'department': dept_data},
            message=f"Retrieved department {department.name}"
        )

    except Exception as e:
        return handle_api_error(e)

@api_personnel.route('/departments/<int:dept_id>', methods=['PUT'])
@api_login_required
@api_permission_required(PERMISSION_MANAGE_USERS)
def update_department(dept_id):
    """
    Update an existing department.
    """
    try:
        department = Department.query.get_or_404(dept_id)
        data = request.get_json()

        # Validate name if provided
        if 'name' in data and data['name']:
            # Check if name already exists (excluding current department)
            existing_dept = Department.query.filter(
                Department.name == data['name'],
                Department.id != dept_id
            ).first()
            if existing_dept:
                return api_response(
                    success=False,
                    message='Un dipartimento con questo nome esiste già',
                    status_code=400
                )
            department.name = data['name']

        # Update other fields
        if 'description' in data:
            department.description = data['description']
        if 'manager_id' in data:
            department.manager_id = data['manager_id']
        if 'parent_id' in data:
            department.parent_id = data['parent_id']
        if 'budget' in data:
            department.budget = data['budget']

        db.session.commit()

        # Return updated department data
        dept_data = {
            'id': department.id,
            'name': department.name,
            'description': department.description,
            'manager_id': department.manager_id,
            'parent_id': department.parent_id,
            'budget': department.budget,
            'employee_count': len([emp for emp in department.employees if emp.is_active]),
            'updated_at': department.updated_at.isoformat()
        }

        return api_response(
            data={'department': dept_data},
            message=f"Dipartimento '{department.name}' aggiornato con successo"
        )

    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)

@api_personnel.route('/departments/<int:dept_id>', methods=['DELETE'])
@api_login_required
@api_permission_required(PERMISSION_MANAGE_USERS)
def delete_department(dept_id):
    """
    Delete a department (soft delete by setting is_active=False).
    """
    try:
        department = Department.query.get_or_404(dept_id)

        # Check if department has employees
        active_employees = len([emp for emp in department.employees if emp.is_active])
        if active_employees > 0:
            return api_response(
                success=False,
                message='Impossibile eliminare un dipartimento con dipendenti assegnati',
                status_code=400
            )

        # Check if department has subdepartments
        active_subdepartments = len([sub for sub in department.subdepartments if getattr(sub, 'is_active', True)])
        if active_subdepartments > 0:
            return api_response(
                success=False,
                message='Impossibile eliminare un dipartimento con sotto-dipartimenti',
                status_code=400
            )

        # Soft delete
        department.is_active = False
        db.session.commit()

        return api_response(
            message=f"Dipartimento '{department.name}' eliminato con successo"
        )

    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)

@api_personnel.route('/skills', methods=['GET'])
@api_login_required
@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)
def get_skills():
    """
    Get list of skills with usage statistics.
    """
    try:
        # Get skills with user count
        skills_query = db.session.query(
            Skill,
            func.count(UserSkill.user_id).label('user_count')
        ).outerjoin(UserSkill).group_by(Skill.id)

        category = request.args.get('category')
        if category:
            skills_query = skills_query.filter(Skill.category == category)

        search = request.args.get('search', '').strip()
        if search:
            skills_query = skills_query.filter(
                or_(
                    Skill.name.ilike(f'%{search}%'),
                    Skill.description.ilike(f'%{search}%')
                )
            )

        skills_data = []
        for skill, user_count in skills_query.all():
            skill_data = {
                'id': skill.id,
                'name': skill.name,
                'category': skill.category,
                'description': skill.description,
                'user_count': user_count,
                'users': [
                    {
                        'id': us.user.id,
                        'full_name': us.user.full_name,
                        'proficiency_level': us.proficiency_level,
                        'years_experience': us.years_experience
                    }
                    for us in skill.user_skills
                ] if hasattr(skill, 'user_skills') else []
            }
            skills_data.append(skill_data)

        # Get categories for filter
        categories = db.session.query(Skill.category).distinct().all()
        categories_list = [cat[0] for cat in categories if cat[0]]

        return api_response(
            data={
                'skills': skills_data,
                'categories': categories_list
            },
            message=f"Retrieved {len(skills_data)} skills"
        )

    except Exception as e:
        return handle_api_error(e)
